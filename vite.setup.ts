import {afterAll, afterEach, beforeAll, vi} from "vitest";
import "vitest-canvas-mock";
import "./__mocks__/asyncUtilMock";
import "./__mocks__/hooks/useSettings.mock";
import localStorageMock from "./__mocks__/localStorageMock";
import "./__mocks__/matchMediaMock";
import "./__mocks__/services/analytic.service.mock";
import "./__mocks__/services/company.service.mock";
import "./__mocks__/services/filter.service.mock";
import "./__mocks__/services/partnerpage.service.mock";
import "./__mocks__/useAuthStateMock";
import "./__mocks__/useCSVUploadMock";

let originalLocalStorage: Storage;
beforeAll(() => {
    originalLocalStorage = window.localStorage;
    (window as any).localStorage = localStorageMock;
});
afterAll(() => {
    (window as any).localStorage = originalLocalStorage;
});
afterEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
});
