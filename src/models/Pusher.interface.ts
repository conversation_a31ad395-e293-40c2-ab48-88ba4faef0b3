export interface IPusherData {
    message: string;
    channel: string;
    event: string;
    type: string;
    pollEnds?: string;
    polls?: string[];
    isAdmin?: boolean;
}

export interface IChat {
    id: string;
    message_type: string;
    message: string;
    date: string;
    creator: string;
    receiver: string;
    sender_first_name: string;
    sender_last_name: string;
    company_name: string;
    company_type: string;
    user_avatar?: string;
    is_admin?: boolean;
}

export interface IPublicChat {
    creator: string;
    date: string;
    id: string;
    message: string;
    message_type: "publicChat";
    pusher_channel: string;
    receiver: string;
    can_ban?: boolean;
}

export interface IPusherMsg {
    message: string;
    creator_user_id: string;
    receiver_user_id: string;
    created_at: string;
    message_id: string;
    creator_first_name: string;
    creator_last_name: string;
}

export interface IChatMsg {
    sender: string;
    message: string;
    created_at: string;
    user_name: string;
    user_avatar: string;
}

export interface IBroadcastMsg {
    message: string;
    date: string;
    creator?: string;
    id?: string;
    message_type?: string;
    receiver?: string;
    edited_message_id?: string;
}

export interface IPusherPoll {
    pitch_event_id: string;
    poll_questions: IQuestion[];
    vendor_id: string;
    vendor_name: string;
    vendor_handle?: string;
}

export interface IQuestion {
    id: string;
    poll_options: IOption[];
    question: string;
    question_type: string;
    show_additional_feedback: true;
    poll_category: string;
}

export interface IOption {
    created: string;
    id: string;
    option: string;
    poll_question_id: string;
    shows_on_reports: boolean;
    updated?: string;
}

export interface IBreakoutRooms {
    company_id: string;
    company_name: string;
    break_out_link: string;
    avatar?: string;
}
