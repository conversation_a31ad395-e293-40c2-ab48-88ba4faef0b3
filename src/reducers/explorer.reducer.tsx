import {ICompany} from "../Interfaces/company.interface";
import {IProfile} from "../Interfaces/people.interface";

export const ADD_PROFILES = "ADD_PROFILES";
export const UPDATE_PROFILES = "UPDATE_PROFILES";
export const CLEAR_PROFILES = "CLEAR_PROFILES";

interface IAction {
    type: string;
    payload?: Array<ICompany | IProfile>;
}

export const explorerReducer = (state: Array<ICompany | IProfile> = [], action: IAction) => {
    switch (action.type) {
        case ADD_PROFILES:
            return [...state, ...action.payload!];

        case UPDATE_PROFILES:
            if (!action.payload) return state;
            return action.payload;

        case CLEAR_PROFILES:
            return [];

        default:
            return state;
    }
};
