import {IPState} from "../contexts/pusher.context";

export const UPDATE_PUSHER = "APP/PUSHER/UPDATE";
export const CLEAR_PUSHER = "APP/PUSHER/CLEAR";

export const initialPusherState: IPState = {
    broadcastMsgs: [],
    chatList: [],
    chatHistory: {},
    publicChatHistory: {},
    polls: undefined,
    unreadCount: {},
    availablePortalChatUsers: [],
    selectedPortalChat: undefined,
    portalChatHistory: {},
};

interface IAction {
    type: string;
    payload?: IPState;
}

export const pusherReducer = (state: IPState = initialPusherState, action: IAction) => {
    switch (action.type) {
        case UPDATE_PUSHER:
            return {
                ...state,
                ...action.payload,
            };
        case CLEAR_PUSHER:
            return initialPusherState;
        default:
            return state;
    }
};
