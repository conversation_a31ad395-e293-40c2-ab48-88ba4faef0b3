import {useQuery} from "@tanstack/react-query";
import type {CardTypesResponse} from "../Interfaces/adminAdvertisements.interface";
import {PERMISSION_GROUPS} from "../Interfaces/features/features.interface";
import {DEFAULT_QUERY_CONFIGS} from "../constants/commonStrings.constant";
import adminAdvertisementService from "../services/adminAdvertisement.service";
import useAppConfig from "./useAppConfig";
import usePermissions from "./usePermissions";

const useAdvertisementMeta = () => {
    const {hasPermissions} = usePermissions();

    // added read permission in because the Filter dropdown needs to work for View only as well as Update
    const isAdvertisementPerm = hasPermissions([
        PERMISSION_GROUPS.ADMIN_AD_MNGMT_UPDATE,
        PERMISSION_GROUPS.ADMIN_AD_MNGMT_READ,
    ]);
    const {config, isLoadingConfig} = useAppConfig({config_key: "AD_ACTIVE"});

    const fetchAdCardTypes = () => {
        return new Promise<CardTypesResponse[]>((resolve, reject) => {
            adminAdvertisementService
                .getCardTypes()
                .then(res => {
                    resolve(res.data);
                })
                .catch(err => {
                    reject(err);
                });
        });
    };

    const fetchAdLocations = () => {
        return new Promise((resolve, reject) => {
            adminAdvertisementService
                .getAvailableLocations()
                .then(res => {
                    resolve(res.data);
                })
                .catch(err => {
                    reject(err);
                });
        });
    };

    const {isLoading: isLoadingCardTypes, data: card_types} = useQuery(
        ["advertisement-card-types"],
        () => fetchAdCardTypes(),
        {
            enabled: isAdvertisementPerm && !isLoadingConfig && config?.value.toUpperCase() !== "FALSE",
            keepPreviousData: true,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const locationsQuery = useQuery<any>(["advertisement-locations"], () => fetchAdLocations(), {
        enabled: !isLoadingConfig && config?.value.toUpperCase() !== "FALSE",
        keepPreviousData: true,
        ...DEFAULT_QUERY_CONFIGS(),
    });

    return {
        isLoadingCardTypes,
        card_types,
        isLoadingLocations: locationsQuery.isLoading,
        locations: locationsQuery.data,
    };
};

export default useAdvertisementMeta;
