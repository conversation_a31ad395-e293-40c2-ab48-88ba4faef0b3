import useActiveCompany from "./useActiveCompany";
import useAppConfig from "./useAppConfig";
import useAuthState from "./useAuthState";
import {useFeatureFlags} from "./useFeatureFlags";

const useIsPlaidAvailable = () => {
    const {isFlagActivated, isLoadingFeatureFlags: isLoadingPlaidEnabled} = useFeatureFlags();
    const {activeCompany, isLoading: isLoadingActiveCompany} = useActiveCompany();
    const {authState} = useAuthState();
    const isPlaidFlagEnabled = isFlagActivated("PLAID_INTEGRATION");
    const {config, isLoadingConfig} = useAppConfig({config_key: "PLAID_COUNTRY_ALLOW_LIST"});
    const allowedCountries: string[] = isLoadingConfig
        ? []
        : !!config?.value
        ? config?.value?.replace(/\s/g, "").split(",") || []
        : [];

    const checkIsAddressValid = (address?: string | null, country_code?: string | null) => {
        if (!address) return false;
        //? If app config is empty or not added default to allow all countries to use plaid
        if (!isLoadingConfig && allowedCountries.length === 0) return true;
        const uppercaseAllowedCountries = allowedCountries.map(country => country.toUpperCase());
        const addressParts = address.split(", ");
        const lastPart = addressParts[addressParts.length - 1];
        return (
            uppercaseAllowedCountries.includes(lastPart.toUpperCase()) ||
            uppercaseAllowedCountries.includes(country_code || "")
        );
    };

    const isEnabled = isPlaidFlagEnabled && checkIsAddressValid(activeCompany?.address, activeCompany?.country_code);

    return {
        isLoading: isLoadingPlaidEnabled || isLoadingActiveCompany || !authState?.id || isLoadingConfig,
        isAvailable: isEnabled,
    };
};

export default useIsPlaidAvailable;
