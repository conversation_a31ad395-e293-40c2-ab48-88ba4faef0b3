import {useEffect, useState} from "react";
import {useDebounce} from "./useDebounce";

export const useStickyPosition = (
    ref: any,
    options?: {
        disable?: boolean;
        top?: number | string;
        left?: number | string;
        bottom?: number | string;
        right?: number | string;
    },
) => {
    const [isSticky, setIsSticky] = useState(false);
    const debounced = useDebounce(isSticky, 100);
    useEffect(() => {
        if (options?.disable) return;
        let listener: any = null;
        if (ref.current) {
            const stickyElem = ref.current;
            const currStickyPos = stickyElem.getBoundingClientRect().top + window.scrollY;
            listener = window.addEventListener("scroll", () => {
                if (window.scrollY >= currStickyPos) {
                    !isSticky && setIsSticky(true);
                } else if (window.scrollY < currStickyPos) {
                    isSticky && setIsSticky(false);
                }
            });
        }
        return () => {
            if (listener) {
                window.removeEventListener("scroll", listener);
            }
        };
    }, [ref, options]);

    useEffect(() => {
        const stickyElem = ref.current;
        if (stickyElem) {
            stickyElem.style.position = debounced ? "fixed" : "relative";
            stickyElem.style.top = debounced ? options?.top || "0px" : "initial";
        }
    }, [debounced]);

    return {isSticky: debounced};
};
