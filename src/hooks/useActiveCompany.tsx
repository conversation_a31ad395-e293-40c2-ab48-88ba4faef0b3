import {useQuery, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useMemo, useState} from "react";
import type {IActiveCompanyAPI, ICompanyConfigs, ICompanyType} from "../Interfaces/company.interface";
import {ICurrency} from "../Interfaces/currency.interface";
import type {IRole} from "../Interfaces/permissions.interface";
import {DEFAULT_QUERY_CONFIGS} from "../constants/commonStrings.constant";
import CompanyType, {MSPCompanyTypes, VendorCompanyTypes} from "../constants/companyType.constant";
import {ACTIVE_COMPANY_COOKIE, ACTIVE_COMPANY_COOKIE_ID} from "../constants/cookies.contant";
import {VENDOR_TYPES} from "../constants/vendorProfiles.constants";
import {companyService} from "../services/company.service";
import {userService} from "../services/user.service";
import useAuthState from "./useAuthState";
import {useCookieState} from "./useCookieState";
import {DIRECT_PLAN_TYPES} from "../constants/profiles.constant";

interface IUserCompany {
    avatar: string;
    friendly_url: string;
    id: string;
    name: string;
    company_type: ICompanyType;
    is_distributor: boolean;
    parent_id: string;
    role: IRole;
    client_ids: string[];
    subdomain: string | null;
}

export type TUserCompanies = IUserCompany[];

interface IChannelCommandMenuCounts {
    total_approved_product_review: number;
    total_pending_request: number;
    total_accept_request: number;
}

interface IMSPCounts {
    total_saved_template: number;
    total_folder: number;
}

export interface IActiveCompany extends IActiveCompanyAPI {
    avatar: string;
    counts?: {
        vendor?: IChannelCommandMenuCounts;
        msp?: IMSPCounts;
    };
    config?: ICompanyConfigs;
    currency?: ICurrency;
}

export default function useActiveCompany(): {
    activeCompany: IActiveCompany | undefined;
    setActiveCompany: (friendly_url: string, id: string) => void;
    clearActiveCompany: () => void;
    selectedFriendlyUrl: string;
    selectedId: string;
    isLoading: boolean;
    isMSP: boolean;
    isBasicMSP: boolean;
    isClientMsp: boolean;
    isAffiliateMsp: boolean;
    hasIntegrationsAccess: boolean;
    clientMspHasNoExpensesAccess?: boolean;
    isDirect: boolean;
    isVendor: boolean;
    isMSPLocation: boolean;
    isAffiliateCorporation: boolean;
    userCompanies: TUserCompanies | undefined;
    isLoadingUserCompanies: boolean;
    companyConfigs: ICompanyConfigs | undefined;
    isLoadingConfigs: boolean;
    companyCurrency?: ICurrency;
    isPremiumMSP: boolean;
    hasExpensesAccess?: boolean;
    isParentMSPActingAsCustomer: boolean;
    isPremiumDirect: boolean;
    isDirectBasic: boolean;
} {
    const [storedCompanyFriendlyUrl, setStoredCompanyFriendlyUrl] = useCookieState("", ACTIVE_COMPANY_COOKIE);
    const [storedCompanyId, setStoredCompanyId] = useCookieState("", ACTIVE_COMPANY_COOKIE_ID);
    const [isSwitchingCompanies, setIsSwitchingCompanies] = useState(false);
    const {authState} = useAuthState();
    const userMainCompanyFriendlyUrl = authState?.company_friendly_url;
    const userMainCompanyId = authState?.company_id;
    const queryClient = useQueryClient();

    const {
        data: {companyFriendlyUrl, companyId},
    } = useQuery<{
        companyFriendlyUrl: string;
        companyId: string;
    }>(["activeCompanyFriendlyUrl"], {
        initialData: {companyFriendlyUrl: storedCompanyFriendlyUrl, companyId: storedCompanyId},
        enabled: false,
    });

    const companyQuery = useQuery<IActiveCompanyAPI>(
        ["active-company-api", companyFriendlyUrl],
        async () => {
            if (isSwitchingCompanies || !authState?.authenticated) return Promise.resolve({} as IActiveCompanyAPI);
            return companyService.getActiveCompany(companyFriendlyUrl).then(r => {
                setIsSwitchingCompanies(false);
                return r.data;
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(companyFriendlyUrl) && !isSwitchingCompanies && !!authState?.authenticated,
            refetchOnWindowFocus: true,
            /**
             * ! ATTENTION
             * staleTime sets how often the query will be "needing" to be refetched
             * by default at DEFAULT_QUERY_CONFIGS() it is set to 10min, by lowering this too much, we create a problem
             * react-query is refetching it more than needed and doing an assertion to check if the data is the same
             * but this is causing the app to re-render more than needed
             * And because of that, any portal (like MenuButton) which are temporarily added to the DOM, will be removed
             * and added again, causing a flickering effect (if the state of creation is persisted in parent) or just close (like a dropdown)
             */
            //staleTime: 0,
        },
    );
    const companyInfo = companyQuery.data;

    const companiesQuery = useQuery<TUserCompanies>(
        ["user-companies", authState?.id],
        () => {
            return new Promise<TUserCompanies>((resolve, reject) => {
                userService
                    .getCompanies()
                    .then(res => {
                        resolve(res.data);
                    })
                    .catch(err => reject(err));
            });
        },
        {
            enabled:
                !!authState.authenticated &&
                !!authState?.id &&
                Boolean(companyFriendlyUrl) &&
                !isSwitchingCompanies &&
                Boolean(storedCompanyId),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const userCompanies = companiesQuery.data;
    const isLoadingUserCompanies = companiesQuery.isLoading;

    const companyConfigsQuery = useQuery<ICompanyConfigs>(
        ["currencies", companyInfo?.id],
        async () => {
            const promiseToReturn: Promise<ICompanyConfigs> = new Promise((resolve, reject) =>
                companyService
                    .getCompanyConfigs(companyInfo?.id!)
                    .then((r: AxiosResponse<ICompanyConfigs>) => resolve(r.data))
                    .catch((e: AxiosError) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            enabled: !!companyInfo?.id && !!authState?.authenticated,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const isLoadingConfigs = companyConfigsQuery.isLoading;
    const currency = companyConfigsQuery.data?.currency;

    const activeCompany = companyInfo
        ? {
              ...companyInfo,
              avatar: companyInfo.profile_images?.CompanyAvatar?.[0].src ?? "",
              counts: {
                  vendor: {
                      total_approved_product_review: companyInfo.total_approved_product_review ?? 0,
                      total_pending_request: companyInfo.total_pending_request ?? 0,
                      total_accept_request: companyInfo.total_accept_request ?? 0,
                  },
                  msp: {
                      total_saved_template: companyInfo.total_saved_template ?? 0,
                      total_folder: companyInfo.total_folder ?? 0,
                  },
              },
              config: companyConfigsQuery.data,
              currency,
          }
        : undefined;
    const isMSP = activeCompany?.company_type ? MSPCompanyTypes.includes(activeCompany?.company_type) : false;
    const isVendor = activeCompany?.company_type ? VendorCompanyTypes.includes(activeCompany?.company_type) : false;
    const isClientMsp = activeCompany?.company_type === CompanyType.MSP_CLIENT;
    const isAffiliateMsp = activeCompany?.company_type === CompanyType.FRANCHISE_MSP;
    const isMSPLocation = activeCompany?.company_type === CompanyType.MSP_LOCATION;
    const isAffiliateCorporation = activeCompany?.company_type === CompanyType.FRANCHISE_CORPORATE_MSP;
    const hasIntegrationsAccess = !!(activeCompany?.manage_clients && isMSP);
    const hasExpensesAccess = ( !!activeCompany?.manage_expenses && !isVendor ) || (isClientMsp && activeCompany?.client_plaid_integration_enabled);
    const isDirect = activeCompany?.company_type === CompanyType.DIRECT;
    const isDirectBasic = isDirect && activeCompany?.company_profile_type?.value === DIRECT_PLAN_TYPES.DIRECT_BASIC;
    const isPremiumDirect = isDirect && activeCompany?.company_profile_type?.value === DIRECT_PLAN_TYPES.DIRECT_PREMIUM;
    const isPremiumMSP =
        (isMSP || isClientMsp) && activeCompany?.company_profile_type?.value === VENDOR_TYPES.MSP_BUSINESS_PREMIUM;
    const isBasicMSP =
        (isMSP || isClientMsp) && activeCompany?.company_profile_type?.value === VENDOR_TYPES.MSP_BUSINESS_BASIC;
    const isParentMSPActingAsCustomer = useMemo(() => {
        return (
            isClientMsp &&
            (companiesQuery.data?.length || 0) > 1 &&
            companiesQuery.data?.findIndex(c => c.id === activeCompany?.client_parent_id) !== -1
        );
    }, [isClientMsp, companiesQuery.data, activeCompany?.client_parent_id]);
    const clientMspHasNoExpensesAccess = isClientMsp && !hasExpensesAccess;
    const setActiveCompanyQueryData = (friendlyUrl: string, id: string) => {
        queryClient.setQueryData(["activeCompanyFriendlyUrl"], {
            companyFriendlyUrl: friendlyUrl,
            companyId: id,
        });
    };

    const clearActiveCompany = () => {
        setStoredCompanyFriendlyUrl("");
        setStoredCompanyId("");
        setActiveCompanyQueryData("", "");
    };

    const setActiveCompany = (friendlyUrl: string, id: string) => {
        setIsSwitchingCompanies(true);
        setActiveCompanyQueryData(friendlyUrl, id);
    };

    useEffect(() => {
        if (!storedCompanyFriendlyUrl && userMainCompanyFriendlyUrl && !storedCompanyFriendlyUrl && userMainCompanyId) {
            setActiveCompanyQueryData(userMainCompanyFriendlyUrl, userMainCompanyId);
        }
    }, [userMainCompanyFriendlyUrl]);

    useEffect(() => {
        storedCompanyId && sessionStorage.setItem("activeCompanyId", storedCompanyId);
        setStoredCompanyFriendlyUrl(() => {
            setIsSwitchingCompanies(false);
            setStoredCompanyId(companyId);
            return companyFriendlyUrl;
        });
    }, [companyFriendlyUrl]);

    return {
        activeCompany,
        setActiveCompany,
        clearActiveCompany,
        selectedFriendlyUrl: storedCompanyFriendlyUrl,
        selectedId: storedCompanyId,
        isLoading: companyQuery.isLoading,
        isMSP,
        isBasicMSP,
        isClientMsp,
        isAffiliateMsp,
        hasIntegrationsAccess,
        isDirect,
        isVendor,
        isMSPLocation,
        isAffiliateCorporation,
        isPremiumMSP,
        userCompanies,
        isLoadingUserCompanies,
        companyConfigs: companyConfigsQuery.data,
        companyCurrency: currency,
        isLoadingConfigs,
        hasExpensesAccess,
        clientMspHasNoExpensesAccess,
        isParentMSPActingAsCustomer,
        isPremiumDirect,
        isDirectBasic,
    };
}
