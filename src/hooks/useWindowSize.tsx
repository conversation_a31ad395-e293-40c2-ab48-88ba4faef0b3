import {useState, useEffect} from "react";
import {useDebounce} from "./useDebounce";

export interface ISize {
    width: number;
    height: number;
}

/**
 * Hook to get the size of the window updated when the window is resized
 * @returns {Object} The size of the window as an object {width, height}
 */
const useWindowSize = (): ISize => {
    const [windowSize, setWindowSize] = useState<ISize>({
        width: 0,
        height: 0,
    });
    const debounced = useDebounce(windowSize, 100);
    useEffect(() => {
        function handleResize() {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight,
            });
        }
        window.addEventListener("resize", handleResize);
        handleResize();
        return () => window.removeEventListener("resize", handleResize);
    }, []);
    return debounced;
};

export default useWindowSize;
