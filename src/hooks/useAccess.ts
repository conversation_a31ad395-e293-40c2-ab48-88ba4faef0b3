import {IRule, PossibleRules} from "../Interfaces/businessRules.interface";
import usePermissions from "./usePermissions";

export function useAccess() {
    const {isSuperAdmin} = usePermissions();

    /**
     * Function that checks if the user has access to a specific feature
     * @param {keyof typeof PossibleRules} feat What feature you're trying to access? Choose one from PossibleRules keys autocomplete should help
     * @param {IRule} profileRules Profile rules answer from api, use companyRules.data of peopleRules.data
     * @returns {boolean}
     */
    const hasAccess: (
        feat: keyof typeof PossibleRules,
        profileRules: IRule[] | undefined,
        options?: {
            ignoreAdmin?: boolean;
        },
    ) => boolean = (feat, profileRules, options) => {
        const {ignoreAdmin = false} = options || {};
        if (profileRules === undefined) return true;
        if (isSuperAdmin && !ignoreAdmin) {
            return true;
        }
        const rule = profileRules?.find(rule => rule.profile_rule === feat);
        if (rule) {
            if (typeof rule.rule_value === "boolean") {
                return !!rule.rule_value;
            } else {
                return rule.rule_value > 0;
            }
        }
        return true;
    };

    const getLimit: (feat: keyof typeof PossibleRules, profileRules: IRule[]) => number | undefined = (
        feat,
        profileRules,
    ) => {
        const rule = profileRules?.find(rule => rule.profile_rule === feat);
        if (rule) {
            if (rule.value_type === "NumericType") {
                return Number(rule.rule_value);
            } else {
                return undefined;
            }
        }
        return undefined;
    };

    return {hasAccess, getLimit};
}
