import {useLocation} from "react-router-dom";
import {USERS_TYPE} from "../enums/usersTypes.enum";
import {useGetFriendlyUrl} from "../utils/profile.util";
import {useCompany} from "./fetches/useCompany";
import useActiveCompany from "./useActiveCompany";
import CompanyType from "../constants/companyType.constant";

const useClientMSP = (mspFriendlyUrl?: string) => {
    const location = useLocation();
    const route_friendly_url = useGetFriendlyUrl(location.pathname);
    const msp_friendly_url = mspFriendlyUrl || route_friendly_url;
    const { company } = useCompany(msp_friendly_url, {isCompany: true, calls: {all: false, company: true}});
    const { activeCompany } = useActiveCompany();
    return{
        activeCompany: company?.data || activeCompany
    }
};

export default useClientMSP;
