import {useState, useEffect, useCallback, useRef} from "react";
import useWindowSize from "./useWindowSize";
import {useDebounce} from "./useDebounce";

const useElementSize = (ref, options?: {ignoreInnerHtmlChange?: boolean}) => {
    const [size, setSize] = useState({width: 0, height: 0});
    const windowSize = useWindowSize();
    const debouncedWindowSize = useDebounce(windowSize);
    const hasCapturedSize = useRef<boolean>(false);

    const updateSize = () => {
        if (ref.current) {
            setSize({
                width: ref.current.offsetWidth,
                height: ref.current.offsetHeight,
            });
            hasCapturedSize.current = true;
        }
    };

    useEffect(() => {
        if (!ref.current || (options?.ignoreInnerHtmlChange && hasCapturedSize.current)) return;

        updateSize();

        const observer = new ResizeObserver(() => {
            updateSize();
        });

        observer.observe(ref.current);

        return () => {
            observer.disconnect();
        };
    }, [debouncedWindowSize, options]);

    return size;
};

export default useElementSize;
