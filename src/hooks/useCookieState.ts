import {useEffect, useState, type Dispatch, type SetStateAction} from "react";
import {getDomainForCookies} from "../utils/subdomain.util";
import useCookies from "./useCookies";

interface ICookieStateOptions {
    /** Number of hours until cookie gets expired  */
    expireIn?: number;
    /** Domain to be used to set the cookie  */
    domain?: string;
    /** Path to be used to set the cookie  */
    path?: string;
    /** If true, cookie will be set with secure flag  */
    secure?: boolean;
    /** If sent, cookie will be set with sameSite flag  */
    sameSite?: "strict" | "lax" | "none";
    /** If true, cookie will be set with httpOnly flag  */
    httpOnly?: boolean;
}

/**
 * Hook to manage a state that will be stored in a cookie.
 * The default value is what we have store in the cookie otherwise initial value.
 * @param key The key of the cookie.
 * @param initialValue The initial value of the state.
 * @returns The state and a function to update it.
 */
export function useCookieState<T>(
    initialValue: T,
    key: string,
    options?: ICookieStateOptions,
): [T, Dispatch<SetStateAction<T>>] {
    const [cookieValue, setCookie] = useCookies({
        cookieKey: key,
        expireIn: options?.expireIn || 168, //1 week default
        domain: options?.domain || getDomainForCookies(),
        path: options?.path || "/",
        secure: options?.secure || false,
        sameSite: options?.sameSite || "strict",
        httpOnly: options?.httpOnly || false,
    });
    const [state, setState] = useState<T>(() => {
        return cookieValue ? (cookieValue as T) : initialValue;
    });

    useEffect(() => {
        setCookie(JSON.stringify(state));
    }, [key, state]);

    return [state, setState];
}
