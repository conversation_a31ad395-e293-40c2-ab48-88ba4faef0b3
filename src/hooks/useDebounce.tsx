import {useEffect, useRef, useState} from "react";
import useNotification from "./useNotification";
export function useDebounce<T>(
    value: T,
    delay: number = 1000,
    options?: {
        minLength?: number;
        minLengthNotify?: string;
    },
) {
    const firstMount = useRef(true);
    const [debouncedValue, setDebouncedValue] = useState(value);
    const notify = useNotification();
    useEffect(() => {
        const handler = setTimeout(() => {
            if (options && !firstMount.current) {
                if (
                    options?.minLength &&
                    (typeof value === "string" || Array.isArray(value)) &&
                    value?.length < options?.minLength &&
                    value
                ) {
                    options?.minLengthNotify && notify(options?.minLengthNotify, "Error");
                    return;
                }
            }
            firstMount.current = false;
            setDebouncedValue(value);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
        // eslint-disable-next-line
    }, [value, delay]);
    return debouncedValue;
}
