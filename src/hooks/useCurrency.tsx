import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useRef} from "react";
import {DEFAULT_QUERY_CONFIGS} from "../constants/commonStrings.constant";
import {ICompanyConfigs} from "../Interfaces/company.interface";
import {ICurrency, ICurrencyWithExchange} from "../Interfaces/currency.interface";
import {companyService} from "../services/company.service";
import {currencyService} from "../services/currency.service";
import FormatNumber from "../utils/formatNumber.util";
import {useQueryHelper} from "./helpers/useQueryHelper";
import useActiveCompany from "./useActiveCompany";
import useNotification from "./useNotification";

interface UseCurrencyProps {
    /** Optional company ID to override the active company */
    companyId?: string;
}

interface UseCurrencyReturn {
    /** Formats a number to a currency string without symbol */
    formatCurrency: (amount: number, currency?: string) => string;
    /** Current exchange rates for the company's currency */
    exchangeRates: ICurrencyWithExchange;
    /** The active company's currency configuration */
    companyCurrency: ICurrency | undefined;
    /** List of all available currencies */
    allCurrencies: ICurrency[];
    /**
     * Converts an amount between currencies
     * @param amount - The amount to convert
     * @param currency - The target currency code
     * @param baseAmountIsTargetCurrency - If true, converts from target currency to company currency
     */
    convertCurrency: (amount: number, currency: string, baseAmountIsTargetCurrency?: boolean) => number;
    /**
     * Updates the company's default currency
     * @param currency_id - The ID of the new currency
     * @param companyId - Optional company ID to override the active company
     */
    updateCompanyCurrency: (currency_id: string, companyId?: string) => Promise<void>;
}

/**
 * Hook for managing currency operations including formatting, conversion, and company currency settings
 *
 * @example
 * ```typescript
 * const { formatCurrency, convertCurrency, companyCurrency } = useCurrency();
 *
 * // Format amount in company's currency
 * const formatted = formatCurrency(1234.56); // "1,234.56"
 *
 * // Convert amount to EUR
 * const converted = convertCurrency(100, "EUR"); // converts from company currency to EUR
 *
 * // Convert amount from EUR to company currency
 * const convertedBack = convertCurrency(100, "EUR", true);
 * ```
 *
 * @param props - Configuration options for the hook
 * @returns Object containing currency utility functions and data
 */
export default function useCurrency({companyId}: UseCurrencyProps = {}): UseCurrencyReturn {
    const {activeCompany} = useActiveCompany();
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const notify = useNotification();
    const {invalidateMatchedQueries} = useQueryHelper();
    const finalCompanyId = companyId || activeCompany?.id;

    const allCurrenciesQuery = useQuery<ICurrency[]>(
        ["all-currencies"],
        async () => {
            const promiseToReturn: Promise<ICurrency[]> = new Promise((resolve, reject) =>
                currencyService
                    .getAllCurrencies()
                    .then((r: AxiosResponse<ICurrency[]>) => resolve(r.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const allCurrencies = allCurrenciesQuery.data || [];

    const companyConfigsQuery = useQuery<ICompanyConfigs>(
        ["currencies", finalCompanyId],
        async () => {
            const promiseToReturn: Promise<ICompanyConfigs> = new Promise((resolve, reject) =>
                companyService
                    .getCompanyConfigs(finalCompanyId!)
                    .then((r: AxiosResponse<ICompanyConfigs>) => resolve(r.data))
                    .catch((e: AxiosError) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            enabled: !!finalCompanyId,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const companyCurrency = companyConfigsQuery.data?.currency;

    const formatCurrency = (amount: number, currency?: string): string => {
        return FormatNumber.currencyWithoutSymbol(amount, currency || companyCurrency?.key || "USD");
    };

    const currencyConversionQuery = useQuery<ICurrencyWithExchange>(
        ["currency-conversion", companyCurrency?.id],
        () => {
            return new Promise<ICurrencyWithExchange>((resolve, reject) => {
                currencyService
                    .getConversions(companyCurrency?.id!)
                    .then(r => {
                        resolve(r.data);
                    })
                    .catch(reject);
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!companyCurrency?.id,
        },
    );
    const exchangeRates = currencyConversionQuery.data || ({} as ICurrencyWithExchange);

    const convert = (amount: number, currency: string, baseAmountIsTargetCurrency?: boolean): number => {
        const rate = exchangeRates.exchange_rates?.[0]?.rates?.[currency]?.rate || 1;
        if (baseAmountIsTargetCurrency) {
            return Math.round((amount / rate) * 100) / 100;
        }
        return Math.round(amount * rate * 100) / 100;
    };

    const convertCurrency = (amount: number, currency: string, baseAmountIsTargetCurrency?: boolean): number => {
        if (currencyConversionQuery.isLoading) {
            if (timeoutRef.current) clearTimeout(timeoutRef.current);
            timeoutRef.current = setTimeout(() => {
                convertCurrency(amount, currency, baseAmountIsTargetCurrency);
            }, 750);
            return amount;
        }
        return convert(amount, currency, baseAmountIsTargetCurrency);
    };

    const updateCompanyCurrency = async (currency_id: string, companyId?: string) => {
        const company = companyId ?? activeCompany?.id ?? "";
        const configs = {
            currency_id,
        };
        await companyService
            .updateCompanyConfigs(company, configs)
            .then(() => {
                notify("Currency updated successfully", "Success");
                invalidateMatchedQueries(["currencies", activeCompany?.id]);
            })
            .catch(() => {
                notify("Failed to update currency", "Error");
            });
    };

    useEffect(() => {
        return () => {
            if (timeoutRef.current) clearTimeout(timeoutRef.current);
        };
    }, []);

    return {formatCurrency, exchangeRates, companyCurrency, allCurrencies, convertCurrency, updateCompanyCurrency};
}
