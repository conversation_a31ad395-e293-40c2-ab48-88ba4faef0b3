import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UseQ<PERSON>yR<PERSON>ult, useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {IBlog} from "../../Interfaces/blog.interface";
import {IRule} from "../../Interfaces/businessRules.interface";
import {IContentCounts} from "../../Interfaces/contentFeed.interface";
import {IFollowers} from "../../Interfaces/followers.interface";
import {IMediaGallery} from "../../Interfaces/mediaGallery.interface";
import {IPeople} from "../../Interfaces/people.interface";
import {IVideosLoad} from "../../Interfaces/profiles.interface";
import {IMutateData, IUpdatingQuery} from "../../Interfaces/queries.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {USER_NOT_PUBLIC} from "../../constants/errorMessages.constant";
import {followCompanyService} from "../../services/followCompany.service";
import {mediaGalleryService} from "../../services/mediaGallery.service";
import {profileService} from "../../services/profile.service";
import {getErrorFromArray} from "../../utils/error.util";
import useAuthState from "../useAuthState";
import useNotification from "../useNotification";
import usePeopleMutations from "./usePeopleMutations";

interface IOptions {
    isCompany?: boolean;
    refreshAfterMutate?: boolean;
    useID?: boolean;
    calls?: {
        people?: boolean;
        peopleVideos?: boolean;
        peopleBlogs?: boolean;
        peopleGalleries?: boolean;
        peopleCounts?: boolean;
        peopleFollowings?: boolean;
        peopleRules?: boolean;
        all?: boolean;
    };
}

interface IUsePeople {
    people: UseQueryResult<IPeople | undefined, unknown>;
    peopleVideos: UseQueryResult<IVideosLoad | undefined, unknown>;
    peopleBlogs: UseQueryResult<IBlog[] | undefined, unknown>;
    peopleGalleries: UseQueryResult<IMediaGallery[] | undefined, unknown>;
    peopleCounts: UseQueryResult<IContentCounts | undefined, unknown>;
    peopleFollowings: UseQueryResult<IFollowers[] | undefined, unknown>;
    peopleRules: UseQueryResult<IRule[] | undefined, unknown>;
    isUpdating: IUpdatingQuery;
    isUpdatingDispatcher: React.Dispatch<any>;
    updatePeople: UseMutationResult<
        AxiosResponse<any, any>,
        AxiosError<any, any>,
        IMutateData,
        {currentPeople: IPeople | undefined} | {currentPeople?: undefined}
    >;
}

export function usePeople(friendly_url: string, options?: IOptions): IUsePeople {
    const {updatePeople, isUpdating, isUpdatingDispatcher} = usePeopleMutations(friendly_url, options);
    const {authState} = useAuthState();
    const notify = useNotification();

    const shouldCall = (call: string) => {
        if (options?.calls) {
            const allValue = options.calls["all"];
            const value = options.calls[call];
            return value === undefined ? (allValue === undefined ? true : allValue) : value;
        }
        return true;
    };

    const people = useQuery<IPeople | undefined>(
        ["people", !options?.isCompany ? friendly_url : "fake"],
        () => {
            if (!options?.isCompany && friendly_url && shouldCall("people")) {
                const promiseToReturn: Promise<IPeople | any> = new Promise((resolve, reject) =>
                    options?.useID
                        ? profileService
                              .loadInfo(friendly_url)
                              .then((r: AxiosResponse<IPeople>) => {
                                  const people: IPeople = {
                                      ...r.data,
                                      name: r.data.first_name + " " + r.data.last_name,
                                  };
                                  resolve(people);
                              })
                              .catch((e: AxiosError<any>) => {
                                  if (e.response?.data?.message === USER_NOT_PUBLIC) {
                                      return notify(getErrorFromArray(e), "Error");
                                  }
                                  reject(e);
                              })
                        : profileService
                              .loadByFriendlyUrl(friendly_url)
                              .then((r: AxiosResponse<IPeople>) => {
                                  const people: IPeople = {
                                      ...r.data,
                                      name: r.data.first_name + " " + r.data.last_name,
                                  };
                                  resolve(people);
                              })
                              .catch((e: AxiosError<any>) => {
                                  if (e.response?.data?.message === USER_NOT_PUBLIC) {
                                      return notify(getErrorFromArray(e), "Error");
                                  }
                                  reject(e);
                              }),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !options?.isCompany && Boolean(friendly_url) && shouldCall("people"),
        },
    );

    const peopleVideos = useQuery<IVideosLoad | undefined>(
        ["people-videos", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleVideos")) {
                const promiseToReturn: Promise<IVideosLoad | any> = new Promise((resolve, reject) =>
                    profileService
                        .loadUserVideos(people.data?.id!)
                        .then((r: AxiosResponse<IVideosLoad>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleVideos"), ...DEFAULT_QUERY_CONFIGS()},
    );

    const peopleBlogs = useQuery<IBlog[] | undefined>(
        ["people-blogs", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleBlogs")) {
                const promiseToReturn: Promise<IBlog[] | any> = new Promise((resolve, reject) =>
                    profileService
                        .getBlogs(friendly_url, false)
                        .then((r: AxiosResponse<IBlog[]>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleBlogs"), ...DEFAULT_QUERY_CONFIGS()},
    );

    const peopleGalleries = useQuery<IMediaGallery[] | undefined>(
        ["people-galleries", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleGalleries")) {
                const promiseToReturn: Promise<IMediaGallery[] | any> = new Promise((resolve, reject) =>
                    mediaGalleryService
                        .getMediaGalleryById(people.data?.id!)
                        .then((r: AxiosResponse<IMediaGallery[]>) => {
                            const sortedGalleries = r.data.sort((a, b) =>
                                DateTime.fromISO(b.created_at).diff(DateTime.fromISO(a.created_at)).as("seconds"),
                            );
                            resolve(sortedGalleries);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleGalleries"), ...DEFAULT_QUERY_CONFIGS()},
    );

    const peopleCounts = useQuery<IContentCounts | undefined>(
        ["people-counts", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleCounts")) {
                const promiseToReturn: Promise<IContentCounts | any> = new Promise((resolve, reject) =>
                    profileService
                        .getUserContentCounts(people.data?.id!)
                        .then((r: AxiosResponse<IContentCounts>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleCounts"), ...DEFAULT_QUERY_CONFIGS()},
    );

    const peopleFollowings = useQuery<IFollowers[] | undefined>(
        ["people-followings", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleFollowings")) {
                const promiseToReturn: Promise<IFollowers[] | any> = new Promise((resolve, reject) =>
                    followCompanyService
                        .getAllUserFollowers(people.data?.id!)
                        .then((r: AxiosResponse<any>) => resolve(r.data?.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleFollowings"), ...DEFAULT_QUERY_CONFIGS()},
    );

    const peopleRules = useQuery<IRule[] | undefined>(
        ["people-rules", friendly_url],
        () => {
            if (!options?.isCompany && shouldCall("peopleRules") && authState.authenticated) {
                const promiseToReturn: Promise<IRule[] | any> = new Promise((resolve, reject) =>
                    profileService
                        .getRules(friendly_url, false)
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return Promise.resolve(undefined);
        },
        {enabled: !!people.data && shouldCall("peopleRules") && authState.authenticated, ...DEFAULT_QUERY_CONFIGS()},
    );

    return {
        people,
        updatePeople,
        peopleVideos,
        peopleBlogs,
        peopleGalleries,
        peopleCounts,
        peopleFollowings,
        peopleRules,
        isUpdating,
        isUpdatingDispatcher,
    };
}
