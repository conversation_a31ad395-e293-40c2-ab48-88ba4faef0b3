import {useQuery, type UseQueryResult} from "@tanstack/react-query";
import type {AxiosError, AxiosResponse} from "axios";
import {Dispatch, SetStateAction, useState} from "react";
import {
    IContract,
    IContractsKPIs,
    IMspContractFirstLoadResponse,
    IMspContractFirstLoadState,
} from "../../Interfaces/mspContracts.interface";
import type {IUpdatingQuery} from "../../Interfaces/queries.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {filterService} from "../../services/filters.service";
import {mspContractsService} from "../../services/mspContract.service";
import {buildFilters} from "../../utils/filters.util";
import useMspContractsMutations from "./useMspContractsMutations";
import {getDateXDaysFromNow} from "../../utils/formatDate";

export interface IRecurrenceType {
    id: string;
    key: string;
    name: string;
    description: null;
    order: number;
    intervals: [
        {
            id: string;
            recurrence_id: string;
            key: string;
            name: string;
            description: null;
            order: number;
            interval: number;
            created_at: string;
            updated_at: string;
        },
        {
            id: string;
            recurrence_id: string;
            key: string;
            name: string;
            description: null;
            order: number;
            interval: number;
            created_at: string;
            updated_at: string;
        },
    ];
    created_at: string;
    updated_at: string;
}

type TRecurrenceTypes = IRecurrenceType[];

interface IOptions {
    refreshAfterMutate?: boolean;
    calls?: {
        firstLoad?: boolean;
        contractTypes?: boolean;
        billingTypes?: boolean;
        contractBillingTypeOptions?: boolean;
        upcomingRenewals?: boolean;
        frequencyOptions?: boolean;
        subscriptionCalendarView?: boolean;
        subscriptionListView?: boolean;
        contractAgreementTypes?: boolean;
        kpis?: boolean;
        notificationTypes?: boolean;
        all?: boolean;
    };
    calendarViewFilters?: any;
    upcomingRenewalsFilters?: any;
    defaultListViewFilters?: any;
}

interface IUseMspContracts {
    firstLoad: UseQueryResult<IMspContractFirstLoadState | undefined, unknown>;
    billingTypes: UseQueryResult<any>;
    contractTypes: UseQueryResult<any>;
    contractNotificationTypes: UseQueryResult<any>;
    contractBillingTypeOptions: UseQueryResult<any>;
    subscriptionListView: UseQueryResult<any>;
    subscriptionCalendarView: UseQueryResult<any>;
    frequencyOptions: UseQueryResult<TRecurrenceTypes>;
    upcomingRenewals: UseQueryResult<IContract[]>;
    filterStateUpcomingRenewals: [
        {startDate: string; endDate: string},
        Dispatch<SetStateAction<{startDate: string; endDate: string}>>,
    ];
    listViewFilterState: [any, Dispatch<SetStateAction<any>>];
    calendarViewFilterState: [any, Dispatch<SetStateAction<any>>];
    contractAgreementTypes: UseQueryResult<any>;
    isUpdating: IUpdatingQuery;
    updateMspContracts: any;
    isUpdatingDispatcher: any;
    kpis: UseQueryResult<IContractsKPIs | undefined>;
}

export function useMspContracts(msp_id?: string, options?: IOptions): IUseMspContracts {
    const [filterStateUpcomingRenewals, setFilterStateUpcomingRenewals] = useState<{
        startDate: string;
        endDate: string;
    }>({startDate: new Date().toISOString(), endDate: getDateXDaysFromNow(180)});
    const [listViewFilters, setListViewFilters] = useState<any>(options?.defaultListViewFilters);
    const [calendarViewFilters, setCalendarViewFilters] = useState<any>();
    const listViewQueryKey = ["subscriptionListView", msp_id, JSON.stringify(listViewFilters)];
    const calendarViewQueryKey = ["subscriptionCalendarView", msp_id, JSON.stringify(calendarViewFilters)];
    const mspUpcomingRenewalsQueryKey = ["mspUpcomingRenewals", msp_id, JSON.stringify(filterStateUpcomingRenewals)];
    const {updateMspContracts, isUpdating, isUpdatingDispatcher} = useMspContractsMutations(msp_id, {
        ...options,
        listViewQueryKey,
    });

    const shouldCall = (call: keyof NonNullable<IOptions["calls"]>) => {
        if (options?.calls) {
            const allValue = options.calls["all"];
            const value = options.calls[call];
            return value === undefined ? (allValue === undefined ? true : allValue) : value;
        }
        return true;
    };

    const firstLoad = useQuery<IMspContractFirstLoadResponse | undefined>(
        ["firstLoad", msp_id],
        async () => {
            if (msp_id && shouldCall("firstLoad")) {
                const promiseToReturn: Promise<IMspContractFirstLoadResponse | any> = new Promise((resolve, reject) =>
                    mspContractsService
                        .getMspContractsFirstLoad(msp_id)
                        .then((r: AxiosResponse<IMspContractFirstLoadResponse>) => {
                            const filters = buildFilters(r.data.filters || {}, {});
                            resolve({
                                ...r.data,
                                filters,
                            });
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(msp_id) && shouldCall("firstLoad"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const kpis = useQuery<IContractsKPIs | undefined>(
        ["contracts-kpis", msp_id],
        async () => {
            const promiseToReturn: Promise<IContractsKPIs> = new Promise((resolve, reject) =>
                mspContractsService
                    .getContractsKPIs(msp_id!)
                    .then((r: AxiosResponse<IContractsKPIs>) => {
                        resolve(r.data);
                    })
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            enabled: Boolean(msp_id) && shouldCall("kpis"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const subscriptionListView = useQuery<IContract[] | undefined>(
        listViewQueryKey,
        async () => {
            if (msp_id && shouldCall("subscriptionListView") && listViewFilters) {
                const promiseToReturn: Promise<IContract[] | any> = new Promise((resolve, reject) =>
                    mspContractsService
                        .loadSubscriptionListView(msp_id, listViewFilters)
                        .then((r: AxiosResponse<IContract[]>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(msp_id) && shouldCall("subscriptionListView"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const subscriptionCalendarView = useQuery<IContract[] | undefined>(
        calendarViewQueryKey,
        async () => {
            if (
                msp_id &&
                shouldCall("subscriptionCalendarView") &&
                calendarViewFilters &&
                calendarViewFilters?.dynamic?.start_date &&
                calendarViewFilters?.dynamic?.end_date
            ) {
                const promiseToReturn: Promise<IContract[] | any> = new Promise((resolve, reject) =>
                    mspContractsService
                        .loadSubscriptionCalendarView(msp_id, calendarViewFilters)
                        .then((r: AxiosResponse<IContract[]>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled:
                Boolean(msp_id) &&
                shouldCall("subscriptionCalendarView") &&
                !!calendarViewFilters?.dynamic?.start_date &&
                !!calendarViewFilters?.dynamic?.end_date,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const upcomingRenewals = useQuery<any | undefined>(
        mspUpcomingRenewalsQueryKey,
        async () => {
            if (
                msp_id &&
                shouldCall("upcomingRenewals") &&
                filterStateUpcomingRenewals.startDate &&
                filterStateUpcomingRenewals.endDate
            ) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    mspContractsService
                        .getUpcomingRenewals(
                            msp_id,
                            filterStateUpcomingRenewals.startDate,
                            filterStateUpcomingRenewals.endDate,
                        )
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("upcomingRenewals") && !!msp_id,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const billingTypes = useQuery<any | undefined>(
        ["mspContractBillingTypes"],
        async () => {
            if (msp_id && shouldCall("billingTypes")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    filterService
                        .getMspContractBillingTypes()
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("billingTypes"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const contractTypes = useQuery<any | undefined>(
        ["mspContractTypes"],
        async () => {
            if (msp_id && shouldCall("contractTypes")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    filterService
                        .getMspContractTypes()
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("contractTypes"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const contractNotificationTypes = useQuery<any | undefined>(
        ["mspContractNotificationTypes"],
        async () => {
            if (msp_id && shouldCall("notificationTypes")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    filterService
                        .getContractNotificationTypes()
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("notificationTypes"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const contractBillingTypeOptions = useQuery<any | undefined>(
        ["mspContractBillingTypeOptions"],
        async () => {
            if (msp_id && shouldCall("contractBillingTypeOptions")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    filterService
                        .getContractBillingTypeOptions()
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("contractBillingTypeOptions"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const frequencyOptions = useQuery<TRecurrenceTypes>(
        ["frequencyOptions"],
        async () => {
            const promiseToReturn: Promise<TRecurrenceTypes> = new Promise((resolve, reject) =>
                filterService
                    .getFrequencyOptions()
                    .then((r: AxiosResponse<TRecurrenceTypes>) => resolve(r.data))
                    .catch((e: AxiosError) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            enabled: shouldCall("frequencyOptions") && !!msp_id,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const contractAgreementTypes = useQuery<any | undefined>(
        ["contractAgreementTypes"],
        async () => {
            if (msp_id && shouldCall("contractAgreementTypes")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    mspContractsService
                        .getContractAgreementTypes()
                        .then((r: AxiosResponse<any>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("contractAgreementTypes"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    return {
        firstLoad,
        kpis,
        billingTypes,
        contractTypes,
        contractNotificationTypes,
        contractBillingTypeOptions,
        subscriptionListView,
        subscriptionCalendarView,
        upcomingRenewals,
        frequencyOptions,
        filterStateUpcomingRenewals: [filterStateUpcomingRenewals, setFilterStateUpcomingRenewals],
        listViewFilterState: [listViewFilters, setListViewFilters],
        calendarViewFilterState: [calendarViewFilters, setCalendarViewFilters],
        contractAgreementTypes,
        isUpdating,
        updateMspContracts,
        isUpdatingDispatcher,
    };
}
