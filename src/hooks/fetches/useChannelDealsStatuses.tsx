import {useQuery} from "@tanstack/react-query";
import type {AxiosResponse} from "axios";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {filterService} from "../../services/filters.service";
import {TStatusKey} from "../../Interfaces/channelDeal.interface";

export interface IOptionsResponse {
    id: string;
    name: string;
    lookup_option_id: string;
    order: number;
    show_answer_option: boolean;
    value_key: TStatusKey;
}

export default function useChannelDealsStatuses() {
    const {data, isLoading, refetch} = useQuery(
        ["all-channel-deals-statuses"],
        () => {
            return new Promise<IOptionsResponse[]>((resolve, reject) => {
                filterService
                    .getChannelDealsStatuses()
                    .then((r: AxiosResponse<IOptionsResponse[]>) => {
                        resolve(r.data);
                    })
                    .catch(reject);
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const getStatus = (keyOrId: TStatusKey | string): IOptionsResponse | undefined => {
        return data?.find(status => status.value_key === keyOrId || status.id === keyOrId);
    };

    return {
        channelDealsStatuses: data,
        isLoading,
        refetch,
        getStatus,
    };
}
