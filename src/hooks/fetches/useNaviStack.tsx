import {
    useInfiniteQ<PERSON>y,
    UseInfiniteQueryResult,
    useQuery,
    useQueryClient,
    type UseQueryResult,
} from "@tanstack/react-query";
import type {AxiosError, AxiosResponse} from "axios";
import {useState, type Dispatch, type SetStateAction} from "react";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import type {ICategory} from "../../Interfaces/categories.interface";
import type {INewFilters, IPageInnerHeadFilter} from "../../Interfaces/filters.interface";
import type {IFolder} from "../../Interfaces/folders.interface";
import type {ITemplateData} from "../../Interfaces/msp.interface";
import type {IMissingAdoptionStackRes, IStackCategorization} from "../../Interfaces/myStack.interface";
import type {IPaginationData} from "../../Interfaces/pagination.interface";
import type {IUpdatingQuery} from "../../Interfaces/queries.interface";
import {categoriesService} from "../../services/category.service";
import {companyService} from "../../services/company.service";
import {folderServices} from "../../services/folder.service";
import {mspService} from "../../services/msp.service";
import {mspPartnersService} from "../../services/mspPartners.service";
import {buildFilters, clearFilters} from "../../utils/filters.util";
import useNaviStackMutations from "./useNaviStackMutations";

interface IOptions {
    isCompany?: boolean;
    refreshAfterMutate?: boolean;
    isMSP?: boolean;
    isViewingAsAffiliateParent?: boolean;
    loadRecommendedStack?: boolean;
    customerStack?: boolean;
    companyType?: string;
    calls?: {
        naviStack?: boolean;
        naviStackCategories?: boolean;
        naviStackSavedDocumentsFolders?: boolean;
        naviStackSavedDocuments?: boolean;
        allSavedDocuments?: boolean;
        myStackFilters?: boolean;
        missingAdoptionStack?: boolean;
        all?: boolean;
    };
    defaultMyStackFilters?: INewFilters;
}

interface IUseNaviStack {
    naviStack: UseQueryResult<IStackCategorization[] | undefined, unknown>;
    naviStackCategories: UseQueryResult<ICategory[] | undefined, unknown>;
    naviStackSavedDocuments: UseInfiniteQueryResult<IPaginationData<ITemplateData[]> | undefined, unknown>;
    naviStackSavedDocumentsFolders: UseInfiniteQueryResult<IPaginationData<IFolder[]> | undefined, unknown>;
    isUpdating: IUpdatingQuery;
    updateNaviStack: any;
    isUpdatingDispatcher: any;
    numOfCategoriesFilled?: number;
    searchStateDocs: [string, Dispatch<SetStateAction<string>>];
    filtersStateDocs: [INewFilters | undefined, Dispatch<SetStateAction<INewFilters | undefined>>];
    searchStateFolders: [string, Dispatch<SetStateAction<string>>];
    filtersStateFolders: [INewFilters | undefined, Dispatch<SetStateAction<INewFilters | undefined>>];
    showAllFoldersState: [boolean, Dispatch<SetStateAction<boolean>>];
    allSavedDocuments: UseQueryResult<ITemplateData[] | undefined, unknown>;
    allFoldersData: UseQueryResult<IFolder[]>;
    myStackFilters: UseQueryResult<IPageInnerHeadFilter>;
    myStackSearchState: [string, Dispatch<SetStateAction<string>>];
    myStackFiltersState: [INewFilters | undefined, Dispatch<SetStateAction<INewFilters | undefined>>];
    missingAdoptionStack: UseQueryResult<IMissingAdoptionStackRes | undefined, unknown>;
    missingAdoptionStackParentCategories: {[key: string]: ICategory};
    missingAdoptionStackSubcategories: {[key: string]: ICategory};
}

export function useNaviStack(subject_id: string, options?: IOptions): IUseNaviStack {
    const {updateNaviStack, isUpdating, isUpdatingDispatcher} = useNaviStackMutations(subject_id, options);
    const [searchValueFolders, setSearchValueFolders] = useState<string>("");
    const [filtersFolders, setFiltersFolders] = useState<INewFilters>();
    const [searchValueDocs, setSearchValueDocs] = useState<string>("");
    const [filtersDocs, setFiltersDocs] = useState<INewFilters>();
    const [searchValueMyStack, setSearchValueMyStack] = useState<string>("");
    const [filtersMyStack, setFiltersMyStack] = useState<INewFilters>(options?.defaultMyStackFilters || {});
    const [showAllFolders, setShowAllFolders] = useState<boolean>(false);
    const queryClient = useQueryClient();

    const shouldCall = (call: string) => {
        if (options?.calls) {
            const allValue = options.calls["all"];
            const value = options.calls[call];
            return value === undefined ? (allValue === undefined ? true : allValue) : value;
        }
        return true;
    };

    const naviStack = useQuery<IStackCategorization[]>(
        [
            "naviStack",
            !options?.isCompany ? "fake" : subject_id,
            searchValueMyStack,
            JSON.stringify(clearFilters(filtersMyStack)),
            String(options?.loadRecommendedStack),
            options?.customerStack ? "customerStack" : undefined,
        ],
        async () => {
            const promiseToReturn: Promise<IStackCategorization[]> = new Promise((resolve, reject) =>
                companyService[
                    options?.isViewingAsAffiliateParent
                        ? "getAffiliateStack"
                        : options?.customerStack
                          ? "getCustomerStack"
                          : "getMyStack"
                ]<AxiosResponse<IStackCategorization[]>>(
                    subject_id,
                    {
                        search_word: searchValueMyStack,
                        dynamic: clearFilters({
                            ...(filtersMyStack || {}),
                            is_recommended_stack: !!options?.loadRecommendedStack ? "1" : "0",
                        }),
                    },
                    r => resolve(r.data),
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            enabled: Boolean(subject_id) && shouldCall("naviStack") && options?.isMSP !== false,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
        },
    );

    const categoriesFilledObj = naviStack.data?.reduce(
        (acc: {[id: string]: boolean}, s: IStackCategorization) => {
            if (acc[s.category_id]) return acc;
            acc[s.category_id] = true;
            return acc;
        },
        {} as {[id: string]: boolean},
    );
    const numOfCategoriesFilled = categoriesFilledObj ? Object.keys(categoriesFilledObj)?.length : 0;

    const naviStackCategories = useQuery<ICategory[] | undefined>(
        ["naviStackCategories", options?.customerStack ? "customerStack" : undefined, options?.companyType],
        async () => {
            if (shouldCall("naviStackCategories")) {
                const promiseToReturn: Promise<ICategory[] | any> = new Promise((resolve, reject) =>
                    categoriesService
                        .getNaviStackCategories({companyTypes: options?.companyType})
                        .then((r: AxiosResponse<ICategory[]>) => resolve(r.data))
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return undefined;
        },
        {
            enabled: Boolean(subject_id) && shouldCall("naviStackCategories") && options?.isMSP !== false,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const naviStackSavedDocuments = useInfiniteQuery<IPaginationData<ITemplateData[]>>(
        ["naviStackSavedDocuments", subject_id, searchValueDocs, JSON.stringify(clearFilters(filtersDocs))],
        async ({pageParam = 1}) => {
            const promiseToReturn = new Promise<IPaginationData<ITemplateData[]>>((resolve, reject) =>
                mspPartnersService.getAllSavedDocuments<AxiosResponse<IPaginationData<ITemplateData[]>>>(
                    subject_id,
                    {
                        page: pageParam,
                        items_per_page: 20,
                        search_word: searchValueDocs,
                        dynamic: clearFilters(filtersDocs),
                    },
                    r => resolve(r.data),
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            getNextPageParam: res => {
                return res.meta?.last_page > res.meta?.current_page ? res.meta?.current_page + 1 : null;
            },
            onSettled: () => {
                queryClient.invalidateQueries(["mspCounts"]);
            },
            enabled: Boolean(subject_id) && shouldCall("naviStackSavedDocuments") && options?.isMSP !== false,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
        },
    );

    const naviStackSavedDocumentsFolders = useInfiniteQuery<IPaginationData<IFolder[]>>(
        [
            "naviStackSavedDocumentsFolders",
            subject_id,
            searchValueFolders,
            JSON.stringify(clearFilters(filtersFolders)),
        ],
        async ({pageParam = 1}) => {
            const promiseToReturn: Promise<IPaginationData<IFolder[]>> = new Promise((resolve, reject) =>
                folderServices.getAllFolders<AxiosResponse<IPaginationData<IFolder[]>>>(
                    subject_id,
                    {
                        page: pageParam,
                        items_per_page: showAllFolders ? 20 : 10,
                        search_word: searchValueFolders,
                        dynamic: clearFilters(filtersFolders),
                    },
                    r => resolve(r.data),
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            getNextPageParam: res => {
                return res.meta?.last_page > res.meta?.current_page ? res.meta?.current_page + 1 : null;
            },
            enabled: Boolean(subject_id) && shouldCall("naviStackSavedDocumentsFolders") && options?.isMSP !== false,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
        },
    );

    const allSavedDocuments = useQuery<ITemplateData[]>(
        ["allSavedDocuments", subject_id],
        async () => {
            const promiseToReturn = new Promise<ITemplateData[]>((resolve, reject) =>
                mspPartnersService.getAllSavedDocuments<AxiosResponse<ITemplateData[]>>(
                    subject_id,
                    {},
                    r => resolve(r.data),
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            enabled:
                Boolean(subject_id) &&
                shouldCall("allSavedDocuments") &&
                Boolean(naviStackSavedDocuments.data) &&
                options?.isMSP !== false,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
        },
    );

    const allFoldersData = useQuery<IFolder[]>(
        ["allFoldersData", subject_id],
        async () => {
            const promiseToReturn: Promise<IFolder[]> = new Promise((resolve, reject) =>
                folderServices.getAllFolders<AxiosResponse<IFolder[]>>(subject_id!, {}, r => resolve(r.data), reject),
            );
            return promiseToReturn;
        },
        {
            enabled: Boolean(subject_id) && shouldCall("naviStackSavedDocumentsFolders") && options?.isMSP !== false,
            refetchOnWindowFocus: false,
        },
    );

    const myStackFilters = useQuery(
        ["myStackFilters", !options?.isCompany ? "fake" : subject_id],
        () => {
            return mspService.getMyStackFilters().then(r => {
                return buildFilters(r.data.filters, r.data.sorts);
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: shouldCall("myStackFilters"),
        },
    );

    const missingAdoptionStack = useQuery(
        ["missingAdoptionStack", !options?.isCompany ? "fake" : subject_id],
        () => {
            const promiseToReturn: Promise<IMissingAdoptionStackRes> = new Promise((resolve, reject) =>
                companyService.getMissingAdoptionStack(subject_id, r => resolve(r.data), reject),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled:
                shouldCall("missingAdoptionStack") &&
                !!subject_id &&
                !options?.loadRecommendedStack &&
                !options?.isViewingAsAffiliateParent &&
                !!options?.calls?.missingAdoptionStack &&
                !!options.isCompany,
        },
    );

    const missingAdoptionStackParentCategories = {};
    const missingAdoptionStackSubcategories = {};
    if (missingAdoptionStack?.data?.items?.length) {
        missingAdoptionStack?.data?.items?.forEach(item => {
            if (!naviStack?.data?.find(stack => stack.category_id === item.sub_category?.id)) {
                missingAdoptionStackSubcategories[item.sub_category?.id] = item.sub_category;
            }
            if (
                item.sub_category?.parent &&
                !naviStack?.data?.find(stack => stack.parent_category_id === item.sub_category?.parent?.id)
            ) {
                missingAdoptionStackParentCategories[item.sub_category.parent.id] = item.sub_category.parent;
            }
        });
    }

    return {
        naviStack,
        naviStackCategories,
        isUpdating,
        updateNaviStack,
        isUpdatingDispatcher,
        numOfCategoriesFilled,
        searchStateFolders: [searchValueFolders, setSearchValueFolders],
        filtersStateFolders: [filtersFolders, setFiltersFolders],
        naviStackSavedDocumentsFolders,
        searchStateDocs: [searchValueDocs, setSearchValueDocs],
        filtersStateDocs: [filtersDocs, setFiltersDocs],
        naviStackSavedDocuments,
        showAllFoldersState: [showAllFolders, setShowAllFolders],
        allSavedDocuments,
        allFoldersData,
        myStackFilters,
        myStackSearchState: [searchValueMyStack, setSearchValueMyStack],
        myStackFiltersState: [filtersMyStack, setFiltersMyStack],
        missingAdoptionStack,
        missingAdoptionStackParentCategories,
        missingAdoptionStackSubcategories,
    };
}
