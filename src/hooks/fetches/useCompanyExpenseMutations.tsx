import {useMutation, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {Reducer, useReducer} from "react";
import {IMutateData, IServices, IUpdatingQuery, MutateTypes} from "../../Interfaces/queries.interface";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../useNotification";
import {plaidService} from "../../services/plaid.service";
import {useQueryHelper} from "../helpers/useQueryHelper";
import {IExpenseSubscription, ITransactionItem} from "../../Interfaces/plaid.interface";

const updateReducer = (state: IUpdatingQuery = {} as IUpdatingQuery, payload: {key: MutateTypes; value: boolean}) => {
    return {...state, [payload.key]: payload.value};
};

export default function useCompanyExpenseMutations(company_id?: string) {
    const [isUpdating, isUpdatingDispatcher] = useReducer<Reducer<IUpdatingQuery, any>>(
        updateReducer,
        {} as IUpdatingQuery,
    );
    const notify = useNotification();
    const queryClient = useQueryClient();
    const {updateMatchedQueries, invalidateMatchedQueries} = useQueryHelper();
    const transactionsKey = ["company-expenses-transactions", company_id];
    const transactionsFiltersKey = ["company-expenses-transactions-filters", company_id];
    const subscriptionsKey = ["company-expenses-subscriptions", company_id];
    const subscriptionsFiltersKey = ["company-expenses-subscriptions-filters", company_id];

    const updateServices: IServices = {
        [MutateTypes.LocalUpdate]: newInfo => {
            return new Promise(resolve => resolve(newInfo));
        },
        [MutateTypes.UpdateTransaction]: newInfo => {
            return plaidService.updateTransaction(company_id || "", newInfo.body);
        },
        [MutateTypes.UpdateSubscription]: newInfo => {
            return plaidService.updateSubscription(company_id || "", newInfo.body);
        },
        [MutateTypes.SyncExpenseAccount]: newInfo => {
            return plaidService.syncBankAccount(company_id || "", newInfo?.id || "");
        },
        [MutateTypes.DeleteExpenseAccount]: newInfo => {
            return plaidService.deleteBankAccount(company_id || "", newInfo?.id || "");
        },
        [MutateTypes.BulkUpdateTransaction]: newInfo => {
            return plaidService.bulkUpdateTransaction(company_id || "", newInfo.body);
        },
        [MutateTypes.BulkUpdateSubscription]: newInfo => {
            return plaidService.bulkUpdateSubscription(company_id || "", newInfo.body);
        },
        [MutateTypes.BulkSyncExpenseAccount]: newInfo => {
            return plaidService.bulkSyncBankAccount(company_id || "", newInfo.body);
        },
        [MutateTypes.BulkDeleteExpenseAccount]: newInfo => {
            return plaidService.bulkDeleteBankAccount(company_id || "", newInfo.body);
        },
        [MutateTypes.UpdateExpenseAccountDisplayName]: newInfo => {
            return plaidService.updateExpenseAccount(company_id || "", newInfo?.id || "", newInfo.body);
        },
        [MutateTypes.UpdateAlertSettings]: newInfo => {
            return plaidService.saveAlertSettings(company_id || "", newInfo.body);
        },
    };

    const invalidateCompanyExpensesChartsAndKPIs = () => {
        invalidateMatchedQueries(["company-expenses-all-expenses-breakdown", company_id]);
        invalidateMatchedQueries(["all-expenses-chart-data", company_id]);
        invalidateMatchedQueries(["company-expenses-summary", company_id]);
        invalidateMatchedQueries(["company-expenses-subscription-costs", company_id]);
        invalidateMatchedQueries(["company-expenses-counts", company_id]);
        invalidateMatchedQueries(["search-company-transactions", company_id]);
        invalidateMatchedQueries(["search-company-subscriptions", company_id]);
    };

    const updateCompanyExpenses = useMutation(newInfo => updateServices[newInfo?.mutate_type || "update"](newInfo), {
        // optimistic updated
        onMutate: async (newInfo: IMutateData) => {
            let updatingKey: string = newInfo?.mutate_type;
            await isUpdatingDispatcher({key: updatingKey, value: true});
            if (newInfo.mutate_is_updating_append) {
                isUpdatingDispatcher({
                    key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                    value: true,
                });
            }
            return {};
        },
        onError: (err: AxiosError<any>, newInfo) => {
            if (newInfo.onError) newInfo.onError(err);
            let updatingKey: string = newInfo?.mutate_type;
            isUpdatingDispatcher({key: updatingKey, value: false});
            if (newInfo.mutate_is_updating_append) {
                isUpdatingDispatcher({
                    key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                    value: false,
                });
            }
            if (company_id && err?.code !== "ERR_CANCELED") {
                notify(getErrorFromArray(err), "Error");
            }
        },
        onSuccess: (response: AxiosResponse<any>, newInfo) => {
            switch (newInfo.mutate_type) {
                case MutateTypes.LocalUpdate:
                    break;
                case MutateTypes.UpdateTransaction:
                    let oldTransaction: ITransactionItem | null = null;
                    updateMatchedQueries(transactionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: (old.data || []).map(item => {
                                if (item.id === response.data?.id) {
                                    oldTransaction = item;
                                }
                                return item.id === response.data?.id ? response.data : item;
                            }),
                        };
                    });
                    updateMatchedQueries(["infinite-company-expenses-transactions", company_id], old => {
                        if (!old?.pages) return old;
                        return {
                            ...old,
                            pages: old.pages.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.map(item => {
                                        return item.id === response.data?.id ? response.data : item;
                                    }),
                                };
                            }),
                        };
                    });
                    invalidateMatchedQueries(transactionsFiltersKey);
                    invalidateMatchedQueries(["suggested-expenses-category-detail", company_id]);
                    invalidateCompanyExpensesChartsAndKPIs();
                    notify("Successfully updated transaction", "Success");
                    if (newInfo?.body?.contract_id) {
                        invalidateMatchedQueries(["msp-contract-details", company_id]);
                        invalidateMatchedQueries(["firstLoad", company_id]);
                        invalidateMatchedQueries(["subscriptionListView", company_id]);
                        invalidateMatchedQueries(["contract-expenses-overtime", company_id]);
                        oldTransaction?.contract?.id &&
                            invalidateMatchedQueries({
                                0: "company-expenses-transactions",
                                1: company_id,
                                6: oldTransaction?.contract?.id,
                            });
                    }
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    break;
                case MutateTypes.UpdateSubscription:
                    let oldMatch: IExpenseSubscription | null = null;
                    updateMatchedQueries(subscriptionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: (old.data || []).map(item => {
                                if (item.id === response.data?.id) {
                                    oldMatch = item;
                                }
                                return item.id === response.data?.id ? response.data : item;
                            }),
                        };
                    });
                    updateMatchedQueries(["company-expenses-infinite-subscriptions", company_id], old => {
                        if (!old?.pages) return old;
                        return {
                            ...old,
                            pages: old.pages.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.map(item => {
                                        return item.id === response.data?.id ? response.data : item;
                                    }),
                                };
                            }),
                        };
                    });
                    updateMatchedQueries(["infinite-company-expenses-transactions", company_id], old => {
                        if (!old?.pages) return old;
                        return {
                            ...old,
                            pages: old.pages.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.map(item => {
                                        return item.plaid_subscription_id === response.data?.id
                                            ? {
                                                  ...item,
                                                  category: response.data?.category,
                                                  ignore: response.data?.ignore,
                                                  plaid_category: response.data?.plaid_category,
                                                  product: response.data?.product,
                                                  sub_category_id: response.data?.sub_category_id,
                                                  vendor: response.data?.vendor,
                                                  notes: response.data?.notes,
                                              }
                                            : item;
                                    }),
                                };
                            }),
                        };
                    });
                    updateMatchedQueries(transactionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: (old.data || []).map(item => {
                                return item.plaid_subscription_id === response.data?.id
                                    ? {
                                          ...item,
                                          category: response.data?.category,
                                          ignore: response.data?.ignore,
                                          plaid_category: response.data?.plaid_category,
                                          product: response.data?.product,
                                          sub_category_id: response.data?.sub_category_id,
                                          vendor: response.data?.vendor,
                                          notes: response.data?.notes,
                                      }
                                    : item;
                            }),
                        };
                    });
                    invalidateMatchedQueries(subscriptionsFiltersKey);
                    invalidateCompanyExpensesChartsAndKPIs();
                    invalidateMatchedQueries(["suggested-expenses-category-detail", company_id]);
                    invalidateMatchedQueries(["company-expenses-transactions", company_id]);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    if (newInfo?.body?.contract_id) {
                        invalidateMatchedQueries(["msp-contract-details", company_id]);
                        invalidateMatchedQueries(["firstLoad", company_id]);
                        invalidateMatchedQueries(["subscriptionListView", company_id]);
                        invalidateMatchedQueries(["contract-expenses-overtime", company_id]);
                        oldMatch?.contract?.id &&
                            invalidateMatchedQueries({
                                0: "company-expenses-transactions",
                                1: company_id,
                                6: oldMatch?.contract?.id,
                            });
                    }
                    notify("Successfully updated subscription", "Success");
                    break;
                case MutateTypes.SyncExpenseAccount:
                    updateMatchedQueries(["all-company-expenses-linked-accounts", company_id], old => {
                        return (
                            old?.map(account =>
                                account.id === newInfo.id
                                    ? {
                                          ...account,
                                          is_syncing: true,
                                      }
                                    : account,
                            ) || []
                        );
                    });
                    updateMatchedQueries(["company-expenses-linked-accounts", company_id], old => {
                        return {
                            ...old,
                            pages: old?.pages?.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.map(institution => ({
                                        ...institution,
                                        ...(institution.id === newInfo?.id && {
                                            is_syncing: true,
                                        }),
                                    })),
                                };
                            }),
                        };
                    });
                    invalidateMatchedQueries(subscriptionsKey);
                    invalidateMatchedQueries(transactionsKey);
                    invalidateMatchedQueries(["company-expenses-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-prior-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-subscriptions-filters", company_id]);
                    invalidateCompanyExpensesChartsAndKPIs();
                    notify(`Successfully syncing account`, "Success");
                    break;
                case MutateTypes.DeleteExpenseAccount:
                    updateMatchedQueries(["all-company-expenses-linked-accounts", company_id], old => {
                        return old?.filter(i => i.id !== newInfo.id) || [];
                    });
                    updateMatchedQueries(["company-expenses-linked-accounts", company_id], old => {
                        return {
                            ...old,
                            pages: old?.pages?.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.filter(institution => institution.id !== newInfo.id),
                                };
                            }),
                        };
                    });
                    invalidateMatchedQueries(subscriptionsKey);
                    invalidateMatchedQueries(transactionsKey);
                    invalidateMatchedQueries(["company-expenses-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-prior-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-subscriptions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-breakdown-filters", company_id]);
                    invalidateCompanyExpensesChartsAndKPIs();
                    invalidateMatchedQueries(["company-expenses-alert-notifications", company_id]);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    notify(`Successfully deleted account`, "Success");
                    break;
                case MutateTypes.BulkDeleteExpenseAccount:
                    const deletedIds = new Set(newInfo.body);
                    updateMatchedQueries(["all-company-expenses-linked-accounts", company_id], old => {
                        return old?.filter(i => !deletedIds.has(i.id)) || [];
                    });
                    updateMatchedQueries(["company-expenses-linked-accounts", company_id], old => {
                        return {
                            ...old,
                            pages: old?.pages?.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.filter(institution => !deletedIds.has(institution.id)),
                                };
                            }),
                        };
                    });
                    invalidateCompanyExpensesChartsAndKPIs();
                    invalidateMatchedQueries(["company-expenses-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-prior-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-subscriptions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-breakdown-filters", company_id]);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    notify(`Successfully deleted accounts`, "Success");
                    invalidateMatchedQueries(["company-expenses-alert-notifications", company_id]);
                    break;
                case MutateTypes.BulkSyncExpenseAccount:
                    const syncIds = new Set(response.data.filter(item => item.is_syncing).map(item => item.id));
                    updateMatchedQueries(["all-company-expenses-linked-accounts", company_id], old => {
                        return old?.map(institution => ({
                            ...institution,
                            accounts: institution.accounts?.map(account =>
                                syncIds.has(institution.id)
                                    ? {
                                          ...account,
                                          is_syncing: true,
                                      }
                                    : account,
                            ),
                        }));
                    });
                    updateMatchedQueries(["company-expenses-linked-accounts", company_id], old => {
                        return {
                            ...old,
                            pages: old?.pages?.map(page => {
                                return {
                                    ...page,
                                    data: page.data?.map(institution => ({
                                        ...institution,
                                        ...(syncIds.has(institution.id) && {
                                            is_syncing: true,
                                        }),
                                    })),
                                };
                            }),
                        };
                    });
                    invalidateCompanyExpensesChartsAndKPIs();
                    notify(`Successfully syncing accounts`, "Success");
                    break;
                case MutateTypes.BulkUpdateSubscription:
                    const subscriptionKeyValuePair = Object.fromEntries(response.data.map(item => [item.id, item]));
                    updateMatchedQueries(subscriptionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: old.data.map(item =>
                                subscriptionKeyValuePair[item.id] ? subscriptionKeyValuePair[item.id] : item,
                            ),
                        };
                    });
                    updateMatchedQueries(transactionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: old.data.map(item =>
                                subscriptionKeyValuePair[item.plaid_subscription_id]
                                    ? {
                                          ...item,
                                          category: subscriptionKeyValuePair[item.plaid_subscription_id]?.category,
                                          ignore: subscriptionKeyValuePair[item.plaid_subscription_id]?.ignore,
                                          plaid_category:
                                              subscriptionKeyValuePair[item.plaid_subscription_id]?.plaid_category,
                                          product: subscriptionKeyValuePair[item.plaid_subscription_id]?.product,
                                          sub_category_id:
                                              subscriptionKeyValuePair[item.plaid_subscription_id]?.sub_category_id,
                                          vendor: subscriptionKeyValuePair[item.plaid_subscription_id]?.vendor,
                                          notes: subscriptionKeyValuePair[item.plaid_subscription_id]?.notes,
                                      }
                                    : item,
                            ),
                        };
                    });
                    invalidateMatchedQueries(subscriptionsFiltersKey);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    invalidateCompanyExpensesChartsAndKPIs();
                    !newInfo.disableDefaultNotify && notify("Successfully updated subscriptions", "Success");
                    break;
                case MutateTypes.BulkUpdateTransaction:
                    const transactionKeyValuePair = Object.fromEntries(response.data.map(item => [item.id, item]));
                    updateMatchedQueries(transactionsKey, old => {
                        if (!old?.data) return old;
                        return {
                            ...old,
                            data: old.data.map(item =>
                                transactionKeyValuePair[item.id] ? transactionKeyValuePair[item.id] : item,
                            ),
                        };
                    });
                    invalidateMatchedQueries(transactionsFiltersKey);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", company_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", company_id]);
                    invalidateCompanyExpensesChartsAndKPIs();
                    !newInfo.disableDefaultNotify && notify("Successfully updated transactions", "Success");
                    break;
                case MutateTypes.UpdateExpenseAccountDisplayName:
                    notify("Successfully updated account");
                    updateMatchedQueries(["all-company-expenses-linked-accounts", company_id], old => {
                        if (!old) return old;
                        return old?.map(account =>
                            account.id === newInfo?.id
                                ? {...account, display_name: newInfo.body?.display_name}
                                : account,
                        );
                    });
                    updateMatchedQueries(["company-expenses-linked-accounts", company_id], old => {
                        if (!old || !old?.pages) return old;
                        return {
                            ...old,
                            pages: old.pages.map(page => ({
                                ...page,
                                data: page.data.map(account =>
                                    account.id === newInfo?.id
                                        ? {...account, display_name: newInfo.body?.display_name}
                                        : account,
                                ),
                            })),
                        };
                    });
                    invalidateMatchedQueries(["company-expenses-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-prior-transactions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-subscriptions-filters", company_id]);
                    invalidateMatchedQueries(["company-expenses-breakdown-filters", company_id]);
                    break;
                case MutateTypes.UpdateAlertSettings:
                    updateMatchedQueries(["company-expenses-alert-settings", company_id], () => response.data);
                    notify("Successfully updated alert settings", "Success");
                    break;
                default:
                    break;
            }
            if (newInfo.onSuccess) newInfo.onSuccess(response);
        },
        // This will always run after error or success:
        onSettled: (data, err, newInfo) => {
            let updatingKey: string = newInfo?.mutate_type;
            isUpdatingDispatcher({key: updatingKey, value: false});
            if (newInfo.mutate_is_updating_append) {
                isUpdatingDispatcher({
                    key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                    value: false,
                });
            }
            newInfo?.onSettled?.();
        },
    });

    return {updateCompanyExpenses, isUpdating, isUpdatingDispatcher};
}
