import {useMutation, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {Reducer, useReducer} from "react";
import {IStackCategorization} from "../../Interfaces/myStack.interface";
import {
    INaviStackMutateData,
    INaviStackServices,
    IUpdatingQuery,
    MutateTypes,
} from "../../Interfaces/queries.interface";
import {companyService} from "../../services/company.service";
import {getErrorFromArray} from "../../utils/error.util";
import {useQueryHelper} from "../helpers/useQueryHelper";
import useNotification from "../useNotification";

interface IOptions {
    isCompany?: boolean;
    refreshAfterMutate?: boolean;
    isMsp?: boolean;
    loadRecommendedStack?: boolean;
    customerStack?: boolean;
}

const updateReducer = (state: IUpdatingQuery = {} as IUpdatingQuery, payload: {key: MutateTypes; value: boolean}) => {
    return {...state, [payload.key]: payload.value};
};

export default function useNaviStackMutations(subject_id: string, options?: IOptions) {
    const [isUpdating, isUpdatingDispatcher] = useReducer<Reducer<IUpdatingQuery, any>>(
        updateReducer,
        {} as IUpdatingQuery,
    );
    const notify = useNotification();
    const queryClient = useQueryClient();
    const {invalidateMatchedQueries, updateMatchedQueries} = useQueryHelper();
    const updateServices: INaviStackServices = {
        [MutateTypes.LocalUpdate]: newInfo => {
            return new Promise(resolve => resolve(newInfo));
        },
        [MutateTypes.RemoveFromStack]: newInfo =>
            companyService.deleteMyStackProduct(newInfo.subject_id, {
                id: newInfo.stack_id || "",
                customerStack: options?.customerStack,
            }),
        [MutateTypes.AddToStack]: newInfo =>
            companyService.storeMyStack(newInfo.subject_id || newInfo.company_id || "", {
                ...(newInfo.requestBody || {}),
                customerStack: options?.customerStack,
            }),
        [MutateTypes.UpdateStack]: newInfo =>
            companyService.updateMyStack(newInfo.subject_id || newInfo.company_id || "", {
                ...(newInfo.requestBody || {}),
                customerStack: options?.customerStack,
            }),
        [MutateTypes.RemoveBrandStackCategory]: newInfo =>
            companyService.deleteBrandStackCategory(subject_id, newInfo.category_id || ""),
    };

    const updateNaviStack = useMutation(newInfo => updateServices[newInfo?.mutate_type || "update"](newInfo), {
        // optimistic updated
        onMutate: async (newInfo: INaviStackMutateData) => {
            if (options?.isCompany) {
                await queryClient.cancelQueries(["naviStack", subject_id]);
                if (newInfo.mutate_is_updating_append) {
                    await isUpdatingDispatcher({
                        key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                        value: true,
                    });
                }
                await isUpdatingDispatcher({key: newInfo?.mutate_type, value: true});
                return {};
            }
            return {};
        },
        onError: (err: AxiosError<any>, newInfo) => {
            if (newInfo.onError) newInfo.onError(err);
            if (newInfo.mutate_is_updating_append) {
                isUpdatingDispatcher({
                    key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                    value: false,
                });
            }
            isUpdatingDispatcher({key: newInfo?.mutate_type, value: false});
            if (subject_id && err?.code !== "ERR_CANCELED") {
                notify(getErrorFromArray(err), "Error");
            }
        },
        onSuccess: (response: AxiosResponse<any>, newInfo) => {
            switch (newInfo.mutate_type) {
                case MutateTypes.LocalUpdate:
                    queryClient.setQueryData(["naviStack", subject_id], (old: IStackCategorization[] | undefined) => {
                        return [...(old || ([] as IStackCategorization[])), ...((newInfo as any) || ({} as any))];
                    });
                    break;
                case MutateTypes.AddToStack:
                    // queryClient.setQueryData(["naviStack", subject_id], (old: IStackCategorization[] | undefined) => {
                    //     let afterAddToStack = [...(old || [])];
                    //     Object.keys(newInfo.requestBody)?.forEach(key => {
                    //         afterAddToStack = afterAddToStack.filter(s => (s.category_id === key ? false : true));
                    //         afterAddToStack = [
                    //             ...afterAddToStack,
                    //             ...(response.data || [])?.filter(s => s.category_id === key),
                    //         ];
                    //     });
                    //     return afterAddToStack;
                    // });
                    if (options?.loadRecommendedStack && newInfo.updateAdoptionDetails) {
                        invalidateMatchedQueries(["mspBrandStackAdoptionDetail", newInfo.msp_friendly_url]);
                    }
                    invalidateMatchedQueries(["naviStack", subject_id]);
                    invalidateMatchedQueries(["missingAdoptionStack", subject_id]);
                    if (newInfo.updateAdoptionDetails) {
                        if (newInfo.msp_friendly_url) {
                            invalidateMatchedQueries(["mspBrandStackAdoptionDetail", newInfo?.msp_friendly_url]);
                        }
                    }
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", subject_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", subject_id]);
                    invalidateMatchedQueries(["company-stack-and-contract-products", subject_id]);
                    break;
                case MutateTypes.RemoveFromStack:
                    updateMatchedQueries(["naviStack", subject_id], old => old.filter(s => s.id !== newInfo.stack_id));
                    invalidateMatchedQueries(["missingAdoptionStack", subject_id]);
                    if (newInfo.msp_friendly_url && newInfo.updateAdoptionDetails) {
                        invalidateMatchedQueries(["mspBrandStackAdoptionDetail", newInfo?.msp_friendly_url]);
                    }
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", subject_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", subject_id]);
                    invalidateMatchedQueries(["company-stack-and-contract-products", subject_id]);
                    break;
                case MutateTypes.UpdateStack:
                    queryClient.refetchQueries(["naviStack", subject_id]);
                    invalidateMatchedQueries(["company-dashboard-stack-breakdown", subject_id]);
                    invalidateMatchedQueries(["company-dashboard-quick-overview", subject_id]);
                    invalidateMatchedQueries(["company-stack-and-contract-products", subject_id]);
                    break;
                case MutateTypes.RemoveBrandStackCategory:
                    notify("Successfully removed category from stack", "Success");
                    invalidateMatchedQueries(["naviStack", subject_id]);
                    if (newInfo.msp_friendly_url && newInfo.updateAdoptionDetails) {
                        invalidateMatchedQueries(["mspBrandStackAdoptionDetail", newInfo?.msp_friendly_url]);
                    }
                    break;
                default:
                    notify("NaviStack updated successfully", "Success");
                    break;
            }
            if (newInfo.onSuccess) newInfo.onSuccess(response);
        },

        onSettled: (data, err, newInfo) => {
            if (newInfo.mutate_is_updating_append) {
                isUpdatingDispatcher({
                    key: newInfo?.mutate_type + newInfo.mutate_is_updating_append,
                    value: false,
                });
            }
            isUpdatingDispatcher({key: newInfo?.mutate_type, value: false});
            if (subject_id && options?.refreshAfterMutate) {
                queryClient.invalidateQueries(["naviStack", subject_id]);
            }
        },
    });

    return {updateNaviStack, isUpdating, isUpdatingDispatcher};
}
