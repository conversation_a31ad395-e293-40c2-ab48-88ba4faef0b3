import {useMutation, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {Reducer, useReducer} from "react";
import {IMutateData, IServices, IUpdatingQuery, MutateTypes} from "../../Interfaces/queries.interface";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../useNotification";
import {mspService} from "../../services/msp.service";
import {MSP_BRAND_TEXT} from "../../constants/commonStrings.constant";

const updateReducer = (state: IUpdatingQuery = {} as IUpdatingQuery, payload: {key: MutateTypes; value: boolean}) => {
    return {...state, [payload.key]: payload.value};
};

export default function useMspClientsMutations(friendly_url?: string) {
    const [isUpdating, isUpdatingDispatcher] = useReducer<Reducer<IUpdatingQuery, any>>(
        updateReducer,
        {} as IUpdatingQuery,
    );
    const notify = useNotification();
    const queryClient = useQueryClient();

    const updateServices: IServices = {
        [MutateTypes.LocalUpdate]: newInfo => {
            return new Promise(resolve => resolve(newInfo));
        },
        [MutateTypes.StoreMspClient]: newInfo => {
            if (!friendly_url) return new Promise(resolve => resolve(null));
            return mspService.saveClient(newInfo.body, friendly_url);
        },
        [MutateTypes.UpdateMspClient]: newInfo => {
            if (!friendly_url) return new Promise(resolve => resolve(null));
            return mspService.saveClient(newInfo.body, friendly_url);
        },
        [MutateTypes.DeleteMspClient]: newInfo => {
            if (!friendly_url) return new Promise(resolve => resolve(null));
            return mspService.deleteClient(newInfo.friendly_url || "", newInfo.id || "");
        },
    };

    const updateAllClientsQueries = (
        action: "delete" | "store" | "edit",
        newInfo: IMutateData,
        response: AxiosResponse,
    ) => {
        const allQueries = queryClient.getQueryCache().findAll();
        allQueries.forEach(query => {
            const queryKey = query.queryKey;
            if (queryKey[0] === "mspClients" && queryKey[1] === friendly_url) {
                const subjectId = response.data?.id;
                if (action === "delete") {
                    queryClient.setQueryData(queryKey, (old: any) => {
                        if (!old || !old.pages?.length) return old;
                        const newData = {...old};
                        newData.pages = newData.pages.map(page => ({
                            ...page,
                            data: page.data.filter(item => item.id !== newInfo.id),
                            meta: {...page.meta, total: page.meta.total - 1},
                        }));
                        return newData;
                    });
                } else if (action === "store") {
                    queryClient.setQueryData(queryKey, (old: any) => {
                        if (!old) return old;
                        const newData = {...old};
                        newData.pages = newData.pages.map((page, index) => ({
                            ...page,
                            data:
                                index + 1 === newData.pages.length
                                    ? [...page.data, Array.isArray(response.data) ? response.data[0] : response.data]
                                    : page.data,
                            meta: {...page.meta, total: page.meta.total + 1},
                        }));
                        return newData;
                    });
                } else if (action === "edit") {
                    queryClient.setQueryData(queryKey, (old: any) => {
                        if (!old) return old;
                        const newData = {...old};
                        const responseData = Array.isArray(response.data) ? response.data[0] : response.data;
                        newData.pages = newData.pages.map(page => ({
                            ...page,
                            data: page.data?.map(item =>
                                item.id === subjectId
                                    ? {
                                          ...item,
                                          ...responseData,
                                      }
                                    : item,
                            ),
                        }));
                        return newData;
                    });
                }
            }
        });
    };

    const updateMspClients = useMutation(newInfo => updateServices[newInfo?.mutate_type || "update"](newInfo), {
        // optimistic updated
        onMutate: async (newInfo: IMutateData) => {
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            await isUpdatingDispatcher({key: updatingKey, value: true});
            return {};
        },
        onError: (err: AxiosError<any>, newInfo) => {
            if (newInfo.onError) newInfo.onError(err);
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            isUpdatingDispatcher({key: updatingKey, value: false});
            if (friendly_url && err?.code !== "ERR_CANCELED") {
                notify(getErrorFromArray(err), "Error");
            }
        },
        onSuccess: (response: AxiosResponse<any>, newInfo) => {
            switch (newInfo.mutate_type) {
                case MutateTypes.LocalUpdate:
                    break;
                case MutateTypes.StoreMspClient:
                    updateAllClientsQueries("store", newInfo, response);
                    break;
                case MutateTypes.UpdateMspClient:
                    updateAllClientsQueries("edit", newInfo, response);
                    notify(`${MSP_BRAND_TEXT.CUSTOMER} successfully updated`, "Success");
                    break;
                case MutateTypes.DeleteMspClient:
                    updateAllClientsQueries("delete", newInfo, response);
                    notify("Successfully deleted client", "Success");
                    break;
                default:
                    break;
            }
            if (newInfo.onSuccess) newInfo.onSuccess(response);
        },
        // This will always run after error or success:
        onSettled: (data, err, newInfo) => {
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            isUpdatingDispatcher({key: updatingKey, value: false});
            newInfo?.onSettled?.();
        },
    });

    return {updateMspClients, isUpdating, isUpdatingDispatcher};
}
