import {useQuery} from "@tanstack/react-query";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {mspService} from "../../services/msp.service";
import {ICompanyInvite} from "../../Interfaces/companyInvite.interface";
import {AxiosError} from "axios";

export default function useCompanyInviteValidation(signature: string, validationType: "client" | "affiliate") {
    const inviteValidationQuery = useQuery<ICompanyInvite>(
        ["companyInviteValidation", signature],
        () => {
            return validationType === "client"
                ? new Promise((resolve, reject) => {
                      mspService
                          .validateInvitation(signature)
                          .then(response => {
                              resolve(response.data);
                          })
                          .catch(error => {
                              reject(error as AxiosError);
                          });
                  })
                : new Promise((resolve, reject) => {
                      mspService
                          .validateAffiliateInvitation(signature)
                          .then(response => {
                              resolve(response.data);
                          })
                          .catch(error => {
                              reject(error as AxiosError);
                          });
                  });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!signature,
        },
    );

    return inviteValidationQuery;
}
