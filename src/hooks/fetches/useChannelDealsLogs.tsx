import {UseInfiniteQueryResult, UseQueryResult, useInfiniteQuery, useQuery} from "@tanstack/react-query";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {Dispatch, SetStateAction, useEffect, useState} from "react";
import {INewFilters} from "../../Interfaces/filters.interface";
import {buildFilters, clearFilters} from "../../utils/filters.util";
import {channelDealsLogsService} from "../../services/channelDealsLogs.service";

interface IOptions {
    refreshAfterMutate?: boolean;
    calls?: {
        channelDealsHistory?: boolean;
        channelDealsHistoryFilters?: boolean;
        all?: boolean;
    };
}

interface IUseChannelDealsLogs {
    channelDealsHistory: UseInfiniteQueryResult<any>;
    channelDealsHistoryFilters: UseQueryResult<any>;
    channelDealsHistorySearchState: [string, Dispatch<SetStateAction<string>>];
    channelDealsHistoryFilterState: [INewFilters | undefined, Dispatch<SetStateAction<INewFilters | undefined>>];
    channelDealsHistoryPage: number;
}

export function useChannelDealsLogs(deal_id: string, options?: IOptions): IUseChannelDealsLogs {
    const [channelDealsHistorySearch, setChannelDealsHistorySearch] = useState<string>("");
    const [channelDealsHistoryFilterOptions, setChannelDealsHistoryFilterOptions] = useState<INewFilters | undefined>({
        sort: "created_at__DESC",
    });
    const [channelDealsHistoryPage, setChannelDealsHistoryPage] = useState<number>(1);
    const stringifiedChannelDealsHistoryFilterOptions = JSON.stringify(channelDealsHistoryFilterOptions);

    useEffect(() => {
        setChannelDealsHistoryPage(1);
    }, [channelDealsHistorySearch, stringifiedChannelDealsHistoryFilterOptions]);

    const shouldCall = (call: string) => {
        if (options?.calls) {
            const allValue = options.calls["all"];
            const value = options.calls[call];
            return value === undefined ? (allValue === undefined ? true : allValue) : value;
        }
        return true;
    };

    const channelDealsHistory = useInfiniteQuery<any>(
        ["channelDealsHistory", deal_id, channelDealsHistorySearch, stringifiedChannelDealsHistoryFilterOptions],
        async ({pageParam}) => {
            if (shouldCall("channelDealsHistory")) {
                setChannelDealsHistoryPage(pageParam || 1);
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) => {
                    return channelDealsLogsService
                        .getChannelDealsHistory(deal_id, {
                            search_word: channelDealsHistorySearch,
                            page: pageParam || 1,
                            dynamic: clearFilters(channelDealsHistoryFilterOptions),
                            items_per_page: 10,
                        })
                        .then(res => {
                            resolve(res.data);
                        })
                        .catch(err => {
                            reject(err);
                        });
                });
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("channelDealsHistory") && !!deal_id,
            ...DEFAULT_QUERY_CONFIGS(),
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
            retryOnMount: true,
            refetchOnMount: true,
        },
    );

    const channelDealsHistoryFilters = useQuery<any>(
        ["channelDealsHistoryFilters", deal_id],
        async () => {
            if (shouldCall("channelDealsHistoryFilters")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) => {
                    return channelDealsLogsService
                        .getChannelDealsHistoryFilters(deal_id)
                        .then(res => {
                            resolve(buildFilters(res.data, {}));
                        })
                        .catch(err => {
                            reject(err);
                        });
                });
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: shouldCall("channelDealsHistoryFilters") && !!deal_id,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    return {
        channelDealsHistory,
        channelDealsHistoryFilters,
        channelDealsHistorySearchState: [channelDealsHistorySearch, setChannelDealsHistorySearch],
        channelDealsHistoryFilterState: [channelDealsHistoryFilterOptions, setChannelDealsHistoryFilterOptions],
        channelDealsHistoryPage,
    };
}
