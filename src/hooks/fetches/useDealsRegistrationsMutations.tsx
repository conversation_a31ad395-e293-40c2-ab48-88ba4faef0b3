import {useMutation, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {Reducer, useReducer} from "react";
import {IMutateData, IServices, IUpdatingQuery, MutateTypes} from "../../Interfaces/queries.interface";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../useNotification";
import {useQueryHelper} from "../helpers/useQueryHelper";
import {dealRegistrationService} from "../../services/dealRegistration.service";
import uploadChunks from "../../utils/chunkUpload.util";

const updateReducer = (state: IUpdatingQuery = {} as IUpdatingQuery, payload: {key: MutateTypes; value: boolean}) => {
    return {...state, [payload.key]: payload.value};
};

export default function useDealsRegistrationsMutations(company_id?: string) {
    const [isUpdating, isUpdatingDispatcher] = useReducer<Reducer<IUpdatingQuery, any>>(
        updateReducer,
        {} as IUpdatingQuery,
    );
    const notify = useNotification();
    const {updateMatchedQueries, invalidateMatchedQueries} = useQueryHelper();
    const queryClient = useQueryClient();
    const dealRegistrationsQueryKey = ["dealRegistrations", company_id || ""];
    const draftDealRegistrationsQueryKey = ["draftDealRegistrations", company_id || ""];

    const updateServices: IServices = {
        [MutateTypes.LocalUpdate]: newInfo => {
            return new Promise(resolve => resolve(newInfo));
        },
        [MutateTypes.DeleteDealRegistration]: newInfo => {
            return new Promise((resolve, reject) => {
                dealRegistrationService
                    .deleteDeal(newInfo.company_id || "", newInfo.id || "")
                    .then(r => resolve(r.data))
                    .catch(e => reject(e));
            });
        },
        [MutateTypes.UpdateDealRegistration]: newInfo => {
            return new Promise((resolve, reject) => {
                dealRegistrationService
                    .updateDeal(newInfo.company_id || "", newInfo.body)
                    .then(r => resolve(r.data))
                    .catch(e => reject(e));
            });
        },
        [MutateTypes.CreateDealRegistration]: newInfo => {
            return dealRegistrationService.storeDeal(newInfo.company_id || "", newInfo.body);
        },
        [MutateTypes.CreateDealDocument]: newInfo => {
            return dealRegistrationService.createDealDocument(
                newInfo.company_id || "",
                newInfo.id || "",
                newInfo.name || "",
            );
        },
        [MutateTypes.UploadDealDocument]: newInfo => {
            if (!newInfo.file) return new Promise(resolve => resolve(null));
            return uploadChunks(
                newInfo.file,
                (formData, id, d) => {
                    if (d.isLastChunk) {
                        formData.append("title", newInfo?.name || "");
                    }
                    formData.append("document", d.chunk);
                    formData.append("id", newInfo.doc_id || "");
                    return dealRegistrationService.uploadDealDocument(
                        newInfo.company_id || "",
                        newInfo.id || "",
                        formData,
                    );
                },
                progress => newInfo?.progressCallback?.(progress),
            );
        },
        [MutateTypes.ApproveDeal]: newInfo => {
            if (!newInfo.id || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.approveDeal(company_id, newInfo.id);
        },
        [MutateTypes.DeclineDeal]: newInfo => {
            if (!newInfo.body || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.declineDeal(company_id, newInfo.body);
        },
        [MutateTypes.WithdrawDeal]: newInfo => {
            if (!newInfo.body || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.withdrawDeal(company_id, newInfo.body);
        },
        [MutateTypes.RequestDealInfo]: newInfo => {
            if (!newInfo.body || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.requestInfoDeal(company_id, newInfo.body);
        },
        [MutateTypes.CreateDealContacts]: newInfo => {
            if (!newInfo.body || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.storeDealContact(company_id, newInfo?.id || null, newInfo.body);
        },
        [MutateTypes.DeleteDealContacts]: newInfo => {
            if (!newInfo.body || !company_id) return new Promise(resolve => resolve(null));
            return dealRegistrationService.deleteDealContacts(company_id, newInfo?.id || null, newInfo.body);
        },
    };

    const updateDealRegistrations = useMutation(newInfo => updateServices[newInfo?.mutate_type || "update"](newInfo), {
        // optimistic updated
        onMutate: async (newInfo: IMutateData) => {
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            await isUpdatingDispatcher({key: updatingKey, value: true});
            queryClient.setQueryData([`mutationStatus`, updatingKey], true);
            return {};
        },
        onError: (err: AxiosError<any>, newInfo) => {
            if (newInfo.onError) newInfo.onError(err);
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            isUpdatingDispatcher({key: updatingKey, value: false});
            queryClient.setQueryData([`mutationStatus`, updatingKey], false);
            if (company_id && err?.code !== "ERR_CANCELED") {
                notify(getErrorFromArray(err), "Error");
            }
        },
        onSuccess: (response: AxiosResponse<any>, newInfo) => {
            switch (newInfo.mutate_type) {
                case MutateTypes.LocalUpdate:
                    break;
                case MutateTypes.DeleteDealRegistration:
                    updateMatchedQueries(["dealRegistrations", company_id], old => {
                        return {
                            ...old,
                            data: (old.data || []).filter(deal => deal.id !== newInfo?.id),
                        };
                    });
                    updateMatchedQueries(["draftDealRegistrations", company_id], old => {
                        return {
                            ...old,
                            data: (old.data || []).filter(deal => deal.id !== newInfo?.id),
                        };
                    });
                    break;
                case MutateTypes.UpdateDealRegistration:
                case MutateTypes.ApproveDeal:
                case MutateTypes.DeclineDeal:
                case MutateTypes.RequestDealInfo:
                case MutateTypes.WithdrawDeal:
                    if (newInfo.mutate_type === MutateTypes.WithdrawDeal) {
                        notify("Deal withdrawn successfully", "Success");
                    }
                    if (newInfo.mutate_type === MutateTypes.DeclineDeal) {
                        notify("Deal declined successfully.", "Success");
                    }
                    invalidateMatchedQueries(dealRegistrationsQueryKey);
                    invalidateMatchedQueries(draftDealRegistrationsQueryKey);
                    invalidateMatchedQueries(["dealRegistrationsTotals", company_id]);
                    invalidateMatchedQueries(["dealHistory", company_id]);
                    break;
                case MutateTypes.CreateDealRegistration:
                    invalidateMatchedQueries(dealRegistrationsQueryKey);
                    invalidateMatchedQueries(draftDealRegistrationsQueryKey);
                    invalidateMatchedQueries(["dealRegistrationsTotals", company_id]);
                    invalidateMatchedQueries(["dealRegistrationsFilters", company_id]);
                    break;
                case MutateTypes.UploadDealDocument:
                case MutateTypes.CreateDealDocument:
                    invalidateMatchedQueries(dealRegistrationsQueryKey);
                    invalidateMatchedQueries(draftDealRegistrationsQueryKey);
                    invalidateMatchedQueries(["dealHistory", company_id]);
                    break;
                default:
                    break;
            }
            if (newInfo.onSuccess) newInfo.onSuccess(response);
        },
        // This will always run after error or success:
        onSettled: (data, err, newInfo) => {
            let updatingKey: string = newInfo?.mutate_type;
            updatingKey += newInfo.mutate_is_updating_append || "";
            isUpdatingDispatcher({key: updatingKey, value: false});
            queryClient.setQueryData([`mutationStatus`, updatingKey], false);
            newInfo?.onSettled?.();
        },
    });

    const getMutationStatus = (mutateType: string) => {
        return queryClient.getQueryData<boolean>([`mutationStatus`, mutateType]) || false;
    };

    return {updateDealRegistrations, isUpdating, isUpdatingDispatcher, getMutationStatus};
}
