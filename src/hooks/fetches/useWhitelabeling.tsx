import {useTheme} from "@mui/material";
import {useQuery, UseQueryResult} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useAtom} from "jotai";
import {AFFILIATE_WHITELABEL_HOSTS, DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {IWhitelabeling} from "../../Interfaces/whitelabeling.interface";
import {whitelabelingService} from "../../services/whitelabeling.service";
import {getContrastColor} from "../../utils/color.util";
import {getSubdomain} from "../../utils/url.util";
import {whitelabelingAtom} from "../../contexts/globalProvider";

export interface IUseWhitelabeling {
    whitelabeling: IWhitelabeling | null;
    setWhitelabeling: any;
    whitelabelingQuery: UseQueryResult<IWhitelabeling>;
    enabled: boolean;
}

export default function useWhitelabeling(): IUseWhitelabeling {
    const [whitelabeling, setWhitelabeling] = useAtom(whitelabelingAtom);
    const subdomain = getSubdomain();
    const theme = useTheme();
    const whitelabelingEnabled =
        !!subdomain.company && !!AFFILIATE_WHITELABEL_HOSTS.some(host => window.location.host.includes(host));
    const whitelabelingQuery = useQuery<IWhitelabeling>(
        ["whitelabeling-query", subdomain.company],
        () => {
            const promiseToReturn: Promise<IWhitelabeling> = new Promise((resolve, reject) =>
                whitelabelingService
                    .loadBySubdomain(subdomain.company!)
                    .then((response: AxiosResponse<IWhitelabeling>) => {
                        const data = {
                            ...response.data,
                            primary_color_contrast: getContrastColor(
                                response.data?.primary_color || theme.palette.secondary.main,
                            ),
                            accent_color_contrast: getContrastColor(
                                response.data?.accent_color || theme.palette.primary.main,
                            ),
                        };
                        setWhitelabeling(data);
                        resolve(data);
                    })
                    .catch((error: AxiosError<any>) => reject(error)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            staleTime: Infinity,
            cacheTime: Infinity,
            retry: false,
            enabled: whitelabelingEnabled,
        },
    );

    return {
        whitelabeling,
        setWhitelabeling,
        whitelabelingQuery,
        enabled: whitelabelingEnabled,
    };
}
