import {useQuery} from "@tanstack/react-query";
import type {AxiosResponse} from "axios";
import type {ICompanyType} from "../../Interfaces/company.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import CompanyType from "../../constants/companyType.constant";
import {companyService} from "../../services/company.service";

export default function useCompanyTypes(excludeMspClientType: boolean = true) {
    const {data, isLoading, refetch} = useQuery(
        ["all-company-types"],
        () => {
            return new Promise<ICompanyType[]>((resolve, reject) => {
                companyService
                    .getCompanyTypesNoEnum()
                    .then((r: AxiosResponse<ICompanyType[]>) => {
                        resolve(r.data);
                    })
                    .catch(reject);
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const allCompanyTypeResult = data?.filter(item =>
        excludeMspClientType ? item.value !== CompanyType.MSP_CLIENT : true,
    );

    return {
        allCompanyTypes: allCompanyTypeResult ?? defaultCompanyTypes,
        isLoading,
        refetch,
        typeIsManageClientsCandidate,
    };
}

const typeIsManageClientsCandidate = (companyType: string | undefined) => {
    return [
        CompanyType.ISP_ALL,
        CompanyType.MSSP,
        CompanyType.VAR,
        CompanyType.FRANCHISE_MSP,
        CompanyType.FRANCHISE_CORPORATE_MSP,
        CompanyType.MSP_LOCATION,
    ].includes(companyType ?? "");
};

//This is not real data, just a placeholder until we fetch in from BE
const defaultCompanyTypes: ICompanyType[] = [
    {
        id: "placeholder_id_1",
        label: "MSP",
        value: "ISP_ALL",
        type_is_of_vendor: false,
        order: 1,
    },
    {
        id: "placeholder_id_2",
        label: "MSP Client",
        value: "MSP_CLIENT",
        type_is_of_vendor: false,
        order: 1,
    },
    {
        id: "placeholder_id_3",
        label: "Vendor",
        value: "VENDOR_ALL",
        type_is_of_vendor: true,
        order: 2,
    },
    {
        id: "placeholder_id_4",
        label: "Franchise/Affiliate Corp",
        value: "FRANCHISE_CORPORATE_MSP",
        type_is_of_vendor: false,
        order: 2,
    },
    {
        id: "placeholder_id_5",
        label: "Individual",
        value: "INDIVIDUAL",
        type_is_of_vendor: false,
        order: 3,
    },
    {
        id: "placeholder_id_6",
        label: "MSSP",
        value: "MSSP",
        type_is_of_vendor: false,
        order: 4,
    },
    {
        id: "placeholder_id_7",
        label: "Franchise/Affiliate MSP",
        value: "FRANCHISE_MSP",
        type_is_of_vendor: false,
        order: 5,
    },
    {
        id: "placeholder_id_8",
        label: "Master Agent",
        value: "MASTER_AGENT",
        type_is_of_vendor: true,
        order: 6,
    },
    {
        id: "placeholder_id_9",
        label: "Distributor",
        value: "DISTRIBUTOR",
        type_is_of_vendor: true,
        order: 7,
    },
    {
        id: "placeholder_id_10",
        label: "VAR",
        value: "VAR",
        type_is_of_vendor: false,
        order: 8,
    },
    {
        id: "placeholder_id_11",
        label: "Professional Services or Consultant",
        value: "ITConsultant",
        type_is_of_vendor: true,
        order: 9,
    },
    {
        id: "placeholder_id_12",
        label: "Conference and Events",
        value: "CONFERENCE_EVENTS",
        type_is_of_vendor: true,
        order: 10,
    },
    {
        id: "placeholder_id_13",
        label: "Media Group or Personality",
        value: "Media",
        type_is_of_vendor: true,
        order: 11,
    },
    {
        id: "placeholder_id_14",
        label: "Peer Group",
        value: "PEER_GROUP",
        type_is_of_vendor: true,
        order: 12,
    },
    {
        id: "placeholder_id_15",
        label: "Finance/Tech Investor",
        value: "FINANCE_ALL",
        type_is_of_vendor: true,
        order: 13,
    },
    {
        id: "placeholder_id_16",
        label: "Other",
        value: "ELSE",
        type_is_of_vendor: true,
        order: 14,
    },
];
