import {
    useInfiniteQuery,
    UseInfiniteQueryResult,
    useQuery,
    useQueryClient,
    type UseQueryResult,
} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useAtom} from "jotai";
import {DateTime} from "luxon";
import {Dispatch, SetStateAction, useEffect, useMemo, useState} from "react";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {INewFilters, IPageInnerHeadFilter} from "../../Interfaces/filters.interface";
import {IPaginationData} from "../../Interfaces/pagination.interface";
import {
    AllExpensesBarChartResponse,
    ExpenseItem,
    IAlertSettingsResponse,
    IAllExpensesBreakdownItem,
    IExpenseBreakdownItem,
    IExpensesCountResponse,
    IExpenseSubscription,
    IExpenseSummaryResponse,
    IGetOverviewBreakdownBody,
    ILinkedAccountData,
    IPlaidAlertNotification,
    IPlaidAlertNotificationOption,
    IPlaidAlertPeriodNotificationOption,
    IPriorTransactionItem,
    ISubscriptionsCostResponse,
    ITransactionItem,
    IUpcomingExpensesSummary,
} from "../../Interfaces/plaid.interface";
import {IUpdatingQuery} from "../../Interfaces/queries.interface";
import {plaidService} from "../../services/plaid.service";
import {isInitialSyncInProgressAtom} from "../../uicomponents/ModalManager/ExpensesModals/expenses.atoms";
import {buildFilters, updateFilterNullIds} from "../../utils/filters.util";
import useIsPlaidAvailable from "../useIsPlaidAvailable";
import useCompanyExpenseMutations from "./useCompanyExpenseMutations";

interface IOptions {
    refreshAfterMutate?: boolean;
    calls?: {
        linkedAccounts?: boolean;
        expensesCount?: boolean;
        expensesOverTime?: boolean;
        expensesSummary?: boolean;
        expensesBreakdown?: boolean;
        expensesBreakdownFilters?: boolean;
        transactions?: boolean;
        infiniteTransactions?: boolean;
        transactionsFilters?: boolean;
        subscriptions?: boolean;
        infiniteSubscriptions?: boolean;
        subscriptionsFilters?: boolean;
        priorTransactions?: boolean;
        priorTransactionsFilters?: boolean;
        subscriptionCosts?: boolean;
        allExpensesBarChartData?: boolean;
        expensesBreakdownOverview?: boolean;
        pagedLinkedAccounts?: boolean;
        all?: boolean;
        alertSettings?: boolean;
        alertOptions?: boolean;
        alertPeriodOptions?: boolean;
        alertNotifications?: boolean;
        upcomingSummary?: boolean;
    };
    subscriptionId?: string;
    dates?: {
        year?: string;
        start_date?: string;
        end_date?: string;
        date?: string;
    };
    allExpensesBarChartDates?: {
        year?: string;
        start_date?: string;
        end_date?: string;
    };
    costsCategoryId?: string | null;
    costsVendorId?: string | null;
    transactionsVendorId?: string | null;
    //? Default toggle button filter for expenses breakdown overview
    expensesBreakdownOverviewDefaultToggle?: "all" | "stack" | "subscription";
    //? Default filter values for transactions, subscriptions, expenses breakdown, and overtime chart
    categoryId?: string | null;
    subCategoryId?: string | null;
    plaidCategoryId?: string;
    plaidSubCategoryId?: string;
    contract_id?: string;
    transaction_id?: string;
    stack_only?: boolean; //? Filters transactions and subscriptions to show only mapped items
    expenseSummaryDates?: {start_date: string; end_date: string};
    hide_uncategorized?: boolean;
    expensesSummaryFilters?: {category?: string[]; sub_category?: string[]};
}

interface IExpenseSummaryRequestBody {
    date?: string;
    start_date?: string;
    end_date?: string;
    category?: Array<string | null>;
    sub_category?: Array<string | null>;
}

export interface IUseCompanyExpenses {
    handlePlaidLinkComplete: () => void;
    //? Linked Accounts
    linkedAccounts: UseQueryResult<ILinkedAccountData[] | undefined>;
    pagedLinkedAccounts: UseInfiniteQueryResult<IPaginationData<ILinkedAccountData[]> | undefined>;
    linkedAccountsSearch: string;
    setLinkedAccountsSearch: Dispatch<SetStateAction<string>>;
    linkedAccountsFilterState: INewFilters;
    setLinkedAccountsFilterState: Dispatch<SetStateAction<INewFilters>>;
    //? Counts
    expensesCount: UseQueryResult<IExpensesCountResponse | undefined>;
    // ? Expenses Over Time
    expensesOverTime: UseQueryResult<ExpenseItem[] | undefined>;
    expensesOverTimeRequestBody: {start_date: string; end_date: string};
    setExpensesOverTimeRequestBody: Dispatch<SetStateAction<{start_date: string; end_date: string}>>;
    //? Expense Summary
    expensesSummary: UseQueryResult<IExpenseSummaryResponse | undefined>;
    expenseSummaryDates: {
        date?: string;
        start_date?: string;
        end_date?: string;
    };
    setExpenseSummaryDates: Dispatch<SetStateAction<IExpenseSummaryRequestBody>>;
    //? Expenses Breakdonw
    expensesBreakdown: UseQueryResult<IExpenseBreakdownItem[] | undefined>;
    expensesBreakdownFilters: UseQueryResult<IPageInnerHeadFilter | undefined>;
    expensesBreakdownFilterState: any;
    setExpensesBreakdownFilterState: Dispatch<INewFilters>;
    //? Subscriptions
    subscriptions: UseQueryResult<IPaginationData<IExpenseSubscription[]> | undefined>;
    infiniteSubscriptions: UseInfiniteQueryResult<IPaginationData<IExpenseSubscription[]> | undefined>;
    subscriptionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined>;
    subscriptionFilterState: INewFilters | undefined;
    setSubscriptionFilterState: Dispatch<INewFilters | undefined>;
    subscriptionItemsPerPage: number;
    setSubscriptionsItemsPerPage: Dispatch<SetStateAction<number>>;
    subscriptionSearch: string;
    setSubscriptionsSearch: Dispatch<SetStateAction<string>>;
    subscriptionPage: number;
    setSubscriptionsPage: Dispatch<SetStateAction<number>>;
    subscriptionCosts: UseQueryResult<ISubscriptionsCostResponse | undefined>;
    //? Transactions
    transactions: UseQueryResult<IPaginationData<ITransactionItem[]> | undefined>;
    infiniteTransactions: UseInfiniteQueryResult<IPaginationData<ITransactionItem[]> | undefined>;
    transactionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined>;
    transactionsFilterState: INewFilters | undefined;
    setTransactionsFilterState: Dispatch<INewFilters | undefined>;
    transactionsItemsPerPage: number;
    setTransactionsItemsPerPage: Dispatch<number>;
    transactionsPage: number;
    setTransactionsPage: Dispatch<SetStateAction<number>>;
    transactionsSearch: string;
    setTransactionsSearch: Dispatch<SetStateAction<string>>;
    //? Prior Transactions
    priorTransactions: UseInfiniteQueryResult<IPaginationData<IPriorTransactionItem[]> | undefined>;
    priorTransactionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined>;
    priorTransactionsSearch: string;
    setPriorTransactionsSearch: Dispatch<SetStateAction<string>>;
    priorTransactionsFilterState: INewFilters | undefined;
    setPriorTransactionsFilterState: Dispatch<INewFilters | undefined>;
    allExpensesBarChartData: UseQueryResult<AllExpensesBarChartResponse | undefined>;
    expensesBreakdownOverview: UseQueryResult<IAllExpensesBreakdownItem[] | undefined>;
    expensesBreakdownOverviewFilters: IGetOverviewBreakdownBody;
    setExpensesBreakdownOverviewFilters: Dispatch<SetStateAction<IGetOverviewBreakdownBody>>;
    //? Alert Settings and Options
    alertSettings: UseQueryResult<IAlertSettingsResponse | undefined>;
    alertOptions: UseQueryResult<IPlaidAlertNotificationOption[] | undefined>;
    alertPeriodOptions: UseQueryResult<IPlaidAlertPeriodNotificationOption[] | undefined>;
    alertNotifications: UseInfiniteQueryResult<IPaginationData<IPlaidAlertNotification[]>>;
    alertNotificationsFilters: UseQueryResult<IPageInnerHeadFilter | undefined>;
    alertNotificationsFilterState: INewFilters | undefined;
    setAlertNotificationsFilterState: Dispatch<INewFilters | undefined>;
    alertNotificationsSearch: string;
    setAlertNotificationsSearch: Dispatch<SetStateAction<string>>;
    upcomingSummary: UseQueryResult<IUpcomingExpensesSummary | undefined>;
    isPlaidEnabled: boolean;
    hasNoAccounts: boolean;
    isLoadingPlaidEnabled: boolean;
    isInitialSyncInProgress: boolean;
    hasSomeAccountsSyncing: boolean;
    updateCompanyExpenses: any;
    isUpdating: IUpdatingQuery;
}

export function useCompanyExpenses(company_id?: string, options?: IOptions): IUseCompanyExpenses {
    const lastYearDate = new Date();
    const lastMonthDate = new Date();
    lastYearDate.setFullYear(lastYearDate.getFullYear() - 1);
    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
    const [expensesOverTimeRequestBody, setExpensesOverTimeRequestBody] = useState({
        start_date: lastYearDate.toISOString(),
        end_date: new Date().toISOString(),
        ...(options?.subCategoryId !== undefined && {sub_category_id: options.subCategoryId}),
        ...(options?.categoryId !== undefined && {category_id: options.categoryId}),
        ...(options?.plaidCategoryId && {
            plaid_category_id: [options.plaidCategoryId],
        }),
        ...(options?.transaction_id && {
            transaction_id: options.transaction_id,
        }),
        ...(options?.stack_only && {stack_only: "1"}),
    });
    const [isInitialSyncInProgress, setIsInitialSyncInProgress] = useAtom(isInitialSyncInProgressAtom);
    const [hasSomeAccountsSyncing, setHasSomeAccountsSyncing] = useState(false);
    const [expenseSummaryDates, setExpenseSummaryDates] = useState<IExpenseSummaryRequestBody>({
        ...(options?.dates?.start_date &&
            options?.dates?.end_date && {
                start_date: options?.dates?.start_date,
                end_date: options.dates.end_date,
            }),
        ...(options?.expenseSummaryDates ? options.expenseSummaryDates : {}),
        ...(options?.expensesSummaryFilters?.category && {
            category: options.expensesSummaryFilters.category,
        }),
        ...(options?.expensesSummaryFilters?.sub_category && {
            sub_category: options.expensesSummaryFilters.sub_category,
        }),
    });
    const [expensesBreakdownFilterState, setExpensesBreakdownFilterState] = useState<INewFilters>({
        expenses: "all",
        by: "sub_category",
        ...(options?.categoryId && {category_id: options.categoryId}),
        ...(options?.stack_only && {stack_only: "1"}),
    });
    const [transactionsFilterState, setTransactionsFilterState] = useState<INewFilters | undefined>({
        ...(options?.dates?.start_date &&
            options?.dates?.end_date && {
                date: {
                    start_date: options?.dates?.start_date,
                    end_date: options.dates.end_date,
                },
            }),
        ...(options?.categoryId !== undefined && {
            category: [options?.categoryId || "null"],
        }),
        ...(options?.transactionsVendorId !== undefined && {
            vendor: [options?.transactionsVendorId || "null"],
        }),
        ...(options?.subCategoryId !== undefined && {
            sub_category: [options?.subCategoryId || "null"],
        }),
        ...(options?.plaidCategoryId && {
            plaid_category: [options.plaidCategoryId],
        }),
        ...(options?.contract_id && {
            contract_id: options.contract_id,
        }),
        ...(options?.transaction_id && {
            transaction_id: options.transaction_id,
        }),
        ...(options?.stack_only && {stack_only: "1"}),
        ...(options?.subscriptionId && {subscription_id: options.subscriptionId}),
    });
    const [transactionsItemsPerPage, setTransactionsItemsPerPage] = useState<number>(20);
    const [transactionsPage, setTransactionsPage] = useState<number>(1);
    const [transactionsSearch, setTransactionsSearch] = useState<string>("");
    const [subscriptionFilterState, setSubscriptionFilterState] = useState<INewFilters | undefined>({
        ...(options?.dates?.start_date &&
            options?.dates?.end_date && {
                start_date: options?.dates?.start_date,
                end_date: options.dates.end_date,
            }),
        ...(options?.categoryId !== undefined && {
            category: [options?.categoryId || "null"],
        }),
        ...(options?.subCategoryId !== undefined && {
            sub_category: [options?.subCategoryId || "null"],
        }),
        ...(options?.plaidCategoryId && {
            plaid_category: [options.plaidCategoryId],
        }),
        ...(options?.stack_only && {stack_only: "1"}),
        ...(options?.subscriptionId && {subscription_id: options.subscriptionId}),
    });
    const [subscriptionItemsPerPage, setSubscriptionsItemsPerPage] = useState<number>(20);
    const [subscriptionPage, setSubscriptionsPage] = useState<number>(1);
    const [subscriptionSearch, setSubscriptionsSearch] = useState<string>("");
    const [priorTransactionsFilterState, setPriorTransactionsFilterState] = useState<INewFilters | undefined>({
        sort: "sorting_date__DESC",
    });
    const [priorTransactionsSearch, setPriorTransactionsSearch] = useState<string>("");
    const [linkedAccountsSearch, setLinkedAccountsSearch] = useState<string>("");
    const [linkedAccountsFilterState, setLinkedAccountsFilterState] = useState<INewFilters>({});
    const now = DateTime.now();
    const start = now.minus({days: 30}).toUTC().toISO();
    const end = now.endOf("day").toUTC().toISO();
    const [expensesBreakdownOverviewFilters, setExpensesBreakdownOverviewFilters] = useState<IGetOverviewBreakdownBody>(
        {
            start_date: start,
            end_date: end,
            filter: options?.expensesBreakdownOverviewDefaultToggle || "all",
            ...(options?.hide_uncategorized && {hide_uncategorized: 1}),
            ...(options?.stack_only && {stack_only: "1"}),
        },
    );
    const [alertNotificationsFilterState, setAlertNotificationsFilterState] = useState<INewFilters | undefined>();
    const [alertNotificationsSearch, setAlertNotificationsSearch] = useState<string>("");
    const {updateCompanyExpenses, isUpdating} = useCompanyExpenseMutations(company_id);
    const queryClient = useQueryClient();
    const {isLoading: isLoadingPlaidEnabled, isAvailable: isPlaidEnabled} = useIsPlaidAvailable();
    const subscriptionId = options?.subscriptionId;

    const shouldCall = (call: string) => {
        if (options?.calls) {
            const allValue = options.calls["all"];
            const value = options.calls[call];
            return value === undefined ? (allValue === undefined ? true : allValue) : value;
        }
        return true;
    };

    const linkedAccounts: UseQueryResult<ILinkedAccountData[] | undefined> = useQuery<ILinkedAccountData[] | undefined>(
        ["all-company-expenses-linked-accounts", company_id],
        async () => {
            if (company_id) {
                const promiseToReturn: Promise<ILinkedAccountData[]> = new Promise((resolve, reject) =>
                    plaidService
                        .getAllLinkedAccounts(company_id)
                        .then((r: AxiosResponse<ILinkedAccountData[]>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && options?.calls?.linkedAccounts !== false,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const furtherCallsEnabled: boolean =
        options?.calls?.linkedAccounts === false ? true : !linkedAccounts?.isLoading && !!linkedAccounts.data?.length;

    const pagedLinkedAccounts = useInfiniteQuery<IPaginationData<ILinkedAccountData[]> | undefined>(
        [
            "company-expenses-linked-accounts",
            company_id,
            linkedAccountsSearch,
            JSON.stringify(linkedAccountsFilterState),
        ],
        async ({pageParam = undefined}) => {
            if (company_id && shouldCall("pagedLinkedAccounts")) {
                const promiseToReturn: Promise<IPaginationData<ILinkedAccountData[]>> = new Promise((resolve, reject) =>
                    plaidService
                        .getLinkedAccounts(company_id, {
                            page: pageParam || 1,
                            search_word: linkedAccountsSearch,
                            dynamic: linkedAccountsFilterState,
                        })
                        .then((r: AxiosResponse<IPaginationData<ILinkedAccountData[]>>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            staleTime: 0,
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
            enabled: Boolean(company_id) && shouldCall("pagedLinkedAccounts"),
            refetchOnMount: true,
        },
    );

    const expensesCount: UseQueryResult<IExpensesCountResponse | undefined> = useQuery<
        IExpensesCountResponse | undefined
    >(
        ["company-expenses-counts", company_id],
        async () => {
            if (company_id && shouldCall("expensesCount")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    plaidService
                        .getExpensesCount(company_id)
                        .then((r: AxiosResponse<any>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("expensesCount") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const expensesOverTime: UseQueryResult<ExpenseItem[] | undefined> = useQuery<ExpenseItem[] | undefined>(
        ["company-expenses-over-time", company_id, JSON.stringify(expensesOverTimeRequestBody)],
        async () => {
            if (company_id && shouldCall("expensesOverTime")) {
                const promiseToReturn: Promise<ExpenseItem[]> = new Promise((resolve, reject) =>
                    plaidService
                        .getExpensesOverTime(company_id, expensesOverTimeRequestBody)
                        .then((r: AxiosResponse<ExpenseItem[]>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("expensesOverTime") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const expensesSummary: UseQueryResult<IExpenseSummaryResponse | undefined> = useQuery<
        IExpenseSummaryResponse | undefined
    >(
        ["company-expenses-summary", company_id, JSON.stringify(expenseSummaryDates)],
        async () => {
            if (company_id && shouldCall("expensesSummary")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    plaidService
                        .getExpensesSummary(company_id, {
                            ...expenseSummaryDates,
                            ...(options?.expensesSummaryFilters?.category && {
                                category: options.expensesSummaryFilters.category,
                            }),
                            ...(options?.expensesSummaryFilters?.sub_category && {
                                sub_category: options.expensesSummaryFilters.sub_category,
                            }),
                        })
                        .then((r: AxiosResponse<any>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("expensesSummary") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const subscriptions: UseQueryResult<IPaginationData<IExpenseSubscription[]> | undefined> = useQuery<
        any | undefined
    >(
        [
            "company-expenses-subscriptions",
            company_id,
            JSON.stringify(subscriptionFilterState),
            subscriptionItemsPerPage,
            subscriptionPage,
            subscriptionSearch,
        ],
        async () => {
            if (!!company_id && shouldCall("subscriptions")) {
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    plaidService
                        .getSubscriptions(company_id, {
                            page: subscriptionPage,
                            items_per_page: subscriptionItemsPerPage,
                            search_word: subscriptionSearch,
                            dynamic: subscriptionFilterState,
                        })
                        .then((r: AxiosResponse<any>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(null));
        },
        {
            enabled: Boolean(company_id) && shouldCall("subscriptions") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const infiniteSubscriptions = useInfiniteQuery<IPaginationData<IExpenseSubscription[]> | undefined>(
        [
            "company-expenses-infinite-subscriptions",
            company_id,
            JSON.stringify(subscriptionFilterState),
            subscriptionItemsPerPage,
            subscriptionSearch,
        ],
        async ({pageParam = 1}) => {
            if (!!company_id && shouldCall("infiniteSubscriptions")) {
                const response = await plaidService.getSubscriptions(company_id, {
                    page: pageParam,
                    items_per_page: subscriptionItemsPerPage,
                    search_word: subscriptionSearch,
                    dynamic: subscriptionFilterState,
                });
                return response.data;
            }
            return undefined;
        },
        {
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
            enabled: Boolean(company_id) && shouldCall("infiniteSubscriptions"),
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const subscriptionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined> = useQuery<any | undefined>(
        ["company-expenses-subscriptions-filters", company_id],
        async () => {
            if (company_id && shouldCall("subscriptionsFilters")) {
                const dateRange = {
                    start_date: subscriptionFilterState?.start_date as string,
                    end_date: subscriptionFilterState?.end_date as string,
                };
                const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                    plaidService
                        .getSubscriptionsFilters(company_id, dateRange)
                        .then((r: AxiosResponse<any>) => {
                            resolve(buildFilters(updateFilterNullIds(r.data?.filters), r.data?.sorts || {}));
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("subscriptionsFilters") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
            staleTime: 0,
            refetchOnMount: true,
        },
    );

    const expensesBreakdown: UseQueryResult<IExpenseBreakdownItem[] | undefined> = useQuery<
        IExpenseBreakdownItem[] | undefined
    >(
        ["company-expenses-breakdown", company_id, JSON.stringify(expensesBreakdownFilterState)],
        async () => {
            if (company_id && shouldCall("expensesBreakdown")) {
                const promiseToReturn: Promise<IExpenseBreakdownItem[]> = new Promise((resolve, reject) =>
                    plaidService
                        .getExpensesBreakdown(company_id, {dynamic: expensesBreakdownFilterState})
                        .then((r: AxiosResponse<IExpenseBreakdownItem[]>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled:
                Boolean(company_id) &&
                shouldCall("expensesBreakdown") &&
                (!!expensesBreakdownFilterState?.date ||
                    (!!expensesBreakdownFilterState.start_date && !!expensesBreakdownFilterState.end_date)) &&
                furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const expensesBreakdownFilters: UseQueryResult<IPageInnerHeadFilter | undefined> = useQuery<
        IPageInnerHeadFilter | undefined
    >(
        ["company-expenses-breakdown-filters", company_id],
        async () => {
            if (company_id && shouldCall("expensesBreakdown")) {
                const promiseToReturn: Promise<IPageInnerHeadFilter> = new Promise((resolve, reject) =>
                    plaidService
                        .getExpensesBreakdownFilters(company_id)
                        .then(r => {
                            resolve(buildFilters(r.data?.filters, {}));
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("expensesBreakdown") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const transactions: UseQueryResult<IPaginationData<ITransactionItem[]> | undefined> = useQuery<
        IPaginationData<ITransactionItem[]> | undefined
    >(
        [
            "company-expenses-transactions",
            company_id,
            JSON.stringify(transactionsFilterState),
            transactionsItemsPerPage,
            transactionsPage,
            transactionsSearch,
            options?.categoryId || options?.transactionsVendorId || options?.contract_id,
        ],
        async () => {
            if (company_id && shouldCall("transactions")) {
                const promiseToReturn: Promise<IPaginationData<ITransactionItem[]>> = new Promise((resolve, reject) =>
                    plaidService
                        .getTransactions(company_id, {
                            page: transactionsPage,
                            items_per_page: transactionsItemsPerPage,
                            search_word: transactionsSearch,
                            dynamic: transactionsFilterState,
                        })
                        .then((r: AxiosResponse<IPaginationData<ITransactionItem[]>>) => {
                            resolve(r.data);
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("transactions") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const infiniteTransactions = useInfiniteQuery(
        [
            "infinite-company-expenses-transactions",
            company_id,
            JSON.stringify(transactionsFilterState),
            transactionsItemsPerPage,
            transactionsSearch,
            options?.categoryId || options?.transactionsVendorId,
        ],
        async ({pageParam = 1}) => {
            if (company_id && shouldCall("infiniteTransactions")) {
                const response = await plaidService.getTransactions(company_id, {
                    page: pageParam,
                    items_per_page: transactionsItemsPerPage,
                    search_word: transactionsSearch,
                    dynamic: transactionsFilterState,
                });
                return response.data;
            }
            return undefined;
        },
        {
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
            enabled: Boolean(company_id) && shouldCall("infiniteTransactions") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const transactionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined> = useQuery<
        IPageInnerHeadFilter | undefined
    >(
        [
            "company-expenses-transactions-filters",
            company_id,
            transactionsFilterState?.start_date || "",
            transactionsFilterState?.end_date || "",
        ],
        async () => {
            if (company_id && shouldCall("transactionsFilters")) {
                const dateRange = {
                    start_date: transactionsFilterState?.start_date as string,
                    end_date: transactionsFilterState?.end_date as string,
                };
                const promiseToReturn: Promise<IPageInnerHeadFilter> = new Promise((resolve, reject) =>
                    plaidService
                        .getTransactionsFilters(company_id, dateRange)
                        .then(r => {
                            resolve(buildFilters(updateFilterNullIds(r.data?.filters), r.data?.sorts || {}));
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("transactionsFilters") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
            staleTime: 0,
            refetchOnMount: true,
        },
    );

    const priorTransactions: UseInfiniteQueryResult<IPaginationData<IPriorTransactionItem[]> | undefined> =
        useInfiniteQuery<IPaginationData<IPriorTransactionItem[]> | undefined>(
            [
                "company-expenses-prior-transactions",
                company_id,
                subscriptionId,
                JSON.stringify(priorTransactionsFilterState),
                priorTransactionsSearch,
            ],
            async ({pageParam}) => {
                if (company_id && shouldCall("priorTransactions") && subscriptionId) {
                    const promiseToReturn: Promise<IPaginationData<IPriorTransactionItem[]>> = new Promise(
                        (resolve, reject) =>
                            plaidService
                                .getPriorTransactions(company_id, subscriptionId, {
                                    page: pageParam || 1,
                                    search_word: priorTransactionsSearch,
                                    dynamic: priorTransactionsFilterState,
                                })
                                .then((r: AxiosResponse<IPaginationData<IPriorTransactionItem[]>>) => {
                                    resolve(r.data);
                                })
                                .catch((e: AxiosError<any>) => reject(e)),
                    );
                    return promiseToReturn;
                }
                return new Promise(resolve => resolve(undefined));
            },
            {
                enabled:
                    Boolean(company_id) &&
                    shouldCall("priorTransactions") &&
                    Boolean(subscriptionId) &&
                    furtherCallsEnabled,
                getNextPageParam: res => {
                    return res?.meta
                        ? res?.meta?.last_page > res?.meta?.current_page
                            ? res?.meta?.current_page + 1
                            : null
                        : null;
                },
                ...DEFAULT_QUERY_CONFIGS(),
            },
        );

    const priorTransactionsFilters: UseQueryResult<IPageInnerHeadFilter | undefined> = useQuery<
        IPageInnerHeadFilter | undefined
    >(
        ["company-expenses-prior-transactions-filters", company_id, subscriptionId],
        async () => {
            if (company_id && shouldCall("priorTransactionsFilters") && subscriptionId) {
                const promiseToReturn: Promise<IPageInnerHeadFilter> = new Promise((resolve, reject) =>
                    plaidService
                        .getPriorTransactionsFilters(company_id, subscriptionId)
                        .then((r: AxiosResponse<any>) => {
                            resolve(buildFilters(r.data?.filters, r.data?.sorts || {}));
                        })
                        .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled:
                Boolean(company_id) &&
                shouldCall("priorTransactionsFilters") &&
                Boolean(subscriptionId) &&
                furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
            refetchOnMount: true,
        },
    );

    const subscriptionCosts: UseQueryResult<ISubscriptionsCostResponse | undefined> = useQuery<
        ISubscriptionsCostResponse | undefined
    >(
        ["company-expenses-subscription-costs", company_id, options?.costsCategoryId, options?.costsVendorId],
        async () => {
            if (company_id && shouldCall("subscriptionCosts")) {
                const promiseToReturn: Promise<ISubscriptionsCostResponse> = new Promise((resolve, reject) =>
                    options?.costsCategoryId !== undefined
                        ? plaidService
                              .getSubscriptionsCostByCategory(company_id, options.costsCategoryId || "")
                              .then((r: AxiosResponse<ISubscriptionsCostResponse>) => {
                                  resolve(r.data);
                              })
                              .catch((e: AxiosError<any>) => reject(e))
                        : options?.costsVendorId !== undefined
                          ? plaidService
                                .getSubscriptionsCostByVendor(company_id, options.costsVendorId || "")
                                .then((r: AxiosResponse<ISubscriptionsCostResponse>) => {
                                    resolve(r.data);
                                })
                                .catch((e: AxiosError<any>) => reject(e))
                          : plaidService
                                .getSubscriptionsCost(company_id, options?.stack_only || false)
                                .then((r: AxiosResponse<any>) => {
                                    resolve(r.data);
                                })
                                .catch((e: AxiosError<any>) => reject(e)),
                );
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("subscriptionCosts") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const allExpensesBarChartData: UseQueryResult<AllExpensesBarChartResponse | undefined> = useQuery<any | undefined>(
        ["all-expenses-chart-data", company_id, JSON.stringify(options?.allExpensesBarChartDates)],
        async () => {
            if (company_id && shouldCall("allExpensesBarChartData")) {
                const promiseToReturn: Promise<AllExpensesBarChartResponse> = new Promise((resolve, reject) => {
                    if (!options?.allExpensesBarChartDates) {
                        reject();
                        return;
                    }
                    return plaidService
                        .getExpensesOverviewOvertime(company_id, options.allExpensesBarChartDates)
                        .then(res => resolve(res.data))
                        .catch(err => reject(err));
                });
                return promiseToReturn;
            }
            return new Promise(resolve => resolve(undefined));
        },
        {
            enabled: Boolean(company_id) && shouldCall("allExpensesBarChartData") && furtherCallsEnabled,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const expensesBreakdownOverview = useQuery<IAllExpensesBreakdownItem[]>(
        ["company-expenses-all-expenses-breakdown", company_id, JSON.stringify(expensesBreakdownOverviewFilters)],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getOverviewBreakdown(company_id || "", expensesBreakdownOverviewFilters)
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!company_id && shouldCall("expensesBreakdownOverview") && furtherCallsEnabled,
        },
    );

    const alertSettings = useQuery<IAlertSettingsResponse>(
        ["company-expenses-alert-settings", company_id],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getCompanyAlertSettings(company_id || "")
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!company_id && shouldCall("alertSettings") && furtherCallsEnabled},
    );

    const alertOptions = useQuery<IPlaidAlertNotificationOption[]>(
        ["company-expenses-alert-options"],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getPlaidNotificationAlertOptions()
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!company_id && shouldCall("alertOptions")},
    );

    const alertPeriodOptions = useQuery<IPlaidAlertPeriodNotificationOption[]>(
        ["company-expenses-alert-period-options"],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getPlaidNotificationAlertPeriodOptions()
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!company_id && shouldCall("alertPeriodOptions")},
    );

    const memoizedNotificationFilterState = useMemo(
        () => JSON.stringify(alertNotificationsFilterState),
        [alertNotificationsFilterState],
    );

    const alertNotifications = useInfiniteQuery<IPaginationData<IPlaidAlertNotification[]>>(
        [
            "company-expenses-alert-notifications",
            company_id,
            alertNotificationsSearch || "",
            memoizedNotificationFilterState,
        ],
        ({pageParam}) => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getCompanyAlertNotifications(company_id || "", {
                        page: pageParam || 1,
                        search_word: alertNotificationsSearch,
                        items_per_page: 10,
                        dynamic: alertNotificationsFilterState,
                    })
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!company_id && shouldCall("alertNotifications") && furtherCallsEnabled,
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
        },
    );

    const alertNotificationsFilters = useQuery<IPageInnerHeadFilter>(
        ["company-expenses-alert-notifications-filters", company_id],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getCompanyAlertNotificationsFilters(company_id || "")
                    .then(r => resolve(buildFilters(r.data?.filters, r.data?.sorts || {})))
                    .catch(err => reject(err));
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!company_id && shouldCall("alertNotifications") && furtherCallsEnabled},
    );

    const upcomingSummary = useQuery<IUpcomingExpensesSummary>(
        ["upcomingSummary", company_id],
        () => {
            return new Promise((resolve, reject) => {
                plaidService
                    .getUpcomingExpensesSummary(company_id!)
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!company_id && shouldCall("upcomingSummary") && furtherCallsEnabled,
        },
    );

    const hasNoAccounts = !linkedAccounts.isLoading && !linkedAccounts.data?.length;

    const handlePlaidLinkComplete = () => {
        if (hasNoAccounts) {
            setIsInitialSyncInProgress(true);
        }
    };

    useEffect(() => {
        if (!linkedAccounts.data?.length) return;
        const isInititalSyncInProgressValue = hasNoAccounts
            ? false
            : linkedAccounts?.data?.reduce((prev, linkedAccount) => {
                  return prev && linkedAccount?.is_syncing;
              }, true) || false;
        if (isInititalSyncInProgressValue !== isInitialSyncInProgress)
            setIsInitialSyncInProgress(isInititalSyncInProgressValue);
        setHasSomeAccountsSyncing(!!linkedAccounts.data?.some(linkedAccount => !!linkedAccount?.is_syncing));
    }, [linkedAccounts.dataUpdatedAt, hasNoAccounts]);

    useEffect(() => {
        setTransactionsPage(1);
    }, [transactionsFilterState]);

    return {
        handlePlaidLinkComplete,
        linkedAccounts,
        expensesCount,
        expensesOverTime,
        expensesSummary,
        hasNoAccounts,
        expenseSummaryDates,
        setExpenseSummaryDates,
        expensesOverTimeRequestBody,
        setExpensesOverTimeRequestBody,
        subscriptions,
        infiniteSubscriptions,
        subscriptionsFilters,
        expensesBreakdown,
        expensesBreakdownFilters,
        expensesBreakdownFilterState,
        setExpensesBreakdownFilterState,
        transactions,
        infiniteTransactions,
        transactionsFilters,
        transactionsFilterState,
        setTransactionsFilterState,
        transactionsItemsPerPage,
        setTransactionsItemsPerPage,
        transactionsPage,
        setTransactionsPage,
        transactionsSearch,
        setTransactionsSearch,
        subscriptionFilterState,
        setSubscriptionFilterState,
        subscriptionItemsPerPage,
        setSubscriptionsItemsPerPage,
        subscriptionSearch,
        setSubscriptionsSearch,
        subscriptionPage,
        setSubscriptionsPage,
        priorTransactions,
        priorTransactionsFilters,
        priorTransactionsFilterState,
        setPriorTransactionsFilterState,
        priorTransactionsSearch,
        setPriorTransactionsSearch,
        subscriptionCosts,
        allExpensesBarChartData,
        pagedLinkedAccounts,
        linkedAccountsSearch,
        setLinkedAccountsSearch,
        isPlaidEnabled,
        isLoadingPlaidEnabled,
        isInitialSyncInProgress,
        expensesBreakdownOverview,
        expensesBreakdownOverviewFilters,
        setExpensesBreakdownOverviewFilters,
        linkedAccountsFilterState,
        setLinkedAccountsFilterState,
        alertSettings,
        alertOptions,
        alertPeriodOptions,
        alertNotifications,
        alertNotificationsFilters,
        alertNotificationsFilterState,
        setAlertNotificationsFilterState,
        alertNotificationsSearch,
        setAlertNotificationsSearch,
        upcomingSummary,
        hasSomeAccountsSyncing,
        updateCompanyExpenses,
        isUpdating,
    };
}
