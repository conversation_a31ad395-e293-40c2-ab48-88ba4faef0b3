import {useQuery} from "@tanstack/react-query";
import {categoriesService} from "../../services/category.service";
import {ICategory} from "../../Interfaces/categories.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";

const fetchCategories = async (company_type?: string): Promise<ICategory[]> => {
    const response = await categoriesService.getAllCategories();
    return response.data;
};

export const useCategories = (
    company_type?: string,
    options?: {
        disableAllCategories?: boolean;
        plaidCategories?: boolean;
    },
) => {
    const categoriesQuery = useQuery({
        queryKey: ["category-autocomplete", company_type || null],
        queryFn: () => fetchCategories(company_type),
        ...DEFAULT_QUERY_CONFIGS(),
        staleTime: Infinity,
        cacheTime: Infinity,
        retry: false, // Disable automatic retries
        networkMode: "always", // Ensures request is consistent
        enabled: !options?.disableAllCategories,
    });

    const plaidCategoriesQuery = useQuery(
        ["plaid-categories-search"],
        () => {
            return new Promise((resolve, reject) => {
                categoriesService
                    .getAllCategories(undefined, undefined, undefined, 1, undefined, undefined, "PLAID")
                    .then(res => resolve(res.data.map(c => ({id: c.id, label: c.name, color: c.color}))))
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!options?.plaidCategories,
        },
    );

    return {
        categories: categoriesQuery,
        plaidCategories: plaidCategoriesQuery,
    };
};
