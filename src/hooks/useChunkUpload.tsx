import {useEffect, useRef, useState} from "react";
import useAppConfig from "./useAppConfig";
import {AxiosResponse} from "axios";
import useNotification from "./useNotification";
import {v4 as uuidv4} from "uuid";
import {getErrorFromArray} from "../utils/error.util";
import {chunkConfigKeys, chunkSizeKey, maxParConnKey, maxSizeKey} from "../constants/commonStrings.constant";

export interface IFileQueue {
    title: string;
    categories?: string[];
    tags?: string[];
    thumbnail: File;
    handleUploadProgress: Function;
    service: any;
    description: string;
    duration?: string;
    handleError?: Function;
    is_partner_content?: boolean;
    partner_page_id?: string;
    partner_page_section_id?: string;

    //? Added for pitch video upload
    isPitchVideoUpload?: boolean;
    file?: File;
    pitch_event_id?: string;
    vendor_id?: string;
    media_visibility?: string;
}

interface IChunkObj {
    id: string;
    index: number;
    data: Blob;
    size: number;
    started: boolean;
    isLastChunk: boolean;
    isPitchVideoUpload?: boolean;
}

export default function useChunkUpload(enabled: boolean = true) {
    if (!enabled) {
        return {
            getFileContext: () => null,
            triggerUpload: () => null,
        };
    }
    const [progress, setProgress] = useState(0);

    const {configs} = useAppConfig({config_key: chunkConfigKeys});
    const maxChunkSize = parseInt(configs?.find(c => c.key === chunkSizeKey)?.value || "50") * 1024 * 1024;
    const maxVideoSize = parseInt(configs?.find(c => c.key === maxSizeKey)?.value || "5000");
    const maxParallelConnections = parseInt(configs?.find(c => c.key === maxParConnKey)?.value || "1");

    const notify = useNotification();
    const fileToBeUpload = useRef<File>();
    const fileSize = useRef<number>(0);
    const chunkCount = useRef<number>(0);
    const fileUID = useRef<string>();
    const titleData = useRef<string>();
    const categoriesData = useRef<string[]>();
    const tagsData = useRef<string[]>();
    const descriptionData = useRef<string>();
    const durationData = useRef<string>();
    const thumbnailData = useRef<File>();
    const isPartnerContentData = useRef<boolean>();
    const partnerPageIdData = useRef<string>();
    const partnerPageSectionIdData = useRef<string>();
    const pitchVideoUploadData = useRef<undefined | {file?: File; pitch_event_id?: string; vendor_id?: string}>();
    const uploadService = useRef<(data: FormData, id: string) => Promise<AxiosResponse<any, any>>>();
    const progressHandler = useRef<Function>();
    const entityId = useRef<string>();
    const chunkStorage = useRef<IChunkObj[]>([]);
    const uploadSpots = useRef<Array<{index: number} | null>>(new Array(maxParallelConnections).fill(null));
    const count = useRef(0);
    const errorHandler = useRef<any>();
    const mediaVisibility = useRef<string>();

    const getFileContext = (file: File, isPitchVideoUpload?: boolean) => {
        const _file = file;
        if (!_file) return;
        if (_file.size > maxVideoSize * 1024 * 1024) {
            notify("File size is too large", "Error");
            return;
        }
        fileSize.current = _file.size;
        const _totalCount =
            _file.size % maxChunkSize === 0 ? _file.size / maxChunkSize : Math.floor(_file.size / maxChunkSize) + 1;
        chunkCount.current = _totalCount;
        fileToBeUpload.current = _file;
        const _uuid = uuidv4();
        fileUID.current = _uuid;
        chunkStorage.current = new Array(_totalCount).fill(null);
        chunkStorage.current.forEach((_, index) => {
            const _begin = index * maxChunkSize;
            const _end = _begin + maxChunkSize;
            const _chunk = _file.slice(_begin, _end);
            const _size = _chunk.size;
            chunkStorage.current[index] = {
                id: _uuid,
                index: index + 1,
                data: _chunk,
                size: _size,
                started: false,
                isLastChunk: index === _totalCount - 1,
                isPitchVideoUpload: isPitchVideoUpload ? true : false,
            };
        });
    };

    const setupRefs = (options: IFileQueue) => {
        titleData.current = options.title;
        categoriesData.current = options.categories;
        tagsData.current = options.tags;
        thumbnailData.current = options.thumbnail;
        uploadService.current = options.service;
        descriptionData.current = options.description;
        durationData.current = options.duration;
        progressHandler.current = options.handleUploadProgress;
        errorHandler.current = options.handleError || undefined;
        isPartnerContentData.current = options?.is_partner_content;
        partnerPageIdData.current = options?.partner_page_id;
        mediaVisibility.current = options?.media_visibility;
        partnerPageSectionIdData.current = options?.partner_page_section_id;
        pitchVideoUploadData.current = options?.isPitchVideoUpload
            ? {
                  //   file: options.file,
                  pitch_event_id: options.pitch_event_id,
                  vendor_id: options.vendor_id,
              }
            : undefined;
    };

    const fileUpload = (options?: IFileQueue, id?: string) => {
        if (!titleData.current && options) setupRefs(options);
        if (!entityId.current && id) entityId.current = id;
        const chunkObj = chunkStorage.current.find(c => !c?.started && c?.index === count.current + 1);
        if (!chunkObj) return;
        if (chunkObj.isLastChunk && !uploadSpots.current.every(spot => spot === null)) return;
        uploadChunk(chunkObj);
    };

    const uploadChunk = async (chunkObj: IChunkObj) => {
        try {
            const spotIndex = await uploadSpots.current.findIndex(spot => spot === null);
            uploadSpots.current[spotIndex] = await {index: chunkObj.index};
            chunkStorage.current[chunkObj.index - 1].started = true;
            count.current++;
            if (uploadSpots.current.some(spot => spot === null)) {
                fileUpload();
            }
            const formData = new FormData();
            if (fileUID.current) formData.append("dzuuid", fileUID.current);
            formData.append("dzchunkindex", chunkObj.index.toString());
            formData.append("dztotalfilesize", fileSize.current.toString());
            formData.append("dzchunksize", chunkObj.size.toString());
            formData.append("dztotalchunkcount", chunkCount.current.toString());
            formData.append("dzchunkbyteoffset", (chunkObj.index * maxChunkSize).toString());
            if (fileToBeUpload.current?.name)
                formData.append("file_extension", fileToBeUpload.current.name.split(".").pop()!);
            if (fileToBeUpload.current?.type) formData.append("mime_type", fileToBeUpload.current.type);
            if (chunkObj.isLastChunk) {
                if (titleData.current) formData.append("title", titleData.current);
                if (durationData.current) formData.append("duration", durationData.current);
                if (descriptionData.current) formData.append("description", descriptionData.current);
                if (categoriesData.current?.length)
                    categoriesData.current.forEach(c => formData.append("categories[]", c));
                if (tagsData.current?.length) tagsData.current.forEach(t => formData.append("tags[]", t));
                if (thumbnailData.current) formData.append("thumbnail", thumbnailData.current);
                if (isPartnerContentData.current) formData.append("is_partner_content", "1");
                if (partnerPageIdData.current) formData.append("partner_page_id", partnerPageIdData.current);
                if (partnerPageSectionIdData.current)
                    formData.append("partner_page_section_id", partnerPageSectionIdData.current);
                if (pitchVideoUploadData.current) {
                    formData.append("pitch_event_id", pitchVideoUploadData.current.pitch_event_id || "");
                    formData.append("vendor_id", pitchVideoUploadData.current.vendor_id || "");
                }
                if (mediaVisibility.current) formData.append("media_visibility", mediaVisibility.current);
            }
            formData.append("video", chunkObj.data);
            if (!uploadService.current) return;
            const response = await uploadService.current(formData, entityId.current!);
            if (response.status === 200) {
                if (chunkObj.isLastChunk) {
                    await uploadCompleted();
                } else {
                    const _percentage = Math.round((chunkObj.size * 100) / fileSize.current);
                    setProgress(curr => {
                        const _new = curr + _percentage < 100 ? curr + _percentage : 100;
                        progressHandler.current?.(_new);
                        return _new;
                    });
                    const nullIndex = await uploadSpots.current.findIndex(spot => spot?.index === chunkObj.index);
                    uploadSpots.current[nullIndex] = await null;
                    if (chunkObj.index < chunkCount.current - 1) {
                        fileUpload();
                    }
                    if (uploadSpots.current.every(spot => spot === null) && chunkObj.index === chunkCount.current - 1) {
                        fileUpload();
                    }
                }
            }
        } catch (error: any) {
            errorHandler.current?.(error);
            notify(getErrorFromArray(error), "Error");
        }
    };

    const uploadCompleted = async () => {
        setProgress(100);
        progressHandler.current?.(100);
        titleData.current = undefined;
        categoriesData.current = undefined;
        tagsData.current = undefined;
        thumbnailData.current = undefined;
        uploadService.current = undefined;
        progressHandler.current = undefined;
        descriptionData.current = undefined;
        durationData.current = undefined;
        entityId.current = undefined;
        chunkStorage.current = [];
        uploadSpots.current = new Array(maxParallelConnections).fill(null);
        fileToBeUpload.current = undefined;
        fileUID.current = undefined;
        fileSize.current = 0;
        chunkCount.current = 0;
        count.current = 0;
        pitchVideoUploadData.current = undefined;
        setTimeout(() => {
            setProgress(0);
        }, 1000);
    };

    useEffect(() => {
        uploadSpots.current = new Array(maxParallelConnections).fill(null);
    }, [maxParallelConnections]);

    return {progress, getFileContext, file: fileToBeUpload, triggerUpload: fileUpload};
}
