import {useEffect, useState} from "react";
export const useTimeoutReset = (delay: number = 1000) => {
    const [tempValue, setTempValue] = useState<any>(null);

    useEffect(() => {
        const handler = setTimeout(() => {
            setTempValue(null);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
    }, [delay, tempValue]);
    return {tempValue, setTempValue};
};
