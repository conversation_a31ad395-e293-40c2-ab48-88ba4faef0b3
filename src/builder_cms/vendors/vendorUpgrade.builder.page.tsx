import {useEffect, useState} from "react";
import {Helmet} from "react-helmet";

import {BuilderComponent, builder} from "@builder.io/react";

const BuilderVendorUpgrade = () => {
    const [builderContent<PERSON>son, setBuilderContent<PERSON>son] = useState<any>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);

    useEffect(() => {
        builder
            .get("main-pages", {url: "/vendor-upgrade"})
            .promise()
            .then(data => {
                setBuilderContentJson(data);
                setBuilderSEO(data.data.seo);
            });
    }, []);

    return (
        <>
            <Helmet>
                <title>{builderSEO?.title || "Upgrade Vendor Account - Channel Program"}</title>
                <meta
                    name="description"
                    content={
                        builderSEO?.description ||
                        "Vendors can upgrade here to gain access to exclusive perks and rewards."
                    }
                />
                <meta
                    property="og:description"
                    content={
                        builderSEO?.description ||
                        "Vendors can upgrade here to gain access to exclusive perks and rewards."
                    }
                />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:type" content="website" />
                <meta
                    property="og:title"
                    content={builderSEO?.openGraphTitle || "Upgrade Vendor Account - Channel Program"}
                />
                <meta
                    name="og:description"
                    content={
                        builderSEO?.description ||
                        "Vendors can upgrade here to gain access to exclusive perks and rewards."
                    }
                />
                <meta
                    property="website:tag"
                    content={
                        builderSEO?.keywords ||
                        "channel program, it channel vendors, technology vendors, vendor upgrade"
                    }
                />
                <meta
                    property="keywords"
                    content={
                        builderSEO?.keywords ||
                        "channel program, it channel vendors, technology vendors, vendor upgrade"
                    }
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                <meta
                    name="twitter:title"
                    content={builderSEO?.twitterTitle || "Upgrade Vendor Account - Channel Program"}
                />
                <meta
                    name="twitter:description"
                    content={
                        builderSEO?.twitterDescription ||
                        "Vendors can upgrade here to gain access to exclusive perks and rewards."
                    }
                />
                <meta name="twitter:image" content={builderSEO?.twitterImage || "/Media/seo/twitter-home.jpg"} />
            </Helmet>
            <BuilderComponent model="main-pages" content={builderContentJson} />
        </>
    );
};

export default BuilderVendorUpgrade;
