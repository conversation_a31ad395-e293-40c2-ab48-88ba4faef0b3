import {useEffect, useState} from "react";
import styles from "../../styles/pages/pitch.module.sass";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import {Helmet} from "react-helmet";

import {BuilderComponent, builder} from "@builder.io/react";

export default function BuilderPitch() {
    const [builderContent<PERSON>son, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);

    useEffect(() => {
        builder
            .get("main-pages", {url: "/pitch"})
            .promise()
            .then(res => {
                setBuilderContentJson(res);
                setBuilderSEO(res.data.seo);
            });
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <Helmet>
                <title>{builderSEO?.title || "Channel Program - Pitch"}</title>
                <meta
                    name="description"
                    content={
                        builderSEO?.description ||
                        "You’re invited to Channel Pitch, a FREE livestream event! <PERSON> and <PERSON> will introduce you to innovative, emerging technology vendors."
                    }
                />
                <meta
                    property="og:description"
                    content={
                        builderSEO?.description ||
                        "You’re invited to Channel Pitch, a FREE livestream event! Kevin Lancaster and Matt Solomon will introduce you to innovative, emerging technology vendors."
                    }
                />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:title" content={builderSEO?.openGraphTitle || "Channel Program - Pitch"} />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords || "channel pitch, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords || "channel pitch, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta name="twitter:title" content={builderSEO?.twitterTitle || "Channel Program - Pitch"} />
                <meta
                    name="twitter:description"
                    content={
                        builderSEO?.twitterDescription ||
                        "You’re invited to Channel Pitch, a FREE livestream event! Kevin Lancaster and Matt Solomon will introduce you to innovative, emerging technology vendors."
                    }
                />
                <meta name="twitter:image" content={builderSEO?.twitterImage || "/Media/seo/twitter-home.jpg"} />
            </Helmet>
            <div className={styles.pitchContainer}>
                {builderContentJson && <BuilderComponent model="pitch" content={builderContentJson} />}
            </div>
        </div>
    );
}
