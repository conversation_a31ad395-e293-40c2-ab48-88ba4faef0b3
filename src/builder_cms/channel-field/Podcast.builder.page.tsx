import Container from "react-bootstrap/Container";
import {useEffect, useState} from "react";
import {Row} from "react-bootstrap";
import {useForm, useWatch} from "react-hook-form";
import styles from "../../styles/pages/cash.module.sass";
import Helmet from "react-helmet";
import EpisodeComponent from "./Episode.builder.component";
import Loader from "../../utils/loader";

import {builder} from "@builder.io/react";

export default function BuilderPodcast() {
    const [sorting, setSorting] = useState<string>("new");

    const methods = useForm();
    const watcher = useWatch({control: methods.control});

    const [podcastPages, setBuilderContentJson] = useState<object[]>([]);

    const getPodcasts = (): void => {
        builder.getAll("blog").then(data => {
            setBuilderContentJson(data);
        });
    };

    useEffect(() => {
        getPodcasts();
    }, []);

    useEffect(() => {
        if (watcher.podcastOrder === "old" && sorting) {
            setSorting("old");
            setBuilderContentJson(podcastPages!.reverse());
        }
        if (watcher.podcastOrder === "new") {
            setSorting("new");
            getPodcasts();
        }
    }, [watcher.podcastOrder, sorting, podcastPages]);

    return (
        <>
            <div className={styles.podcastWrapper}>
                <Helmet>
                    <title>{"Channelfied Podcast | The IT Channel's #1 Podcast"}</title>
                    <meta
                        name="description"
                        content={
                            "Channel Program brings you the latest IT industry podcast. Hear from some of today's thought leaders, innovators and experts on topics ranging from entrepreneurship to cybersecurity."
                        }
                    />
                    <meta
                        property="og:description"
                        content={
                            "Channel Program brings you the latest IT industry podcast. Hear from some of today's thought leaders, innovators and experts on topics ranging from entrepreneurship to cybersecurity."
                        }
                    />
                    <meta property="og:url" content="https://www.channelprogram.com" />
                    <meta property="og:site_name" content="Channel Program" />
                    <meta property="og:title" content={"The Most Innovative IT Channel Collaboration Platform"} />
                    <meta property="og:locale" content="en_US" />
                    <meta property="og:type" content="website" />
                    <meta property="website:tag" content={"channel podcast, channel industry, podcast"} />
                    <meta property="keywords" content={"channel podcast, channel industry, podcast"} />
                    <meta property="robots" content="index,follow" />
                    <meta property="og:image" content={"/Media/seo/og-home.jpg"} />
                    <meta property="og:image:type" content="image/jpeg" />
                    <meta property="og:image:width" content="800" />
                    <meta property="og:image:height" content="600" />
                    <meta property="fb:admins" content="Channel-Program-113031624351535" />
                    <meta name="twitter:card" content="summary" />
                    <meta name="twitter:site" content="@bethechannel" />
                    <meta name="twitter:title" content={"The IT Channel's #1 Podcast"} />
                    <meta
                        name="twitter:description"
                        content={
                            "Channel Program brings you the latest IT industry podcast. Hear from some of today's thought leaders, innovators and experts on topics ranging from entrepreneurship to cybersecurity."
                        }
                    />
                    <meta name="twitter:image" content={"/Media/seo/twitter-home.jpg"} />
                </Helmet>

                <Container>
                    <img className={styles.connector} alt="Connector dots" src={"/Media/hero-connector-icon.png"} />
                    <h1 className={styles.pageTitle}>Podcasts</h1>
                    {podcastPages.length > 0 ? (
                        <Row className={styles.podcastGrid}>
                            {podcastPages?.map((podcast: object) => (
                                <EpisodeComponent
                                    key={podcast["id"]}
                                    thumbnail={podcast["data"].thumbnail}
                                    title={podcast["data"].pageTitle}
                                    publishDate={podcast["data"].datePosted}
                                    description={podcast["data"].pageDescription}
                                    channel="Channelfied Podcast"
                                    pid={podcast["id"]}
                                    hideBtn
                                />
                            ))}{" "}
                        </Row>
                    ) : (
                        <Row>
                            <Loader />
                        </Row>
                    )}
                </Container>
            </div>
            <Container className={styles.podcastFooter}>
                <img
                    className={styles.podcastFooterImg}
                    alt="Woman with white connector overlaying large C"
                    src={"/Media/podcastFooter.png"}
                />
                <img
                    className={styles.podcastFooterBG}
                    alt="Dots and lines"
                    src={"/Media/backgrounds/dotted-grid-for-orange.png"}
                />
                <button className={styles.podcastFooterBtn}>
                    <a href="/register">Join Channel Program today!</a>
                </button>
            </Container>
        </>
    );
}
