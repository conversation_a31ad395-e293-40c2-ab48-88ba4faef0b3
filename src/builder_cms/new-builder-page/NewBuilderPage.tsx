import {BuilderComponent, builder} from "@builder.io/react";
import {useEffect, useState} from "react";
import {Helmet} from "react-helmet";
import {useLocation, useNavigate} from "react-router-dom";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import styles from "../../styles/builder/newBuilderPage/newBuilderPage.module.sass";
import Loader from "../../utils/loader";

export default function NewBuilderPage() {
    const [loading, setLoading] = useState<boolean>(true);
    const [modelType, setModelType] = useState<string>("main-pages");
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);
    const navigate = useNavigate();
    const location = useLocation();
    const {authState} = useAuthState();
    const isLoggedIn = authState.authenticated;
    const notify = useNotification();

    useEffect(() => {
        setLoading(true);
        if (location.pathname === routeConfig.MSPClientLoginPage.path) {
            //This condition is added because of a weird behavior of the
            //builder.get("main-pages", {url: location.pathname})
            //it was finding the MSPClientLoginPage.path inside the result
            //causing the logout from an msp client to show a wrong login page
            builder
                .get("sign-up-pages", {url: location.pathname})
                .promise()
                .then(res => {
                    if (res) {
                        setModelType("sign-up-pages");
                        setBuilderContentJson(res);
                        setBuilderSEO(res?.data?.seo);
                    } else {
                        // If no main-page or sign-up-page with current url was found, return to home
                        navigate(routeConfig.Home.path);
                    }
                })
                .catch(() => {
                    navigate(routeConfig.Home.path);
                });
        } else {
            builder
                .get("main-pages", {url: location.pathname})
                .promise()
                .then(res => {
                    if (res) {
                        if (res?.data?.privatePage) {
                            if (!isLoggedIn) {
                                notify("Please sign in or create an account to view this page", "Error");
                                navigate(routeConfig.Login.path + "?redirect_url=" + location.pathname);
                            }
                        }
                        setModelType("main-pages");
                        setBuilderContentJson(res);
                        setBuilderSEO(res?.data?.seo);
                    } else {
                        builder
                            .get("sign-up-pages", {url: location.pathname})
                            .promise()
                            .then(res => {
                                if (res) {
                                    setModelType("sign-up-pages");
                                    setBuilderContentJson(res);
                                    setBuilderSEO(res?.data?.seo);
                                } else {
                                    // If no main-page or sign-up-page with current url was found, return to home
                                    navigate(routeConfig.Home.path);
                                }
                            })
                            .catch(() => {
                                navigate(routeConfig.Home.path);
                            });
                    }
                })
                .catch(() => {
                    navigate(routeConfig.Home.path);
                });
        }
        setLoading(false);
        // eslint-disable-next-line
    }, [location.pathname]);

    return (
        <>
            <Helmet>
                <title>{builderSEO?.title}</title>
                <meta
                    name="description"
                    content={
                        builderSEO?.description ??
                        "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta
                    property="og:description"
                    content={
                        builderSEO?.description ??
                        "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta property="og:url" content="https://www.channelprogram.com" />
                <meta property="og:site_name" content="Channel Program" />
                <meta
                    property="og:title"
                    content={builderSEO?.openGraphTitle ?? "The Most Innovative IT Channel Collaboration Platform"}
                />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords ?? "channel pitch, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords ?? "channel pitch, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage ?? "/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta
                    name="twitter:title"
                    content={builderSEO?.twitterTitle ?? "Where IT Vendors Build Their Channel"}
                />
                <meta
                    name="twitter:description"
                    content={
                        builderSEO?.twitterDescription ??
                        "Live and on-demand events where new and emerging technology IT Vendors present their solutions to Channel Partners"
                    }
                />
                <meta
                    name="twitter:image"
                    content={
                        builderSEO?.twitterImage
                            ? builderSEO.twitterImage
                            : builderSEO?.openGraphImage
                            ? builderSEO?.openGraphImage
                            : "/Media/seo/twitter-home.jpg"
                    }
                />
            </Helmet>
            {loading ? (
                <div
                    className={`${styles.loaderContainer} d-flex align-items-center justify-content-center fullSection`}>
                    <Loader inline loading big />
                </div>
            ) : (
                <div id="new-builder-page-container" className={`${modelType === "main-pages" && ROUTE_CLASS}`}>
                    {modelType === "main-pages" ? (
                        <BuilderComponent model="main-pages" content={builderContentJson} />
                    ) : (
                        <BuilderComponent
                            model="sign-up-pages"
                            content={builderContentJson}
                            data={{is_registered: false}}
                        />
                    )}
                </div>
            )}
        </>
    );
}
