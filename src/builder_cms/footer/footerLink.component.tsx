import {useTheme} from "@mui/material";
import CustomLink from "../../components/Links/CustomLink.component";
import OutboundLink from "../../components/Links/OutboundLink.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";

interface IProps {
    name: string;
    url: string;
    openInNewTab?: boolean;
}

export default function FooterLink({url, name, openInNewTab}: IProps) {
    const isExternalLink =
        openInNewTab || typeof url === "string" ? url.startsWith("http") || url.startsWith("www") : false;
    const theme = useTheme();
    const testid = "builderFooter" + url;
    return isExternalLink ? (
        <OutboundLink
            href={url}
            data-testid={testid}
            rel="noopener"
            target="_blank"
            style={{
                lineHeight: "18px",
            }}>
            <Typography
                fontInter
                sx={{
                    color: theme.palette.secondary.main,
                    fontSize: "14px !important",
                    fontWeight: "400 !important",
                }}>
                {name}
            </Typography>
        </OutboundLink>
    ) : (
        <CustomLink
            to={url}
            data-testid={testid}
            style={{
                lineHeight: "18px",
            }}>
            <Typography
                fontInter
                variant="body4"
                sx={{
                    color: theme.palette.secondary.main,
                    fontSize: "14px !important",
                    fontWeight: "400 !important",
                }}>
                {name}
            </Typography>
        </CustomLink>
    );
}
