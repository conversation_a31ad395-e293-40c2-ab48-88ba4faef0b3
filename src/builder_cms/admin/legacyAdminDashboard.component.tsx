import {Builder} from "@builder.io/react";
import {useQuery} from "@tanstack/react-query";
import {useCallback} from "react";
import {Link} from "react-router-dom";
import {NotificationDot} from "../../components/Indicators/notification.component";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {adminService} from "../../services/admin.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import AccessControl from "../../utils/accessControl.util";

interface IAdminAlert {
    product_reviews: number;
    company_claimer_response: number;
    industry_events: number;
    channel_deal_requests: number;
    user_content_request: number;
}

export function LegacyAdminDashboard() {
    const getAdminAlerts = useCallback(() => {
        const promiseToReturn: Promise<IAdminAlert | any> = new Promise(resolve => {
            adminService
                .getAdminAlerts()
                .then(r => resolve(r.data))
                .catch(() => {});
        });
        return promiseToReturn;
    }, []);

    const alerts = useQuery<IAdminAlert>(["admin-alerts"], getAdminAlerts, {
        ...DEFAULT_QUERY_CONFIGS(),
        retry: false,
    });

    return (
        <Box sx={{display: "flex", flexDirection: "column", gap: 5, marginTop: "-20px"}}>
            <AccessControl
                permissions={[
                    ...(routeConfig.AdminProductReviews.permissions ?? []),
                    ...(routeConfig.MspResponses.permissions ?? []),
                    ...(routeConfig.FlaggedComments.permissions ?? []),
                ]}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        Admin Tasks
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                        <AccessControl permissions={routeConfig.AdminProductReviews.permissions}>
                            <Link to={routeConfig.AdminProductReviews.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCAdminBroadcast"
                                    tooltip={{title: "Product Reviews"}}>
                                    {routeConfig.AdminProductReviews.name}
                                    <NotificationDot
                                        qty={alerts?.data?.["product_reviews"] || 0}
                                        style={{top: "-5px", right: "15px"}}
                                    />
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.MspResponses.permissions}>
                            <Link to={routeConfig.MspResponses.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToMspResponses"
                                    tooltip={{title: "Go to " + routeConfig.MspResponses.name}}>
                                    {routeConfig.MspResponses.name}
                                    <NotificationDot
                                        qty={alerts?.data?.["company_claimer_responses"] || 0}
                                        style={{top: "-5px", right: "15px"}}
                                    />
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.FlaggedComments.permissions}>
                            <Link to={routeConfig.FlaggedComments.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToFlaggedComments"
                                    tooltip={{title: "Go to " + routeConfig.FlaggedComments.name}}>
                                    {routeConfig.FlaggedComments.name}
                                </Button>
                            </Link>
                        </AccessControl>
                    </Box>
                </Box>
            </AccessControl>
            <AccessControl
                permissions={[
                    ...(routeConfig.CategoriesTagsMngmt.permissions ?? []),
                    ...(routeConfig.ProfileEnrichmentManagement.permissions ?? []),
                    ...(routeConfig.AdminIndustryCalendar.permissions ?? []),
                    ...(routeConfig.AdminJobTitle.permissions ?? []),
                    ...(routeConfig.AdminChannelCharts.permissions ?? []),
                    ...(routeConfig.AdminEmailWhitelist.permissions ?? []),
                    ...(routeConfig.AdminAdvertisementManagement.permissions ?? []),
                    ...(routeConfig.AdminTangoSettings.permissions ?? []),
                    ...(routeConfig.AdminInviteManagement.permissions ?? []),
                    ...(routeConfig.ChannelDealsManagement.permissions ?? []),
                ]}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        System
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 2, flexWrap: "wrap"}}>
                        <AccessControl permissions={routeConfig.CategoriesTagsMngmt.permissions}>
                            <Link to={routeConfig.CategoriesTagsMngmt.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCategories"
                                    tooltip={{title: "Go to categories/tags management"}}>
                                    {routeConfig.CategoriesTagsMngmt.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.ChannelDealsManagement.permissions}>
                            <Link to={routeConfig.ChannelDealsManagement.path}>
                                <Button
                                    id="goToChannelDealsManagement"
                                    variant="outlined"
                                    color="secondary"
                                    tooltip={{title: "Go to channel deals management"}}>
                                    {routeConfig.ChannelDealsManagement.name}
                                    <NotificationDot
                                        qty={alerts?.data?.["channel_deal_requests"] || 0}
                                        style={{top: "-5px", right: "15px"}}
                                    />
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.UserContentManagement.permissions}>
                            <Link to={routeConfig.UserContentManagement.path}>
                                <Button
                                    id="goToUserContentManagement"
                                    variant="outlined"
                                    color="secondary"
                                    tooltip={{title: "Go to Vendor/Product Awaiting Approval"}}>
                                    {routeConfig.UserContentManagement.name}
                                    <NotificationDot
                                        qty={alerts?.data?.["pending_user_content"] || 0}
                                        style={{top: "-5px", right: "15px"}}
                                    />
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.ProfileEnrichmentManagement.permissions}>
                            <Link to={routeConfig.ProfileEnrichmentManagement.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToProfileEnrichment"
                                    tooltip={{title: "Go to profile enrichment management"}}>
                                    {routeConfig.ProfileEnrichmentManagement.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminIndustryCalendar.permissions}>
                            <Link to={routeConfig.AdminIndustryCalendar.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCalendar"
                                    tooltip={{title: "Go to industry calendar"}}>
                                    {routeConfig.IndustryCalendar.name}
                                    <NotificationDot
                                        qty={alerts?.data?.["industry_events"] || 0}
                                        style={{top: "-5px", right: "15px"}}
                                    />
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminJobTitle.permissions}>
                            <Link to={routeConfig.AdminJobTitle.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToJobTitle"
                                    tooltip={{title: "Job Title"}}>
                                    {routeConfig.AdminJobTitle.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminChannelCharts.permissions}>
                            <Link to={routeConfig.AdminChannelCharts.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToChannelCharts"
                                    tooltip={{title: "Channel Charts"}}>
                                    {routeConfig.AdminChannelCharts.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminEmailWhitelist.permissions}>
                            <Link to={routeConfig.AdminEmailWhitelist.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToEmailWhitelist"
                                    tooltip={{title: "Email Whitelist"}}>
                                    {routeConfig.AdminEmailWhitelist.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminAdvertisementManagement.permissions}>
                            <Link to={routeConfig.AdminAdvertisementManagement.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCpAdvertisementManagement"
                                    tooltip={{title: "Go to advertisement management"}}>
                                    {routeConfig.AdminAdvertisementManagement.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminTangoSettings.permissions}>
                            <Link to={routeConfig.AdminTangoSettings.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCpTangoSettings"
                                    tooltip={{title: "Go to Tango settings"}}>
                                    {routeConfig.AdminTangoSettings.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        {/* commenting the code as we dont need as of now, we are getting options from app config now.*/}
                        {/* <Link to={routeConfig.AdminInviteManagement.path}>
                        <Button
                            variant="outlined" color="secondary"
               Box sx={{display: "flex", flexDirection: "column", gap: 3}}             id="goToInviteManagement"
                            tooltip={{title: "Invite Management"}}>
                            {routeConfig.AdminInviteManagement.name}
                        </Button>
                    </Link> */}
                    </Box>
                </Box>
            </AccessControl>
            <Box>
                <Typography fontInter weight={500} fontSize={23} color="secondary">
                    Data
                </Typography>
                <hr className="mb-3 mt-1" />
                <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                    <AccessControl permissions={routeConfig.CompanyManagement.permissions}>
                        <Link to={routeConfig.CompanyManagement.path}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                id="goToCompanyManagement"
                                tooltip={{title: "Go to companies management"}}>
                                {routeConfig.CompanyManagement.name}
                            </Button>
                        </Link>
                    </AccessControl>
                    <AccessControl permissions={routeConfig.UsersManagement.permissions}>
                        <Link to={routeConfig.UsersManagement.path}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                id="goToUserManagement"
                                tooltip={{title: "Go to users management"}}>
                                {routeConfig.UsersManagement.name}
                                <NotificationDot
                                    qty={alerts?.data?.["user_company_requests"] || 0}
                                    style={{top: "-5px", right: "15px"}}
                                />
                            </Button>
                        </Link>
                    </AccessControl>
                    <AccessControl permissions={routeConfig.AdminReportRemoveUser.permissions}>
                        <Link to={routeConfig.AdminReportRemoveUser.path}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                id="goToAdminRemoveUser"
                                tooltip={{title: "Go to Remove User from Report"}}>
                                {routeConfig.AdminReportRemoveUser.name}
                            </Button>
                        </Link>
                    </AccessControl>
                    <AccessControl permissions={routeConfig.AdminTemplateRolesManagement.permissions}>
                        <Link to={routeConfig.AdminTemplateRolesManagement.path}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                id="goToTemplateRolesManagement"
                                tooltip={{title: "Go to Template Roles Management"}}>
                                {routeConfig.AdminTemplateRolesManagement.name}
                            </Button>
                        </Link>
                    </AccessControl>
                </Box>
            </Box>
            <AccessControl
                permissions={[
                    ...(routeConfig.PitchManagement.permissions ?? []),
                    ...(routeConfig.EventCharts.permissions ?? []),
                    ...(routeConfig.UserReports.permissions ?? []),
                    ...(routeConfig.ReviewReports.permissions ?? []),
                    ...(routeConfig.ProductReviewReports.permissions ?? []),
                ]}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        Reports
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                        <AccessControl permissions={routeConfig.PitchReports.permissions}>
                            <Link to={routeConfig.PitchReports.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToPitchReports"
                                    tooltip={{title: "Go to pitch reports"}}>
                                    {routeConfig.PitchReports.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.EventCharts.permissions}>
                            <Link to={routeConfig.EventCharts.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToEventCharts"
                                    tooltip={{title: "Go to events charts"}}>
                                    {routeConfig.EventCharts.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.UserReports.permissions}>
                            <Link to={routeConfig.UserReports.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToUserReports"
                                    tooltip={{title: "Go to user reports"}}>
                                    {routeConfig.UserReports.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.ReviewReports.permissions}>
                            <Link to={routeConfig.ReviewReports.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToReviewReports"
                                    tooltip={{title: "This report includes all reviews and review status's by date"}}>
                                    {routeConfig.ReviewReports.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.ProductReviewReports.permissions}>
                            <Link to={routeConfig.ProductReviewReports.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToReviewReports"
                                    tooltip={{title: "This report includes all reviews and review status's by date"}}>
                                    {routeConfig.ProductReviewReports.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.CustomViewReports.permissions}>
                            <Link to={routeConfig.CustomViewReports.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCustomReviewReports"
                                    tooltip={{title: "This allows you to run any reports that is a sql view."}}>
                                    {routeConfig.CustomViewReports.name}
                                </Button>
                            </Link>
                        </AccessControl>
                    </Box>
                </Box>
            </AccessControl>
            <AccessControl permissions={routeConfig.VideoAnalytics.permissions}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        Analytics
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                        <Link to={routeConfig.VideoAnalytics.path}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                id="goToVideoAnalytics"
                                tooltip={{title: "Go to video analytics"}}>
                                {routeConfig.VideoAnalytics.name}
                            </Button>
                        </Link>
                    </Box>
                </Box>
            </AccessControl>
            <AccessControl
                permissions={[
                    ...(routeConfig.AdminBroadcast.permissions ?? []),
                    ...(routeConfig.AdminCustomEmails.permissions ?? []),
                ]}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        Communication
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                        <AccessControl permissions={routeConfig.AdminBroadcast.permissions}>
                            <Link to={routeConfig.AdminBroadcast.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToAdminBroadcast"
                                    tooltip={{title: "Go to admin broadcast"}}>
                                    {routeConfig.AdminBroadcast.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AdminCustomEmails.permissions}>
                            <Link to={routeConfig.AdminCustomEmails.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToAdminCustomEmails"
                                    tooltip={{title: "Go to admin custom emails"}}>
                                    {routeConfig.AdminCustomEmails.name}
                                </Button>
                            </Link>
                        </AccessControl>
                    </Box>
                </Box>
            </AccessControl>
            <AccessControl
                permissions={[
                    ...(routeConfig.AppConfiguration.permissions ?? []),
                    ...(routeConfig.AppJobs.permissions ?? []),
                    ...(routeConfig.ActivityLogs.permissions ?? []),
                    ...(routeConfig.FeatureFlag.permissions ?? []),
                ]}>
                <Box>
                    <Typography fontInter weight={500} fontSize={23} color="secondary">
                        System Configuration
                    </Typography>
                    <hr className="mb-3 mt-1" />
                    <Box sx={{display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap"}}>
                        <AccessControl permissions={routeConfig.AppConfiguration.permissions}>
                            <Link to={routeConfig.AppConfiguration.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToAppConf"
                                    tooltip={{title: "Go to application configuration"}}>
                                    {routeConfig.AppConfiguration.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.AppJobs.permissions}>
                            <Link to={routeConfig.AppJobs.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCpJobs"
                                    tooltip={{title: "Go to jobs"}}>
                                    {routeConfig.AppJobs.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.ActivityLogs.permissions}>
                            <Link to={routeConfig.ActivityLogs.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToCpActivityLogs"
                                    tooltip={{title: "Go to activity logs"}}>
                                    {routeConfig.ActivityLogs.name}
                                </Button>
                            </Link>
                        </AccessControl>
                        <AccessControl permissions={routeConfig.FeatureFlag.permissions}>
                            <Link to={routeConfig.FeatureFlag.path}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    id="goToFeatureFlagConfig"
                                    tooltip={{title: "Go to feature flags configuration"}}>
                                    {routeConfig.FeatureFlag.name}
                                </Button>
                            </Link>
                        </AccessControl>
                    </Box>
                </Box>
            </AccessControl>
        </Box>
    );
}

Builder.registerComponent(LegacyAdminDashboard, {
    name: "Legacy Admin Dashboard",
});
