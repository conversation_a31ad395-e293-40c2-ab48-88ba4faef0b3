import {builder, BuilderComponent} from "@builder.io/react";
import React, {useEffect, useState} from "react";
import {Helmet} from "react-helmet";
import {Navigate} from "react-router-dom";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import {PORTAL_UNAUTHORIZED_VENDOR} from "../../constants/errorMessages.constant";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import HomeNewsFeed from "../../pages/NewsFeed/HomeNewsFeed.page";
import useQuery from "../../utils/query.util";
import {useSubdomain} from "../../hooks/useSubdomain";
import useActiveCompany from "../../hooks/useActiveCompany";
import CompanyDashboard from "../../pages/Dashboard/CompanyDashboard.page";
import {IS_DIRECT_IT_SITE, SITE_NAME, SITE_URL} from "../../constants/siteFlags";

export default function Home() {
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);
    const notify = useNotification();
    const query = useQuery();
    const error_message = query.get("m");
    const subdomainObj = useSubdomain();
    const isMSPClientPage = subdomainObj.isMSPClientPage;
    const {isMSP, isDirect, isClientMsp} = useActiveCompany();

    const {authState} = useAuthState();

    useEffect(() => {
        builder
            .get("main-pages", {url: "/redesigned-home"})
            .promise()
            .then(res => {
                if (res) {
                    if (!authState.authenticated) {
                        setBuilderContentJson(res);
                    }
                    setBuilderSEO(res.data.seo);
                }
            });
    }, [authState.authenticated]);

    useEffect(() => {
        if (error_message === "unauthorized") {
            notify(PORTAL_UNAUTHORIZED_VENDOR, "Error");
        }
    }, [error_message]);

    useEffect(() => {
        if (builderContentJson) {
            const itspBtns: any = document.querySelectorAll(".itsp_btn");
            const vendorBtns: any = document.querySelectorAll(".vendor_btn");
            const defaultItspItems = document.querySelectorAll(".itsp_show");
            defaultItspItems.forEach((item: any) => (item.style.display = "none"));
            if (!itspBtns || !vendorBtns) return;
            itspBtns.forEach(itspBtn => {
                itspBtn.onclick = e => {
                    e.preventDefault();
                    const vendorItems = document.querySelectorAll(".vendor_show");
                    const itspItems = document.querySelectorAll(".itsp_show");
                    vendorItems.forEach((item: any) => (item.style.display = "none"));
                    itspItems.forEach((item: any) => (item.style.display = "block"));
                    vendorBtns.forEach(vendorBtn => {
                        !vendorBtn.classList.contains("inactive") && vendorBtn.classList.add("inactive");
                    });
                    itspBtns.forEach(itspBtn => {
                        itspBtn.classList.remove("inactive");
                    });
                    itspItems[0]?.scrollIntoView({behavior: "smooth"});
                };
            });
            vendorBtns.forEach(vendorBtn => {
                vendorBtn.onclick = e => {
                    e.preventDefault();
                    const itspItems = document.querySelectorAll(".itsp_show");
                    const vendorItems = document.querySelectorAll(".vendor_show");
                    itspItems.forEach((item: any) => (item.style.display = "none"));
                    vendorItems.forEach((item: any) => (item.style.display = "block"));
                    itspBtns.forEach(itspBtn => {
                        !itspBtn.classList.contains("inactive") && itspBtn.classList.add("inactive");
                    });
                    vendorBtns.forEach(vendorBtn => {
                        vendorBtn.classList.remove("inactive");
                    });
                    vendorItems[0]?.scrollIntoView({behavior: "smooth"});
                };
            });
        }
    }, [builderContentJson]);

    if (isMSPClientPage && !authState.authenticated) return <Navigate to={routeConfig.MSPClientLoginPage.path} />;
    if (IS_DIRECT_IT_SITE && !authState.authenticated) return <Navigate to={routeConfig.Login.path} />;
    return (
        <>
            <Helmet>
                <title>{builderSEO?.title || `${SITE_NAME} | Home`}</title>
                <meta
                    name="description"
                    content={
                        builderSEO?.description ||
                        "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta property="og:url" content={SITE_URL} />
                <meta property="og:site_name" content={SITE_NAME} />
                <meta
                    property="og:title"
                    content={builderSEO?.openGraphTitle || "The Most Innovative IT Channel Collaboration Platform"}
                />
                <meta
                    name="og:description"
                    content={
                        builderSEO?.description ||
                        "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords || "channel pitch, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords || "channel pitch, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                <meta
                    name="twitter:title"
                    content={builderSEO?.twitterTitle || "Where IT Vendors Build Their Channel"}
                />
                <meta
                    name="twitter:description"
                    content={
                        builderSEO?.twitterDescription ||
                        "Live and on-demand events where new and emerging technology IT Vendors present their solutions to Channel Partners"
                    }
                />
                <meta name="twitter:image" content={builderSEO?.twitterImage || "/Media/seo/twitter-home.jpg"} />
            </Helmet>
            {authState.authenticated && (
                <>
                    {isMSP || IS_DIRECT_IT_SITE || isDirect || isClientMsp ? (
                        <CompanyDashboard />
                    ) : (
                        <div className={ROUTE_CLASS}>
                            <HomeNewsFeed />
                        </div>
                    )}
                </>
            )}
            {!authState.authenticated && <BuilderComponent model="main-pages" content={builderContentJson} />}
        </>
    );
}
