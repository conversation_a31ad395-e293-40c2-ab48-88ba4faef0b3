import builder, {BuilderComponent} from "@builder.io/react";
import {useEffect, useState} from "react";
import Helmet from "react-helmet";
import {useLocation} from "react-router-dom";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";

export type BlogArticle = {
    id?: string;
    url?: string;
    title?: string;
    thumbnail?: string;
    isPostFeatured?: boolean;
    date?: number;
    description?: string;
    hasCoverImage?: boolean;
    author?: {
        name?: string;
        picture?: string;
    };
    isOnHomePage?: boolean;
};
export default function Blog() {
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);
    const location = useLocation();

    useEffect(() => {
        builder
            .get("main-pages", {url: location.pathname})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderContentJson(res);
                    setBuilderSEO(res.data.seo);
                }
            });
        // eslint-disable-next-line
    }, []);

    return (
        <div>
            <Helmet>
                <title>{builderSEO?.title || "Channel Program - Blogs"}</title>
                <meta
                    name="description"
                    content={builderSEO?.description || "Read Latest News and Articles from Channel Program"}
                />
                <meta
                    property="og:description"
                    content={builderSEO?.description || "Read Latest News and Articles from Channel Program"}
                />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:title" content={builderSEO?.openGraphTitle || "Channel Program - Blogs"} />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords || "channel blog, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords || "channel blog, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta name="twitter:title" content={builderSEO?.twitterTitle || "Channel Program - Blogs"} />
                <meta
                    name="twitter:description"
                    content={builderSEO?.twitterDescription || "Read Latest News and Articles from Channel Program"}
                />
                <meta name="twitter:image" content={builderSEO?.twitterImage || "/Media/seo/twitter-home.jpg"} />
            </Helmet>

            <div className={ROUTE_CLASS}>
                <div className={ROUTE_CONTAINER_CLASS}>
                    <BuilderComponent model="main-pages" content={builderContentJson} />
                </div>
            </div>
        </div>
    );
}
