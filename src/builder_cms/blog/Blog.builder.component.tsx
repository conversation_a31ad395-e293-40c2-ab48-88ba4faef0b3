import {Card} from "react-bootstrap";
import styles from "../../styles/pages/blog.module.sass";
import "../../styles/builder/blog.sass";
import {DATE_FORMAT, formatDateBuilder} from "../../utils/formatDate";
import {Link} from "react-router-dom";

interface IBlData {
    blogType?: boolean;
    thumbnail?: any;
    profileImg?: any;
    title?: string;
    description?: string;
    publishDate?: number;
    author?: string;
    className?: string;
    id?: string;
    url?: string;
    redirectUrl: string;
    basePostUrl?: string;
    hasCoverImage?: boolean;
    isFeatured?: boolean;
}

export default function BlogComponent(props: IBlData) {
    const isFeatured = props.blogType;

    return (
        <Card className={`${isFeatured === true ? "featuredCard" : "blogCard"}`}>
            <Link to={props.url || `${props.redirectUrl}/${props.id}`} id={"card-link-" + props.id}>
                {props.thumbnail && props.hasCoverImage && (
                    <div className={`${isFeatured ? styles.featuredCardImg : styles.cardImg}`}>
                        <Card.Img variant={isFeatured ? "featured" : "blog"} src={props.thumbnail} />
                    </div>
                )}
                <div className={styles.cardBody}>
                    <div>
                        {isFeatured ? <small className={styles.featuredTag}>Featured</small> : null}
                        <h4 className={`${isFeatured ? styles.featuredTitle : styles.cardTitle}`}>
                            {props.title && props.title.length > 75
                                ? props.title.substring(0, 75) + "..."
                                : props.title}
                        </h4>
                        {isFeatured ? (
                            <Card.Text className={`pt-3 pb-3`}>
                                {props.description && props.description.length > 50
                                    ? props.description.split(" ").splice(0, 50).join(" ") + " [read more]"
                                    : props.description}
                            </Card.Text>
                        ) : null}
                    </div>
                    <footer className={styles.postMeta}>
                        <img src={props.profileImg} alt="Profile" className={`${styles.profileImg}`} />
                        <div className={styles.authorDateMeta}>
                            {props.author && (
                                <p>
                                    <small className={styles.orangeText}>{props.author}</small>
                                </p>
                            )}
                            <p>
                                {props.publishDate && typeof props.publishDate === "number" && (
                                    <small>{formatDateBuilder(props.publishDate, DATE_FORMAT)}</small>
                                )}
                            </p>
                        </div>
                    </footer>
                </div>
            </Link>
        </Card>
    );
}

BlogComponent.defaultProps = {
    blogType: false,
};
