import {Builder} from "@builder.io/react";
import {ArrowForwardOutlined} from "@mui/icons-material";
import {Divider, Grid} from "@mui/material";
import {AxiosError} from "axios";
import {useAtom} from "jotai";
import {useEffect, useState} from "react";
import {Helmet} from "react-helmet";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useNavigate, useSearchParams} from "react-router-dom";
import {ICompanyInvite, IMSPClientLoginForm} from "../../../Interfaces/companyInvite.interface";
import ForgotPassword from "../../../components/Auth/forgotPassword.component";
import routeConfig from "../../../constants/routeConfig";
import useCompanyInviteValidation from "../../../hooks/fetches/useCompanyInviteValidation";
import useWhitelabeling from "../../../hooks/fetches/useWhitelabeling";
import useAppConfig from "../../../hooks/useAppConfig";
import useAuthState from "../../../hooks/useAuthState";
import {useLogin} from "../../../hooks/useLogin";
import useNotification from "../../../hooks/useNotification";
import useSettings from "../../../hooks/useSettings";
import {useSubdomain} from "../../../hooks/useSubdomain";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import PasswordInput from "../../../uicomponents/Molecules/PasswordInput/passwordInput.component";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {twofaAtom} from "./clientLogin.atoms";
import OldTwoFactorChallenge from "../../../components/Auth/OldTwoFactorChallenge.component";

export const MSPClientLoginForm = ({title}: IMSPClientLoginForm) => {
    const [params] = useSearchParams();
    const signature = params.get("signature") ?? "";
    const companyInviteValidationResponse = useCompanyInviteValidation(signature, "client");
    const navigate = useNavigate();
    const {authState} = useAuthState();
    const {settings, updateSettings} = useSettings();
    const parentSubdomain = authState.parent_subdomain;
    const isLoggedIn = authState.authenticated;
    const methods = useForm();
    const {triggerLogin} = useLogin();
    const [twoFAModalConfig] = useAtom(twofaAtom);
    const codeSent = twoFAModalConfig.codeSent;
    const [forgotPassword, setForgotPassword] = useState<boolean>(false);
    const {config} = useAppConfig({config_key: "CLIENT_DOMAIN"});
    const isLoading = signature ? companyInviteValidationResponse.isLoading : settings.loading;
    const [companyInvite, setCompanyInvite] = useState<ICompanyInvite>();
    const notify = useNotification();
    const {whitelabeling} = useWhitelabeling();
  

    const dashboardRedirect = useSubdomain({
        defaultValues: {
            pathname: routeConfig.Home.path,
            company: parentSubdomain,
        },
    });

    const handleSuccessCompanyInviteValidation = (data: ICompanyInvite) => {
        setCompanyInvite(data);
        if (!data?.activated) {
            navigate(routeConfig.MSPClientRegisterPage.path + "?signature=" + signature);
            return;
        }
        methods.setValue("email", data?.email);
    };

    const sendLoginInfo = async (data: any) => {
        updateSettings(UPDATE_SETTINGS, {loading: true});
        triggerLogin(data.email, data.password, data.signature);
    };

    useEffect(() => {
        if (isLoggedIn) {
            const redirectUrl = params.get("redirect_url");
            if (redirectUrl) {
                if (redirectUrl.startsWith("/")) {
                    navigate(redirectUrl);
                } else {
                    navigate("/" + redirectUrl);
                }
                return;
            }

            dashboardRedirect.navigate();
        }
    }, [isLoggedIn]);

    useEffect(() => {
        if (!companyInviteValidationResponse.isLoading) {
            if (companyInviteValidationResponse.data) {
                handleSuccessCompanyInviteValidation(companyInviteValidationResponse.data);
            }
            if (companyInviteValidationResponse.error) {
                notify(getErrorFromArray(companyInviteValidationResponse.error as AxiosError), "Error");
            }
        }
    }, [companyInviteValidationResponse.data, companyInviteValidationResponse.error]);

    return (
        <>
            <Helmet>
                <title>
                    Customer Sign In
                    {companyInvite?.child_company?.name
                        ? ` - ${companyInvite?.child_company.subdomain}.${config?.value}`
                        : ""}
                </title>
                <meta name="description" content="Join our community and gain access to our events and platform." />
                <meta
                    property="og:description"
                    content="Join our community and gain access to our events and platform."
                />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content={companyInvite?.child_company?.name ?? "Navistack"} />
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Client Login" />
                <meta name="og:description" content="Join our community and gain access to our events and platform." />
                <meta property="website:tag" content="channel program, it channel vendors, technology vendors" />
                <meta property="keywords" content="channel program, it channel vendors, technology vendors" />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content="/Media/seo/og-home.jpg" />
                <meta name="twitter:title" content="Client Login" />
                <meta
                    name="twitter:description"
                    content="Join our community and gain access to our events and platform."
                />
                <meta name="twitter:image" content="/Media/seo/twitter-home.jpg" />
            </Helmet>
            {isLoading ? (
                <Loader inline loading version="iconBlue" />
            ) : !twoFAModalConfig.show && (
                <>
                    <Box style={{flexDirection: "row", background: "#FFF"}}>
                        <Box
                            sx={{
                                maxWidth: "clamp(450px, 90vw, 800px)",
                                margin: "0 auto",
                            }}>
                            <Grid
                                container
                                display="flex"
                                flexDirection="column"
                                sx={{
                                    width: "100%",
                                }}>
                                <Box flex={1}>
                                    {whitelabeling && whitelabeling.logo && (
                                        <>
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    position: "relative",
                                                    justifyContent: "center",
                                                    img: {
                                                        objectFit: "contain",
                                                    },
                                                }}>
                                                <img width="160px" height="45px" src={whitelabeling.logo} alt="Logo" />
                                            </Box>
                                            <Divider sx={{marginTop: 3, backgroundColor: "primary", width: "100%"}} />
                                        </>
                                    )}
                                    <Box
                                        sx={{marginTop: 3, marginBottom: 3, display: "flex", justifyContent: "center"}}>
                                        <Typography
                                            sx={{
                                                fontFamily: "var(--Poppins)!important",
                                                textAlign: "center",
                                                fontWeight: 600,
                                                lineHeight: "120%",
                                            }}
                                            size={30}
                                            color="secondary">
                                            {title}
                                        </Typography>
                                    </Box>
                                </Box>
                                <FormProvider {...methods}>
                                    <form onSubmit={methods.handleSubmit(sendLoginInfo)}>
                                        <Box display="grid" width="100%" gap={3}>
                                            <TextBoxComponent
                                                id="email"
                                                placeholder="Enter your registered email"
                                                tooltipHelperText="Email is required"
                                                label="Email"
                                                isRequired
                                                validateOnTheFly
                                            />
                                            <Box>
                                                <PasswordInput
                                                    id="password"
                                                    placeholder="Enter your password"
                                                    tooltipHelperText="Password is required"
                                                    label="Password"
                                                    isRequired
                                                    validateOnTheFly
                                                    criteriaHelper={false}
                                                />
                                                <Typography variant="body4" fontInter>
                                                    <Link
                                                        to="#"
                                                        style={{fontWeight: 700}}
                                                        onClick={() => setForgotPassword(!forgotPassword)}>
                                                        Forgot your password?
                                                    </Link>
                                                </Typography>
                                            </Box>
                                        </Box>
                                        <Divider
                                            sx={{
                                                marginTop: 3,
                                                marginBottom: 3,
                                                backgroundColor: "primary",
                                                width: "100%",
                                            }}
                                        />
                                        <Box
                                            flex={1}
                                            flexDirection={"row"}
                                            justifyContent={"center"}
                                            alignItems={"center"}>
                                            <MuiButtonComponent
                                                variant={!!whitelabeling ? "contained" : "tonal"}
                                                id="sign_in"
                                                type="submit"
                                                sx={{
                                                    margin: "auto",
                                                    width: "90%",
                                                }}>
                                                <Box sx={{fontWeight: "600", fontSize: "18px"}}>
                                                    <ArrowForwardOutlined
                                                        sx={{width: 18, height: 18, marginRight: 1}}
                                                    />
                                                    {settings.loading || (!!authState?.id)
                                                        ? "Login in..."
                                                        : "Login"}
                                                </Box>
                                            </MuiButtonComponent>
                                        </Box>
                                    </form>
                                </FormProvider>
                            </Grid>
                        </Box>
                    </Box>
                    <ModalComponent
                        onClose={() => {
                            setForgotPassword(false);
                        }}
                        showBottom={false}
                        content={
                            <ForgotPassword
                                showRegisterLink={false}
                                forgotPassword={forgotPassword}
                                setForgotPassword={setForgotPassword}
                            />
                        }
                        showCancelButton={false}
                        showOkButton={false}
                        open={forgotPassword}
                    />
                </>
            )}
            {!!twoFAModalConfig.show && !!twoFAModalConfig.config && (
                <Box
                    sx={{
                        position: "fixed",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        background: "rgba(0, 0, 0, 0.5)",
                        zIndex: 9999,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}>
                    <Box
                        sx={{
                            background: "#FFF",
                            borderRadius: "10px",
                            padding: "20px",
                        }}>
                        <OldTwoFactorChallenge
                            {...twoFAModalConfig.config}
                            codeSent={codeSent}
                            onSuccess={twoFAModalConfig.twoFASuccess}
                            onCancel={twoFAModalConfig.twoFACancel}
                        />
                    </Box>
                </Box>
            )}
        </>
    );
};

Builder.registerComponent(MSPClientLoginForm, {
    name: "MSP Client Login Form",
    inputs: [
        {
            required: true,
            name: "title",
            friendlyName: "Title",
            type: "text",
        },
    ],
});
