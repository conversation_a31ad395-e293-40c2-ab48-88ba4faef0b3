import {useEffect} from "react";
import {useNavigate, useParams} from "react-router-dom";
import routeConfig from "../../../constants/routeConfig";

const MSPOpenLinkRedirectPage = () => {
    const {signature} = useParams<{signature: string}>();
    const navigate = useNavigate();
    useEffect(() => {
        if (signature) {
            navigate(`${routeConfig.MSPClientRegisterPage.path}?signature=${signature}`, {replace: true});
        }
    }, [signature, navigate]);

    return null;
};

export default MSPOpenLinkRedirectPage;
