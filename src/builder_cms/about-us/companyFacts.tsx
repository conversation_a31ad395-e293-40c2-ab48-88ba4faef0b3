import {Builder} from "@builder.io/react";
import containers from "../../styles/builder/library.module.sass";
import textStyles from "../../styles/builder/text.module.sass";

export const CompanyFacts = ({title, description, facts}) => (
    <section className={textStyles.companyDataSendOff} style={{backgroundImage: `url('/Media/map-image.png')`}}>
        <div className={containers.container}>
            <div className="row row-new ">
                <div className="col-12 col-md-10 col-xl-6 mx-auto">
                    <h2 className="title text-center mb-4 px-4">
                        {title ? title : "We are always trying to raise the bar"}
                    </h2>
                    <p className={`${textStyles.text} text-center`}>
                        {description
                            ? description
                            : `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis vestibulum
                                    pellentesque enim non volutpat. Morbi fermentum laoreet tempor.`}
                    </p>
                </div>
            </div>
            <div className="row row-new pt-0">
                <div className="col-12 mx-auto">
                    <div className={textStyles.accoladesDataGrid}>
                        {facts?.map((fact, index) => (
                            <span key={index} className={textStyles.accoladeDataItem}>
                                <span className={textStyles.accoladeData}>{fact?.data ? fact?.data : "400+"}</span>
                                <span className={textStyles.accoladeLabel}>
                                    {fact?.label ? fact?.label : "Pitch Attendees"}
                                </span>
                            </span>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    </section>
);

Builder.registerComponent(CompanyFacts, {
    name: "CompanyFacts",
    friendlyName: "Company Facts",
    inputs: [
        {
            name: "title",
            type: "text",
        },
        {
            name: "description",
            type: "text",
        },
        {
            name: "facts",
            type: "list",
            defaultValue: [{label: "[label]", data: "[data]"}],
            subFields: [
                {
                    name: "label",
                    type: "text",
                },
                {
                    name: "data",
                    type: "text",
                },
            ],
        },
    ],
    defaultStyles: {
        marginTop: "0",
    },
});
