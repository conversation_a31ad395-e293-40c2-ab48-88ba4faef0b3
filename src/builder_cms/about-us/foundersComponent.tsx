import {Builder} from "@builder.io/react";
import containers from "../../styles/builder/library.module.sass";
import textStyles from "../../styles/builder/text.module.sass";
import {Link} from "react-router-dom";
import {Divider} from "../components/divider";
import {useState} from "react";
export const Founders = ({title, kevin, matt}) => {
    const [openKevin, setOpenKevin] = useState<boolean>(kevin?.description.length < 500);
    const [openMatt, setOpenMatt] = useState<boolean>(matt?.description.length < 500);

    return (
        <section className="founders">
            <div className={containers.container}>
                <div className="row row-new">
                    <div className="col-12">
                        <h2 className="mb-3">{title ? title : "Meet the team behind Channel Program"}</h2>
                    </div>

                    <div className={textStyles.founderContainer}>
                        <div className={textStyles.founder}>
                            <div className={textStyles.founderInfo}>
                                <div className={textStyles.picContainer}>
                                    <div className={textStyles.founderPic}>
                                        <img
                                            src={
                                                kevin?.image
                                                    ? kevin?.image
                                                    : "https://tedwaffl.sirv.com/example-pic.png"
                                            }
                                            alt=""
                                        />
                                    </div>
                                    {kevin?.link && (
                                        <>
                                            {kevin?.link.includes("channelprogram.com") ? (
                                                <Link
                                                    to={
                                                        kevin?.link.includes("/u/")
                                                            ? `/u/${kevin?.link.split("/u/")[1]}`
                                                            : kevin?.link
                                                    }
                                                    className={textStyles.underlineLink}>
                                                    {kevin?.linkText ? kevin?.linkText : "Read his blog"}
                                                </Link>
                                            ) : (
                                                <a
                                                    href={kevin?.link.replace("http://", "https://")}
                                                    className={textStyles.underlineLink}>
                                                    {kevin?.linkText ? kevin?.linkText : "Read his blog"}
                                                </a>
                                            )}
                                        </>
                                    )}
                                </div>
                                <h3 className={textStyles.founderName}>Kevin Lancaster</h3>
                                <span className={textStyles.founderTitle}>{kevin?.title}</span>
                                <p
                                    className={`text mb-3 ${
                                        openKevin
                                            ? textStyles.foundersDescriptionOpen
                                            : textStyles.foundersDescriptionClosed
                                    }`}>
                                    {kevin?.description}
                                </p>
                                {kevin?.description.length > 500 && (
                                    <div className="d-flex justify-content-center">
                                        <p
                                            className={`cPointer ${textStyles.underlineLink}`}
                                            onClick={() => {
                                                setOpenKevin(!openKevin);
                                            }}>
                                            {openKevin ? "Read Less" : "Read More"}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className={textStyles.founder}>
                            <div className={textStyles.founderInfo}>
                                <div className={textStyles.picContainer}>
                                    <div className={textStyles.founderPic}>
                                        <img
                                            src={
                                                matt?.image ? matt?.image : "https://tedwaffl.sirv.com/example-pic.png"
                                            }
                                            alt=""
                                        />
                                    </div>
                                    {matt?.link && (
                                        <>
                                            {matt?.link.includes("channelprogram.com") ? (
                                                <Link
                                                    to={
                                                        matt?.link.includes("/u/")
                                                            ? `/u/${matt?.link.split("/u/")[1]}`
                                                            : matt?.link
                                                    }
                                                    className={textStyles.underlineLink}>
                                                    {matt?.linkText ? matt?.linkText : "Read his blog"}
                                                </Link>
                                            ) : (
                                                <a
                                                    href={matt?.link.replace("http://", "https://")}
                                                    className={textStyles.underlineLink}>
                                                    {matt?.linkText ? matt?.linkText : "Read his blog"}
                                                </a>
                                            )}
                                        </>
                                    )}
                                </div>
                                <h3 className={textStyles.founderName}>Matt Solomon</h3>
                                <span className={textStyles.founderTitle}>{matt?.title}</span>
                                <p
                                    className={`text mb-3 ${
                                        openMatt
                                            ? textStyles.foundersDescriptionOpen
                                            : textStyles.foundersDescriptionClosed
                                    }`}>
                                    {matt?.description}
                                </p>
                                {matt?.description.length > 500 && (
                                    <div className="d-flex justify-content-center">
                                        <p
                                            className={`cPointer ${textStyles.underlineLink}`}
                                            onClick={() => {
                                                setOpenMatt(!openMatt);
                                            }}>
                                            {openMatt ? "Read Less" : "Read More"}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                <Divider />
            </div>
        </section>
    );
};
Builder.registerComponent(Founders, {
    name: "FoundersSection",
    friendlyName: "Founders Section",
    inputs: [
        {
            name: "title",
            type: "text",
        },
        {
            name: "kevin",
            type: "object",
            subFields: [
                {
                    name: "title",
                    type: "text",
                },
                {
                    name: "description",
                    type: "longText",
                },
                {
                    name: "image",
                    type: "file",
                    allowedFileTypes: ["jpeg", "png", "mp4", "gif", "pdf"],
                },
                {
                    name: "link",
                    type: "text",
                },
                {
                    name: "linkText",
                    type: "text",
                },
            ],
        },
        {
            name: "matt",
            type: "object",
            subFields: [
                {
                    name: "title",
                    type: "text",
                },
                {
                    name: "description",
                    type: "longText",
                },
                {
                    name: "image",
                    type: "file",
                    allowedFileTypes: ["jpeg", "png", "mp4", "gif", "pdf"],
                },
                {
                    name: "link",
                    type: "text",
                },
                {
                    name: "linkText",
                    type: "text",
                },
            ],
        },
    ],
    defaultStyles: {
        marginTop: "0",
    },
});
