import {useState, useEffect} from "react";

import styles from "../../styles/pages/cash.module.sass";

import Helmet from "react-helmet";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";

import {BuilderComponent, builder} from "@builder.io/react";

export default function BuilderChannelCash() {
    const [builderContent<PERSON>son, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);

    useEffect(() => {
        builder
            .get("main-pages", {url: window.location.pathname})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderContentJson(res);
                    setBuilderSEO(res.data.seo);
                }
            });
    }, []);
    return (
        <div className={ROUTE_CLASS}>
            <Helmet>
                <title>{builderSEO ? builderSEO.title : "Channel Program | Channel Cash"}</title>
                <meta
                    name="description"
                    content={
                        builderSEO
                            ? builderSEO.description
                            : "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta
                    property="og:description"
                    content={
                        builderSEO
                            ? builderSEO.description
                            : "Live and on-demand events where new and emerging technology Vendors present their solutions to Channel Partners"
                    }
                />
                <meta property="og:url" content="https://www.channelprogram.com" />
                <meta property="og:site_name" content="Channel Program" />
                <meta
                    property="og:title"
                    content={
                        builderSEO ? builderSEO.openGraphTitle : "The Most Innovative IT Channel Collaboration Platform"
                    }
                />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO ? builderSEO.keywords : "channel pitch, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO ? builderSEO.keywords : "channel pitch, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO ? builderSEO.openGraphImage : "/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta property="fb:admins" content="Channel-Program-113031624351535" />
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:site" content="@bethechannel" />
                <meta
                    name="twitter:title"
                    content={builderSEO ? builderSEO.twitterTitle : "Where IT Vendors Build Their Channel"}
                />
                <meta
                    name="twitter:description"
                    content={
                        builderSEO
                            ? builderSEO.twitterDescription
                            : "Live and on-demand events where new and emerging technology IT Vendors present their solutions to Channel Partners"
                    }
                />
                <meta
                    name="twitter:image"
                    content={builderSEO ? builderSEO.twitterImage : "/Media/seo/twitter-home.jpg"}
                />
            </Helmet>
            <div className={styles.cashContainer}>
                {builderContentJson && <BuilderComponent model="main-pages" content={builderContentJson} />}
            </div>
        </div>
    );
}
