import {Builder} from "@builder.io/react";
import {CompanyProfileTypes} from "../../Interfaces/businessRules.interface";
import ProfileCard from "../../components/Profile/profileCard.component";
import styles from "../../styles/components/pitchSpeakers.module.sass";

type featuredProfileI = {
    title: string;
    profiles: {
        name: string;
        image: string;
        handle: string;
        id: string;
        path: string;
        friendly_url: string;
    }[];
};

export const FeaturedProfile = (props: featuredProfileI) => {
    const {profiles, title} = props;
    return (
        <div className={styles.featuredSpeakers}>
            <div className={styles.title}> {title}</div>
            <div className={`${styles.speakers} d-flex flex-wrap`}>
                {profiles.map(profile => {
                    return (
                        <div key={profile.id} className={`${styles.speakerWrapper} col-12 col-md-6 col-xl-4`}>
                            <ProfileCard
                                user={{
                                    id: profile.id,
                                    name: profile.name,
                                    avatar: profile.image,
                                    friendly_url: profile.friendly_url,
                                    handle: profile.handle,
                                    is_company: true,
                                    type: CompanyProfileTypes.VENDOR_FREE,
                                    type_is_of_vendor: true,
                                    current_user_is_following: false,
                                    followers_count: 0,
                                    job_title_name: "",
                                }}
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

Builder.registerComponent(FeaturedProfile, {
    name: "Featured Profiles",
    defaultStyles: {
        marginTop: "0",
    },
    inputs: [
        {
            name: "title",
            type: "text",
            defaultValue: "[Featured Tiles]",
        },
        {
            name: "profiles",
            friendlyName: "Profiles Information",
            type: "list",
            defaultValue: [
                {
                    name: "[Soap]",
                    image: "https://via.placeholder.com/200x200",
                    id: "0",
                    hanlde: "None",
                },
            ],
            subFields: [
                {
                    name: "name",
                    friendlyName: "Name",
                    type: "text",
                    defaultValue: "[Soap]",
                },
                {
                    name: "image",
                    friendlyName: "Image",
                    type: "file",
                    defaultValue: "https://via.placeholder.com/200x200",
                    allowedFileTypes: ["jpeg", "png", "gif"],
                },
                {
                    name: "id",
                    friendlyName: "Id",
                    type: "text",
                    defaultValue: "0",
                },
                {
                    name: "handle",
                    friendlyName: "Handle",
                    type: "text",
                    defaultValue: "None",
                },
            ],
        },
    ],
});
