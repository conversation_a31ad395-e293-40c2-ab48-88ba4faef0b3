import {Builder} from "@builder.io/react";
import styles from "../../styles/pages/register.module.sass";

export const RegisterBanner = (props: {bannerImage?: string}) => {
    return (
        <div className={styles.topBanner}>
            <img src={props.bannerImage} alt="Channel program grow your business influence your channel" />
        </div>
    );
};

Builder.registerComponent(RegisterBanner, {
    name: "Register Top Banner",
    inputs: [
        {
            name: "bannerImage",
            friendlyName: "Banner Image",
            type: "file",
            allowedFileTypes: ["jpeg", "png"],
        },
    ],
});
