import {Builder} from "@builder.io/react";
import Close from "@mui/icons-material/Close";
import {useTheme} from "@mui/material";
import DOMPurify from "dompurify";
import {useState} from "react";
import ReactIcon from "../../components/Icons/reactIcon.component";
import CustomLink from "../../components/Links/CustomLink.component";
import {MENU_COOKIE} from "../../constants/cookies.contant";
import {useBrowserStorageState} from "../../hooks/useBrowserStorage";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import IconButton from "../../uicomponents/Atoms/IconButton/IconButton.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";

interface IProps {
    text: string;
    action?: {
        actionText: string;
        actionLink: string;
    };
    icon?: string;
}

export function MenuBanner({text, action, icon}: IProps) {
    const [cookie, setCookie] = useBrowserStorageState("", MENU_COOKIE, "session");
    const [hide, setHide] = useState(!!cookie ? cookie === text : false);
    const {actionText, actionLink} = action || {};
    const theme = useTheme();
    const showIcon = icon && icon.startsWith("Md");

    return hide ? null : (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                width: "100%",
                gap: 2,
                background: theme.palette.secondary.main,
                padding: "12px",
                position: "relative",
            }}>
            <Box
                sx={{
                    display: "flex",
                    width: "100%",
                    gap: 2,
                    alignItems: "center",
                    justifyContent: "center",
                    textAlign: "center",
                }}>
                {showIcon && (
                    <ReactIcon
                        iconName={icon}
                        color={theme.palette.primary[400]}
                        size="16px"
                        style={{verticalAlign: "top"}}
                    />
                )}
                <Typography
                    fontInter
                    sx={{
                        "&,& *": {
                            color: `${theme.palette.neutral[100]}!important`,
                            fontFamily: "var(--Inter)",
                            fontSize: "16px !important",
                        },
                    }}
                    dangerouslySetInnerHTML={{__html: DOMPurify.sanitize(text)}}
                />
                <Box sx={{display: "none", gap: 2, "@media (min-width: 768px)": {display: "block"}}}>
                    {actionLink && actionText && (
                        <CustomLink to={actionLink} target="_blank">
                            <Typography
                                fontInter
                                sx={{
                                    "&,& *": {
                                        color: `${theme.palette.primary[400]}!important`,
                                        fontFamily: "var(--Inter)",
                                        fontSize: "16px !important",
                                        fontWeight: "700 !important",
                                    },
                                }}
                                dangerouslySetInnerHTML={{__html: DOMPurify.sanitize(actionText)}}
                            />
                        </CustomLink>
                    )}
                </Box>
                <Box sx={{width: "32px", height: "32px"}}>
                    <IconButton
                        sx={{
                            position: "absolute",
                            top: "50%",
                            right: "12px",
                            transform: "translateY(-50%)",
                            "&:hover": {
                                background: "transparent !important",
                            },
                        }}
                        onClick={() => {
                            setCookie(text);
                            setHide(true);
                        }}>
                        <Close color="neutral" />
                    </IconButton>
                </Box>
            </Box>
            <Box
                sx={{
                    display: "flex",
                    width: "100%",
                    gap: 2,
                    justifyContent: "center",
                    "@media (min-width: 768px)": {display: "none"},
                }}>
                {actionLink && actionText && (
                    <CustomLink to={actionLink}>
                        <Typography
                            fontInter
                            sx={{
                                "&,& *": {
                                    color: `${theme.palette.primary[400]}!important`,
                                    fontFamily: "var(--Inter)",
                                    fontSize: "16px !important",
                                    fontWeight: "700 !important",
                                },
                            }}
                            dangerouslySetInnerHTML={{__html: DOMPurify.sanitize(actionText)}}
                        />
                    </CustomLink>
                )}
            </Box>
        </Box>
    );
}

Builder.registerComponent(MenuBanner, {
    name: "menu-banner",
    inputs: [
        {
            name: "text",
            friendlyName: "Main Text",
            type: "richText",
        },
        {
            name: "action",
            friendlyName: "Banner Action",
            type: "object",
            subFields: [
                {
                    name: "actionText",
                    friendlyName: "Text",
                    type: "richText",
                },
                {
                    name: "actionLink",
                    friendlyName: "Link",
                    type: "text",
                },
            ],
        },
        {
            name: "icon",
            friendlyName: "Icon",
            type: "string",
            helperText: "Icon name from https://react-icons.github.io/react-icons/icons/md/",
            defaultValue: "MdCardGiftcard",
        },
    ],
});
