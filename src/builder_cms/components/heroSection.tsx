import {Builder} from "@builder.io/react";
import {LoginOutlined} from "@mui/icons-material";
import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import DOMPurify from "dompurify";
import {useRef} from "react";
import CustomLink from "../../components/Links/CustomLink.component";
import SearchInput from "../../components/Search/searchInput.component";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import {companyService} from "../../services/company.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import FixedPlacedImages from "../../uicomponents/Molecules/FixedPlacedCircles/fixedPlacedCircles.component";

interface IHeroSectionProps {
    title?: string;
    description?: string;
    backgroundImage?: string;
    button?: {
        text: string;
        link: string;
    };
}

interface IVendorAvatar {
    id: string;
    name: string;
    avatar: string;
}

export function HeroSection({title, backgroundImage, button, description}: IHeroSectionProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const theme = useTheme();
    const imagesQuery = useQuery<IVendorAvatar[]>(
        ["vendor-avatars"],
        () => {
            const promiseToReturn: Promise<IVendorAvatar[]> = new Promise((resolve, reject) =>
                companyService
                    .getVendorsAvatars()
                    .then((r: AxiosResponse<IVendorAvatar[]>) => resolve(r.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    return (
        <Box
            sx={{
                minHeight: "calc(100vh - 60px)",
                position: "relative",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "column",
                perspective: "1px",
                perspectiveOrigin: "center center",
                transformStyle: "preserve-3d",
                zIndex: 5,
                padding: "35px 0 274px",
                "@media (min-width: 768px)": {
                    padding: "77px 0 274px",
                },
            }}>
            <Box
                sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    zIndex: 5,
                }}
            />
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "column",
                    textAlign: "center",
                    zIndex: 8,
                    gap: "40px",
                    maxWidth: "940px",
                }}>
                <Box
                    sx={{
                        maxWidth: "238px",
                        height: "74px",
                    }}>
                    <img
                        src="/Media/logo_icons.png"
                        alt="logo"
                        style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                        }}
                    />
                </Box>
                {title && (
                    <Typography
                        variant="h1"
                        color={theme.palette.secondary.main}
                        sx={{
                            fontFamily: "var(--RocGrotesk)!important",
                            fontWeight: "400!important",
                        }}>
                        {title}
                    </Typography>
                )}
                {!!description && (
                    <Typography
                        variant="h3"
                        fontInter
                        sx={{
                            maxWidth: "95vw",
                            "& *": {
                                color: theme.palette.secondary.main,
                            },
                        }}
                        dangerouslySetInnerHTML={{__html: DOMPurify.sanitize(description)}}
                    />
                )}
                <Box
                    sx={{
                        display: "none",
                        width: "100%",
                        "@media (min-width: 768px)": {
                            display: "flex",
                        },
                        "& > *": {
                            flex: "1 1 100%",
                            "& > div#mainSearch": {
                                width: "100%",
                            },
                        },
                        "& input": {
                            backgroundColor: "#fff!important",
                            color: `${theme.palette.neutral[800]}!important`,
                            "&::placeholder": {
                                color: theme.palette.neutral[600],
                                opacity: "1!important",
                            },
                        },
                    }}>
                    <SearchInput placeholder="Search Channel Program for products, categories, vendors, content, etc..." />
                </Box>
                {!!button?.link && !!button.text && (
                    <CustomLink to={button.link} id="cta-button-action">
                        <Button
                            id="cta-button-ui"
                            variant="tonal"
                            sx={{
                                padding: "16px 32px 16px 16px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "8px",
                                fontSize: "18px!important",
                                fontWeight: "600!important",
                                lineHeight: "1.2!important",
                            }}>
                            <LoginOutlined
                                htmlColor="white"
                                sx={{
                                    width: "18px",
                                    height: "18px",
                                }}
                            />
                            {button.text}
                        </Button>
                    </CustomLink>
                )}
            </Box>
            <Box
                sx={{
                    width: "100%",
                    height: "100%",
                    position: "absolute",
                    top: 0,
                    left: 0,
                    zIndex: 6,
                    overflow: "hidden",
                    maxWidth: "100vw",
                }}>
                <Box
                    sx={{
                        position: "absolute",
                        width: "1440px",
                        height: "707px",
                        zIndex: 6,
                        bottom: 0,
                        left: "50%",
                        transform: "translateX(-50%)",
                        background: "url(/Media/Rectangles_full_hero.png) no-repeat bottom center",
                        backgroundSize: "cover",
                        "&::before": {
                            content: "''",
                            position: "absolute",
                            height: "100%",
                            width: "100%",
                            top: 0,
                            left: 0,
                            zIndex: 7,
                            background: "url(/Media/Categories_full_hero.png) no-repeat bottom center",
                            backgroundSize: "cover",
                        },
                    }}
                />
                <Box
                    ref={containerRef}
                    sx={{
                        position: "absolute",
                        width: "1px",
                        height: "1px",
                        zIndex: 7,
                        bottom: "127px",
                        left: "50%",
                        transform: "translateX(-50%)",
                        pointerEvents: "none",
                    }}>
                    <FixedPlacedImages companies={imagesQuery.data} containerRef={containerRef.current} />
                </Box>
            </Box>
        </Box>
    );
}

Builder.registerComponent(HeroSection, {
    name: "Hero Section",
    inputs: [
        {
            name: "title",
            type: "text",
            friendlyName: "Hero Title",
        },
        {
            name: "description",
            type: "richText",
            friendlyName: "Hero Description",
        },
        {
            name: "button",
            type: "object",
            friendlyName: "CTA Button",
            subFields: [
                {
                    name: "text",
                    type: "text",
                    friendlyName: "Button Text",
                },
                {
                    name: "link",
                    type: "string",
                    friendlyName: "Button Link",
                },
            ],
        },
        {
            name: "backgroundImage",
            type: "file",
            allowedFileTypes: ["jpeg", "png", "jpg"],
            friendlyName: "Image Source",
        },
    ],
    defaultStyles: {
        marginTop: "0",
    },
});
