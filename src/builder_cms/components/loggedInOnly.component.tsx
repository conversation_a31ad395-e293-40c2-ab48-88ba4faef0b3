import {Builder, withChildren} from "@builder.io/react";
import useAuthState from "../../hooks/useAuthState";

interface IProps {
    children: any;
    useCustomLoginStatus?: boolean;
    customLoginStatus?: boolean;
}

export const LoggedInOnly = (props: IProps) => {
    const {authState} = useAuthState();
    const authenticated = props.useCustomLoginStatus ? props.customLoginStatus : authState.authenticated;
    if (!authenticated) return null;
    return <div>{props.children?.map(el => el)}</div>;
};

const LoggedInOnlyWithChildren = withChildren(LoggedInOnly);

Builder.registerComponent(LoggedInOnlyWithChildren, {
    name: "Logged In Only",
    inputs: [
        {
            name: "useCustomLoginStatus",
            friendlyName: "Use Custom Login Status",
            helperText: "Used to toggle the login status for testing. Do not have this enabled in production.",
            type: "boolean",
        },
        {
            name: "customLoginStatus",
            friendlyName: "Custom Login Status",
            helperText: "If custom login status is toggled on the login status of the page will use this variable.",
            type: "boolean",
        },
    ],
});
