import {Builder} from "@builder.io/react";
import {Share} from "@mui/icons-material";
import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import DomToImage from "dom-to-image";
import {ReactNode, useEffect, useRef, useState} from "react";
import {useLocation} from "react-router-dom";
import {IPopularCategory} from "../../Interfaces/categories.interface";
import ProductByCategoryQuadrantChart from "../../components/Charts/productReviewQuadrant/productsByCategory.component";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {categoriesService} from "../../services/category.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import VerticalTabs from "../../uicomponents/Atoms/Tabs/VerticalTabs.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ShareButton from "../../uicomponents/Molecules/ShareButton/shareButton.component";
import Loader from "../../utils/loader";

const TRANSPARENT_PIXEL = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
export default function TopCategories() {
    const [isMobile, setIsMobile] = useState(false);
    const [sharing, setSharing] = useState(false);
    const location = useLocation();
    const theme = useTheme();
    const topCategoriesChart = useRef<HTMLDivElement>(null);
    const categoriesQuery = useQuery<IPopularCategory[]>(
        ["popular-categories"],
        () => {
            return new Promise<IPopularCategory[]>((resolve, reject) => {
                categoriesService.getPopularCategories(
                    (res: AxiosResponse<IPopularCategory[]>) => {
                        resolve(res.data);
                    },
                    err => {
                        reject(err);
                    },
                );
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const categories = categoriesQuery.data;
    const subCategories = categories?.flatMap(category => category["sub-categories"] ?? ([] as IPopularCategory[]));

    const shareImage = async () => {
        const image = await DomToImage.toJpeg(topCategoriesChart.current as HTMLElement, {
            imagePlaceholder: TRANSPARENT_PIXEL,
        })
            .then(data => {
                return data;
            })
            .catch(() => {
                return "";
            });
        const blob = await (await fetch(image)).blob();
        const imageFile = new File([blob], "Top-Trending-Companies-By-Category.jpg", {type: blob.type});
        if (navigator.canShare({files: [imageFile]})) {
            try {
                await navigator.share({
                    title: "Top Trending Companies on Channel Program",
                    text: "Look at the companies that are trending on Channel Program!",
                    files: [imageFile],
                });
            } catch (error) {
                console.error("Error while sharing:", error);
            }
        } else {
            const link = document.createElement("a");
            link.href = image;
            link.download = `Top-Trending-Companies-${new Date().toISOString()}-by-category.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        setSharing(false);
    };

    const fetchSelectedCategoryUrl = () => {
        const BASE_CATEGORIES_URL = window.location.origin + routeConfig.Categories.path;
        const selectedCategory = sessionStorage.getItem("selected-top-category");
        const categoryFriendlyUrl = subCategories?.find(category => category.id === selectedCategory)?.friendly_url;
        if (selectedCategory) {
            return `${BASE_CATEGORIES_URL}/${categoryFriendlyUrl}`;
        }
        return BASE_CATEGORIES_URL;
    };

    useEffect(() => {
        const userAgent = navigator.userAgent;
        const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
        const width = window.innerWidth;
        setIsMobile(mobileRegex.test(userAgent) && width < 1000);
    }, []);

    return (
        <Box
            sx={{
                padding: "80px 24px",
                width: "100%",
                display: "flex",
                flexDirection: "column",
                gap: "24px",
                margin: "0 auto",
                "@media (min-width: 768px)": {
                    padding: "80px 48px",
                },
            }}>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    flexWrap: "wrap",
                    gap: 4,
                }}>
                <Typography
                    variant="h3"
                    sx={{
                        color: "secondary.main",
                        fontWeight: "400!important",
                        fontSize: "51px!important",
                        fontFamily: "var(--RocGrotesk)!important",
                    }}>
                    Most Popular Categories
                </Typography>
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "16px",
                    }}>
                    <Button
                        id="view-all-vendors"
                        to={
                            location.pathname.includes(routeConfig.Categories.path)
                                ? undefined
                                : routeConfig.Categories.path
                        }
                        onClick={
                            location.pathname.includes(routeConfig.Categories.path)
                                ? () => {
                                      const allCategorieSection = document.getElementById("all-categories");
                                      allCategorieSection?.scrollIntoView({behavior: "smooth"});
                                  }
                                : undefined
                        }
                        variant="text"
                        color="secondary"
                        sx={{
                            fontSize: "18px!important",
                            color: theme.palette.secondary[600],
                        }}>
                        View All Categories
                    </Button>
                    {!isMobile ? (
                        <ShareButton
                            variant="outlined"
                            fetchUrl={fetchSelectedCategoryUrl}
                            disableDefaultButtonStyles
                        />
                    ) : (
                        <Button
                            id="share"
                            onClick={() => {
                                setSharing(true);
                                shareImage();
                            }}
                            variant="outlined"
                            color="secondary"
                            disabled={sharing}
                            sx={{
                                gap: 1,
                                fontSize: "18px!important",
                            }}>
                            {sharing ? (
                                <Loader inline loading version="iconBlue" />
                            ) : (
                                <>
                                    <Share
                                        htmlColor={theme.palette.secondary[800]}
                                        sx={{
                                            fontSize: "15px!important",
                                        }}
                                    />
                                    Share
                                </>
                            )}
                        </Button>
                    )}
                </Box>
            </Box>
            {categories?.length ? (
                <>
                    <Typography
                        variant="h3"
                        fontInter
                        sx={{
                            color: "neutral.700",
                            fontWeight: "400!important",
                            fontSize: "23px!important",
                        }}>
                        Click on a Category or Subcategory name to see how products and vendors stack up against one
                        another
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 5,
                            width: "100%",
                            flexWrap: "wrap",
                            "&>div:first-child": {
                                width: "100%",
                            },
                            "&>div:last-child": {
                                width: "100%",
                                flexGrow: 1,
                            },
                            "@media (min-width: 1200px)": {
                                flexWrap: "nowrap",
                                "&>div:first-child": {
                                    width: "400px",
                                },
                                "&>div:last-child": {
                                    width: "auto",
                                    flexGrow: 1,
                                },
                            },
                        }}>
                        {categories && (
                            <VerticalTabs
                                id="selected-top-category"
                                defaultTab={categories[0] && categories[0]["sub-categories"]?.[0].id}
                                onlyOneOpen
                                alwaysOpen
                                items={categories.map(category => ({
                                    value: category.id,
                                    label: category.name,
                                    items: category["sub-categories"]?.map(subcategory => ({
                                        value: subcategory.id,
                                        label: subcategory.name,
                                    })),
                                }))}
                                panels={subCategories?.map(category => ({
                                    value: category.id,
                                    children: (
                                        <Box
                                            key={category.id}
                                            sx={{
                                                display: "flex",
                                                flexDirection: "column",
                                                gap: 3,
                                                alignItems: "flex-end",
                                                maxWidth: "790px",
                                                marginLeft: "auto",
                                            }}>
                                            <Button
                                                id="view-all-vendors"
                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                    ":friendly_url",
                                                    category?.friendly_url || "",
                                                )}
                                                variant="text"
                                                color="secondary"
                                                sx={{
                                                    fontSize: "18px!important",
                                                    color: theme.palette.secondary[600],
                                                    fontWeight: "600!important",
                                                    lineHeight: "22px",
                                                    whiteSpace: "pre-wrap",
                                                }}>
                                                View All Products on {category.name}
                                            </Button>
                                            <Box
                                                sx={{width: "100%", backgroundColor: "white"}}
                                                ref={topCategoriesChart}>
                                                <ProductByCategoryQuadrantChart category={category} />
                                            </Box>
                                        </Box>
                                    ) as ReactNode,
                                }))}
                            />
                        )}
                    </Box>
                </>
            ) : (
                <Typography>No popular categories at the moment. Check back later.</Typography>
            )}
        </Box>
    );
}

Builder.registerComponent(TopCategories, {
    name: "Most Popular Categories",
    inputs: [],
    defaultStyles: {
        marginTop: "0",
    },
});
