import {useEffect, useState} from "react";
import {useLocation} from "react-router";
import useAuthState from "../../hooks/useAuthState";
import {IPitchObject} from "../../models/PitchObject.interface";
import styles from "../../styles/pages/pitch.module.sass";
import {DATE_FULL_NODAY, formatDate, TIME_FORMAT} from "../../utils/formatDate";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {CPToast} from "../../components/CPToast";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import {CalendarIcon, ClockIcon} from "../../components/Icons";
import PitchRegisterButton from "../../components/Buttons/pitchRegister.component";

import {Builder} from "@builder.io/react";
import {GET_USER_PITCHES} from "../../reducers/auth.reducer";
import usePitchesState from "../../hooks/usePitchesState";
import {GET_NOT_STARTED} from "../../reducers/pitches.reducer";
import AddEventCalendarButton from "../../components/CalendarControls/AddEventCalendarButton.component";

export const UpcomingPitches = () => {
    const [showNotification, setShowNotification] = useState<boolean>(false);

    const location = useLocation();
    const {authState, updateAuthState} = useAuthState();
    const {pitchesState, updatePitchesState} = usePitchesState();

    useEffect(() => {
        if (authState?.userPitches?.length < 1 && authState.id) {
            updateAuthState(GET_USER_PITCHES);
        }
        if (pitchesState.pitches && pitchesState.pitches.length > 0) return;

        updatePitchesState(GET_NOT_STARTED);
    }, [location, authState.authenticated]);

    return (
        <>
            {!pitchesState.pitches ? null : (
                <>
                    <div className="d-flex flex-wrap my-lg-5 py-lg-3 w-100 justify-content-between justify-content-lg-around">
                        {pitchesState.pitches.map((upcomingPitch: IPitchObject, index: number) => {
                            return (
                                <div className={styles.pitchCard} key={upcomingPitch.id}>
                                    <div className="w-100">
                                        <TitleContainer
                                            as="h3"
                                            text={upcomingPitch.name}
                                            noConnector
                                            position="start"
                                            className="mb-2"
                                            style={{wordBreak: "break-word"}}
                                        />
                                        <div className="d-flex align-items-center">
                                            <CalendarIcon size={18} />
                                            <SubtitleContainer
                                                as="p"
                                                text={
                                                    upcomingPitch.start_date &&
                                                    formatDate(upcomingPitch.start_date, DATE_FULL_NODAY)
                                                }
                                            />
                                        </div>
                                        <div className="d-flex align-items-center">
                                            <ClockIcon size={18} />
                                            <SubtitleContainer
                                                as="p"
                                                text={
                                                    upcomingPitch.start_date &&
                                                    formatDate(upcomingPitch.start_date, TIME_FORMAT, true)
                                                }
                                            />
                                        </div>
                                    </div>
                                    <div className={styles.addToCalendarWrapper}>
                                        <AddEventCalendarButton
                                            id={upcomingPitch.id}
                                            addEventId={upcomingPitch.addevent_id}
                                        />
                                    </div>
                                    <PitchRegisterButton
                                        classes="mt-3 mb-1 py-3 w-lg-100"
                                        pitch={pitchesState.pitches![index]}
                                    />
                                </div>
                            );
                        })}
                    </div>
                </>
            )}
            <CPToast
                type="light"
                toastShow={showNotification}
                setToastShow={setShowNotification}
                description="You have unregistered successfully. You will no longer receive notifications for this event."
                title="Unregistered from Pitch"
            />
        </>
    );
};

Builder.registerComponent(UpcomingPitches, {
    name: "Upcoming Pitch Registration Card",
    defaultStyles: {
        marginTop: "0",
    },
});
