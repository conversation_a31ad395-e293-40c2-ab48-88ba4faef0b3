import {Builder} from "@builder.io/react";
import textStyles from "../../styles/builder/text.module.sass";
import {Link} from "react-router-dom";
import buttons from "../../styles/builder/buttons.module.sass";
import BrandConnector from "../../components/Logo/brandConnector.component";

export const CallToActionBlue = ({title, description, linkUrl, linkText}) => (
    <section className={textStyles.callToActionBlue} style={{backgroundImage: `url('/Media/dot-pattern.png')`}}>
        <div className="row pt-0">
            <div className={textStyles.callToActionContainer}>
                <div>
                    <BrandConnector size="32" className="mb-3 mx-auto mx-lg-0" />
                    <h2 className={`title ${textStyles.white} mb-3`}>
                        {title ? title : `Have any questions about us?`}
                    </h2>
                    <p className={`${textStyles.text} ${textStyles.white}`}>
                        {description ? description : `Lorem ipsum dolor sit amet, consectetur adipiscing elit Duis.`}
                    </p>
                </div>

                <Link
                    to={!linkUrl || linkUrl.includes("channelprogram.com/contact") ? "/contact" : linkUrl}
                    className={`${buttons.btnPrimary} mx-auto ms-lg-auto me-lg-0`}>
                    {linkText ? linkText : `Let's Connect!`}
                </Link>
            </div>
        </div>
    </section>
);

Builder.registerComponent(CallToActionBlue, {
    name: "CallToActionBlue",
    friendlyName: "Call to Action",
    inputs: [
        {name: "title", type: "text"},
        {name: "description", type: "text"},
        {name: "linkUrl", type: "text"},
        {name: "linkText", type: "text"},
    ],
    defaultStyles: {
        marginTop: "0",
    },
});
