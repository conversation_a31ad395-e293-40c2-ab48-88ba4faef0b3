import {useEffect} from "react";
import RegisterForm from "../../components/Auth/RegisterForm.component";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";

import {Builder} from "@builder.io/react";
import useQuery from "../../utils/query.util";
type regisFormI = {
    firstCheckbox: any;
    secondCheckbox: any;
    Fields: any[];
    vendorUpgradeImage?: string;
    disableRedirect?: boolean;
    customRedirect?: string;
};
export const RegistrationForm = (props: regisFormI) => {
    const {authState} = useAuthState();
    const navigate = useNavigate();
    const isLoggedIn = authState.authenticated && authState.access_token;
    const query = useQuery();
    const redirect_url = query.get("redirect_url");

    useEffect(() => {
        if (props.disableRedirect) return;
        if (isLoggedIn) {
            if (redirect_url?.includes("/partner-invite/")) {
                return;
            }
            navigate(props.customRedirect ? props.customRedirect : routeConfig.Home.path);
        }
        // eslint-disable-next-line
    }, [isLoggedIn]);

    return (
        <>
            <RegisterForm disableRedirect={props.disableRedirect} />
        </>
    );
};

Builder.registerComponent(RegistrationForm, {
    name: "Registration Form",
    inputs: [
        {
            name: "disableRedirect",
            type: "boolean",
            friendlyName: "Disable Redirect",
            helperText: "By default if the user is logged in the component will redirect to home page",
        },
        {
            name: "customRedirect",
            type: "text",
            friendlyName: "Custom Redirect",
            helperText: "If disable redirect is not enabled, the redirect will go to this route (Format: /example/)",
        },
        {
            name: "firstCheckbox",
            type: "richText",
            friendlyName: "Text for the  first checkbox",
        },
        {
            name: "secondCheckbox",
            type: "richText",
            friendlyName: "Text for the second checkbox",
        },
        {
            name: "vendorUpgradeImage",
            type: "file",
            friendlyName: "Vendor Upgrade Image",
            allowedFileTypes: ["jpeg", "png", "gif", "pdf", "svg"],
        },
        {
            name: "Fields",
            type: "list",
            defaultValue: [
                {field_id: "first_name", label: "First Name", step: 1},
                {field_id: "last_name", label: "Last Name", step: 1},
                {field_id: "email", label: "Email", step: 1},
                {field_id: "terms_consent", label: "", step: 1},
                {field_id: "VALIDATION_MODE", label: "", step: 2},
                {field_id: "confirmationCode", label: "Confirmation Code", step: 3},
                {field_id: "password", label: "Password", placeholder: "", step: 4},
                {field_id: "password_confirmation", label: "Password Confirmation", placeholder: "", step: 4},
                {field_id: "company_type", label: "Company Type", placeholder: "", step: 5},
                {field_id: "jobTitle", label: "Job Title", placeholder: "", step: 5},
                {field_id: "company_website", label: "Company Website", placeholder: "", step: 5},
                {field_id: "company_name", label: "Company Name", placeholder: "", step: 5},
                {field_id: "mobileConfirmationNumber", label: "Mobile Confirmation Number", placeholder: "", step: 5},
                {field_id: "phone", label: "Phone", placeholder: "", step: 5},
            ],
            onChange: () => {},
            subFields: [
                {
                    name: "field_id",
                    friendlyName: "Field ID: ",
                    type: "string",
                    defaultValue: "first_name",
                    helperText:
                        "Available Inputs: first_name, last_name, email, confirmationCode, company_website, company_name, phone, company_type, jobTitle, VALIDATION_MODE, password, password_confirmation, terms_consent",
                },
                {
                    name: "step",
                    type: "number",
                    defaultValue: 1,
                    helperText: "Steps are currently in fixed steps. Do not change this value.",
                    hideFromUI: true,
                },
                {
                    name: "label",
                    type: "string",
                    defaultValue: "First Name",
                },
                {
                    name: "placeholder",
                    type: "string",
                    defaultValue: "Enter first name",
                },
                {
                    name: "className",
                    type: "string",
                    defaultValue: "",
                    helperText:
                        "To Customize styling go to builder data tab on the register form component and open the code section. You should see an option to edit HTML/CSS. Click that and add styling for the class you made.",
                },
            ],
        },
    ],
    // noWrap:true
});
