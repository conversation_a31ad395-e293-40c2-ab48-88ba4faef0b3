import {Builder} from "@builder.io/react";
import {useTheme} from "@mui/material";
import Skeleton from "@mui/material/Skeleton";
import {useQuery} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {IProducts} from "../../Interfaces/products.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {categoriesService} from "../../services/category.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import CPSlider from "../../uicomponents/Molecules/CPSlider/cpSlider.component";
import ProductCard from "../../uicomponents/Organism/ProductCard/productCard.component";

export default function TrendingProductsBuilder() {
    const theme = useTheme();
    const productsQuery = useQuery<IProducts[]>(
        ["trending-products"],
        () => {
            return new Promise<IProducts[]>((resolve, reject) => {
                categoriesService.getTrendingProducts(
                    (res: AxiosResponse<IProducts[]>) => {
                        resolve(res.data);
                    },
                    err => {
                        reject(err);
                    },
                );
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const products = productsQuery.data;

    return (
        <Box
            sx={{
                backgroundColor: "secondary.100",
                width: "100vw",
                maxWidth: "100vw",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
            }}>
            <Box
                sx={{
                    padding: "80px 32px",
                    display: "flex",
                    flexDirection: "column",
                    gap: "24px",
                    width: "100%",
                    margin: "0 auto",
                    "@media (min-width: 768px)": {
                        padding: "80px 48px",
                    },
                }}>
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        flexWrap: "wrap",
                        gap: 4,
                    }}>
                    <Typography
                        fontInter
                        variant="h3"
                        sx={{
                            color: "secondary.main",
                            fontWeight: "400!important",
                            fontSize: "51px!important",
                            fontFamily: "var(--RocGrotesk)!important",
                        }}>
                        Top Trending Products
                    </Typography>
                    <Button
                        id="view-all-products"
                        to={routeConfig.ProductReviewSearch.path}
                        variant="outlined"
                        color="secondary"
                        sx={{
                            fontSize: "18px!important",
                            "&:hover": {
                                backgroundColor: `${theme.palette.secondary.main}!important`,
                                color: "white!important",
                            },
                        }}>
                        View All Products
                    </Button>
                </Box>
                <Typography
                    fontInter
                    variant="h2"
                    sx={{
                        color: "neutral.700",
                        fontSize: "23px!important",
                    }}>
                    Search from <strong>1000+</strong> products and companies to fit your needs
                </Typography>
                {products?.length ? (
                    <CPSlider
                        arrows
                        autoplay
                        autoplaySpeed={5000}
                        draggable
                        easing="ease-in-out"
                        infinite
                        slidesToShow={products?.length < 4 ? products?.length : 4}
                        slidesToScroll={products?.length < 4 ? products?.length : 4}
                        appendDots={dots => (
                            <Box
                                sx={{
                                    display: "flex !important",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    gap: "8px",
                                    width: "100%",
                                }}>
                                {dots}
                            </Box>
                        )}
                        responsive={[
                            {
                                breakpoint: 1200,
                                settings: {
                                    slidesToShow: 3,
                                    slidesToScroll: 3,
                                },
                            },
                            {
                                breakpoint: 992,
                                settings: {
                                    slidesToShow: 2,
                                    slidesToScroll: 2,
                                },
                            },
                            {
                                breakpoint: 600,
                                settings: {
                                    slidesToShow: 1,
                                    slidesToScroll: 1,
                                },
                            },
                        ]}
                        wrapperSx={{
                            width: "100%",
                            "& button.slick-arrow": {
                                width: "32px!important",
                                height: "32px!important",
                                background: `${theme.palette.secondary[200]}!important`,
                                "&::before": {
                                    width: "32px!important",
                                    height: "32px!important",
                                    fontSize: "22px!important",
                                    lineHeight: "22px!important",
                                    transform: "translate(0,0)!important",
                                    zIndex: 10,
                                    opacity: "1!important",
                                    color: `${theme.palette.secondary[200]}!important`,
                                    /* border: `5px solid ${theme.palette.secondary[200]}!important`, */
                                    borderRadius: "50%!important",
                                    padding: "0!important",
                                    margin: "0!important",
                                    display: "flex!important",
                                    alignItems: "center!important",
                                    justifyContent: "center!important",
                                    marginTop: "2px!important",
                                },
                                "&::after": {
                                    content: "''",
                                    position: "absolute",
                                    top: "50%",
                                    left: "50%",
                                    transform: "translate(-50%, -50%)",
                                    width: "14px",
                                    height: "14px",
                                    borderRadius: "50%",
                                    background: `${theme.palette.secondary.main}!important`,
                                    zIndex: -1,
                                },
                            },
                        }}>
                        {products?.length
                            ? products?.map((product: IProducts) => (
                                  <Box
                                      key={product.id}
                                      sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "center",
                                      }}>
                                      <ProductCard sx={{maxWidth: "350px"}} key={product.id} {...product} />
                                  </Box>
                              ))
                            : Array(10)
                                  .fill(null)
                                  ?.map((_, i) => <Skeleton key={i} variant="rounded" width={300} height={200} />)}
                    </CPSlider>
                ) : (
                    <Typography fontInter>No trending products at the moment. Check back later.</Typography>
                )}
            </Box>
        </Box>
    );
}

Builder.registerComponent(TrendingProductsBuilder, {
    name: "Top Trending Products Section",
    defaultStyles: {
        marginTop: "0",
    },
});
