import builder, {Builder, withChildren} from "@builder.io/react";
import {useEffect, useState} from "react";
import {ModalComponent} from "../../components/Modal";
import styles from "../../styles/components/eventCalendarFreeModal.module.sass";
import ErrorBoundary from "../../utils/errorBoundary.util";
import OutboundLink from "../../components/Links/OutboundLink.component";

interface IProps {
    show?: boolean;
    setShow: any;
}

const EventCalendarFreeModal = (props: IProps) => {
    const [data, setData] = useState<
        undefined | {content: string; title: string; url: string; openInNewTab: boolean}
    >();

    const getData = async () => {
        const data: any = await builder.getAll("event-calendar-free-modal");
        setData(data[0]?.data);
    };

    useEffect(() => {
        getData();
    }, [props.show]);

    return (
        <ErrorBoundary>
            <ModalComponent
                modalSwitcher={props.show}
                modalSwitcherCallback={props.setShow}
                contentClassName={styles.freeModal}
                modalBodyClassName={styles.freeModalBody}
                headerTitle={data?.title}
                form={
                    <div className="d-flex flex-column justify-content-between align-items-center mh-100">
                        <div
                            id="content"
                            className="h-100 p-2 w-75"
                            dangerouslySetInnerHTML={{__html: data?.content || ""}}
                        />
                        <div className="d-flex flex-row align-items-center justify-content-end w-100">
                            <span
                                className="linkStyle h6 me-3"
                                style={{cursor: "pointer"}}
                                onClick={() => props.setShow && props.setShow(false)}>
                                Cancel
                            </span>
                            <OutboundLink
                                href={data?.url || "#"}
                                target={data?.openInNewTab ? "_blank" : ""}
                                className="cpBlueBtn">
                                Contact Sales
                            </OutboundLink>
                        </div>
                    </div>
                }
            />
        </ErrorBoundary>
    );
};

const EventCalendarFreeModalWithChildren = withChildren(EventCalendarFreeModal);

Builder.registerComponent(EventCalendarFreeModalWithChildren, {
    name: "Event Calendar - Free Vendor Modal",
    defaultChildren: [
        {
            "@type": "@builder.io/sdk:Element",
            component: {name: "Text", options: {text: "I am child text block!"}},
        },
    ],
    inputs: [
        {
            name: "title",
            type: "text",
        },
        {
            name: "content",
            friendlyName: "Rich text to show as the content of the modal",
            type: "richText",
        },
    ],
});

export default EventCalendarFreeModal;
