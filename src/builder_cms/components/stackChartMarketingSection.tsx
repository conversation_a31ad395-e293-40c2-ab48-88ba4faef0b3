import {useEffect, useState} from "react";
import {BuilderComponent, builder} from "@builder.io/react";

const StackChartMarketingContent = () => {
    const [builderContentJson, setBuilderContentJson] = useState();

    useEffect(() => {
        builder
            .get("stack-chart-marketing-content", {url: window.location.pathname})
            .promise()
            .then(setBuilderContentJson);
    }, []);

    return <BuilderComponent model="stack-chart-marketing-content" content={builderContentJson} />;
};

export default StackChartMarketingContent;
