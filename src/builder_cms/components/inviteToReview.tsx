import builder, {<PERSON>uild<PERSON>, with<PERSON><PERSON><PERSON>n} from "@builder.io/react";
import {useEffect, useState} from "react";
import ButtonComponent from "../../components/FormControls/button.component";
import {ModalComponent} from "../../components/Modal";
import styles from "../../styles/components/inviteToReview.module.sass";

interface IProps {
    children?: any;
    textToCopy?: string;
    productName?: string;
    vendorName?: string;
    productLink?: string;
    className?: string;
}

const textKeys: Array<string[]> = [
    ["PRODUCT_NAME", "productName"],
    ["VENDOR_NAME", "vendorName"],
    ["PRODUCT_LINK", "productLink"],
];

const InviteToReview = (props: IProps) => {
    const [show, setShow] = useState(false);
    const [data, setData] = useState<any>();
    const [copied, setCopied] = useState(false);

    const parseText = (text: string | undefined) => {
        if (!text) return "";
        let updated = text;
        textKeys.forEach((key: string[]) => {
            if (key[0] === "PRODUCT_LINK") {
                updated = updated.replaceAll(`{${key[0]}}`, window.location.origin + props[key[1]] || " ");
                return;
            }
            updated = updated.replaceAll(`{${key[0]}}`, props[key[1]] || " ");
        });
        return updated;
    };

    const renderedText = parseText(data?.textToCopy);

    const getData = async () => {
        const data: any = await builder.getAll("invite-to-review");
        setData(data[0]?.data);
    };

    const handleCopy = async () => {
        const range = document.createRange();
        const textToCopy = document.getElementById("textToCopy");
        if (!textToCopy) return;
        range.selectNode(textToCopy);
        window.getSelection()?.removeAllRanges();
        window.getSelection()?.addRange(range);
        document.execCommand("copy");
        window.getSelection()?.removeAllRanges();
        setCopied(true);
    };

    useEffect(() => {
        getData();
        return () => {
            setTimeout(() => {
                setCopied(false);
            }, 500);
        };
    }, [show]);

    return (
        <>
            <div className={props.className} onClick={() => setShow(true)}>
                {props.children}
            </div>
            <ModalComponent
                modalSwitcher={show}
                modalSwitcherCallback={setShow}
                contentClassName={styles.inviteModal}
                modalBodyClassName={styles.inviteModalBody}
                headerTitle={copied ? data.successTextTitle || "" : data?.textToCopyTitle || ""}
                form={
                    <div className="d-flex flex-column justify-content-between align-items-center mh-100">
                        <div
                            id="textToCopy"
                            className="h-100 p-2 w-75"
                            dangerouslySetInnerHTML={{__html: copied ? data?.successText : renderedText || ""}}
                        />
                        <ButtonComponent
                            id="copyTextBtn"
                            onClick={copied ? () => setShow(false) : handleCopy}
                            className="cpBlueBtn"
                            tooltip={copied ? "" : "Click to Copy"}>
                            {copied ? "Ok" : "Copy Message"}
                        </ButtonComponent>
                    </div>
                }
            />
        </>
    );
};

const InviteToReviewWithChildren = withChildren(InviteToReview);

Builder.registerComponent(InviteToReviewWithChildren, {
    name: "Invite to Review",
    defaultChildren: [
        {
            "@type": "@builder.io/sdk:Element",
            component: {name: "Text", options: {text: "I am child text block!"}},
        },
    ],
    inputs: [
        {
            name: "textToCopy",
            friendlyName: `Text to Copy: This is the text that will be copied to the clipboard. You can use opening and closing curly brackets {} to signify a variable name. Available keys are ${textKeys.map(
                key => key + ",",
            )}`,
            type: "richText",
        },
    ],
});

export default InviteToReview;
