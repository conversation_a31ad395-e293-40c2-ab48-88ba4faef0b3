import {Builder} from "@builder.io/react";
import {ButtonPropsVariantOverrides} from "@mui/material";
import {OverridableStringUnion} from "@mui/types";
import {AxiosError} from "axios";
import {useState} from "react";
import {v4 as uuidv4} from "uuid";
import {MutateTypes} from "../../Interfaces/queries.interface";
import routeConfig from "../../constants/routeConfig";
import {useCompany} from "../../hooks/fetches/useCompany";
import useActiveCompany from "../../hooks/useActiveCompany";
import useNotification from "../../hooks/useNotification";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import {getErrorFromArray} from "../../utils/error.util";

interface IActionButton {
    variant?: OverridableStringUnion<"text" | "outlined" | "contained", ButtonPropsVariantOverrides>;
    text: string;
    action: string;
    target: string;
    custom_url?: string;
}

const ACTION_KEYS = {
    CONTACT_US: "Contact Us",
    CUSTOM_URL: "Custom URL",
    HIDE_AFFILIATES_BANNER: "Hide Affiliates Banner",
    HIDE_CLIENTS_BANNER: "Hide Clients Banner",
    HIDE_DISTRIBUTORS_BANNER: "Hide Distributors Banner",
};

const TARGET_KEYS = {
    _blank: "New Page",
    _self: "Same Page",
};

const BUTTON_TYPES = {
    tonal_blue: "Gradient Blue",
    tonal: "Gradient Orange",
    text: "Link Blue",
    outlined: "Outline Blue",
};

export const ActionButton = ({variant, text, action, target = "_self", custom_url = undefined}: IActionButton) => {
    const notify = useNotification();
    const [loading, setLoading] = useState<boolean>(false);
    const {activeCompany} = useActiveCompany();
    const {updateCompany} = useCompany(activeCompany?.friendly_url ?? "", {calls: {all: false}});
    const handleError = (error: AxiosError<any>) => {
        setLoading(false);
        notify(getErrorFromArray(error), "Error");
    };

    const hideBanner = (fieldName: string) => {
        setLoading(true);
        updateCompany.mutate({
            mutate_type: MutateTypes.UpdateBannerFlag,
            body: {id: activeCompany?.id, [fieldName]: false},
            onSuccess: handleSuccessHideBanner,
            onError: handleError,
        });
    };

    const handleSuccessHideBanner = () => {
        setLoading(false);
    };

    const handleClick = () => {
        const selectedTarget = (Object.keys(TARGET_KEYS) as (keyof typeof TARGET_KEYS)[]).find(key => {
            return TARGET_KEYS[key] === target;
        });
        switch (action) {
            case ACTION_KEYS.CONTACT_US:
                window.open(routeConfig.Contact.path, selectedTarget);
                break;
            case ACTION_KEYS.CUSTOM_URL:
                window.open(custom_url, selectedTarget);
                break;
            case ACTION_KEYS.HIDE_AFFILIATES_BANNER:
                hideBanner("show_manage_affiliates_banner");
                break;
            case ACTION_KEYS.HIDE_CLIENTS_BANNER:
                hideBanner("show_manage_clients_banner");
                break;
            case ACTION_KEYS.HIDE_DISTRIBUTORS_BANNER:
                hideBanner("show_distributor_banner");
                break;
            default:
                return new Error("Logic not implemented for " + action);
        }
    };

    const selectedVariant = (Object.keys(BUTTON_TYPES) as (keyof typeof BUTTON_TYPES)[]).find(key => {
        return BUTTON_TYPES[key] === variant;
    });

    return (
        <Button loading={loading} variant={selectedVariant} color="secondary" id={uuidv4()} onClick={handleClick}>
            {text}
        </Button>
    );
};

Builder.registerComponent(ActionButton, {
    name: "Action Button",
    inputs: [
        {
            required: true,
            name: "variant",
            friendlyName: "Button's variant type",
            type: "string",
            enum: Object.values(BUTTON_TYPES),
        },
        {
            required: true,
            name: "text",
            friendlyName: "Button's text",
            type: "text",
        },
        {
            required: true,
            name: "action",
            friendlyName: "Button's action",
            type: "string",
            enum: Object.values(ACTION_KEYS),
        },
        {
            required: true,
            name: "target",
            friendlyName: "Button's target",
            type: "string",
            enum: Object.values(TARGET_KEYS),
        },
        {
            required: false,
            name: "custom_url",
            friendlyName: "Button's custom url",
            type: "string",
        },
    ],
});
