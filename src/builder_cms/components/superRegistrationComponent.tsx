import {useEffect, useState} from "react";
import {Link, useNavigate} from "react-router-dom";
import RegisterForm from "../../components/Auth/OldRegisterForm.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import styles from "../../styles/pages/register.module.sass";
import useQuery from "../../utils/query.util";

import {Builder} from "@builder.io/react";

export function Register(props) {
    const [registered, setRegistered] = useState(false);

    const [completeSentence, setCompleteSentence] = useState("");

    const {registerTitle, breakWord, bannerTitle, linkText, firstCheckbox, secondCheckbox, labels, Image} = props;

    const text = registerTitle;

    const {authState} = useAuthState();
    const navigate = useNavigate();
    const isLoggedIn = authState.authenticated;
    const query = useQuery();
    const eventId = query.get("pitch_event_id");
    const loginPath = routeConfig.Login.path + (eventId ? `?pitch_event_id=${eventId}` : "");

    function getFormClass() {
        if (!registered) {
            return "showElement";
        } else {
            return "hideToLeft";
        }
    }

    function getMessageClass() {
        if (registered) {
            return "showElement";
        } else {
            return "hideToRight";
        }
    }

    function makeCompleteSentence() {
        let indexOfBreak: number | null = null;
        let first_sentence: string = "";
        let second_sentence: string = "";
        if (breakWord) {
            indexOfBreak = text.indexOf(breakWord);
            first_sentence = text.substring(0, indexOfBreak! + breakWord.length);
            second_sentence = text.substring(indexOfBreak + breakWord.length);

            setCompleteSentence(first_sentence + "<br/>" + second_sentence);
        }
        if (breakWord === "" || !breakWord) {
            setCompleteSentence(text);
        }
    }

    useEffect(() => {
        if (isLoggedIn) {
            navigate(routeConfig.Home.path);
        }
        // eslint-disable-next-line
    }, [isLoggedIn]);

    useEffect(() => {
        makeCompleteSentence();
    });

    return (
        <div className="routePadding">
            {!registered && (
                <>
                    <h2
                        className="text-center"
                        dangerouslySetInnerHTML={{
                            __html:
                                completeSentence !== ""
                                    ? completeSentence
                                    : "To join our community and attend Channel Pitch,<br/> create a Channel Program account (100% free!)",
                        }}></h2>

                    <Link to={loginPath} className={styles.loginLink}>
                        {linkText ? linkText : "If you already have an account, sign in to register here."}
                    </Link>
                </>
            )}
            <div className={styles.formContainer}>
                <div className={`${getFormClass()} d-flex flex-wrap`}>
                    <div className="col-12 col-lg-6 pe-md-3">
                        <RegisterForm
                            registered={registered}
                            firstCheckbox={firstCheckbox}
                            secondCheckbox={secondCheckbox}
                            setRegistered={setRegistered}
                            labels={labels}
                        />
                    </div>
                    <div className={`col-12 col-lg-6 ps-md-3 ${styles.lineupContainer}`}>
                        <h2 className="align-items-center text-center">
                            {bannerTitle ? bannerTitle : "January 2022 Lineup Preview"}
                        </h2>
                        <img
                            src={Image ? Image : "/Media/register/lineup-image.jpg"}
                            alt="Lineup of speakers for january pitch."
                        />
                    </div>
                </div>
                <div className={getMessageClass() + " " + styles.messageBody}>
                    <TitleContainer as="h2" text="Almost done!" />
                    <SubtitleContainer
                        as="p"
                        text="Check your email! We sent you an account verification email."
                        className="mb-5"
                    />
                    <Link to={loginPath} className="cpMainBtn">
                        Back to Login
                    </Link>
                    <br />
                </div>
            </div>
        </div>
    );
}

Builder.registerComponent(Register, {
    name: "Super Registration Form",
    inputs: [
        {
            name: "registerTitle",
            friendlyName: "Title for Sign Up Forms",
            type: "longText",
            defaultValue: "[Default Text]",
        },
        {
            name: "breakWord",
            friendlyName: "Line Break Word for title",
            type: "longText",
        },
        {
            name: "bannerTitle",
            friendlyName: "Banner Title for Image",
            type: "longText",
        },
        {
            name: "linkText",
            friendlyName: "Link for text",
            type: "string",
        },
        {
            name: "labels",
            friendlyName: "Form Labels",
            type: "object",
            subFields: [
                {
                    name: "first_name",
                    type: "string",
                },
                {
                    name: "last_name",
                    type: "string",
                },
                {
                    name: "email",
                    type: "string",
                },
                {
                    name: "company_name",
                    type: "string",
                },
                {
                    name: "company_type",
                    type: "string",
                },
                {
                    name: "password",
                    type: "string",
                },
                {
                    name: "password_confirmation",
                    type: "string",
                },
            ],
        },
        {
            name: "firstCheckbox",
            type: "richText",
            friendlyName: "Text for the  first checkbox",
        },
        {
            name: "secondCheckbox",
            type: "richText",
            friendlyName: "Text for the second checkbox",
        },
        {
            name: "Image",
            type: "file",
            allowedFileTypes: ["jpeg", "png", "gif"],
            friendlyName: "Image for Upcoming Pitch ",
        },
    ],
});
