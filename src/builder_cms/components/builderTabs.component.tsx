import {Builder, BuilderBlocks} from "@builder.io/react";
import {useState} from "react";
import styles from "../../styles/builder/tabs.module.sass";

interface IProps {
    tabs: any[];
    defaultTab?: number;
    builderBlock?: any;
    tabClassName?: string;
    tabWrapperClassName?: string;
}

export function BuilderTabs(props: IProps) {
    const [activeTab, setActiveTab] = useState(props.defaultTab || 0);
    return (
        <>
            <div className={`${styles.tabLabelContainer} ${props.tabWrapperClassName}`}>
                {props.tabs?.map((item, index) => (
                    <span
                        key={index}
                        className={`${styles.tabLabel} ${props.tabClassName}`}
                        aria-selected={index === activeTab}
                        onClick={() => {
                            setActiveTab(index);
                        }}>
                        {item.label}
                        <div className={`${styles.tabLine} ${index < activeTab ? styles.tabLineLeft : ""}`} />
                    </span>
                ))}
            </div>
            {props.tabs?.length && (
                <BuilderBlocks
                    parentElementId={props.builderBlock.id}
                    dataPath={`component.options.tabs.${activeTab}.content`}
                    blocks={props.tabs[activeTab].content}
                />
            )}
        </>
    );
}

Builder.registerComponent(BuilderTabs, {
    name: "Tabs",
    inputs: [
        {
            name: "defaultTab",
            type: "number",
            required: true,
            defaultValue: 0,
            helperText: "The tab to show by default. Pass in the number that the tab is from the left starting from 0.",
        },
        {
            name: "tabClassName",
            type: "text",
            required: false,
            defaultValue: "",
        },
        {
            name: "tabWrapperClassName",
            type: "text",
            required: false,
            defaultValue: "",
        },
        {
            name: "tabs",
            type: "list",
            subFields: [
                {
                    name: "label",
                    type: "text",
                    defaultValue: "New tab",
                },
                {
                    name: "content",
                    type: "uiBlocks",
                    defaultValue: [],
                },
            ],
            defaultValue: [
                {
                    label: "New Tab",
                    content: [],
                },
            ],
        },
    ],
});
