import {useEffect, useRef, useState} from "react";
import useAuthState from "../../hooks/useAuthState";
import {IPitchObject} from "../../models/PitchObject.interface";
import {UpcomingPitch} from "../../components/Home/upcomingPitch.component";
import styles from "../../styles/pages/homeUpdate.module.sass";
import {Builder} from "@builder.io/react";
import usePitchesState from "../../hooks/usePitchesState";
import {GET_NOT_STARTED} from "../../reducers/pitches.reducer";
import {GET_USER_PITCHES} from "../../reducers/auth.reducer";

type UpcomingPitchI = {
    upcomingPitchImage?: string;
    imageAspectRatio?: string;
    pitchId?: string;
    registerUrl?: string;
};

export function UpcomingPitchCard({upcomingPitchImage, imageAspectRatio, pitchId, registerUrl}: UpcomingPitchI) {
    const [nextPitch, setNextPitch] = useState<IPitchObject>({} as IPitchObject);

    const {authState, updateAuthState} = useAuthState();
    const {pitchesState, updatePitchesState} = usePitchesState();
    const hasFetched = useRef(false);

    useEffect(() => {
        if (pitchesState.pitches && pitchesState.pitches.length > 0) {
            if (pitchId) {
                const findPitchId = pitchesState.pitches.find(pitch => pitch.id === pitchId);
                if (findPitchId) {
                    localStorage.nextEventId = pitchesState.pitches?.[0]?.id;
                    return setNextPitch(findPitchId);
                }
            }
            setNextPitch(pitchesState.pitches?.[0]);
            localStorage.nextEventId = pitchesState.pitches?.[0]?.id;
            return;
        }
        if (hasFetched.current) return;
        hasFetched.current = true;
        updatePitchesState(GET_NOT_STARTED);
        // eslint-disable-next-line
    }, [pitchesState.pitches]);

    useEffect(() => {
        if (authState?.userPitches?.length < 1 && authState.id) {
            updateAuthState(GET_USER_PITCHES);
        }

        // eslint-disable-next-line
    }, []);

    return (
        <UpcomingPitch
            nextPitch={nextPitch}
            upcomingPitchImage={upcomingPitchImage}
            imageAspectRatio={imageAspectRatio}
            registerUrl={registerUrl}
            className={styles.upcomingPitchFitAspectRatio}
        />
    );
}

Builder.registerComponent(UpcomingPitchCard, {
    name: "Upcoming Pitch Card",
    inputs: [
        {
            name: "upcomingPitchImage",
            type: "file",
            allowedFileTypes: ["jpeg", "png", "gif"],
            friendlyName: "Image for Upcoming Pitch ",
        },
        {
            name: "imageAspectRatio",
            type: "string",
            defaultValue: "3/1",
            friendlyName: "Enter aspect ratio as a fraction for the background image, default is 3/1",
        },
        {
            name: "pitchId",
            type: "string",
            friendlyName: "Enter the pitch id to be shown in the card",
        },
        {
            name: "registerUrl",
            type: "string",
            friendlyName: "Enter the custom url for the register button",
        },
    ],
    defaultStyles: {
        marginTop: "0",
    },
});
