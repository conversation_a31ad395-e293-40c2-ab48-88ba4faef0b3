import {Builder} from "@builder.io/react";
import {NavLink} from "react-router-dom";

export const LinkButton = ({url, text, activeClassName, inactiveClassName}) => (
    <NavLink to={url} className={(nav: any) => (nav.isActive ? `${activeClassName}` : `${inactiveClassName}`)}>
        {text}
    </NavLink>
);

Builder.registerComponent(LinkButton, {
    name: "NavLink Button",
    inputs: [
        {
            name: "url",
            friendlyName: "But<PERSON>'s URL",
            type: "text",
        },
        {
            name: "text",
            friendlyName: "But<PERSON>'s text",
            type: "text",
        },
        {
            name: "activeClassName",
            friendlyName: "Active Class Name",
            type: "text",
        },
        {
            name: "inactiveClassName",
            friendlyName: "Inactive Class Name",
            type: "text",
        },
    ],
});
