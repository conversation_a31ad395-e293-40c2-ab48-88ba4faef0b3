import {IVideos} from "./profiles.interface";

export interface ISpeaker {
    id: string;
    name: string;
    avatar: string;
    handle: string;
    video: IVideos;
    image: string;
    friendly_url?: string;
}

export interface IFeaturedSpeaker {
    breakout_room_link: string;
    category: any;
    company_id: string;
    id: string;
    pitch_event: any;
    pitch_name: string;
    vendor: {
        name: string;
        id: string;
        avatar: string;
        current_user_is_following: boolean;
        followers_count: number;
        friendly_url: string;
        handle: string;
        users: any[];
        videos: any;
    };
}
