import {ImageTypes} from "../enums/imageTypes.enum";
import type {IMuiSelectObj} from "../uicomponents/Molecules/MuiSelect/MuiSelect.component";
import {IBulletin} from "./bulletin.interface";
import {ICompanyType} from "./company.interface";
import type {IAuthor} from "./contentFeed.interface";
import {IVendorCompany} from "./msp.interface";
import type {IMeta, IPaginationData} from "./pagination.interface";
import {IPartnerAsset} from "./partnerAssets.interface";
import type {IParent, IProperties} from "./profiles.interface";
import type {ICategoriesData} from "./search.interface";
import type {ITag} from "./tags.interface";

export interface IPartnerPage {
    id: string;
    name: string;
    status: string;
    title: string;
    friendly_url: string;
    description: string;
    header_image: IHeaderImage;
    company_id: string;
    author_id: string;
    sections: Array<IPartnerPageSection>;
    created_at: string;
    updated_at: string;
    bulletins?: IBulletin[];
}

export interface IHeaderImage {
    alt?: string;
    collection_name?: string;
    description?: string;
    id: string;
    image_type?: ImageTypes;
    src?: string;
    title?: string;
}

export interface IPartnerPageSection {
    id: string;
    partner_page_id: string;
    status: boolean;
    title: string;
    main_content_id: string;
    layout: string;
    order: 0;
    author_id: string;
    created_at: string;
    updated_at: string;
    contents?: any[];
}

interface IPartnerData {
    id: string;
    name: string;
    description: string | null;
    handle: string | null;
    friendly_url: string;
    subdomain: string | null;
    avatar: string | null;
    subcategories: [];
    product_reviews_count: number | null;
    partner_flag: boolean;
    type: ICompanyType;
}

export interface IPartnerInvite {
    accepted_at: string;
    email: string;
    followed_partner_id: string;
    follower_partner_id: string;
    id: string;
    initiator: string;
    invited_at: string;
    follower_partner: IPartnerData;
    invited_by: {
        avatar: string | null;
        company: string | null;
        description: string | null;
        friendly_url: string | null;
        handle: string | null;
        id: string;
        is_private: boolean;
        name: string;
    };
    partner: IPartnerData;
    rejected_at: string;
    rejected_reason: string;
    status: string;
    source: string;
    name?: string;
    last_sent_invite_at?: string;
}

export interface IPartnerDataSection {
    author_id: string;
    created_at: string;
    id: string;
    layout: string;
    main_content_id: string | null;
    order: number;
    partner_page_id: string;
    status: string;
    title: string;
    updated_at: string;
}

export interface IVendorLayouts {
    Article: "articles_layout";
    Assets: "assets_layout";
    Document: "documents_layout";
    Video: "videos_layout";
    Template: "templates_layout";
}

export enum VENDOR_LAYOUTS {
    Article = "articles_layout",
    Assets = "assets_layout",
    Document = "documents_layout",
    Video = "videos_layout",
    Template = "templates_layout",
}

export enum VENDOR_LAYOUTS_TITLES {
    "templates_layout" = "Brandable Content",
    "videos_layout" = "Videos",
    "documents_layout" = "Vendor Documentation",
    "articles_layout" = "Vendor Blogs",
    "assets_layout" = "Vendor Assets",
}

export interface IVendorConfirmationModalData {
    id: string;
    name: string;
    invited_by?: {name: string};
    follower_partner?: {name: string};
    partner?: {name: string};
    followed_partner_id?: string;
    email?: string;
}
export type TVendorConfirmationModalTypes = "Accept" | "Reject" | "Invitation" | "Remove" | "Edit" | "";

export interface IVendorConfirmationModal {
    show: boolean;
    type: TVendorConfirmationModalTypes;
    defaultValue?: IMuiSelectObj;
}

export type TConfirmationWithData<T = IVendorConfirmationModalData> = {data: T} & IVendorConfirmationModal;

export interface IPartnerPageDataResponse {
    articles: IArticleContent[];
    assets: IVendorAsset[];
    documents: ISectionDocuments[];
    videos: IVideo[];
    templates: ITemplateContent[];
}
export interface IPaginatedPartnerPageDataResponse {
    articles: IPaginationData<IArticleContent[]>;
    assets: IPaginationData<IVendorAsset[]>;
    documents: IPaginationData<ISectionDocuments[]>;
    videos: IPaginationData<IVideo[]>;
    templates: IPaginationData<ITemplateContent[]>;
}

export interface IArticleContent {
    id: string;
    title: string;
    friendly_url: string;
    excerpt: string;
    body: string;
    status: string;
    likes_count: number;
    user_has_liked: boolean;
    images: Array<{
        id: string;
        src: string;
        alt: string;
        title: string;
        description: string;
        image_type: string;
        collection_name: string;
    }>;
    categories: Array<ICategoriesData>;
    tags: Array<ITag>;
    author: IAuthor;
    is_partner_content: boolean;
    section_id: string;
    section_content_id: string;
    is_hidden: boolean;
    published_at: string;
    created_at: string;
    updated_at: string;
    media_visibility?: "vendor" | "msp" | "all";
    main_content_id?: string;
}

export interface ITemplateContent {
    id: string;
    name: string;
    collection_name: string;
    file_name: string;
    mime_type: string;
    title: string;
    description: string;
    custom_properties: IProperties;
    size: number;
    thumbnail_url: string;
    url: string;
    likes_count: number;
    user_has_liked: boolean;
    comments_count: number;
    user_has_commented: boolean;
    section_id: string;
    section_content_id: string;
    is_hidden: boolean;
    created_at: string;
    updated_at: string;
    parent: IParent | null;
    tags: Array<ITag>;
    author: IAuthor;
    vendor_company_id: string;
    vendor_company: IVendorCompany;
    main_content_id?: string;
}

interface ISectionDocuments {
    id: string;
    name: string;
    collection_name: string;
    file_name: string;
    mime_type: string;
    title: string;
    description: string;
    custom_properties: IProperties;
    size: number;
    thumbnail_url: string;
    url: string;
    likes_count: number;
    user_has_liked: boolean;
    comments_count: number;
    user_has_commented: boolean;
    section_id: string;
    section_content_id: string;
    is_hidden: boolean;
    created_at: string;
    updated_at: string;
    parent: IParent | null;
    tags: Array<ITag>;
    author: IAuthor;
    main_content_id?: string;
}

interface IVendorAsset extends IPartnerAsset {
    tags: Array<ITag>;
    section_id: string;
    section_content_id: string;
    is_hidden: boolean;
    main_content_id?: string;
}

interface IVideo {
    id: string;
    title: string;
    url: string;
    thumbnail: string;
    likes_count: number;
    user_has_liked: boolean;
    comments_count: number;
    user_has_commented: boolean;
    custom_properties: IProperties;
    section_id: string;
    section_content_id: string;
    is_hidden: boolean;
    created_at: string;
    parent: IParent | null;
    tags: Array<ITag>;
    author: IAuthor;
    main_content_id?: string | null;
}

export interface ISectionResponse {
    id: string;
    partner_page_id: string;
    status: string;
    title: string;
    main_content_id: string | null;
    layout: string;
    order: number;
    author_id: string;
    created_at: string;
    updated_at: string | null;
}
export type TPartnerPageLayouts =
    | "articles_layout"
    | "assets_layout"
    | "documents_layout"
    | "videos_layout"
    | "templates_layout";
export type TPartnerPageSectionsTypes = "Article" | "Assets" | "Document" | "Video" | "Template";
export type TPartnerContents<T extends TPartnerPageLayouts> = T extends "articles_layout"
    ? IArticleContent[]
    : T extends "assets_layout"
      ? IVendorAsset[]
      : T extends "documents_layout"
        ? ISectionDocuments[]
        : T extends "videos_layout"
          ? IVideo[]
          : T extends "templates_layout"
            ? ITemplateContent[]
            : never;
export type IPartnerSection<T extends TPartnerPageLayouts = TPartnerPageLayouts> = {
    [key in T]: {
        id: string;
        partner_page_id: string;
        status: string;
        title: string;
        main_content_id: string | null;
        layout: T;
        order: number;
        author_id: string;
        created_at: string;
        updated_at: string | null;
        contents: TPartnerContents<T>;
        meta: IMeta;
    };
};
