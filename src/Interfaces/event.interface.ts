import {IAvatarV2Props} from "../uicomponents/Molecules/Avatar/avatarV2.component";

export interface IEventReport {
    event_id: string;
    vendor_id: string;
}

export interface IEventAttendeeCount {
    event_name: string;
    attendee_count: number;
    event_date: Date;
    event_id: string;
}

export type IndustryEventTypes = "in person" | "virtual" | "webinar" | "virtual_product_demo";
export interface IEvent {
    id: any;
    event_id: string;
    event_name: string;
    description?: string;
    link: string;
    organization: string;
    start_date: string;
    end_date: string;
    in_person_or_virtual: IndustryEventTypes;
    location: string;
    cp_event: boolean;
    is_published: boolean;
    created: any;
    created_at?: string;
    updated: any;
    updated_at?: string;
    subject_id?: string;
    subject_name?: string;
    subject_friendly_url?: string;
    author_id?: string;
    avatar?: string | IAvatarV2Props;
    feature_flag?: boolean;
    feature_start_date: string;
    feature_end_date: string;
    image?: any;
    time_zone: any;
    time_zone_id?: string | null;
}

export interface ICalendarEventsAPI {
    [year: string]: {
        [month: string]: IEvent[];
    };
}
