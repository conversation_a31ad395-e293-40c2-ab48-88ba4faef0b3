import {ICategory} from "./categories.interface";

export interface IAdvertisementBody {
    name: string;
    description?: string;
    subject_id: string;
    subject_type: string;
    advertisement_card_type_id: string;
    link?: string;
    additional_data?: any;
    start_date: string;
    end_date: string;
    status: string;
    locations: string[];
    image?: any;
    id?: string;
    category_id?: string;
}

export interface IAdvertisement {
    id: string;
    name: string;
    description: string | null;
    images: Array<{
        id: string;
        src: string | null;
        alt: string;
        title: string;
        description: string;
        image_type: string;
        collection_name: string;
    }>;
    link?: string;
    subject_id: string;
    subject_type: "companyProfile" | "industryEvent" | "pitchEvent";
    subject: {
        id: string;
        type: string;
        name: string;
        description: string | null;
        handle: string;
        friendly_url: string;
        avatar: string | null;
        is_private: boolean;
        created_at: string | null;
        updated_at: string | null;
        subject?: any;
    };
    advertisement_card_type_id: string;
    advertisement_card_type: {
        id: string;
        label: string;
        description: string | null;
        created_at: string;
        updated_at: string | null;
        key: string;
    };
    locations: Array<{
        id: string;
        label: string;
        description: string | null;
        created_at: string;
        updated_at: string | null;
    }>;
    status: "active" | "draft";
    created_at: string;
    updated_at: string | null;
    start_date: string;
    end_date: string;
}

export interface INaviStackAdvertisement {
    id: string;
    name: string;
    description: string | null;
    link: string | null;
    images: any[];
    subject_id: string;
    subject_type: string;
    subject: {
        id: string;
        type: "company";
        name: string;
        description: string | null;
        handle: string;
        friendly_url: string;
        avatar: string | null;
        is_private: false;
        subscription: {
            value: string;
            label: string;
        };
        rating?: number | null;
        subdomain?: string | null;
        type_is_of_vendor?: boolean | null;
        is_distributor?: boolean | null;
        manage_clients?: boolean | null;
    };
    advertisement_card_type_id: string;
    additional_data: any;
    advertisement_card_type: {
        id: string;
        label: string;
        key: string;
        description: string | null;
        created_at: string;
        updated_at: string | null;
    };
    locations: [
        {
            id: string;
            label: string;
            key: string;
            description: string | null;
            created_at: string;
            updated_at: string | null;
        },
    ];
    start_date: string;
    end_date: string;
    status: "active" | "draft";
    created_at: string;
    updated_at: string;
    category_id: string;
    category: ICategory;
}

export interface CardTypesResponse {
    id: string;
    key: string;
    label: string;
    description: string | null;
    created_at: string;
    updated_at: string;
}
