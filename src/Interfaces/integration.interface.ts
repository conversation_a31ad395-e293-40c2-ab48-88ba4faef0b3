export interface IIntegration {
    id: string
    key: string
    title: string
    image_url: string
    header: string
    left_column: string
    mapping: IMapping[]
}

export interface IMapping {
    id: string
    integration_id: number
    parent_id: number|null
    key: string
    name: string
    dropdown_list: string|null
    children: IChildren[]
    selection:string|null
    open:boolean|null
    is_custom:boolean|null
    append_key:string|null
    dropdown_selection: string|null
    type: string|null
    value: string|null
}

export interface IChildren {
    id: string
    integration_id: number
    parent_id: number|null
    key: string
    name: string
    dropdown_list: string|null
    children: any[]
    selection:string|null
    open:boolean|null
    is_custom:boolean|null
    append_key:string|null
    dropdown_selection: string|null
    type: string|null
    value: string|null
}
