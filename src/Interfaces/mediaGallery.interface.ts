import {IAuthor} from "./contentFeed.interface";
import {IParent} from "./profiles.interface";
import {ITag} from "./tags.interface";

export interface StoreGalleryBody {
    images: any[];
    tags: string[];
    owner_type: string;
    owner_id: any;
    title: string;
    description: string;
}

export interface EditPropertiesBody {
    title?: string;
    description?: string;
    tags?: string[];
    replace_tags?: boolean;
    media_visibility?: string;
    is_partner_content?: any;
}

export interface IMediaItem {
    collection_name: string;
    created_at: string;
    custom_properties: any;
    description: string;
    file_name: string;
    id: string;
    mime_type: string;
    name: string;
    size: number;
    title: string;
    updated_at: string;
    url: string;
}

export interface IMediaGallery {
    custom_properties: {
        author_id: string;
        author_name: string;
        description: string;
        tags: string[] | null;
        title: string;
        media_visibility?: string;
        is_partner_content?: boolean;
    };
    id: string;
    media: IMediaItem[];
    updated_at: string;
    created_at: string;
    author?: IAuthor;
    parent?: IParent;
    likes_count?: number;
    user_has_liked?: boolean;
    tags?: ITag[];
}
