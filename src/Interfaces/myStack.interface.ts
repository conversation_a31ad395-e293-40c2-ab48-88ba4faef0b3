import {ICategory} from "./categories.interface";
import {IImage} from "./images.interface";
import {IProductListing} from "./products.interface";
import {IStatusScope} from "./statusScope.interface";

export interface IVendorStack {
    id: string;
    name: string;
    handle: string;
    avatar: IImage | null;
    friendly_url?: string;
}

export interface IStackCategorization {
    category: ICategory;
    category_id: string;
    created: string;
    id: string;
    partner_status: string;
    product: IProductListing;
    product_id: string;
    stack_company: IStackCompany;
    stack_company_id: string;
    updated: string;
    prm_partner_status: string | null;
    parent_category_id?: string;
    client_usage?: {installed: number; total: number};
}

export interface IStackCompany {
    avatar: string | null;
    description: string | null;
    friendly_url: string;
    handle: string;
    id: string;
    name: string;
    partner_flag: boolean;
    product_reviews_count: number | null;
    subcategories: any[];
    subdomain: string;
    rating?: number | string;
    is_distributor: boolean;
    is_mdf: boolean;
    status_scope?: IStatusScope;
}

export interface IMissingAdoptionStackItem {
    sub_category: ICategory;
    products: IProductListing[];
}

export interface IMissingAdoptionStackRes {
    missing_categories_count: number;
    missing_products_count: number;
    items: IMissingAdoptionStackItem[];
}
