import {ICategory} from "./categories.interface";

export interface IPlaidInstitution {
    institution_id: string;
    name: string;
    logo: string | null;
}

export interface IPlaidAccount {
    id: string;
    mask: string;
    name: string;
    type: "depository" | "credit" | "investment" | "loan"; // Enum of possible account types
    last_sync: string;
    is_syncing: boolean;
}

export interface ILinkedAccountData {
    display_name: string | null;
    id: string;
    institution_logo: string | null;
    is_syncing: boolean;
    last_sync: string;
    mask: string;
    name: string;
    type: string;
}

export interface IExpensesCountResponse {
    subscriptions: number;
    transactions: number;
    bank_accounts: number;
    bank_links: number;
}

export interface IExpenseSummaryItem {
    amount: string;
    percentage_diff: {increased: boolean; change_percentage: string; previous_amount: string};
    subscriptions: number;
    transactions?: number;
    transaction?: number;
}

export interface IExpenseSummaryResponse {
    all: IExpenseSummaryItem;
    stack: IExpenseSummaryItem;
    subscriptions: Omit<IExpenseSummaryItem, "subscriptions">;
    contracts: {
        amount: string;
        percentage_diff: {increased: boolean; change_percentage: string; previous_amount: string};
        count: number;
    };
}

export interface IExpenseSubscription {
    id: string;
    description: string;
    frequency: "UNKNOWN" | "WEEKLY" | "BIWEEKLY" | "SEMI_MONTHLY" | "MONTHLY" | "ANNUALLY";
    annual_cost: number | null;
    ignore: boolean;
    last_date: string;
    notes: string | null;
    amount: string;
    sub_category_id: string;
    category: {
        id: string;
        name: string;
        color: string;
    };
    product: {
        id: string;
        name: string;
    };
    account: {
        account_id: string;
        mask: string;
        name: string;
        type: string;
        institution_logo: string;
    };
    vendor: {
        id: string;
        avatar: string | null;
        name: string;
        type: string;
        cancellation_link: string | null;
    };
    contract: {
        id: string;
        name: string;
    };
    is_stack_linked?: boolean;
    plaid_category: {
        id: string;
        color: string;
        name: string;
    };
}

export interface IExpenseBreakdownItem {
    amount: string;
    color: string;
    id: string | null;
    name: string;
    percentage: number;
    avatar?: string | null;
    sub_categories?: Array<{
        amount: string;
        id: string | null;
        name: string;
        percentage: number;
        previous_info: {
            change_percentage: string;
            increased: boolean;
        };
    }>;
    parent?: {id: string; name: string; color: string};
}

export interface IExpenseAccount {
    account_id: string;
    institution_logo: string | null;
    mask: string;
    name: string;
    type: string;
}

export interface ITransactionItem {
    account: IExpenseAccount | null;
    amount: string;
    category: ICategory | null;
    contract: any | null;
    date: string;
    description: string | null;
    id: string;
    notes: string | null;
    product: any | null;
    vendor: any | null;
    sub_category_id: string | null;
    ignore?: boolean;
    last_date: string | null;
    frequency?: string;
    is_stack_linked?: boolean;
    plaid_category: {
        id: string;
        color: string;
        name: string;
    };
    plaid_subscription_id?: string | null;
    subscription?: {
        frequency: string;
        last_date: string;
        notes?: string | null;
    };
}

export interface IPriorTransactionItem {
    id: string;
    date: string;
    amount: string;
}

export interface ISubscriptionsCostResponse {
    month: {
        current: string;
        previous: string;
    };
    year: {
        current: string;
        previous: string;
    };
}

export interface ExpenseItem {
    label: string;
    value: number;
}

export interface AllExpensesBarChartResponse {
    unmapped_expenses: ExpenseItem[];
    stack_expenses: ExpenseItem[];
    subscription_expenses: ExpenseItem[];
}

export interface IAllExpensesBreakdownItem {
    name: string;
    color: string;
    id: string;
    amount: string;
    percentage: string;
    percentage_diff: {
        increased: boolean;
        change_percentage: string;
    };
    not_linked_categories: {
        id: string;
        name: string;
        products_count: number;
    }[];
    sub_categories: {
        name: string;
        id: string;
        amount: string;
        percentage: string;
        percentage_diff: {
            increased: boolean;
            change_percentage: string;
        };
    }[];
}

export interface IGetOverviewBreakdownBody {
    start_date: string;
    end_date: string;
    filter: "all" | "stack" | "subscription";
    sort?: string;
    order_by?: string;
}

export interface ICompanyExpenseSearchItem {
    id: string;
    amount: string;
    description: string | null;
    logo_url: string | null;
    merchant_name: string | null;
    product: {
        id: string;
        name: string;
    };
    vendor: {
        id: string;
        avatar: string;
        name: string;
        type: string;
    };
    type?: string;
}

export interface INotLinkedExpenseItem {
    product: {
        id: string;
        name: string;
    };
    vendor: {
        id: string;
        avatar: string | null;
        name: string;
        type: string;
    };
    contract: {
        id: string;
        name: string;
    };
    row_id?: string;
}

export interface ISuggestedExpenseItem {
    id: string;
    date: string;
    type: "transaction";
    amount: string;
    account: {
        account_id: string;
        mask: string;
        name: string;
        type: string;
        institution_logo: string;
    };
    vendor: {id: string; name: string} | null;
    merchant_name: string;
    logo_url: string;
    description: string;
}

//? Alerts

export interface IPlaidAlertNotificationOption {
    id: string;
    lookup_option_id: string;
    name: string;
    order: number;
    show_answer_option: boolean;
    value_key: string;
}

export interface IPlaidAlertPeriodNotificationOption {
    id: string;
    lookup_option_id: string;
    name: string;
    order: number;
    show_answer_option: boolean;
    value_key: string;
}

export interface IAlertSettingsResponse {
    alerts: {
        type: {
            id: string;
            name: string;
            value_key: string;
        };
        period: {
            id: string;
            name: string;
            value_key: string;
        };
        active: boolean;
    }[];
    recipients: {
        role_id: {
            id: string;
            key: string;
            title: string;
            display_name: string;
        };
        emails: string[];
    };
}

export interface ISaveAlertSettingsPayload {
    alerts: Array<{type_id: string; period_id: string; active: boolean}>;
    recipients: {
        role_id: string;
        emails: string[];
    };
}

export interface IPlaidAlertNotification {
    subscription: {
        id: string;
        merchant_name: string;
        description: string;
        vendor: {
            id: string;
            avatar: string;
            name: string;
            type: string;
        };
        product: {
            id: string;
            name: string;
        };
    };
    type: {
        id: string;
        name: string;
        value_key: string;
    };
    period: {
        id: string;
        name: string;
        value_key: string;
    };
    extra_info: {
        previous_total: string;
        current_total: string;
        percentage_diff: {
            increased: boolean;
            change_percentage: string;
        };
        previous_date_range: {
            start: string;
            end: string;
        };
        current_date_range: {
            start: string;
            end: string;
        };
        //? Upcoming
        count?: number;
        total_amount?: number;
        upcoming_date?: string;
        amount?: string;
        stable_since?: string;
        last_billed_date?: string;
    };
    created_at: string;
}

export interface IPlaidBarChartItem {
    label: string;
    value: number;
}

export interface IUpcomingExpensesSummary {
    count: number;
    cost: string;
    upcoming_date: string;
    percentage_diff: {
        increased: boolean;
        change_percentage: string;
    };
}
