import {ICategory} from "./categories.interface";
import {IAuthor} from "./contentFeed.interface";
import {IImage} from "./images.interface";
import {ILinks, IMeta} from "./pagination.interface";
import {IParent, IVideos} from "./profiles.interface";
import {IStatusScope} from "./statusScope.interface";

export interface IProducts {
    id: string;
    company_id: string;
    company?: IAuthor;
    name: string;
    overview: string;
    description: string;
    url: string;
    features: IProductFeature[];
    pricing: IProductPricing[];
    friendly_url: string;
    categories?: ICategory[];
    images?: IImage[];
    videos?: IVideos[];
    categoriesSelected?: string[];
    total_reviews?: number;
    likes_count?: number;
    user_has_liked?: boolean;
    parent?: IParent;
    rating?: number | string;
    author?: IAuthor;
    created_at?: string;
}

export interface IProductFeature {
    title: string;
    description?: string;
    display_on_feed?: boolean;
}

export interface IProductPricing {
    title: string;
    description?: string;
    price: number;
    pricing_type: string;
}
export interface IReply {
    id: string;
    review_id: string;
    message: string;
    created_at: string;
}

export interface IReview {
    id: string;
    title: string;
    author: IAuthor;
    product_user_verified: boolean;
    rating: number;
    recommendation: number;
    created_at: string;
    likes_count: number;
    user_has_liked: boolean;
    answers: IReviewAnswer[];
    reviewer?: IParent;
    hide_reviewer_name?: boolean;
    recommended_by_reviewer_value?: number;
    replies?: IReply[];
    model?: IReviewModel;
    reviewer_update_review_at?: string;
    satisfaction_rating?: string;
}

export interface PaginatedReviews {
    data: IReview[];
    links: ILinks;
    meta: IMeta;
}

export interface IReviewQuestion {
    archived_at: string | null;
    id: string;
    impacts_rating: boolean;
    is_about_you_question: boolean;
    is_required: boolean;
    is_archived: boolean;
    model_type: string;
    order: number;
    question: string;
    question_type: string;
    question_key: string;
}

export interface IReviewQuestionOption {
    archived_at: string | null;
    display_value: string | any;
    id: string;
    impact_rating_value: number;
    is_archived: boolean;
    key: string;
    order: number;
    review_question_id: string;
    show_answer_option?: boolean;
    show_answer_option_label?: string;
    validation?: any;
}

export interface IReviewAnswer {
    id: string;
    question: string;
    answer: string;
    review_id: string;
    review_question: IReviewQuestion;
    review_question_id: string;
    review_question_option: IReviewQuestionOption;
    review_question_option_id: string;
}

export interface IReviewModel {
    friendly_url: string;
    id: string;
    name: string;
    type: string;
    company?: IReviewModelCompany;
}

export interface IReviewModelCompany {
    id: string;
    name: string;
    description: string;
    handle: string;
    friendly_url: string;
    avatar: string | null;
    subcategories: ICategory[];
}

export interface PaginatedProducts {
    data: IProducts[];
    links: ILinks;
    meta: IMeta;
}

export interface IProductListing {
    categories: ICategory[];
    company: IAuthor;
    company_id: string;
    description: string;
    friendly_url: string;
    id: string;
    name: string;
    rating?: string;
    recommendation_rating?: number;
    satisfaction_rating?: string;
    total_reviews: number;
    status_scope?: IStatusScope;
}
