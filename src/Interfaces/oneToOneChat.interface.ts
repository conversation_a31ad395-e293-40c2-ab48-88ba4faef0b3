import type {ICompanyType} from "./company.interface";
import {ITypesProfile} from "./people.interface";

export interface ICompanyPartner {
    id: string;
    name: string;
    handle: string;
    friendly_url: string;
    subdomain: string | null;
    avatar: string | null;
    users?: ICompanyPartnerUser[];
    claimers?: ICompanyPartnerUser[];
    contacts_ids?: string[];
    contacts_count?: number;
    partner?: {
        accepted_at: string;
        email: string;
        followed_partner_id: string;
        follower_partner_id: string;
        id: string;
        initiator: string;
        invited_at: string;
        rejected_at: string | null;
        rejected_reason: string | null;
        status: string;
        accepted_reason: string | null;
        accepted_reason_name: string | null;
        accepted_reason_other: string | null;
        is_in_contact_list?: boolean;
    };
    partner_engagement?: string;
    partner_flag?: boolean;
    type: ICompanyType;
    profile_type?: ITypesProfile;
    is_distributor?: boolean;
}

export interface IChannelProgramPartnerObj {
    id: any;
    name: string;
    friendly_url: any;
    subdomain: any;
    handle: string;
    avatar: string;
    claimers: ICompanyPartnerUser[];
}

export interface ICompanyPartnerUser {
    id: string;
    type: "user";
    name: string;
    description: string | null;
    handle: string;
    friendly_url: string;
    avatar: string | null;
    is_private: boolean;
    newMessages?: boolean;
    is_hidden?: boolean;
    company?: {
        id: string;
        name: string;
        handle?: string;
        description?: string | null;
        friendly_url?: string;
        avatar?: string | null;
    };
}

export interface IOneToOneUserListItem {
    id: string;
    name: string;
    handle: string;
    avatar: string | null;
    friendly_url: string;
    profile_link: string;
    user_profile_type: string | null;
    newMessages?: boolean;
    is_hidden?: boolean;
    company?: {
        id: string;
        name: string;
        handle?: string;
        description?: string | null;
        friendly_url?: string;
        avatar?: string | null;
    };
}

export interface IOneToOneChat {
    id: string;
    message_type: "oneToOneChat";
    message: string;
    date: string | null;
    creator_id: string;
    creator_company?: any;
    created_by: {
        id: string;
        name: string;
        description: string;
        handle: string;
        friendly_url: string;
        avatar: null | string;
        is_private: boolean;
        company: string | null;
    };
    receiver_id: string;
    received_by?: {
        id: string;
        name: string;
        description: string;
        handle: string;
        friendly_url: string;
        avatar: string | null;
        is_private: boolean;
        company: string | null;
    };
    receiver_company?: any;
    pusher_channel?: string;
    created_at: string | null;
    updated_at?: string | null;
}

export interface IOneToOnePusherChat {
    created_at: string;
    creator_first_name: string;
    creator_friendly_url: string;
    creator_last_name: string;
    creator_user_id: string;
    creator_company_id?: string;
    creator_company_name?: string;
    message: string;
    message_id: string;
    receiver_company_friendly_url?: string;
    receiver_company_id?: string;
    receiver_company_name?: string;
}

export interface ISendOnetoOneChatRes {
    id: string;
    message_type: "oneToOneChat";
    message: string;
    date: string | null;
    creator_id: string;
    receiver_id: string;
    pusher_channel: "private-cp-pusher.globalPrivate";
    created_at: string | null;
    updated_at: string | null;
}
