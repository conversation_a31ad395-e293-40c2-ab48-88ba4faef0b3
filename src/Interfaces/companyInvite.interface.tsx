import {IAuth} from "../contexts/auth.context";
import {ICompanySimple} from "./company.interface";
import {IUserProfile} from "./people.interface";

export interface IMSPClientLoginForm {
    title: string;
}

export interface ICompanyInvite {
    id: string;
    parent_company_id: string;
    parent_company: ICompanySimple;
    child_company_id: string;
    child_company: ICompanySimple;
    email: string;
    invited_user: IAuth;
    user_invite: boolean;
    activated: boolean;
    activated_at: string;
    type: string;
    author_id: string | null;
    author: IUserProfile;
    affiliate_brand_id: string | null;
    created_at: string;
    updated_at: string;
}
