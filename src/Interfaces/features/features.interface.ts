export const ADMIN_DASHBOARD_PERMISSION_GROUPS = {
    //? Admin tasks
    ADMIN_FLAGGED_COMMENTS_READ: "ADMIN_FLAGGED_COMMENTS_READ",
    ADMIN_FLAGGED_COMMENTS_UPDATE: "ADMIN_FLAGGED_COMMENTS_UPDATE",
    ADMIN_MSP_RESPONSES_READ: "ADMIN_MSP_RESPONSES_READ",
    ADMIN_MSP_RESPONSES_UPDATE: "ADMIN_MSP_RESPONSES_UPDATE",
    ADMIN_PRODUCT_REVIEWS_READ: "ADMIN_PRODUCT_REVIEWS_READ",
    ADMIN_PRODUCT_REVIEWS_UPDATE: "ADMIN_PRODUCT_REVIEWS_UPDATE",
    ADMIN_MOVE_PRODUCT_REVIEWS_READ: "ADMIN_MOVE_PRODUCT_REVIEWS_READ",
    ADMIN_MOVE_PRODUCT_REVIEWS_UPDATE: "ADMIN_MOVE_PRODUCT_REVIEWS_UPDATE",

    //? Channel Engage
    ADMIN_ENGAGE_MNGMT_READ: "ADMIN_ENGAGE_MNGMT_READ",
    ADMIN_ENGAGE_MNGMT_UPDATE: "ADMIN_ENGAGE_MNGMT_UPDATE",
    ADMIN_POLL_MNGMT_READ: "ADMIN_POLL_MNGMT_READ",
    ADMIN_POLL_MNGMT_UPDATE: "ADMIN_POLL_MNGMT_UPDATE",

    //? System
    ADMIN_CATEGORIES_TAGS_READ: "ADMIN_CATEGORIES_TAGS_READ",
    ADMIN_CATEGORIES_TAGS_UPDATE: "ADMIN_CATEGORIES_TAGS_UPDATE",
    ADMIN_INDUSTRY_CALENDAR_READ: "ADMIN_INDUSTRY_CALENDAR_READ",
    ADMIN_INDUSTRY_CALENDAR_UPDATE: "ADMIN_INDUSTRY_CALENDAR_UPDATE",
    ADMIN_PROFILE_ENRICHMENT_READ: "ADMIN_PROFILE_ENRICHMENT_READ",
    ADMIN_PROFILE_ENRICHMENT_UPDATE: "ADMIN_PROFILE_ENRICHMENT_UPDATE",
    ADMIN_CHANNEL_CHARTS_READ: "ADMIN_CHANNEL_CHARTS_READ",
    ADMIN_CHANNEL_CHARTS_UPDATE: "ADMIN_CHANNEL_CHARTS_UPDATE",
    ADMIN_JOB_TITLE_READ: "ADMIN_JOB_TITLE_READ",
    ADMIN_JOB_TITLE_UPDATE: "ADMIN_JOB_TITLE_UPDATE",
    ADMIN_AD_MNGMT_READ: "ADMIN_AD_MNGMT_READ",
    ADMIN_AD_MNGMT_UPDATE: "ADMIN_AD_MNGMT_UPDATE",
    ADMIN_EMAIL_WHITELIST_READ: "ADMIN_EMAIL_WHITELIST_READ",
    ADMIN_EMAIL_WHITELIST_UPDATE: "ADMIN_EMAIL_WHITELIST_UPDATE",
    ADMIN_TANGO_SETTINGS_READ: "ADMIN_TANGO_SETTINGS_READ",
    ADMIN_TANGO_SETTINGS_UPDATE: "ADMIN_TANGO_SETTINGS_UPDATE",

    //? Data
    ADMIN_USER_MNGMT_READ: "ADMIN_USER_MNGMT_READ",
    ADMIN_USER_MNGMT_UPDATE: "ADMIN_USER_MNGMT_UPDATE",
    ADMIN_2FA_MNGMT_READ: "ADMIN_2FA_MNGMT_READ",
    ADMIN_2FA_MNGMT_UPDATE: "ADMIN_2FA_MNGMT_UPDATE",
    ADMIN_USER_COMPANY_REQUESTS_UPDATE: "ADMIN_USER_COMPANY_REQUESTS_UPDATE",
    ADMIN_COMPANY_MNGMT_READ: "ADMIN_COMPANY_MNGMT_READ",
    ADMIN_COMPANY_MNGMT_UPDATE: "ADMIN_COMPANY_MNGMT_UPDATE",
    ADMIN_REMOVE_USERS_REPORTS_READ: "ADMIN_REMOVE_USERS_REPORTS_READ",
    ADMIN_REMOVE_USERS_REPORTS_UPDATE: "ADMIN_REMOVE_USERS_REPORTS_UPDATE",
    ADMIN_ROLE_MNGMT_READ: "ADMIN_ROLE_MNGMT_READ",
    ADMIN_ROLE_MNGMT_UPDATE: "ADMIN_ROLE_MNGMT_UPDATE",

    //? Reports
    ADMIN_REPORTS_READ: "ADMIN_REPORTS_READ",
    ADMIN_REPORTS_UPDATE: "ADMIN_REPORTS_UPDATE",
    ADMIN_EVENT_REPORTS_READ: "ADMIN_EVENT_REPORTS_READ",
    ADMIN_EVENT_REPORTS_UPDATE: "ADMIN_EVENT_REPORTS_UPDATE",
    ADMIN_EVENT_CHARTS_READ: "ADMIN_EVENT_CHARTS_READ",
    ADMIN_EVENT_CHARTS_UPDATE: "ADMIN_EVENT_CHARTS_UPDATE",
    ADMIN_USER_REPORTS_READ: "ADMIN_USER_REPORTS_READ",
    ADMIN_USER_REPORTS_UPDATE: "ADMIN_USER_REPORTS_UPDATE",
    ADMIN_ALL_REVIEWS_READ: "ADMIN_ALL_REVIEWS_READ",
    ADMIN_ALL_REVIEWS_UPDATE: "ADMIN_ALL_REVIEWS_UPDATE",
    ADMIN_REVIEWS_BY_VENDOR_OR_PRODUCT_READ: "ADMIN_REVIEWS_BY_VENDOR_OR_PRODUCT_READ",
    ADMIN_REVIEWS_BY_VENDOR_OR_PRODUCT_UPDATE: "ADMIN_REVIEWS_BY_VENDOR_OR_PRODUCT_UPDATE",
    ADMIN_CUSTOM_REPORTS_READ: "ADMIN_CUSTOM_REPORTS_READ",
    ADMIN_CUSTOM_REPORTS_UPDATE: "ADMIN_CUSTOM_REPORTS_UPDATE",

    //? Analytics
    ADMIN_VIDEO_ANALYTICS_READ: "ADMIN_VIDEO_ANALYTICS_READ",
    ADMIN_VIDEO_ANALYTICS_UPDATE: "ADMIN_VIDEO_ANALYTICS_UPDATE",

    //? Communication
    ADMIN_SEND_BROADCAST_READ: "ADMIN_SEND_BROADCAST_READ",
    ADMIN_SEND_BROADCAST_UPDATE: "ADMIN_SEND_BROADCAST_UPDATE",
    ADMIN_IN_APP_EMAILS_READ: "ADMIN_IN_APP_EMAILS_READ",
    ADMIN_IN_APP_EMAILS_UPDATE: "ADMIN_IN_APP_EMAILS_UPDATE",

    //? System Configuration
    ADMIN_APP_CONFIGS_READ: "ADMIN_APP_CONFIGS_READ",
    ADMIN_APP_CONFIGS_UPDATE: "ADMIN_APP_CONFIGS_UPDATE",
    ADMIN_APP_JOBS_READ: "ADMIN_APP_JOBS_READ",
    ADMIN_APP_JOBS_UPDATE: "ADMIN_APP_JOBS_UPDATE",
    ADMIN_ACTIVITY_LOGS_READ: "ADMIN_ACTIVITY_LOGS_READ",
    ADMIN_ACTIVITY_LOGS_UPDATE: "ADMIN_ACTIVITY_LOGS_UPDATE",
    ADMIN_FEATURE_FLAGS_READ: "ADMIN_FEATURE_FLAGS_READ",
    ADMIN_FEATURE_FLAGS_UPDATE: "ADMIN_FEATURE_FLAGS_UPDATE",

    //? Other
    ADMIN_MANAGE_CHANNEL_DEALS_READ: "ADMIN_MANAGE_CHANNEL_DEALS_READ",
    ADMIN_MANAGE_CHANNEL_DEALS_UPDATE: "ADMIN_MANAGE_CHANNEL_DEALS_UPDATE",
    ADMIN_AFFILIATE_BRANDS_MNGMT_READ: "ADMIN_AFFILIATE_BRANDS_MNGMT_READ",
    ADMIN_AFFILIATE_BRANDS_MNGMT_UPDATE: "ADMIN_AFFILIATE_BRANDS_MNGMT_UPDATE",
    ADMIN_USER_REQUESTS_MNGMT_READ: "ADMIN_USER_REQUESTS_MNGMT_READ",
    ADMIN_USER_REQUESTS_MNGMT_UPDATE: "ADMIN_USER_REQUESTS_MNGMT_UPDATE",
    ADMIN_HUBSPOT_MNGMT_READ: "ADMIN_HUBSPOT_MNGMT_READ",
    ADMIN_HUBSPOT_MNGMT_UPDATE: "ADMIN_HUBSPOT_MNGMT_UPDATE",
    ADMIN_CYCLR_MNGMT_READ: "ADMIN_CYCLR_MNGMT_READ",
    ADMIN_CYCLR_MNGMT_UPDATE: "ADMIN_CYCLR_MNGMT_UPDATE",
} as const;

export type TAdminDashboardPermissionGroupsKey = keyof typeof ADMIN_DASHBOARD_PERMISSION_GROUPS;
export type TAdminDashboardPermissionGroupsValue =
    (typeof ADMIN_DASHBOARD_PERMISSION_GROUPS)[TAdminDashboardPermissionGroupsKey];

export const ADMIN_VENDOR_PERMISSION_GROUPS = {
    ADMIN_VENDOR_ANNOUNCEMENTS_UPDATE: "ADMIN_VENDOR_ANNOUNCEMENTS_UPDATE",
    ADMIN_VENDOR_ANNOUNCEMENTS_READ: "ADMIN_VENDOR_ANNOUNCEMENTS_READ",
    ADMIN_VENDOR_CHANNEL_COMMAND_UPDATE: "ADMIN_VENDOR_CHANNEL_COMMAND_UPDATE",
    ADMIN_VENDOR_CHANNEL_COMMAND_READ: "ADMIN_VENDOR_CHANNEL_COMMAND_READ",
    ADMIN_VENDOR_CHANNEL_DEALS_UPDATE: "ADMIN_VENDOR_CHANNEL_DEALS_UPDATE",
    ADMIN_VENDOR_CHANNEL_DEALS_READ: "ADMIN_VENDOR_CHANNEL_DEALS_READ",
    ADMIN_VENDOR_COMPANY_PROFILE_UPDATE: "ADMIN_VENDOR_COMPANY_PROFILE_UPDATE",
    ADMIN_VENDOR_COMPANY_PROFILE_READ: "ADMIN_VENDOR_COMPANY_PROFILE_READ",
    ADMIN_VENDOR_MANAGE_CONNECTIONS_UPDATE: "ADMIN_VENDOR_MANAGE_CONNECTIONS_UPDATE",
    ADMIN_VENDOR_MANAGE_CONNECTIONS_READ: "ADMIN_VENDOR_MANAGE_CONNECTIONS_READ",
    ADMIN_VENDOR_DATA_EXPORT_UPDATE: "ADMIN_VENDOR_DATA_EXPORT_UPDATE",
    ADMIN_VENDOR_DATA_EXPORT_READ: "ADMIN_VENDOR_DATA_EXPORT_READ",
    ADMIN_VENDOR_DEAL_REGISTRATION_UPDATE: "ADMIN_VENDOR_DEAL_REGISTRATION_UPDATE",
    ADMIN_VENDOR_DEAL_REGISTRATION_READ: "ADMIN_VENDOR_DEAL_REGISTRATION_READ",
    ADMIN_VENDOR_DOCUMENT_REBRAND_UPDATE: "ADMIN_VENDOR_DOCUMENT_REBRAND_UPDATE",
    ADMIN_VENDOR_DOCUMENT_REBRAND_READ: "ADMIN_VENDOR_DOCUMENT_REBRAND_READ",
    ADMIN_VENDOR_EVENTS_CALENDAR_UPDATE: "ADMIN_VENDOR_EVENTS_CALENDAR_UPDATE",
    ADMIN_VENDOR_EVENTS_CALENDAR_READ: "ADMIN_VENDOR_EVENTS_CALENDAR_READ",
    ADMIN_VENDOR_INTEGRATIONS_UPDATE: "ADMIN_VENDOR_INTEGRATIONS_UPDATE",
    ADMIN_VENDOR_INTEGRATIONS_READ: "ADMIN_VENDOR_INTEGRATIONS_READ",
    ADMIN_VENDOR_MANAGE_CONTENT_UPDATE: "ADMIN_VENDOR_MANAGE_CONTENT_UPDATE",
    ADMIN_VENDOR_MANAGE_CONTENT_READ: "ADMIN_VENDOR_MANAGE_CONTENT_READ",
    ADMIN_VENDOR_MASS_MESSAGING_UPDATE: "ADMIN_VENDOR_MASS_MESSAGING_UPDATE",
    ADMIN_VENDOR_MASS_MESSAGING_READ: "ADMIN_VENDOR_MASS_MESSAGING_READ",
    ADMIN_VENDOR_PRODUCT_REVIEWS_UPDATE: "ADMIN_VENDOR_PRODUCT_REVIEWS_UPDATE",
    ADMIN_VENDOR_PRODUCT_REVIEWS_READ: "ADMIN_VENDOR_PRODUCT_REVIEWS_READ",
    ADMIN_VENDOR_MANAGE_REPORTS_UPDATE: "ADMIN_VENDOR_MANAGE_REPORTS_UPDATE",
    ADMIN_VENDOR_MANAGE_REPORTS_READ: "ADMIN_VENDOR_MANAGE_REPORTS_READ",
    ADMIN_VENDOR_ROLE_MANAGEMENT_UPDATE: "ADMIN_VENDOR_ROLE_MANAGEMENT_UPDATE",
    ADMIN_VENDOR_ROLE_MANAGEMENT_READ: "ADMIN_VENDOR_ROLE_MANAGEMENT_READ",
    ADMIN_VENDOR_USER_MANAGEMENT_UPDATE: "ADMIN_VENDOR_USER_MANAGEMENT_UPDATE",
    ADMIN_VENDOR_USER_MANAGEMENT_READ: "ADMIN_VENDOR_USER_MANAGEMENT_READ",
    ADMIN_VENDOR_PRODUCTS_UPDATE: "ADMIN_VENDOR_PRODUCTS_UPDATE",
    ADMIN_VENDOR_PRODUCTS_READ: "ADMIN_VENDOR_PRODUCTS_READ",
} as const;

export type TAdminVendorPermissionGroupsKey = keyof typeof ADMIN_VENDOR_PERMISSION_GROUPS;
export type TAdminVendorPermissionGroupsValue =
    (typeof ADMIN_VENDOR_PERMISSION_GROUPS)[TAdminVendorPermissionGroupsKey];

export const ADMIN_MSP_PERMISSION_GROUPS = {
    ADMIN_MSP_AFFILIATES_UPDATE: "ADMIN_MSP_AFFILIATES_UPDATE",
    ADMIN_MSP_AFFILIATES_READ: "ADMIN_MSP_AFFILIATES_READ",
    ADMIN_MSP_BRAND_STACK_UPDATE: "ADMIN_MSP_BRAND_STACK_UPDATE",
    ADMIN_MSP_BRAND_STACK_READ: "ADMIN_MSP_BRAND_STACK_READ",
    ADMIN_MSP_CLIENTS_MGNMT_UPDATE: "ADMIN_MSP_CLIENTS_MGNMT_UPDATE",
    ADMIN_MSP_CLIENTS_MGNMT_READ: "ADMIN_MSP_CLIENTS_MGNMT_READ",
    ADMIN_MSP_COMPANY_PROFILE_UPDATE: "ADMIN_MSP_COMPANY_PROFILE_UPDATE",
    ADMIN_MSP_COMPANY_PROFILE_READ: "ADMIN_MSP_COMPANY_PROFILE_READ",
    ADMIN_MSP_MANAGE_CONTRACTS_UPDATE: "ADMIN_MSP_MANAGE_CONTRACTS_UPDATE",
    ADMIN_MSP_MANAGE_CONTRACTS_READ: "ADMIN_MSP_MANAGE_CONTRACTS_READ",
    ADMIN_MSP_DATA_EXPORT_UPDATE: "ADMIN_MSP_DATA_EXPORT_UPDATE",
    ADMIN_MSP_DATA_EXPORT_READ: "ADMIN_MSP_DATA_EXPORT_READ",
    ADMIN_MSP_DEAL_REGISTRATION_UPDATE: "ADMIN_MSP_DEAL_REGISTRATION_UPDATE",
    ADMIN_MSP_DEAL_REGISTRATION_READ: "ADMIN_MSP_DEAL_REGISTRATION_READ",
    ADMIN_MSP_DOCUMENT_REBRAND_UPDATE: "ADMIN_MSP_DOCUMENT_REBRAND_UPDATE",
    ADMIN_MSP_DOCUMENT_REBRAND_READ: "ADMIN_MSP_DOCUMENT_REBRAND_READ",
    ADMIN_MSP_INTEGRATIONS_UPDATE: "ADMIN_MSP_INTEGRATIONS_UPDATE",
    ADMIN_MSP_INTEGRATIONS_READ: "ADMIN_MSP_INTEGRATIONS_READ",
    ADMIN_MSP_MANAGE_REPORTS_UPDATE: "ADMIN_MSP_MANAGE_REPORTS_UPDATE",
    ADMIN_MSP_MANAGE_REPORTS_READ: "ADMIN_MSP_MANAGE_REPORTS_READ",
    ADMIN_MSP_ROLE_MANAGEMENT_UPDATE: "ADMIN_MSP_ROLE_MANAGEMENT_UPDATE",
    ADMIN_MSP_ROLE_MANAGEMENT_READ: "ADMIN_MSP_ROLE_MANAGEMENT_READ",
    ADMIN_MSP_SAVED_DOCUMENTS_UPDATE: "ADMIN_MSP_SAVED_DOCUMENTS_UPDATE",
    ADMIN_MSP_SAVED_DOCUMENTS_READ: "ADMIN_MSP_SAVED_DOCUMENTS_READ",
    ADMIN_MSP_MANAGE_STACK_UPDATE: "ADMIN_MSP_MANAGE_STACK_UPDATE",
    ADMIN_MSP_MANAGE_STACK_READ: "ADMIN_MSP_MANAGE_STACK_READ",
    ADMIN_MSP_USER_MANAGEMENT_UPDATE: "ADMIN_MSP_USER_MANAGEMENT_UPDATE",
    ADMIN_MSP_USER_MANAGEMENT_READ: "ADMIN_MSP_USER_MANAGEMENT_READ",
    ADMIN_MSP_MANAGE_VENDORS_UPDATE: "ADMIN_MSP_MANAGE_VENDORS_UPDATE",
    ADMIN_MSP_MANAGE_VENDORS_READ: "ADMIN_MSP_MANAGE_VENDORS_READ",
} as const;

export type TAdminMSPPermissionGroupsKey = keyof typeof ADMIN_MSP_PERMISSION_GROUPS;
export type TAdminMSPPermissionGroupsValue = (typeof ADMIN_MSP_PERMISSION_GROUPS)[TAdminMSPPermissionGroupsKey];

export const ADMIN_INTERNAL_IT_PERMISSION_GROUPS = {
    ADMIN_INTIT_AFFILIATES_MNGMT_UPDATE: "ADMIN_INTIT_AFFILIATES_MNGMT_UPDATE",
    ADMIN_INTIT_AFFILIATES_MNGMT_READ: "ADMIN_INTIT_AFFILIATES_MNGMT_READ",
    ADMIN_INTIT_BRAND_STACK_UPDATE: "ADMIN_INTIT_BRAND_STACK_UPDATE",
    ADMIN_INTIT_BRAND_STACK_READ: "ADMIN_INTIT_BRAND_STACK_READ",
    ADMIN_INTIT_COMPANY_PROFILE_UPDATE: "ADMIN_INTIT_COMPANY_PROFILE_UPDATE",
    ADMIN_INTIT_COMPANY_PROFILE_READ: "ADMIN_INTIT_COMPANY_PROFILE_READ",
    ADMIN_INTIT_MANAGE_CONTRACTS_UPDATE: "ADMIN_INTIT_MANAGE_CONTRACTS_UPDATE",
    ADMIN_INTIT_MANAGE_CONTRACTS_READ: "ADMIN_INTIT_MANAGE_CONTRACTS_READ",
    ADMIN_INTIT_DATA_EXPORT_UPDATE: "ADMIN_INTIT_DATA_EXPORT_UPDATE",
    ADMIN_INTIT_DATA_EXPORT_READ: "ADMIN_INTIT_DATA_EXPORT_READ",
    ADMIN_INTIT_DEAL_REGISTRATION_UPDATE: "ADMIN_INTIT_DEAL_REGISTRATION_UPDATE",
    ADMIN_INTIT_DEAL_REGISTRATION_READ: "ADMIN_INTIT_DEAL_REGISTRATION_READ",
    ADMIN_INTIT_DOCUMENT_REBRAND_UPDATE: "ADMIN_INTIT_DOCUMENT_REBRAND_UPDATE",
    ADMIN_INTIT_DOCUMENT_REBRAND_READ: "ADMIN_INTIT_DOCUMENT_REBRAND_READ",
    ADMIN_INTIT_INTEGRATIONS_UPDATE: "ADMIN_INTIT_INTEGRATIONS_UPDATE",
    ADMIN_INTIT_INTEGRATIONS_READ: "ADMIN_INTIT_INTEGRATIONS_READ",
    ADMIN_INTIT_MANAGE_REPORTS_UPDATE: "ADMIN_INTIT_MANAGE_REPORTS_UPDATE",
    ADMIN_INTIT_MANAGE_REPORTS_READ: "ADMIN_INTIT_MANAGE_REPORTS_READ",
    ADMIN_INTIT_ROLE_MANAGEMENT_UPDATE: "ADMIN_INTIT_ROLE_MANAGEMENT_UPDATE",
    ADMIN_INTIT_ROLE_MANAGEMENT_READ: "ADMIN_INTIT_ROLE_MANAGEMENT_READ",
    ADMIN_INTIT_SAVED_DOCUMENTS_UPDATE: "ADMIN_INTIT_SAVED_DOCUMENTS_UPDATE",
    ADMIN_INTIT_SAVED_DOCUMENTS_READ: "ADMIN_INTIT_SAVED_DOCUMENTS_READ",
    ADMIN_INTIT_MANAGE_STACK_UPDATE: "ADMIN_INTIT_MANAGE_STACK_UPDATE",
    ADMIN_INTIT_MANAGE_STACK_READ: "ADMIN_INTIT_MANAGE_STACK_READ",
    ADMIN_INTIT_USER_MANAGEMENT_UPDATE: "ADMIN_INTIT_USER_MANAGEMENT_UPDATE",
    ADMIN_INTIT_USER_MANAGEMENT_READ: "ADMIN_INTIT_USER_MANAGEMENT_READ",
    ADMIN_INTIT_MANAGE_VENDORS_UPDATE: "ADMIN_INTIT_MANAGE_VENDORS_UPDATE",
    ADMIN_INTIT_MANAGE_VENDORS_READ: "ADMIN_INTIT_MANAGE_VENDORS_READ",
} as const;

export type TAdminInternalITPermissionGroupsKey = keyof typeof ADMIN_INTERNAL_IT_PERMISSION_GROUPS;
export type TAdminInternalITPermissionGroupsValue =
    (typeof ADMIN_INTERNAL_IT_PERMISSION_GROUPS)[TAdminInternalITPermissionGroupsKey];

const ADMIN_CUST_PERMISSION_GROUPS = {
    ADMIN_CUST_COMPANY_PROFILE_UPDATE: "ADMIN_CUST_COMPANY_PROFILE_UPDATE",
    ADMIN_CUST_COMPANY_PROFILE_READ: "ADMIN_CUST_COMPANY_PROFILE_READ",
    ADMIN_CUST_MANAGE_CONTRACTS_UPDATE: "ADMIN_CUST_MANAGE_CONTRACTS_UPDATE",
    ADMIN_CUST_MANAGE_CONTRACTS_READ: "ADMIN_CUST_MANAGE_CONTRACTS_READ",
    ADMIN_CUST_DATA_EXPORT_UPDATE: "ADMIN_CUST_DATA_EXPORT_UPDATE",
    ADMIN_CUST_DATA_EXPORT_READ: "ADMIN_CUST_DATA_EXPORT_READ",
    ADMIN_CUST_MANAGE_REPORTS_UPDATE: "ADMIN_CUST_MANAGE_REPORTS_UPDATE",
    ADMIN_CUST_MANAGE_REPORTS_READ: "ADMIN_CUST_MANAGE_REPORTS_READ",
    ADMIN_CUST_ROLE_MANAGEMENT_UPDATE: "ADMIN_CUST_ROLE_MANAGEMENT_UPDATE",
    ADMIN_CUST_ROLE_MANAGEMENT_READ: "ADMIN_CUST_ROLE_MANAGEMENT_READ",
    ADMIN_CUST_USER_MANAGEMENT_UPDATE: "ADMIN_CUST_USER_MANAGEMENT_UPDATE",
    ADMIN_CUST_USER_MANAGEMENT_READ: "ADMIN_CUST_USER_MANAGEMENT_READ",
} as const;

export type TAdminCustPermissionGroupsKey = keyof typeof ADMIN_CUST_PERMISSION_GROUPS;
export type TAdminCustPermissionGroupsValue = (typeof ADMIN_CUST_PERMISSION_GROUPS)[TAdminCustPermissionGroupsKey];

export const PERMISSION_GROUPS = {
    // SUPER ADMIN
    CP_SUPER_ADMIN: "CP_SUPER_ADMIN",
    // Admin Dashboard Permissions
    ...ADMIN_DASHBOARD_PERMISSION_GROUPS,
    // Admin Vendor Permissions
    ...ADMIN_VENDOR_PERMISSION_GROUPS,
    // Admin MSP Permissions
    ...ADMIN_MSP_PERMISSION_GROUPS,
    // Internal IT Permissions
    ...ADMIN_INTERNAL_IT_PERMISSION_GROUPS,
    // Customer Permissions
    ...ADMIN_CUST_PERMISSION_GROUPS,

    //MSP
    SUBSCRIPTION_MANAGEMENT_READ: "SUBSCRIPTION_MANAGEMENT_READ",
    SUBSCRIPTION_MANAGEMENT_UPDATE: "SUBSCRIPTION_MANAGEMENT_UPDATE",
    MANAGE_STACK_READ: "MANAGE_STACK_READ",
    MANAGE_STACK_UPDATE: "MANAGE_STACK_UPDATE",
    MANAGE_CONTRACTS_READ: "MANAGE_CONTRACTS_READ",
    MANAGE_CONTRACTS_UPDATE: "MANAGE_CONTRACTS_UPDATE",
    MANAGE_VENDORS_UPDATE: "MANAGE_VENDORS_UPDATE",
    MANAGE_VENDORS_READ: "MANAGE_VENDORS_READ",
    // PLAID
    PLAID_MGNMT_READ: "PLAID_MGNMT_READ",
    PLAID_MGNMT_UPDATE: "PLAID_MGNMT_UPDATE",
    //NEW
    SAVED_DOCUMENTS_READ: "SAVED_DOCUMENTS_READ",
    SAVED_DOCUMENTS_UPDATE: "SAVED_DOCUMENTS_UPDATE",
    PRODUCT_REVIEWS_UPDATE: "PRODUCT_REVIEWS_UPDATE",
    MSP_CLIENTS_MGNMT_UPDATE: "MSP_CLIENTS_MGNMT_UPDATE",
    MSP_CLIENTS_MGNMT_READ: "MSP_CLIENTS_MGNMT_READ",
    DOCUMENT_REBRAND_UPDATE: "DOCUMENT_REBRAND_UPDATE",
    DOCUMENT_REBRAND_READ: "DOCUMENT_REBRAND_READ",
    BRAND_STACK_READ: "BRAND_STACK_READ",
    BRAND_STACK_UPDATE: "BRAND_STACK_UPDATE",
    INTEGRATIONS_READ: "INTEGRATIONS_READ",
    INTEGRATIONS_UPDATE: "INTEGRATIONS_UPDATE",
    AFFILIATES_MNGMT_READ: "AFFILIATES_MNGMT_READ",
    AFFILIATES_MNGMT_UPDATE: "AFFILIATES_MNGMT_UPDATE",
    MANAGE_DEALS_READ: "MANAGE_DEALS_READ",
    MANAGE_DEALS_UPDATE: "MANAGE_DEALS_UPDATE",

    //VENDOR
    EVENTS_CALENDAR_READ: "EVENTS_CALENDAR_READ",
    EVENTS_CALENDAR_UPDATE: "EVENTS_CALENDAR_UPDATE",
    MANAGE_REPORTS_READ: "MANAGE_REPORTS_READ",
    MANAGE_REPORTS_UPDATE: "MANAGE_REPORTS_UPDATE",
    CHANNEL_COMMAND_READ: "CHANNEL_COMMAND_READ",
    CHANNEL_COMMAND_UPDATE: "CHANNEL_COMMAND_UPDATE",
    PRODUCTS_READ: "PRODUCTS_READ",
    PRODUCTS_UPDATE: "PRODUCTS_UPDATE",
    //NEW
    MASS_MESSAGING_READ: "MASS_MESSAGING_READ",
    MASS_MESSAGING_UPDATE: "MASS_MESSAGING_UPDATE",
    MANAGE_REVIEWS_READ: "MANAGE_REVIEWS_READ",
    MANAGE_REVIEWS_UPDATE: "MANAGE_REVIEWS_UPDATE",
    ANNOUNCEMENTS_READ: "ANNOUNCEMENTS_READ",
    ANNOUNCEMENTS_UPDATE: "ANNOUNCEMENTS_UPDATE",
    MANAGE_CHANNEL_DEALS_READ: "MANAGE_CHANNEL_DEALS_READ",
    MANAGE_CHANNEL_DEALS_UPDATE: "MANAGE_CHANNEL_DEALS_UPDATE",
    ADMIN_MANAGE_USER_CONTENT_READ: "ADMIN_MANAGE_USER_CONTENT_READ",
    ADMIN_MANAGE_USER_CONTENT_UPDATE: "ADMIN_MANAGE_USER_CONTENT_UPDATE",

    //SHARED MSP & VENDOR
    MANAGE_CONNECTIONS_READ: "MANAGE_CONNECTIONS_READ",
    MANAGE_CONNECTIONS_UPDATE: "MANAGE_CONNECTIONS_UPDATE",
    PRODUCT_REVIEWS_READ: "PRODUCT_REVIEWS_READ",
    COMPANY_PROFILE_READ: "COMPANY_PROFILE_READ",
    COMPANY_PROFILE_UPDATE: "COMPANY_PROFILE_UPDATE",
    USER_MANAGEMENT_READ: "USER_MANAGEMENT_READ",
    USER_MANAGEMENT_UPDATE: "USER_MANAGEMENT_UPDATE",
    ROLE_MANAGEMENT_READ: "ROLE_MANAGEMENT_READ",
    ROLE_MANAGEMENT_UPDATE: "ROLE_MANAGEMENT_UPDATE",
    DATA_EXPORT: "DATA_EXPORT_READ",
    DATA_EXPORT_UPDATE: "DATA_EXPORT_UPDATE",
    MANAGE_CONTENT_READ: "MANAGE_CONTENT_READ",
    MANAGE_CONTENT_UPDATE: "MANAGE_CONTENT_UPDATE",

    //CUSTOMER
    MSP_CLIENT_PRODUCTS_READ: "MSP_CLIENT_PRODUCTS_READ",
    MSP_CLIENT_PRODUCTS_UPDATE: "MSP_CLIENT_PRODUCTS_UPDATE",
};

//types of PERMISSION_GROUPS
export type TPermissionGroupsKeys = (typeof PERMISSION_GROUPS)[keyof typeof PERMISSION_GROUPS];
export type TPermissionGroupsKeysArr = TPermissionGroupsKeys[];

export const PERMISSION_FEATURE_TAB_KEYS = {
    CP_SUPER_ADMIN: "CP_SUPER_ADMIN",
    SYSTEM_ADMIN: "SYSTEM_ADMIN",
    PARTNER_ADMIN: "PARTNER_ADMIN",
    COMPANY_ADMIN: "COMPANY_ADMIN",
} as const;

export type PermissionFeatureTabKey = keyof typeof PERMISSION_FEATURE_TAB_KEYS;
export type PermissionFeatureTabValue = (typeof PERMISSION_FEATURE_TAB_KEYS)[PermissionFeatureTabKey];

export const PERMISSION_FEATURE_TAB_KEYS_LABELS = {
    CP_SUPER_ADMIN: "CP Super Admin",
    SYSTEM_ADMIN: "System Admin",
    PARTNER_ADMIN: "Partner Admin",
    COMPANY_ADMIN: "Company Admin",
} as const;

const createBaseRoleMapping = <T extends Record<string, string>>(roles: T): Record<string, string> => {
    const baseRoleMapping: Record<string, string> = {};

    Object.keys(roles).forEach(key => {
        // Extract the base role after the second underscore
        const baseRole = key.split("_").slice(2).join("_");
        baseRoleMapping[baseRole] = baseRole;
    });

    return baseRoleMapping;
};

type TAdminRolesPermissionsKeys =
    | TAdminVendorPermissionGroupsKey
    | TAdminMSPPermissionGroupsKey
    | TAdminInternalITPermissionGroupsKey
    | TAdminCustPermissionGroupsKey;
type RemovePrefix<T, Prefix extends string> = T extends `${Prefix}${infer Rest}` ? Rest : T;
type RemoveAdminPrefix<T> = RemovePrefix<T, "ADMIN_VENDOR_" | "ADMIN_MSP_" | "ADMIN_INTIT_" | "ADMIN_CUST_">;

export const COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS = createBaseRoleMapping({
    ...ADMIN_INTERNAL_IT_PERMISSION_GROUPS,
    ...ADMIN_MSP_PERMISSION_GROUPS,
    ...ADMIN_VENDOR_PERMISSION_GROUPS,
}) as Record<RemoveAdminPrefix<TAdminRolesPermissionsKeys>, RemoveAdminPrefix<TAdminRolesPermissionsKeys>>;
