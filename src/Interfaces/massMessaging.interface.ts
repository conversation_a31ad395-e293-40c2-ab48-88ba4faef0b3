import {ContactListTypes} from "../enums/contactListTypes.enum";
import {ICompanyProfileType} from "./company.interface";
import {IPeople, IUserProfile} from "./people.interface";

export interface IMassMessageForm {
    massMessage: string;
    contactListID: string;
    contactListType: ContactListTypes;
    partners: string[];
    messageTypeOption: string;
}
export interface ISelectedCompany {
    partnership_id: string;
    contacts_ids: string[];
}
export interface IContactList {
    id: string;
    name: string;
    type: ContactListTypes;
    companies_count: number;
    contacts_count: number;
}
export interface IContactListPartnershipType {
    accepted_reason?: string;
}
export interface IContactListPartner {
    id: string;
    name: string;
    type: ICompanyProfileType;
    profile_type: ICompanyProfileType;
    partner: IContactListPartnershipType;
    friendly_url: string;
    avatar?: string | null;
    users: IUserProfile[];
    claimers?: Partial<IPeople>[];
    created_at?: string;
    last_logged_in_at?: string;
    subdomain?: string;
    partner_flag?: boolean;
    is_distributor?: boolean;
    manage_clients?: boolean;
    rating?: number;
    parent_id?: string;
    affiliate_id?: string;
    is_affiliate_brand_main_company?: boolean;
    parent?: any;
}
export interface IContactListPartnership {
    id: string | null;
    company: IContactListPartner;
}
