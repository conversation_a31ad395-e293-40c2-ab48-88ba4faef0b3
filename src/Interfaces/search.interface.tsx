import {IBlog} from "./blog.interface";
import {IDoc} from "./company.interface";
import {IMediaGallery} from "./mediaGallery.interface";
import {ILinks, IMeta} from "./pagination.interface";
import {ITypesProfile, UserStatus} from "./people.interface";
import {IParent} from "./profiles.interface";

export interface ISearch {
    id: string;
    type:
        | "categories"
        | "content-video"
        | "content-image"
        | "content-blog"
        | "content-doc"
        | "vendors"
        | "products"
        | "people"
        | "claimer"
        | "overview"
        | "title"
        | "seeMore";
    name?: string;
    label?: string;
    title?: string;
    avatar?: string;
    handle?: string;
    thumbnail?: string;
    profile_type?: ITypesProfile;
    friendly_url?: string;
    image?: string;
    is_first?: boolean;
    is_first_section?: boolean;

    // Used for company management
    email?: string;
    last_name?: string;
    parent_name?: string;
    parent_id?: string;
    parent_friendly_url?: string;
    parent_type?: "userProfile" | "companyProfile";
    is_distributor?: boolean;

    // Used for docs
    mime_type?: string;
    // Used for private label
    is_private?: boolean;
    subscription_label?: string;
    subscription_value?: string;
}

export interface ISearchClaimers {
    id: string;
    name: string;
    email: string;
    friendly_url: string;
    company_id: string;
    company_name?: string;
    company_friendly_url?: string;
}

export interface ISearchResults {
    categories: ICategoriesResult;
    content: IContentResult;
    vendors: IVendorsResult;
    products: IProductsResult;
    people: IPeopleResult;
}

export interface ICategoriesData {
    id: string;
    name: string;
    color: string;
    parent_id?: string | null;
    friendly_url?: string;
    total_usage_count?: string;
    section_title?: string;
    parent?: IPeopleData;
    icon_name?: string | null;
    description?: string | null;
    featured?: boolean;
    is_hidden?: boolean;
    is_top_category?: boolean;
    number_of_products?: number;
    stack_chart_min_y?: string;
    stack_chart_max_y?: string;
    stack_chart_min_x?: string;
    stack_chart_max_x?: string;
    default_my_stack_category?: boolean;
    navistack_reminder?: string | null;
}

export interface ICategoriesResult {
    data: ICategoriesData[];
    meta: IMeta;
    links: ILinks;
}

export interface IVideosSearch {
    id: string;
    title: string;
    created_at: string;
    thumbnail?: string;
    description?: string;
    duration?: string;
    thumbnail_url?: string;
    views_count?: number;
    likes_count?: number;
    parent?: IParent;
    name?: string;
    url?: string;
}

export interface IContentResult {
    videos: IContentResultVideos;
    images: IContentResultImages;
    blogs: IContentResultBlogs;
    documents: IContentResultDocs;
    data?: IVideosSearch[] | IMediaGallery[] | IBlog[] | IDoc[];
    meta?: IMeta;
    links?: ILinks;
}

export interface ISearchPagination {
    videos: IContentResultVideos;
    images: IContentResultImages;
    blogs: IContentResultBlogs;
    documents: IContentResultDocs;
}

export interface IContentResultVideos {
    data: IVideosSearch[];
    meta: IMeta;
    links: ILinks;
}

export interface IContentResultImages {
    data: IMediaGallery[];
    meta: IMeta;
    links: ILinks;
}

export interface IContentResultBlogs {
    data: IBlog[];
    meta: IMeta;
    links: ILinks;
}

export interface IContentResultDocs {
    data: IDoc[];
    meta: IMeta;
    links: ILinks;
}

export interface IVendorsResult {
    data: {
        id: string;
        name: string;
        handle: string;
        friendly_url: string;
        avatar?: string;
        subscription_label?: string;
        subscription_value?: string;
        is_distributor?: boolean;
    }[];
    meta: IMeta;
    links: ILinks;
}

export interface IProductsResult {
    data: {
        id: string;
        name: string;
        friendly_url: string;
        image?: string;
        parent: {id: string; name: string; handle: string; friendly_url: string};
    }[];
    meta: IMeta;
    links: ILinks;
}

export interface IPeopleData {
    id: string;
    name: string;
    handle: string;
    friendly_url: string;
    current_user_is_following: boolean;
    avatar?: string;
    profile_type?: ITypesProfile;
    types?: ITypes;
    is_private?: boolean;
    status?: UserStatus;
}

export interface IPeopleResult {
    data: IPeopleData[];
    meta: IMeta;
    links: ILinks;
}

interface ITypes {
    type: string;
    id: string;
    label: string;
    value: string;
    type_is_of_vendor: boolean;
}
