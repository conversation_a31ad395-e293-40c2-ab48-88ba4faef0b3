import {ITag} from "./tags.interface";

export interface IAddAssetsPostForm {
    title: string; // max length 200
    // status: StatusTypes;
    images: any[]; // file or url
    description?: string; // max length 255
    tags?: ITag[] | string[]; // max of 3
    visibilityType?: string;
}

export enum StatusTypes {
    Archived = "Archived",
    Draft = "Draft",
    Published = "Published",
    Unpublished = "Unpublished",
}

export interface IAddAssetsPostData extends Omit<IAddAssetsPostForm, "tags"> {
    tags: string[];
    subject_id: string;
    subject_type: SubjectTypes;
    is_partner_content: boolean;
    media_visibility?: string;
}

export enum SubjectTypes {
    UserProfile = "userProfile",
    CompanyProfile = "companyProfile",
    VendorProfile = "vendorProfile",
}
