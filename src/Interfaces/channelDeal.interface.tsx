export type TStatusKey =
    | "CHANNEL_DEALS_STATUSES_APPROVED"
    | "CHANNEL_DEALS_STATUSES_INACTIVE"
    | "CHANNEL_DEALS_STATUSES_PENDING"
    | "CHANNEL_DEALS_STATUSES_DECLINED";

export interface IUpdateHeader {
    company_id?: string;
    deal_name?: string;
    status_id?: string;
    reject_reason?: string;
    decline_reason?: string;
    offer_details?: string;
    category_id?: string;
    sub_category_id?: string;
    email_receiving_leads?: string;
    other_considerations?: string;
    end_date?: string;
    is_featured?: boolean;
}

export interface IChannelDealCreateHeader {
    company_id?: string;
    deal_name?: string;
    status_id?: string;
    decline_reason: string;
    offer_detail?: string;
    category_id: string;
    sub_category_id: string;
    email_receiving_leads: string;
    other_considerations: string;
    end_date?: string;
    is_featured?: boolean;
}

export interface IChannelDealData {
    id: string;
    company_id: string;
    company_name: string;
    deal_name: string;
    status_id: string;
    status_name: string;
    status_key: TStatusKey;
    reject_reason: string | null;
    offer_details: string;
    category_id: string;
    category_name: string;
    sub_category_id: string | null;
    sub_category_name?: string;
    email_receiving_leads: string;
    other_considerations: string;
    created_by_user_id: string;
    created_by_name: string;
    reviewer_by_user_id: string;
    reviewer_name: string | null;
    company_avatar: string | null;
    is_featured: boolean;
    end_date: string;
    claimed_status?: {
        pending_companies: Array<string>;
        declined_companies: Array<string>;
        approved_companies: Array<string>;
    };
    company_has_approved_deals: boolean;
    company_rating: number;
    created_at: string;
    updated_at: string;
}
