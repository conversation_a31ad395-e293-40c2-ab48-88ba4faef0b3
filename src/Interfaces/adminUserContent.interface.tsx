import {ICompany} from "./company.interface";
import {IProducts} from "./products.interface";
import {IStatusScope} from "./statusScope.interface";

export type TUserContentStatuses = "approved" | "pending" | "declined";

export type TUserContentData = IUserContentCompanyData | IUserContentProductData;

export interface IUserContentCompanyData extends ICompany {
    status_scope: IStatusScope;
    action: string;
}

export interface IUserContentProductData extends Omit<IProducts, "company"> {
    status_scope: IStatusScope;
    action: string;
    company: IUserContentCompanyData;
}

export interface IApproveUserContentCompanyForm {
    id: string;
    name: string;
    profile_company_website_url: string;
    linkedin?: string;
    description: string;
}

export interface IApproveUserContentProductForm {
    id: string;
    company_name: string;
    name: string;
    url: string;
    description: string;
    category_id: string;
}
