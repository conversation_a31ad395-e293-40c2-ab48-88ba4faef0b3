export const VENDOR_PLAN_TYPES = {
    VENDOR_FREE: "VENDOR_FREE",
    BASIC: "VENDOR_BASIC",
    PREMIUM: "VENDOR_PREMIUM",
    PLUS: "VENDOR_PLUS",
};

export const MSP_PLAN_TYPES = {
    MSP_BUSINESS_BASIC: "MSP_BUSINESS_BASIC",
    MSP_BUSINESS_PREMIUM: "MSP_BUSINESS_PREMIUM",
};

export const DIRECT_PLAN_TYPES = {
    DIRECT_BASIC: "DIRECT_BASIC",
    DIRECT_PREMIUM: "DIRECT_PREMIUM",
};

export const ALL_PLAN_TYPES = {
    ...VENDOR_PLAN_TYPES,
    ...MSP_PLAN_TYPES,
    ...DIRECT_PLAN_TYPES,
};

export type TVendorPlanType = (typeof VENDOR_PLAN_TYPES)[keyof typeof VENDOR_PLAN_TYPES];
export type TMspPlanType = (typeof MSP_PLAN_TYPES)[keyof typeof MSP_PLAN_TYPES];
export type TDirectPlanType = (typeof DIRECT_PLAN_TYPES)[keyof typeof DIRECT_PLAN_TYPES];
export type TAllPlanType = (typeof ALL_PLAN_TYPES)[keyof typeof ALL_PLAN_TYPES];
