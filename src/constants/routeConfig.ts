import {PERMISSION_GROUPS, TPermissionGroupsKeysArr} from "../Interfaces/features/features.interface";

const routes = {
    Login: {path: "/login", name: "Login"},
    HomeOne: {path: "/homeOne", name: "HomeOne"},
    Home: {path: "/", name: "Home"},
    Register: {path: "/register", name: "Register"},
    UserRegister: {path: "/userregister", name: "Event Register"},
    MSPRegister: {path: "/mspregister", name: "MSP Register"},
    VendorUpgradePage: {path: "/vendor-upgrade", name: "Vendor Upgrade"},
    Podcast: {path: "/podcast", name: "Channelfied Podcast"},
    Blog: {path: "/blog", name: "Blog List"},
    ProductReview: {path: "/product-review", name: "Product Review List"},
    AboutUs: {path: "/about-us", name: "About Us"},
    Contact: {path: "/contact", name: "<PERSON>"},
    AboutMsp: {path: "/about/msp", name: "About MSP"},
    Msp: {path: "/about/msp", name: "About MSP"},
    AboutVendor: {path: "/about/vendor", name: "About Vendor"},
    Vendor: {path: "/about/vendor", name: "About Vendor"},
    Pitch: {path: "/pitch", name: "Channel Pitch"},
    ConfirmEmail: {path: "/confirm-email", name: "Confirm Email"},
    ResetPassword: {path: "/reset-password", name: "Reset Password"},
    ClientResetPassword: {path: "/msp-client-reset", name: "Reset Password", isClientMSPPage: true},
    Account: {path: "/account", name: "Account", MSPClientCanAccess: true},
    Logout: {path: "/logout", name: "Logout", MSPClientCanAccess: true},
    Attend: {path: "/attend", name: "Register Attendance"},
    WatchPitch: {path: "/watch/pitch", name: "Channel Pitch"},
    Explorer: {path: "/explorer", name: "Explorer"},
    VideoExplorer: {path: "/explorer/videos", name: "Video Explorer"},
    ProfileExplorer: {path: "/explorer/profiles", name: "People Explorer"},
    Cash: {path: "/cash", name: "Channel Cash"},
    Terms: {path: "/terms", name: "Terms"},
    Policy: {path: "/policy", name: "Policy"},
    RegistrationTerms: {path: "/registration-terms", name: "Registration Terms"},
    AcceptableUsePolicy: {path: "/acceptable-use-policy", name: "Acceptable Use Policy"},
    VendorProfile: {path: "/v/:id", name: "Vendor Profile", static_path: "/v/"},
    UserProfile: {path: "/u/:id", name: "User Profile", static_path: "/u/", MSPClientCanAccess: true},
    MspProfile: {path: "/i/:id/:tab", name: "MSP Profile", static_path: "/i/"},
    InternalITProfile: {path: "/it/:id/:tab", name: "Internal IT Profile", static_path: "/it/"},
    CompanyClaim: {path: "/company-claim", name: "Company Claim"},
    WatchVideo: {path: "/watch/video/:id", name: "Watch Video"},
    Categories: {path: "/categories", name: "Categories"},
    CategoriesVendorsProducts: {path: "/categories/:friendly_url", name: "Categories Directory"},
    SearchResults: {path: "/search", name: "Search results"},
    PublicBlog: {path: "/cp/blog/:friendly_url", name: "Public Blog", static_path: "/blog/", isPartnerPortal: true},
    PRMLogin: {path: "/login", name: "PRM Login", static_path: "/login/", isPartnerPortal: true},
    CompanyPortal: {path: "/company-portal/:tab", name: "Company Portal"},
    CompanyPortalHome: {path: "/company-portal/my-channel", name: "Company Portal"},
    ProductReviewSearch: {path: "/products", name: "Product Review Search"},
    ProductDetails: {path: "/product/:friendly_url", name: "Product Details"},
    CreateReview: {path: "/create-review/:friendly_url", name: "Create Review"},
    CompletedReview: {path: "/completed-review", name: "Completed Review"},
    ChannelCharts: {path: "/channelcharts/:chart_name", name: "Channel Charts"},
    IndustryCalendar: {path: "/industrycalendar", name: "Industry Calendar"},
    IndustryMap: {path: "/industrymap", name: "Industry Map 2023"},
    UnsubscribeIndustryEvents: {
        path: "/industrycalendar/unsubscribe/:unsubscription_token",
        name: "Unsubscribe Industry Events",
    },
    DocumentRebrand: {
        path: "/document-rebrand",
        name: "Document Rebrand",
        isPartnerPortal: true,
        permissions: [PERMISSION_GROUPS.DOCUMENT_REBRAND_READ] as TPermissionGroupsKeysArr,
    },
    VendorPortal: {
        // path: "/partner-portal/v/:friendly_url",
        path: "/",
        name: "Vendor Portal",
        // static_path: "/v/",
        isPartnerPortal: true,
    },
    VendorPortalFolder: {
        path: "/folder/:folderId",
        name: "Folder",
        isPartnerPortal: true,
    },
    ChannelMeetings: {path: "/channelmeetings", name: "Channel Meeting"},
    MyStack: {
        path: "/my-stack/navistack",
        name: "NaviStack",
        permissions: [PERMISSION_GROUPS.MANAGE_STACK_READ] as TPermissionGroupsKeysArr,
    },
    CompanyNaviStack: {
        path: "/my-stack/:tab",
        name: "NaviStack",
        MSPClientCanAccess: true,
        permissions: [PERMISSION_GROUPS.MANAGE_STACK_READ] as TPermissionGroupsKeysArr,
    },
    CompanyContracts: {
        path: "/my-stack/subscriptions",
        name: "Manage Contracts",
        MSPClientCanAccess: true,
        permissions: [PERMISSION_GROUPS.MANAGE_CONTRACTS_READ] as TPermissionGroupsKeysArr,
    },
    AddCompanyContract: {
        path: "/my-stack/create-contract",
        name: "Add Vendor Contract",
        MSPClientCanAccess: true,
        permissions: [PERMISSION_GROUPS.MANAGE_CONTRACTS_READ] as TPermissionGroupsKeysArr,
    },
    DealRegistrations: {
        path: "/my-stack/deal-registrations",
        name: "Register a Deal",
        permissions: [PERMISSION_GROUPS.MANAGE_DEALS_READ] as TPermissionGroupsKeysArr,
    },
    VendorDealRegistrations: {
        path: "/company-portal/deal-registrations",
        name: "Deal Registrations",
        permissions: [PERMISSION_GROUPS.MANAGE_DEALS_READ] as TPermissionGroupsKeysArr,
    },
    AcceptInvite: {
        path: "/partner-invite/:inviteId/company/:friendly_url/:subDomain",
        name: "Accept Invite",
        isPartnerPortal: true,
    },
    NewAcceptInvitation: {
        path: "/partner-invitation/:subdomain/:token",
        name: "Accept Invitation",
        isPartnerPortal: true,
    },
    ManageClients: {
        path: "/manage-clients",
        name: "Manage Customers",
        permissions: [PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_READ] as TPermissionGroupsKeysArr,
    },
    ManageWhitelabeling: {
        path: "/manage-whitelabeling",
        name: "Manage Whitelabeling",
        permissions: [PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_READ] as TPermissionGroupsKeysArr,
    },
    UpdateCustomizableEmail: {
        path: "/manage-whitelabeling/update-customizable-email",
        name: "Update Customizable Email",
        permissions: [PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_UPDATE] as TPermissionGroupsKeysArr,
    },
    ManagedAffiliateProfile: {
        path: "/ma/:friendly_url",
        name: "Managed Affiliate Profile",
    },
    ManageAffiliates: {
        path: "/multistack-manager",
        name: "MultiStack Manager",
        permissions: [PERMISSION_GROUPS.AFFILIATES_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    AffiliatesPendingInvites: {
        path: "/multistack-pending-invites",
        name: "Pending Invitations",
        permissions: [PERMISSION_GROUPS.AFFILIATES_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    Integrations: {
        path: "/integrations",
        name: "Integration",
        permissions: [PERMISSION_GROUPS.INTEGRATIONS_READ] as TPermissionGroupsKeysArr,
    },
    TechnologyMap: {path: "/technologymap", name: "Technology Map"},
    AccessManagement: {
        path: "/access-management/:tab",
        name: "Manage Access",
        permissions: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ] as TPermissionGroupsKeysArr,
    },
    //Admin
    Dashboard: {path: "/admin/dashboard", name: "Admin Dashboard"},
    PitchDay: {
        path: "/admin/pitch-day",
        name: "Pitch Day",
        permissions: [PERMISSION_GROUPS.ADMIN_ENGAGE_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    PitchManagement: {
        path: "/admin/pitch-management",
        name: "Pitch Management",
        permissions: [PERMISSION_GROUPS.ADMIN_ENGAGE_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    PitchVideoUpload: {
        path: "/admin/pitch-video-upload",
        name: "Pitch Video Upload",
        permissions: [PERMISSION_GROUPS.ADMIN_ENGAGE_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    PollManagement: {
        path: "/admin/poll-management",
        name: "Poll Management",
        permissions: [PERMISSION_GROUPS.ADMIN_POLL_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    PitchReports: {
        path: "/admin/reports/events",
        name: "Event Reports",
        permissions: [PERMISSION_GROUPS.ADMIN_EVENT_REPORTS_READ] as TPermissionGroupsKeysArr,
    },
    EventCharts: {
        path: "/admin/reports/events/charts",
        name: "Event Charts",
        permissions: [PERMISSION_GROUPS.ADMIN_EVENT_CHARTS_READ] as TPermissionGroupsKeysArr,
    },
    UserReports: {
        path: "/admin/reports/users",
        name: "User Reports",
        permissions: [PERMISSION_GROUPS.ADMIN_USER_REPORTS_READ] as TPermissionGroupsKeysArr,
    },
    ReviewReports: {
        path: "/admin/reports/reviews",
        name: "All Reviews",
        permissions: [PERMISSION_GROUPS.ADMIN_ALL_REVIEWS_READ] as TPermissionGroupsKeysArr,
    },
    CustomViewReports: {
        path: "/admin/reports/customview",
        name: "Custom Views",
        permissions: [PERMISSION_GROUPS.ADMIN_CUSTOM_REPORTS_READ] as TPermissionGroupsKeysArr,
    },
    ProductReviewReports: {
        path: "/admin/reports/product-reviews",
        name: "Reviews by Vendor/Product Reports",
        permissions: [PERMISSION_GROUPS.ADMIN_REVIEWS_BY_VENDOR_OR_PRODUCT_READ] as TPermissionGroupsKeysArr,
    },
    CompanyManagement: {
        path: "/admin/company-management",
        name: "Company Management",
        permissions: [PERMISSION_GROUPS.ADMIN_COMPANY_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    UsersManagement: {
        path: "/admin/users-management",
        name: "Users Management",
        permissions: [PERMISSION_GROUPS.ADMIN_USER_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    ChannelDealsManagement: {
        path: "/admin/channel-deals-management",
        name: "Channel Deals Management",
        permissions: [PERMISSION_GROUPS.ADMIN_MANAGE_CHANNEL_DEALS_READ] as TPermissionGroupsKeysArr,
    },
    UserContentManagement: {
        path: "/admin/user-content-management",
        name: "Vendor/Product Awaiting Approval",
        permissions: [PERMISSION_GROUPS.ADMIN_MANAGE_USER_CONTENT_READ] as TPermissionGroupsKeysArr,
    },
    VideoAnalytics: {
        path: "/admin/video-analytics",
        name: "Video Analytics",
        permissions: [PERMISSION_GROUPS.ADMIN_VIDEO_ANALYTICS_READ] as TPermissionGroupsKeysArr,
    },
    AdminIndustryCalendar: {
        path: "/admin/industry-calendar",
        name: "Industry Calendar",
        permissions: [PERMISSION_GROUPS.ADMIN_INDUSTRY_CALENDAR_READ] as TPermissionGroupsKeysArr,
    },
    AdminJobTitle: {
        path: "/admin/job-title-management",
        name: "Job Title Management",
        permissions: [PERMISSION_GROUPS.ADMIN_JOB_TITLE_READ] as TPermissionGroupsKeysArr,
    },
    AdminEmailWhitelist: {
        path: "/admin/email-whitelist",
        name: "Email Whitelist",
        permissions: [PERMISSION_GROUPS.ADMIN_EMAIL_WHITELIST_READ] as TPermissionGroupsKeysArr,
    },
    AdminInviteManagement: {
        path: "/admin/invite-management",
        name: "Invite Management",
    },
    VideoAnalyticsDetail: {
        path: "/admin/video-analytics/:id",
        name: "Video Analytics Detail",
        permissions: [PERMISSION_GROUPS.ADMIN_VIDEO_ANALYTICS_READ] as TPermissionGroupsKeysArr,
    },
    CompanyManagementDetail: {
        path: "/admin/company-management/:company_id/:tab",
        name: "Company Detail",
        permissions: [PERMISSION_GROUPS.ADMIN_COMPANY_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    UserManagementDetail: {
        path: "/admin/user-management/:id/:tab",
        name: "User Detail",
        permissions: [PERMISSION_GROUPS.ADMIN_USER_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    CategoriesMngmt: {
        path: "/admin/categories",
        name: "Categories Management",
        permissions: [PERMISSION_GROUPS.ADMIN_CATEGORIES_TAGS_READ] as TPermissionGroupsKeysArr,
    },
    CategoriesTagsMngmt: {
        path: "/admin/categories-tags",
        name: "Categories/Tags Management",
        permissions: [PERMISSION_GROUPS.ADMIN_CATEGORIES_TAGS_READ] as TPermissionGroupsKeysArr,
    },
    ProfileEnrichmentManagement: {
        path: "/admin/profile-enrichment-management",
        name: "Profile Enrichment Management",
        permissions: [PERMISSION_GROUPS.ADMIN_PROFILE_ENRICHMENT_READ] as TPermissionGroupsKeysArr,
    },
    ProfileEnrichmentManagementDetail: {
        path: "/admin/profile-enrichment-management/:id",
        name: "Profile Enrichment Management Detail",
        permissions: [PERMISSION_GROUPS.ADMIN_PROFILE_ENRICHMENT_READ] as TPermissionGroupsKeysArr,
    },
    AdminBroadcast: {
        path: "/admin/broadcast",
        name: "Send Broadcasts",
        permissions: [PERMISSION_GROUPS.ADMIN_SEND_BROADCAST_READ] as TPermissionGroupsKeysArr,
    },
    AdminCustomEmails: {
        path: "/admin/custom-emails",
        name: "In-App Emails",
        permissions: [PERMISSION_GROUPS.ADMIN_IN_APP_EMAILS_READ] as TPermissionGroupsKeysArr,
    },
    AdminCustomEmailsUpdate: {
        path: "/admin/custom-emails/:email_id",
        name: "Update In-App Email",
        permissions: [PERMISSION_GROUPS.ADMIN_IN_APP_EMAILS_READ] as TPermissionGroupsKeysArr,
    },
    MspResponses: {
        path: "/admin/msp-responses",
        name: "IT Service Provider Responses",
        permissions: [PERMISSION_GROUPS.ADMIN_MSP_RESPONSES_READ] as TPermissionGroupsKeysArr,
    },
    AdminProductReviews: {
        path: "/admin/product-reviews",
        name: "Product Reviews",
        permissions: [PERMISSION_GROUPS.ADMIN_PRODUCT_REVIEWS_READ] as TPermissionGroupsKeysArr,
    },
    AppSysConf: {
        path: "/admin/sys-conf",
        name: "System Configurations",
        permissions: [PERMISSION_GROUPS.ADMIN_APP_CONFIGS_READ] as TPermissionGroupsKeysArr,
    },
    AppConfiguration: {
        path: "/admin/sys-conf/config",
        name: "Application Configurations",
        permissions: [PERMISSION_GROUPS.ADMIN_APP_CONFIGS_READ] as TPermissionGroupsKeysArr,
    },
    FeatureFlag: {
        path: "/admin/feature-flag/config",
        name: "Feature Flags",
        permissions: [PERMISSION_GROUPS.ADMIN_FEATURE_FLAGS_READ] as TPermissionGroupsKeysArr,
    },
    AppJobs: {
        path: "/admin/sys-conf/jobs",
        name: "Application Jobs",
        permissions: [PERMISSION_GROUPS.ADMIN_APP_JOBS_READ] as TPermissionGroupsKeysArr,
    },
    ActivityLogs: {
        path: "/admin/sys-conf/activity-logs",
        name: "Activity Logs",
        permissions: [PERMISSION_GROUPS.ADMIN_ACTIVITY_LOGS_READ] as TPermissionGroupsKeysArr,
    },
    FlaggedComments: {
        path: "/admin/flagged-comments",
        name: "Flagged Comments",
        permissions: [PERMISSION_GROUPS.ADMIN_FLAGGED_COMMENTS_READ] as TPermissionGroupsKeysArr,
    },
    AdminProductReviewDetail: {
        path: "/admin/product-reviews/:id",
        name: "Product Review Detail",
        permissions: [PERMISSION_GROUPS.ADMIN_PRODUCT_REVIEWS_READ] as TPermissionGroupsKeysArr,
    },
    AdminChannelCharts: {
        path: "/admin/channel-charts",
        name: "Channel Charts",
        permissions: [PERMISSION_GROUPS.ADMIN_CHANNEL_CHARTS_READ] as TPermissionGroupsKeysArr,
    },
    AdminReportRemoveUser: {
        path: "/admin/remove-from-report",
        name: "Remove Users from Report",
        permissions: [PERMISSION_GROUPS.ADMIN_REMOVE_USERS_REPORTS_READ] as TPermissionGroupsKeysArr,
    },
    AdminAdvertisementManagement: {
        path: "/admin/advertisement-management",
        name: "Advertisement Management",
        permissions: [PERMISSION_GROUPS.ADMIN_AD_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    AdminAddAdvertisement: {
        path: "/admin/advertisement-management/add",
        name: "Add Advertisement",
        permissions: [PERMISSION_GROUPS.ADMIN_AD_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    AdminAddNaviStackSponsored: {
        path: "/admin/advertisement/add-navistack-sponsored",
        name: "Add NaviStack Sponsored",
        permissions: [PERMISSION_GROUPS.ADMIN_AD_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    AdminTangoSettings: {
        path: "/admin/tango-settings",
        name: "Tango Settings",
        permissions: [PERMISSION_GROUPS.ADMIN_TANGO_SETTINGS_READ] as TPermissionGroupsKeysArr,
    },
    AdminAddTangoCatalog: {
        path: "/admin/tango-settings/add",
        name: "Add Catalog",
        permissions: [PERMISSION_GROUPS.ADMIN_TANGO_SETTINGS_READ] as TPermissionGroupsKeysArr,
    },
    AdminPermissionsManagement: {
        path: "/admin/permission-management",
        name: "Permission Management",
        permissions: [PERMISSION_GROUPS.CP_SUPER_ADMIN] as TPermissionGroupsKeysArr,
    },
    RolesManagement: {
        path: "/role-management",
        name: "Role Management",
        permissions: [PERMISSION_GROUPS.ROLE_MANAGEMENT_READ] as TPermissionGroupsKeysArr,
        MSPClientCanAccess: true,
    },
    CompanyUsersManagement: {
        path: "/company-users-management/:tab",
        name: "Users Management",
        permissions: [PERMISSION_GROUPS.USER_MANAGEMENT_READ] as TPermissionGroupsKeysArr,
        MSPClientCanAccess: true,
    },
    VendorChannelDeals: {
        path: "/company-channel-deals",
        name: "Channel Deals",
        MSPClientCanAccess: false,
        permissions: [
            PERMISSION_GROUPS.MANAGE_CHANNEL_DEALS_READ,
            PERMISSION_GROUPS.ADMIN_VENDOR_CHANNEL_DEALS_READ,
        ] as TPermissionGroupsKeysArr,
    },
    AdminTemplateRolesManagement: {
        path: "/admin/template-management",
        name: "Manage Template Roles",
        permissions: [PERMISSION_GROUPS.ADMIN_ROLE_MNGMT_READ] as TPermissionGroupsKeysArr,
    },
    UpsertBlog: {
        path: "/create-blog",
        name: "Create Blog",
    },
    UpsertPRMBlog: {
        path: "/create-portal-blog",
        name: "Create Blog",
        isPartnerPortal: true,
    },
    CompanyExpenses: {
        path: "/company-expenses",
        name: "Company Expenses",
        MSPClientCanAccess: true,
    },

    //Builder
    LegalHome: {path: "/legal", name: "Legal Related Page"},
    LegalPage: {path: "/legal/:title", name: "Legal Related Page"},
    PodcastEpisode: {path: "/podcast/:pid", name: "Channelfied Podcast Episode"},
    BlogPost: {path: "/blog/:pid", name: "Channel Program Blog"},
    ChannelDeals: {path: "/channel-deals", name: "Channel Deals"},
    ProductReviewPost: {path: "/product-review/:pid", name: "Channel Program Product Review"},
    ChannelProgramPages: {path: "/c/:page", name: "Miscellaneous Pages", static_path: "/c/"},
    SignUpPages: {path: "/register/:page", name: "Sign Up Pages"},
    FirePage: {path: "/fire", name: "Fire Page"},
    SellerPage: {path: "/seller", name: "Seller Page"},
    CareersPage: {path: "/careers", name: "Careers Page"},

    //MSP Client routes
    MspClientProfile: {
        path: "/mc/:friendly_url",
        name: "Client Profile",
        MSPClientCanAccess: true,
    },
    MSPClientLoginPage: {path: "/msp-client-login", name: "MSP Client Login", isClientMSPPage: true},
    MSPClientRegisterPage: {path: "/msp-client-register", name: "MSP Client Login", isClientMSPPage: true},
    CustomerNavistack: {
        path: "/manageyourproducts",
        name: "Manage Your Products",
        isClientMSPPage: true,
        MSPClientCanAccess: true,
        permissions: [PERMISSION_GROUPS.MSP_CLIENT_PRODUCTS_READ] as TPermissionGroupsKeysArr,
    },
    MSPClientOpenLinkRedirect: {path: "/s/:signature", name: "MSP Client Login", isClientMSPPage: true},
} as const;

type TRouteConfig = {
    [key in keyof typeof routes]: {
        path: string;
        name: string;
        static_path?: string;
        isPartnerPortal?: boolean;
        MSPClientCanAccess?: boolean;
        isClientMSPPage?: boolean;
        permissions?: TPermissionGroupsKeysArr;
    };
};

const routeConfig: TRouteConfig = routes;

export default routeConfig;
