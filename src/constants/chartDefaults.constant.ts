import {getDateXDaysFromNow} from "../utils/formatDate";

export const emptyChartDataMspContracts = [
    {id: 0, value: 10, label: "series A"},
    {id: 1, value: 15, label: "series B"},
    {id: 2, value: 20, label: "series C"},
];

export const zeroedCostPieChartData = [{id: 0, value: 1, label: ""}];

export const demoChartTotalMspContracts = "3,450.40";

export const demoMostRecentRenewalDate = getDateXDaysFromNow(6);
