import {UseQueryOptions} from "@tanstack/react-query";

export const APP_NAME = "Channel Program";
export const API_BASE = "api/";
export const API_BASE_V1 = "v1/";

export const SERVER_ENV = {
    development: import.meta.env.VITE_APP_BACKEND_SERVER_LOCAL,
    production: import.meta.env.VITE_APP_BACKEND_SERVER_URL,
};
export const SERVER_API_BASE_V1 = SERVER_ENV[import.meta.env.MODE] + API_BASE + API_BASE_V1;

export const CHANNEL_PROGRAM_SUBDOMAIN = import.meta.env.VITE_APP_CHANNEL_PROGRAM_SUBDOMAIN || "channelprogram";
export const DIRECT_IT_SUBDOMAIN = import.meta.env.VITE_APP_DIRECT_IT_SUBDOMAIN || "app";

export const WRITE_A_REVIEW_BUTTON_TEXT = "Write a review";
export const INVITE_TO_REVIEW_TEXT = "Invite to Review";
export const REQUIRED_TEXT = "Required";
export const LOAD_MORE_TEXT = "Load More";
export const LOAD_ALL_TEXT = "Load All";
export const SUBCATEGORIES_TEXT = "Subcategories";
export const SUBCATEGORY_TEXT = "Subcategory";
export const BREAKOUT_MAX_NAME = 20;
export const ROUTE_CLASS = "newRoute";
export const ROUTE_CONTAINER_CLASS = "newContainer";
export const ROUTE_CONTAINER_ALT_CLASS = "newAltContainer";
export const STRICT_ROUTE_CLASS = "strictRoutePadding";
export const ADMIN_ROUTE_CLASS = "routePadding fullAdminSection";
export const MININUM_VIEW_COUNT = import.meta.env.VITE_APP_MININUM_VIEW_COUNT;

export const YT_API_BASE = "https://youtube.googleapis.com/youtube/v3/";
export const PLAYLIST_ID = "PLRc25D0nAGeWw7_PbO-iubC7wCUGLXAt1";
export const YT_KEY = import.meta.env.VITE_APP_YOUTUBE_KEY;
export const PHONE = import.meta.env.VITE_APP_PHONE_SALES;
export const HUBSPOT_NEWSLETTER_PORTAL_ID = import.meta.env.VITE_APP_HUBSPOT_NEWSLETTER_PORTAL_ID;
export const HUBSPOT_NEWSLETTER_FORM_ID = import.meta.env.VITE_APP_HUBSPOT_NEWSLETTER_FORM_ID;
export const HUBSPOT_CONTACT_PORTAL_ID = import.meta.env.VITE_APP_HUBSPOT_CONTACT_PORTAL_ID;
export const HUBSPOT_CONTACT_FORM_ID = import.meta.env.VITE_APP_HUBSPOT_CONTACT_FORM_ID;
export const PITCH_DURATION = import.meta.env.VITE_APP_PITCH_DURATION;
export const PITCH_MINUTES_AFTER_END = import.meta.env.VITE_APP_PITCH_MINUTES_AFTER_END;
export const BROADCAST_DRIVER = import.meta.env.VITE_APP_BROADCAST_DRIVER;
export const PUSHER_APP_ID = import.meta.env.VITE_APP_PUSHER_APP_ID;
export const PUSHER_APP_KEY = import.meta.env.VITE_APP_PUSHER_APP_KEY;
export const PUSHER_APP_SECRET = import.meta.env.VITE_APP_PUSHER_APP_SECRET;
export const PUSHER_APP_CLUSTER = import.meta.env.VITE_APP_PUSHER_APP_CLUSTER;
export const HUBSPOT_TRACKER_ID = import.meta.env.VITE_APP_HUBSPOT_TRACKER_ID;
export const FACEBOOK_PAGEVIEW_ID = import.meta.env.VITE_APP_FACEBOOK_PAGEVIEW_ID;
export const FACEBOOK_DOMAIN_VERIFY = import.meta.env.VITE_APP_FACEBOOK_DOMAIN_VERIFY;
export const CHANNEL_PROGRAM_COMPANY_ID = import.meta.env.VITE_APP_CP_COMPANY_ID || "";
export const CHANNEL_PROGRAM_FRIENDLY_URL = import.meta.env.VITE_APP_CHANNEL_PROGRAM_FRIENDLY_URL || "";
export const BUILDER_API = import.meta.env.VITE_APP_BUILDER_API_KEY;
export const GTM_ID = import.meta.env.VITE_APP_GTM_ID;
export const HUBSPOT_TRACKING_ID = import.meta.env.VITE_APP_HUBSPOT_TRACKING_ID;
export const HUBSPOT_VENDOR_CONTACT_FORM_ID = import.meta.env.VITE_APP_HUBSPOT_VENDOR_CONTACT_FORM_ID;
export const HUBSPOT_VENDOR_CONTACT_PORTAL_ID = import.meta.env.VITE_APP_HUBSPOT_VENDOR_CONTACT_PORTAL_ID;
export const POLL_TIME = parseInt(import.meta.env.VITE_APP_POLL_TIME || "150");
export const STALE_TIME = 1000 * 60 * parseInt(import.meta.env.VITE_APP_STALE_TIME || "10");
export const CACHE_TIME = 1000 * 60 * parseInt(import.meta.env.VITE_APP_CACHE_TIME || "10");

export const HTTP_PROTOCOLS = ["http://www", "https://www", "http://", "https://"];
const mainHostsString = import.meta.env.VITE_APP_MAIN_HOSTS;
export const MAIN_HOSTS = mainHostsString ? mainHostsString.split(",") : [];
const affiliateWhiteLabelHosts = import.meta.env.VITE_APP_MSP_CLIENT_WHITELABEL_HOSTS;
export const AFFILIATE_WHITELABEL_HOSTS = affiliateWhiteLabelHosts ? affiliateWhiteLabelHosts.split(",") : [];
const defaultSubdomainsString = import.meta.env.VITE_APP_DEFAULT_SUBDOMAINS;
export const DEFAULT_SUBDOMAINS = defaultSubdomainsString ? defaultSubdomainsString.split(",") : [];
const whiteLabelIgnore = import.meta.env.VITE_APP_MSP_CLIENT_WHITELABEL_IGNORE;
export const AFFILIATE_WHITELABEL_IGNORE = whiteLabelIgnore ? whiteLabelIgnore.split(",") : [];
export const EXCEPTION_2FA = import.meta.env.VITE_APP_EXCEPTION_2FA;
export const CP_HELP_URL = import.meta.env.VITE_APP_CP_HELP_URL;
export const CP_INFO_URL = import.meta.env.VITE_APP_CP_INFO_URL;
type TDefaultQueryConfigs = (params?: {
    refetch?: {onWindowFocus?: boolean; onReconnect?: boolean; onMount?: boolean};
}) => Omit<UseQueryOptions<any, any, any, any>, "queryKey" | "queryFn" | "initialData"> & {
    initialData?: () => undefined;
};
export const DEFAULT_QUERY_CONFIGS: TDefaultQueryConfigs = (params?: {
    refetch?: {onWindowFocus?: boolean; onReconnect?: boolean; onMount?: boolean};
}) => ({
    staleTime: STALE_TIME,
    cacheTime: CACHE_TIME,
    refetchOnWindowFocus: params?.refetch?.onWindowFocus ?? false,
    refetchOnReconnect: params?.refetch?.onReconnect ?? false,
    refetchOnMount:
        params?.refetch?.onMount !== undefined ? params.refetch.onMount : query => query.state.isInvalidated,
    retry: (failureCount, error) => {
        if (error?.response?.status || error.status === 404) {
            return false;
        }
        return failureCount < 3;
    },
});

export const EVENT_CREATION_MESSAGE = "Your event has been submitted, and will be reviewed by our team.";
export const VENDOR_UNARCHIVING_EVENT_MESSAGE = "Your event has been unarchived, and will be reviewed by our team.";
export const VENDOR_EDITING_APPROVED_EVENT =
    "<b>Thanks for updating your event!</b><br />We’ll review the changes shortly, and you’ll receive an email notification as soon as it’s live again.";

export const ICONS = {
    edit: "FaPencilAlt",
    archive: "RiInboxArchiveFill",
    unarchive: "RiInboxUnarchiveLine",
    delete: "FaTrash",
    draft: "FaEdit",
    approve: "FaThumbsUp",
    reject: "FaThumbsDown",
    awaiting_approval: "FaHourglassHalf",
    info: "HiOutlineInformationCircle",
    duplicate: "HiDuplicate",
};

export const chunkSizeKey = "CHUNK_SIZE_MB";
export const maxSizeKey = "MAX_VIDEO_SIZE_MB";
export const maxParConnKey = "MAX_PARALLEL_CONNECTIONS";
export const chunkConfigKeys = [chunkSizeKey, maxSizeKey, maxParConnKey];
export const addToStack = "Build My Stack";
export const portalAccess = "Portal Access Granted";
export const portalRequestPending = "Portal Request Pending";
export const portalNoAccess = "Portal Access Not Requested";
export const missedMessageText = "Message Waiting";
export const maxImageUploadCount = 9;
export const acceptedImageFormats = "JPEG, JPG, PNG, GIF, SVG, WebP";
export const acceptedImageForBrandable = "JPEG, JPG, PNG";
export const acceptedDocumentFormats = "DOC, DOCX, PPT, PPTX, PDF, RTF, XLS, XLSX, TXT, CSV, ODP, KEY";
export const acceptedVideoFormats = "MP4, MOV, OG* & WEBM";
export const acceptedDocumentOrImageOrVideoFormats = [
    acceptedImageFormats,
    acceptedDocumentFormats,
    acceptedVideoFormats,
].join(", ");
export const eventDescriptionLimit = 2500;
export const brandableUploadTooltipTitle = `Accepted assets formats are ${acceptedImageForBrandable}`;
export const productDescriptionLimit = 750;
export const MAX_PROFILE_DESCRIPTION_LENGTH = 1500;
export const FILE_DESCRIPTION_MAX_LENGTH = 500;
export const PRODUCT_FEATURE_DESCRIPTION_MAX_LENGTH = 255;
export const PRICING_DESCRIPTION_MAX_LENGTH = 125;
export const BULLETIN_MESSAGE_MAX_LENGTH = 500;
export const MASS_MESSAGE_MAX_LENGTH = 2500;
export const downloadFolderMsg =
    "Your export will start soon. Feel free to close this message and keep using the application. Please avoid closing your browser, as doing so will interrupt the export process.";
export const referralIdParam = "referral_id";
export const getReferralId = () => sessionStorage.getItem(referralIdParam);
export const storedReferralRegister = "storedReferralRegister";
export const reviewTitleLimit = 85;
export const DOWNLOAD_STARTING_MESSAGE =
    "Your download will start shortly. Please don't close the tab or your browser.";
export const PERMANENT_ACTION_CONFIRMATION = "This cannot be undone. Please confirm if you’d like to proceed.";
export const PAGE_INSUFFICENT_PERMISSIONS =
    "We're sorry, you don't appear to have the correct permissions to access that page";
export const noResultsFoundOnSearchOrFilter =
    "No results found based on your search or filter criteria. Please update your search or filter and try again.";
export const MSP_CONTRACT_TEXT = {
    VENDOR_CONTRACTS: "Manage Contracts",
    CONTRACT: "Contract",
    ADD: "Add Contract",
};
export const BETTERTRACKER_CONTRACT_TEXT = "ContractTracker";
export const MSP_BRAND_TEXT = {
    ADD_CUSTOMER: "Add Customer",
    CUSTOMERS: "Customers",
    MANAGE_CUSTOMERS: "Manage Customers",
    CUSTOMER: "Customer",
};
export const PARENT_STACK_COUNT_TEXT = "in the Stack";
export const MSP_STACK_COUNT_TEXT = "on your Stack";
export const CUSTOMER_STACK_COUNT_TEXT = "on the Stack";

export const ANNOUNCEMENTS_TEXT = "Announcements"; //? Previously "Bulletins"
export const ANNOUNCEMENT_TEXT = "Announcement";
export const NAVISTACK_SPONSORED_LABEL = "Sponsored By";
export const REQUEST_DEMO_TEXT = "Request a Demo";
export const DISTRIBUTOR_LINE_CARD_TEXT = "Vendor Line Card";

export const MY_CUSTOMERS_ADD_IMPORT_TEXT = "Add or import your customers.";
export const NOT_CUSTOMERS_FOUND_TEXT =
    "No customers found. Click Manage Customers to add or import customers. Once added, you can assign the installed product to them.";

export const UNSUBSCRIBE_TITLE_TEXT = "Are you sure you want to unsubscribe?";
export const ADMIN_UNSUBSCRIBE_TITLE_TEXT = "Unsubscribe Confirmation";

export const UNSUBSCRIBE_OK_BTN_TEXT = "Yes, Unsubscribe";

export const UNSUBSCRIBE_CANCEL_BTN_TEXT = "No, Keep me subscribed";

export const ADMIN_UNSUBSCRIBE_CANCEL_BTN_TEXT = "No, Keep subscribed";
export const COMPANY_TYPES_FILTER = ["CHANNEL_PROGRAM", "MY_IT_SPEND", "MSP_LOCATION"];

export const CP_FLAGGED_USER_MESSAGE = "A Channel Program admin flagged this user, contact us to learn more.";

export const CHANNEL_DEAL_REQUEST_SENT =
    "Your request for the deal has been submitted and is under review. You'll receive further details shortly.";

export const CHANNEL_DEAL_INVITE_EMAIL_SENT =
    "<p>To proceed, please complete your account setup.</p> <br> <p>An invitation to register has been sent to <b>{{email_address}}</b>. Follow the link in the email to finish creating your account.</p> <br> <p>Once your account setup is complete, we’ll review your request and share the next steps with you.</p>";

export const CHANNEL_DEAL_INVITE_EXISTING_USER_SENT =
    "<p>An email confirmation has been sent to <b>{{email_address}}</b> with a link to sign in. </p> <p>Once logged in, your request will be reviewed, and we’ll keep you updated on the next steps.</p>";

export const CHANNEL_DEAL_PENDING_INVITE_REGISTER =
    "<p>No invitation email was sent because the company <b>{{company_name}}</b> and email <b>{{email_address}}</b> already exist in the Channel Program.</p> <p>Please log in to your account or check your email for the invitation if you haven't completed the registration yet. </p>";

export const PORTAL_VISIBILITY_LABEL = "Select the appropriate level of visibility";
export const REVIEW_TITLE_REQUIRED = "A title is required";

export const REVIEW_TITLE_MIN_CHARACTERS = "Your review title must be at least 5 characters";

export const MSP_LOCATION_STACK_LABEL = "Corporation";
export const AFFILIATE_STACK_LABEL = "Brand";

export const getStackTypeLabel = (useLocationLabel: boolean) =>
    useLocationLabel ? MSP_LOCATION_STACK_LABEL : AFFILIATE_STACK_LABEL;

export const SYNCING_PLAID =
    "Your expenses are currently syncing. This process may take up to 10 minutes or longer, depending on the number of accounts and transactions being linked.";

export const ACCOUNT_NOT_FOUND = "We`re unable to find your account. Please confirm your email address and try again.";
