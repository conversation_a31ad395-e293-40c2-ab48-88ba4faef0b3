export const QUESTION_TYPES = {
    MULTIPLE_ANSWER: "MultipleAnswer",
    SINGLE_ANSWER: "SingleAnswer",
    STAR_RATING: "StarRating",
    SCALE: "Scale",
    OPEN_TEXT: "OpenText",
    SINGLE_LINE_TEXT: "SingleLineText",
    RADIO_BUTTONS: "RadioButton",
};

// ? Question Keys get returned by BE and should not change
export const QUESTION_KEYS = {
    RECOMMENDATION_RATING: "RECOMMENDATION_RATING",
    JOB_TITLE: "JOB_TITLE",
    ADDITIONAL_FEATURES: "ARTHANFEORFUTHY<PERSON><PERSON>IT<PERSON>EINVESOINTHFUPLBESP",
    LAST_ACTIVE: "WHDIYOL<PERSON>CUSTHTE",
};

export const REVIEW_STATUS = {
    ABANDONED: "abandoned",
    APPROVED: "approved",
    FLAGGED: "flagged",
    SUBMITTED: "submitted",
    UNDER_REVIEW: "under_review",
    ARCHIVED: "archived",
};
