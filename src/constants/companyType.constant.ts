const CompanyType = {
    CHANNEL_PROGRAM: "CHANNEL_PROGRAM",
    CONFERENCE_EVENTS: "CONFERENCE_EVENTS",
    DISTRIBUTOR: "Distributor",
    ELSE: "ELS<PERSON>",
    FINANCE_ALL: "FINANCE_ALL",
    FRANCHISE_MSP: "FRANCHISE_MSP",
    FRANCHISE_CORPORATE_MSP: "FRANCHISE_CORPORATE_MSP",
    INDIVIDUAL: "INDIVIDUAL",
    ISP_ALL: "ISP_ALL",
    ITConsultant: "ITConsultant",
    MASTER_AGENT: "MASTER_AGENT",
    Media: "Media",
    MSP_CLIENT: "MSP_CLIENT",
    MSP: "MSP",
    MSSP: "MSSP",
    PEER_GROUP: "PEER_GROUP",
    VAR: "VAR",
    VENDOR: "Vendor",
    VENDOR_ALL: "VENDOR_ALL",
    DIRECT: "DIRECT",
    MSP_LOCATION: "MSP_LOCATION",
} as const;

type CompanyType = (typeof CompanyType)[keyof typeof CompanyType];

export default CompanyType;

export type TCompanyTypeObj = typeof CompanyType;
export type TCompanyType = (typeof CompanyType)[keyof typeof CompanyType];
export const MSPCompanyTypes: string[] = [
    CompanyType.MSP,
    CompanyType.MSSP,
    CompanyType.FRANCHISE_MSP,
    CompanyType.FRANCHISE_CORPORATE_MSP,
    CompanyType.ISP_ALL,
    CompanyType.INDIVIDUAL,
    CompanyType.VAR,
];
export const VendorCompanyTypes: string[] = [
    CompanyType.CHANNEL_PROGRAM,
    CompanyType.CONFERENCE_EVENTS,
    CompanyType.Media,
    CompanyType.MASTER_AGENT,
    CompanyType.DISTRIBUTOR,
    CompanyType.VENDOR,
    CompanyType.VENDOR_ALL,
    CompanyType.FINANCE_ALL,
    CompanyType.PEER_GROUP,
    CompanyType.ELSE,
    CompanyType.ITConsultant,
];
