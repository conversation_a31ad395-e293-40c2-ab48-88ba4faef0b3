import {colorPalette} from "../themes/themes.constant";
import Badge from "../uicomponents/Atoms/Badge/badge.component";
import Typography from "../uicomponents/Atoms/Typography/Typography.component";
import FileDownloadDoneIcon from "@mui/icons-material/FileDownloadDone";
import WarningIcon from "@mui/icons-material/Warning";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import Box from "../uicomponents/Atoms/Box/Box.component";

export const ADOPTION_STATUS = {
    ADDED_TO_STACK: "ADDED_TO_STACK",
    MISSING: "MISSING",
    AFFILIATE_CHOICE: "AFFILIATE_CHOICE",
};

const defaultAdoptionStatusBadgeProps = {
    sx: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: 12,
        width: 12,
        maxWidth: 12,
        overflow: "hidden",
        padding: 0,
        margin: "0 !important",
    },
    style: {
        padding: "2px",
        margin: 0,
        minWidth: 16,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
    },
    padding: "0px",
};

export const ADOPTION_STATUS_ICON = {
    ADDED_TO_STACK: (
        <Badge color="success" {...defaultAdoptionStatusBadgeProps}>
            <Box padding="2px">
                <FileDownloadDoneIcon color="success" sx={{height: 12, width: 12}} fontSize="small" />
            </Box>
        </Badge>
    ),
    MISSING: (
        <Badge color="warning" {...defaultAdoptionStatusBadgeProps}>
            <Box padding="2px">
                <WarningIcon color="warning" sx={{height: 12, width: 12}} fontSize="small" />
            </Box>
        </Badge>
    ),
    AFFILIATE_CHOICE: (
        <Badge color="blue" {...defaultAdoptionStatusBadgeProps}>
            <Box padding="2px">
                <PersonAddIcon htmlColor={colorPalette.blue[600]} sx={{height: 12, width: 12}} fontSize="small" />
            </Box>
        </Badge>
    ),
};

export const ADOPTION_STATUS_TOOLTIP_TEXT = {
    ADDED_TO_STACK: "Adopted category and/or product",
    MISSING: "Category and/or product yet to be adopted",
    AFFILIATE_CHOICE: "Added by Location",
};

export const ADOPTION_STATUS_BADGE = {
    ADDED_TO_STACK: (
        <Badge
            color="success"
            sx={{display: "flex", alignItems: "center", gap: "4px"}}
            title={ADOPTION_STATUS_TOOLTIP_TEXT[ADOPTION_STATUS.ADDED_TO_STACK]}>
            <FileDownloadDoneIcon color="success" sx={{height: 12, width: 12}} />
            <Typography color={colorPalette.success[700]} variant="subtitle4" fontInter fontSize="11px !important">
                Added to Stack
            </Typography>
        </Badge>
    ),
    MISSING: (
        <Badge
            color="warning"
            sx={{display: "flex", alignItems: "center", gap: "4px"}}
            title={ADOPTION_STATUS_TOOLTIP_TEXT[ADOPTION_STATUS.MISSING]}>
            <WarningIcon color="warning" sx={{height: 12, width: 12}} />
            <Typography color={colorPalette.warning[800]} variant="subtitle4" fontInter fontSize="11px !important">
                Not on Stack
            </Typography>
        </Badge>
    ),
    AFFILIATE_CHOICE: (
        <Badge
            color="blue"
            sx={{display: "flex", alignItems: "center", gap: "4px"}}
            title={ADOPTION_STATUS_TOOLTIP_TEXT[ADOPTION_STATUS.AFFILIATE_CHOICE]}>
            <PersonAddIcon htmlColor={colorPalette.blue[600]} sx={{height: 12, width: 12}} />
            <Typography color={colorPalette.blue[600]} variant="subtitle4" fontInter fontSize="11px !important">
                Added by Location
            </Typography>
        </Badge>
    ),
};
