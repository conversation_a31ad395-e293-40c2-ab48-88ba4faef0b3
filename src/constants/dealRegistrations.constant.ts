import {IDealRegistration, IUpdateDealRegistration} from "../Interfaces/dealRegistrations.interface";

export const DEAL_STATUS = {
    DRAFT: "draft",
    PENDING: "pending",
    APPROVED: "approved",
    INFO_REQUESTED: "needInfo",
    DECLINED: "declined",
    WIT<PERSON>RAWN: "withdrawn",
} as const;

export type DEAL_STATUS_TYPES = (typeof DEAL_STATUS)[keyof typeof DEAL_STATUS];

export const DEAL_STATUS_LABELS = {
    [DEAL_STATUS.DRAFT]: "Draft",
    [DEAL_STATUS.PENDING]: "Pending",
    [DEAL_STATUS.APPROVED]: "Approved",
    [DEAL_STATUS.INFO_REQUESTED]: "Info Requested",
    [DEAL_STATUS.DECLINED]: "Declined",
    [DEAL_STATUS.WITHDRAWN]: "Withdrawn",
};

export const PARTNERSHIP_STATUS = {
    CURRENT_PARTNER: "current_partner",
    PROSPECT: "prospect",
    LEAD: "lead",
    OPPORTUNITY: "opportunity",
    OTHER: "other",
};

export const CONTACT_METHODS = {
    EMAIL: "email",
    PHONE: "phone",
};

export const DEAL_DOCUMENT_TYPE = "document";

export const DEAL_EXPIRATION_OPTIONS = [
    {id: "30", label: "30 days"},
    {id: "60", label: "60 days"},
    {id: "90", label: "90 days"},
    {id: "120", label: "120 days"},
    {id: "null", label: "No expiration date"},
];

export interface ICreateDealUpdateBody extends Omit<IDealRegistration, "company_id" | "id" | "owner_id" | "name"> {
    company_id?: string;
    status_reason?: string | null;
    id?: string;
    owner_id?: string;
    name?: string;
}

export const createDealUpdateBody = (deal: ICreateDealUpdateBody) => {
    const body = {
        id: deal.id,
        company_id: deal.owner?.id || deal?.company_id,
        product_id: deal.product.id,
        name: deal.name,
        scope: String(deal.scope) || null,
        size: deal.size || null,
        // size_description: deal.size_description,
        status: deal.status || DEAL_STATUS.PENDING,
        expiration_days: Number(deal.expiration_days) || null,
        // partnership_status: deal.partnership_status || null,
        notes: deal.notes,
        description: deal.description,
        company_details: deal.company_details || null,
        stakeholders: deal.stakeholders || [],
    } as IUpdateDealRegistration;
    if (deal?.status_reason) {
        body["status_reason"] = deal.status_reason;
    }
    if (deal?.reason_description) {
        body["reason_description"] = deal.reason_description;
    }
    return body;
};
