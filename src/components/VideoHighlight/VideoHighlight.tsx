import {useEffect, useRef, useState} from "react";
import ReactPlayer from "react-player";
import {PlayIcon} from "../Icons";
import styles from "./videoHighlight.module.sass";

interface IPlayer {
    videoUrl: string;
    onStart?: Function;
    videoId?: string;
    light?: boolean;
    autoPlay?: boolean;
    download?: boolean;
    className?: string;
    thumbnail?: string;
}

export function VideoHighlight(props: IPlayer) {
    const [isPlaying, setIsPlaying] = useState(false);
    const videoWrapperRef = useRef<any>();
    const handleStart = () => {
        props.onStart && props.onStart();
    };

    useEffect(() => {
        if (props.videoUrl) {
            setTimeout(() => {
                const videoTag = videoWrapperRef.current?.querySelector("video");
                if (videoTag) {
                    videoTag.src = props.videoUrl;
                    // videoTag.load();
                }
            }, 0);
        }
    }, [props.videoUrl, videoWrapperRef.current]);

    return (
        <div className={`${styles.videoContainer} ${props.className || ""}`} ref={videoWrapperRef}>
            {props.thumbnail && !isPlaying ? (
                <div className={styles.thumbnailContainer} onClick={() => setIsPlaying(true)}>
                    <div className={styles.playIcon}>
                        <PlayIcon size={80} />
                    </div>
                    <img src={props.thumbnail} alt="Thumbnail" className={styles.thumbnail} />
                </div>
            ) : null}
            <ReactPlayer
                onStart={handleStart}
                url={props.videoUrl}
                width="100%"
                height="100%"
                style={{position: "absolute", top: "0", left: "0"}}
                controls={true}
                light={props.light}
                autoPlay={props.autoPlay}
                playing={isPlaying}
                config={{
                    file: {attributes: {controlsList: props.download ? "" : "nodownload"}},
                    youtube: {
                        playerVars: {modestbranding: 1},
                    },
                }}
                onError={e => console.log("Error : ", e.target.error, props.videoUrl)}
            />
        </div>
    );
}
