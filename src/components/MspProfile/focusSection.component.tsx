import {useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {useCompany} from "../../hooks/fetches/useCompany";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import useSettings from "../../hooks/useSettings";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IFocus, IFocusOption} from "../../Interfaces/msp.interface";
import {MutateTypes} from "../../Interfaces/queries.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {mspService} from "../../services/msp.service";
import styles from "../../styles/components/focusSection.module.sass";
import {getContrastColor, getShadeColor} from "../../utils/color.util";
import {getErrorFromArray} from "../../utils/error.util";
import {formatStringToPercentage} from "../../utils/formatString.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import {EditIcon} from "../Icons";
import BrandConnector from "../Logo/brandConnector.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import EditFocusModal from "./editFocusModal.component";

interface IProps {
    entityId: string;
    friendlyUrl: string;
    vendorType?: string;
    claimerId?: string;
}

const focusColors = {
    "0": "#7f6000",
    "1": "#274e13",
    "2": "#073763",
    "3": "#0c343d",
};

export default function FocusSection(props: IProps) {
    const [editFocusModal, setEditFocusModal] = useState(false);
    const [editFocus, setEditFocus] = useState({} as IFocus);

    const {authState} = useAuthState();
    const {userCompanies} = useActiveCompany();
    const {company, updateCompany} = useCompany(props.friendlyUrl, {isCompany: true, isMsp: true});
    const {settings, updateSettings} = useSettings();
    const {hasPermissions} = usePermissions();
    const hasAdminEditAccess = hasPermissions([PERMISSION_GROUPS.ADMIN_COMPANY_MNGMT_UPDATE]);
    const info = company.data;
    const focuses = info?.focuses || [];
    const isEditable = authState?.id
        ? authState.id === props.claimerId ||
          hasAdminEditAccess ||
          !!userCompanies?.find(c => c.friendly_url === props.friendlyUrl)
        : false;
    const queryClient = useQueryClient();
    const notify = useNotification();

    const randomHexColor = () => {
        const hex = Math.floor(Math.random() * 16777215).toString(16);
        return `#${hex}`;
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleEditFocus = (focus: IFocus) => {
        setEditFocus(focus);
        setEditFocusModal(true);
    };

    const handleSaveFocus = (updatedFocus: IFocus) => {
        if (!focuses.length) return;
        const updatedFocusesObject = focuses.map(f => (f.id === updatedFocus.id ? updatedFocus : f));
        updateCompany.mutate({
            focuses: updatedFocusesObject,
            mutate_type: MutateTypes.LocalUpdate,
        });
    };

    const onSuccessLoadFocuses = async (response: AxiosResponse<Array<IFocus>>) => {
        let focuses = await response.data;
        focuses = focuses.filter(focus => focus.options.length !== 0);
        updateSettings(UPDATE_SETTINGS, {all_focuses: focuses});
    };

    useEffect(() => {
        if (props.friendlyUrl) {
            queryClient.refetchQueries(["company", props.friendlyUrl]);
        }
        //eslint-disable-next-line
    }, [props.friendlyUrl]);

    useEffect(() => {
        if (settings.all_focuses || !props.friendlyUrl) return;
        mspService.getAllFocuses(props.friendlyUrl, onSuccessLoadFocuses, handleError);
        //eslint-disable-next-line
    }, [settings.all_focuses, focuses, props.friendlyUrl]);

    return (
        <>
            <TitleContainer as="h2" text="Focus" noConnector position="start" className="mb-0" />
            <div className={styles.focusContainer}>
                {!focuses && !settings.all_focuses ? (
                    <div>
                        <Loader inline loading />
                    </div>
                ) : focuses.length === 0 ? (
                    <div>
                        <SubtitleContainer as="span" text={"No focuses."} />
                    </div>
                ) : (
                    focuses?.map((focus: IFocus, index) => {
                        if (!settings.all_focuses?.find(f => f.id === focus.id)) return null;
                        const backgroundColor = focusColors[index % 4] || randomHexColor();
                        return (
                            <div key={focus.id} className={styles.focusItem}>
                                <div className={styles.focusItemTitle}>
                                    <BrandConnector />
                                    <p className="mx-3 my-3">{focus.name}</p>
                                    {isEditable && (
                                        <>
                                            <ButtonComponent
                                                onClick={() => {
                                                    handleEditFocus(focus);
                                                }}
                                                type="button"
                                                id={`editFocus-${focus.id}`}
                                                className={styles.editBtn}>
                                                <EditIcon size={12} />
                                            </ButtonComponent>
                                        </>
                                    )}
                                </div>
                                <div className="d-flex align-items-start justify-content-center flex-wrap">
                                    {focus.options.length > 0 &&
                                        focus?.options?.map((option: IFocusOption) => {
                                            const transparency = 100 - parseFloat(option.percentage!);
                                            const shadeColor = getShadeColor(backgroundColor, transparency);
                                            const contrastColor = getContrastColor(shadeColor);
                                            const circleStyling = {
                                                backgroundColor: shadeColor,
                                                color: contrastColor,
                                            };
                                            return (
                                                <div
                                                    key={option.id}
                                                    className={`d-flex flex-column align-items-center justify-content-center m-2 ${styles.optionContainer}`}>
                                                    <div className={styles.focusCircle} style={circleStyling}>
                                                        {formatStringToPercentage(option.percentage!)}
                                                    </div>
                                                    <p className="mt-2">{option.name}</p>
                                                </div>
                                            );
                                        })}
                                </div>
                            </div>
                        );
                    })
                )}
                {editFocusModal && info && (
                    <EditFocusModal
                        friendlyUrl={props.friendlyUrl}
                        focus={editFocus}
                        onClose={() => setEditFocusModal(false)}
                        companyId={info?.id}
                        onSave={handleSaveFocus}
                    />
                )}
            </div>
        </>
    );
}
