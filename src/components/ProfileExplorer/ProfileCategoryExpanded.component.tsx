import React, {useEffect, useState} from "react";
import styles from "../../styles/components/profileCard.module.sass";
import {explorerService} from "../../services/explorer.service";
import {AxiosError} from "axios";
import ExplorerCard from "../Explorer/explorerCard.component";
import Loader from "../../utils/loader";
import {IMeta} from "../../Interfaces/pagination.interface";
import useNotification from "../../hooks/useNotification";
import {getErrorFromArray} from "../../utils/error.util";
import {
    ExplorerProfileInfluencerAndUserType,
    ExplorerProfileVendorType,
} from "../../Interfaces/ProfileExplorer.interface";
import {IFilter} from "../../Interfaces/filters.interface";

interface IProps {
    selectedFilter: IFilter;
}

const ProfileCategoryExpanded: React.FC<IProps> = (props: IProps) => {
    const {selectedFilter} = props;
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
    const [metaData, setMetaData] = useState<IMeta | null>(null);
    const [profiles, setProfiles] = useState<ExplorerProfileInfluencerAndUserType[] | ExplorerProfileVendorType[]>([]);
    const notify = useNotification();

    const onSuccessGetProfiles = (res: any) => {
        if (res.data.meta.current_page === 1) {
            setProfiles(res.data.data);
        } else {
            const updatedProfiles = [...profiles, ...res.data.data];
            setProfiles(updatedProfiles);
        }
        setMetaData(res.data.meta);
        setIsLoadingMore(false);
        setIsLoading(false);
    };

    const onErrorGetProfiles = (err: AxiosError<any>) => {
        setIsLoadingMore(false);
        setIsLoading(false);
        notify(getErrorFromArray(err), "Error");
    };

    const handleExploreMore = () => {
        if (metaData?.current_page) {
            const nextPage = metaData.current_page + 1;
            setIsLoadingMore(true);
            explorerService.GetExplorerProfilesByType(
                selectedFilter.value,
                nextPage,
                "",
                onSuccessGetProfiles,
                onErrorGetProfiles,
            );
        }
    };

    useEffect(() => {
        setIsLoading(true);
        window.scrollTo(0, 0);
        explorerService.GetExplorerProfilesByType(
            selectedFilter.value,
            1,
            "",
            onSuccessGetProfiles,
            onErrorGetProfiles,
        );
        //eslint-disable-next-line
    }, [selectedFilter]);

    return (
        <div className={styles.filteredCategoryContainer}>
            <div className="d-flex justify-content-center align-items-center mb-5">
                <span className={styles.categoryTitle}>{selectedFilter.label}</span>
            </div>
            <div className="d-flex flex-row flex-wrap gap-3 px-3">
                {!isLoading ? (
                    profiles.length > 0 ? (
                        profiles.map((profile: ExplorerProfileInfluencerAndUserType | ExplorerProfileVendorType) => {
                            let type: string = "";
                            if ("is_company" in profile) {
                                type = profile.type;
                            } else {
                                type = profile.profile_type?.value;
                            }
                            return (
                                <ExplorerCard
                                    entity={{
                                        ...profile,
                                        friendly_url: profile.friendly_url || profile.id,
                                        avatar: profile.avatar || "",
                                        type,
                                    }}
                                    key={profile.id}
                                    className={styles.profileExplorerCard}
                                />
                            );
                        })
                    ) : (
                        <span>No Results...</span>
                    )
                ) : (
                    <div className="d-flex w-100 align-items-center justify-content-center">
                        <Loader inline loading big />
                    </div>
                )}
                {!isLoading && (metaData?.current_page || 0) < (metaData?.last_page || 0) ? (
                    <div className="w-100 justify-content-center d-flex flex-row">
                        {isLoadingMore ? (
                            <Loader inline loading />
                        ) : (
                            <span onClick={handleExploreMore} style={{color: "var(--bs-blue)", cursor: "pointer"}}>
                                Explore More
                            </span>
                        )}
                    </div>
                ) : null}
            </div>
        </div>
    );
};

export default ProfileCategoryExpanded;
