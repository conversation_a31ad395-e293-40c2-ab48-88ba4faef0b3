import Col from "react-bootstrap/Col";
import Card from "react-bootstrap/Card";
import styles from "../../styles/pages/episode.module.sass";
import {DATE_FORMAT, formatDate} from "../../utils/formatDate";
import {useEffect, useState} from "react";
import {Link} from "react-router-dom";
import {useNavigate} from "react-router-dom";
import pathConfig from "../../constants/pathConfig";

interface IEpData {
    thumbnail: any;
    title: string;
    publishDate: string;
    description: string;
    channel: string;
    className?: string;
    videoUrl: string;
    pid: string;
    hideBtn?: boolean;
    horizontal?: boolean;
}

export default function EpisodeComponent(props: IEpData) {
    const [dateFormatted, setDateFormatted] = useState<string>();
    const navigate = useNavigate();

    const navigateToEpisode = (data: IEpData) => {
        navigate({
            pathname: pathConfig.Podcast,
            search: `?pid=${data.pid}`,
        });
    };

    useEffect(() => {
        setDateFormatted(formatDate(props.publishDate, DATE_FORMAT));
    }, [props.publishDate]);

    return props.title.toLowerCase() === "private video" ? (
        <></>
    ) : (
        <Col
            className={`col-12 col-md-6 col-lg-3 ${styles.cardContainer} ${props.className} ${
                props.horizontal && "p-0"
            }`}>
            <Card
                className={`border-0 h-100 cPointer ${props.horizontal && "flex-row align-items-center"}`}
                onClick={() => navigateToEpisode(props)}>
                <div className={`${props.horizontal ? styles.cardImgContHori : styles.cardImgContainer}`}>
                    <Card.Img variant="top" src={props.thumbnail && props.thumbnail.url} className={styles.cardImg} />
                </div>
                <Card.Body
                    className={`h-100 p-0 d-flex ${
                        !props.horizontal ? "px-2 mx-1 justify-content-between flex-column" : ""
                    }`}>
                    <div>
                        {props.horizontal ? null : (
                            <p className={styles.dateText}>
                                <small>{dateFormatted}</small>
                            </p>
                        )}
                        <Card.Title>
                            {props.horizontal ? (
                                <h4 className={!props.horizontal ? styles.cardTitle : styles.cardTitleHori}>
                                    {props.title.length > 60 ? props.title.substring(0, 60) + "..." : props.title}
                                </h4>
                            ) : (
                                <h4 className={!props.horizontal ? styles.cardTitle : styles.cardTitleHori}>
                                    {props.title.length > 75 ? props.title.substring(0, 75) + "..." : props.title}
                                </h4>
                            )}
                        </Card.Title>
                        {props.horizontal ? (
                            <p className={styles.dateTextHori}>
                                <small>{dateFormatted}</small>
                            </p>
                        ) : null}
                        {props.horizontal ? null : (
                            <Card.Text className="secondaryText">
                                {props.description.length > 110
                                    ? props.description.substring(0, 110) + "..."
                                    : props.description}
                            </Card.Text>
                        )}
                    </div>
                    {props.hideBtn ? null : (
                        <div className="w-100 text-center mt-4">
                            <Link className="cpPodcastBtn" to={"/podcast?pid=" + props.pid} target="_self">
                                Watch now
                            </Link>
                        </div>
                    )}
                </Card.Body>
            </Card>
        </Col>
    );
}
