import {IPaginationData} from "../../Interfaces/pagination.interface";
import styles from "../../styles/pages/Categories.module.sass";
import {PaginationButton} from "../Pagination/paginationButton.component";
import DirectoryItem from "./directoryItem.component";
import {DirectoryListType} from "./directoryList.enum";

interface IProps {
    items: IPaginationData;
    categoryId: string;
    friendlyUrl: string;
    listType: DirectoryListType;
    productParents?: any[];
    onSuccessLoadMore: (response: any) => void;
}

export const DirectoryList = (props: IProps) => {
    return (
        <ul className={props.listType === DirectoryListType.Products ? styles.productList : styles.vendorList}>
            {props?.items?.data?.map((item, index) => {
                return (
                    <DirectoryItem
                        index={index}
                        item={item}
                        productParents={props.productParents}
                        listType={props.listType}
                        key={item.id}
                    />
                );
            })}
            <div className="text-center mt-4">
                {props.items?.meta.current_page !== props.items?.meta.last_page && (
                    <PaginationButton
                        text="Load More"
                        id="loadMoreDirectory"
                        url={
                            props.items?.links.next! +
                            (props.listType === DirectoryListType.Products ? "&subcategory_id=" + props.categoryId : "")
                        }
                        method="get"
                        onGetResult={props.onSuccessLoadMore}
                        className="cpMainBtn"
                    />
                )}
            </div>
        </ul>
    );
};
