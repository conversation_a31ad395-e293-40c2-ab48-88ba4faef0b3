import {useMemo} from "react";
import CPTooltip, {DEFAULT_TOOLTIP, ITooltip} from "../../uicomponents/Atoms/Tooltip/tooltip.component";
import {makeAbsoluteUrl} from "../../utils/url.util";

interface IOutboundLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
    referred?: boolean;
    tooltip?: ITooltip;
}

/**
 * Component for rendering an outbound link with customizable attributes.
 *
 * @component
 * @param {boolean} [referred=false] - Whether the link should include "noreferrer" attribute.
 * @param {...React.AnchorHTMLAttributes<HTMLAnchorElement>} props - Additional attributes for the anchor element.
 * @returns {JSX.Element} - Rendered outbound link.
 */
export default function OutboundLink({referred = false, tooltip = DEFAULT_TOOLTIP, ...props}: IOutboundLinkProps) {
    const isMailToLink = props.href?.toString().startsWith("mailto:");
    const href = isMailToLink ? props.href : makeAbsoluteUrl(props.href ?? "");
    const rel = useMemo(() => {
        const list = new Set([...(props?.rel || "").split(" "), "noreferrer", "noopener"]);
        if (referred) list.delete("noreferrer");
        return [...list].join(" ");
    }, [props]);

    return (
        <CPTooltip {...tooltip}>
            <a {...props} href={href} rel={rel} target={props?.target || "_blank"}>
                {props.children}
            </a>
        </CPTooltip>
    );
}
