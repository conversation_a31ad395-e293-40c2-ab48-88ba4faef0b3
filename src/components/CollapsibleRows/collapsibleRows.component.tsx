import {ArrowForwardIos} from "@mui/icons-material";
import {ReactNode, useState} from "react";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import CPTooltip from "../../uicomponents/Atoms/Tooltip/tooltip.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";

interface ICollapsibleRow {
    children: ReactNode;
    title: string;
    additionalAction?: ReactNode;
}

interface Props {
    rows: ICollapsibleRow[];
}

export default function CollapsibleRows({rows}: Props) {
    const [collapsed, setCollapsed] = useState<number[]>([]);

    const renderIcon = (index: number) => {
        if (collapsed.includes(index)) {
            return (
                <ArrowForwardIos
                    onClick={() => setCollapsed(prev => prev.filter(i => i !== index))}
                    role="button"
                    sx={{
                        fontSize: "24px",
                        color: "blue.600",
                        transform: "rotate(90deg)",
                    }}
                />
            );
        }
        return (
            <ArrowForwardIos
                onClick={() => setCollapsed(prev => [index, ...prev])}
                role="button"
                sx={{
                    fontSize: "24px",
                    color: "blue.600",
                    transform: "rotate(270deg)",
                }}
            />
        );
    };
    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                width: "100%",
            }}>
            {rows.map((row, index) => {
                return (
                    <Box
                        key={index}
                        sx={{
                            flex: "1 1 auto",
                            width: "100%",
                            display: "flex",
                            flexDirection: "column",
                            backgroundColor: index % 2 !== 0 ? "neutral.200" : "neutral.100",
                            border: "1px solid",
                            borderColor: index % 2 !== 0 ? "neutral.100" : "neutral.300",
                            borderRadius: "8px",
                            padding: "18px 8px",
                            transition: "all 0.3s",
                            maxHeight: collapsed.includes(index) ? "60px" : "auto",
                            overflow: "hidden",
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                gap: 2,
                                alignItems: "center",
                                width: "100%",
                                justifyContent: "space-between",
                            }}>
                            <Typography size={20} weight={600} color="neutral.700">
                                {row.title}
                            </Typography>
                            {}
                            {collapsed.includes(index) ? (
                                <CPTooltip title="Expand Section">{renderIcon(index)}</CPTooltip>
                            ) : (
                                <CPTooltip title="Collapse Section">{renderIcon(index)}</CPTooltip>
                            )}
                        </Box>
                        <Box
                            sx={{
                                paddingTop: "16px",
                            }}>
                            {row.children}
                        </Box>
                    </Box>
                );
            })}
        </Box>
    );
}
