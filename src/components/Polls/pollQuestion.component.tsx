import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import useAuthState from "../../hooks/useAuthState";
import usePusherState from "../../hooks/usePusherState";
import useSettings from "../../hooks/useSettings";
import {IPollsOptionsAnswer} from "../../Interfaces/polls.interfaces";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {pollService} from "../../services/poll.service";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import CheckBoxComponent from "../FormControls/checkBox.component";
import TextBoxComponent from "../FormControls/textBox.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import {IPollQuestionProps} from "./pollModal.component";

export function PollQuestion(props: IPollQuestionProps) {
    const [loading, setLoading] = useState(false);
    const [errorController, setErrorController] = useState<boolean>(false);

    const pollMethods = useForm();
    const {pusherState, updatePusherState} = usePusherState();
    const {authState} = useAuthState();
    const {updateSettings} = useSettings();
    const userId = authState.id!;

    let selectedOptions: string[];
    let answersObject: IPollsOptionsAnswer;

    const handleSuccessAnswer = () => {
        setLoading(false);
        pollMethods.reset();
        if (props.activeIndex + 1 === props.questionsCount) {
            updatePusherState(UPDATE_PUSHER, {polls: undefined});
            props.setActiveIndex(0);
            return props.setPollToAnswer(false);
        }
        props.setActiveIndex(props.activeIndex + 1);
    };

    const handleError = (error: AxiosError<any>) => {
        setLoading(false);
        pollMethods.reset();
        const show = true;
        const message = getErrorFromArray(error);
        const finalNotification = {show, message};
        updateSettings(UPDATE_SETTINGS, {notification: finalNotification});
        if (props.activeIndex >= 0 && props.activeIndex + 1 === props.questionsCount) {
            props.setActiveIndex(0);
            return props.setPollToAnswer(false);
        }
        props.setActiveIndex(props.activeIndex! + 1);
    };

    const savePollAnswer = (questionId: string, pollOptions: IPollsOptionsAnswer) => {
        const pollCategory: string = props.question?.poll_category || "";
        pollService.saveAnswer(
            userId!,
            props.eventId!,
            questionId,
            pollOptions.poll_option_id,
            pollOptions.additional_feedback,
            pusherState.polls?.vendor_id!,
            pollCategory,
            handleSuccessAnswer,
            handleError,
        );
    };

    const handlePollAction = data => {
        selectedOptions = [];
        answersObject = {};
        let newData = {...data};
        newData = Object.fromEntries(Object.entries(newData).filter(([_, v]) => v != null));
        newData = Object.fromEntries(Object.entries(newData).filter(([_, v]) => v !== false));
        newData = Object.fromEntries(Object.entries(newData).filter(([_, v]) => v !== ""));
        if (Object.keys(newData).length === 0) {
            setErrorController(true);
            return;
        }
        setErrorController(false);
        setLoading(true);
        if (props.question?.show_additional_feedback) {
            answersObject.additional_feedback = newData[`${props.question?.id}-additional-feedback`];
            delete newData[`${props.question?.id}-additional-feedback`];
        }
        if (typeof Object.values(newData)[0] === "string") {
            selectedOptions = Object.values(newData);
        } else if (typeof Object.values(newData)[0] === "boolean") {
            selectedOptions = Object.keys(newData);
        }
        answersObject.poll_option_id = selectedOptions;
        savePollAnswer(props.question?.id!, answersObject);
    };

    const handlePollFormChange = (e: any) => {
        const {value, id, type} = e.target;
        if (type === "checkbox") {
            pollMethods.setValue(id, e.target.checked);
        } else {
            pollMethods.setValue(id, value);
        }
    };

    useEffect(() => {
        if (props.activeIndex === 0) {
            return;
        }
        if (
            (!props.question?.poll_options || props.question?.poll_options.length < 1) &&
            !props.question?.show_additional_feedback
        ) {
            if (props.activeIndex + 1 > props.questionsCount) {
                return props.setPollToAnswer(false);
            }
            props.setActiveIndex(props.activeIndex + 1);
        }
    }, [props.activeIndex]);

    return (
        <FormProvider {...pollMethods}>
            <div className="text-center">
                <SubtitleContainer as="p" text={props.question?.question} size="20" />
                <div className="my-2">
                    {props.question?.question_type === "SingleAnswer" && (
                        <form onChange={handlePollFormChange} onSubmit={pollMethods.handleSubmit(handlePollAction)}>
                            <div className="d-flex flex-column align-items-start justify-content-center">
                                {props.question?.poll_options.map((option, index) => {
                                    return (
                                        <CheckBoxComponent
                                            type="radio"
                                            id={props.question?.id!}
                                            testid={`${props.question?.question || "option"}-${index}`}
                                            value={option.id}
                                            key={index}>
                                            <SubtitleContainer as="p" text={option.option} size="16" />
                                        </CheckBoxComponent>
                                    );
                                })}
                                {props.question?.show_additional_feedback && (
                                    <div className="my-4 w-100">
                                        <TextBoxComponent
                                            id={`${props.question?.id}-additional-feedback`}
                                            testid={`${props.question?.question}-additional-feedback`}
                                            label="Additional Feedback"
                                        />
                                    </div>
                                )}
                            </div>
                            <ButtonComponent type="submit" disabled={loading} id="continueSingle">
                                {props.activeIndex + 1 === props.questionsCount ? "Submit" : "Next"}
                                {loading && <Loader inline version="iconWhite" loading />}
                            </ButtonComponent>
                        </form>
                    )}
                    {props.question?.question_type === "MultipleChoice" && (
                        <form onChange={handlePollFormChange} onSubmit={pollMethods.handleSubmit(handlePollAction)}>
                            <div className="d-flex flex-column align-items-start justify-content-center">
                                {props.question?.poll_options.map((option, index) => {
                                    return (
                                        <CheckBoxComponent
                                            id={`${option?.id}`}
                                            key={index}
                                            testid={`${props.question?.question || "option"}-${index}`}>
                                            <SubtitleContainer as="p" text={option.option} size="16" />
                                        </CheckBoxComponent>
                                    );
                                })}
                                {props.question?.show_additional_feedback && (
                                    <div className="mt-2 mb-4 w-100">
                                        <TextBoxComponent
                                            id={`${props.question?.id}-additional-feedback`}
                                            testid={`${props.question?.question}-additional-feedback`}
                                            label="Additional Feedback"
                                            containerClassName="mb-0"
                                        />
                                    </div>
                                )}
                            </div>
                            {errorController && (
                                <TextBoxComponent
                                    id={`${props.question?.id}-error`}
                                    testid={`${props.question?.question}-error`}
                                    helperText="Please fill one of the fields before sending"
                                    showError
                                    className="d-none"
                                    containerClassName="d-flex justify-content-center w-100"
                                />
                            )}
                            <ButtonComponent type="submit" disabled={loading} id="continueMultiple">
                                {props.activeIndex + 1 === props.questionsCount ? "Submit" : "Next"}
                                {loading && <Loader inline version="iconWhite" loading />}
                            </ButtonComponent>
                        </form>
                    )}
                </div>
            </div>
        </FormProvider>
    );
}
