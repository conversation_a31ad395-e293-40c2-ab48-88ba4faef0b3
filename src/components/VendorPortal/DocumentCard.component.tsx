import HeadingComponent from "../../uicomponents/Typography/Heading.component";
import {AxiosError, AxiosResponse} from "axios";
import styles from "../../styles/components/vendorPortal.module.sass";
import {Card} from "react-bootstrap";
import {analyticService} from "../../services/analytic.service";
import {useState} from "react";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import Avatar from "@mui/material/Avatar";
import useNotification from "../../hooks/useNotification";
import {getErrorFromArray} from "../../utils/error.util";
import {downloadDoc, openDoc} from "../../utils/downloadDoc.util";
import {vendorProfileService} from "../../services/vendorProfile.service";
import {PinIcon} from "../Icons";
import ParagraphComponent from "../../uicomponents/Typography/Paragraph.component";
import ShareButton from "../../uicomponents/Molecules/ShareButton/shareButton.component";
import ViewDownloadButton from "../../uicomponents/Molecules/ViewDownloadButton/viewDownloadButton.component";
import {removeQueryParam} from "../../utils/url.util";
import {CHANNEL_PROGRAM_COMPANY_ID} from "../../constants/commonStrings.constant";

interface IProps {
    data: any;
    index: number;
    partnerPageId?: string;
    onClickDocument?: Function;
    mainContentId?: string;
    highlighted?: boolean;
    mspId?: string;
    companyId?: string;
    as_company_id?: string;
}

const DocumentCardComponent = (props: IProps) => {
    const [showMore, setShowMore] = useState(false);
    const notify = useNotification();
    const {data} = props;
    const isMSP = !!props.mspId;
    const urlWithoutCid = removeQueryParam(location.href, "cid");
    const isChannelProgram = props?.companyId === CHANNEL_PROGRAM_COMPANY_ID;
    // const isCpClaimer = !props?.as_company_id && props?.companyId === CHANNEL_PROGRAM_COMPANY_ID;
    const storeAnalytic = () => {
        analyticService.storePartnerAnalytic("partnerPortalContentClick", props.partnerPageId || "", {
            partner_page_section_content_type: "mediaType",
            partner_page_section_type: "documents_layout",
            partner_page_section_content_id: data.id,
            partner_page_section_id: data.partner_page_section_id,
            company_id: props.mspId || "",
        });
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleSuccessDownload = (response: AxiosResponse) => {
        downloadDoc(response.data, data?.subject?.file_name);
    };

    const onDownloadFile = () => {
        vendorProfileService.downloadDoc(
            data?.subject?.id,
            data?.subject?.model_id,
            data?.subject?.mime_type,
            handleSuccessDownload,
            handleError,
        );
    };

    const handleSuccessOpen = (response: AxiosResponse) => {
        openDoc(response.data, data?.subject?.file_name);
    };

    const onOpenFile = () => {
        notify("Document will open in a new browser window shortly.", "Success");
        vendorProfileService.downloadDoc(
            data?.subject?.id,
            data?.subject?.model_id,
            data?.subject?.mime_type,
            handleSuccessOpen,
            handleError,
        );
    };

    const [isModalOpen, setIsModalOpen] = useState(false);
    return (
        <>
            <Card
                style={isMSP ? undefined : {pointerEvents: "none"}}
                id={data?.id}
                className={`${styles.cardContainer} ${showMore ? "" : styles.cardHeight} ${styles.cp} ${
                    props.highlighted ? styles.cardContainerHighlighted : ""
                } position-relative`}
                key={data?.id}
                onClick={() => {
                    storeAnalytic();
                    // props.onClickDocument!();
                }}>
                <div className={`${styles.mainContentContainer} ${styles.bordered}`}>
                    <Card.Img
                        style={
                            !data?.subject?.thumbnail_url
                                ? {backgroundImage: `url(/Media/backgrounds/no_document.svg)`}
                                : {}
                        }
                        className={
                            !data?.subject?.thumbnail_url
                                ? `${styles.cardImage} ${styles.bordered} ${styles.documentImage}  ${styles.noThumbnail}`
                                : `${styles.cardImage} ${styles.bordered} ${styles.documentImage}`
                        }
                        variant="top"
                        src={data?.subject?.thumbnail_url}
                    />
                    <div className={`${styles.actionsContainer}`}>
                        {!isChannelProgram && (
                            <ShareButton
                                url={urlWithoutCid + `${isMSP ? "&" : "?"}cid=${data?.id}`}
                                style={{marginRight: "5px"}}
                            />
                        )}
                        <ViewDownloadButton
                            onView={onOpenFile}
                            onDownload={onDownloadFile}
                            custom_properties={{
                                subject_id: data.subject_id,
                                subject_name: data?.subject?.custom_properties?.title,
                                subject_type: data?.subject_type,
                            }}
                        />
                    </div>
                    {data?.id === props.mainContentId && (
                        <div className={`${styles.pinnedStarContainer}`}>
                            <span className={`${styles.pinnedStar}`}>
                                <PinIcon size={15} />
                            </span>
                        </div>
                    )}
                </div>
                <Card.Body className={styles.cardBody} style={{wordBreak: "break-all"}}>
                    <HeadingComponent
                        style={{fontSize: "12px !important", fontWeight: "700 !important", color: "#315EC3 !important"}}
                        noConnector
                        position="start"
                        text={
                            data?.subject?.custom_properties?.title?.length > 50
                                ? data?.subject?.custom_properties?.title?.substring(0, 50) + "..." ?? ""
                                : data?.subject?.custom_properties?.title
                        }
                    />
                    {data?.subject?.custom_properties?.description && (
                        <HeadingComponent
                            style={{
                                fontSize: "16px !important",
                                fontWeight: "400 !important",
                                color: "#5E6977 !important",
                            }}
                            text={
                                showMore
                                    ? data?.subject?.custom_properties?.description
                                    : data?.subject?.custom_properties?.description?.length < 30
                                    ? data?.subject?.custom_properties?.description
                                    : data?.subject?.custom_properties?.description?.substring(0, 30) + "..." ?? ""
                            }
                            noConnector
                            position="start"
                        />
                    )}
                    {data?.subject?.custom_properties?.description?.length > 30 && (
                        <p
                            style={{
                                fontSize: 16,
                                color: "#FF6120",
                                border: "none",
                                background: "none",
                                boxShadow: "none",
                                textDecoration: "underline",
                                cursor: "pointer",
                                alignItems: "center",
                                zIndex: 1,
                                pointerEvents: "auto",
                            }}
                            id="showMore"
                            onClick={() => setShowMore(!showMore)}>
                            {showMore ? "Show Less" : "Show More"}
                        </p>
                    )}
                    {isChannelProgram && !props?.as_company_id && (
                        <HeadingComponent
                            position="start"
                            noConnector
                            text={`Visibility: ${
                                data?.subject?.custom_properties?.media_visibility
                                    ? data?.subject?.custom_properties?.media_visibility?.toUpperCase()
                                    : "ALL"
                            }`}
                            style={{
                                fontSize: "14px !important",
                                color: "#B4B4B8 !important",
                                fontWeight: "400 !important",
                            }}
                        />
                    )}
                </Card.Body>
            </Card>
            <ModalComponent
                open={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={data?.subject?.custom_properties?.title}
                showCancelButton={false}
                showOkButton={false}
                content={
                    <>
                        {data.subject.thumbnail_url && (
                            <Avatar
                                variant="square"
                                src={data.subject.thumbnail_url}
                                alt={data?.subject?.custom_properties?.title}
                                sx={{
                                    height: 200,
                                    width: "auto",
                                    paddingBottom: "10px",
                                    img: {
                                        objectFit: "contain",
                                    },
                                }}
                            />
                        )}
                        <ParagraphComponent
                            position="start"
                            size="sm"
                            text={data?.subject?.custom_properties?.description}
                        />
                    </>
                }
            />
        </>
    );
};

export default DocumentCardComponent;
