import {FolderOpenOutlined} from "@mui/icons-material";
import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import EditOutlined from "@mui/icons-material/EditOutlined";
import PushPinOutlined from "@mui/icons-material/PushPinOutlined";
import VisibilityOffOutlined from "@mui/icons-material/VisibilityOffOutlined";
import VisibilityOutlined from "@mui/icons-material/VisibilityOutlined";
import type {TFolderContentTypes} from "../../Interfaces/folders.interface";
import type {TPartnerPageLayouts} from "../../Interfaces/partnerPage.interface";
import type {THandleContentFunction} from "../../pages/VendorPortal/vendorPortal.page";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import type {IMenuButtonItem} from "../../uicomponents/Molecules/MenuButton/menuButton.component";
import {SxProps, Theme} from "@mui/material/styles";

export function buildPRMActions({
    item,
    canEditContent,
    sectionContentId,
    sectionId,
    isHidden,
    handleContent,
    mainContentId,
    sectionType,
    friendlyUrl,
    visibility,
    onAddToFolderClick,
}: {
    item: {
        id: string;
        name: string;
        type: TFolderContentTypes;
        title?: string;
    };
    canEditContent: boolean;
    sectionContentId: string;
    sectionId: string;
    isHidden: boolean;
    handleContent: THandleContentFunction;
    mainContentId: string | null;
    sectionType: TPartnerPageLayouts;
    friendlyUrl?: string;
    visibility?: "all" | "msp" | "vendor";
    onAddToFolderClick: (item: {id: string; name: string; type: TFolderContentTypes}) => void;
}): IMenuButtonItem[] {
    const cardActionTypographyProps: {
        variant: "subtitle4";
        fontInter: boolean;
        fontWeight: 600;
        sx: SxProps<Theme>;
    } = {
        variant: "subtitle4",
        fontInter: true,
        fontWeight: 600,
        sx: theme => ({
            color: theme.palette.blue[800],
            display: "flex",
            alignItems: "center",
            gap: "16px",
            svg: {
                color: `${theme.palette.neutral[500]} !important`,
                fill: `${theme.palette.neutral[500]} !important`,
            },
        }),
    };
    return canEditContent
        ? [
              {
                  id: "add-to-folder",
                  sort: "Add to Folder",
                  children: (
                      <Typography {...cardActionTypographyProps}>
                          <FolderOpenOutlined color="neutral" /> Add to Folder
                      </Typography>
                  ),
                  onClick: () => onAddToFolderClick({id: item.id, name: item.title || item.name, type: item.type}),
              },
              {
                  id: "visibility-toggle",
                  sort: sectionContentId ? "Hide" : "Show",
                  children: (
                      <Typography {...cardActionTypographyProps}>
                          {isHidden ? (
                              <VisibilityOutlined color="neutral" />
                          ) : (
                              <VisibilityOffOutlined color="neutral" />
                          )}
                          {!isHidden ? " Hide" : " Show"}
                      </Typography>
                  ),
                  onClick: () =>
                      handleContent(
                          sectionContentId,
                          "toggleHide",
                          sectionType,
                          sectionId,
                          isHidden,
                          item.name,
                          visibility ?? "all",
                          friendlyUrl,
                      ),
              },
              ...(sectionContentId
                  ? [
                        {
                            id: "pin",
                            sort: mainContentId === sectionContentId ? "Unpin" : "Pin",
                            children: (
                                <Typography {...cardActionTypographyProps}>
                                    {mainContentId === sectionContentId ? (
                                        <PushPinOutlined color="neutral" sx={{transform: "rotate(35deg)"}} />
                                    ) : (
                                        <PushPinOutlined color="neutral" />
                                    )}
                                    {mainContentId === sectionContentId ? " Unpin" : " Pin"}
                                </Typography>
                            ),
                            onClick: () =>
                                handleContent(
                                    sectionContentId,
                                    "pin",
                                    sectionType,
                                    sectionId,
                                    isHidden,
                                    item?.name,
                                    visibility ?? "all",
                                    friendlyUrl,
                                ),
                        },
                    ]
                  : []),
              {
                  id: "edit",
                  sort: "Edit",
                  children: (
                      <Typography {...cardActionTypographyProps}>
                          <EditOutlined color="neutral" />
                          Edit
                      </Typography>
                  ),
                  onClick: () =>
                      handleContent(
                          item?.id,
                          "edit",
                          sectionType,
                          sectionId,
                          isHidden,
                          item?.name,
                          visibility ?? "all",
                          friendlyUrl,
                      ),
              },
              {
                  id: "delete",
                  sort: "zzzDelete",
                  children: (
                      <Typography
                          variant="subtitle4"
                          fontInter
                          fontWeight={600}
                          sx={theme => ({
                              color: theme.palette.error.main,
                              display: "flex",
                              alignItems: "center",
                              gap: "16px",
                          })}>
                          <DeleteOutlined color="error" />
                          Delete
                      </Typography>
                  ),
                  onClick: () =>
                      handleContent(
                          item?.id,
                          "delete",
                          sectionType,
                          sectionId,
                          isHidden,
                          item?.name,
                          visibility ?? "all",
                          friendlyUrl,
                      ),
              },
          ]
        : [];
}
