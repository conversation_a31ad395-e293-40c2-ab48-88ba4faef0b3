import {Grid, Skeleton, useTheme} from "@mui/material";
import {useState} from "react";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import type {TFolderContentTypes} from "../../Interfaces/folders.interface";
import type {IPartnerSection} from "../../Interfaces/partnerPage.interface";
import {CONTENT_SUBJECT_TYPES} from "../../constants/subjectTypes.constant";
import {VENDOR_SECTION_TYPES} from "../../enums/vendorPortalContentTypes.enum";
import useCompanyProfile from "../../hooks/useCompanyProfile";
import type {THandleContentFunction} from "../../pages/VendorPortal/vendorPortal.page";
import {analyticService} from "../../services/analytic.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import CPSlider from "../../uicomponents/Molecules/CPSlider/cpSlider.component";
import ContentCard from "../../uicomponents/Molecules/ContentCard/contentCard.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import useQuery from "../../utils/query.util";
import {removeQueryParam} from "../../utils/url.util";
import {buildPRMActions} from "./buildPRMActions.util";
import {PORTAL_VISIBILITY_GROUPS} from "../../constants/visibilityLevels.constant";
import BallotOutlinedIcon from "@mui/icons-material/BallotOutlined";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import {LinkAndSanitizeRichText} from "../../utils/miscellaneous";
import {IUpdatingQuery, MutateTypes} from "../../Interfaces/queries.interface";

type IProps = {
    assets: IPartnerSection<"assets_layout">["assets_layout"];
    partnerPageId?: string;
    onViewAllClick: (id: string) => void;
    handleContent: THandleContentFunction;
    hasFilters?: boolean;
    onAddToFolderClick: (item: {id: string; name: string; type: TFolderContentTypes}) => void;
    isLoading?: boolean;
    totalItems?: number;
    isUpdating?: IUpdatingQuery;
};

type TItem = IPartnerSection<"assets_layout">["assets_layout"]["contents"][number];

const VPAssetSection = (props: IProps) => {
    const {assets, isUpdating} = props;
    const assetsData = assets?.contents ?? [];
    const {canEditContent, mspId, companyId, isChannelProgram} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE],
            read: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ],
        },
    });

    const [selectedAsset, setSelectedAsset] = useState<TItem | null>(null);

    const query = useQuery();
    const contentIdToFocus = query.get("cid");
    const urlWithoutCid = removeQueryParam(window.location.href, "cid");
    const theme = useTheme();
    const hasMore = assetsData?.length > 1;

    const storeAnalytic = (data: TItem) => {
        analyticService.storePartnerAnalytic("partnerPortalContentClick", props.partnerPageId || "", {
            partner_page_section_content_type: "mediaType",
            partner_page_section_type: "documents_layout",
            partner_page_section_content_id: data.section_content_id,
            partner_page_section_id: data.section_id,
            company_id: mspId || "",
        });
    };

    return (
        <>
            <div style={{width: "100%", display: "flex", flexDirection: "column", gap: "24px"}}>
                <div
                    style={{
                        width: "100%",
                        borderBottom: `1px solid ${theme.palette.neutral[300]}`,
                        paddingBottom: "4px",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                    }}>
                    <div
                        style={{
                            display: "flex",
                            gap: "8px",
                        }}>
                        <BallotOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.neutral[500]} />
                        <Typography
                            variant="h3"
                            fontInter
                            sx={theme => ({
                                fontSize: "18px !important",
                                color: theme.palette.neutral[700],
                                fontWeight: "500 !important",
                            })}>
                            {VENDOR_SECTION_TYPES.ASSET}
                        </Typography>
                        <Badge title={`${props.totalItems || 0}`} color="blue">
                            {props.totalItems || 0}
                        </Badge>
                    </div>
                    {hasMore && (
                        <div>
                            <Button
                                id="view-all-templates"
                                onClick={() => props.onViewAllClick?.(assets?.id)}
                                variant="text"
                                color="secondary"
                                sx={theme => ({color: theme.palette.secondary[600], fontWeight: "700 !important"})}>
                                See All
                            </Button>
                        </div>
                    )}
                </div>
                {props.isLoading ? (
                    <Grid container spacing={2}>
                        {Array(3)
                            .fill(null)
                            .map((_, i) => (
                                <Grid item xs={12} md={6} lg={4} key={i}>
                                    <Skeleton
                                        sx={{
                                            height: "300px",
                                            width: "100%",
                                            transform: "scale(1)",
                                        }}
                                    />
                                </Grid>
                            ))}
                    </Grid>
                ) : !assetsData?.length ? (
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                        }}>
                        <Typography variant="body4" fontInter>
                            {props.hasFilters
                                ? "No content found based on your search or filter criteria. Please update your search or filter and try again."
                                : `No content available.
                            ${canEditContent ? " To add, please click Add New." : ""}`}
                        </Typography>
                    </div>
                ) : (
                    <div style={{marginBottom: "24px"}}>
                        <CPSlider
                            gap={3}
                            slidesToShow={3}
                            centeredDots
                            wrapperSx={{
                                "& .slick-slider": {
                                    paddingBottom: 3,
                                },
                            }}>
                            {assetsData?.map(asset => {
                                if (!asset.media.length) return null;
                                const headerActionsLoading =
                                    isUpdating?.[MutateTypes.PinContent + asset.section_content_id] ||
                                    isUpdating?.[MutateTypes.ToggleHideSectionData + asset.section_content_id] ||
                                    isUpdating?.[MutateTypes.RemoveSectionData + asset.id];
                                return (
                                    <ContentCard
                                        key={asset.id}
                                        headerActionsLoading={headerActionsLoading}
                                        focused={contentIdToFocus === asset.id}
                                        headerActions={buildPRMActions({
                                            handleContent: props.handleContent,
                                            canEditContent,
                                            item: {
                                                id: asset.id,
                                                name: asset.custom_properties?.title ?? "",
                                                type: CONTENT_SUBJECT_TYPES.MEDIA_GALLERY,
                                            },
                                            sectionContentId: asset.section_content_id,
                                            sectionId: props.assets?.id || "",
                                            mainContentId: asset?.main_content_id || "",
                                            isHidden: asset.is_hidden,
                                            sectionType: "assets_layout",
                                            onAddToFolderClick: props.onAddToFolderClick,
                                        })}
                                        cardAction={{
                                            onClick: () => {
                                                setSelectedAsset(asset);
                                                storeAnalytic(asset);
                                            },
                                        }}
                                        companyId={companyId}
                                        badge={`${asset.media.length} Vendor Asset${asset.media.length > 1 ? "s" : ""}`}
                                        content={{
                                            id: asset.id,
                                            name: asset.custom_properties?.title,
                                            description: asset.custom_properties?.description,
                                            tags: asset.tags,
                                            visiblity: !canEditContent
                                                ? undefined
                                                : asset.main_content_id === asset.section_content_id
                                                ? "pinned"
                                                : asset.is_hidden
                                                ? "hidden"
                                                : "visible",
                                            created_at: asset.created_at,
                                            type: "asset",
                                            image: asset.media[0].url,
                                            mediaVisibility: canEditContent
                                                ? asset.custom_properties.media_visibility ||
                                                  PORTAL_VISIBILITY_GROUPS.ALL
                                                : undefined,
                                        }}
                                        shareUrl={
                                            isChannelProgram
                                                ? undefined
                                                : urlWithoutCid + `${mspId ? "&" : "?"}cid=${asset.id}`
                                        }
                                    />
                                );
                            })}
                        </CPSlider>
                    </div>
                )}
            </div>
            <ModalComponent
                open={!!selectedAsset}
                fullWidth={true}
                maxWidth="lg"
                onClose={() => setSelectedAsset(null)}
                title={`${selectedAsset?.custom_properties?.title}`}
                showCancelButton={false}
                showOkButton={false}
                content={
                    <Box sx={{marginTop: "8px"}}>
                        <Box
                            dangerouslySetInnerHTML={LinkAndSanitizeRichText(
                                selectedAsset?.custom_properties?.description || "",
                            )}
                        />
                        <Box
                            sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: "16px",
                                marginTop: "24px",
                            }}>
                            {selectedAsset?.media.map(image => (
                                <div
                                    key={image.id}
                                    style={{
                                        minWidth: "340px",
                                        flex: "0 1 30%",
                                    }}>
                                    <ContentCard
                                        key={image.id}
                                        focused={contentIdToFocus === image.id}
                                        canOpen
                                        canDownload
                                        companyId={companyId}
                                        analyticsData={{
                                            mspID: mspId,
                                            itemID: selectedAsset.section_content_id,
                                            partnerPageId: props.partnerPageId,
                                        }}
                                        badge=""
                                        content={{
                                            id: image.id,
                                            name: image.custom_properties?.title || image.name || image.file_name,
                                            created_at: image.created_at,
                                            type: "asset",
                                            image: image.url,
                                            mime_type: image.mime_type,
                                            file_name: image.file_name,
                                        }}
                                    />
                                </div>
                            ))}
                        </Box>
                    </Box>
                }
            />
        </>
    );
};

export default VPAssetSection;
