import {Grid, Skeleton, useTheme} from "@mui/material";
import {useEffect, useState} from "react";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import type {TFolderContentTypes} from "../../Interfaces/folders.interface";
import {IPartnerSection} from "../../Interfaces/partnerPage.interface";
import routeConfig from "../../constants/routeConfig";
import {VENDOR_SECTION_TYPES} from "../../enums/vendorPortalContentTypes.enum";
import useCompanyProfile from "../../hooks/useCompanyProfile";
import type {THandleContentFunction} from "../../pages/VendorPortal/vendorPortal.page";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import CPSlider from "../../uicomponents/Molecules/CPSlider/cpSlider.component";
import ContentCard from "../../uicomponents/Molecules/ContentCard/contentCard.component";
import ContentPreview from "../../uicomponents/Molecules/ContentPreview/contentPreview.component";
import useQuery from "../../utils/query.util";
import {removeQueryParam} from "../../utils/url.util";
import {buildPRMActions} from "./buildPRMActions.util";
import {PORTAL_VISIBILITY_GROUPS} from "../../constants/visibilityLevels.constant";
import VideoLibraryOutlinedIcon from "@mui/icons-material/VideoLibraryOutlined";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import {IUpdatingQuery, MutateTypes} from "../../Interfaces/queries.interface";

type IProps = {
    videos: IPartnerSection<"videos_layout">["videos_layout"];
    partnerPageId?: string;
    defaultPlayingVideo?: string;
    onViewAllClick: (id: string) => void;
    handleContent: THandleContentFunction;
    hasFilters?: boolean;
    onAddToFolderClick: (item: {id: string; name: string; type: TFolderContentTypes}) => void;
    isLoading?: boolean;
    totalItems?: number;
    isUpdating?: IUpdatingQuery;
};

type TItem = IPartnerSection<"videos_layout">["videos_layout"]["contents"][number];

const VPVideoSection = (props: IProps) => {
    const {videos, isUpdating} = props;
    const videosData = videos?.contents ?? [];
    const {canEditContent, isChannelProgram} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE],
            read: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ],
        },
    });
    const query = useQuery();
    const defaultPlayingVideo = query.get("cid");

    const [currentPlayingVideo, setCurrentPlayingVideo] = useState<TItem>();
    const urlWithoutCid = removeQueryParam(location.href, "cid");

    useEffect(() => {
        if (defaultPlayingVideo && videosData?.length) {
            const foundVideo = videosData.find(video => video.id === defaultPlayingVideo);
            setCurrentPlayingVideo(foundVideo);
        }
    }, [defaultPlayingVideo, videosData]);

    const theme = useTheme();
    const hasMore = videosData?.length > 1;

    return (
        <div style={{width: "100%", display: "flex", flexDirection: "column", gap: "24px"}}>
            <div
                style={{
                    width: "100%",
                    borderBottom: `1px solid ${theme.palette.neutral[300]}`,
                    paddingBottom: "4px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                }}>
                <div
                    style={{
                        display: "flex",
                        gap: "8px",
                    }}>
                    <VideoLibraryOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.neutral[500]} />
                    <Typography
                        variant="h3"
                        fontInter
                        sx={theme => ({
                            fontSize: "18px !important",
                            color: theme.palette.neutral[700],
                            fontWeight: "500 !important",
                        })}>
                        {VENDOR_SECTION_TYPES.VIDEOS}
                    </Typography>
                    <Badge title={`${props.totalItems || 0}`} color="blue">
                        {props.totalItems || 0}
                    </Badge>
                </div>
                {hasMore && (
                    <div>
                        <Button
                            id="view-all-blogs"
                            onClick={() => props.onViewAllClick?.(videos?.id)}
                            variant="text"
                            color="secondary"
                            sx={theme => ({color: theme.palette.secondary[600], fontWeight: "700 !important"})}>
                            See All
                        </Button>
                    </div>
                )}
            </div>
            {currentPlayingVideo && (
                <div>
                    <ContentPreview
                        onClose={() => {
                            setCurrentPlayingVideo(undefined);
                            history.replaceState({}, "", urlWithoutCid);
                        }}
                        previewType="video"
                        content={{
                            id: currentPlayingVideo.id,
                            name: currentPlayingVideo.custom_properties?.title,
                            description: currentPlayingVideo.custom_properties?.description,
                            tags: currentPlayingVideo.tags,
                            visiblity:
                                currentPlayingVideo.main_content_id === currentPlayingVideo.id
                                    ? "pinned"
                                    : currentPlayingVideo.section_content_id
                                    ? "visible"
                                    : "hidden",
                            created_at: currentPlayingVideo.created_at,
                            image: currentPlayingVideo.thumbnail,
                            type: "video",
                            url: currentPlayingVideo?.url,
                        }}
                    />
                </div>
            )}
            {props.isLoading ? (
                <Grid container spacing={2}>
                    {Array(3)
                        .fill(null)
                        .map((_, i) => (
                            <Grid item xs={12} md={6} lg={4} key={i}>
                                <Skeleton
                                    sx={{
                                        height: "300px",
                                        width: "100%",
                                        transform: "scale(1)",
                                    }}
                                />
                            </Grid>
                        ))}
                </Grid>
            ) : !videosData?.length ? (
                <div className="d-flex text-center justify-content-center">
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                        }}>
                        <Typography variant="body4" fontInter>
                            {props.hasFilters
                                ? "No content found based on your search or filter criteria. Please update your search or filter and try again."
                                : `No content available.
                            ${canEditContent ? " To add, please click Add New." : ""}`}
                        </Typography>
                    </div>
                </div>
            ) : (
                <div>
                    <CPSlider
                        gap={3}
                        slidesToShow={3}
                        centeredDots
                        wrapperSx={{
                            "& .slick-slider": {
                                paddingBottom: 3,
                            },
                        }}>
                        {videosData?.length > 0 &&
                            videosData?.map(video => {
                                const headerActionsLoading =
                                    isUpdating?.[MutateTypes.PinContent + video.section_content_id] ||
                                    isUpdating?.[MutateTypes.ToggleHideSectionData + video.section_content_id] ||
                                    isUpdating?.[MutateTypes.RemoveSectionData + video.id];
                                return (
                                    <ContentCard
                                        key={video?.id}
                                        headerActionsLoading={headerActionsLoading}
                                        focused={currentPlayingVideo?.id === video?.id}
                                        cardAction={{
                                            onClick: () => {
                                                setCurrentPlayingVideo(video);
                                                history.replaceState({}, "", `${urlWithoutCid}?cid=${video.id}`);
                                            },
                                        }}
                                        headerActions={buildPRMActions({
                                            handleContent: props.handleContent,
                                            canEditContent,
                                            item: {
                                                id: video.id,
                                                name: video.title ?? "",
                                                type: "video",
                                            },
                                            sectionContentId: video.section_content_id,
                                            sectionId: props.videos?.id || "",
                                            isHidden: video.is_hidden,
                                            mainContentId: video.main_content_id || "",
                                            sectionType: "videos_layout",
                                            onAddToFolderClick: props.onAddToFolderClick,
                                        })}
                                        content={{
                                            id: video.id,
                                            name: video?.custom_properties?.title,
                                            description: video?.custom_properties?.description,
                                            tags: video.tags,
                                            visiblity: !canEditContent
                                                ? undefined
                                                : video.main_content_id === video.section_content_id
                                                ? "pinned"
                                                : video.is_hidden
                                                ? "hidden"
                                                : "visible",
                                            created_at: video.created_at,
                                            image: video?.thumbnail,
                                            type: "video",
                                            mediaVisibility: canEditContent
                                                ? video.custom_properties.media_visibility ||
                                                  PORTAL_VISIBILITY_GROUPS.ALL
                                                : undefined,
                                        }}
                                        shareUrl={
                                            isChannelProgram
                                                ? undefined
                                                : routeConfig.WatchVideo.path.replace(":id", video.id)
                                        }
                                    />
                                );
                            })}
                    </CPSlider>
                </div>
            )}
        </div>
    );
};

export default VPVideoSection;
