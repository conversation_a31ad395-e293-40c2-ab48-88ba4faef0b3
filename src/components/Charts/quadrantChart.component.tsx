import CloseIcon from "@mui/icons-material/Close";
import {useTheme} from "@mui/material";
import {useEffect, useRef, useState} from "react";
import {
    CartesianGrid,
    Cell,
    ReferenceArea,
    ReferenceLine,
    Scatter,
    Scatter<PERSON>hart,
    Tooltip,
    XAxis,
    YAxis,
    ZAxis,
} from "recharts";
import useWindowSize from "../../hooks/useWindowSize";
import styles from "../../styles/components/quadrantChart.module.sass";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import MuiIconButtonComponent from "../../uicomponents/Buttons/MuiIconButton.component";
import ErrorBoundary from "../../utils/errorBoundary.util";
import Loader from "../../utils/loader";

export interface IQuadrantChartProps {
    data: any[];
    CustomPoint?: any;
    CustomTooltip?: any;
    CustomMultiPoint?: any;
    xDataKey: string;
    xAxisLabel?:
        | string
        | {
              left: string;
              center: string;
              right: string;
          };
    yDataKey: string;
    yAxisLabel?:
        | string
        | {
              top: string;
              center: string;
              bottom: string;
          };
    uniqueIdentifier: string;
    width: number | "container";
    maxWidth?: number;
    height: number;
    quadrantLabels?: {
        topLeft?: string;
        topLeftColor?: string;
        topRight?: string;
        topRightColor?: string;
        bottomLeft?: string;
        bottomLeftColor?: string;
        bottomRight?: string;
        bottomRightColor?: string;
    };
    isLoading?: boolean;
    minX?: number;
    minY?: number;
    maxX?: number;
    maxY?: number;
    errorText?: string;
    pointHeight?: number;
    chartWrapperId?: string;
    //? Mobile Props
    mobileBreakpoint?: number;
    mobilePointHeight?: number;
    mobileWidth?: number;
    bgColors?: {
        topLeft?: string;
        topLeftLabel?: string;
        topRight?: string;
        topRightLabel?: string;
        bottomLeft?: string;
        bottomLeftLabel?: string;
        bottomRight?: string;
        bottomRightLabel?: string;
    };
    showWaterMark?: boolean;
}

const TOP_LEFT = "topLeft";
const TOP_RIGHT = "topRight";
const BOTTOM_LEFT = "bottomLeft";
const BOTTOM_RIGHT = "bottomRight";
const quadrants = [TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT];

const getKeyAt = (key: string, obj) => key.split(".").reduce((o, i) => (o ? o[i] : false), obj);

const is_firefox = /firefox/i.test(navigator.userAgent);

const QuadrantChart = (props: IQuadrantChartProps) => {
    const {CustomPoint, CustomTooltip, CustomMultiPoint, quadrantLabels} = props;
    const [multipleIdentifier, setMultipleIdentifier] = useState<any>();
    const [calculatedWidth, setCalculatedWidth] = useState(0);
    const [pointsInQuadrant, setPointsInQuadrant] = useState({
        topLeft: 0,
        topRight: 0,
        bottomLeft: 0,
        bottomRight: 0,
    });
    const [expandedQuadrant, setExpandedQuadrant] = useState<
        undefined | "topRight" | "topLeft" | "bottomRight" | "bottomLeft"
    >(undefined);
    const theme = useTheme();
    const windowSize = useWindowSize();
    const shouldAnimate = useRef(true);
    const chartWrapperRef = useRef<any>();
    const scatterRef = useRef<any>();
    const preventMultiClose = useRef<any>(false);
    const yAxisWidth = 25;
    const xAxisHeight = 25;
    const xMin = props.minX || 0;
    const xMax = props.maxX || 100;
    const yMin = props.minY || 0;
    const yMax = props.maxY || 100;
    const pointsX: number[] = [];
    const pointsY: number[] = [];
    const chartHeight: number = props.height || 1000;
    const startingWidth = props.width === "container" ? 1200 : props.width || 1000;
    const chartWidth: any =
        typeof props.width === "string"
            ? windowSize.width - yAxisWidth - 40
            : windowSize.width > startingWidth
            ? startingWidth
            : windowSize.width - yAxisWidth - 40;
    for (let i = 1; i <= 8; i++) {
        pointsX.push((calculatedWidth + yAxisWidth / 2) * (i / 8) + 5);
        pointsY.push((chartHeight - xAxisHeight / 2) * (i / 8) - 5);
    }
    const isMobile = props.mobileBreakpoint && props.mobileBreakpoint >= windowSize.width;
    const pointHeight = isMobile && props.mobilePointHeight ? props.mobilePointHeight : props.pointHeight || 55;
    const yRange = yMax - yMin;
    const xRange = xMax - xMin;
    const halfwayX = (xMin + xMax) / 2;
    const halfwayY = (yMin + yMax) / 2;
    const xMinWithPadding = xMin - xRange * 0.06;
    const yMinWithPadding = yMin - yRange * 0.06;
    const xMaxWithPadding = xMax + xRange * 0.06;
    const yMaxWithPadding = yMax + yRange * 0.06;

    const xDomains = {
        default: [xMin, xMax],
        [TOP_LEFT]: [xMin, halfwayX],
        [TOP_RIGHT]: [halfwayX, xMax],
        [BOTTOM_LEFT]: [xMin, halfwayX],
        [BOTTOM_RIGHT]: [halfwayX, xMax],
    };

    const yDomains = {
        default: [yMin, yMax],
        [TOP_LEFT]: [halfwayY, yMax],
        [TOP_RIGHT]: [halfwayY, yMax],
        [BOTTOM_LEFT]: [yMin, halfwayY],
        [BOTTOM_RIGHT]: [yMin, halfwayY],
    };

    const chartData = props.data.map((item: any) => {
        const copy = {...item};
        let copyX = getKeyAt(props.xDataKey, item);
        let copyY = getKeyAt(props.yDataKey, item);
        if (copyX > xMax) {
            copyX = xMax;
        }
        if (copyX < xMin) {
            copyX = xMin;
        }
        if (copyY > yMax) {
            copyY = yMax;
        }
        if (copyY < yMin) {
            copyY = yMin;
        }
        return {...copy, [props.xDataKey]: copyX, [props.yDataKey]: copyY};
    });

    const chartIsEmpty =
        props.data?.filter(item => {
            const itemX = getKeyAt(props.xDataKey, item);
            const itemY = getKeyAt(props.yDataKey, item);
            if (typeof itemX === "number" && typeof itemY === "number") {
                return true;
            }
            return false;
        })?.length <= 0;

    const handleIdentifierChange = (value: any) => {
        if (preventMultiClose.current || value === multipleIdentifier) return;
        setMultipleIdentifier(value);
    };

    const setPreventMultiClose = (value: boolean) => {
        preventMultiClose.current = Boolean(value);
    };

    const clearAllMultiPointStyling = () => {
        const mp: any = document.querySelectorAll(`foreignObject.${styles.multiPointActive}`);
        mp.forEach(p => {
            p?.classList.remove(styles.multiPointActive);
            p?.classList.add(styles.multiPoint);
        });
    };

    const handleMouseMove = () => {
        const hovered = chartWrapperRef.current?.querySelectorAll(":hover");
        let hasMultiHoveredItem = false;
        hovered.forEach(ele => {
            if (ele.classList.contains(styles.multiPoint)) {
                hasMultiHoveredItem = true;
                ele.classList.replace(styles.multiPoint, styles.multiPointActive);
                return;
            }
            if (ele.classList.contains(styles.multiPointActive)) {
                hasMultiHoveredItem = true;
            }
        });
        if (!hasMultiHoveredItem && multipleIdentifier) {
            handleIdentifierChange(undefined);
        }
    };

    const QuadrantBackgrounds = () => (
        <>
            <ReferenceArea
                x1={halfwayX}
                x2={xMaxWithPadding}
                y1={halfwayY}
                y2={yMaxWithPadding}
                fill={props.bgColors?.topRight || "#E8F4E8"}
                stroke={props.bgColors?.topRight || "#E8F4E8"}
                strokeOpacity={1}
                id="topRightReference"
                onClick={e => {
                    e.preventDefault();
                    setExpandedQuadrant(TOP_RIGHT);
                }}
                ifOverflow="hidden"
                className="cursor-pointer"
            />
            <ReferenceArea
                x1={xMinWithPadding}
                x2={halfwayX}
                y1={halfwayY}
                y2={yMaxWithPadding}
                fill={props.bgColors?.topLeft || "#FCF0EC"}
                stroke={props.bgColors?.topLeft || "#FCF0EC"}
                strokeOpacity={1}
                id="topLeftReference"
                onClick={e => {
                    e.preventDefault();
                    setExpandedQuadrant(TOP_LEFT);
                }}
                ifOverflow="hidden"
                className="cursor-pointer"
            />
            <ReferenceArea
                x1={xMinWithPadding}
                x2={halfwayX}
                y1={yMinWithPadding}
                y2={halfwayY}
                fill={props.bgColors?.bottomLeft || "#FCECEF"}
                stroke={props.bgColors?.bottomLeft || "#FCECEF"}
                strokeOpacity={1}
                id="bottomLeftReference"
                onClick={e => {
                    e.preventDefault();
                    setExpandedQuadrant(BOTTOM_LEFT);
                }}
                ifOverflow="hidden"
                className="cursor-pointer"
            />
            <ReferenceArea
                x1={halfwayX}
                x2={xMaxWithPadding}
                y1={yMinWithPadding}
                y2={halfwayY}
                fill={props.bgColors?.bottomRight || "#E5F1FE"}
                stroke={props.bgColors?.bottomRight || "#E5F1FE"}
                strokeOpacity={1}
                id="bottomRightReference"
                onClick={e => {
                    e.preventDefault();
                    setExpandedQuadrant(BOTTOM_RIGHT);
                }}
                ifOverflow="hidden"
                className="cursor-pointer"
            />
        </>
    );

    const axisLines = () => (
        <>
            <ReferenceLine y={(yMax + yMin) / 2} stroke="#FFF" strokeWidth="5px" />
            <ReferenceLine x={(xMax + xMin) / 2} stroke="#FFF" strokeWidth="5px" />
        </>
    );

    const renderChartQuadrantLabels = () => {
        const quadrantLabelStyles = {
            [TOP_RIGHT]: {
                backgroundColor: props.bgColors?.topRightLabel || "",
            },
            [TOP_LEFT]: {
                backgroundColor: props.bgColors?.topLeftLabel || "",
            },
            [BOTTOM_RIGHT]: {
                backgroundColor: props.bgColors?.bottomRightLabel || "",
            },
            [BOTTOM_LEFT]: {
                backgroundColor: props.bgColors?.bottomLeftLabel || "",
            },
        };
        return quadrants
            .filter(q => (expandedQuadrant ? q === expandedQuadrant : true))
            .map((q, idx) => (
                <div
                    key={idx}
                    className={`${styles.quadrantLabel} ${styles["quadrantLabel_" + q]}`}
                    style={quadrantLabelStyles[q]}>
                    <Typography color={props.quadrantLabels?.[q + "Color"] || ""} uppercase variant="subtitle4">
                        {quadrantLabels?.[q] || ""}({pointsInQuadrant[q]})
                    </Typography>
                </div>
            ));
    };

    const calculatePointsInEachQuadrant = () => {
        const dataAttribute = "data-quadrant";
        const allElements = document.querySelectorAll(`[${dataAttribute}]`);
        const elements = Array.from(allElements);
        if (elements) {
            setPointsInQuadrant({
                topLeft: elements.filter(e => e.getAttribute(dataAttribute) === TOP_LEFT)?.length || 0,
                topRight: elements.filter(e => e.getAttribute(dataAttribute) === TOP_RIGHT)?.length || 0,
                bottomLeft: elements.filter(e => e.getAttribute(dataAttribute) === BOTTOM_LEFT)?.length || 0,
                bottomRight: elements.filter(e => e.getAttribute(dataAttribute) === BOTTOM_RIGHT)?.length || 0,
            });
        }
    };

    const calculatePointsQuadrant = (
        pointX: any,
        pointY: number,
    ): {
        quadrant: string;
        isTopQuadrant: boolean;
        isLeftQuadrant: boolean;
    } | null => {
        if (typeof pointX !== "number" || typeof pointY !== "number") return null;
        const yMidPoint = (yMax + yMin) / 2;
        const xMidPoint = (xMax + xMin) / 2;
        const isTopQuadrant = pointY > yMidPoint;
        const isLeftQuadrant = pointX < xMidPoint;
        return {
            quadrant: `${isTopQuadrant ? "top" : "bottom"}${isLeftQuadrant ? "Left" : "Right"}`,
            isTopQuadrant,
            isLeftQuadrant,
        };
    };

    useEffect(() => {
        setTimeout(() => {
            if (multipleIdentifier !== undefined || preventMultiClose.current) return;
            clearAllMultiPointStyling();
        }, 0);
    }, [multipleIdentifier]);

    useEffect(() => {
        if (props.data) {
            setTimeout(() => {
                shouldAnimate.current = false;
            }, 500);
        }
        calculatePointsInEachQuadrant();
        return () => {
            shouldAnimate.current = true;
        };
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.data, props.xDataKey, props.yDataKey]);

    useEffect(() => {
        if (chartWrapperRef.current) {
            const width = chartWrapperRef.current.offsetWidth;
            if (props.maxWidth && width > props.maxWidth) {
                setCalculatedWidth(props.maxWidth);
                return;
            }
            setCalculatedWidth(width);
        }
    }, [windowSize, props.maxWidth]);

    useEffect(() => {
        setTimeout(() => {
            if (chartWrapperRef.current && !calculatedWidth && props.width === "container") {
                const width = chartWrapperRef.current.offsetWidth;
                if (props.maxWidth && width > props.maxWidth) {
                    setCalculatedWidth(props.maxWidth);
                    return;
                }
                setCalculatedWidth(width);
            }
        }, 100);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [chartWrapperRef.current]);

    return (
        <ErrorBoundary>
            <div
                key={props.chartWrapperId}
                className="position-relative overflow-none w-100 overflow-hidden"
                style={{
                    height: chartHeight,
                    maxWidth: props.maxWidth ? props.maxWidth + 40 + yAxisWidth : "",
                }}
                id={props.chartWrapperId}
                onMouseMove={handleMouseMove}
                ref={chartWrapperRef}>
                {props.isLoading && !props.errorText ? (
                    <div
                        style={{
                            height: chartHeight - 20 - xAxisHeight,
                            width: (props.width === "container" ? calculatedWidth : chartWidth) - 40 - yAxisWidth,
                            position: "absolute",
                            top: 20,
                            left: 20 + yAxisWidth,
                            backgroundColor: "rgb(6, 37, 86, 0.3)",
                            pointerEvents: "none",
                            zIndex: 3,
                        }}
                        className="d-flex justify-content-center align-items-center">
                        <Loader inline big loading style={{padding: 0, margin: 0}} />
                    </div>
                ) : chartIsEmpty ? (
                    <div
                        style={{
                            height: 50,
                            width: 250,
                            position: "absolute",
                            top: "calc(50% - 35px)",
                            left: "calc(50% - 115px)",
                            backgroundColor: "#FFF",
                            pointerEvents: "none",
                            zIndex: 3,
                        }}
                        className="d-flex justify-content-center align-items-center border shadow">
                        No Results Found...
                    </div>
                ) : null}
                {props.errorText && (
                    <div
                        style={{
                            height: chartHeight - 40 - xAxisHeight,
                            width: (props.width === "container" ? calculatedWidth : chartWidth) - 40 - yAxisWidth,
                            top: 20,
                            left: 20 + yAxisWidth,
                        }}
                        className={`d-flex justify-content-center align-items-center ${styles.errorText}`}>
                        <span className="border shadow">{props.errorText}</span>
                    </div>
                )}
                {expandedQuadrant && (
                    <div className="position-absolute top-0" style={{zIndex: 1000, left: "calc(50% + 20px)"}}>
                        <MuiIconButtonComponent
                            id="closeExpand"
                            icon={<CloseIcon />}
                            onClick={() => setExpandedQuadrant(undefined)}
                            color="#FFF"
                            style={{
                                border: `1px solid ${theme.palette.neutral[400]}`,
                                backgroundColor: "#FFF",
                            }}
                        />
                    </div>
                )}
                <ScatterChart
                    width={props.width === "container" ? calculatedWidth : chartWidth}
                    height={chartHeight}
                    margin={{
                        top: 20,
                        right: 20,
                        // bottom: 20,
                        left: 20,
                    }}>
                    <CartesianGrid strokeDasharray="2" verticalPoints={pointsX} horizontalPoints={pointsY} />
                    {props.showWaterMark && (
                        <image
                            href="/Media/watermarks/channel-program-watermark.svg"
                            x={`calc(50% - ${chartWidth / (isMobile ? 4 : 8)}px)`}
                            y={`calc(50% - ${chartHeight / (isMobile ? 2 : 4)}px)`}
                            width={chartWidth / (isMobile ? 2 : 4)}
                            height={chartHeight / (isMobile ? 1 : 2)}
                            opacity={0.25}
                        />
                    )}
                    {QuadrantBackgrounds()}
                    {axisLines()}
                    <XAxis
                        type="number"
                        dataKey={props.xDataKey}
                        name="Market Awareness"
                        axisLine={false}
                        tick={false}
                        label={
                            typeof props.xAxisLabel === "string"
                                ? {
                                      value: props.xAxisLabel,
                                      style: {
                                          color: "var(--cpNavy)",
                                          fontSize: 18,
                                          transform: isMobile ? "translateY(20px)" : "translateY(0px)",
                                      },
                                  }
                                : undefined
                        }
                        domain={xDomains[expandedQuadrant ? expandedQuadrant : "default"]}
                        height={xAxisHeight}
                        padding={{
                            left: 30,
                            right: 30,
                        }}
                        allowDataOverflow
                    />
                    <YAxis
                        type="number"
                        dataKey={props.yDataKey}
                        name="Differentiation"
                        axisLine={false}
                        tick={false}
                        label={
                            typeof props.yAxisLabel === "string"
                                ? {
                                      value: props.yAxisLabel,
                                      angle: -90,
                                      style: {color: "var(--cpNavy)", fontSize: 18},
                                  }
                                : undefined
                        }
                        width={yAxisWidth}
                        domain={yDomains[expandedQuadrant ? expandedQuadrant : "default"]}
                        padding={{
                            bottom: 30,
                            top: 30,
                        }}
                        allowDataOverflow
                    />
                    <ZAxis range={isMobile ? [1250, 1250] : [2750, 2750]} />
                    {CustomTooltip !== false && (
                        <Tooltip
                            cursor={false}
                            allowEscapeViewBox={{x: true, y: true}}
                            content={tooltipProps => {
                                const {payload, active} = tooltipProps;
                                if (active && payload && payload.length) {
                                    let currentPayload = payload[0] && payload[0].payload;
                                    if (!currentPayload) return null;
                                    if (multipleIdentifier) {
                                        currentPayload = props.data.find(
                                            item =>
                                                props.uniqueIdentifier.split(".").reduce((o, i) => o[i], item) ===
                                                multipleIdentifier,
                                        );
                                    }
                                    return <CustomTooltip {...currentPayload} />;
                                }
                                return null;
                            }}
                        />
                    )}
                    {!props.errorText && !props.isLoading && (
                        <Scatter
                            name="test"
                            data={chartData}
                            fill="#8884d8"
                            ref={scatterRef}
                            //! I don't think this code is needed anymore, keeping it in case issues are found
                            // onMouseLeave={() => {
                            //     if (!preventMultiClose.current) {
                            //         handleIdentifierChange(undefined);
                            //     }
                            // }}
                            onMouseEnter={e => {
                                const {x, y} = e;
                                const svg = chartWrapperRef.current?.querySelector(
                                    ".recharts-wrapper > .recharts-surface > .recharts-scatter > .recharts-layer",
                                );
                                const points = svg?.querySelectorAll(":scope > .recharts-scatter-symbol");
                                if (!svg || !points) return;
                                points.forEach(p => {
                                    const childX = p.firstChild?.getAttribute("x");
                                    const childY = p.firstChild?.getAttribute("y");
                                    if (String(x) === childX && String(y) === childY) {
                                        svg.appendChild(p);
                                    }
                                });
                            }}
                            isAnimationActive={false}
                            //@ts-ignore
                            shape={
                                props.CustomPoint
                                    ? p => {
                                          if (!p || p.x < 0 || p.y < 0) {
                                              return null;
                                          }
                                          const points = scatterRef?.current?.state?.curPoints;
                                          const matches =
                                              points?.filter(point => point.x === p.x && point.y === p.y) || [];
                                          if (matches.length > 1) {
                                              const foundIndex = matches.findIndex(
                                                  point =>
                                                      getKeyAt(props.uniqueIdentifier, point) ===
                                                      getKeyAt(props.uniqueIdentifier, p),
                                              );
                                              if (foundIndex === 0) {
                                                  const identifiers = matches.map(point =>
                                                      props.uniqueIdentifier.split(".").reduce((o, i) => o[i], point),
                                                  );
                                                  const quadrantInfo = calculatePointsQuadrant(p.node.x, p.node.y);
                                                  const pointId = getKeyAt(props.uniqueIdentifier, p.payload);
                                                  const multiPointId = `multiPoint-${pointId}`;
                                                  const isActive = identifiers.includes(multipleIdentifier);
                                                  return (
                                                      <foreignObject
                                                          {...p}
                                                          className={styles.multiPoint}
                                                          id={multiPointId}
                                                          width={pointHeight}
                                                          height={pointHeight}
                                                          onMouseEnter={e => {
                                                              if (!is_firefox) return;
                                                              e.currentTarget.classList.replace(
                                                                  styles.multiPoint,
                                                                  styles.multiPointActive,
                                                              );
                                                          }}
                                                          onClick={(e: any) => {
                                                              if (e.target) {
                                                                  //@ts-ignore
                                                                  e.target.classList.remove(styles.multiPoint);
                                                                  e.target.classList.add(styles.multiPointActive);
                                                              }
                                                          }}
                                                          version="1.1"
                                                          xmlns="http://www.w3.org/2000/svg">
                                                          <div className={styles.matchesContainerWrapper}>
                                                              {props.CustomMultiPoint ? (
                                                                  <CustomMultiPoint
                                                                      matches={matches}
                                                                      className={styles.multiPointContent}
                                                                  />
                                                              ) : (
                                                                  <h3 className={styles.numberText}>
                                                                      {matches.length}
                                                                  </h3>
                                                              )}
                                                              <div className={styles.matchesContainer}>
                                                                  {matches.reverse().map((match, index) => {
                                                                      const newMultipleIdentifier =
                                                                          props.uniqueIdentifier
                                                                              .split(".")
                                                                              .reduce((o, i) => o[i], match);
                                                                      const isFirstIndex = index === 0;
                                                                      const indexIsEven = index % 2 === 0;
                                                                      const positions: any = {top: 0, left: 0};
                                                                      if (!isFirstIndex) {
                                                                          if (quadrantInfo?.isTopQuadrant) {
                                                                              positions.top = indexIsEven
                                                                                  ? (index / 2) * pointHeight
                                                                                  : 0;
                                                                          } else {
                                                                              positions.top = indexIsEven
                                                                                  ? -(index / 2) * pointHeight
                                                                                  : 0;
                                                                          }
                                                                          if (quadrantInfo?.isLeftQuadrant) {
                                                                              positions.left = indexIsEven
                                                                                  ? 0
                                                                                  : Math.round(index / 2) * pointHeight;
                                                                          } else {
                                                                              positions.left = indexIsEven
                                                                                  ? 0
                                                                                  : -Math.round(index / 2) *
                                                                                    pointHeight;
                                                                          }
                                                                      }
                                                                      return (
                                                                          <div
                                                                              key={index}
                                                                              data-quadrant={quadrantInfo?.quadrant}
                                                                              data-payload={JSON.stringify(
                                                                                  match.payload || match || "",
                                                                              )}
                                                                              onMouseEnter={() => {
                                                                                  handleIdentifierChange(
                                                                                      newMultipleIdentifier,
                                                                                  );
                                                                              }}
                                                                              style={{
                                                                                  position: "absolute",
                                                                                  height: pointHeight,
                                                                                  width: pointHeight,
                                                                                  ...positions,
                                                                              }}
                                                                              className={styles.multiPointsWrapper}>
                                                                              <CustomPoint
                                                                                  {...match}
                                                                                  animationClassName=""
                                                                                  clearTooltipPayload={() => {
                                                                                      handleIdentifierChange(undefined);
                                                                                  }}
                                                                                  resetHeight={() => {
                                                                                      const p =
                                                                                          document?.querySelectorAll(
                                                                                              `#${multiPointId}`,
                                                                                          );
                                                                                      if (p) {
                                                                                          p.forEach(point => {
                                                                                              point.setAttribute(
                                                                                                  "height",
                                                                                                  `${pointHeight}`,
                                                                                              );
                                                                                          });
                                                                                      }
                                                                                  }}
                                                                                  isActive={isActive}
                                                                                  resetAllMulti={() => {
                                                                                      const mp = document.querySelector(
                                                                                          `#${multiPointId}`,
                                                                                      );
                                                                                      mp?.classList.remove(
                                                                                          styles.multiPointActive,
                                                                                      );
                                                                                      mp?.classList.add(
                                                                                          styles.multiPoint,
                                                                                      );
                                                                                  }}
                                                                                  preventMultiClose={preventMultiClose}
                                                                                  setPreventMultiClose={
                                                                                      setPreventMultiClose
                                                                                  }
                                                                                  quadrantInfo={quadrantInfo}
                                                                                  channelChartsWrapper={
                                                                                      chartWrapperRef.current
                                                                                  }
                                                                              />
                                                                          </div>
                                                                      );
                                                                  })}
                                                              </div>
                                                          </div>
                                                      </foreignObject>
                                                  );
                                              }
                                              return null;
                                          }
                                          const {xAxis: _xAxis, yAxis: _yAxis, zAxis: _zAxis, ...customPointProps} = p;
                                          const quadrantInfo = calculatePointsQuadrant(
                                              p.payload[props.xDataKey],
                                              p.payload[props.yDataKey],
                                          );
                                          return (
                                              <CustomPoint
                                                  {...customPointProps}
                                                  animationClassName={shouldAnimate.current ? styles.pointFade : ""}
                                                  data-quadrant={quadrantInfo?.quadrant}
                                                  data-unique-identifier={getKeyAt(props.uniqueIdentifier, p.payload)}
                                                  data-payload={JSON.stringify(p.payload || "")}
                                              />
                                          );
                                      }
                                    : undefined
                            }>
                            {!props.CustomPoint &&
                                props.data.map((entry, index) => <Cell key={`cell-${index}`} fill={`red`} />)}
                        </Scatter>
                    )}
                </ScatterChart>
                {renderChartQuadrantLabels()}
                {typeof props.xAxisLabel !== "string" && (
                    <div className={styles.xAxisCustomLabels}>
                        <Typography variant="subtitle4" color={theme.palette.blue[400]}>
                            {props.xAxisLabel?.left}
                        </Typography>
                        <div className={styles.horizontalLine}>
                            ···························································································
                        </div>
                        <Typography variant="subtitle4" className={styles.xAxisCenterLabel}>
                            {props.xAxisLabel?.center}
                        </Typography>
                        <div className={styles.horizontalLine}>
                            ···························································································
                        </div>
                        <Typography variant="subtitle4" color={theme.palette.blue[800]}>
                            {props.xAxisLabel?.right}
                        </Typography>
                    </div>
                )}
                {typeof props.yAxisLabel !== "string" && (
                    <div className={styles.yAxisCustomLabels}>
                        <Typography variant="subtitle4" color={theme.palette.blue[800]}>
                            {props.yAxisLabel?.top}
                        </Typography>
                        <div className={styles.verticalLine}>
                            ···························································································
                        </div>
                        <Typography variant="subtitle4" className={styles.yAxisCenterLabel}>
                            {props.yAxisLabel?.center}
                        </Typography>
                        <div className={styles.verticalLine}>
                            ···························································································
                        </div>
                        <Typography variant="subtitle4" color={theme.palette.blue[400]}>
                            {props.yAxisLabel?.bottom}
                        </Typography>
                    </div>
                )}
            </div>
        </ErrorBoundary>
    );
};

export default QuadrantChart;
