import {PolarAngle<PERSON><PERSON>s, PolarGrid, PolarRadiusAxis, Radar, RadarChart, ResponsiveContainer, Tooltip} from "recharts";
import Loader from "../../utils/loader";

interface IDataKey {
    key: string;
    name?: string;
    fill?: string;
}

interface IProps {
    showGrid?: boolean;
    axisDataKey: string;
    dataKeys: IDataKey[];
    data: any;
    maxWidth?: number;
    gridFill?: string;
    maxValue?: number;
    loading?: boolean;
    noDataMessage?: string;
    minHeight?: number;
    labelStroke?: string;
    polarRadiusAxisFill?: string;
    customTooltip?: any;
    labelColor?: string;
}

const CustomTick = tickProps => {
    const {payload, x, y, textAnchor, stroke, radius} = tickProps;
    let writingMode = "";
    if (payload.index === 1 || payload.index === 4) {
        writingMode = "vertical-rl";
    }
    return (
        <g className="recharts-layer recharts-polar-angle-axis-tick">
            <text
                radius={radius}
                fill={stroke}
                x={x}
                y={y}
                className="recharts-text recharts-polar-angle-axis-tick-value"
                textAnchor={writingMode ? "center" : textAnchor}
                style={
                    writingMode
                        ? {
                              writingMode: writingMode as any,
                              transform: `translate(${x < 100 ? "-5px" : "5px"}, -${payload.offset / 2}px)`,
                          }
                        : {}
                }>
                <tspan x={x} dy="0em" fontSize="14px">
                    {payload.value}
                </tspan>
            </text>
        </g>
    );
};

const RadarChartComponent = (props: IProps) => {
    const {showGrid, dataKeys, axisDataKey} = props;

    return (
        <div className="w-100">
            <ResponsiveContainer
                width="100%"
                height="100%"
                minHeight={props.minHeight || 250}
                className={
                    props.loading || !props.data.length ? "d-flex justify-content-center align-items-center" : ""
                }>
                {props.loading ? (
                    <Loader inline loading version="iconWhite" big />
                ) : !props.data.length ? (
                    <div className="d-flex justify-content-center align-items-center w-100 h-100">
                        <span className="p-2 shadow fw-bold my-auto text-light">
                            {props.noDataMessage || "No Data..."}
                        </span>
                    </div>
                ) : (
                    <RadarChart data={props.data} outerRadius={90} height={250}>
                        {showGrid && <PolarGrid fill={props.gridFill} />}
                        {dataKeys.map((key: IDataKey) => {
                            return <Radar key={key.key} dataKey={key.key} name={key.name} fill={key.fill} />;
                        })}
                        <PolarRadiusAxis
                            angle={45}
                            domain={[0, props.maxValue ? props.maxValue : 150]}
                            stroke={props.polarRadiusAxisFill || "#fff"}
                            fontSize={12}
                        />
                        <PolarAngleAxis
                            dataKey={axisDataKey}
                            stroke={props.labelStroke}
                            tick={(p: any) => <CustomTick {...p} stroke={props.labelColor} />}
                        />
                        <Tooltip content={props.customTooltip ? props.customTooltip : undefined} />
                    </RadarChart>
                )}
            </ResponsiveContainer>
        </div>
    );
};

export default RadarChartComponent;
