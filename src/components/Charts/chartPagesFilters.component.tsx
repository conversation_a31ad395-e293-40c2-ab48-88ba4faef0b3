import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useReducer, useRef, useState} from "react";
import {Accordion, useAccordionButton} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {IPitchFilter, IPitchPollDate} from "../../Interfaces/filters.interface";
import useNotification from "../../hooks/useNotification";
import {abortRequest} from "../../services/api.service";
import {chartService} from "../../services/chart.service";
import {filterService} from "../../services/filters.service";
import styles from "../../styles/components/pitchChartFilters.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
import CheckBoxComponent from "../FormControls/checkBox.component";
import TextBoxComponent from "../FormControls/textBox.component";
import ReactIcon from "../Icons/reactIcon.component";

const UPDATE = "UPDATE";
const SET_PITCH_DATES = "SET_PITCH_DATES";
const SET_METRICS = "SET_METRICS";
const SET_VENDORS = "SET_VENDORS";
const SET_PRODUCT_CATEGORIES = "SET_PRODUCT_CATEGORIES";

interface IAction {
    type: "SET_PITCH_DATES" | "SET_METRICS" | "SET_VENDORS" | "SET_PRODUCT_CATEGORIES" | "UPDATE";
    payload: any;
}

interface IFilterReducerState {
    pitch_dates: IPitchPollDate[];
    metrics: Array<{id: string; name: string}>;
    vendors: Array<{id: string; name: string}>;
    product_categories: any[];
}

const CustomAccordionToggle = ({children, eventKey}) => {
    const handleClick = useAccordionButton(eventKey);
    return (
        <div className="d-flex flex-row align-items-start ms-2" onClick={handleClick}>
            {children}
        </div>
    );
};

const filterReducer = (state: IFilterReducerState, action: IAction) => {
    switch (action.type) {
        case UPDATE:
            return {...state, ...action.payload};
        case SET_VENDORS:
            return {...state, vendors: action.payload};
        case SET_PRODUCT_CATEGORIES:
            return {...state, product_categories: action.payload};
        case SET_METRICS:
            return {...state, metrics: action.payload};
        case SET_PITCH_DATES:
            return {...state, pitch_dates: action.payload};
        default:
            return state;
    }
};

interface IProps {
    handleMetricChange?: Function;
    handleDataChange?: Function;
    handleErrorMessage?: Function;
    setLoading?: any;
}

const DEFAULT_LIST_SIZE = 8;
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setMonth(oneYearAgo.getMonth() - 12);

const ChartPagesFilters = (props: IProps) => {
    const [isLoading, setIsLoading] = useState({
        pitch_dates: false,
        metrics: false,
        product_categories: false,
        vendors: false,
    });
    const [filters, filterDispatch] = useReducer(filterReducer, {
        pitch_dates: [],
        metrics: [],
        product_categories: [],
        vendors: [],
    });
    const [listSize, setListSize] = useState({
        pitch_dates: DEFAULT_LIST_SIZE,
        metrics: DEFAULT_LIST_SIZE,
        vendors: DEFAULT_LIST_SIZE,
        product_categories: DEFAULT_LIST_SIZE,
    });
    const [isSearchOpen, setIsSearchOpen] = useState({
        product_categories: false,
        vendors: false,
    });
    const [previousYearOpen, setPreviousYearOpen] = useState(false);
    const allVendorsForCurrentSelection = useRef<any>(null);
    const methods = useForm({shouldUnregister: false});
    const notify = useNotification();
    const currentErrorMessage = useRef("");
    const productCategorySearchValue = methods.watch("product_categories_search");
    const vendorsSearchValue = methods.watch("vendors_search");

    const filteredVendors = filters.vendors?.filter(v => {
        if (!allVendorsForCurrentSelection.current) return true;
        return allVendorsForCurrentSelection.current[v.id];
    });

    const hasSelectedYear = (year: string) => {
        const formValues = methods.getValues();
        return filters["pitch_dates"].reduce((acc, prev) => {
            if (prev.year === year) {
                return formValues[prev.id] && acc;
            }
            return acc;
        }, true);
    };

    const selectYear = (year: string) => {
        let passedId: string = "";
        if (hasSelectedYear(year)) {
            const filtered = filters.pitch_dates.filter(pollDate => pollDate.year === year);
            filtered.forEach(pollDate => {
                passedId = pollDate.id;
                methods.setValue(pollDate.id, false);
            });
        } else {
            filters.pitch_dates
                .filter(pollDate => pollDate.year === year)
                .forEach(pollDate => {
                    passedId = pollDate.id;
                    methods.setValue(pollDate.id, true);
                });
        }
        handleFilterSelect({target: {id: passedId}});
    };

    const handleFilterSelect = async (e: any) => {
        const {id} = e.target;
        if (id === "product_categories_search" || id === "vendors_search") return;
        const formValues = methods.getValues();
        const isDateChange = filters.pitch_dates.find(pitch_date => pitch_date.id === id);
        const isCategoryChange = !isDateChange && filters.product_categories.find(c => c.id === id);
        const isVendorChange = !isDateChange && !isCategoryChange && filters.vendors.find(v => v);
        //? Checking that the selected filter has at least one selected
        if (isDateChange || isCategoryChange || isVendorChange) {
            const abortPreviousRequest = () => {
                abortRequest(
                    "controllers/http://channel_program_be.test/api/v1/filters/chart/quadrants/products-categories",
                );
                abortRequest("controllers/http://channel_program_be.test/api/v1/chart/quadrant");
            };
            const newDateFilterList = filters["pitch_dates"].filter(item => formValues[item.id]);
            const newCategoryFilterList = filters["product_categories"].filter(item => formValues[item.id]);
            const newVendorFilterList = filteredVendors.filter(v => formValues[v.id]);
            if (newDateFilterList.length === 0) {
                const newErrorMessage = "There are no results based on the filter criteria.";
                currentErrorMessage.current = newErrorMessage;
                props.handleErrorMessage && props.handleErrorMessage(newErrorMessage);
                abortPreviousRequest();
                return;
            }
            if (!isDateChange) {
                if (newCategoryFilterList.length === 0) {
                    const newErrorMessage = "Select a Product Category to Show Results.";
                    currentErrorMessage.current = newErrorMessage;
                    props.handleErrorMessage && props.handleErrorMessage(newErrorMessage);
                    abortPreviousRequest();
                    return;
                } else if (newVendorFilterList.length === 0) {
                    currentErrorMessage.current = "Select a Vendor to Show Results";
                    props.handleErrorMessage && props.handleErrorMessage(`Select a Vendor to Show Results.`);
                    abortPreviousRequest();
                    return;
                }
            }
        }
        setIsLoading({...isLoading, product_categories: isDateChange, vendors: isDateChange});
        currentErrorMessage.current = "";
        props.handleErrorMessage && props.handleErrorMessage("");
        props.setLoading && props.setLoading(true);
        const productCategoryRes =
            isCategoryChange || isVendorChange
                ? {data: filters.product_categories}
                : await filterService.getQuadrantProductCategories(
                      filters.pitch_dates.filter(date => formValues[date.id]).map(date => date.id),
                  );
        if (productCategoryRes.data) {
            if (isDateChange) {
                setIsLoading({...isLoading, product_categories: false});
                filterDispatch({
                    type: UPDATE,
                    payload: isDateChange
                        ? {product_categories: productCategoryRes.data, vendors: filters.vendors}
                        : {product_categories: productCategoryRes.data},
                });
                setListSize({
                    ...listSize,
                    product_categories: isDateChange ? DEFAULT_LIST_SIZE : listSize.product_categories,
                    vendors: isDateChange ? DEFAULT_LIST_SIZE : listSize.vendors,
                });
            }
            getNewChartData(
                isDateChange
                    ? {
                          categories: productCategoryRes.data.map(category => category.id),
                          vendors: filters.vendors.map(v => v.id),
                      }
                    : null,
                isDateChange,
            );
        }
    };

    const getNewChartData = (customOptions: any, shouldUpdateAvailableVendors: boolean = false) => {
        props.setLoading && props.setLoading(true);
        const formValues = methods.getValues();
        const filteredPitches = filters.pitch_dates.filter(pitch => formValues[pitch.id]).map(pitch => pitch.id);
        const filteredMetrics = filters.metrics.filter(metric => formValues[metric.id]).map(metric => metric.id);
        const filteredCategories = filters.product_categories
            .filter(category => formValues[category.id])
            .map(category => category.id);
        const filteredVendors = allVendorsForCurrentSelection.current
            ? Object.keys(allVendorsForCurrentSelection.current)
                  .filter(vendorId => formValues[vendorId])
                  .map(vendorId => vendorId)
            : filters.vendors.filter(vendor => formValues[vendor.id]).map(vendor => vendor.id);
        const options = {
            pitches: filteredPitches.length === filters.pitch_dates.length ? null : filteredPitches,
            metrics: filteredMetrics.length === filters.metrics ? null : filteredMetrics,
            categories: filteredCategories.length === filters.product_categories ? null : filteredCategories,
            vendors:
                filteredVendors.length ===
                (allVendorsForCurrentSelection.current
                    ? Object.keys(allVendorsForCurrentSelection.current).length
                    : filters.vendors.length)
                    ? null
                    : filteredVendors,
        };
        const final = {...options, ...customOptions};
        chartService.getQuadrantChartData(
            final,
            (res: any) => {
                if (shouldUpdateAvailableVendors) {
                    const newAvailableVendors = {};
                    res.data.forEach(point => {
                        if (point.company?.id) {
                            newAvailableVendors[point.company.id] = true;
                        }
                    });
                    allVendorsForCurrentSelection.current = newAvailableVendors;
                }
                onSuccessGetChartData(res);
            },
            handleGetChartDataError,
        );
    };

    const onSuccessGetChartData = (res: AxiosResponse) => {
        setIsLoading({
            pitch_dates: false,
            metrics: false,
            product_categories: false,
            vendors: false,
        });
        props.setLoading && props.setLoading(false);
        props.handleDataChange && props.handleDataChange(res.data);
    };

    const handleGetChartDataError = () => {
        props.setLoading && props.setLoading(false);
        props.handleDataChange && props.handleDataChange([]);
    };

    const handleSelectAll = (filterKey: string) => {
        let passedId = "";
        const alreadyAllSelected = hasSelectedAllOptions(filterKey);
        if (filterKey === "vendors") {
            filteredVendors?.forEach(item => {
                methods.setValue(item.id, !alreadyAllSelected);
                passedId = item.id;
            });
        } else {
            filters[filterKey]?.forEach(item => {
                methods.setValue(item.id, !alreadyAllSelected);
                passedId = item.id;
            });
        }
        handleFilterSelect({target: {id: passedId}});
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        props.setLoading && props.setLoading(false);
        props.handleDataChange && props.handleDataChange([]);
    };

    const loadMore = (key: string) => {
        setListSize({...listSize, [key]: filters[key].length});
    };

    const hasSelectedAllOptions = (filterKey: string) => {
        const formValues = methods.getValues();
        if (filterKey === "vendors") {
            return filteredVendors?.reduce((acc, prev) => acc && formValues[prev.id], true) || false;
        }
        return filters[filterKey]?.reduce((acc, prev) => acc && formValues[prev.id], true) || false;
    };

    const openTextFilter = (filterKey: string, id: string) => {
        const shouldOpen = !isSearchOpen[filterKey];
        setIsSearchOpen({...isSearchOpen, [filterKey]: shouldOpen});
        setTimeout(() => {
            if (shouldOpen) methods.setFocus(id);
            if (!shouldOpen) methods.setValue(id, "");
        }, 250);
    };

    useEffect(() => {
        setTimeout(() => {
            const formValues = methods.getValues();
            filters["product_categories"]?.forEach(item => {
                if (!formValues[item.id]) {
                    methods.setValue(item.id, true);
                }
            });
        }, 1000);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [filters.product_categories]);

    useEffect(() => {
        setTimeout(() => {
            const formValues = methods.getValues();
            filters["vendors"]?.forEach(item => {
                if (!formValues[item.id]) {
                    methods.setValue(item.id, true);
                }
            });
        }, 1000);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [allVendorsForCurrentSelection.current]);

    useEffect(() => {
        setIsLoading({...isLoading, pitch_dates: true, metrics: true, product_categories: true, vendors: true});
        Promise.all([filterService.getPitchEventsWithAnswers(), filterService.getPitchBubbleMetrics()])
            .then(([pitchRes, metricsRes]) => {
                const pitchesToSelect: IPitchFilter[] = [];
                let previousYearShouldOpen = false;
                const pitchData = pitchRes.data.map((pitch: IPitchFilter) => {
                    const pitch_date = new Date(pitch.start_date);
                    if (pitch_date > oneYearAgo) {
                        if (pitch_date.getFullYear() !== today.getFullYear()) {
                            previousYearShouldOpen = true;
                        }
                        pitchesToSelect.push(pitch);
                    }
                    return {...pitch, month: String(pitch_date.getMonth()), year: String(pitch_date.getFullYear())};
                });
                setPreviousYearOpen(previousYearShouldOpen);
                //? Code to get the most current pitch
                // let mostCurrentPitch: any = null;
                // pitchData.forEach((pollDate: IPitchPollDate) => {
                //     if (!mostCurrentPitch) {
                //         mostCurrentPitch = pollDate;
                //         return;
                //     }
                //     const currentDate = new Date(mostCurrentPitch.start_date);
                //     const newPitchDate = new Date(pollDate.start_date);
                //     if (newPitchDate > currentDate) {
                //         mostCurrentPitch = pollDate;
                //         return;
                //     }
                // });
                const pitchIds = pitchesToSelect.map(p => p.id);
                Promise.all([
                    filterService.getQuadrantProductCategories(pitchIds),
                    filterService.getPitchPollCompanies(),
                ]).then(([productCategoriesRes, vendorRes]) => {
                    setIsLoading({
                        ...isLoading,
                        pitch_dates: false,
                        metrics: false,
                        product_categories: false,
                        vendors: false,
                    });
                    filterDispatch({
                        type: UPDATE,
                        payload: {
                            pitch_dates: pitchData,
                            metrics: metricsRes.data,
                            product_categories: productCategoriesRes.data,
                            vendors: vendorRes.data,
                        },
                    });
                    // mostCurrentPitch && methods.setValue((mostCurrentPitch as any).id, true);
                    pitchIds.forEach(id => {
                        methods.setValue(id, true);
                    });
                    metricsRes.data.forEach(metric => {
                        methods.setValue(metric.id, true);
                    });
                    props.handleMetricChange && props.handleMetricChange(metricsRes.data);
                    setTimeout(() => {
                        getNewChartData(
                            {
                                pitches: pitchIds,
                                metrics: metricsRes.data.map(metric => metric.id),
                                categories: productCategoriesRes.data.map(category => category.id),
                            },
                            true,
                        );
                    }, 500);
                });
            })
            .catch(vendorErr => {
                handleError(vendorErr);
            });
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <FormProvider {...methods}>
            <form
                className="d-flex flex-column col-12 col-lg-3 shadow-lg p-3 me-3 rounded"
                onChange={handleFilterSelect}>
                <label className="fw-bold">
                    Events <Loader inline loading={isLoading["pitch_dates"]} />
                </label>
                <Accordion
                    alwaysOpen
                    defaultActiveKey={previousYearOpen ? ["0", "1"] : ["0"]}
                    key={String(previousYearOpen)}>
                    {filters?.pitch_dates
                        .reduce((acc: any, prev: IPitchPollDate) => {
                            return !acc.includes(prev.year) ? [...acc, prev.year] : acc;
                        }, [])
                        .map((year: string, index: number) => (
                            <Accordion.Item
                                as="div"
                                key={year}
                                className="d-flex flex-column p-0 border-0"
                                eventKey={String(index)}>
                                <CustomAccordionToggle eventKey={String(index)}>
                                    <>
                                        <ReactIcon iconName="AiOutlineFolder" style={{height: 20, width: 20}} />
                                        <label>{year}</label>
                                        <span
                                            onClick={(e: any) => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                selectYear(year);
                                            }}
                                            className={styles.selectAllBtn}>
                                            {hasSelectedYear(year) ? "Unselect All" : "Select All"}
                                        </span>
                                    </>
                                </CustomAccordionToggle>
                                <Accordion.Collapse as="div" eventKey={String(index)}>
                                    <div className="d-flex flex-column ps-3" id="channelChartPitchDateFilters">
                                        {filters.pitch_dates
                                            .filter(item => item.year === year)
                                            .map((item: any, index: number) => (
                                                <CheckBoxComponent
                                                    key={item.id}
                                                    id={item.id}
                                                    className="m-1"
                                                    testid={`option-${year}-${index}`}>
                                                    <span title={item.name} className={styles.checkboxLabel}>
                                                        {item.name}
                                                    </span>
                                                </CheckBoxComponent>
                                            ))}
                                    </div>
                                </Accordion.Collapse>
                            </Accordion.Item>
                        ))}
                </Accordion>
                <label className="mt-2 d-flex align-items-center flex-wrap">
                    <>
                        <span className={styles.filterTitle}>Product Categories</span>
                        {!isLoading["product_categories"] ? (
                            <>
                                <div
                                    className={styles.filterSearchIcon}
                                    onClick={() => openTextFilter("product_categories", "product_categories_search")}>
                                    <ReactIcon
                                        iconName={
                                            isSearchOpen["product_categories"]
                                                ? "MdOutlineSearchOff"
                                                : "MdOutlineSearch"
                                        }
                                        size="20"
                                    />
                                </div>
                                <span
                                    onClick={() => handleSelectAll("product_categories")}
                                    className={styles.selectAllBtn}>
                                    {hasSelectedAllOptions("product_categories") ? "Unselect All" : "Select All"}
                                </span>
                            </>
                        ) : null}
                        <Loader inline loading={isLoading["product_categories"]} />
                    </>
                </label>
                {isSearchOpen["product_categories"] && (
                    <TextBoxComponent
                        id="product_categories_search"
                        containerClassName={styles.filterSearchBar}
                        placeholder="Search For Categories"
                    />
                )}
                <div className="d-flex flex-column ps-2">
                    {filters.product_categories
                        .slice(
                            0,
                            productCategorySearchValue
                                ? filters.product_categories.length
                                : listSize["product_categories"],
                        )
                        .map((product_category: any) => (
                            <CheckBoxComponent
                                key={product_category.id}
                                className={`mt-0 h-auto ${
                                    product_category.name
                                        .toUpperCase()
                                        .includes(productCategorySearchValue?.toUpperCase() || "")
                                        ? ""
                                        : "d-none"
                                }`}
                                disabled={isLoading["product_categories"]}
                                testid={`option-${product_category.name}`}
                                id={product_category.id}>
                                <span title={product_category.name} className={styles.checkboxLabel}>
                                    {product_category.name}
                                </span>
                            </CheckBoxComponent>
                        ))}
                    {filters.product_categories.length > listSize["product_categories"] &&
                        !productCategorySearchValue && (
                            <span
                                onClick={() => loadMore("product_categories")}
                                className={`ms-4 ${styles.selectAllBtn}`}>
                                Load All
                            </span>
                        )}
                </div>
                <label className="mt-2 fw-bold d-flex align-items-center">
                    <>
                        <span className={styles.filterTitle}>Vendors</span>
                        {!isLoading["vendors"] ? (
                            <>
                                <div
                                    className={styles.filterSearchIcon}
                                    onClick={() => openTextFilter("vendors", "vendors_search")}>
                                    <ReactIcon
                                        iconName={isSearchOpen["vendors"] ? "MdOutlineSearchOff" : "MdOutlineSearch"}
                                        size="20"
                                    />
                                </div>
                                <span onClick={() => handleSelectAll("vendors")} className={styles.selectAllBtn}>
                                    {hasSelectedAllOptions("vendors") ? "Unselect All" : "Select All"}
                                </span>
                            </>
                        ) : null}
                        <Loader inline loading={isLoading["vendors"]} />
                    </>
                </label>
                {isSearchOpen["vendors"] && (
                    <TextBoxComponent
                        id="vendors_search"
                        containerClassName={styles.filterSearchBar}
                        placeholder="Search For Vendors"
                    />
                )}
                <div className="d-flex flex-column ps-2">
                    {filteredVendors
                        .slice(0, vendorsSearchValue ? filteredVendors.length : listSize["vendors"])
                        .map((vendor: any, index: number) => (
                            <CheckBoxComponent
                                key={vendor.id}
                                className={`mt-0 h-auto ${
                                    vendor.name.toUpperCase().includes(vendorsSearchValue?.toUpperCase() || "")
                                        ? ""
                                        : "d-none"
                                }`}
                                disabled={isLoading["vendors"]}
                                testid={`option-${index}`}
                                id={vendor.id}>
                                <span title={vendor.name} className={styles.checkboxLabel}>
                                    {vendor.name}
                                </span>
                            </CheckBoxComponent>
                        ))}
                    {filteredVendors.length > listSize["vendors"] && !vendorsSearchValue && (
                        <span onClick={() => loadMore("vendors")} className={`ms-4 ${styles.selectAllBtn}`}>
                            Load All
                        </span>
                    )}
                </div>
            </form>
        </FormProvider>
    );
};

export default ChartPagesFilters;
