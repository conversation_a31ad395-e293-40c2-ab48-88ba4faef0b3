import {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, Label, ResponsiveContainer} from "recharts";
import {IBarChartData, IDataKeyLabelValue} from "../../Interfaces/charts/barChart.interface";
import Loader from "../../utils/loader";

interface IProps {
    chartTitle?: string;
    data: IBarChartData[];
    xAxisLabel: string;
    yAxisLabel: string;
    xAxisLabelColor?: string;
    yAxisLabelColor?: string;
    xAxisColor?: string;
    yAxisColor?: string;
    showLegend: boolean;
    showTooltip: boolean;
    showGridLines: boolean;
    dataKeys: IDataKeyLabelValue[];
    width: number | string;
    height: number | string;
    rotateXAxisTick?: number;
    tickOffset?: number | Function;
    loading?: boolean;
    noDataMessage?: string;
    customChartMargin?: any;
    customInterval?: number;
    customLoaderProps?: {
        big?: boolean;
        version?: "iconOrange" | "iconWhite" | "iconBlue";
    };
    customTooltip?: any;
    hideTicks?: boolean;
}

export function BarChartComponent(props: IProps) {
    const customLoaderProps = props.customLoaderProps || {};
    return (
        <>
            <h2>{props.chartTitle}</h2>
            <ResponsiveContainer width={props.width} height={props.height} minHeight={props.height}>
                {props.loading ? (
                    <div
                        className="d-flex justify-content-center align-items-center w-100 h-100"
                        style={{minHeight: props.height}}>
                        <Loader inline loading {...customLoaderProps} />
                    </div>
                ) : !props.data.length ? (
                    <div
                        className="d-flex justify-content-center align-items-center w-100 h-100"
                        style={{minHeight: props.height}}>
                        <span className="p-2 shadow fw-bold">{props.noDataMessage || "No Data..."}</span>
                    </div>
                ) : (
                    <BarChart
                        data={props.data}
                        margin={
                            props.customChartMargin
                                ? props.customChartMargin
                                : {
                                      top: 15,
                                      right: 5,
                                      left: 10,
                                      bottom: 15,
                                  }
                        }>
                        {props.showGridLines && <CartesianGrid strokeDasharray="3 3" />}
                        <XAxis
                            dataKey={"xAxisName"}
                            interval={props.customInterval || 0}
                            tickSize={10}
                            stroke={props.xAxisColor}
                            tick={
                                props.hideTicks
                                    ? false
                                    : p => {
                                          return (
                                              <CustomTick
                                                  rotate={props.rotateXAxisTick || "0"}
                                                  tickOffset={
                                                      props.tickOffset
                                                          ? typeof props.tickOffset === "function"
                                                              ? props.tickOffset(p.index)
                                                              : props.tickOffset
                                                          : 0
                                                  }
                                                  {...p}
                                              />
                                          );
                                      }
                            }>
                            <Label
                                value={props.xAxisLabel}
                                offset={0}
                                position="insideBottom"
                                fill={props.xAxisLabelColor}
                                stroke={props.xAxisLabelColor ? "transparent" : undefined}
                                fontSize={14}
                            />
                        </XAxis>

                        <YAxis dataKey={"yAxisName"} stroke={props.yAxisColor} fontSize={14}>
                            <Label
                                value={props.yAxisLabel}
                                angle={-90}
                                position="insideLeft"
                                fill={props.yAxisLabelColor}
                                stroke={props.xAxisLabelColor ? "transparent" : undefined}
                                fontSize={14}
                            />
                        </YAxis>

                        {props.showTooltip && (
                            <Tooltip content={props.customTooltip ? props.customTooltip : undefined} />
                        )}

                        {props.showLegend && <Legend />}

                        {props.dataKeys.map(item => {
                            return (
                                <Bar
                                    id={item.keyName}
                                    dataKey={item.keyName}
                                    key={item.keyName}
                                    fill={item.fillColor}
                                    name={item.label}
                                    background={false}
                                />
                            );
                        })}
                    </BarChart>
                )}
            </ResponsiveContainer>
        </>
    );
}

export function CustomTick(props: any) {
    const {x, y, payload, rotate, tickOffset} = props;

    return (
        <g transform={`translate(${x},${y - (tickOffset || 5)})`}>
            <text
                x={0}
                y={0}
                dy={16}
                textAnchor="end"
                fill="#676767"
                transform={`rotate(${rotate})`}
                style={{textOverflow: "ellipsis"}}>
                {payload.value}
            </text>
        </g>
    );
}
