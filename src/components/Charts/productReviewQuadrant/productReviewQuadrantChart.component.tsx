import builder from "@builder.io/react";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import {useTheme} from "@mui/material";
import {AxiosResponse} from "axios";
import domtoimage from "dom-to-image";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IAdvertisement} from "../../../Interfaces/adminAdvertisements.interface";
import {SUBCATEGORIES_TEXT} from "../../../constants/commonStrings.constant";
import useAppConfig from "../../../hooks/useAppConfig";
import useSettings from "../../../hooks/useSettings";
import {chartService} from "../../../services/chart.service";
import styles from "../../../styles/components/productReviewQuadrantChart.module.sass";
import Accordion from "../../../uicomponents/Atoms/Accordion/Accordion.component";
import AccordionDetails from "../../../uicomponents/Atoms/Accordion/AccordionDetails.component";
import AccordionSummary from "../../../uicomponents/Atoms/Accordion/AccordionSummary.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import CheckboxWithLabel from "../../../uicomponents/Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import convertImageUrlToFile from "../../../utils/imageToFile.util";
import QuadrantChart, {IQuadrantChartProps} from "../quadrantChart.component";
import ProductReviewQuadrantPoint from "./productReviewQuadrantPoint.component";

interface IProps {
    category: any;
    resizeKey: string;
    showSubcategoryFilter?: boolean;
    ad?: IAdvertisement;
    ad_location?: any;
    quadrantBgColors?: any;
    onScreenshot?: Function;
}

interface ScoredProduct {
    company: {name: string; friendly_url: string};
    category: [{id: string; name: string}];
    friendly_url: string;
    name: string;
    partner_satisfaction: number;
    popularity: number;
    recommendation_rating: number;
    satisfaction_rating: number;
    total_number_of_reviews_for_product: number;
    total_number_of_reviews_for_product_category: string;
}

type ICustomQuadrantChartProps = Partial<IQuadrantChartProps>;

const ProductReviewQuadrantChart = (props: IProps) => {
    const [loading, setLoading] = useState(false);
    const [chartData, setChartData] = useState<ScoredProduct[]>([]);
    const [vendors, setVendors] = useState<ScoredProduct[]>([]);
    const [uniqueSubcategories, setUniqueSubcategories] = useState<any[]>([]);
    const [collapseExpanded, setCollapseExpanded] = useState({
        vendors: true,
        categories: true,
    });
    const [builderChartDescription, setBuilderChartDescription] = useState("");
    const {settings} = useSettings();
    const {configs} = useAppConfig({
        config_key: ["STACKCHART_MIN_Y", "STACKCHART_MAX_Y", "STACKCHART_MIN_X", "STACKCHART_MAX_X"],
    });
    const theme = useTheme();
    const methods = useForm({shouldUnregister: false});
    const chartWrapperRef = useRef<any>();
    const offScreenChartWrapperRef = useRef<any>();
    const hasCapturedScreenshot = useRef<boolean>(false);
    const xDataKey = "satisfaction_rating";
    const yDataKey = "popularity";
    const isParentCategory = !Boolean(props.category?.parent_id);
    const parent_category = isParentCategory
        ? props.category
        : settings.all_categories?.find(c => c.id === props.category?.parent_id);
    const configsFromCategory = isParentCategory
        ? {
              stack_chart_min_y: parent_category?.stack_chart_min_y,
              stack_chart_max_y: parent_category?.stack_chart_max_y,
              stack_chart_min_x: parent_category?.stack_chart_min_x,
              stack_chart_max_x: parent_category?.stack_chart_max_x,
          }
        : {
              stack_chart_min_y: props.category?.stack_chart_min_y ?? parent_category?.stack_chart_min_y,
              stack_chart_max_y: props.category?.stack_chart_max_y ?? parent_category?.stack_chart_max_y,
              stack_chart_min_x: props.category?.stack_chart_min_x ?? parent_category?.stack_chart_min_x,
              stack_chart_max_x: props.category?.stack_chart_max_x ?? parent_category?.stack_chart_max_x,
          };
    const minY = configsFromCategory?.stack_chart_min_y
        ? Number(configsFromCategory?.stack_chart_min_y)
        : Number(configs.find(c => c.key === "STACKCHART_MIN_Y")?.value || "0");
    const maxY = configsFromCategory?.stack_chart_max_y
        ? Number(configsFromCategory?.stack_chart_max_y)
        : Number(configs.find(c => c.key === "STACKCHART_MAX_Y")?.value || "1");
    const minX = configsFromCategory?.stack_chart_min_x
        ? Number(configsFromCategory?.stack_chart_min_x)
        : Number(configs.find(c => c.key === "STACKCHART_MIN_X")?.value || "0");
    const maxX = configsFromCategory?.stack_chart_max_x
        ? Number(configsFromCategory?.stack_chart_max_x)
        : Number(configs.find(c => c.key === "STACKCHART_MAX_X")?.value || "5");
    const chartWrapperId = "productReviewQuadrantChartWrapper";

    const filteredData = chartData.filter((p: ScoredProduct) => {
        return methods.watch(p.company.friendly_url) && p.category.some(a => !!methods.watch(a.id));
    });
    const search = methods.watch("search");
    const categorySearch = methods.watch("categorySearch");

    const onSuccessGetChartData = (res: AxiosResponse) => {
        const {data, seo_image} = res.data;
        setChartData(data);
        const uniqueVendors: any = Array.from(new Set(data.map((p: ScoredProduct) => p.company.friendly_url))).map(
            friendly_url => {
                return data.find(a => a.company.friendly_url === friendly_url);
            },
        );
        const uniqueSubcategories: Array<{id: string; name: string}> = [];
        data.forEach((p: ScoredProduct) => {
            p.category.forEach(category => {
                if (!uniqueSubcategories.some(a => a.id === category.id)) {
                    uniqueSubcategories.push(category);
                }
            });
        });
        uniqueSubcategories.sort((a, b) => {
            const aName = a.name.toUpperCase();
            const bName = b.name.toUpperCase();
            return aName === bName ? 0 : aName < bName ? -1 : 1;
        });
        uniqueVendors.sort((a, b) => {
            const aName = (a.company.name || "").toUpperCase();
            const bName = (b.company.name || "").toUpperCase();
            return aName === bName ? 0 : aName < bName ? -1 : 1;
        });
        setUniqueSubcategories(uniqueSubcategories);
        setVendors(uniqueVendors);
        setLoading(false);
        if (seo_image) {
            props.onScreenshot?.(seo_image);
            hasCapturedScreenshot.current = true;
        }
    };

    const handleError = () => {
        setLoading(false);
    };

    const getDescription = async () => {
        const descriptions: any = await builder.getAll("channel-chart-descriptions");
        const found = descriptions.find(description => description.data.chartKey === "product-review");
        setBuilderChartDescription(found.data?.description || "");
    };

    const handleSelectAll = (toSelect: "vendors" | "categories" = "vendors") => {
        if (vendors && toSelect === "vendors") {
            const hasSelectedAll = hasSelectedAllOptions("vendors");
            vendors.forEach((product: ScoredProduct) => {
                methods.setValue(product.company.friendly_url, hasSelectedAll ? false : true);
            });
        }
        if (uniqueSubcategories && toSelect === "categories") {
            const hasSelectedAll = hasSelectedAllOptions("categories");
            uniqueSubcategories.forEach((category: any) => {
                methods.setValue(category.id, hasSelectedAll ? false : true);
            });
        }
    };

    const hasSelectedAllOptions = (filterKey: "vendors" | "categories") => {
        const formValues = methods.getValues();
        if (filterKey === "vendors")
            return vendors?.reduce((acc, prev) => acc && formValues[prev.company.friendly_url], true) || false;
        if (filterKey === "categories")
            return uniqueSubcategories?.reduce((acc, prev) => acc && formValues[prev.id], true) || false;
    };

    const chartProps: ICustomQuadrantChartProps = {
        data: filteredData || [],
        isLoading: loading,
        xDataKey: xDataKey,
        minY,
        maxY,
        minX,
        maxX,
        xAxisLabel: {
            left: "Satisfied",
            center: "SATISFACTION",
            right: "Seamless",
        },
        yDataKey,
        yAxisLabel: {
            top: "Most Popular",
            center: "POPULARITY",
            bottom: "Popular",
        },
        width: "container",
        height: 600,
        pointHeight: 50,
        uniqueIdentifier: "friendly_url",
        quadrantLabels: {
            topLeft: "High Potential",
            topLeftColor: theme.palette.primary["main"],
            topRight: "Chart Toppers",
            topRightColor: theme.palette.success[700],
            bottomLeft: "Niche",
            bottomLeftColor: theme.palette.error[700],
            bottomRight: "Challengers",
            bottomRightColor: (theme.palette as any).blue[700],
        },
        bgColors: {
            topLeftLabel: theme.palette.primary[200],
            topRightLabel: theme.palette.success[200],
            bottomLeftLabel: theme.palette.error[200],
            bottomRightLabel: (theme.palette as any).blue[200],
            ...(props.quadrantBgColors || {}),
        },
        CustomTooltip: false,
        CustomMultiPoint: ({matches, className}) => (
            <div className={`${styles.multiPoint} ${className}`}>
                <h3 className={styles.numberText}>{matches.length}</h3>
                <div className={styles.previewSection}>
                    {matches.map(match => {
                        return (
                            <img
                                key={match.company?.id}
                                alt="avatar"
                                src={
                                    match.company?.avatar
                                        ? match.company.avatar
                                        : createImageFromInitials(25, match.company.name, "", "company")
                                }
                                className={styles.customPointPreview}
                            />
                        );
                    })}
                </div>
            </div>
        ),
        showWaterMark: true,
    };

    const PreviewChart = () => (
        // @ts-ignore
        <QuadrantChart
            key={props.resizeKey}
            chartWrapperId={chartWrapperId + "preview"}
            mobileBreakpoint={undefined}
            CustomPoint={p => <ProductReviewQuadrantPoint categoryLinksEnabled={isParentCategory} isPreview {...p} />}
            {...chartProps}
        />
    );

    useEffect(() => {
        if (!builderChartDescription) {
            getDescription();
        }
        if (props.category) {
            setLoading(true);
            chartService.getProductReviewChartData(props.category?.id, onSuccessGetChartData, handleError);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.category]);

    useEffect(() => {
        handleSelectAll();
        handleSelectAll("categories");
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [vendors, uniqueSubcategories]);

    useEffect(() => {
        if (!props.onScreenshot) return;
        //@ts-ignore
        window.prerenderReady = false;
        if (chartData?.length && !loading && !hasCapturedScreenshot.current) {
            hasCapturedScreenshot.current = true;
            setTimeout(() => {
                if (!offScreenChartWrapperRef.current) return;
                domtoimage
                    .toPng(offScreenChartWrapperRef.current, {
                        imagePlaceholder:
                            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAIAQMAAAD+wSzIAAAABlBMVEX///+/v7+jQ3Y5AAAADklEQVQI12P4AIX8EAgALgAD/aNpbtEAAAAASUVORK5CYII",
                    })
                    .then((dataUrl: string) => {
                        offScreenChartWrapperRef.current?.remove();
                        convertImageUrlToFile(dataUrl, `${props.category?.id}_chart_seo_image`, (newFile: File) => {
                            chartService.uploadSEOImage(props.category?.id, newFile, (res: AxiosResponse) => {
                                props.onScreenshot?.(res.data?.src);
                                //@ts-ignore
                                window.prerenderReady = true;
                            });
                        });
                    })
                    .catch(error => {
                        console.error("Checking : Error capturing screenshot:", error);
                    });
            }, 100);
        }
    }, [chartData, loading]);

    return (
        <>
            <div className="d-flex flex-column flex-lg-row justify-content-center align-items-start">
                <div
                    ref={chartWrapperRef}
                    style={{maxWidth: 800}}
                    className="col-12 col-lg-8 d-flex flex-column align-items-center justify-content-center">
                    {/* @ts-ignore */}
                    <QuadrantChart
                        key={props.resizeKey}
                        chartWrapperId={chartWrapperId}
                        mobileBreakpoint={768}
                        mobilePointHeight={30}
                        CustomPoint={p => <ProductReviewQuadrantPoint categoryLinksEnabled={isParentCategory} {...p} />}
                        {...chartProps}
                    />
                    <p className="mx-4">{builderChartDescription}</p>
                </div>
                <div className="col-12 col-lg-4 flex-column ps-1 pt-3 printHidden">
                    <Typography variant="subtitle1" uppercase color={theme.palette.blue["main"]}>
                        Filter By
                    </Typography>
                    <FormProvider {...methods}>
                        <Accordion
                            disableGutters
                            elevation={0}
                            expanded={collapseExpanded.vendors}
                            onChange={() => {
                                setCollapseExpanded({...collapseExpanded, vendors: !collapseExpanded.vendors});
                            }}>
                            <AccordionSummary title={collapseExpanded.vendors ? "Collapse" : "Expand"}>
                                <div className="d-flex flex-row align-items-center justify-content-between w-100">
                                    <Typography variant="body1" color={theme.palette.blue["main"]}>
                                        Companies
                                    </Typography>
                                    {collapseExpanded.vendors ? <CloseIcon /> : <AddIcon />}
                                </div>
                            </AccordionSummary>
                            <AccordionDetails>
                                <div className="d-flex flex-column">
                                    {/* <label className="mt-2 d-flex align-items-center flex-wrap">
                                    <span className={styles.filterTitle}>Vendors</span>
                                    <div
                                        className={styles.filterSearchIcon}
                                        onClick={() =>
                                            setIsSearchOpen({...isSearchOpen, vendors: !isSearchOpen.vendors})
                                        }>
                                        <ReactIcon
                                            iconName={
                                                isSearchOpen["vendors"] ? "MdOutlineSearchOff" : "MdOutlineSearch"
                                            }
                                            size="20"
                                        />
                                    </div>
                                    <span onClick={() => handleSelectAll()} className={styles.selectAllBtn}>
                                        {hasSelectedAllOptions("vendors") ? "Unselect All" : "Select All"}
                                    </span>
                                    {isSearchOpen["vendors"] && (
                                        <TextBoxComponent
                                            id="search"
                                            containerClassName={styles.filterSearchBar}
                                            placeholder="Search For Vendors"
                                        />
                                    )}
                                </label> */}
                                    <div className="d-flex flex-column ps-2 align-items-start">
                                        {vendors.length > 1 && (
                                            <span onClick={() => handleSelectAll()} className={styles.selectAllBtn}>
                                                {hasSelectedAllOptions("vendors") ? "Unselect All" : "Select All"}
                                            </span>
                                        )}
                                        <div
                                            className="d-flex flex-column align-items-start w-100"
                                            style={{maxHeight: 210, overflowY: "auto", overflowX: "hidden"}}>
                                            {vendors.map((product: ScoredProduct) => {
                                                return (
                                                    <CheckboxWithLabel
                                                        key={product.company.friendly_url}
                                                        className={`mt-0 h-auto ${
                                                            !product.company?.name
                                                                .toUpperCase()
                                                                .includes(search?.toUpperCase() || "") && "d-none"
                                                        }`}
                                                        id={product.company.friendly_url}
                                                        label={product.company.name}
                                                    />
                                                );
                                            })}
                                        </div>
                                    </div>
                                </div>
                            </AccordionDetails>
                        </Accordion>
                        {props.showSubcategoryFilter && (
                            <Accordion
                                elevation={0}
                                disableGutters
                                expanded={collapseExpanded.categories}
                                onChange={() => {
                                    setCollapseExpanded({
                                        ...collapseExpanded,
                                        categories: !collapseExpanded.categories,
                                    });
                                }}>
                                <AccordionSummary
                                    title={collapseExpanded.categories ? "Collapse" : "Expand"}
                                    sx={{
                                        borderTop: collapseExpanded.categories ? "1px solid #E0E0E0" : "none",
                                    }}>
                                    <div className="d-flex flex-row align-items-center justify-content-between w-100">
                                        <Typography variant="body1" color={theme.palette.blue["main"]}>
                                            {SUBCATEGORIES_TEXT}
                                        </Typography>
                                        {collapseExpanded.categories ? <CloseIcon /> : <AddIcon />}
                                    </div>
                                </AccordionSummary>
                                <AccordionDetails>
                                    {/* <label className="mt-2 d-flex align-items-center flex-wrap">
                                    <span className={styles.filterTitle}>Subcategories</span>
                                    <div
                                        className={styles.filterSearchIcon}
                                        onClick={() =>
                                            setIsSearchOpen({
                                                ...isSearchOpen,
                                                categories: !isSearchOpen.categories,
                                            })
                                        }>
                                        <ReactIcon
                                            iconName={
                                                isSearchOpen["categories"] ? "MdOutlineSearchOff" : "MdOutlineSearch"
                                            }
                                            size="20"
                                        />
                                    </div>
                                    <span onClick={() => handleSelectAll("categories")} className={styles.selectAllBtn}>
                                        {hasSelectedAllOptions("categories") ? "Unselect All" : "Select All"}
                                    </span>
                                    {isSearchOpen["categories"] && (
                                        <TextBoxComponent
                                            id="categorySearch"
                                            containerClassName={styles.filterSearchBar}
                                            placeholder="Search For Subcategories"
                                        />
                                    )}
                                </label> */}
                                    <div className="d-flex flex-column ps-2 align-items-start">
                                        {uniqueSubcategories.length > 1 && (
                                            <span
                                                onClick={() => handleSelectAll("categories")}
                                                className={styles.selectAllBtn}>
                                                {hasSelectedAllOptions("categories") ? "Unselect All" : "Select All"}
                                            </span>
                                        )}
                                        <div
                                            className="d-flex flex-column align-items-start w-100"
                                            style={{maxHeight: 210, overflowY: "auto", overflowX: "hidden"}}>
                                            {uniqueSubcategories.map((category: {id: string; name: string}) => {
                                                return (
                                                    <CheckboxWithLabel
                                                        key={category.id}
                                                        className={`mt-0 h-auto ${
                                                            !category.name
                                                                .toUpperCase()
                                                                .includes(categorySearch?.toUpperCase() || "") &&
                                                            "d-none"
                                                        }`}
                                                        id={category.id}
                                                        label={category.name}
                                                    />
                                                );
                                            })}
                                        </div>
                                    </div>
                                </AccordionDetails>
                            </Accordion>
                        )}
                    </FormProvider>
                </div>
            </div>
            <div style={{maxHeight: 1, maxWidth: 1, overflow: "auto", zIndex: 3}}>
                <div
                    ref={offScreenChartWrapperRef}
                    id="productReviewQuadrantChartWrapper"
                    style={{
                        width: 1150,
                        height: 600,
                        overflow: "visible",
                    }}
                    className={`${
                        chartData?.length && !loading ? "d-flex" : "d-none"
                    } align-items-center justify-content-center`}>
                    <PreviewChart />
                </div>
            </div>
        </>
    );
};

export default ProductReviewQuadrantChart;
