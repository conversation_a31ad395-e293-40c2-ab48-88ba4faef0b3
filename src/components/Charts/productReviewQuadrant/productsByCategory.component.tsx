import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {useEffect} from "react";
import {IPopularCategory} from "../../../Interfaces/categories.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import useAppConfig from "../../../hooks/useAppConfig";
import useSettings from "../../../hooks/useSettings";
import {chartService} from "../../../services/chart.service";
import styles from "../../../styles/components/productReviewQuadrantChart.module.sass";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import QuadrantChart from "../quadrantChart.component";
import ProductReviewQuadrantPoint from "./productReviewQuadrantPoint.component";

interface IProps {
    category: IPopularCategory;
}

interface ScoredProduct {
    company: {name: string; friendly_url: string};
    category: [{id: string; name: string}];
    friendly_url: string;
    name: string;
    partner_satisfaction: number;
    popularity: number;
    recommendation_rating: number;
    satisfaction_rating: number;
    total_number_of_reviews_for_product: number;
    total_number_of_reviews_for_product_category: string;
}

const ProductByCategoryQuadrantChart = (props: IProps) => {
    const {settings, getAllCategories} = useSettings();
    const {configs} = useAppConfig({
        config_key: ["STACKCHART_MIN_Y", "STACKCHART_MAX_Y", "STACKCHART_MIN_X", "STACKCHART_MAX_X"],
    });
    const category = settings.all_categories?.find(c => c.id === props.category?.id);
    const theme = useTheme();
    const xDataKey = "satisfaction_rating";
    const yDataKey = "popularity";
    const isParentCategory = !props.category?.parent_id;
    const parent_category = isParentCategory
        ? category
        : settings.all_categories?.find(c => c.id === category?.parent_id);
    const configsFromCategory = isParentCategory
        ? {
              stack_chart_min_y: parent_category?.stack_chart_min_y,
              stack_chart_max_y: parent_category?.stack_chart_max_y,
              stack_chart_min_x: parent_category?.stack_chart_min_x,
              stack_chart_max_x: parent_category?.stack_chart_max_x,
          }
        : {
              stack_chart_min_y: category?.stack_chart_min_y ?? parent_category?.stack_chart_min_y,
              stack_chart_max_y: category?.stack_chart_max_y ?? parent_category?.stack_chart_max_y,
              stack_chart_min_x: category?.stack_chart_min_x ?? parent_category?.stack_chart_min_x,
              stack_chart_max_x: category?.stack_chart_max_x ?? parent_category?.stack_chart_max_x,
          };
    const minY = configsFromCategory?.stack_chart_min_y
        ? Number(configsFromCategory?.stack_chart_min_y)
        : Number(configs.find(c => c.key === "STACKCHART_MIN_Y")?.value || "0");
    const maxY = configsFromCategory?.stack_chart_max_y
        ? Number(configsFromCategory?.stack_chart_max_y)
        : Number(configs.find(c => c.key === "STACKCHART_MAX_Y")?.value || "1");
    const minX = configsFromCategory?.stack_chart_min_x
        ? Number(configsFromCategory?.stack_chart_min_x)
        : Number(configs.find(c => c.key === "STACKCHART_MIN_X")?.value || "0");
    const maxX = configsFromCategory?.stack_chart_max_x
        ? Number(configsFromCategory?.stack_chart_max_x)
        : Number(configs.find(c => c.key === "STACKCHART_MAX_X")?.value || "5");
    const chartWrapperId = "productReviewQuadrantChartWrapper";

    const chartQuery = useQuery<ScoredProduct[]>(
        ["popular-categories-chart", props.category.friendly_url],
        () => {
            return new Promise<ScoredProduct[]>((resolve, reject) => {
                chartService.getProductReviewChartData(
                    props.category?.id,
                    (res: AxiosResponse) => {
                        resolve(res.data.data);
                    },
                    err => {
                        reject(err);
                    },
                );
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const chartProps = {
        data: chartQuery.data || ([] as ScoredProduct[]),
        isLoading: chartQuery.isLoading,
        xDataKey: xDataKey,
        minY,
        maxY,
        minX,
        maxX,
        xAxisLabel: {
            left: "Satisfied",
            center: "SATISFACTION",
            right: "Seamless",
        },
        yDataKey,
        yAxisLabel: {
            top: "Most Popular",
            center: "POPULARITY",
            bottom: "Popular",
        },
        width: "container" as const,
        height: 600,
        pointHeight: 50,
        uniqueIdentifier: "friendly_url",
        quadrantLabels: {
            topLeft: "High Potential",
            topLeftColor: theme.palette.primary["main"],
            topRight: "Chart Toppers",
            topRightColor: theme.palette.success[700],
            bottomLeft: "Niche",
            bottomLeftColor: theme.palette.error[700],
            bottomRight: "Challengers",
            bottomRightColor: (theme.palette as any).blue[700],
        },
        bgColors: {
            topLeftLabel: theme.palette.primary[200],
            topRightLabel: theme.palette.success[200],
            bottomLeftLabel: theme.palette.error[200],
            bottomRightLabel: (theme.palette as any).blue[200],
        },
        CustomTooltip: false,
        CustomMultiPoint: ({matches, className}) => (
            <div className={`${styles.multiPoint} ${className}`}>
                <h3 className={styles.numberText}>{matches.length}</h3>
                <div className={styles.previewSection}>
                    {matches.map(match => {
                        return (
                            <img
                                key={match.company?.id}
                                alt="avatar"
                                src={
                                    match.company?.avatar
                                        ? match.company.avatar
                                        : createImageFromInitials(25, match.company.name, "", "company")
                                }
                                className={styles.customPointPreview}
                            />
                        );
                    })}
                </div>
            </div>
        ),
        showWaterMark: true,
    };

    useEffect(() => {
        if (!settings.all_categories?.length) getAllCategories();
    }, []);

    return (
        <>
            <QuadrantChart
                chartWrapperId={chartWrapperId}
                mobileBreakpoint={768}
                mobilePointHeight={30}
                CustomPoint={p => <ProductReviewQuadrantPoint categoryLinksEnabled={isParentCategory} {...p} />}
                {...chartProps}
            />
        </>
    );
};

export default ProductByCategoryQuadrantChart;
