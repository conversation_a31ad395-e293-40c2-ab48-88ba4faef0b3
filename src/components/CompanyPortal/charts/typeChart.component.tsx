import {Tooltip} from "@mui/material";
import useTheme from "@mui/system/useTheme";
import {useEffect, useState} from "react";
import {IPartnerChartTypeItem} from "../../../Interfaces/partnerEngagement.interface";
import {PartnerPageService} from "../../../services/partnerpage.service";
import styles from "../../../styles/components/partnerPortalTab.module.sass";
import CardComponent from "../../../uicomponents/Cards/Card.component";
import ParagraphComponent from "../../../uicomponents/Typography/Paragraph.component";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import RadarChartComponent from "../../Charts/RadarChart.component";
import {InfoIcon} from "../../Icons";

interface IProps {
    partnerPageId?: string;
}

const TypeChartComponent = (props: IProps) => {
    const [loading, setLoading] = useState(props.partnerPageId ? false : true);
    const [data, setData] = useState<IPartnerChartTypeItem[]>([]);
    const [totalCount, setTotalCount] = useState<number>(15);
    const theme = useTheme();

    const getFriendlySubjectName = (subject: string) => {
        if (subject === "Article") return "Blog";
        return subject;
    };

    const onSuccessLoadContent = res => {
        setData(res.data?.data.map((item: any) => ({...item, subject: getFriendlySubjectName(item.subject)})));
        const maxValue = res.data.data?.reduce(
            (prev, current) => (prev.count > current.count ? prev.count : current),
            1,
        );
        setTotalCount(maxValue);
        setLoading(false);
    };

    useEffect(() => {
        if (props.partnerPageId) {
            setLoading(true);
            PartnerPageService.loadTypeData(props.partnerPageId || "", onSuccessLoadContent);
        }
    }, [props.partnerPageId]);

    return (
        <ErrorBoundary>
            <CardComponent
                title={
                    <div className="d-flex align-items-center justify-content-between">
                        <ParagraphComponent size="xs" text="Engagement by Type" />
                        <Tooltip
                            title="Engagement metrics rolled up by type"
                            placement="bottom-end"
                            classes={{
                                tooltip: "bg-light text-dark p-2",
                            }}>
                            <div>
                                <InfoIcon size={18} color="#fff" />
                            </div>
                        </Tooltip>
                    </div>
                }
                backgroundColor={theme.palette.blue[600]}
                headerClassName={styles.partnerPortalHeaderTimeSpent}>
                <RadarChartComponent
                    axisDataKey="subject"
                    data={data}
                    dataKeys={[{key: "count", name: "Clicks", fill: theme.palette.blue[200]}]}
                    showGrid
                    gridFill={theme.palette.blue[400]}
                    maxValue={totalCount}
                    loading={loading}
                    labelStroke={theme.palette.blue[500]}
                    polarRadiusAxisFill={theme.palette.blue[100]}
                    customTooltip={({active, payload}) => {
                        if (active && payload && payload.length) {
                            const data = payload[0].payload;
                            return (
                                <div
                                    className="bg-light d-flex flex-column p-2 rounded shadow-sm"
                                    style={{minWidth: 100}}>
                                    <ParagraphComponent size="xs" text={data.subject} />
                                    <hr className="fadedBorder m-0 p-0" />
                                    <ParagraphComponent size="xxs" text={`Clicks: ${data.count}`} />
                                </div>
                            );
                        }
                        return null;
                    }}
                    labelColor="#fff"
                />
            </CardComponent>
        </ErrorBoundary>
    );
};

export default TypeChartComponent;
