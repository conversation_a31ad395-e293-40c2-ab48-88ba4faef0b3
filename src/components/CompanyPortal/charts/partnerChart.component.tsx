import {Tooltip} from "@mui/material";
import useTheme from "@mui/system/useTheme";
import {useEffect, useState} from "react";
import {IPartnerChartInnerItem, IPartnerChartOuterItem} from "../../../Interfaces/partnerEngagement.interface";
import {PartnerPageService} from "../../../services/partnerpage.service";
import styles from "../../../styles/components/partnerPortalTab.module.sass";
import CardComponent from "../../../uicomponents/Cards/Card.component";
import ParagraphComponent from "../../../uicomponents/Typography/Paragraph.component";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import PieChartComponent from "../../Charts/pieChart.component";
import {InfoIcon} from "../../Icons";

interface IProps {
    partnerPageId?: string;
}

const PartnerChartComponent = (props: IProps) => {
    const [loading, setLoading] = useState(props.partnerPageId ? false : true);
    const [inner, setInner] = useState<{
        total_count: number;
        data: IPartnerChartInnerItem[];
    }>({
        total_count: 0,
        data: [],
    });
    const [outer, setOuter] = useState<{
        total_count: number;
        data: IPartnerChartOuterItem[];
    }>({
        total_count: 0,
        data: [],
    });
    const partners = inner?.data?.reduce((acc, item) => {
        acc[item.partner_id] = item.category;
        return acc;
    }, {});
    const theme = useTheme();
    const actionColors = {
        partnerPortalContentDownload: "#0B2BD9",
        partnerPortalContentClick: "#0B66E3",
        partnerPortalView: "#008CCC",
        partnerPortalChatOpen: theme.palette.sky[700],
    };

    const onSuccessLoadContent = res => {
        setInner(res.data.inner);
        setOuter(res.data.outer);
        setLoading(false);
    };

    useEffect(() => {
        if (props.partnerPageId) {
            setLoading(true);
            PartnerPageService.loadPartnerData(props.partnerPageId || "", onSuccessLoadContent);
        }
    }, [props.partnerPageId]);

    const friendlyActionNames = {
        partnerPortalContentDownload: "Downloads",
        partnerPortalContentClick: "Clicks",
        partnerPortalView: "Views",
        partnerPortalChatOpen: "Chats",
    };

    return (
        <ErrorBoundary>
            <CardComponent
                title={
                    <div className="d-flex align-items-center justify-content-between">
                        <ParagraphComponent size="xs" text="Engagement by Partner" />
                        <Tooltip
                            title="Engagement metrics breakdown by partner"
                            placement="bottom-end"
                            classes={{
                                tooltip: "bg-light text-dark p-2",
                            }}>
                            <div>
                                <InfoIcon size={18} color="#008CCC" />
                            </div>
                        </Tooltip>
                    </div>
                }
                backgroundColor={theme.palette.sky[200]}
                headerClassName={styles.partnerPortalHeaderChats}>
                <PieChartComponent
                    data={inner?.data?.map((item: IPartnerChartInnerItem) => {
                        return {
                            name: "Engagement Count: ",
                            value: Math.round(inner.total_count * item.percentage),
                            label: item.category,
                            type: "inner",
                            percentage: Math.round(item.percentage * 100),
                        };
                    })}
                    dataKey="value"
                    outerDataKey="value"
                    outerData={outer?.data?.map((item: IPartnerChartOuterItem) => ({
                        name: item.category,
                        value: item.count,
                        label: `${partners[item.partner_id] + " " || ""}${
                            friendlyActionNames[item.category] || "Unknown"
                        }`,
                        type: "outer",
                        percentage: Math.round(item.percentage * 100),
                    }))}
                    generateOuterColor={(data: any) => {
                        return actionColors[data.name] || theme.palette.sky[700];
                    }}
                    height={250}
                    allowedColors={[theme.palette.sky[700], theme.palette.sky[500]]}
                    loading={loading}
                    showTooltip
                    customLoaderProps={{
                        big: true,
                        version: "iconWhite",
                    }}
                    customTooltip={({active, payload}) => {
                        if (active && payload && payload.length) {
                            const data = payload[0].payload;
                            if (data.type === "inner") {
                                return (
                                    <div className="bg-light d-flex flex-column p-2 rounded shadow-sm">
                                        <ParagraphComponent size="xs" text={data.label} />
                                        <hr className="fadedBorder m-0 p-0" />
                                        <ParagraphComponent
                                            size="xxs"
                                            text={`Engagment Percentage: ${data.percentage}%`}
                                        />
                                        <ParagraphComponent size="xxs" text={`Total Engagement: ${data.value}`} />
                                    </div>
                                );
                            }
                            return (
                                <div className="bg-light d-flex flex-column p-2 rounded shadow-sm">
                                    <ParagraphComponent size="xs" text={data.label} />
                                    <hr className="fadedBorder m-0 p-0" />
                                    <ParagraphComponent
                                        size="xxs"
                                        text={`Engagement Percentage: ${data.percentage}%`}
                                    />
                                    <ParagraphComponent size="xxs" text={`Total Engagement: ${data.value}`} />
                                </div>
                            );
                        }
                        return null;
                    }}
                    hideInnerLabels
                    hideOuterLabels
                />
            </CardComponent>
        </ErrorBoundary>
    );
};

export default PartnerChartComponent;
