import Tooltip from "@mui/material/Tooltip";
import useTheme from "@mui/system/useTheme";
import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {OverlayTrigger, Popover} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {IPartnerChangeOverTimeItem} from "../../../Interfaces/partnerEngagement.interface";
import {PartnerPageService} from "../../../services/partnerpage.service";
import styles from "../../../styles/components/partnerPortalTab.module.sass";
import MuiButtonComponent from "../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import MuiIconButtonComponent from "../../../uicomponents/Buttons/MuiIconButton.component";
import CardComponent from "../../../uicomponents/Cards/Card.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import ParagraphComponent from "../../../uicomponents/Typography/Paragraph.component";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {formatDate} from "../../../utils/formatDate";
import {LineChartComponent} from "../../Charts/lineChart.component";
import {InfoIcon} from "../../Icons/info.icon";

interface IProps {
    partnerPageId?: string;
}

const oneDay = 24 * 60 * 60 * 1000;
const oneWeek = 7 * oneDay;
const oneMonth = 30 * oneDay;
const todayDateString = new Date().toISOString().split("T")[0];

const ChangeOverTime = (props: IProps) => {
    const [loading, setLoading] = useState(props.partnerPageId ? false : true);
    const [data, setData] = useState<IPartnerChangeOverTimeItem[]>([]);
    const [dates, setDates] = useState<any>({
        startDate: null,
        endDate: null,
    });
    const [show, setShow] = useState(false);
    const theme = useTheme();
    const methods = useForm();
    const difference = dates.endDate && dates.startDate ? dates.endDate - dates.startDate : -1;
    const showDatesAs =
        difference === -1 ? "week" : difference <= oneWeek ? "day" : difference <= oneMonth ? "week" : "month";
    const endDate = methods.watch("endDate");
    const startDate = methods.watch("startDate");
    const formattedStartDate =
        startDate && Date.parse(startDate) ? new Date(startDate).toISOString().split("T")[0] : undefined;

    const onLoadPartnerData = (res: AxiosResponse) => {
        setData(res.data);
        setLoading(false);
    };

    const handleDateChange = (data: any) => {
        if (props.partnerPageId && data.startDate && data.endDate) {
            if (!Date.parse(data.startDate) || !Date.parse(data.endDate)) {
                if (!Date.parse(data.startDate))
                    methods.setError("startDate", {type: "manual", message: "Invalid date"});
                if (!Date.parse(data.endDate)) methods.setError("endDate", {type: "manual", message: "Invalid date"});
                return;
            }
            setLoading(true);
            setDates({
                startDate: new Date(data.startDate),
                endDate: new Date(data.endDate),
            });
            setShow(false);
            PartnerPageService.loadChangeOverTime(
                {
                    partnerPageId: props.partnerPageId || "",
                    startDate: formatDate(new Date(data.startDate).toISOString(), "yyyy-mm-dd") + " 00:00:00",
                    endDate: formatDate(new Date(data.endDate).toISOString(), "yyyy-mm-dd") + " 23:59:59",
                },
                onLoadPartnerData,
            );
            methods.reset();
        }
    };

    useEffect(() => {
        if (props.partnerPageId) {
            setLoading(true);
            PartnerPageService.loadChangeOverTime(
                {
                    partnerPageId: props.partnerPageId || "",
                },
                onLoadPartnerData,
            );
        }
    }, [props.partnerPageId]);

    return (
        <ErrorBoundary>
            <CardComponent
                // title="Engagement Over Time"
                title={
                    <div className="d-flex align-items-center">
                        <ParagraphComponent size="xs" text="Engagement Over Time" />
                        <Tooltip
                            title="Engagement metrics over a defined period (default by last month by week)"
                            placement="bottom"
                            classes={{
                                tooltip: "bg-light text-dark p-2",
                            }}>
                            <div>
                                <InfoIcon size={18} color="#fff" />
                            </div>
                        </Tooltip>
                    </div>
                }
                backgroundColor={theme.palette.blue[600]}
                headerStyles={{
                    ".MuiTypography-root": {
                        color: `#fff !important`,
                    },
                }}
                headerActions={
                    <OverlayTrigger
                        placement="left"
                        show={show}
                        onToggle={() => setShow(!show)}
                        trigger="click"
                        overlay={p => {
                            return (
                                <Popover {...p}>
                                    <Popover.Body>
                                        <FormProvider {...methods}>
                                            <form
                                                onSubmit={methods.handleSubmit(handleDateChange)}
                                                className="d-flex flex-column px-3 py-2">
                                                <ParagraphComponent text="Start Date" size="xs" position="start" />
                                                <TextBoxComponent
                                                    id="startDate"
                                                    type="date"
                                                    isRequired
                                                    isDate
                                                    containerClassName="mb-2"
                                                    maxDate={
                                                        endDate && endDate !== "Invalid Date"
                                                            ? methods.watch("endDate")
                                                            : todayDateString
                                                    }
                                                />
                                                <ParagraphComponent text="End Date" size="xs" position="start" />
                                                <TextBoxComponent
                                                    id="endDate"
                                                    type="date"
                                                    isRequired
                                                    isDate
                                                    containerClassName="mb-2"
                                                    minDate={formattedStartDate}
                                                    maxDate={todayDateString}
                                                />
                                                <MuiButtonComponent id="submit" type="submit" size="xs">
                                                    Filter
                                                </MuiButtonComponent>
                                            </form>
                                        </FormProvider>
                                    </Popover.Body>
                                </Popover>
                            );
                        }}>
                        <div>
                            <MuiIconButtonComponent
                                id="moreChangeOverTime"
                                icon="MdMoreHoriz"
                                size="xs"
                                backgroundColor={theme.palette.blue[800]}
                            />
                        </div>
                    </OverlayTrigger>
                }
                headerClassName={styles.partnerPortalHeaderTemplateUsage}>
                <LineChartComponent
                    data={data.map((item: IPartnerChangeOverTimeItem) => {
                        const weekStart = new Date(new Date().getFullYear(), 0, 1);
                        weekStart.setDate(weekStart.getDate() + 7 * (Number(item.name) - 1));
                        const weekEnd = new Date(new Date().getFullYear(), 0, 1);
                        weekEnd.setDate(weekEnd.getDate() + 7 * Number(item.name));
                        const monthStart = new Date(new Date().getFullYear(), Number(item.name) - 1, 1);
                        const monthEnd = new Date(new Date().getFullYear(), Number(item.name), 1);
                        monthEnd.setDate(monthEnd.getDate() - 1);
                        return {
                            xAxisName:
                                showDatesAs === "day"
                                    ? formatDate(item.complete_date, "D")
                                    : showDatesAs === "week"
                                    ? `${formatDate(weekStart.toISOString(), "D")} - ${formatDate(
                                          weekEnd.toISOString(),
                                          "D",
                                      )}`
                                    : `${formatDate(monthStart.toISOString(), "D")} - ${formatDate(
                                          monthEnd.toISOString(),
                                          "D",
                                      )}`,
                            yAxisName: item.count,
                            barName: item.name,
                        };
                    })}
                    dataKeys={[
                        {
                            keyName: "yAxisName",
                            label: "Count",
                            fillColor: theme.palette.blue[400],
                        },
                    ]}
                    xAxisLabel={`Time in ${showDatesAs}s`}
                    yAxisLabel="Engagement"
                    showLegend={false}
                    xAxisLabelColor="#fff"
                    yAxisLabelColor="#fff"
                    xAxisColor="#fff"
                    yAxisColor="#fff"
                    showGridLines={false}
                    showTooltip
                    width={"100%"}
                    height={250}
                    chartMargin={{
                        top: 0,
                        right: 0,
                        left: 0,
                        bottom: 0,
                    }}
                    asArea
                    loading={loading}
                    customLoaderProps={{
                        big: true,
                        version: "iconWhite",
                    }}
                    hideTick
                    customTooltip={({active, payload}) => {
                        if (active && payload && payload.length) {
                            const data = payload[0].payload;
                            return (
                                <div
                                    className="bg-light d-flex flex-column p-2 rounded shadow-sm"
                                    style={{minWidth: 100}}>
                                    <ParagraphComponent size="xs" text={data.xAxisName} />
                                    <hr className="fadedBorder m-0 p-0" />
                                    <ParagraphComponent size="xxs" text={`Engagement Count: ${data.yAxisName}`} />
                                </div>
                            );
                        }
                        return null;
                    }}
                />
            </CardComponent>
        </ErrorBoundary>
    );
};

export default ChangeOverTime;
