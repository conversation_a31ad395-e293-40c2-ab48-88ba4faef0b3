import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {useLocation, useNavigate} from "react-router";
import {useParams} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import {PartnerPageService} from "../../services/partnerpage.service";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";

const AcceptInvite = () => {
    const [acceptReasons, setAcceptReasons] = useState<any[]>([]);
    const {authState} = useAuthState();
    const isLoggedIn = authState.authenticated;
    const {inviteId, subDomain} = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const notify = useNotification();
    const currentPartnerOptionId = acceptReasons?.find(item => item.name.toUpperCase() === "CURRENT PARTNER")?.id;

    const onSuccessRequestAccess = () => {
        navigate(routeConfig.VendorPortal.path + `?as=${location.state?.as || authState.company_id}`);
    };

    const onSuccessGetAcceptReasons = (res: AxiosResponse) => {
        setAcceptReasons(res.data);
    };

    const onError = (err: AxiosError) => {
        if (err.response?.status === 404) {
            notify("We’re sorry this link has expired.", "Error");
            navigate(routeConfig.Home.path);
            return;
        }
        notify(getErrorFromArray(err), "Error");
        navigate(routeConfig.Home.path);
    };

    useEffect(() => {
        if (inviteId && isLoggedIn && currentPartnerOptionId) {
            PartnerPageService.acceptPartnerInvite(
                inviteId,
                currentPartnerOptionId,
                "",
                onSuccessRequestAccess,
                onError,
            );
            return;
        }
        // eslint-disable-next-line
    }, [inviteId, isLoggedIn, currentPartnerOptionId]);

    useEffect(() => {
        if (!isLoggedIn) {
            window.location.href = `${window.location.protocol}//${subDomain}.${window.location.host}${routeConfig.Login.path}?redirect_url=${window.location.pathname}`;
            return;
        }
        PartnerPageService.getInvitationAcceptReasons(onSuccessGetAcceptReasons);
        // eslint-disable-next-line
    }, [isLoggedIn]);

    return (
        <>
            <Loader loading={isLoggedIn}></Loader>
        </>
    );
};

export default AcceptInvite;
