import {useQuery} from "@tanstack/react-query";
import {useState} from "react";
import {useParams} from "react-router-dom";
import {ICompanyPartner} from "../../Interfaces/oneToOneChat.interface";
import {
    CHANNEL_PROGRAM_COMPANY_ID,
    CHANNEL_PROGRAM_SUBDOMAIN,
    DEFAULT_QUERY_CONFIGS,
} from "../../constants/commonStrings.constant";
import {useNaviStack} from "../../hooks/fetches/useNaviStack";
import {IActiveCompany} from "../../hooks/useActiveCompany";
import {useSubdomain} from "../../hooks/useSubdomain";
import {mspPartnersService} from "../../services/mspPartners.service";
import {PartnerPageService} from "../../services/partnerpage.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Advertisement from "../../uicomponents/Organism/Advertisement/advertisement.component";
import ChannelProgramResourcesV2 from "../../uicomponents/Organism/ChannelProgramResourcesV2/ChannelProgramResourcesV2";
import CompanyExpensesUpgradeCard from "../../uicomponents/Organism/CompanyExpenses/CompanyExpensesUpgradeCard.component";
import MSPTrackedSubscriptionsTab from "../../uicomponents/Organism/MSPTrackedSubscriptionsTab/MSPTrackedSubscriptionsTab.component";
import MspTechStackTab from "../../uicomponents/Organism/MspTechStackTab/MspTechStackTab.component";
import MyVendorPortalsTab from "../../uicomponents/Organism/MyVendorPortalsTab/myVendorPortalsTab.component";
import SavedDocuments from "../../uicomponents/Organism/SavedDocuments";
import UpcomingEventsWidget from "../../uicomponents/Organism/UpcomingEventsWidget/UpcomingEventsWidget";
import CompanyPartnersModal from "../Modal/companyPartnersModal.component";
import MspManagedClientsWidget from "./MspManagedClientsWidget/MspManagedClientsWidget.component";
import {IS_BETTERTRACKER_SITE} from "../../constants/siteFlags";

interface IProps {
    disableActions?: boolean;
    ad?: any;
    adLocation?: any;
    isLoading: boolean;
    isClientMsp: boolean;
    isMSP: boolean;
    isMSPLocation: boolean;
    activeCompany: IActiveCompany | undefined;
    loadMissingBrandStack?: boolean;
}

interface IMSPCounts {
    total_saved_template: number;
    total_folder: number;
}

const NewMSPPartnerPortalTab = (props: IProps) => {
    const [showPartnersFor, setShowPartnersFor] = useState<string>("");
    const {tab} = useParams<{tab?: string}>();
    const company_id = props.activeCompany?.id;
    const userNotifications: any = useQuery(["user-notifications"])?.data || {};
    const {isMSPClientPage} = useSubdomain();
    const allNotifications = userNotifications?.pages?.flatMap(page => page.data) || [];

    const {data: companyPartnersData} = useQuery<ICompanyPartner[] | undefined>(
        ["companyPartners", props.activeCompany?.id],
        async () => {
            if (!company_id) return;
            const {data} = await PartnerPageService.getCompanyPartners(company_id);
            return data;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(company_id) && props.isMSP,
        },
    );

    const {
        myStackFilters,
        naviStack,
        myStackFiltersState,
        updateNaviStack,
        missingAdoptionStack,
        naviStackCategories,
        numOfCategoriesFilled,
    } = useNaviStack(company_id || "", {
        isCompany: true,
        calls: {
            myStackFilters: true,
            naviStack: true,
            missingAdoptionStack: !!props.loadMissingBrandStack,
            naviStackCategories: true,
        },
    });

    const partnerToShow = companyPartnersData?.find(partner => partner.id === showPartnersFor);
    const channelProgramPartners = companyPartnersData?.find(partner => partner.id === CHANNEL_PROGRAM_COMPANY_ID);
    const missedChatNotifications = allNotifications?.filter(item => {
        if (!item) return false;
    }).filter(
        ({action, status, author}) =>
            action === "oneToOneChat" &&
            status === "NotRead" &&
            channelProgramPartners?.claimers?.some(u => u.id === author?.id),
    );
    const showPulsingRedDotAlert = missedChatNotifications?.some(
        n => n.custom_properties?.creator_company_id === CHANNEL_PROGRAM_COMPANY_ID,
    );

    const {data: countsQueryData = {total_saved_template: 0, total_folder: 0}} = useQuery<IMSPCounts | undefined>(
        ["mspCounts", props.activeCompany?.id],
        async () => {
            if (!props.isMSP || props.isLoading || !company_id) {
                return {total_saved_template: 0, total_folder: 0};
            }
            const {data} = await mspPartnersService.getSavedDocumentsCount(company_id);
            return data;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(company_id) && props.isMSP && !props.isLoading,
        },
    );

    const {total_folder: totalFolders} = countsQueryData;

    /* const {isLoading: isLoadingClaimers} = useQuery<Partial<IPeople>[] | undefined>(
        ["claimersQuery", friendly_url],
        async () => {
            const promiseToReturn: Promise<Partial<IPeople>[] | any> = new Promise((resolve, reject) =>
                companyService
                    .getClaimers(friendly_url!)
                    .then((r: AxiosResponse<Partial<IPeople>[]>) => {
                        updateCompany.mutate({
                            ...activeCompany,
                            claimers: r.data,
                            mutate_type: MutateTypes.LocalUpdate,
                        });
                        resolve(r.data);
                    })
                    .catch(err => {
                        notify(getErrorFromArray(err), "Error");
                        reject(err);
                    }),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(friendly_url) && tab === "profileAdmin",
        },
    );
    const loadingInfo = isLoadingPartners || isLoadingClaimers || isLoadingCounts; */

    const handleViewChannel = (company_id: string) => {
        window.location.href = `${window.location.protocol}//${CHANNEL_PROGRAM_SUBDOMAIN}.${window.location.host}?as_company_id=${company_id}`;
    };

    const renderTabComponent = () => {
        switch (tab) {
            case "vendor-portals":
                return (
                    <Box className="newContainer" sx={theme => ({background: theme.palette.neutral[100]})}>
                        <MyVendorPortalsTab
                            company_id={company_id}
                            setShowPartnersFor={setShowPartnersFor}
                            isMSP={props.isMSP}
                        />
                    </Box>
                );
            case "saved-documents":
                return (
                    <Box className="newContainer" sx={theme => ({background: theme.palette.neutral[100]})}>
                        <SavedDocuments
                            companyId={company_id!}
                            totalFolders={totalFolders}
                            activeCompany={props.activeCompany}
                        />
                    </Box>
                );
            // case "eventsCalendar":
            //     return (
            //         <Box className="newContainer" sx={theme => ({background: theme.palette.neutral[100]})}>
            //             <EventsCalendar company={activeCompany} />
            //         </Box>
            //     );
            /* case "profile-admins":
                return (
                    <Box className="newContainer" sx={theme => ({background: theme.palette.neutral[100]})}>
                        <ProfileAdmins loadingInfo={loadingInfo} claimers={claimers} />
                    </Box>
                ); */
            case "subscriptions":
                return (
                    <MSPTrackedSubscriptionsTab
                        companyId={company_id}
                        company_name={props.activeCompany?.name}
                        company_profile_type={props.activeCompany?.company_profile_type}
                        isClientMsp={props.isClientMsp}
                    />
                );
            default:
                return (
                    <MspTechStackTab
                        company_id={company_id}
                        disableActions={props.disableActions}
                        setShowPartnersFor={setShowPartnersFor}
                        hideVendors={IS_BETTERTRACKER_SITE}
                        handleViewChannel={handleViewChannel}
                        showNotificationDot={!!missedChatNotifications?.length}
                        isClientMsp={props.isClientMsp}
                        missingAdoptionStack={missingAdoptionStack}
                        myStackFilters={myStackFilters}
                        myStackFiltersState={myStackFiltersState}
                        naviStack={naviStack}
                        naviStackCategories={naviStackCategories}
                        numOfCategoriesFilled={numOfCategoriesFilled}
                        updateNaviStack={updateNaviStack}
                        activeCompany={props.activeCompany}
                        isMSPLocation={props.isMSPLocation}
                    />
                );
        }
    };

    return (
        <>
            <Box
                className="printHidden grow"
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "24px",
                    maxWidth: "100%",
                    "@media (min-width: 992px)": {
                        maxWidth:
                            props.isClientMsp || isMSPClientPage || tab === "subscriptions" || IS_BETTERTRACKER_SITE
                                ? "calc(100% - 24px)"
                                : "calc(100% - 24px - 350px)",
                    },
                }}>
                {props?.ad?.name && !props.isClientMsp && (
                    // NOTE: Adding both medium and large advertisements here per Erica and Krista 5/7/2025 12:05 PM MST
                    // Before was only rendering the medium which was stupid
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            width: "100%",
                        }}>
                        <Advertisement
                            ad={props?.ad}
                            ad_location={props?.adLocation}
                            allowedCardType={"horizontal-lg"}
                        />
                        <Advertisement
                            ad={props?.ad}
                            ad_location={props?.adLocation}
                            allowedCardType={"horizontal-md"}
                        />
                    </Box>
                )}
                <Box>{renderTabComponent()}</Box>
                {!props.isClientMsp && !isMSPClientPage && !IS_BETTERTRACKER_SITE && (
                    <>
                        <Box
                            sx={() => ({
                                "@media (min-width: 760px)": {
                                    display: "none",
                                },
                            })}>
                            <ChannelProgramResourcesV2
                                elevation={0}
                                company={props?.activeCompany}
                                disableActions={props.disableActions}
                                title="MSP Resources"
                                onClick={() => handleViewChannel(company_id!)}
                                avatar="/Media/short-logo.png"
                                cardSx={{maxWidth: 350}}
                                forceVendor
                                hasUnread={!!missedChatNotifications?.length}
                            />
                        </Box>
                        {props?.activeCompany?.manage_clients && (
                            <Box
                                sx={() => ({
                                    display: "none",
                                    "@media (min-width: 760px)": {
                                        display: "none",
                                    },
                                })}>
                                <MspManagedClientsWidget
                                    msp_friendly_url={props?.activeCompany?.friendly_url}
                                    company_id={company_id}
                                    naviStack={naviStack}
                                    updateNaviStack={updateNaviStack}
                                />
                            </Box>
                        )}
                        <Box
                            sx={theme => ({
                                background: theme.palette.neutral[100],
                                display: "none",
                                "@media (min-width: 760px)": {
                                    display: "none",
                                },
                            })}>
                            <UpcomingEventsWidget
                                company_id={company_id}
                                isMSP={props?.activeCompany?.type_is_of_vendor === false}
                            />
                        </Box>
                    </>
                )}
                {tab !== "subscriptions" && (
                    <Box
                        sx={theme => ({
                            background: theme.palette.neutral[100],
                            "@media (min-width: 760px)": {
                                display: "none",
                            },
                        })}>
                        <CompanyExpensesUpgradeCard variant="card" />
                    </Box>
                )}
            </Box>
            {!props.isClientMsp && !isMSPClientPage && tab !== "subscriptions" && !IS_BETTERTRACKER_SITE && (
                <Box
                    className="printHidden"
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "24px",
                        width: "100%",
                        flexShrink: 0,
                        "@media (min-width:992px)": {width: "unset", minWidth: "350px", maxWidth: "350px"},
                    }}>
                    <Box
                        sx={{
                            display: "none",
                            "@media (min-width: 760px)": {
                                display: "block",
                            },
                        }}>
                        <ChannelProgramResourcesV2
                            elevation={0}
                            company={props.activeCompany}
                            disableActions={props.disableActions}
                            title="MSP Resources"
                            onClick={() => handleViewChannel(company_id!)}
                            avatar="/Media/short-logo.png"
                            cardSx={{maxWidth: 350}}
                            forceVendor
                            hasUnread={showPulsingRedDotAlert}
                        />
                    </Box>
                    {props.activeCompany?.manage_clients && (
                        <Box
                            sx={theme => ({
                                background: {xs: "none", lg: theme.palette.neutral[100]},
                                display: "none",
                                "@media (min-width: 760px)": {
                                    display: "block",
                                },
                            })}>
                            <MspManagedClientsWidget
                                msp_friendly_url={props.activeCompany?.friendly_url}
                                company_id={company_id}
                                naviStack={naviStack}
                                updateNaviStack={updateNaviStack}
                            />
                        </Box>
                    )}
                    <Box
                        sx={{
                            background: "transparent",
                            display: "none",
                            "@media (min-width: 760px)": {
                                display: "block",
                            },
                        }}>
                        <UpcomingEventsWidget
                            company_id={company_id}
                            isMSP={props.activeCompany?.type_is_of_vendor === false}
                        />
                    </Box>
                    {props?.ad?.name && (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                maxWidth: "350px",
                            }}>
                            <Advertisement
                                ad={props.ad}
                                ad_location={props.adLocation}
                                allowedCardType="stacked"
                                wrapperClassName="mb-3"
                            />
                        </Box>
                    )}
                    {props.isClientMsp && !props.isClientMsp && (
                        <Box
                            sx={{
                                background: "transparent",
                                display: "none",
                                "@media (min-width: 760px)": {
                                    display: "block",
                                },
                            }}>
                            <CompanyExpensesUpgradeCard variant="card" />
                        </Box>
                    )}
                </Box>
            )}
            {partnerToShow && showPartnersFor ? (
                <CompanyPartnersModal
                    show={true}
                    setShow={() => setShowPartnersFor("")}
                    type="claimers"
                    partner={partnerToShow || undefined}
                />
            ) : null}
        </>
    );
};

export default NewMSPPartnerPortalTab;
