import {useEffect, useMemo} from "react";
import Helmet from "react-helmet";
import {useNavigate, useParams} from "react-router-dom";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useActiveCompany from "../../hooks/useActiveCompany";
import usePermissions from "../../hooks/usePermissions";
import useRandomAdvertisement from "../../hooks/useRandomAdvertisement";
import {CompanyProfileTypes} from "../../Interfaces/businessRules.interface";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import NewMSPPartnerPortalTab from "./newMspPartnerPortalTab.component";
import useIsViewingAsAffiliateParent from "../../hooks/useIsViewingAsAffiliateParent";
import {IS_BETTERTRACKER_SITE} from "../../constants/siteFlags";

export default function MspMyStack() {
    const {
        activeCompany,
        setActiveCompany,
        userCompanies,
        isLoading: isLoadingActiveCompany,
        isClientMsp,
        isMSPLocation,
        isAffiliateMsp,
    } = useActiveCompany();
    const {tab} = useParams();
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const navigate = useNavigate();
    const isViewingAsAffiliateParent = useIsViewingAsAffiliateParent();

    const {ad, ad_location} = useRandomAdvertisement("My Stack");

    const hasViewAccess = useMemo(
        () =>
            hasPermissions([
                PERMISSION_GROUPS.MANAGE_STACK_READ,
                ...(tab === "subscriptions" ? [PERMISSION_GROUPS.MANAGE_CONTRACTS_READ] : []),
            ]),
        [hasPermissions, tab],
    );
    const isMSP = useMemo(() => {
        if (!activeCompany) return false;
        const {company_profile_type, type_is_of_vendor} = activeCompany;
        return (
            company_profile_type?.value === CompanyProfileTypes.MSP_BUSINESS_BASIC ||
            company_profile_type?.value === CompanyProfileTypes.MSP_BUSINESS_PREMIUM ||
            !type_is_of_vendor
        );
    }, [activeCompany]);

    useEffect(() => {
        if (!activeCompany || isLoadingPermissions) return;
        if (!hasViewAccess || !isMSP) {
            navigate(routeConfig.Home.path);
            return;
        }
        const claimedCompany = userCompanies?.find(c => c.friendly_url === tab);
        if (tab && claimedCompany && activeCompany?.friendly_url !== tab) {
            setActiveCompany(tab, claimedCompany.id);
            navigate(routeConfig.CompanyNaviStack.path.replace(":tab", "navistack"));
        }
    }, [activeCompany, tab, isLoadingPermissions, hasViewAccess, isMSP, navigate]);

    return (
        <>
            <Helmet>
                <title>{IS_BETTERTRACKER_SITE ? "Products" : "My Stack"}</title>
            </Helmet>
            <div className={ROUTE_CLASS}>
                <NewMSPPartnerPortalTab
                    ad={ad}
                    adLocation={ad_location}
                    activeCompany={activeCompany}
                    isClientMsp={isClientMsp}
                    isLoading={isLoadingActiveCompany}
                    isMSP={isMSP}
                    isMSPLocation={isMSPLocation}
                    loadMissingBrandStack={isAffiliateMsp || isViewingAsAffiliateParent}
                />
            </div>
        </>
    );
}
