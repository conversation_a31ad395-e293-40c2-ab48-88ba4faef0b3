import ButtonComponent from "../FormControls/button.component";
import {ReactNode, useState} from "react";
import {paginationService} from "../../services/pagination.service";
import {AxiosError, AxiosResponse} from "axios";
import useNotification from "../../hooks/useNotification";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";

interface IPaginationButton {
    text: string;
    url: string;
    id: string;
    onGetResult: (response: any) => void;
    getFullResponse?: boolean;
    className?: string;
    method?: "get" | "post";
    data?: any;
    setLoading?: (loading: boolean) => void;
    icon?: string | ReactNode;
    loaderVersion?: "iconOrange" | "iconWhite" | "iconBlue";
}
export function PaginationButton(props: IPaginationButton) {
    const [loadingRequest, setLoadingRequest] = useState<boolean>();
    const notify = useNotification();

    const onSuccess = (response: AxiosResponse<Array<any>>) => {
        setLoadingRequest(false);
        props.setLoading && props.setLoading(false);
        if (props.getFullResponse) {
            return props.onGetResult(response);
        }
        props.onGetResult(response.data);
    };

    const onError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        props.setLoading && props.setLoading(false);
        setLoadingRequest(false);
    };

    const loadRecords = () => {
        setLoadingRequest(true);
        props.setLoading && props.setLoading(true);
        paginationService
            .GetRecords(props.url, props.method || "post", props.data)
            .then(onSuccess)
            .catch(onError);
    };

    return (
        <>
            <ButtonComponent
                className={props.className ? props.className : "cpMainBtn"}
                id={props.id}
                onClick={() => loadRecords()}
                disabled={loadingRequest}>
                {loadingRequest ? <Loader inline loading version={props?.loaderVersion ?? "iconWhite"} /> : props.text}
                {props?.icon && props.icon}
            </ButtonComponent>
        </>
    );
}
