import ContentLoader from "react-content-loader";

const PitchInfoCardLoader = props => (
    <ContentLoader viewBox="0 0 400 330" height={330} width={400} {...props}>
        <rect x="0" y="0" rx="3" ry="3" width="400" height="17" />
        <rect x="20" y="20" rx="3" ry="3" width="350" height="17" />
        <rect x="20" y="40" rx="3" ry="3" width="270" height="17" />
        <rect x="0" y="60" rx="3" ry="3" width="400" height="17" />
        <rect x="20" y="80" rx="3" ry="3" width="200" height="17" />
        <rect x="20" y="100" rx="3" ry="3" width="80" height="17" />

        <circle cx="45" cy="170" r="30" />
        <rect x="90" y="130" width="100" height="17" />
        <rect x="90" y="155" width="100" height="17" />
        <rect x="90" y="180" width="100" height="17" />

        <circle cx="230" cy="170" r="30" />
        <rect x="280" y="130" width="100" height="17" />
        <rect x="280" y="155" width="100" height="17" />
        <rect x="280" y="180" width="100" height="17" />

        <circle cx="45" cy="280" r="30" />
        <rect x="90" y="235" width="100" height="17" />
        <rect x="90" y="268.2" width="100" height="17" />
        <rect x="90" y="302.6" width="100" height="17" />

        <circle cx="230" cy="280" r="30" />
        <rect x="280" y="235" width="100" height="17" />
        <rect x="280" y="268.2" width="100" height="17" />
        <rect x="280" y="302.6" width="100" height="17" />
    </ContentLoader>
);

export default PitchInfoCardLoader;
