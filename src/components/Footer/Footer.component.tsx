import Logo from "../Logo/logo.component";
import {HUBSPOT_NEWSLETTER_FORM_ID, HUBSPOT_NEWSLETTER_PORTAL_ID, PHONE} from "../../constants/commonStrings.constant";
import Container from "react-bootstrap/Container";
import styles from "../../styles/components/footer.module.sass";
import {Link, useLocation} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import HubspotForm from "react-hubspot-form";
import BrandConnector from "../Logo/brandConnector.component";
import {Col} from "react-bootstrap";
import OutboundLink from "../Links/OutboundLink.component";

export function Footer() {
    const location = useLocation();
    const isAdminPage = location.pathname.includes("admin/");
    return (
        <footer className={isAdminPage ? styles.footerContainer : ""}>
            {!isAdminPage && (
                <Container fluid className={`d-flex flex-wrap align-items-start ${styles.firstContainer}`}>
                    <Col className="col-12 col-lg-9 mb-5 mb-lg-0">
                        <Logo version="verticalWhite" className="d-none d-lg-block" width="140px" />
                        <Logo version="horizontalWhite" className="d-block d-lg-none" />
                        <div
                            className="
                        d-none
                        d-lg-flex
                        align-items-center
                        justify-content-center
                        justify-content-lg-start
                        flex-wrap
                        flex-lg-nowrap
                        my-4
                        my-lg-3">
                            <Link className="me-2 col-lg-auto my-lg-0" to={routeConfig.Home.path}>
                                Home
                            </Link>
                            <Link
                                className="mx-3 col-lg-auto my-lg-0 text-center text-nowrap"
                                to={routeConfig.Pitch.path}>
                                Channel Pitch
                            </Link>
                            <Link
                                className="mx-3 col-lg-auto my-lg-0 text-center text-nowrap"
                                to={routeConfig.Explorer.path}>
                                Channel Explorer
                            </Link>
                            <Link
                                className="mx-3 col-lg-auto my-lg-0 text-center text-nowrap"
                                to={routeConfig.Cash.path}>
                                Channel Cash
                            </Link>
                            <Link
                                className="mx-3 col-lg-auto my-lg-0 text-center text-nowrap"
                                to={routeConfig.Podcast.path}>
                                Channelfied Podcast
                            </Link>
                            <Link className="mx-3 col-lg-auto my-lg-0 text-center" to={routeConfig.AboutUs.path}>
                                About Us
                            </Link>
                            <Link className="mx-3 col-lg-auto my-lg-0 text-center" to={routeConfig.Contact.path}>
                                Contact Us
                            </Link>
                        </div>
                        <div className="mt-5">
                            <p>
                                Channel Program
                                <br /> 3168 Braverton St.
                                <br />
                                Suite 400
                                <br />
                                Edgewater, MD 210037
                            </p>
                        </div>
                    </Col>
                    <Col className="col-12 col-lg-3 text-center">
                        <div className="justify-content-center justify-content-lg-end d-flex">
                            <div className="col-10 col-lg-auto2">
                                <div className="d-flex align-items-center justify-content-center pb-2">
                                    <BrandConnector className="col-1" />
                                    <p className="px-2">Join our newsletter!</p>
                                </div>
                                <p className={styles.newsletterSubtitle}>Be the first to hear our latest news.</p>

                                <HubspotForm
                                    portalId={HUBSPOT_NEWSLETTER_PORTAL_ID}
                                    formId={HUBSPOT_NEWSLETTER_FORM_ID}
                                    loading={<p>Loading Newsletter...</p>}
                                    cssClass="hsForm"
                                />
                            </div>
                        </div>
                    </Col>
                </Container>
            )}
            <Container
                fluid
                className={`d-flex flex-wrap ${styles.secondContainer} ${isAdminPage && "justify-content-center"}`}>
                <div
                    className={`col-12 col-lg-3 order-3 order-lg-1 d-flex align-items-center justify-content-center justify-content-lg-start ${
                        isAdminPage && "justify-content-lg-center"
                    }`}>
                    <p>© {new Date().getFullYear()} Channel Program. All Rights Reserved.</p>
                </div>
                {!isAdminPage && (
                    <>
                        <div className="col-12 col-lg-6 order-2 d-flex align-items-center justify-content-center my-4 my-lg-0">
                            <Link to={routeConfig.Policy.path} target="_blank" rel="noreferrer">
                                Privacy Policy
                            </Link>
                            <span className={styles.barSeparator}>|</span>
                            <Link to={routeConfig.Terms.path} target="_blank" rel="noreferrer">
                                Terms of service
                            </Link>
                            <span className={styles.barSeparator}>|</span>
                            <Link to={routeConfig.AcceptableUsePolicy.path} target="_blank" rel="noreferrer">
                                Acceptable Use Policy
                            </Link>
                            <span className={styles.barSeparator}>|</span>
                            <p>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    className={styles.phoneIcon}>
                                    <path d="m20.487 17.14-4.065-3.696a1.001 1.001 0 0 0-1.391.043l-2.393 2.461c-.576-.11-1.734-.471-2.926-1.66-1.192-1.193-1.553-2.354-1.66-2.926l2.459-2.394a1 1 0 0 0 .043-1.391L6.859 3.513a1 1 0 0 0-1.391-.087l-2.17 1.861a1 1 0 0 0-.29.649c-.015.25-.301 6.172 4.291 10.766C11.305 20.707 16.323 21 17.705 21c.202 0 .326-.006.359-.008a.992.992 0 0 0 .648-.291l1.86-2.171a.997.997 0 0 0-.085-1.39z"></path>
                                </svg>
                                {PHONE}
                            </p>
                        </div>
                        <div
                            className={`col-12 col-lg-3 order-1 order-lg-3 d-flex align-items-center ${styles.socialLinksContainer}`}>
                            <OutboundLink
                                href="https://www.facebook.com/Channel-Program-113031624351535"
                                className={styles.socialLink}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <path d="M13.397 20.997v-8.196h2.765l.411-3.209h-3.176V7.548c0-.926.258-1.56 1.587-1.56h1.684V3.127A22.336 22.336 0 0 0 14.201 3c-2.444 0-4.122 1.492-4.122 4.231v2.355H7.332v3.209h2.753v8.202h3.312z"></path>
                                </svg>
                            </OutboundLink>
                            <OutboundLink href="https://www.twitter.com/bethechannel" className={styles.socialLink}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <path d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z"></path>
                                </svg>
                            </OutboundLink>
                            <OutboundLink
                                href="https://www.linkedin.com/company/channel-program/"
                                className={styles.socialLink}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <circle cx="4.983" cy="5.009" r="2.188"></circle>
                                    <path d="M9.237 8.855v12.139h3.769v-6.003c0-1.584.298-3.118 2.262-3.118 1.937 0 1.961 1.811 1.961 3.218v5.904H21v-6.657c0-3.27-.704-5.783-4.526-5.783-1.835 0-3.065 1.007-3.568 1.96h-.051v-1.66H9.237zm-6.142 0H6.87v12.139H3.095z"></path>
                                </svg>
                            </OutboundLink>
                            <OutboundLink
                                href="https://www.youtube.com/channel/UCQAgOa07oc3CN_IZpuS00wg"
                                className={styles.socialLink}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <path d="M21.593 7.203a2.506 2.506 0 0 0-1.762-1.766C18.265 5.007 12 5 12 5s-6.264-.007-7.831.404a2.56 2.56 0 0 0-1.766 1.778c-.413 1.566-.417 4.814-.417 4.814s-.004 3.264.406 4.814c.23.857.905 1.534 1.763 1.765 1.582.43 7.83.437 7.83.437s6.265.007 7.831-.403a2.515 2.515 0 0 0 1.767-1.763c.414-1.565.417-4.812.417-4.812s.02-3.265-.407-4.831zM9.996 15.005l.005-6 5.207 3.005-5.212 2.995z"></path>
                                </svg>
                            </OutboundLink>
                        </div>
                    </>
                )}
            </Container>
        </footer>
    );
}
