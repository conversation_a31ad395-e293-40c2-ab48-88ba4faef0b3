import {useLocation} from "react-router-dom";
import {useState, useEffect} from "react";
import {BuilderComponent, builder} from "@builder.io/react";

export function FooterForGlobal() {
    const [hideFooter, setHideFooter] = useState(false);
    const location = useLocation();
    const [builderFooterContentJson, setBuilderFooterContentJson] = useState<any>(null);

    useEffect(() => {
        builder
            .get("footer", {url: "/main"})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderFooterContentJson(res);
                }
            });
    }, []);

    useEffect(() => {
        //Check if route matches one of many
        setHideFooter(["/admin", "/reset-password/", "/reset-password"].includes(location.pathname));
    }, [location]);

    return builderFooterContentJson !== null ? (
        !hideFooter ? (
            <BuilderComponent model="footer" content={builderFooter<PERSON>ontent<PERSON><PERSON>} />
        ) : (
            <></>
        )
    ) : (
        <></>
    );
}
