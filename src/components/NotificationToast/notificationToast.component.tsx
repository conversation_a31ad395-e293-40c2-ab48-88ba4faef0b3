import {ToastContainer} from "react-bootstrap";
import Toast from "react-bootstrap/esm/Toast";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";

export default function NotificationToast() {
    const {settings, updateSettings} = useSettings();

    function handleClose() {
        updateSettings(UPDATE_SETTINGS, {notification: {show: false, message: "", title: "", delay: null}});
    }

    return (
        <div className="toaster">
            <ToastContainer position="top-end" className="position-fixed">
                <Toast
                    onClose={handleClose}
                    className="toaster"
                    show={settings.notification.show}
                    delay={settings.notification.delay || 5000}
                    autohide
                    bg="light">
                    <Toast.Header>
                        <span className="me-auto fw-600">{settings.notification.title || "Error"}</span>
                    </Toast.Header>
                    <Toast.Body>{settings.notification.message && settings.notification.message}</Toast.Body>
                </Toast>
            </ToastContainer>
        </div>
    );
}
