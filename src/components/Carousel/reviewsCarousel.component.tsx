import {Builder} from "@builder.io/react";
import {useQuery} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {useCallback, useMemo, useRef} from "react";
import {Link} from "react-router-dom";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useNotification from "../../hooks/useNotification";
import useWindowSize from "../../hooks/useWindowSize";
import {IReview} from "../../Interfaces/products.interface";
import {reviewService} from "../../services/review.service";
import {getErrorFromArray} from "../../utils/error.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import Loader from "../../utils/loader";
import ControlledStarRating from "../FormControls/ControlledStarRating.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import Carousel from "./carousel.component";
import styles from "./reviewsCarousel.module.sass";

export default function ReviewsCarousel() {
    const notify = useNotification();
    const offset = useRef(0);
    const windowSize = useWindowSize();
    const mobileSize = windowSize.width < 768;
    const isMobileSize = useMemo(() => mobileSize, [mobileSize]);

    // Be careful with this number, this is used to determine the active element to show proper style.
    const itemsToShow = isMobileSize ? 1 : 3;

    const {data: reviews} = useQuery<IReview[]>(
        ["latest-reviews"],
        () => {
            const getLatestReviews: Promise<IReview[]> = new Promise(resolve => {
                return reviewService
                    .getRecentReviews({items: 10, offset: offset.current})
                    .then((r: AxiosResponse<{next_offset: number; reviews: IReview[]}>) => resolve(r.data.reviews))
                    .catch(e => notify(getErrorFromArray(e), "Error"));
            });
            return getLatestReviews;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const carouselItems = useCallback(
        props => {
            const {currentIndex} = props;
            return !!reviews?.length
                ? reviews.map((review, index) => {
                      const active = isMobileSize
                          ? currentIndex - 1 === index
                          : currentIndex === reviews.length + 2
                          ? index === 0
                          : currentIndex > reviews.length + 2
                          ? index === currentIndex - reviews.length - 2
                          : currentIndex - 2 < 0
                          ? index === reviews.length - (currentIndex === 0 ? 2 : 1)
                          : currentIndex - 2 === index;
                      return (
                          <Link
                              to={{
                                  pathname: routeConfig.ProductDetails.path.replace(
                                      ":friendly_url",
                                      review?.model?.friendly_url || "",
                                  ),
                                  search: `?review_id=${review?.id}`,
                              }}
                              key={index}
                              className={`${styles.review} ${!!currentIndex && active ? styles.active : ""}`}>
                              <div className={styles.headerAvatar}>
                                  <div>
                                      <img
                                          src={
                                              review.reviewer?.avatar ||
                                              createImageFromInitials(
                                                  150,
                                                  review.reviewer?.name || "Product User",
                                                  "",
                                                  "user",
                                              )
                                          }
                                          alt={review.reviewer?.name}
                                      />
                                  </div>
                              </div>
                              <div className={styles.reviewContent}>
                                  <TitleContainer
                                      as="h4"
                                      text={review.reviewer?.name || "Product User"}
                                      position="center"
                                      noConnector
                                  />
                                  <SubtitleContainer
                                      as="p"
                                      text={review.title}
                                      position="center"
                                      className={styles.title}
                                  />
                                  {review?.satisfaction_rating && (
                                      <div className="text-center mt-4 d-flex flex-column align-items-center justify-content-center">
                                          <ControlledStarRating
                                              id="starRating"
                                              maxRating={5}
                                              value={Math.round(100 * parseInt(review?.satisfaction_rating)) / 100}
                                              iconSize={26}
                                              showLabels={false}
                                              containerClassName="m-0 me-1"
                                              readOnly
                                          />
                                          <p>Satisfaction Rating</p>
                                      </div>
                                  )}
                              </div>
                              <div className={styles.companyInfo}>
                                  <div>
                                      <img
                                          src={
                                              review.model?.company?.avatar ||
                                              createImageFromInitials(
                                                  150,
                                                  review.model?.company?.name || "Company",
                                                  "",
                                                  "company",
                                              )
                                          }
                                          alt={review?.model?.company?.name}
                                      />
                                  </div>
                                  <TitleContainer
                                      as="h4"
                                      text={review?.model?.company?.name || "Company"}
                                      position="center"
                                      noConnector
                                      className="mb-0"
                                  />
                              </div>
                          </Link>
                      );
                  })
                : [1, 2, 3, 4, 5, 6].map((_, index) => {
                      const active =
                          currentIndex === index + Math.round([1, 2, 3, 4, 5].length) / 2 ||
                          currentIndex === index + Math.round([1, 2, 3, 4, 5].length) / 2 + [1, 2, 3, 4, 5].length;
                      return (
                          <div
                              key={index}
                              className={`${styles.review} ${!!currentIndex && active ? styles.active : ""}`}>
                              <div className={styles.reviewContent}>
                                  <div className="d-flex justify-content-center">
                                      <Loader inline loading big />
                                  </div>
                              </div>
                          </div>
                      );
                  });
        },
        [reviews, isMobileSize],
    );

    return (
        <div className="w-100">
            <div className="d-flex justify-content-center">
                <TitleContainer as="h2" text="Latest Reviews" position="center" />
            </div>
            <Carousel
                show={itemsToShow}
                infiniteLoop
                items={carouselItems}
                contentClassName={styles.carouselContainer}
            />
        </div>
    );
}

Builder.registerComponent(ReviewsCarousel, {
    name: "Builder Latest Reviews Carousel",
    description: "Review carousel component",
    defaultStyles: {
        marginTop: "0",
    },
});
