.carouselContainer
    width: 100%
    display: flex
    flex-direction: column
    padding-top: 40px

.carouselWrapper
    display: flex
    width: 100%
    position: relative

.carouselContentWrapper
    width: 100%
    height: 100%

.carouselContent
    display: flex
    transition: all 250ms linear
    align-items: flex-end
    -ms-overflow-style: none
    scrollbar-width: none
    &::-webkit-scrollbar, .carouselContent::-webkit-scrollbar
        display: none
    & > *
        width: 100%
        flex-shrink: 0
        flex-grow: 1
    &.show-2 > *
        width: 50%
    &.show-3 > *
        width: calc(100% / 3)
    &.show-4 > *
        width: calc(100% / 4)

.leftArrow, .rightArrow
    position: absolute
    z-index: 1
    top: 50%
    transform: translateY(-50%)
    width: 48px
    height: 48px
    border-radius: 24px
    color: var(--cpWhite)
    font-weight: 700
    background-color: var(--cpBlue)

.leftArrow
    left: 24px

.rightArrow
    right: 24px

@media (hover: none) and (pointer: coarse)
    .leftArrow, .rightArrow
        display: none
