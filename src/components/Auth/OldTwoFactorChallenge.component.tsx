import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {IAuth} from "../../contexts/auth.context";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {authService} from "../../services/auth.service";
import {genericErrorString} from "../../utils/error.util";
import {RichTextSanitizer} from "../../utils/miscellaneous";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../FormControls/textBox.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";

export type T2FAModalConfig = {
    decodedHeader: IAuth;
    access_token: string;
    additional_info?: any;
    last_logged_in_at?: string | null;
};

type PropsType = T2FAModalConfig & {
    qrCode?: string;
    firstConfig?: boolean;
    onSuccess?: (response: AxiosResponse, config: T2FAModalConfig) => void;
    onCancel: () => void;
    codeSent: boolean;
};

export default function OldTwoFactorChallenge(props: PropsType) {
    const [showRecoveryInput, setShowRecoveryInput] = useState<boolean>(false);
    const [error, setError] = useState<string>("");

    const methods = useForm();
    const watcher = useWatch({control: methods.control});
    const {updateSettings} = useSettings();
    const config = {
        decodedHeader: props.decodedHeader,
        access_token: props.access_token,
        additional_info: props.additional_info,
        last_logged_in_at: props.last_logged_in_at,
    };

    const sendInfo = (data: any) => {
        let finalCode: string = "";
        if (data.code_full) {
            finalCode = data.code_full.trim();
        }
        setError("");
        updateSettings(UPDATE_SETTINGS, {loading: true});
        if (props.firstConfig) {
            authService.confirmTwoFA(finalCode, "", handleSuccess, handleError);
        } else {
            authService.twoFactorChallenge(
                finalCode,
                data.recovery_code,
                config.access_token ?? "",
                handleSuccess,
                handleError,
            );
        }
    };

    const handleSuccess = (response: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        props.onSuccess && props.onSuccess(response, config);
    };

    const handleError = (error: any) => {
        methods.reset();
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setError(genericErrorString(error));
    };

    const checkEmptyField = () => {
        if (Object.values(watcher).length < 1) {
            return true;
        }
        if (showRecoveryInput && watcher.recovery_code) return false;
        if (watcher.code_full) {
            return false;
        }
    };

    useEffect(() => {
        if (watcher.recovery_code) {
            return;
        }
        const inputValue = watcher.code_full;

        if (!inputValue) {
            methods.setFocus(`code_full`);
            return;
        }
        // eslint-disable-next-line
    }, [watcher]);

    const QRForm = (
        <>
            <FormProvider {...methods}>
                {error !== "" ? <p className="errorText errorColor text-center w-100">{error}</p> : null}
                {props.qrCode && (
                    <>
                        <SubtitleContainer
                            as="p"
                            position="center"
                            text="Use your Authenticator App to scan the QR code below."
                            size="18"
                        />
                        <div
                            className="d-flex justify-content-center pb-5 mt-3 mx-auto"
                            dangerouslySetInnerHTML={RichTextSanitizer(props.qrCode)}></div>
                    </>
                )}
                <form onSubmit={methods.handleSubmit(sendInfo)}>
                    {showRecoveryInput ? (
                        <div className="d-flex justify-content-center align-items-center w-100">
                            <TextBoxComponent
                                type="text"
                                id="recovery_code"
                                placeholder="Secret code"
                                label="Enter recovery code"
                                containerClassName="w-100 text-center"
                            />
                        </div>
                    ) : (
                        <>
                            <SubtitleContainer
                                as="p"
                                text="Enter code generated by your Authenticator App"
                                position="center"
                            />
                            <div className="d-flex justify-content-center align-items-center w-100">
                                <TextBoxComponent
                                    type="2fa"
                                    placeholder="******"
                                    id="code_full"
                                    maxLength={6}
                                    containerClassName="w-100 text-center"
                                />
                            </div>
                        </>
                    )}
                    <div className="col-12 text-center">
                        <ButtonComponent
                            type="submit"
                            className="cpMainBtn"
                            disabled={checkEmptyField()}
                            id="verifyCode">
                            Verify code
                        </ButtonComponent>
                    </div>
                    {!props.firstConfig && (
                        <ButtonComponent
                            className="textBtn col-12 mt-3"
                            id="chandeCodeType"
                            onClick={() => setShowRecoveryInput(!showRecoveryInput)}
                            type="button">
                            {showRecoveryInput ? "Try with normal code" : "Try with recovery code"}
                        </ButtonComponent>
                    )}
                </form>
            </FormProvider>
        </>
    );

    return <div className="d-flex flex-column justify-content-center">{QRForm}</div>;
}
