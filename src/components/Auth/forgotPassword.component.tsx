import {AxiosError, AxiosResponse} from "axios";
import {Dispatch, useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useNavigate} from "react-router";
import routeConfig from "../../constants/routeConfig";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {authService} from "../../services/auth.service";
import {validateEmail} from "../../utils/validation.util";
import ButtonComponent from "../FormControls/button.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import {Link} from "react-router-dom";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import useNotification from "../../hooks/useNotification";
import {getErrorFromArray} from "../../utils/error.util";
import {IS_CUSTOMER_BETTERTRACKER_SITE} from "../../constants/siteFlags";
import {ACCOUNT_NOT_FOUND} from "../../constants/commonStrings.constant";

interface IModalForgotPass {
    setForgotPassword: Dispatch<boolean>;
    forgotPassword: boolean;
    showRegisterLink?: boolean;
}

export default function ForgotPassword(props: IModalForgotPass) {
    const methods = useForm();
    const [recoverySent, setRecoverySent] = useState<boolean>(false);
    const {settings, updateSettings} = useSettings();
    const navigate = useNavigate();
    const notify = useNotification();

    const recoverPass = (data: any) => {
        updateSettings(UPDATE_SETTINGS, {loading: true});
        authService.forgotPassword(data.email_recovery, handleSuccessRecover, handleErrorRecover);
    };

    const handleSuccessRecover = (response: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        notify(response.data, "Success");
        props.setForgotPassword(false);
    };

    const handleErrorRecover = (error: AxiosError<any>) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setRecoverySent(false);
        const errorMsg = error.response?.data ?? getErrorFromArray(error);
        methods.setError("email_recovery", {
            type: "manual",
            message: errorMsg.message ?? ACCOUNT_NOT_FOUND,
        });
    };

    useEffect(() => {
        if (!props.forgotPassword) setRecoverySent(props.forgotPassword);
    }, [props.forgotPassword]);

    const goToCreateYourAccount = () => {
        navigate(routeConfig.Register.path);
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                gap: 3,
            }}>
            <Typography fontWeight={"600"} variant={"30"} color={"secondary"}>
                Forgot my password
            </Typography>
            <FormProvider {...methods}>
                <form onSubmit={methods.handleSubmit(recoverPass)} className="d-flex flex-column align-items-center">
                    {recoverySent ? (
                        <div className="w-100 text-center">
                            <h3>We have emailed your password reset link!</h3>
                        </div>
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                gap: 3,
                            }}>
                            <Typography fontWeight={"600"} variant={"subtitle3"} color={"secondary"}>
                                Type your email below to get a recovery link
                            </Typography>
                            <TextBoxComponent
                                id="email_recovery"
                                placeholder="Enter your registered email"
                                tooltipHelperText="Email is required"
                                label="Email"
                                isRequired
                                validateOnTheFly
                                validationSchema={v => validateEmail(String(v))}
                            />
                            <Box display="flex" justifyContent="center">
                                <MuiButtonComponent
                                    id="sendRecoveryLink"
                                    type="submit"
                                    variant={IS_CUSTOMER_BETTERTRACKER_SITE ? "contained" : "tonal"}
                                    sx={{
                                        width: "90%",
                                    }}>
                                    <Box sx={{fontWeight: "600", fontSize: "18px"}}>
                                        {settings.loading ? "Sending..." : "Send recovery link"}
                                    </Box>
                                </MuiButtonComponent>
                            </Box>
                        </Box>
                    )}
                </form>
            </FormProvider>
            {(props.showRegisterLink ?? true) && (
                <ButtonComponent
                    id="createYourAccount"
                    type="button"
                    onClick={goToCreateYourAccount}
                    className="textBtn">
                    <span>Create your Account</span>
                </ButtonComponent>
            )}
            <Link
                to="#"
                style={{fontWeight: 700, marginLeft: "16px"}}
                onClick={() => props.setForgotPassword(!props.forgotPassword)}>
                Back to Login
            </Link>
        </Box>
    );
}
