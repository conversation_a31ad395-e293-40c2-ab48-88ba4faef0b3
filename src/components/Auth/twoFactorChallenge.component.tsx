import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {IAuth} from "../../contexts/auth.context";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {authService} from "../../services/auth.service";
import {genericErrorString} from "../../utils/error.util";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import {Stack} from "@mui/material";
import useTheme from "@mui/material/styles/useTheme";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
export type T2FAModalConfig = {
    decodedHeader: IAuth;
    access_token: string;
    additional_info?: any;
    last_logged_in_at?: string | null;
};

type PropsType = T2FAModalConfig & {
    qrCode?: string;
    firstConfig?: boolean;
    onSuccess?: (response: AxiosResponse, config: T2FAModalConfig) => void;
    onCancel: () => void;
    codeSent: boolean;
    hideTwoAuthPage: () => void;
};

export default function TwoFactorChallenge(props: PropsType) {
    const [showRecoveryInput, setShowRecoveryInput] = useState<boolean>(false);
    const [disableCodeField, setDisableCodeField] = useState<boolean>(true);
    const navigate = useNavigate();
    const theme = useTheme();
    const methods = useForm();
    const watcher = useWatch({control: methods.control});
    const {updateSettings} = useSettings();
    const config = {
        decodedHeader: props.decodedHeader,
        access_token: props.access_token,
        additional_info: props.additional_info,
        last_logged_in_at: props.last_logged_in_at,
    };

    const sendInfo = (data: any) => {
        let finalCode: string = "";
        if (data.code_full) {
            finalCode = data.code_full.trim();
        }
        updateSettings(UPDATE_SETTINGS, {loading: true});
        if (props.firstConfig) {
            authService.confirmTwoFA(finalCode, "", handleSuccess, handleError);
        } else {
            authService.twoFactorChallenge(
                finalCode,
                data.recovery_code,
                config.access_token ?? "",
                handleSuccess,
                handleError,
            );
        }
    };

    const handleSuccess = (response: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        props.onSuccess && props.onSuccess(response, config);
    };

    const handleChangeForm = () => {
        if (showRecoveryInput) {
            methods.setValue("recovery_code", "");
            methods.clearErrors("recovery_code");
        } else {
            methods.setValue("code_full", "");
            methods.clearErrors("code_full");
        }
        setShowRecoveryInput(!showRecoveryInput);
    }

    const handleError = (error: any) => {
        if (showRecoveryInput) {
            methods.setError("recovery_code", {
                type: "manual",
                message: genericErrorString(error),
            });
        } else {
            methods.setError("code_full", {
                type: "manual",
                message: genericErrorString(error),
            });
        }
        updateSettings(UPDATE_SETTINGS, {loading: false});
    };

    const checkEmptyField = () => {
        if (showRecoveryInput && watcher.recovery_code && watcher.recovery_code.length > 0) {
            setDisableCodeField(false);
            return;
        }
        if (watcher.code_full && watcher.code_full.length > 0) {
            setDisableCodeField(false);
            return;
        }
        setDisableCodeField(true);
    };

    useEffect(() => {
        if (watcher.recovery_code) {
            return;
        }
        const inputValue = watcher.code_full;
        if (!inputValue) {
            methods.setFocus(`code_full`);
            return;
        }
        checkEmptyField();
        // eslint-disable-next-line
    }, [watcher]);

    return (
        <>
            <FormProvider {...methods}>
                <Box
                    component="form"
                    onSubmit={methods.handleSubmit(sendInfo)}
                    sx={{ width: "100%" }}
                >
                    <Box
                        sx={{
                            marginTop:"24px",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                            gap: 2,
                            width: "100% !important",
                        }}>
                        <Stack sx={{width: "100% !important"}}>
                            {showRecoveryInput ? (
                                <>
                                    <Typography
                                        variant="18"
                                        fontWeight="bold"
                                        align="center"
                                        sx={{
                                            color: theme.palette.neutral[700],
                                            textAlign: "center",
                                            fontFamily: "Poppins",
                                            fontSize: "18px",
                                            fontStyle: "normal",
                                            fontWeight: "600",
                                            lineHeight: "120%"
                                        }}>
                                        Enter your recovery code
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            width: "100% !important",
                                            marginTop:"30px"
                                        }}>
                                        <TextBoxComponent
                                            type="text"
                                            id="recovery_code"
                                            placeholder="Enter a valid recovery code"
                                            label="Recovery code"
                                            containerClassName="w-100 text-center"
                                            onKeyUp={checkEmptyField}
                                        />
                                    </Box>
                                </>
                            ) : (
                                <>
                                    <Typography
                                        variant="18"
                                        fontWeight="bold"
                                        align="center"
                                        sx={{
                                            color: theme.palette.neutral[700],
                                            textAlign: "center",
                                            fontFamily: "Poppins",
                                            fontSize: "18px",
                                            fontStyle: "normal",
                                            fontWeight: "600",
                                            lineHeight: "120%"
                                        }}>
                                        Enter your verification code
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            width: "100% !important",
                                            marginTop:"15px"
                                        }}>
                                        <TextBoxComponent
                                            placeholder="000000"
                                            id="code_full"
                                            maxLength={6}
                                            minLength={6}
                                            inputStyles={{
                                                backgroundColor: `${theme.palette.neutral[100]} !important`,
                                                textAlign: "center !important",
                                                letterSpacing: {sm: "5px", md: "37px"},
                                                width: {sm: "100px", md: "308px"},
                                                fontSize:"24px !important",
                                                fontWeight: "400 !important",
                                                lineHeight: "21px !important",
                                                "&::placeholder": {
                                                    fontSize: '24px !important',
                                                    fontWeight: "400 !important",
                                                    lineHeight: "21px !important",
                                                },
                                                padding: "20px 0px !important",
                                                marginLeft: "30px !important"
                                            }}
                                            parentInputStyle={{
                                                "& .MuiInput-root::after": {
                                                    borderBottom: `2px solid ${theme.palette.neutral[400]} !important`,
                                                    transform: "scaleX(1)",
                                                },
                                            }}
                                            sx={{width: "100% !important"}}
                                            isRequired
                                            hideLabel
                                            validationSchema={v => /^[0-9]+$/.test(v as string)}
                                            onKeyUp={checkEmptyField}
                                            formHelperTextProps={{
                                                sx: {textAlign: "center"},
                                            }}
                                        />
                                    </Box>
                                </>
                            )}
                            {!props.firstConfig && (
                                <>
                                    <Typography
                                        onClick={handleChangeForm}
                                        sx={{
                                            marginTop:showRecoveryInput ? "25px" : "15px",
                                            color: theme.palette.blue[600],
                                            textAlign: "center",
                                            fontFamily: "Inter",
                                            fontSize: "16px",
                                            fontStyle: "normal",
                                            fontWeight: "700",
                                            lineHeight: "130%",
                                            cursor:"pointer"
                                        }}
                                    >
                                        {showRecoveryInput
                                            ? "Use authenticator code instead"
                                            : "Use a recovery code instead"}
                                    </Typography>
                                    {showRecoveryInput && (
                                        <Typography
                                            onClick={() => navigate(routeConfig.Contact.path)}
                                            sx={{
                                                marginTop:"25px",
                                                color: theme.palette.blue[600],
                                                textAlign: "center",
                                                fontFamily: "Inter",
                                                fontSize: "16px",
                                                fontStyle: "normal",
                                                fontWeight: "700",
                                                lineHeight: "130%",
                                                cursor:"pointer"
                                            }}
                                        >
                                            Having issues? Contact us.
                                        </Typography>
                                    )}
                                </>
                            )}
                            <Box
                                sx={{
                                    marginTop:"25px",
                                    display: "flex",
                                    flexDirection: "column",
                                    justifyContent: "center",
                                    alignItems: "flex-start",
                                    alignSelf: "stretch",
                                    height: "1px",
                                    backgroundColor:theme.palette.neutral[500],
                                }}
                            />
                            <Stack
                                sx={{
                                    marginTop:"25px",
                                    display: "flex",
                                    justifyContent: "center",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    width: "100% !important",
                                }}>
                                <MuiButtonComponent
                                    id="verifyCode"
                                    variant="tonal"
                                    type="submit"
                                    disabled={disableCodeField}
                                    sx={{width: {sm: "70%", md: "65%"}, paddingY: 2}}>
                                    <Typography sx={{
                                        color: theme.palette.common["white"],
                                        textAlign: "center",
                                        fontFamily: "Inter",
                                        fontSize: "18px",
                                        fontStyle: "normal",
                                        fontWeight: "600",
                                        lineHeight: "120%"
                                    }}>
                                        <ArrowForwardOutlinedIcon fontSize="small" sx={{marginRight: 1}} /> Verify
                                    </Typography>
                                </MuiButtonComponent>
                                <MuiButtonComponent
                                    id="goBack"
                                    type="button"
                                    variant="outlined"
                                    color="blue"
                                    sx={{width: {sm: "70%", md: "65%"}, paddingY: 2, marginTop:"15px"}}
                                    onClick={props.hideTwoAuthPage}>
                                    <Typography sx={{
                                        color: theme.palette.blue[800],
                                        textAlign: "center",
                                        fontFamily: "Inter",
                                        fontSize: "18px",
                                        fontStyle: "normal",
                                        fontWeight: "600",
                                        lineHeight: "120%"
                                    }}>
                                        <img src="/cp-signup-arrow-back.png" className="arrowSizeLogin" /> Go Back
                                    </Typography>
                                </MuiButtonComponent>
                            </Stack>
                        </Stack>
                    </Box>
                </Box>
            </FormProvider>
        </>
    );
}
