import {BREAKOUT_MAX_NAME} from "../../constants/commonStrings.constant";
import {createImageFromInitials, getRandomColor} from "../../utils/imageFromText.util";
import InfoBanner from "../DescriptionBanner/infoBanner.component";
import {InfoIcon} from "../Icons/info.icon";
import {RoomsIcon} from "../Icons/rooms.icon";
import TitleContainer from "../Titles/titleContainer.component";
import styles from "../../styles/pages/pitchPrivate.module.sass";
import Loader from "../../utils/loader";
import OutboundLink from "../Links/OutboundLink.component";

interface IProps {
    breakoutRooms: any;
    breakoutExpired: boolean;
    hideHeader?: boolean;
}

const BreakoutRooms = (props: IProps) => {
    const {breakoutRooms, breakoutExpired, hideHeader} = props;
    return (
        <>
            {!hideHeader && (
                <div className={styles.endMessage}>
                    <InfoBanner type="info">
                        <div className="d-flex py-3 px-2 align-items-center">
                            <InfoIcon size={24} />
                            <p className="m-0 ms-2">
                                Channel Engage vendors are waiting for you in breakout rooms. You have been anonymously
                                participating in this event. Once you enter a breakout room you give up that anonymity
                                to the vendor in that breakout room only. Don’t forget you may visit more than one
                                vendor breakout room. Just come back to this tab.
                            </p>
                        </div>
                    </InfoBanner>
                </div>
            )}
            <div className={styles.breakoutContainer + (hideHeader ? " m-0" : "")}>
                {!hideHeader && (
                    <div className={styles.breakoutHeader}>
                        <RoomsIcon size={18} />
                        <TitleContainer
                            as="h3"
                            text="Available Breakout Rooms"
                            position="start"
                            noConnector
                            className="mb-0"
                        />
                    </div>
                )}
                <div className={styles.roomsPanel}>
                    {!breakoutRooms ? (
                        <div className="d-flex align-items-center justify-content-center h-100 w-100">
                            <Loader inline loading />
                        </div>
                    ) : breakoutRooms.length > 0 ? (
                        breakoutRooms.map((room, index) => (
                            <div key={index} className={styles.breakoutRoom}>
                                <div className={styles.breakoutRoomAvatar}>
                                    <img
                                        src={
                                            room?.avatar
                                                ? room?.avatar
                                                : createImageFromInitials(50, room.company_name, getRandomColor())
                                        }
                                        alt="avatar"
                                    />
                                </div>
                                <TitleContainer
                                    as="h4"
                                    className="mb-0"
                                    text={
                                        room.company_name.length > BREAKOUT_MAX_NAME
                                            ? `${room.company_name.slice(0, BREAKOUT_MAX_NAME)}...`
                                            : room.company_name
                                    }
                                    noConnector
                                    position="start"
                                />
                                {breakoutExpired ? (
                                    <p aria-disabled="true" className={styles.breakoutBtn}>
                                        Breakout ended
                                    </p>
                                ) : (
                                    <OutboundLink href={room.break_out_link} className={styles.breakoutBtn}>
                                        Join
                                    </OutboundLink>
                                )}
                            </div>
                        ))
                    ) : (
                        <div className="d-flex align-items-center justify-content-center h-100 w-100">
                            <p className="text-center lh-base">
                                This Channel Engage event has no breakout rooms. <br />
                                We hope to see you on the next one!
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default BreakoutRooms;
