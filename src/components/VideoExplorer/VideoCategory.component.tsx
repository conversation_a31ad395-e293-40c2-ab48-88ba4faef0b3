import ExplorerCard from "../Explorer/explorerCard.component";
import styles from "../../styles/components/explorerCategory.module.sass";
import {ExplorerVideoType} from "../../Interfaces/videoExplorer.interface";
import {ICategory} from "../../Interfaces/categories.interface";
import {IFilter} from "../../Interfaces/filters.interface";

interface IProps {
    title: string;
    videos: ExplorerVideoType[];
    setSelectedFilter: (filter: IFilter) => void;
    category: ICategory;
    maxLength: number;
    showTop: boolean;
}

export default function VideoCategory(props: IProps) {
    const handleExploreMore = () => {
        props.setSelectedFilter(props.category as IFilter);
    };
    if (props.videos.length === 0) return null;
    return (
        <div className={styles.container}>
            {props.showTop && <hr style={{marginBottom: 20}} />}
            <div className={styles.stripHeader} style={{marginBottom: 20}}>
                <div className="d-flex align-items-center gap-3">
                    <span>{props.title}</span>
                </div>
            </div>
            <div className={styles.videoStrip + " flex-wrap"}>
                {props.videos?.map(video => {
                    const videoProps = {
                        ...video,
                        company_handle: video.parent.handle,
                        user_handle: video.parent.handle,
                        user_name: video.parent.name,
                        parent_avatar: video.parent.avatar,
                        thumbnail: video.thumbnail_url,
                        parent: video.parent,
                        parent_model_type: video.parent.type.type_is_of_vendor ? "company" : "user",
                    };
                    const entityProp = {
                        id: video.parent.id,
                        friendly_url: video.parent.friendly_url,
                        isCompany: video.parent.type.type_is_of_vendor,
                        name: video.parent.name,
                        avatar: video.parent.avatar,
                    };
                    // @ts-ignore
                    return (
                        <ExplorerCard
                            video={videoProps}
                            key={video.id}
                            className={styles.videoCategoryVideoCard}
                            entity={entityProp}
                        />
                    );
                })}
                {(props.videos?.length || 0) >= props.maxLength && (
                    <span
                        onClick={handleExploreMore}
                        style={{marginTop: "auto", marginLeft: "auto", color: "var(--bs-blue)", cursor: "pointer"}}>
                        Explore More
                    </span>
                )}
            </div>
            <hr />
        </div>
    );
}

VideoCategory.defaultProps = {
    maxLength: 6,
    showTop: false,
};
