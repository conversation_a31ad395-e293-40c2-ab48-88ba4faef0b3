import {useTheme} from "@mui/material";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import CreateOutlinedIcon from "@mui/icons-material/CreateOutlined";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import CategoryAutocomplete, {
    ICategoryOption,
} from "../../uicomponents/Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import {useFormContext} from "react-hook-form";
import {IActiveCompany} from "../../hooks/useActiveCompany";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import {IAddCompanyProductForm} from "./localCompanyProductModal.component";

export interface IAddProductForm {
    id: string | null;
    name: string;
    url?: string;
    parent_category?: string;
    parent_category_id?: string;
    parent_category_color?: string;
    category?: string;
    category_id?: string;
    category_color?: string;
}

interface ILocalProductCard {
    product: IAddProductForm;
    activeCompany: IActiveCompany;
    onResetPBE?: () => void;
}
const LocalProductCard = ({product, activeCompany, onResetPBE}: ILocalProductCard) => {
    const theme = useTheme();
    const {watch, setValue, getValues, trigger, clearErrors} = useFormContext();
    const pbeId = watch("pbe__id");
    const isEditing = pbeId === product.id || !product.id;

    const handleSaveProductData = async () => {
        const isValid = await trigger(["pbe__name", "pbe__url", "pbe__parent_category_id", "pbe__category_id"]);
        if (!isValid) return;
        const values = getValues();
        const updatedProduct = {...product};
        Object.keys(product).forEach(key => {
            updatedProduct[key as keyof typeof product] = values[`pbe__${key}`];
        });
        const updatedList = values.products.map(p => (p.id === null || p.id === pbeId ? updatedProduct : p));
        setValue("products", updatedList);
        onResetPBE?.();
    };

    // Cancel a product that is beeing created
    const handleCancelProductChanging = () => {
        const {products} = getValues();
        setValue(
            "products",
            products.filter(p => !!p.id),
        );
        onResetPBE?.();
    };

    // Open a product to be edited
    const handleEditProduct = () => {
        Object.keys(product).forEach(key => {
            const fieldName = `pbe__${key}` as keyof IAddCompanyProductForm;
            setValue(fieldName, product[key]);
        });
    };

    // Remove a product from the list
    const handleDeleteProduct = () => {
        const {products} = getValues();
        setValue(
            "products",
            products.filter(p => p.id !== product.id),
        );
    };

    if (isEditing) {
        return (
            <Box
                sx={{
                    padding: 1,
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    flex: "0 0 auto",
                    border: `1px solid ${theme.palette.neutral[300]}`,
                    borderRadius: "8px",
                    marginTop: "auto",
                    gridColumn: "span 12",
                }}>
                <TextBoxComponent
                    id="pbe__name"
                    label="Product Name"
                    isRequired
                    validateOnTheFly
                    keepRegistered
                    placeholder="Enter product name"
                />
                <TextBoxComponent
                    id="pbe__url"
                    label="Product URL"
                    isRequired
                    validateOnTheFly
                    keepRegistered
                    placeholder="Enter the link for product details on vendor site"
                />
                <Box
                    sx={{
                        flex: "0 0 auto",
                    }}>
                    <CategoryAutocomplete
                        categoryInputId={"pbe__parent_category_id"}
                        subCategoryInputId={"pbe__category_id"}
                        parentCategoryLabel="Category"
                        subCategoryLabel="Subcategory"
                        parentCategoryRequired
                        wrapperSx={{
                            gap: 2,
                            display: "flex",
                            "& > *": {
                                flex: "0 0 auto",
                                width: "calc(50% - 8px)",
                                minWidth: "auto!important",
                            },
                        }}
                        onParentValueChange={(_, value: string, options: ICategoryOption) => {
                            setValue("pbe__parent_category", options.label);
                            setValue("pbe__parent_category_color", options.color);
                        }}
                        onSubcategoryValueChange={(_, value: string, options: ICategoryOption) => {
                            setValue("pbe__category_color", options.color);
                            setValue("pbe__category", options.label);
                        }}
                        validateOnTheFly
                        keepRegistered
                        company_type={activeCompany?.company_type}
                    />
                </Box>
                <Box
                    sx={{
                        display: "flex",
                        gap: 2,
                        flex: "0 0 auto",
                    }}>
                    <Button
                        id="save-add-local-product"
                        variant="outlined"
                        color="blue"
                        sx={{
                            gap: 1,
                            "& svg": {
                                width: "18px",
                            },
                        }}
                        onClick={handleSaveProductData}>
                        <CheckOutlinedIcon />
                        {product.id === null ? "ADD" : "SAVE"}
                    </Button>
                    <Button
                        id="cancel-add-local-product"
                        variant="outlined"
                        color="error"
                        sx={{
                            gap: 1,
                            "& svg": {
                                width: "18px",
                            },
                        }}
                        onClick={handleCancelProductChanging}>
                        <DeleteOutlineOutlinedIcon />
                        CANCEL
                    </Button>
                </Box>
            </Box>
        );
    }
    return (
        <Box
            sx={{
                padding: 2,
                display: "flex",
                flex: 1,
                flexDirection: "column",
                gap: 2,
                border: `1px solid ${theme.palette.neutral[300]}`,
                borderRadius: "8px",
                gridColumn: "span 12",
            }}>
            <Box sx={{display: "flex", alignItems: "center", gap: 1}}>
                <Typography
                    variant="body2"
                    fontInter
                    sx={{
                        flex: 1,
                        flexShrink: 1,
                        fontWeight: 500,
                        lineHeight: "100%",
                        color: theme.palette.neutral[800],
                    }}>
                    {product.name}
                </Typography>
                <Button
                    id="save-add-local-product"
                    variant="text"
                    color="blue"
                    onClick={() => handleEditProduct()}
                    disabled={!!pbeId}
                    sx={{
                        minWidth: "unset",
                        padding: 0,
                        "& svg": {
                            width: "24px",
                            height: "24px",
                            color: theme.palette.blue[600],
                        },
                    }}>
                    <CreateOutlinedIcon />
                </Button>
                <Button
                    id="save-add-local-product"
                    variant="text"
                    color="error"
                    onClick={() => handleDeleteProduct()}
                    sx={{
                        minWidth: "unset",
                        padding: 0,
                        "& svg": {
                            width: "24px",
                            height: "24px",
                            color: theme.palette.error[600],
                        },
                    }}>
                    <DeleteOutlineOutlinedIcon />
                </Button>
            </Box>
            <Box sx={{display: "flex", flexDirection: "column"}}>
                <Typography
                    variant="body5"
                    fontInter
                    sx={{
                        fontWeight: 400,
                        lineHeight: "16px",
                        color: theme.palette.neutral[800],
                    }}>
                    Product URL
                </Typography>
                <Typography
                    variant="body3"
                    fontInter
                    sx={{
                        fontWeight: 400,
                        lineHeight: "24px",
                        color: theme.palette.neutral[800],
                    }}>
                    {product.url}
                </Typography>
            </Box>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    "& svg": {
                        height: "16px",
                        color: theme.palette.neutral[700],
                    },
                }}>
                <Box
                    sx={{
                        width: "10.67px",
                        height: "10.67px",
                        borderRadius: "50%",
                        backgroundColor: product.parent_category_color,
                    }}
                />
                <Typography
                    variant="body4"
                    fontInter
                    sx={{
                        fontWeight: 600,
                        lineHeight: "140%",
                        color: theme.palette.neutral[700],
                    }}>
                    {product.parent_category}
                </Typography>
                <ArrowForwardOutlinedIcon />
                <Typography
                    variant="body4"
                    fontInter
                    sx={{
                        fontWeight: 600,
                        lineHeight: "140%",
                        color: theme.palette.neutral[700],
                    }}>
                    {product.category}
                </Typography>
            </Box>
        </Box>
    );
};

export default LocalProductCard;
