import styles from "../../styles/components/notificationDot.module.sass";

interface IProps {
    style?: React.CSSProperties;
    qty?: number | string;
    title?: string;
}

export function NotificationDot(props: IProps) {
    // eslint-disable-next-line
    if (props.qty == "0") return null;
    return (
        <div className={styles.notificationIndicator} style={props.style} title={props.title}>
            <div className={styles.container}>
                <div className={!!props.qty ? styles.bigPulsing : styles.pulsing}></div>
                <div className={!!props.qty ? styles.bigStatic : styles.static}>
                    {!!props.qty && <span>{props.qty}</span>}
                </div>
            </div>
        </div>
    );
}
