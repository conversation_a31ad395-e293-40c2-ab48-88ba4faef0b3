import {createElement} from "react";
import BrandConnector from "../Logo/brandConnector.component";

interface ITitle {
    as: string;
    text: string;
    position?: "center" | "start" | "end";
    className?: string;
    connectorSize?: string;
    connectorColor?: "Orange" | "Navy" | "White" | "Black" | "Gray";
    noConnector?: boolean;
    mobileCenter?: boolean;
    breakpoint?: string;
    style?: React.CSSProperties;
    title?: string;
}

/**
 * Factory to generate titles with the custom connector
 * @param {string} as element to generate, ex: "h1", "h2", "p", "a"...
 * @param {string} text text to show at the title
 * @param {string} position position of the title, default: "center"
 * @param {string} className className to add to the title wrapper
 * @param {string} connectorSize size of the connector in px, default: "36px"
 * @param {string} connectorColor color of the connector, default: "Orange"
 * @param {string} title title to show in the browser as a tooltip
 */
export default function TitleContainer(props: ITitle) {
    const elProps = {className: "titleWithLine", style: props.style, title: props.title};
    const align = () => {
        if (props.position && !props.mobileCenter) {
            if (props.position === "start") {
                return "align-items-start text-start";
            }
            if (props.position === "end") {
                return "align-items-end text-end";
            }
            if (props.position === "center") {
                return "align-items-center text-center";
            }
        }
        if (props.position && props.mobileCenter) {
            if (props.position === "start") {
                return `align-items-center align-items-${props.breakpoint || "lg"}-start text-center text-${
                    props.breakpoint || "lg"
                }-start`;
            }
            if (props.position === "end") {
                return `align-items-center align-items-${props.breakpoint || "lg"}-end text-center text-${
                    props.breakpoint || "lg"
                }-start`;
            }
            if (props.position === "center") {
                return "align-items-center text-center";
            }
        } else {
            return "align-items-center text-center";
        }
    };
    return (
        <div id={props.text} className={`titleContainer ${align()} ${props.className}`}>
            {props.noConnector ? null : (
                <BrandConnector
                    size={props.connectorSize || "36"}
                    color={props.connectorColor && props.connectorColor}
                />
            )}
            {createElement(props.as, elProps, props.text)}
        </div>
    );
}
