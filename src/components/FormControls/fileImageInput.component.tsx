import {useState, useRef, ChangeEvent} from "react";
import styles from "../../styles/components/fileInput.module.sass";
import useNotification from "../../hooks/useNotification";
import {CloudUploadIcon} from "../Icons";
import {supportText, accepts, maxSize} from "../../utils/supportText.util";
import ButtonComponent from "./button.component";
import {MediaTypes} from "../../enums/mediaTypes.enum";

interface IProps {
    onFileChange: Function;
    maxSize?: string;
    imageUrl?: string | null;
    disabled?: boolean;
}

const FileImageInputComponent = (props: IProps) => {
    const [file, setFile] = useState<File | null>(null);
    const [dragging, setDragging] = useState(false);
    const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
    const inputFilePicker = useRef<HTMLInputElement | null>(null);

    const notify = useNotification();
    const importFile = () => {
        if (!inputFilePicker.current) return;
        inputFilePicker.current.click();
    };

    const handleDragEnter = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(true);
    };

    const handleDragLeave = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);
    };

    const handleDragOver = e => {
        e.preventDefault();
        e.stopPropagation();
    };
    const handleDrop = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);
        const file = e.dataTransfer.files[0];
        if (file && props.maxSize) {
            if (file.size > parseInt(props.maxSize) * 1024 * 1024) {
                notify("File is too big!", "Error");
                return;
            }
            return setFile(file);
        }
        return;
    };

    const handleUpdateFile = async (e: ChangeEvent<HTMLInputElement>) => {
        const file: File = e.target.files![0];
        if (!file) return;
        if (file && props.maxSize && file.size > parseInt(props.maxSize) * 1024 * 1024) {
            notify("File is too big!", "Error");
            return;
        }
        setFile(file);
        setImagePreviewUrl(URL.createObjectURL(file));
        props.onFileChange(file);
    };

    return (
        <div className="d-flex-block">
            <label className="cpLabel align-self-start">Public image:</label>
            <div
                className={styles.dropBox + " " + (dragging ? styles.dragging : "")}
                onClick={importFile}
                onDragEnter={handleDragEnter}
                onDragExit={handleDragLeave}
                onDragOver={handleDragOver}
                onDrop={handleDrop}>
                <div className={styles.cloudIcon}>
                    <CloudUploadIcon size={50} />
                </div>
                <div className={styles.dropBoxText}>
                    <span>
                        Drag and drop your image here or
                        <ButtonComponent type="button" id="browseFileExplorer">
                            browse from your device
                        </ButtonComponent>
                    </span>
                    <input
                        hidden
                        type="file"
                        accept={accepts[MediaTypes.Image]}
                        onChange={e => handleUpdateFile(e)}
                        ref={inputFilePicker}
                        className={styles.inputFile}
                        disabled={props.disabled}
                    />
                </div>
            </div>
            <div className={styles.supportText}>
                <span>{supportText("image", maxSize.image)}</span>
            </div>
            {!props.imageUrl && file && imagePreviewUrl && (
                <div className={styles.imagePreviewContainer}>
                    <span>Image Selected:</span>
                    <img alt="" src={imagePreviewUrl} />
                </div>
            )}
            {props.imageUrl && (
                <div className={styles.imagePreviewContainer}>
                    <span>Image Selected:</span>
                    <img alt="" src={props.imageUrl} />
                </div>
            )}
        </div>
    );
};

export default FileImageInputComponent;
