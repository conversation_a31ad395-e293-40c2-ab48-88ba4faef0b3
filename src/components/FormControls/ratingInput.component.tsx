import React from "react";
import CheckBoxComponent from "./checkBox.component";
import styles from "../../styles/components/ratingInput.module.sass";
import {ReviewQuestionOption} from "../../Interfaces/reviews.interface";

interface IProps {
    id: string;
    options: ReviewQuestionOption[];
    maxRating: number;
    showLabels?: boolean;
    leftText?: string;
    rightText?: string;
    label?: string;
    isRequired?: boolean;
    errorMessage?: string;
    inputError?: boolean;
    name?: string;
    checked?: any;
    disabled?: boolean;
}

const RatingInput = (props: IProps) => {
    const {id, options, name, maxRating, showLabels, leftText, rightText, label, errorMessage, inputError} = props;
    return (
        <>
            <label className={styles.ratingInputLabel}>{label}</label>
            <div className={styles.ratingInputContainer + " ms-4"}>
                {showLabels && (
                    <div className={styles.ratingLabel + " " + styles.ratingLabelLeft}>
                        <label>1</label>
                        {leftText && <span>{leftText}</span>}
                    </div>
                )}
                {[...Array(props.maxRating)].map((rating, index) => {
                    return (
                        <CheckBoxComponent
                            key={options?.length ? options[index]?.id : index + 1}
                            id={id}
                            testid={`rating-${index}`}
                            type="radio"
                            children={null}
                            className="w-auto m-1 p-0"
                            value={id === "recommended_by_reviewer_value" ? String(index + 1) : options[index].id}
                            isRequired={props.isRequired}
                            showError={props.errorMessage ? true : false}
                            name={name}
                            disabled={props.disabled}
                        />
                    );
                })}
                {showLabels && (
                    <div className={styles.ratingLabel + " " + styles.ratingLabelRight}>
                        <label>{maxRating}</label>
                        {rightText && <span>{rightText}</span>}
                    </div>
                )}
                {inputError && <span className="errorColor ms-5">{errorMessage}</span>}
            </div>
            {/* {props.error && <span className="errorColor">{props.error}</span>} */}
        </>
    );
};

RatingInput.defaultProps = {
    maxRating: 10,
    showLabels: true,
    leftText: "Very Dissatisfied",
    rightText: "Highly Satisfied",
};

export default RatingInput;
