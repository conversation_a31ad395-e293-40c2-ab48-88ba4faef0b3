import {useFormContext, Validate} from "react-hook-form";
import styles from "../../styles/components/checkbox.module.sass";

interface IProps {
    id: string;
    type?: "radio" | "checkbox";
    disabled?: boolean;
    showError?: boolean;
    helperText?: string;
    className?: string;
    value?: string;
    checked?: boolean;
    isRequired?: boolean;
    validationSchema?: Validate<any>;
    children: React.ReactChild | null;
    name?: string;
    getErrorFromFormState?: boolean;
    validateOnTheFly?: boolean;
    testid?: string;
    title?: string;
}

const CheckBoxComponent = (props: IProps) => {
    const {register, formState} = useFormContext();
    const testid = props.testid ? props.testid.replace(/\s/g, "") : props.id;
    return (
        <>
            <label
                data-testid={testid + "-label"}
                title={props.title}
                className={`${props.type && props.type === "radio" ? styles.cpRadio : styles.cpCheckbox} ${
                    props.className
                }`}
                aria-invalid={
                    props.validateOnTheFly
                        ? formState.errors[props.id]
                            ? "true"
                            : "false"
                        : formState.isSubmitted && formState.errors[props.id]
                        ? "true"
                        : "false"
                }>
                {props.value ? (
                    <input
                        className="mx-2"
                        type={props.type || "checkbox"}
                        disabled={props.disabled}
                        {...register(props.id, {
                            required: props.isRequired,
                            validate: props.validationSchema,
                            shouldUnregister: true,
                        })}
                        value={props.value}
                        id={props.id}
                        name={props.name || props.id}
                        data-testid={testid}
                    />
                ) : props.checked ? (
                    <input
                        className="mx-2"
                        type={props.type || "checkbox"}
                        disabled={props.disabled}
                        {...register(props.id, {
                            required: props.isRequired,
                            validate: props.validationSchema,
                            shouldUnregister: true,
                        })}
                        checked={props.checked || false}
                        value={props.value}
                        id={props.id}
                        name={props.name || props.id}
                        data-testid={testid}
                    />
                ) : (
                    <input
                        className="mx-2"
                        type={props.type || "checkbox"}
                        disabled={props.disabled}
                        id={props.id}
                        {...register(props.id, {
                            required: props.isRequired,
                            validate: props.validationSchema,
                            shouldUnregister: true,
                        })}
                        name={props.name ? props.name : props.id}
                        data-testid={testid}
                    />
                )}
                <div className={styles.checkContainer}>
                    <svg viewBox="0 0 21 18">
                        <symbol
                            id={"tick-path" + (props.value || props.id)}
                            viewBox="0 0 21 18"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M5.22003 7.26C5.72003 7.76 7.57 9.7 8.67 11.45C12.2 6.05 15.65 3.5 19.19 1.69"
                                fill="none"
                                strokeWidth="2.25"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </symbol>
                        <defs>
                            <mask id={"tick" + (props.value || props.id)}>
                                <use
                                    className={styles.tick + " " + styles.mask}
                                    href={`#tick-path${props.value || props.id}`}
                                />
                            </mask>
                        </defs>
                        <use
                            className={styles.tick}
                            href={`#tick-path${props.value || props.id}`}
                            stroke="currentColor"
                        />
                        <path
                            fill="white"
                            mask={`url(#tick${props.value || props.id})`}
                            d="M18 9C18 10.4464 17.9036 11.8929 17.7589 13.1464C17.5179 15.6054 15.6054 17.5179 13.1625 17.7589C11.8929 17.9036 10.4464 18 9 18C7.55357 18 6.10714 17.9036 4.85357 17.7589C2.39464 17.5179 0.498214 15.6054 0.241071 13.1464C0.0964286 11.8929 0 10.4464 0 9C0 7.55357 0.0964286 6.10714 0.241071 4.8375C0.498214 2.39464 2.39464 0.482143 4.85357 0.241071C6.10714 0.0964286 7.55357 0 9 0C10.4464 0 11.8929 0.0964286 13.1625 0.241071C15.6054 0.482143 17.5179 2.39464 17.7589 4.8375C17.9036 6.10714 18 7.55357 18 9Z"
                        />
                    </svg>
                    <svg className={styles.lines} viewBox="0 0 11 11">
                        <path d="M5.88086 5.89441L9.53504 4.26746" />
                        <path d="M5.5274 8.78838L9.45391 9.55161" />
                        <path d="M3.49371 4.22065L5.55387 0.79198" />
                    </svg>
                </div>
                <div />
                {props.children}
            </label>
            {props.getErrorFromFormState ? (
                formState.errors[props.id] !== undefined ? (
                    <span className="errorColor">
                        {/* @ts-ignore */}
                        {formState.errors[props.id].type === "required"
                            ? "This Field is Required"
                            : formState.errors[props.id]?.message}
                    </span>
                ) : null
            ) : (
                <span
                    style={
                        (props.showError || formState.errors[props.id]) && props.helperText
                            ? {display: "block"}
                            : {display: "none"}
                    }
                    className="errorColor">
                    {props.showError || formState.errors[props.id] ? props.helperText : ""}
                </span>
            )}
        </>
    );
};

export default CheckBoxComponent;

CheckBoxComponent.defaultProps = {
    showError: false,
    disabled: false,
    helperText: "",
    name: "",
};
