import EmojiPicker, {EmojiClickData, EmojiStyle} from "emoji-picker-react";
import {useRef, useState} from "react";
import {Overlay, OverlayTrigger, Popover, Tooltip} from "react-bootstrap";
import {useFormContext, Validate} from "react-hook-form";
import {SmileyIcon} from "../Icons";
import ReactIcon from "../Icons/reactIcon.component";

interface IProps {
    id: string;
    onEnter?: Function;
    disabled?: boolean;
    showError?: any;
    helperText?: string;
    autoFocus?: boolean;
    autoComplete?: string;
    placeholder?: string;
    type?: string;
    value?: string;
    className?: string;
    containerClassName?: string;
    isRequired?: boolean | string;
    validateOnTheFly?: boolean;
    isDate?: boolean;
    isTwoFA?: boolean;
    isNumber?: boolean;
    minLength?: number;
    maxLength?: number;
    validationSchema?: Validate<any, any>;
    ref?: React.Ref<any>;
    successfull?: boolean;
    label?: string;
    onFocus?: Function;
    onBlur?: Function;
    readOnly?: boolean;
    allowEmojis?: boolean;
    noEmojiSearch?: boolean;
    tooltip?: string;
    labelClassName?: string;
    containerStyles?: any;
    minDate?: string;
    maxDate?: string;
    testid?: string;
    allowDecimals?: boolean;
    emojiPicketPlacement?: any;
    onTextChange?: (text: string) => void;
    defaultValue?: string;
}

const TextBoxComponent = ({
    showError = false,
    disabled = false,
    helperText = "",
    autoFocus = false,
    type = "text",
    placeholder = "",
    ...props
}: IProps) => {
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const {register, formState, setValue, getValues} = useFormContext();
    const target = useRef(null);
    const testid = props.testid ? props.testid.replace(/\s/g, "") : props.label ? `textInput-${props.label}` : props.id;
    const onEmojiClick = (emojiObj: EmojiClickData) => {
        setValue(props.id, getValues()[props.id] + emojiObj.emoji);
    };
    const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (props.onTextChange) {
            props.onTextChange(event.target.value);
        }
    };

    const handlePaste = () => {
        if (type === "text") {
            setValue(props.id, getValues()[props.id]);
        }
    };

    return (
        <div
            className={`cpInputContainer ${props.containerClassName} ${props.allowEmojis ? "flex-row" : ""}`}
            style={props.containerStyles}>
            {props.label ? (
                <label
                    className={`cpLabel align-self-start ${props.labelClassName}`}
                    htmlFor={props.id}
                    aria-invalid={
                        props.validateOnTheFly
                            ? formState.errors[props.id]
                                ? "true"
                                : "false"
                            : formState.isSubmitted && formState.errors[props.id]
                            ? "true"
                            : "false"
                    }>
                    {props.label}
                    {props.tooltip && (
                        <OverlayTrigger placement="top" overlay={<Tooltip>{props.tooltip}</Tooltip>}>
                            <div className="cpInputTooltipTrigger">
                                <ReactIcon size="18px" iconName="BiHelpCircle" />
                            </div>
                        </OverlayTrigger>
                    )}
                </label>
            ) : null}
            <input
                className={`cpInput ${props.className} ${props.successfull ? "successInput" : null} ${
                    props.allowEmojis && "cpInputWithEmoji"
                } ${props.isTwoFA ? "twoFAInput" : null}`}
                autoFocus={autoFocus}
                type={type === "2fa" ? "number" : type}
                placeholder={placeholder}
                id={props.id}
                data-testid={testid}
                maxLength={props.maxLength}
                disabled={disabled}
                autoComplete={props.autoComplete}
                min={type === "datetime-local" || (type === "date" && props.minDate) ? props.minDate : undefined}
                max={type === "datetime-local" || (type === "date" && props.maxDate) ? props.maxDate : undefined}
                step={props.allowDecimals ? ".01" : 0}
                onKeyDown={e => {
                    if (props.onEnter) {
                        if (e.key === "Enter") {
                            props.onEnter();
                        }
                    }
                    if (type === "number" || type === "2fa") {
                        if (e.key === "E" || e.key === "e" || e.key === "+" || e.key === "-") {
                            e.preventDefault();
                        }
                    }
                }}
                onFocus={e => {
                    if (!props.onFocus) return;
                    props.onFocus(e);
                }}
                aria-invalid={
                    props.validateOnTheFly
                        ? formState.errors[props.id]
                            ? "true"
                            : "false"
                        : formState.isSubmitted && formState.errors[props.id]
                        ? "true"
                        : "false"
                }
                // @ts-ignore
                {...register(props.id, {
                    required: props.isRequired,
                    valueAsDate: props.isDate,
                    valueAsNumber: props.isNumber,
                    validate: props.validationSchema,
                    minLength: props.minLength,
                    maxLength: props.maxLength,
                    shouldUnregister: true,
                })}
                onBlur={() => {
                    if (!props.onBlur) return;
                    props.onBlur();
                }}
                readOnly={props.readOnly}
                aria-describedby="basic-addon2"
                onPaste={handlePaste}
                onInput={handleTextChange}
                defaultValue={props.defaultValue}
            />
            {props.allowEmojis && (
                <>
                    <span
                        style={{cursor: "pointer", marginLeft: "-50px", marginTop: 8}}
                        title={showEmojiPicker ? "Hide Emoji Picker" : "Show Emoji Picker"}
                        className="input-group-append"
                        ref={target}
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}>
                        <SmileyIcon size={30} />
                    </span>
                    <Overlay
                        show={showEmojiPicker}
                        target={target.current}
                        placement={props.emojiPicketPlacement ? props.emojiPicketPlacement : "bottom"}>
                        <Popover onMouseLeave={() => setShowEmojiPicker(false)}>
                            <Popover.Body className="emoji-picker-container">
                                <EmojiPicker
                                    skinTonesDisabled={true}
                                    autoFocusSearch={false}
                                    onEmojiClick={onEmojiClick}
                                    searchPlaceHolder="Search For Emoji"
                                    searchDisabled={props.noEmojiSearch}
                                    categories={[]}
                                    emojiStyle={EmojiStyle.APPLE}
                                />
                            </Popover.Body>
                        </Popover>
                    </Overlay>
                </>
            )}
            {showError && <div className="inputError position-relative">{helperText && helperText}</div>}
        </div>
    );
};

export default TextBoxComponent;
