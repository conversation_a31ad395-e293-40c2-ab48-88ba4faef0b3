import {FieldError, useForm<PERSON>ontext, Validate} from "react-hook-form";

interface IProps {
    id: string;
    onEnter?: Function;
    disabled?: boolean;
    showError?: FieldError | boolean | undefined;
    helperText?: string;
    autoFocus?: boolean;
    autoComplete?: string;
    placeholder?: string;
    value?: string;
    className?: string;
    containerClassName?: string;
    isRequired?: boolean | string;
    validateOnTheFly?: boolean;
    isDate?: boolean;
    isTwoFA?: boolean;
    isNumber?: boolean;
    minLength?: number;
    maxLength?: number;
    validationSchema?: Validate<any>;
    ref?: React.Ref<any>;
    successfull?: boolean;
    label?: string;
    onFocus?: Function;
    onBlur?: Function;
}

export default function ColorPickerComponent(props: IProps) {
    const {register, formState} = useFormContext();
    return (
        <div className={"cpInputContainer " + props.containerClassName}>
            {props.label ? (
                <label
                    className="cpLabel align-self-start"
                    htmlFor={props.id}
                    aria-invalid={
                        props.validateOnTheFly
                            ? formState.errors[props.id]
                                ? "true"
                                : "false"
                            : formState.isSubmitted && formState.errors[props.id]
                            ? "true"
                            : "false"
                    }>
                    {props.label}
                </label>
            ) : null}
            <input
                className="w-100 py-2"
                autoFocus={props.autoFocus}
                type="color"
                placeholder={props.placeholder}
                id={props.id}
                maxLength={props.maxLength}
                disabled={props.disabled}
                autoComplete={props.autoComplete}
                onKeyDown={e => {
                    if (props.onEnter) {
                        if (e.key === "Enter") {
                            props.onEnter();
                        }
                    }
                }}
                onFocus={e => {
                    if (!props.onFocus) return;
                    props.onFocus(e);
                }}
                aria-invalid={
                    props.validateOnTheFly
                        ? formState.errors[props.id]
                            ? "true"
                            : "false"
                        : formState.isSubmitted && formState.errors[props.id]
                        ? "true"
                        : "false"
                }
                {...register(props.id, {
                    required: props.isRequired,
                    valueAsDate: props.isDate,
                    valueAsNumber: props.isNumber,
                    validate: props.validationSchema,
                    minLength: props.minLength,
                    maxLength: props.maxLength,
                    shouldUnregister: true,
                })}
                onBlur={() => {
                    if (!props.onBlur) return;
                    props.onBlur();
                }}
            />
            {props.showError && <span className="inputError">{props.helperText && props.helperText}</span>}
        </div>
    );
}
