import EmojiPicker, {EmojiClickData, EmojiStyle} from "emoji-picker-react";
import {useState} from "react";
import {useFormContext, Validate} from "react-hook-form";
import {trimAndRemoveEnterFromMiddle} from "../../utils/formatString.util";
import {SmileyIcon} from "../Icons";

interface IProps {
    id: string;
    onEnter?: Function;
    disabled?: boolean;
    showError?: boolean;
    helperText?: string;
    autoFocus?: boolean;
    placeholder?: string;
    defaultValue?: string;
    rows?: string;
    className?: string;
    containerClassName?: string;
    label?: string;
    isRequired?: boolean;
    isDate?: boolean;
    isNumber?: boolean;
    validateOnTheFly?: boolean;
    maxLength?: number;
    minLength?: number;
    validationSchema?: Validate<any>;
    allowEmojis: boolean;
    testid?: string;
    ignoreDefaultLengthRequirements?: boolean;
}

const TextAreaComponent = (props: IProps) => {
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const {register, formState, watch, setValue, getValues, trigger} = useFormContext();
    const value = watch(props.id);

    const onEmojiClick = (emojiObj: EmojiClickData) => {
        setValue(props.id, getValues()[props.id] + emojiObj.emoji);
    };
    const testid = props.testid ? props.testid.replace(/\s/g, "") : props.label ? `textInput-${props.label}` : props.id;

    return (
        <div className={props.containerClassName ? props.containerClassName : "d-flex flex-column cpInputContainer "}>
            {props.label ? (
                <label className="cpLabel" htmlFor={props.id}>
                    {props.label}
                </label>
            ) : null}
            <textarea
                className={`cpInput ${props.className}`}
                autoFocus={props.autoFocus}
                placeholder={props.placeholder}
                id={props.id}
                data-testid={testid}
                rows={props.rows !== undefined ? parseInt(props.rows) : 1}
                disabled={props.disabled}
                defaultValue={props?.defaultValue}
                onKeyDown={e => {
                    if (props.onEnter) {
                        if (e.key === "Enter") {
                            props.onEnter();
                        }
                    }
                }}
                aria-invalid={
                    props.validateOnTheFly
                        ? formState.errors[props.id]
                            ? "true"
                            : "false"
                        : formState.isSubmitted && formState.errors[props.id]
                        ? "true"
                        : "false"
                }
                {...register(props.id, {
                    required: props.isRequired,
                    valueAsDate: props.isDate,
                    valueAsNumber: props.isNumber,
                    validate: props.validationSchema,
                    maxLength: props.ignoreDefaultLengthRequirements ? undefined : props.maxLength,
                    minLength: props.ignoreDefaultLengthRequirements ? undefined : props.minLength,
                })}
                onChange={e => {
                    setValue(props.id, e.target.value);
                    if (props.validateOnTheFly) {
                        trigger(props.id);
                    }
                }}
            />
            {props.allowEmojis && (
                <>
                    <div
                        style={{cursor: "pointer", marginLeft: "-50px", marginTop: "-15px"}}
                        title={showEmojiPicker ? "Hide Emoji Picker" : "Show Emoji Picker"}
                        className="input-group-append cpEmojiPicker"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}>
                        <SmileyIcon size={30} />
                    </div>
                    {showEmojiPicker ? (
                        <div
                            onMouseLeave={() => setShowEmojiPicker(false)}
                            className="dropdown-menu show emoji-picker-container">
                            <EmojiPicker onEmojiClick={onEmojiClick} categories={[]} emojiStyle={EmojiStyle.APPLE} />
                        </div>
                    ) : null}
                </>
            )}
            {props.maxLength && !props.minLength && (
                <div className="charCounter">
                    <span
                        className={
                            value && trimAndRemoveEnterFromMiddle(value).length > props.maxLength ? "errorColor" : ""
                        }>
                        ({value ? trimAndRemoveEnterFromMiddle(value).length : 0}/{props.maxLength}){" "}
                        {(value ? trimAndRemoveEnterFromMiddle(value).length : 0) > props.maxLength &&
                            "Max length exceeded"}
                    </span>
                </div>
            )}
            {props.minLength && props.maxLength && (
                <div className="charCounter">
                    <span
                        className={
                            value && trimAndRemoveEnterFromMiddle(value).length < props.minLength ? "errorColor" : ""
                        }>
                        Minimum Characters Required: {props.minLength} <br />
                    </span>
                    <span
                        className={
                            value && trimAndRemoveEnterFromMiddle(value).length > props.maxLength ? "errorColor" : ""
                        }>
                        Maximum Characters Allowed: {props.maxLength} <br />
                    </span>
                </div>
            )}
            <span style={props.showError ? {display: "block"} : {display: "none"}} className="errorColor">
                {props.showError ? props.helperText : ""}
            </span>
        </div>
    );
};

export default TextAreaComponent;

TextAreaComponent.defaultProps = {
    showError: false,
    disabled: false,
    helperText: "",
    autoFocus: false,
    type: "text",
    placeholder: "",
    isRequired: false,
    allowEmojis: false,
};
