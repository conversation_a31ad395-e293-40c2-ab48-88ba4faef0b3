import {Dropdown, OverlayTrigger, Tooltip} from "react-bootstrap";
import {Controller, useFormContext, Validate} from "react-hook-form";
import {ReviewQuestionOption} from "../../Interfaces/reviews.interface";
import Loader from "../../utils/loader";
import styles from "../../styles/components/dropdown.module.sass";
import ControlledCheckBox from "./controlledCheckBox.component";
import ReactIcon from "../Icons/reactIcon.component";
import {ICONS} from "../../constants/commonStrings.constant";
import {useState, useEffect} from "react";

interface IProps {
    id: string;
    options: string[][] | ReviewQuestionOption[];
    onEnter?: Function;
    showError?: boolean;
    helperText?: string;
    placeholder?: string;
    className?: string;
    isRequired?: boolean;
    isDate?: boolean;
    isNumber?: boolean;
    containerClassName?: string;
    validationSchema?: Validate<any>;
    label?: string;
    selectedOption?: string;
    style?: any;
    disabled?: boolean;
    validateOnTheFly?: boolean;
    isMultiple?: boolean;
    useCustom?: boolean;
    tooltip?: string;
    labelClassName?: string;
    defaultValue?: string;
    testid?: string;
    onTextChange?: (value: string) => void;
    loaderClass?: string;
}

const DropdownComponent = (props: IProps) => {
    const {register, formState, setValue, watch, control} = useFormContext();
    const [showDrop, setShowDrop] = useState(false);
    const {options} = props;
    const isReviewOptions = options?.[0]?.hasOwnProperty("review_question_id");
    const currentValue = watch(props.id);
    const testid = props.testid
        ? props.testid.replace(/\s/g, "")
        : props.label
        ? `textArea-${props.label.replace(/\s/g, "")}`
        : props.id;

    const handleMultiChange = (key: any) => {
        const current = Array.isArray(currentValue) ? [...currentValue] : [];
        const foundIndex = current.findIndex(value => value === key);
        if (foundIndex >= 0) {
            current.splice(foundIndex, 1);
            setValue(props.id, current || []);
        } else {
            setValue(props.id, [...current, key]);
        }
    };

    const handleSingleChange = (value: string[]) => {
        setValue(props.id, value);
        setShowDrop(false);
    };

    const renderTooltip = (props, text) => (
        <Tooltip id="chatTooltip" {...props}>
            {text}
        </Tooltip>
    );

    useEffect(() => {
        if (props.onTextChange) {
            props.onTextChange(currentValue);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentValue]);
    return (
        <div
            className={`d-flex flex-column selectContainer cpInputContainer ${props.containerClassName} ${
                props.label ? "selectContainer" : "selectContainerNoLabel"
            }`}>
            {props.label ? (
                <label
                    className={`cpLabel ${props.labelClassName}`}
                    htmlFor={props.id}
                    aria-invalid={
                        props.validateOnTheFly
                            ? formState.errors[props.id]
                                ? "true"
                                : "false"
                            : formState.isSubmitted && formState.errors[props.id]
                            ? "true"
                            : "false"
                    }>
                    {props.label}
                    {props.tooltip && (
                        <OverlayTrigger placement="top" overlay={<Tooltip>{props.tooltip}</Tooltip>}>
                            <div className="cpInputTooltipTrigger">
                                <ReactIcon size="18px" iconName="BiHelpCircle" />
                            </div>
                        </OverlayTrigger>
                    )}
                </label>
            ) : null}
            {props.useCustom ? (
                <Dropdown
                    autoClose="outside"
                    className={styles.singleDropdownMenu}
                    show={showDrop}
                    onToggle={() => setShowDrop(curr => !curr)}>
                    <Dropdown.Toggle as="div" className="w-100">
                        <input
                            type="text"
                            disabled={props.disabled}
                            placeholder={props.placeholder}
                            id={props.id}
                            style={props.style!}
                            aria-label={props.placeholder}
                            className={`cpInput cPointer ${props.className} ${
                                currentValue?.[0] === "0" ? "placeholderOption" : null
                            }`}
                            aria-invalid={
                                props.validateOnTheFly
                                    ? formState.errors[props.id]
                                        ? "true"
                                        : "false"
                                    : formState.isSubmitted && formState.errors[props.id]
                                    ? "true"
                                    : "false"
                            }
                            autoComplete="off"
                            value={
                                Array.isArray(currentValue)
                                    ? ((options as any)?.find(o => o[0] === currentValue[0]) || [])[1] || ""
                                    : ""
                            }
                            {...register(props.id, {
                                required: props.isRequired,
                                valueAsDate: props.isDate,
                                valueAsNumber: props.isNumber,
                                validate: props.validationSchema,
                            })}
                        />
                    </Dropdown.Toggle>
                    <Dropdown.Menu show={showDrop}>
                        {(options as [string, string, string][])
                            .filter(op => op[0] !== "0")
                            .map((t: [string, string, string], index: number) => (
                                <div className="d-flex gap-1" key={index}>
                                    <Dropdown.Item
                                        as="div"
                                        data-testid={`${props.id}-option-${index}`}
                                        onClick={() => handleSingleChange(t)}
                                        className="w-auto col-auto position-relative ps-3">
                                        {t[1]}
                                    </Dropdown.Item>
                                    {t?.[2] && (
                                        <OverlayTrigger
                                            placement="right"
                                            delay={{show: 250, hide: 400}}
                                            overlay={p => renderTooltip(p, t[2])}>
                                            <div className={styles.helperIcon}>
                                                <ReactIcon size="18px" iconName={ICONS.info} />
                                            </div>
                                        </OverlayTrigger>
                                    )}
                                </div>
                            ))}
                    </Dropdown.Menu>
                </Dropdown>
            ) : props.isMultiple ? (
                <Controller
                    name={props.id}
                    control={control}
                    defaultValue={[]}
                    rules={{
                        required: props.isRequired,
                        validate: {
                            required: (newVal: any) => (props.isRequired ? newVal && newVal.length > 0 : true),
                        },
                    }}
                    render={({field}) => (
                        <>
                            <Dropdown
                                autoClose="outside"
                                className={styles.multipleDropdown}
                                {...field}
                                data-testid={testid}>
                                <Dropdown.Toggle as="div" className="w-100">
                                    <input
                                        type="text"
                                        disabled={props.disabled}
                                        placeholder={props.placeholder}
                                        id={props.id}
                                        style={props.style!}
                                        aria-label={props.placeholder}
                                        className={`cpInput cPointer ${props.className} ${
                                            !currentValue ? null : "placeholderOption"
                                        }`}
                                        aria-invalid={
                                            props.validateOnTheFly
                                                ? formState.errors[props.id]
                                                    ? "true"
                                                    : "false"
                                                : formState.isSubmitted && formState.errors[props.id]
                                                ? "true"
                                                : "false"
                                        }
                                        value={
                                            Array.isArray(currentValue)
                                                ? ((options as any)?.find(o => o[0] === currentValue[0]) || [])[1] || ""
                                                : ""
                                        }
                                        onChange={() => null}
                                        onBlur={() => null}
                                        autoComplete="off"
                                    />
                                </Dropdown.Toggle>
                                <Dropdown.Menu className={styles.multipleDropdownMenu}>
                                    {isReviewOptions
                                        ? (options as ReviewQuestionOption[]).map(
                                              (option: ReviewQuestionOption, index: number) => (
                                                  <Dropdown.Item
                                                      data-testid={`${props.id}-option-${index}`}
                                                      onClick={() => handleMultiChange(option.id)}
                                                      key={option.id}
                                                      value={option.id}
                                                      id={option.id}>
                                                      {option.display_value}
                                                  </Dropdown.Item>
                                              ),
                                          )
                                        : (options as [string, string][]).map((t: [string, string], index: number) =>
                                              props.isMultiple ? (
                                                  <Dropdown.Item onClick={() => handleMultiChange(t[0])} key={index}>
                                                      <ControlledCheckBox
                                                          id={props.id}
                                                          testid={`${props.id}-checkbox-${index}`}
                                                          value={
                                                              Array.isArray(currentValue)
                                                                  ? currentValue?.includes(t[0])
                                                                  : false
                                                          }
                                                          onChange={() => null}>
                                                          {t[1]}
                                                      </ControlledCheckBox>
                                                  </Dropdown.Item>
                                              ) : (
                                                  <Dropdown.Item
                                                      onClick={() => handleMultiChange(t[0])}
                                                      key={index}
                                                      data-testid={`${props.id}-option-${index}`}>
                                                      {t[1]}
                                                  </Dropdown.Item>
                                              ),
                                          )}
                                </Dropdown.Menu>
                            </Dropdown>
                        </>
                    )}
                />
            ) : (
                <select
                    disabled={props.disabled}
                    placeholder={props.placeholder}
                    id={props.id}
                    style={props.style!}
                    aria-label={props.placeholder}
                    className={`cpInput ${props.className} ${currentValue === "0" ? "placeholderOption" : null}`}
                    data-testid={testid}
                    aria-invalid={
                        props.validateOnTheFly
                            ? formState.errors[props.id]
                                ? "true"
                                : "false"
                            : formState.isSubmitted && formState.errors[props.id]
                            ? "true"
                            : "false"
                    }
                    {...register(props.id, {
                        required: props.isRequired,
                        valueAsDate: props.isDate,
                        valueAsNumber: props.isNumber,
                        validate: props.validationSchema,
                    })}>
                    {props.placeholder ? (
                        <option value="0" style={{color: "var(--cpGray)"}}>
                            {props.placeholder}
                        </option>
                    ) : null}
                    {isReviewOptions
                        ? (options as ReviewQuestionOption[]).map((option: ReviewQuestionOption, index: number) => (
                              <option
                                  key={option.id}
                                  value={option.id}
                                  id={option.id}
                                  data-testid={`${props.id}-option-${index}`}>
                                  {option.display_value}
                              </option>
                          ))
                        : (options as [string, string][]).map((t: [string, string], index: number) =>
                              props.isMultiple ? (
                                  <option key={index} value={t[0]} data-testid={`${props.id}-option-${index}`}>
                                      M{t[1]}
                                  </option>
                              ) : (
                                  <option key={index} value={t[0]} data-testid={`${props.id}-option-${index}`}>
                                      {t[1]}
                                  </option>
                              ),
                          )}
                </select>
            )}
            {options.length < 1 || !options.length ? (
                <div className={props.loaderClass ? `inputLoader ${props.loaderClass}` : "inputLoader"}>
                    <Loader version="iconOrange" inline loading />
                </div>
            ) : null}
            <span style={props.showError ? {display: "block"} : {display: "none"}} className="errorColor">
                {props.showError ? props.helperText : ""}
            </span>
        </div>
    );
};

export default DropdownComponent;

DropdownComponent.defaultProps = {
    showError: false,
    helperText: "",
    placeholder: "",
};
