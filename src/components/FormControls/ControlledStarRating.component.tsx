import StarOutlinedIcon from "@mui/icons-material/StarOutlined";
import {useState} from "react";
import useAppConfig from "../../hooks/useAppConfig";
import {useTimeoutReset} from "../../hooks/useTimeoutReset";
import styles from "../../styles/components/starRating.module.sass";
import {roundToNearestHalf, roundToNearestQtr} from "../../utils/formatNumber.util";

interface IProps {
    id: string;
    options?: any[];
    maxRating: number;
    showLabels?: boolean;
    leftText?: string;
    rightText?: string;
    label?: string;
    isRequired?: boolean;
    errorMessage?: string;
    inputError?: boolean;
    name?: string;
    checked?: any;
    disabled?: boolean;
    value?: any;
    onChange?: Function;
    iconSize?: number;
    containerClassName?: string;
    starWrapperClassName?: string;
    readOnly?: boolean;
    title?: string | ((rating: number) => string);
}

const ControlledStarRating = ({
    maxRating = 10,
    showLabels = true,
    leftText = "Very Dissatisfied",
    rightText = "Highly Satisfied",
    iconSize = 35,
    ...props
}: IProps) => {
    const {options, label, errorMessage, inputError} = props;
    const [hoverFill, setHoverFill] = useState<any>(null);
    const iconAnimation = useTimeoutReset(300);
    const {config, isLoadingConfig} = useAppConfig({config_key: "STAR_RATING_ALLOW_QUARTER_INCREMENTS"});
    const value = props.value || 0;
    const allowQuarterIncrements = !isLoadingConfig && config && config?.value.toUpperCase() !== "FALSE";

    return (
        <>
            {label && <label className={styles.starRatingLabel}>{label}</label>}
            <div className={styles.starRatingContainer + ` ${props.containerClassName}`}>
                {showLabels && (
                    <div className={styles.ratingLabel + " " + styles.ratingLabelLeft}>
                        {leftText && <span>{leftText}</span>}
                    </div>
                )}
                {[...Array(maxRating)].map((_, index) => {
                    const ratingValue = index + 1;
                    const isActive = options
                        ? value === options[index]?.id ||
                          options.findIndex(o => o?.id === value) >= ratingValue ||
                          ratingValue <= hoverFill
                        : ratingValue <= hoverFill || ratingValue <= (value ? Number(value) : -1);
                    const sentValue = options ? options[index]?.id || "" : String(ratingValue);
                    const sentNumber = Number(sentValue);
                    const roundedValue: number = allowQuarterIncrements
                        ? roundToNearestQtr(Number(value))
                        : roundToNearestHalf(Number(value));
                    const roundedString: string = String(roundedValue);
                    return (
                        <div
                            key={index}
                            onMouseEnter={() => !props.readOnly && setHoverFill(ratingValue)}
                            onMouseLeave={() => !props.readOnly && setHoverFill(null)}
                            title={
                                typeof props.title === "function"
                                    ? props.title(roundedValue)
                                    : props.title || roundedValue.toString()
                            }
                            className={`d-flex align-items-center justify-content-center ${props.starWrapperClassName}`}
                            onClick={() => {
                                if (props.readOnly) return;
                                iconAnimation.setTempValue({ratingValue, className: styles.starAnimation});
                                props.onChange && props.onChange(sentValue);
                            }}>
                            {roundedValue <= sentNumber - 1 || roundedValue >= sentNumber || roundedValue % 1 === 0 ? (
                                <StarOutlinedIcon
                                    sx={{
                                        height: iconSize,
                                        width: iconSize,
                                    }}
                                    className={`${
                                        isActive || roundedValue >= sentNumber ? styles.starActive : styles.starInactive
                                    } ${
                                        iconAnimation.tempValue?.ratingValue === ratingValue &&
                                        iconAnimation.tempValue?.className
                                    }`}
                                />
                            ) : (
                                <div
                                    style={{
                                        height: iconSize,
                                        width: iconSize,
                                        position: "relative",
                                    }}>
                                    <StarOutlinedIcon
                                        className={`${styles.starInactive} ${styles.halfStarIcon}`}
                                        sx={{
                                            height: iconSize,
                                            width: iconSize,
                                        }}
                                    />
                                    <StarOutlinedIcon
                                        className={`${styles.halfStarActive} ${
                                            roundedString.endsWith(".25")
                                                ? styles.qtrStarIcon
                                                : roundedString.endsWith(".75")
                                                ? styles.threeQtrStarIcon
                                                : styles.halfStarIconClipped
                                        }`}
                                        sx={{
                                            height: iconSize,
                                            width: iconSize,
                                        }}
                                    />
                                </div>
                            )}
                        </div>
                    );
                })}
                {showLabels && (
                    <div className={styles.ratingLabel + " " + styles.ratingLabelRight}>
                        {rightText && <span>{rightText}</span>}
                    </div>
                )}
                {inputError && <span className="errorColor ms-5">{errorMessage}</span>}
            </div>
        </>
    );
};

export default ControlledStarRating;
