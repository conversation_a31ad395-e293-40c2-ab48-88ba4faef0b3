.sliderContainer {
    max-width: 100vw;
    width: 100vw;
    height: calc(100vh - 100px);
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.sliderContainer img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slider {
    height: calc(100vh - 100px);
}

.slideBtn {
    background: var(--cpNavy) !important;
    border: none !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    color: #fcfcfc !important;
    padding: 13px 17px !important;
    width: 100% !important;
    display: inline-block !important;
}

.slideTitle {
    color: var(--cpNavy) !important;
    font-size: 40px !important;
    font-weight: bold !important;
    text-align: left !important;
}

.slideTxt {
    color: var(--cpNavy) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-align: left !important;
}

/* Tablet Rules (Bootstrap md breakpoint)*/

@media (min-width: 768px) {
    .sliderContainer {
        height: 625px;
    }

    .slider {
        height: 625px;
    }
}

/* Desktop Rules (Bootstrap lg breakpoint)*/

@media (min-width: 992px) {
    .slideBtn {
        width: auto !important;
    }

    .slideTitle {
        text-align: center !important;
        font-size: 56px !important;
    }

    .slideTxt {
        text-align: center !important;
    }
}
