import React, {useState} from "react";
import {<PERSON>, <PERSON>po<PERSON>, Button, Stack, useTheme, Alert, Paper, AlertTitle} from "@mui/material";
import Loader from "../../utils/loader";
import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import {FormProvider, useForm} from "react-hook-form";
import AlertBanner from "../../uicomponents/Molecules/AlertBanner/alertBanner.component";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import AddressAutoComplete from "../../uicomponents/Molecules/AddressAutocomplete/addressAutoComplete.component";
import {authService} from "../../services/auth.service";
import {AxiosError} from "axios";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../../hooks/useNotification";
import {RichTextSanitizer} from "../../utils/miscellaneous";
import {companyService} from "../../services/company.service";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import useAuthState from "../../hooks/useAuthState";
import useSettings from "../../hooks/useSettings";
import {useQueryClient} from "@tanstack/react-query";
import useActiveCompany from "../../hooks/useActiveCompany";
import usePusherState from "../../hooks/usePusherState";
import usePusher from "../../hooks/usePusher";
import WarningOutlinedIcon from "@mui/icons-material/WarningOutlined";
import routeConfig from "../../constants/routeConfig";

interface IProps {
    setStep: any;
    step: number;
    setModal: any;
    setQRCode: any;
    qrCode: string | undefined;
    setRecoveryCodes: any;
    recoveryCodes: string[] | undefined;
    activeCompanyId: string;
    needAddress: boolean;
    updateAuth: (boolean) => void;
    setSecretKey: React.Dispatch<React.SetStateAction<string>>;
    secretKey: string;
}
export function TwoFAAuthSteps({
    setStep,
    step,
    setModal,
    setQRCode,
    qrCode,
    setRecoveryCodes,
    recoveryCodes,
    activeCompanyId,
    needAddress,
    updateAuth,
    setSecretKey,
    secretKey
}: IProps) {
    const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);
    const theme = useTheme();
    const {clearAuthState} = useAuthState();
    const {clearSettings} = useSettings();
    const queryClient = useQueryClient();
    const {clearPState} = usePusherState();
    const {UnsubscribePusher} = usePusher();
    const {clearActiveCompany} = useActiveCompany();
    const [isLoading, setIsLoading] = useState<boolean>();
    const twoFAMethods = useForm();
    const confirmVerificationCode = twoFAMethods.watch("confirmVerificationCode");
    const twoFAVerifyMethods = useForm();
    const addressMethods = useForm();
    const addressAutoComplete = addressMethods.watch("addressAutoComplete");
    const confirm = useForm();
    const notify = useNotification();
    const [cannotScan, setCannotScan] = useState<boolean>(false);

    const handleError = (error: AxiosError) => {
        setIsLoading(false);
        const errMessage = getErrorFromArray(error);
        notify(errMessage, "Error");
    };

    const handleSuccessQRCode = (response: any) => {
        setQRCode(response.data?.svg);
        setIsLoading(false);
        setStep(step + 1);
    };
    const handleSuccessRecoveryCode = (response: any) => {
        setRecoveryCodes(response.data);
    };

    const handleSuccessSecretKey = (response: any) => {
        setSecretKey(response.data.secretKey);
    };

    const handleCopySecret = () => {
        if (secretKey) {
            navigator.clipboard.writeText(secretKey);
        }
    };

    const stepOne = async () => {
        authService.deleteTwoFA(() => {
            authService.enableTwoFA(() => {
                authService.getTwoFARecoveryCodes(handleSuccessRecoveryCode, handleError);
                authService.getTwoFASecretKey(handleSuccessSecretKey, handleError);
                authService.getTwoFAQRCode(handleSuccessQRCode, handleError);
            }, handleError);
        });
    };

    const stepTwo = async (formValues: any) => {
        authService.confirmTwoFA(
            formValues.confirmVerificationCode.trim(),
            "",
            () => {
                setIsLoading(false);
                setStep(step + 1);
            },
            handleError,
        );
    };

    const stepFour = async (formValues: any) => {
        const data = addressMethods.getValues();
        const companyAddressFieldId = "addressAutoComplete";
        const companyAddress = {
            id: activeCompanyId,
            address: formValues.addressAutoComplete || "",
            state: data[companyAddressFieldId + "_state"] || null,
            zip: data[companyAddressFieldId + "_zip"] || null,
            city: data[companyAddressFieldId + "_city"] || null,
            country: data[companyAddressFieldId + "_country"] || null,
        };
        companyService.update(
            companyAddress,
            () => {
                setIsLoading(false);
                setStep(step + 1);
            },
            handleError,
        );
    };

    const goToNextStep = async (formValues?: any) => {
        setIsLoading(true);
        switch (step) {
            case 1:
                await stepOne();
                break;
            case 2:
                await stepTwo(formValues);
                break;
            case 3:
                if (needAddress) {
                    setIsLoading(false);
                    setStep(step + 1);
                } else {
                    setIsLoading(false);
                    updateAuth(true);
                    setModal(false);
                    setStep(1);
                }
                break;
            case 4:
                await stepFour(formValues);
                break;
            case 5:
                setIsLoading(false);
                updateAuth(true);
                setModal(false);
                setStep(1);
                break;
        }
    };
    const handleCopy = () => {
        if (recoveryCodes) {
            const codes = recoveryCodes.join("\n");
            navigator.clipboard.writeText(codes);
        }
    };

    const logout = () => {
        setIsLoggingOut(true);
        authService.logout(
            () => {
                clearData();
                setIsLoggingOut(false);
            },
            () => {
                clearData();
                setIsLoggingOut(false);
            },
        );
    };

    const clearData = () => {
        UnsubscribePusher([], true);
        clearAuthState();
        clearSettings();
        queryClient.clear();
        clearPState();
        clearActiveCompany();
        window.location.href = window.location.origin + routeConfig.Contact.path;
    };

    return (
        <>
            {step == 1 ? (
                <>
                    <Typography
                        sx={{
                            color: theme.palette.neutral[700],
                            textAlign: "center",
                            fontFamily: "Poppins",
                            fontSize: "18px !important",
                            fontStyle: "normal",
                            fontWeight: "400 !important",
                            lineHeight: "150%",
                        }}>
                        We are working to enhance our platform and provide you with the best and safest experience
                        possible. To safeguard your company data, we now require all users to enable Two-Factor
                        Authentication.&nbsp;
                        <Typography
                            variant="18"
                            sx={{
                                color: theme.palette.neutral[700],
                                fontFamily: "Poppins",
                                fontSize: "18px !important",
                                fontStyle: "normal",
                                fontWeight: "600 !important",
                                lineHeight: "150%",
                            }}>
                            Setting it up is quick and easy!
                        </Typography>
                    </Typography>
                    <Stack
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            flexDirection: "column",
                            alignItems: "center",
                            width: "100% !important",
                        }}>
                        <MuiButtonComponent
                            onClick={goToNextStep}
                            id="letsDoIt"
                            variant={"tonal"}
                            type="button"
                            sx={{minWidth: 360, paddingY: 2, width: "55%"}}>
                            <Typography
                                sx={{
                                    color: theme.palette.common["white"],
                                    textAlign: "center",
                                    fontFamily: "Inter",
                                    fontSize: "18px !important",
                                    fontStyle: "normal",
                                    fontWeight: "600 !important",
                                    lineHeight: "120%",
                                }}>
                                {isLoading ? (
                                    <Loader inline loading version="iconWhite" />
                                ) : (
                                    <>
                                        <ArrowForwardOutlinedIcon fontSize="small" /> Let’s do it
                                    </>
                                )}
                            </Typography>
                        </MuiButtonComponent>
                        <Typography
                            onClick={logout}
                            sx={{
                                marginTop: "20px !important",
                                color: theme.palette.blue[600],
                                textAlign: "center",
                                fontFamily: "Inter",
                                fontSize: "16px !important",
                                fontStyle: "normal",
                                fontWeight: "700 !important",
                                lineHeight: "130%",
                                cursor: "pointer",
                            }}>
                            {isLoggingOut ? <Loader inline loading /> : "Have an issue? Contact us."}
                        </Typography>
                    </Stack>
                </>
            ) : step == 2 ? (
                <FormProvider {...twoFAMethods}>
                    <Box
                        component="form"
                        onSubmit={twoFAMethods.handleSubmit(goToNextStep)}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            width: "100%",
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100% !important",
                            }}>
                            <Typography
                                sx={{
                                    color: `${theme.palette.neutral[700]} !important`,
                                    textAlign: "center",
                                    fontFamily: "Poppins",
                                    fontSize: "18px !important",
                                    fontStyle: "normal",
                                    fontWeight: "600 !important",
                                    lineHeight: "150%",
                                }}>
                                Scan the QR Code on your authentication app
                            </Typography>
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    marginTop: "25px",
                                    width: "100% !important",
                                    gap: "40px",
                                    alignSelf: "stretch",
                                }}>
                                <Box
                                    component="span"
                                    dangerouslySetInnerHTML={RichTextSanitizer(qrCode)}
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        width: {xs: 100, md: 224},
                                        height: {xs: 100, md: 225},
                                    }}
                                />
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "flex-start",
                                        justifyContent: "center",
                                        width: {xs: "100%", md: "auto"},
                                        marginTop: "10px",
                                        marginLeft: "15px",
                                    }}>
                                    <Typography
                                        sx={{
                                            color: theme.palette.neutral[700],
                                            textAlign: "center",
                                            fontFamily: "Poppins",
                                            fontSize: "18px !important",
                                            fontStyle: "normal",
                                            fontWeight: "400 !important",
                                            lineHeight: "120%",
                                        }}>
                                        Enter a verification code
                                    </Typography>
                                    <TextBoxComponent
                                        placeholder="000000"
                                        id="confirmVerificationCode"
                                        maxLength={6}
                                        minLength={6}
                                        inputStyles={{
                                            backgroundColor: `${theme.palette.neutral[100]} !important`,
                                            textAlign: "center !important",
                                            letterSpacing: {sm: "5px", md: "37px"},
                                            width: {sm: "100px", md: "308px"},
                                            fontSize: "24px !important",
                                            fontWeight: "400 !important",
                                            lineHeight: "21px !important",
                                            "&::placeholder": {
                                                fontSize: "24px !important",
                                                fontWeight: "400 !important",
                                                lineHeight: "21px !important",
                                            },
                                            padding: "20px 0px 20px 20px !important",
                                        }}
                                        parentInputStyle={{
                                            "& .MuiInput-root::after": {
                                                borderBottom: `2px solid ${theme.palette.neutral[400]} !important`,
                                                transform: "scaleX(1)",
                                            },
                                        }}
                                        sx={{width: "100% !important"}}
                                        isRequired
                                        hideLabel
                                        validationSchema={v => /^[0-9]+$/.test(v as string)}
                                        formHelperTextProps={{
                                            sx: {textAlign: "center"},
                                        }}
                                    />
                                </Box>
                            </Box>
                            <Typography
                                onClick={() => setCannotScan(!cannotScan)}
                                sx={{
                                    marginTop: "20px !important",
                                    color: theme.palette.blue[600],
                                    textAlign: "center",
                                    fontFamily: "Inter",
                                    fontSize: "16px !important",
                                    fontStyle: "normal",
                                    fontWeight: "700 !important",
                                    lineHeight: "130%",
                                    cursor: "pointer",
                                }}>
                                Can’t scan?
                            </Typography>
                            {
                                cannotScan && secretKey &&
                                (
                                    <Typography
                                        sx={{
                                            marginTop: "20px !important",
                                            color: theme.palette.neutral[600],
                                            textAlign: "center",
                                            fontFamily: "Inter",
                                            fontSize: "16px !important",
                                            fontStyle: "normal",
                                            fontWeight: "700 !important",
                                            lineHeight: "130%",
                                        }}>
                                        <Typography
                                            onClick={() => handleCopySecret()}
                                            sx={{
                                                marginTop: "10px !important",
                                                marginBottom: "10px !important",
                                                color: theme.palette.blue[600],
                                                textAlign: "center",
                                                fontFamily: "Inter",
                                                fontSize: "16px !important",
                                                fontStyle: "normal",
                                                fontWeight: "700 !important",
                                                lineHeight: "130%",
                                                cursor: "pointer",
                                            }}>
                                            Click here to copy the code
                                        </Typography>
                                        {secretKey}
                                    </Typography>
                                )
                            }
                            <MuiButtonComponent
                                id="verifyBtn"
                                variant={"tonal"}
                                type="submit"
                                sx={{minWidth: 360, paddingY: 2, width: "54%", marginTop: "34px"}}
                                disabled={
                                    isLoading || confirmVerificationCode?.includes("NaN") || !confirmVerificationCode
                                }>
                                <Typography
                                    sx={{
                                        color: theme.palette.common["white"],
                                        textAlign: "center",
                                        fontFamily: "Inter",
                                        fontSize: "18px !important",
                                        fontStyle: "normal",
                                        fontWeight: "600 !important",
                                        lineHeight: "120%",
                                    }}>
                                    {isLoading ? (
                                        <Loader inline loading version="iconWhite" />
                                    ) : (
                                        <>
                                            <ArrowForwardOutlinedIcon fontSize="small" sx={{marginRight: 1}} /> Verify
                                            and Enable
                                        </>
                                    )}
                                </Typography>
                            </MuiButtonComponent>
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                }}>
                                <Typography
                                    onClick={logout}
                                    sx={{
                                        marginTop: "15px !important",
                                        color: theme.palette.blue[600],
                                        textAlign: "center",
                                        fontFamily: "Inter",
                                        fontSize: "16px !important",
                                        fontStyle: "normal",
                                        fontWeight: "700 !important",
                                        lineHeight: "130%",
                                        cursor: "pointer",
                                    }}>
                                    {isLoggingOut ? <Loader inline loading /> : "Have an issue? Contact us."}
                                </Typography>
                            </Box>
                        </Box>
                    </Box>
                </FormProvider>
            ) : step == 3 ? (
                <FormProvider {...twoFAVerifyMethods}>
                    <Box
                        component="form"
                        onSubmit={twoFAVerifyMethods.handleSubmit(goToNextStep)}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            width: "100%",
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100% !important",
                            }}>
                            <Stack>
                                <AlertBanner
                                    id="welcome-to-channel-program"
                                    subTitle={{
                                        text: <>2FA is now enabled on your account</>,
                                        sx: {
                                            color: theme.palette.neutral[800],
                                            fontFamily: "Inter",
                                            fontSize: "16px",
                                            fontStyle: "normal",
                                            fontWeight: "700",
                                            lineHeight: "130%",
                                        },
                                    }}
                                    variant="success"
                                    containerSx={{
                                        padding: "10px 0px 16px 16px",
                                    }}
                                    icon={
                                        <CheckCircleOutlineIcon
                                            sx={{height: 24, width: 24, marginTop: "6px"}}
                                            htmlColor={theme.palette.success[500]}
                                        />
                                    }
                                />
                                <Box
                                    sx={{
                                        marginTop: "15px",
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        width: "100% !important",
                                    }}>
                                    <Alert severity="default" icon={false} sx={{padding: "0px !important"}}>
                                        <Box display="flex" alignItems="center">
                                            <WarningOutlinedIcon
                                                sx={{
                                                    color: theme.palette.warning[500],
                                                    fontSize: "24px",
                                                }}
                                            />
                                            <Typography
                                                sx={{
                                                    color: theme.palette.neutral[700],
                                                    textAlign: "center",
                                                    fontFamily: "Poppins",
                                                    fontSize: "18px !important",
                                                    fontStyle: "normal",
                                                    fontWeight: "600 !important",
                                                    lineHeight: "150%",
                                                }}>
                                                &nbsp;Save your recovery codes
                                            </Typography>
                                        </Box>
                                        <Box display="flex" alignItems="center" sx={{marginTop: "10px"}}>
                                            <Typography
                                                variant="14"
                                                sx={{
                                                    color: theme.palette.neutral[800],
                                                    fontFamily: "Inter",
                                                    fontSize: "14px !important",
                                                    fontStyle: "normal !important",
                                                    fontWeight: "400 !important",
                                                    lineHeight: "20px !important",
                                                    letterSpacing: "0.1px !important",
                                                }}>
                                                It’s crucial to securely store your recovery codes in a location that is
                                                both safe and easily accessible to you. While logged in, you can find
                                                them in your Profile Settings.
                                            </Typography>
                                        </Box>
                                    </Alert>
                                </Box>
                                <Paper
                                    variant="outlined"
                                    sx={{
                                        marginTop: "15px",
                                        display: "flex",
                                        padding: "16px",
                                        alignItems: "flex-start",
                                        gap: "10px",
                                        alignSelf: "stretch",
                                        borderRadius: "8px",
                                        backgroundColor: theme.palette.neutral[200],
                                        position: "relative",
                                    }}>
                                    <Stack>
                                        {recoveryCodes &&
                                            recoveryCodes.map((code, index) => (
                                                <Typography
                                                    variant="body2"
                                                    key={index}
                                                    sx={{
                                                        fontSize: "14px !important",
                                                    }}>
                                                    • {code}
                                                </Typography>
                                            ))}
                                    </Stack>
                                    <Button
                                        onClick={handleCopy}
                                        sx={{
                                            position: "absolute",
                                            top: {sm: 0, md: 15},
                                            right: 15,
                                            color: theme.palette.blue[600],
                                            "&:hover": {
                                                color: theme.palette.blue[600],
                                                backgroundColor: "transparent",
                                            },
                                            "&:active": {
                                                backgroundColor: "transparent",
                                                color: theme.palette.blue[600],
                                            },
                                        }}
                                        startIcon={<ContentCopyIcon fontSize="small" />}>
                                        COPY
                                    </Button>
                                </Paper>
                            </Stack>
                            <Stack
                                sx={{
                                    marginTop: "24px",
                                    display: "flex",
                                    justifyContent: "center",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    width: "100% !important",
                                }}>
                                <MuiButtonComponent
                                    id="goNext"
                                    type="submit"
                                    variant="outlined"
                                    color="blue"
                                    sx={{width: {sm: "70%", md: "53%"}, paddingY: 2}}>
                                    <Typography
                                        sx={{
                                            color: theme.palette.blue[800],
                                            textAlign: "center",
                                            fontFamily: "Inter",
                                            fontSize: "18px !important",
                                            fontStyle: "normal !important",
                                            fontWeight: "600 !important",
                                            lineHeight: "120% !important",
                                            gap: "8px",
                                        }}>
                                        {needAddress ? (
                                            <>
                                                <ArrowForwardOutlinedIcon fontSize="small" sx={{marginRight: 1}} /> Next
                                            </>
                                        ) : (
                                            <>Close</>
                                        )}
                                    </Typography>
                                </MuiButtonComponent>
                            </Stack>
                        </Box>
                    </Box>
                </FormProvider>
            ) : step == 4 ? (
                <FormProvider {...addressMethods}>
                    <Box
                        component="form"
                        onSubmit={addressMethods.handleSubmit(goToNextStep)}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            width: "100%",
                        }}>
                        <Box display="block" alignItems="center">
                            <Typography
                                sx={{
                                    color: `${theme.palette.neutral[700]} !important`,
                                    textAlign: "center !important",
                                    fontFamily: "Poppins !important",
                                    fontSize: "18px !important",
                                    fontStyle: "normal !important",
                                    fontWeight: "600 !important",
                                    lineHeight: "150% !important",
                                }}>
                                Update your company address!
                            </Typography>
                            <Typography
                                sx={{
                                    color: `${theme.palette.neutral[700]} !important`,
                                    marginTop: "8px !important",
                                    textAlign: "center !important",
                                    fontFamily: "Inter !important",
                                    fontSize: "14px !important",
                                    fontStyle: "normal !important",
                                    fontWeight: "400 !important",
                                    lineHeight: "20px !important",
                                    letterSpacing: "0.1px !important",
                                }}>
                                As part of our data compliance efforts, we need you to verify your company location.
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                marginTop: "24px",
                                display: "flex",
                                justifyContent: "center",
                                flexDirection: "column",
                                alignItems: "center",
                                width: "100% !important",
                            }}>
                            <AddressAutoComplete
                                label="Location"
                                id="addressAutoComplete"
                                renderInput={undefined}
                                options={undefined}
                                placeholder="Enter head office address"
                                validateOnTheFly
                                isRequired
                                value={addressAutoComplete}
                                shouldFormat
                                registerDetails
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                endAdornment="end_icon"
                                popupIcon={<SearchOutlinedIcon sx={{width: "24px", height: "24px"}} />}
                                componentsProps={{
                                    popupIndicator: {
                                        sx: {
                                            transform: "none !important",
                                        },
                                    },
                                }}
                                tooltipHelperText="Location is required"
                                sx={{
                                    width: "100% !important",
                                }}
                            />
                        </Box>
                        <MuiButtonComponent
                            id="verifyAddress"
                            variant={"tonal"}
                            type="submit"
                            sx={{width: {sm: "70%", md: "53%"}, paddingY: 2, marginTop: "30px"}}
                            disabled={isLoading || addressAutoComplete?.includes("NaN") || !addressAutoComplete}>
                            <Typography
                                sx={{
                                    textAlign: "center",
                                    fontFamily: "Inter",
                                    fontSize: "18px !important",
                                    fontStyle: "normal !important",
                                    fontWeight: "600 !important",
                                    lineHeight: "120% !important",
                                    gap: "8px",
                                }}>
                                {isLoading ? (
                                    <Loader inline loading version="iconWhite" />
                                ) : (
                                    <>
                                        <ArrowForwardOutlinedIcon fontSize="small" sx={{marginRight: 1}} /> It’s up to
                                        date
                                    </>
                                )}
                            </Typography>
                        </MuiButtonComponent>
                    </Box>
                </FormProvider>
            ) : (
                <FormProvider {...confirm}>
                    <Box
                        component="form"
                        onSubmit={confirm.handleSubmit(goToNextStep)}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            width: "100%",
                        }}>
                        <Stack spacing={3} sx={{width: "100%"}}>
                            <Alert
                                icon={false}
                                severity="success"
                                sx={{
                                    display: "flex",
                                    padding: "30px 16px",
                                    flexDirection: "column",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    gap: "16px",
                                    alignSelf: "stretch",
                                    borderRadius: "8px",
                                    background: theme.palette.success[100],
                                }}>
                                <Box sx={{display: "flex", alignItems: "center", gap: 2, textAlign: "center"}}>
                                    <CheckCircleOutlineIcon
                                        sx={{height: 20, width: 20}}
                                        htmlColor={theme.palette.success[500]}
                                    />
                                    <Box sx={{textAlign: {sm: "left", md: "justify"}}}>
                                        <AlertTitle
                                            sx={{
                                                marginTop: "4px",
                                                color: theme.palette.neutral[800],
                                                fontFamily: "Inter !important",
                                                fontSize: "16px !important",
                                                fontStyle: "normal !important",
                                                fontWeight: "700 !important",
                                                lineHeight: "130% !important",
                                            }}>
                                            All requested information has been successfully updated!
                                        </AlertTitle>
                                    </Box>
                                </Box>
                            </Alert>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: "100%",
                                    margin: "10px 0px 0px 0px !important",
                                }}>
                                <MuiButtonComponent
                                    id="close"
                                    type="submit"
                                    variant="outlined"
                                    color="blue"
                                    sx={{width: {sm: "70%", md: "55%"}, paddingY: 2, marginTop: "15px"}}>
                                    <Typography
                                        sx={{
                                            color: theme.palette.blue[800],
                                            textAlign: "center !important",
                                            fontFamily: "Inter !important ",
                                            fontSize: "18px !important",
                                            fontStyle: "normal !important",
                                            fontWeight: "600 !important",
                                            lineHeight: "120% !important",
                                        }}>
                                        <>Close</>
                                    </Typography>
                                </MuiButtonComponent>
                            </Box>
                        </Stack>
                    </Box>
                </FormProvider>
            )}
        </>
    );
}
