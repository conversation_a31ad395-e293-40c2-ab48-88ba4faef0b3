import {Navigate, useLocation} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import {isAuthExpired} from "../../hooks/useLocalStorage";
import usePermissions from "../../hooks/usePermissions";
import {TPermissionGroupsKeysArr} from "../../Interfaces/features/features.interface";

interface IProps {
    component: React.ComponentType;
    permissions?: TPermissionGroupsKeysArr;
}

// eslint-disable-next-line react/prop-types
export default function AdminRoute({component: Component, ...rest}: IProps) {
    const {authState} = useAuthState();
    const isExpired = isAuthExpired();
    const loggedIn =
        (authState?.authenticated ||
            (localStorage.authState && JSON.parse(localStorage.authState).value.authenticated)) &&
        !isExpired;
    const location = useLocation();
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const hasAccess = hasPermissions(rest?.permissions || []);

    return (
        <>
            {loggedIn && hasAccess ? (
                <Component />
            ) : isLoadingPermissions ? null : (
                <Navigate
                    state={{from: {...location, privRoute: true, clearAuth: isExpired}}}
                    to={routeConfig.Home.path}
                />
            )}
        </>
    );
}
