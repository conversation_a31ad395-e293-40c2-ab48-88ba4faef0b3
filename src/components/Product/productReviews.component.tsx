import {QueryFunction, UseInfiniteQueryResult, useInfiniteQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import React, {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation, useSearchParams} from "react-router-dom";
import {ICompany} from "../../Interfaces/company.interface";
import {IProducts, IReview, PaginatedReviews} from "../../Interfaces/products.interface";
import {STALE_TIME} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {useInfinite} from "../../hooks/useInfinite";
import useNotification from "../../hooks/useNotification";
import {enumService} from "../../services/enum.service";
import {productService} from "../../services/products.service";
import styles from "../../styles/components/productCard.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
// import ReviewCard from "../Cards/ReviewCard.component";
import useAppConfig from "../../hooks/useAppConfig";
import ReviewCard from "../../uicomponents/Organism/ReviewCard/reviewCard.component";
import {TDynamicCompany} from "../../utils/company.util";
import DropdownComponent from "../FormControls/dropdown.component";

type TCompany = TDynamicCompany<ICompany, ["profile_images"]>;

interface IProps {
    friendly_url?: string;
    product?: IProducts;
    company?: TCompany;
    showReviewQuote?: boolean;
    maxReviews?: number;
    disableInfiniteLoad?: boolean;
}

export default function ProductReviews(props: IProps) {
    const {friendly_url, product} = props;
    const [reviewOrder, setReviewOrder] = useState<[string, string][]>([]);
    const reviewMethods = useForm();
    const orderWatcher = reviewMethods.watch("reviewOrder");
    const notify = useNotification();
    const [searchParams] = useSearchParams();
    const review_id = searchParams.get("review_id");
    const location = useLocation();
    const isProductPage = location.pathname.includes("/product/");
    const {config} = useAppConfig({config_key: "PREVENT_EDIT_REVIEWS_BEFORE_DATE"});

    const meta: any = useRef({last_page: 1});

    const getProductReview: QueryFunction<PaginatedReviews, (string | number)[]> = ({pageParam = 1}) =>
        new Promise(resolve => {
            const selectedOrder = orderWatcher === "MostRecent" ? undefined : orderWatcher;
            const requestBody: any = {
                page: pageParam,
                review_order: selectedOrder ?? undefined,
            };
            if (!selectedOrder) {
                requestBody.review_id = review_id;
            }
            return productService
                .getReviews(friendly_url ?? "", requestBody)
                .then((r: AxiosResponse<any>) => {
                    meta.current = r.data.meta;
                    resolve(r.data);
                })
                .catch((e: AxiosError<any>) => {
                    if (e?.code !== "ERR_CANCELED") notify(getErrorFromArray(e), "Error");
                });
        });

    const productReviews: UseInfiniteQueryResult<PaginatedReviews, any> = useInfiniteQuery(
        ["product-reviews", friendly_url ?? ""],
        getProductReview,
        {
            getNextPageParam: feed => {
                return feed.meta.last_page > feed.meta.current_page
                    ? feed.meta.current_page + 1
                    : feed.meta.current_page;
            },
            staleTime: STALE_TIME,
            refetchOnWindowFocus: false,
        },
    );
    const {lastElementRef, pageNum} = useInfinite(meta.current?.last_page || 0, productReviews.isFetchingNextPage);
    const hasContent = !!productReviews.data?.pages?.[0]?.data.length;

    useEffect(() => {
        productReviews.refetch();
        //eslint-disable-next-line
    }, [orderWatcher]);

    useEffect(() => {
        if (productReviews.hasNextPage && pageNum > 1) {
            productReviews.fetchNextPage({pageParam: pageNum});
        }
        //eslint-disable-next-line
    }, [pageNum]);

    useEffect(() => {
        if (isProductPage) {
            enumService.getReviewOrderFilter(
                (res: AxiosResponse<{[id: string]: string}>) => {
                    const filters: [string, string][] = Object.keys(res.data).map((key: string) => [
                        key,
                        res.data[key],
                    ]);
                    reviewMethods.setValue("reviewOrder", "MostRecent");
                    setReviewOrder(filters);
                },
                (err: AxiosError<any>) => {
                    notify(getErrorFromArray(err), "Error");
                },
            );
        }
        //eslint-disable-next-line
    }, [isProductPage]);
    return (
        <>
            {hasContent ? (
                <>
                    <div className="d-flex justify-content-between pe-4">
                        {/* <TitleContainer
                            as="h2"
                            text={`${product?.name || ""} Reviews`}
                            position="start"
                            noConnector
                            className="mb-0"
                        /> */}
                        {isProductPage && !!reviewOrder?.length && (
                            <FormProvider {...reviewMethods}>
                                <form>
                                    <DropdownComponent
                                        id="reviewOrder"
                                        options={reviewOrder}
                                        selectedOption={orderWatcher}
                                        containerClassName={styles.sortByContainer}
                                        isRequired
                                    />
                                </form>
                            </FormProvider>
                        )}
                    </div>
                    {productReviews.isRefetching ? (
                        <div className="d-flex justify-content-center mt-5">
                            <Loader inline loading big />
                        </div>
                    ) : (
                        <>
                            {productReviews?.data?.pages
                                .slice(0, props.maxReviews ? 1 : productReviews.data.pages.length)
                                .map(page => (
                                    <div key={page.meta.current_page}>
                                        {page.data
                                            .slice(0, props.maxReviews ? props.maxReviews : page.data.length)
                                            .map((review: IReview) => {
                                                return (
                                                    <React.Fragment key={review.id}>
                                                        <hr className="fadedBorder" />
                                                        <ReviewCard
                                                            review={review}
                                                            key={review.id}
                                                            company={props.company}
                                                            showReviewQuote={props.showReviewQuote}
                                                            product={product}
                                                            isTopReview={review.id === review_id}
                                                            preventReviewsBefore={config?.value}
                                                        />
                                                    </React.Fragment>
                                                );
                                            })}
                                    </div>
                                ))}
                            {!props.disableInfiniteLoad && (
                                <div ref={lastElementRef} className="text-center">
                                    {productReviews.isFetchingNextPage ? (
                                        <div className="d-flex justify-content-center align-items-center w-100">
                                            <Loader inline loading />
                                        </div>
                                    ) : (
                                        !productReviews.data?.pages?.[0]?.data?.length &&
                                        "No more content to display right now. Check back later!"
                                    )}
                                </div>
                            )}
                        </>
                    )}
                </>
            ) : (
                <>
                    {/* <div className="d-flex justify-content-between pe-4">
                        <TitleContainer
                            as="h2"
                            text={`${product?.name || ""} Reviews`}
                            position="start"
                            noConnector
                            className="mb-0"
                        />
                    </div> */}
                    <hr className="fadedBorder" />
                    {productReviews.isLoading ? (
                        <div className="d-flex justify-content-center">
                            <Loader inline loading big />
                        </div>
                    ) : (
                        <div className="text-center">
                            No Reviews yet. Be the first to{" "}
                            <Link
                                to={{
                                    pathname: routeConfig.CreateReview.path.replace(
                                        ":friendly_url",
                                        product?.friendly_url ? product.friendly_url : product?.id || "",
                                    ),
                                    search: product?.friendly_url ? "" : `?company_id=${product?.parent?.id}`,
                                }}>
                                write a review!
                            </Link>
                        </div>
                    )}
                </>
            )}
        </>
    );
}
