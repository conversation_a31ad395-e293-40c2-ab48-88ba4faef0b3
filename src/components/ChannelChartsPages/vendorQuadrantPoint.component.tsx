import {useRef, useState} from "react";
import {OverlayTrigger, Popover} from "react-bootstrap";
import {Link} from "react-router-dom";
import {WRITE_A_REVIEW_BUTTON_TEXT} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import styles from "../../styles/pages/channelChartPage.module.sass";
import {createImageFromInitials} from "../../utils/imageFromText.util";

const VendorQuadrantPoint = (p: any) => {
    const [show, setShow] = useState(false);
    const isHoveringPopover = useRef(false);
    const isHoveringPoint = useRef(false);
    const {
        animationClassName: _animationClassName,
        clearTooltipPayload: _clearTooltipPayload,
        tooltipPayload: _tooltipPayload,
        tooltipPosition: _tooltipPosition,
        resetHeight: _resetHeight,
        resetAllMulti: _resetAllMulti,
        chartWrapperRef: _chartWrapperRef,
        quadrantInfo,
        isActive,
        setPreventMultiClose,
        ...pointProps
    } = p;
    const overlayPlacement: any = quadrantInfo
        ? `${quadrantInfo?.isTopQuadrant ? "bottom-" : "top-"}${quadrantInfo.isLeftQuadrant ? "end" : "start"}`
        : "auto";
    /* const cooldownMs = 250; */

    return (
        <foreignObject {...pointProps} className={styles.customPoint}>
            <OverlayTrigger
                placement={overlayPlacement}
                delay={{show: 0, hide: 0}}
                show={show}
                popperConfig={{
                    modifiers: [
                        {
                            name: "preventOverflow",
                            options: {
                                enabled: true,
                                boundariesElement: "viewport",
                            },
                        },
                        {
                            name: "offset",
                            options: {
                                offset: ({popper}) => {
                                    if (overlayPlacement === "bottom-end") {
                                        return [popper.width - 5, -5];
                                    } else if (overlayPlacement === "top-end") {
                                        return [popper.width - 5, -5];
                                    } else if (overlayPlacement === "top-start") {
                                        return [-popper.width + 5, -5];
                                    } else if (overlayPlacement === "bottom-start") {
                                        return [-popper.width + 5, -5];
                                    }
                                    return [15, -15];
                                },
                            },
                        },
                        {
                            name: "hide",
                            enabled: true,
                        },
                    ],
                }}
                overlay={overlayProps => (
                    <Popover
                        id="popover-menu"
                        {...overlayProps}
                        className="shadow border-0 rounded mw-100"
                        onMouseEnter={() => {
                            isHoveringPopover.current = true;
                            isHoveringPoint.current = false;
                            setPreventMultiClose && setPreventMultiClose(true);
                        }}
                        onMouseLeave={() => {
                            isHoveringPopover.current = false;
                            if (!isHoveringPoint.current && !isHoveringPopover.current) {
                                setPreventMultiClose && setPreventMultiClose(false);
                                setShow(false);
                            }
                        }}>
                        <Popover.Body className="popoverBody w-100">
                            <div
                                className={`d-flex flex-column align-items-center bg-light shadow p-3 rounded ${styles.tooltipWrapper}`}>
                                <div
                                    className={`d-flex flex-column align-items-center justify-content-center w-100 ${styles.tooltipHeader}`}>
                                    {p.company.has_claimers ? (
                                        <>
                                            <Link
                                                target="_blank"
                                                to={routeConfig.VendorProfile.path.replace(
                                                    ":id",
                                                    p.company.friendly_url,
                                                )}>
                                                <img
                                                    src={
                                                        p.company?.avatar
                                                            ? p.company.avatar
                                                            : createImageFromInitials(50, p.company.name, "", "company")
                                                    }
                                                    alt="avatar"
                                                    className={styles.tooltipVendorAvatar}
                                                />
                                            </Link>
                                            <div className={styles.vendorInfoContainer}>
                                                <Link
                                                    target="_blank"
                                                    className={styles.vendorNameText}
                                                    to={routeConfig.VendorProfile.path.replace(
                                                        ":id",
                                                        p.company.friendly_url,
                                                    )}>
                                                    <h2 className="text-center mt-1">{p.company.name}</h2>
                                                </Link>
                                                <Link
                                                    to={
                                                        routeConfig.VendorProfile.path.replace(
                                                            ":id",
                                                            p.company.friendly_url,
                                                        ) + "?tab=products"
                                                    }
                                                    target="_blank">
                                                    <span className={styles.writeReviewLink}>
                                                        {WRITE_A_REVIEW_BUTTON_TEXT}
                                                    </span>
                                                </Link>
                                            </div>
                                        </>
                                    ) : (
                                        <>
                                            <img
                                                src={
                                                    p.company?.avatar
                                                        ? p.company.avatar
                                                        : createImageFromInitials(50, p.company.name, "", "company")
                                                }
                                                alt="avatar"
                                                className={styles.tooltipVendorAvatar}
                                            />
                                            <div className={styles.vendorInfoContainer}>
                                                <h2 className={`text-center ${styles.vendorNameText}`}>
                                                    {p.company.name}
                                                </h2>
                                            </div>
                                        </>
                                    )}
                                </div>
                                <div
                                    className={`d-flex flex-column justify-content-center align-items-center ${styles.pitchesWrapper}`}>
                                    {p.pitches.map((pitch, index) => (
                                        <div className="d-flex flex-column w-100 text-center" key={pitch.id}>
                                            <Link
                                                target="_blank"
                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                    ":friendly_url",
                                                    pitch.category_friendly_url,
                                                )}>
                                                <h5>{pitch.category_name}</h5>
                                            </Link>
                                            <span style={{whiteSpace: "nowrap"}}>{pitch.pitch_name}</span>
                                            {index + 1 !== p.pitches.length && <hr className="fadedBorder my-2" />}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </Popover.Body>
                    </Popover>
                )}>
                <img
                    alt="avatar"
                    src={
                        p.company?.avatar
                            ? p.company.avatar
                            : createImageFromInitials(25, p.company.name, "", "company")
                    }
                    className={`${styles.companyAvatar} ${p.animationClassName}`}
                    style={{...p.styles}}
                    onMouseEnter={() => {
                        isHoveringPopover.current = false;
                        isHoveringPoint.current = true;
                        !show && setShow(true);
                    }}
                    onMouseLeave={() => {
                        isHoveringPoint.current = false;
                        setTimeout(() => {
                            if (!isHoveringPopover.current && !isHoveringPoint.current) {
                                setShow(false);
                                if (!isActive) {
                                    setPreventMultiClose && setPreventMultiClose(false);
                                    setShow(false);
                                }
                            }
                        }, 100);
                    }}
                />
            </OverlayTrigger>
        </foreignObject>
    );
};

export default VendorQuadrantPoint;
