import {SxProps, Theme, useTheme} from "@mui/material";
import {ChangeEvent, useRef, useState} from "react";
import {IImage} from "../../Interfaces/images.interface";
import {
    CSV_TYPE_NOT_ACCEPTED,
    DOCUMENT_TYPE_NOT_ACCEPTED,
    IMAGE_TYPE_NOT_ACCEPTED,
    IMAGE_TYPE_NOT_ACCEPTED_BRANDABLE,
    IMAGE_VIDEO_TYPE_NOT_ACCEPTED,
    VIDEO_TYPE_NOT_ACCEPTED,
} from "../../constants/errorMessages.constant";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import useNotification from "../../hooks/useNotification";
import styles from "../../styles/components/uploadComponent.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import getVideoThumbnail from "../../utils/getVideoThumbnail.util";
import {maxSize as Size, accepts, supportText} from "../../utils/supportText.util";
import ButtonComponent from "../FormControls/button.component";
import {CloudUploadIcon, XIcon} from "../Icons";
import SubtitleContainer from "../Titles/subtitleContainer.component";

interface IDropZoneProps {
    style?: React.CSSProperties | undefined;
}
interface IProps {
    id: string;
    file?: File;
    isMultiple?: boolean;
    files?: any[];
    setFile?: any;
    thumbnail?: any;
    setThumbnail?: any;
    thumbnailUrl?: any;
    setThumbnailUrl?: any;
    imagePreviewUrl?: any;
    setImagePreviewUrl?: any;
    clearInputFile?: any;
    fileId?: string;
    maxSize: string;
    maxSizeAlt?: string;
    type: MediaTypes;
    friendlyUrl?: string;
    entityId?: string;
    fileToEdit?: any;
    fileError?: boolean;
    onGetVideoDuration?: Function;
    className?: string;
    icon?: React.ReactNode;
    customResetBtn?: string;
    customSupportText?: any;
    editBoolean?: boolean;
    removeFiles?: Function;
    hidePreview?: boolean;
    accepts?: string;
    dropZoneProps?: IDropZoneProps;
    wrapperSx?: SxProps<Theme>;
    onError?: (message?: string) => void;
    noSupportText?: boolean;
}

export function UploadComponent(props: IProps) {
    const [dragging, setDragging] = useState(false);
    const [loadedVideoData, setLoadedVideoData] = useState(false);
    const [loadedVideoMetadata, setLoadedVideoMetadata] = useState(false);

    const notify = useNotification();
    const inputFilePicker = useRef<HTMLInputElement | null>(null);
    const maxSize = parseInt(props.maxSize) * 1024 * 1024;
    const videoRef = useRef<HTMLVideoElement>(null);
    const thumbInput = useRef<HTMLInputElement>(null);
    const theme = useTheme();

    const importFile = () => {
        if (!inputFilePicker.current) return;
        inputFilePicker.current.click();
    };

    const clearInputs = () => {
        props.clearInputFile();
        setLoadedVideoData(false);
        setLoadedVideoMetadata(false);
    };

    const triggerError = (message: string) => {
        if (props?.onError) {
            props.onError(message);
            return;
        }
        notify(message, "Error");
    };

    const handleUpdateFile = async (e: ChangeEvent<HTMLInputElement>) => {
        const file: File = e.target.files![0];
        if (props?.onError) {
            props.onError(undefined);
        }

        if (file.size > maxSize) {
            triggerError("File is too big!");
            return;
        }

        const acceptedTypes = props?.accepts || accepts.image;
        if (props.type === MediaTypes.CSV && !accepts.csv.split(", ").includes(file.type)) {
            triggerError(CSV_TYPE_NOT_ACCEPTED);
            return;
        }
        if (props.type === MediaTypes.Image && !acceptedTypes.split(", ").includes(file.type)) {
            triggerError(props?.accepts ? IMAGE_TYPE_NOT_ACCEPTED_BRANDABLE : IMAGE_TYPE_NOT_ACCEPTED);
            return;
        }
        if (props.type === MediaTypes.Video && !accepts.video.split(", ").includes(file.type)) {
            triggerError(VIDEO_TYPE_NOT_ACCEPTED);
            return;
        }
        if (props.type === MediaTypes.Document && !accepts.document.split(", ").includes(file.type)) {
            if (file) {
                const fileName = file.name;
                const fileExtension = fileName.split(".").pop();
                if (fileExtension !== "key") {
                    triggerError(DOCUMENT_TYPE_NOT_ACCEPTED);
                    return;
                }
            }
        }
        if (
            props.type === MediaTypes.ImageOrVideo &&
            !accepts.video.split(", ").includes(file.type) &&
            !accepts.image.split(", ").includes(file.type)
        ) {
            triggerError(IMAGE_VIDEO_TYPE_NOT_ACCEPTED);
            return;
        }
        if (file.type.split("/")[0] === MediaTypes.Image) {
            props.setImagePreviewUrl(URL.createObjectURL(file));
        }

        props.setFile(file);
    };

    const handleDrop = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);

        const file = e.dataTransfer.files[0];
        if (props?.onError) {
            props.onError(undefined);
        }

        if (file) {
            if (file.size > maxSize) {
                triggerError("File is too big!");
                return;
            }
            if (props.type === MediaTypes.CSV && !accepts.csv.split(", ").includes(file.type)) {
                triggerError(CSV_TYPE_NOT_ACCEPTED);
                return;
            }
            if (props.type === MediaTypes.Image && !accepts.image.split(", ").includes(file.type)) {
                if (file) {
                    const fileName = file.name;
                    const fileExtension = fileName.split(".").pop();
                    if (fileExtension !== "key") {
                        triggerError(IMAGE_TYPE_NOT_ACCEPTED);
                        return;
                    }
                }
            }
            if (props.type === MediaTypes.Video && !accepts.video.split(", ").includes(file.type)) {
                triggerError(VIDEO_TYPE_NOT_ACCEPTED);
                return;
            }
            if (props.type === MediaTypes.Document && !accepts.document.split(", ").includes(file.type)) {
                triggerError(DOCUMENT_TYPE_NOT_ACCEPTED);
                return;
            }
            if (
                props.type === MediaTypes.ImageOrVideo &&
                !accepts.video.split(", ").includes(file.type) &&
                !accepts.image.split(", ").includes(file.type)
            ) {
                triggerError(IMAGE_VIDEO_TYPE_NOT_ACCEPTED);
                return;
            }
            if (file.type.split("/")[0] === MediaTypes.Image) {
                props.setImagePreviewUrl(URL.createObjectURL(file));
            }
            return props.setFile(file);
        }
        return;
    };

    const handleDragEnter = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(true);
    };

    const handleDragLeave = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);
    };

    const handleDragOver = e => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleGetThumbnail = () => {
        if (!props.file && !props.fileToEdit) return;
        if (!loadedVideoData || !loadedVideoMetadata || !videoRef.current || props.type !== MediaTypes.Video) return;
        const thumbnail = getVideoThumbnail(videoRef.current);
        if (thumbnail) {
            props.setThumbnail(thumbnail);
            props.setThumbnailUrl(URL.createObjectURL(thumbnail));
        }
    };

    const handleThumbUpload = (e: ChangeEvent<HTMLInputElement>) => {
        const file: File | undefined = e.target.files?.[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                notify("Image is too big!", "Error");
                return;
            }
            if (!accepts.image.split(", ").includes(file.type)) {
                notify(IMAGE_TYPE_NOT_ACCEPTED, "Error");
                return;
            }
            props.setThumbnail(file);
            props.setThumbnailUrl(URL.createObjectURL(file));
        }
    };

    const handleVideoLoadedData = () => {
        setLoadedVideoData(true);
        if (!videoRef || !videoRef.current || !props.onGetVideoDuration) return;

        props.onGetVideoDuration(videoRef.current.duration.toString());
    };

    return (
        <Box sx={props.wrapperSx} className={props.className}>
            {props.file || props.fileToEdit || props?.files?.length ? (
                <>
                    {props.isMultiple && (
                        <>
                            <div
                                id={props.id + "DropZone"}
                                data-testid={props.id + "DropZone"}
                                className={styles.dropBox + " " + (dragging ? styles.dragging : "")}
                                onClick={importFile}
                                onDragEnter={handleDragEnter}
                                onDragExit={handleDragLeave}
                                onDragOver={handleDragOver}
                                onDrop={handleDrop}>
                                <div className={styles.cloudIcon}>
                                    <CloudUploadIcon size={50} />
                                </div>
                                <div className={styles.dropBoxText}>
                                    <span>
                                        Drag and drop your{" "}
                                        {props.type === MediaTypes.ImageOrVideo
                                            ? "image or video"
                                            : String(props.type).toUpperCase()}{" "}
                                        here or
                                        <ButtonComponent type="button" id="browseDevice">
                                            browse from your device
                                        </ButtonComponent>
                                    </span>
                                    <input
                                        hidden
                                        type="file"
                                        accept={
                                            accepts[
                                                props.type === MediaTypes.ImageOrVideo ? "imageOrVideo" : props?.type
                                            ]
                                        }
                                        onChange={e => handleUpdateFile(e)}
                                        ref={inputFilePicker}
                                        className={styles.inputFile}
                                        data-testid="inputFilePicker"
                                    />
                                </div>
                            </div>
                            <div className={styles.supportText}>
                                <span>
                                    {props.customSupportText
                                        ? props.customSupportText
                                        : supportText(props.type, props.type)}
                                </span>
                            </div>
                        </>
                    )}
                    <div className={styles.videoContainer}>
                        {!props.fileId && (
                            <div className="p-2">
                                {(!props.fileToEdit || props?.editBoolean) && (
                                    <ButtonComponent
                                        type="button"
                                        onClick={clearInputs}
                                        className="cpBlueBtn"
                                        id="clearInputs"
                                        tooltip="Choose a different file">
                                        {props.customResetBtn
                                            ? props.customResetBtn
                                            : props.type === MediaTypes.ImageOrVideo
                                            ? "Choose image or video"
                                            : "Choose " + String(props.type).toUpperCase()}
                                    </ButtonComponent>
                                )}
                            </div>
                        )}
                        {props.type === MediaTypes.Video ? (
                            <>
                                {props.fileToEdit ? (
                                    <video
                                        className={styles.video}
                                        src={props.fileToEdit?.url}
                                        controls
                                        muted
                                        onLoadedData={handleVideoLoadedData}
                                        onLoadedMetadata={() => setLoadedVideoMetadata(true)}
                                        ref={videoRef}
                                    />
                                ) : (
                                    <video
                                        className={styles.video}
                                        src={URL.createObjectURL(props.file!)}
                                        controls
                                        muted
                                        onLoadedData={handleVideoLoadedData}
                                        onLoadedMetadata={() => setLoadedVideoMetadata(true)}
                                        ref={videoRef}
                                    />
                                )}
                                <span className={styles.thumbnailInfo}>Choose your thumbnail</span>
                                {!props.fileId && !props.fileToEdit && (
                                    <>
                                        <span className={styles.thumbnailHelper}>
                                            &bull;&nbsp; Slide the video progress button to the image you want to use.
                                            <br />
                                            &bull;&nbsp; Click <strong>From video</strong>.
                                            <br />
                                            The selected video image will appear below. If the selected image isn’t what
                                            you were looking for. Repeat the above steps to select the image.
                                        </span>
                                        <span className={styles.thumbnailOr}>OR</span>
                                    </>
                                )}
                                <span className={styles.thumbnailHelper}>
                                    &bull;&nbsp; Upload an image by clicking <strong>From Workstation.</strong> <br />
                                    &bull;&nbsp; Locate the image you’d like to upload. <br />
                                    {supportText("image", Size.image)}
                                </span>
                                <div className={styles.thumbChooseContainer}>
                                    {!props.fileId && !props.fileToEdit && (
                                        <div className="col-6">
                                            <ButtonComponent
                                                type="button"
                                                id="getThumbFromVideo"
                                                className="cpBlueAltBtn"
                                                onClick={handleGetThumbnail}>
                                                From video
                                            </ButtonComponent>
                                        </div>
                                    )}
                                    <div className={props.fileId || props.fileToEdit ? "col-12" : "col-6"}>
                                        <input
                                            type="file"
                                            hidden
                                            accept={accepts["image"]}
                                            ref={thumbInput}
                                            onChange={handleThumbUpload}
                                        />
                                        <ButtonComponent
                                            type="button"
                                            className="cpBlueAltBtn"
                                            id="getThumbFromFile"
                                            onClick={() => {
                                                thumbInput.current!.click();
                                            }}>
                                            From Workstation
                                        </ButtonComponent>
                                    </div>
                                </div>
                                <div className={styles.thumbnailContainer}>
                                    <span>Selected thumbnail:</span>
                                    <img
                                        alt=""
                                        src={
                                            props.thumbnailUrl ||
                                            props.fileToEdit?.thumbnail_url ||
                                            props.fileToEdit?.thumbnail
                                        }
                                        className={styles.thumbnail}
                                    />
                                </div>
                            </>
                        ) : props.type === MediaTypes.Image ? (
                            <>
                                {props.fileToEdit && !props.hidePreview ? (
                                    <div className={styles.thumbnailContainer}>
                                        <span>Image Selected:</span>
                                        <img alt="" src={(props.fileToEdit as IImage).src} />
                                    </div>
                                ) : props?.isMultiple ? (
                                    props.files?.length && (
                                        <div className="row">
                                            {props.files?.map((image, idx) => (
                                                <div key={idx} className="col-md-4 g-2">
                                                    <div key={image} className={styles.thumbnailContainer}>
                                                        <span className="d-flex justify-content-end">
                                                            <XIcon size={20} />
                                                        </span>
                                                        <img alt="" src={URL.createObjectURL(image)} />
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )
                                ) : (
                                    props.file &&
                                    !props.hidePreview && (
                                        <div className={styles.thumbnailContainer}>
                                            <span>Image Selected:</span>
                                            <img alt="" src={props.imagePreviewUrl} />
                                        </div>
                                    )
                                )}
                            </>
                        ) : (
                            <SubtitleContainer
                                as="span"
                                text={`Selected ${String(props.type).toUpperCase()}: ${
                                    props.file?.name || props.fileToEdit?.name
                                }`}
                            />
                        )}
                    </div>
                </>
            ) : (
                <>
                    <div
                        id={props.id + "DropZone"}
                        data-testid={props.id + "DropZone"}
                        className={styles.dropBox + " " + (dragging ? styles.dragging : "")}
                        onClick={importFile}
                        onDragEnter={handleDragEnter}
                        onDragExit={handleDragLeave}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                        {...(props.dropZoneProps || {})}>
                        {props?.icon || (
                            <div className={styles.cloudIcon}>
                                <CloudUploadIcon size={50} />
                            </div>
                        )}
                        <div className={styles.dropBoxText} style={{padding: "8px"}}>
                            <div
                                style={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    color: theme.palette.blue[800],
                                    fontWeight: 400,
                                    lineHeight: "20px",
                                }}>
                                Drag and drop your{" "}
                                {props.type === MediaTypes.ImageOrVideo
                                    ? "image or video"
                                    : String(props.type).toUpperCase()}{" "}
                                here or
                                <span>
                                    <ButtonComponent
                                        type="button"
                                        variant="text"
                                        id="browseDevice"
                                        style={{
                                            textDecoration: "none",
                                        }}
                                        disableTrack>
                                        <Typography
                                            fontInter
                                            variant="body3"
                                            sx={{
                                                color: theme.palette.blue[600],
                                                fontWeight: 600,
                                            }}>
                                            browse from your device
                                        </Typography>
                                    </ButtonComponent>
                                </span>
                            </div>
                            <input
                                hidden
                                type="file"
                                accept={accepts[props.type]}
                                onChange={e => handleUpdateFile(e)}
                                ref={inputFilePicker}
                                className={styles.inputFile}
                                multiple
                                data-testid="inputFilePicker"
                            />
                        </div>
                    </div>
                    {!props.noSupportText && (
                        <div className={styles.supportText}>
                            <span>
                                {props.customSupportText
                                    ? props.customSupportText
                                    : supportText(props.type, props.maxSize)}
                            </span>
                        </div>
                    )}
                </>
            )}
        </Box>
    );
}
