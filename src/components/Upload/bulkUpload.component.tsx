import {SxProps, Theme, useTheme} from "@mui/material";
import {useEffect, useRef, useState} from "react";
import styles from "../../styles/components/uploadComponent.module.sass";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ButtonComponent from "../FormControls/button.component";
import {CloudUploadIcon} from "../Icons";
import {SelectedFileStatus} from "../../enums/fileUpload.enum";
import {
    TAcceptedFileFormatsAndSize,
    TFileAditionalParams,
    TFileWithErrorList,
    TSelectedFile,
    TSelectedFileList,
} from "../../Interfaces/fileUpload.interface";
import {TFileUploadMode} from "../../uicomponents/Organism/FileUploadModal/fileUploadModal.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import {generateSxFromProps} from "../../utils/sx.util";
import useNotification from "../../hooks/useNotification";
import {getValidFileTree, getErrorFileTree, fileUniqID} from "../../utils/fileUpload.util";

interface IDropZoneProps {
    style?: React.CSSProperties | undefined;
}
interface IBulkUploadComponentProps {
    id: string;
    onFilesUpdated: (files: TSelectedFileList) => void;
    onFilesWithErrorsUpdated: (files: TFileWithErrorList) => void;
    maxSize: number;
    maxFileName?: number;
    accepts: TAcceptedFileFormatsAndSize[];
    multiple?: boolean;
    uploadMode?: TFileUploadMode;
    maxFolderDepth?: number;
    flatten?: boolean;
    fileIdPrefix?: string;
    folderIdPrefix?: string;
    aditionalParams?: TFileAditionalParams;
    filterAditionalParams?: (file: TSelectedFile, params: TFileAditionalParams) => TFileAditionalParams;
    context: string;
    sx?: SxProps<Theme>;
    icon?: React.ReactNode;
    dropZoneProps?: IDropZoneProps;
    onError?: (message: string | undefined) => void;
}

/**
 * Component for bulk file upload with drag-and-drop functionality.
 * @param id - Unique identifier for the component.
 * @param onFilesUpdated - Callback function invoked when files are updated.
 * @param onFilesWithErrorsUpdated - Callback function invoked when files with errors are updated.
 * @param maxSize - Maximum file size allowed in MB.
 * @param maxFileName - Maximum file name length
 * @param accepts - Array of acceptable file formats and sizes.
 * @param uploadMode - Specifies upload mode: 'files', 'folders', or 'filesAndFolders'.
 * @param maxFolderDepth - Maximum levels of folder/subfolder depth allowed.
 * @param flatten - Determines if the file list should be flattened or structured as a tree.
 * @param fileIdPrefix - Prefix for generating unique file IDs.
 * @param folderIdPrefix - Prefix for generating unique folder IDs.
 * @param aditionalParams - Additional parameters sent with each file upload.
 * @param filterAditionalParams - A function to filter aditional params based on the file type.
 * @param context - Context for handling files on the backend (BE).
 * @param sx - Style object for customizing component styling (MUI's SxProps).
 * @param icon - Custom icon or element displayed in the upload area.
 * @param dropZoneProps - Additional props for configuring the drop zone area.
 * @param onError - Callback function invoked when an error occurs during upload.
 */
export default function BulkUploadComponent({
    onFilesUpdated = () => {},
    onFilesWithErrorsUpdated = () => {},
    maxSize = 1,
    maxFileName = 50,
    accepts = [],
    multiple = true,
    uploadMode = "files",
    maxFolderDepth = 1,
    flatten = false,
    fileIdPrefix = "f_",
    folderIdPrefix = "d_",
    aditionalParams,
    filterAditionalParams,
    context,
    ...props
}: IBulkUploadComponentProps) {
    const [dragging, setDragging] = useState(false);
    const notify = useNotification();
    const inputFilePicker = useRef<HTMLInputElement | null>(null);
    const theme = useTheme();
    const acceptFolders: boolean = ["folders", "filesAndFolders"].includes(uploadMode);
    const acceptFiles: boolean = ["files", "filesAndFolders"].includes(uploadMode);

    const acceptableMediaTypes = accepts.map(rule => {
        const prefix = !!rule.prefix ? `${rule.prefix}/` : "";
        return {
            type: rule.type,
            formats: rule.formats.map(format => `${prefix.toLowerCase()}${format.toLowerCase()}`),
            size: rule.size,
        };
    });
    const acceptableExtensions = acceptFiles
        ? accepts.map(rule => rule.formats.map(format => `.${format.toLowerCase()}`).join(",")).join(",")
        : "";

    const [fileTree, setFileTree] = useState<TSelectedFileList>([]);
    const [errorsTree, setErrorsTree] = useState<TFileWithErrorList>([]);

    const getFileExtension = (fileName: string) => (fileName.match(/\.([^.]+)$/)?.[1] ?? "").toLowerCase();

    const importFile = () => {
        if (!inputFilePicker.current) return;
        inputFilePicker.current.click();
    };

    const getFileMediaType = (file: File) => {
        const extension = getFileExtension(file.name);
        return acceptableMediaTypes?.find(rule => rule.formats.includes(file.type) || rule.formats.includes(extension));
    };

    const validateFileSize = (file: File): boolean => {
        const fileSize = file.size;
        const maxFileSize = getFileMediaType(file)?.size ?? maxSize;
        const maxSizeInMb = maxFileSize * 1024 * 1024;
        return fileSize <= maxSizeInMb;
    };
    const validateFileType = (file: File): boolean => {
        return !!getFileMediaType(file);
    };

    const validateFileName = (fileName: string): boolean => {
        console.info("Validating File Name: ", fileName);
        return fileName.length <= maxFileName;
    };

    const validateFile = (file: File, isDirectory: boolean): {status: SelectedFileStatus; statusMessage: string} => {
        if (!isDirectory && !validateFileSize(file)) {
            return {
                status: SelectedFileStatus.ERROR,
                statusMessage: `The file size exceeds the maximum allowed size of ${maxFileName}Mb.`,
            };
        }

        if (!isDirectory && !validateFileType(file)) {
            return {
                status: SelectedFileStatus.ERROR,
                statusMessage: "Invalid file format",
            };
        }

        if (!validateFileName(file.name)) {
            return {
                status: SelectedFileStatus.ERROR,
                statusMessage: `The file name exceeds the maximum allowed length of ${maxFileName} characters.`,
            };
        }

        return {
            status: file.name != "" ? SelectedFileStatus.VALID : SelectedFileStatus.ERROR,
            statusMessage: "",
        };
    };

    const makeSelectedFileObj = async (
        file: File,
        relativePath: string,
        isDirectory: boolean = false,
    ): Promise<TSelectedFile> =>
        new Promise(resolve => {
            const {status, statusMessage} = validateFile(file, isDirectory);
            const fileType = isDirectory ? "folder" : getFileMediaType(file)?.type ?? null;
            fileUniqID(file.name, fileType ?? "", relativePath, fileIdPrefix).then((id: string) => {
                const temporaryFileObj: TSelectedFile = {
                    id,
                    fileName: file.name,
                    fileType,
                    fileSize: file.size ?? 0,
                    status,
                    statusMessage,
                    extension: getFileExtension(file.name),
                    file: status === SelectedFileStatus.ERROR ? undefined : file,
                    isDirectory,
                    relativePath: !!relativePath.trim().length ? relativePath : undefined,
                    context,
                };
                const params = !!filterAditionalParams
                    ? filterAditionalParams(temporaryFileObj, aditionalParams ?? {})
                    : aditionalParams;

                resolve({
                    ...temporaryFileObj,
                    ...(!!params ? {aditionalParams: params} : {}),
                });
            });
        });

    const makeFolderObject = async (
        name: string,
        content: TSelectedFileList,
        context: string,
        status?: SelectedFileStatus,
        relativePath?: string,
    ): Promise<TSelectedFile> => ({
        id: await fileUniqID(name, "folder", relativePath ?? "", folderIdPrefix),
        fileName: name,
        fileType: "folder",
        status: status ?? SelectedFileStatus.VALID,
        statusMessage: "",
        extension: "",
        file: undefined,
        fileSize: 0,
        context,
        isDirectory: true,
        relativePath: relativePath ?? "",
        content: content,
    });

    // Reads a file from the input and returns its object representation.
    const readFileFromEntry = async (entry: any, relativePath: string = "") =>
        new Promise<TSelectedFile>(resolve => {
            if (!entry.file) {
                makeSelectedFileObj(entry, relativePath, !!entry.isDirectory).then(file => resolve(file));
            } else {
                entry.file(function (file) {
                    makeSelectedFileObj(file, relativePath, !!entry.isDirectory).then(file => resolve(file));
                });
            }
        });

    const getFileTreeFromEntry = async (
        item: any,
        relativePath: string = "",
        depth: number = 1,
    ): Promise<TSelectedFileList | null> =>
        new Promise(resolve => {
            if (!!item.isDirectory) {
                if (depth > maxFolderDepth || !acceptFolders) {
                    readFileFromEntry(item, relativePath).then(file => {
                        file.status = SelectedFileStatus.ERROR;
                        file.statusMessage =
                            maxFolderDepth > 1
                                ? "Subdirectories with more than N levels are not allowed."
                                : "Subdirectories are not allowed.";
                        resolve([file]);
                    });
                } else {
                    const directoryReader = item.createReader();
                    directoryReader.readEntries(function (entries) {
                        const path = `${relativePath}${item.name}/`;
                        const promises = entries.map(entry => getFileTreeFromEntry(entry, path, depth + 1));
                        Promise.all(promises).then(async files => {
                            const folder = await makeFolderObject(item.name, files.flat(), context);
                            resolve(flatten ? files.flat() : [folder]);
                        });
                    });
                }
            } else {
                readFileFromEntry(item, relativePath).then(file => resolve([file]));
            }
        });

    const updateFileTree = async (files: TSelectedFileList) => {
        const invalidFiles = getErrorFileTree(files);
        const validFiles = getValidFileTree(files);
        setFileTree(prev => [...prev, ...validFiles]);
        setErrorsTree(prev => [...prev, ...invalidFiles]);
    };
    /**
     * Handles files obtained via drag-and-drop
     */
    const handleDroppedFiles = async (items: DataTransferItem[]) => {
        setFileTree([]);
        setErrorsTree([]);
        try {
            items.forEach(async item => {
                const entry = item.webkitGetAsEntry();
                await getFileTreeFromEntry(entry).then(files => {
                    if (!!files) updateFileTree(files);
                });
            });
        } catch (error) {
            notify("Error reading files", "Error");
        }
    };
    const handleDrop = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);

        handleDroppedFiles(Array.from(e.dataTransfer?.items ?? []));
    };

    /**
     * Handles files loaded from the computer via input.
     */
    const getParentFolder = (entry: any): string => {
        const relativePath = (entry?.webkitRelativePath ?? "").match(/([^/]+)\/[^/]+\.[^.]+$/) ?? undefined;
        if (!relativePath) return "Unknown Folder";
        return relativePath[1];
    };

    const handleLoadedFiles = async (items: any[]) => {
        setFileTree([]);
        setErrorsTree([]);
        try {
            if (uploadMode === "folders") {
                // Making parent folder for loaded files
                const parentFolderName = getParentFolder(items[0]);
                const parentFolder = await makeFolderObject(parentFolderName, [], context);
                // Reading file tree from entries
                const filesPromises = items.map(async entry => {
                    const files = await getFileTreeFromEntry(entry, `${parentFolderName}/`);
                    return files ?? [];
                });
                const filesArray = await Promise.all(filesPromises);
                // Updating parent folder and file tree
                parentFolder.content = filesArray.flat();
                updateFileTree([parentFolder]);
            } else {
                items.forEach(async entry => {
                    await getFileTreeFromEntry(entry).then(files => {
                        if (!!files) updateFileTree(files);
                    });
                });
            }
        } catch (error) {
            notify("Error reading files", "Error");
        }
    };

    const handleLoad = e => {
        handleLoadedFiles(Array.from(e.target?.files ?? []));
    };

    useEffect(() => {
        onFilesUpdated(fileTree);
    }, [fileTree]);

    useEffect(() => {
        onFilesWithErrorsUpdated(errorsTree);
    }, [errorsTree]);

    const bulkUploadAreaRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        if (!bulkUploadAreaRef.current || !!inputFilePicker.current) return;
        const input = document.createElement("input");
        input.type = "file";
        input.id = "bulk-upload-file-input";
        input.name = "bulk-upload-file-input";
        input.webkitdirectory = uploadMode === "folders";
        input.multiple = multiple;
        input.accept = acceptableExtensions;
        input.className = styles.inputFile;
        input.onchange = handleLoad.bind(input);

        inputFilePicker.current = input;

        (bulkUploadAreaRef.current as HTMLDivElement).appendChild(input);
        return () => {
            if (inputFilePicker.current) {
                inputFilePicker.current.removeEventListener("change", handleLoad);
            }
        };
    }, [bulkUploadAreaRef]);

    const handleDragEnter = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(true);
    };

    const handleDragLeave = e => {
        e.preventDefault();
        e.stopPropagation();
        setDragging(false);
    };

    const handleDragOver = e => {
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <Box sx={generateSxFromProps(props.sx ?? {}, theme)}>
            <div
                id={props.id + "DropZone"}
                className={styles.dropBox + " " + (dragging ? styles.dragging : "")}
                onClick={importFile}
                onDragEnter={handleDragEnter}
                onDragExit={handleDragLeave}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                {...(props.dropZoneProps || {})}>
                {props?.icon || (
                    <div className={styles.cloudIcon}>
                        <CloudUploadIcon size={50} />
                    </div>
                )}
                <div className={styles.dropBoxText} style={{padding: "8px"}} ref={bulkUploadAreaRef}>
                    <div
                        style={{
                            display: "flex",
                            flexWrap: "wrap",
                            alignItems: "center",
                            justifyContent: "center",
                            color: theme.palette.blue[800],
                            fontWeight: 400,
                            lineHeight: "20px",
                        }}>
                        Drag and drop your files here or
                        <span>
                            <ButtonComponent
                                type="button"
                                variant="text"
                                id="browseDevice"
                                style={{
                                    textDecoration: "none",
                                }}
                                disableTrack>
                                <Typography
                                    fontInter
                                    variant="body3"
                                    sx={{
                                        color: theme.palette.blue[600],
                                        fontWeight: 600,
                                    }}>
                                    browse from your device
                                </Typography>
                            </ButtonComponent>
                        </span>
                    </div>
                </div>
            </div>
        </Box>
    );
}
