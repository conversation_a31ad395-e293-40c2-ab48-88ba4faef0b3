import {UseQueryResult} from "@tanstack/react-query";
import {DateTime} from "luxon";
import {useCallback, useRef, useState} from "react";
import {OverlayTrigger} from "react-bootstrap";
import {OverlayInjectedProps} from "react-bootstrap/esm/OverlayTrigger";
import {Calendar, OnArgs, OnClickFunc, TileContentFunc} from "react-calendar";
import {ICalendarEventsAPI, IEvent} from "../../Interfaces/event.interface";
import styles from "../../styles/components/calendar.module.sass";
import {DATE_TIME_FORMAT, formatDate} from "../../utils/formatDate";
import Loader from "../../utils/loader";
import "./calendar.reset.sass";

interface ITooltipProps extends OverlayInjectedProps {
    e?: IEvent;
    eArr?: IEvent[];
}

interface IProps {
    events: UseQueryResult<ICalendarEventsAPI, unknown>;
    className?: string;
    onSelectDay?: (date: DateTime) => void;
    onSelectMonth?: (date: DateTime) => void;
    isLoading?: boolean;
    defaultSelectedDay?: Date;
}

export default function CalendarCP(props: IProps) {
    const selectedDay = useRef<DateTime | null>(null);
    const selectedMonth = useRef<DateTime | null>(null);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(props.defaultSelectedDay ?? undefined);

    const handleChangeDay = (date: Date) => {
        // if (!checkIfLoggedIn()) return;
        selectedDay.current = DateTime.fromJSDate(date);
        props.onSelectDay?.(DateTime.fromJSDate(date));
        setSelectedDate(date);
    };

    const handleChangeMonthClick: OnClickFunc = date => {
        if (date) {
            props.onSelectMonth?.(DateTime.fromJSDate(date));
            selectedMonth.current = DateTime.fromJSDate(date);
            setSelectedDate(undefined);
        }
    };

    const handleChangeMonth = (data: OnArgs) => {
        if (data.activeStartDate) {
            props.onSelectMonth?.(DateTime.fromJSDate(data.activeStartDate));
            selectedMonth.current = DateTime.fromJSDate(data.activeStartDate);
            setSelectedDate(undefined);
            return;
        }
    };

    const renderTooltip = ({e, eArr, ...rest}: ITooltipProps) => {
        // if (!checkIfLoggedIn()) return <div />;
        if (!!e) {
            const modifiedStartDate = e.start_date;

            return (
                <div key={e.id} className={styles.eventContainer} {...rest}>
                    <h4>{e.event_name?.length > 20 ? `${e.event_name.substring(0, 17)}...` : e.event_name}</h4>
                    <p>{formatDate(modifiedStartDate, DATE_TIME_FORMAT)}</p>
                </div>
            );
        }
        if (!!eArr && eArr.length) {
            return (
                <div className={styles.eventContainer} {...rest}>
                    {eArr.map(evt => {
                        return (
                            <div key={evt.id} className="my-2">
                                <h4 className="fs-16">{evt.event_name}</h4>
                                <p className="fs-14">{formatDate(evt.start_date, DATE_TIME_FORMAT)}</p>
                            </div>
                        );
                    })}
                </div>
            );
        }
    };

    const renderEvents: TileContentFunc = useCallback(
        ({date, view}) => {
            const eventsToRender = props.events.data?.[
                selectedMonth?.current?.year.toString() ?? new Date().getFullYear().toString()
            ]?.[selectedMonth?.current?.month.toString() ?? (new Date().getMonth() + 1).toString()]?.filter(e => {
                const eventDate = DateTime.fromISO(e.start_date);
                const has_event =
                    view === "month" &&
                    eventDate.hasSame(DateTime.fromJSDate(date), "day") &&
                    eventDate.hasSame(DateTime.fromJSDate(date), "month") &&
                    eventDate.hasSame(DateTime.fromJSDate(date), "year");
                return has_event;
            });
            return (
                <>
                    {
                        /* Only bind loader to the first day tile so we do not have an overlap */
                        date.getDay() === 1 && props.events.isLoading && (
                            <div className={styles.loaderContainer}>
                                <Loader fullComponent loading />
                            </div>
                        )
                    }
                    <div className="d-flex gap-2 align-items-top justify-content-center" style={{overflow: "hidden"}}>
                        {eventsToRender?.slice(0, 3)?.map(e => (
                            <OverlayTrigger
                                placement="bottom"
                                delay={{show: 250, hide: 400}}
                                key={e.id}
                                overlay={props => renderTooltip({...props, e})}>
                                <div
                                    className={styles.eventDot}
                                    style={{
                                        backgroundColor: e.cp_event
                                            ? "var(--AccentColor)"
                                            : e?.in_person_or_virtual === "virtual" ||
                                              e.in_person_or_virtual === "webinar" ||
                                              e.in_person_or_virtual === "virtual_product_demo"
                                            ? "var(--cpGreen)"
                                            : "var(--cpNavy)",
                                        overflow: "hidden",
                                    }}
                                />
                            </OverlayTrigger>
                        ))}
                        {eventsToRender && eventsToRender?.length > 3 && (
                            <OverlayTrigger
                                placement="bottom"
                                delay={{show: 250, hide: 400}}
                                key={eventsToRender?.[3]?.id}
                                overlay={props => renderTooltip({...props, eArr: eventsToRender?.slice(3)})}>
                                {eventsToRender?.length > 4 ? (
                                    <div className="auto">
                                        <span className="fs-14">+{eventsToRender.length - 3}</span>
                                    </div>
                                ) : (
                                    <div
                                        className={styles.eventDot}
                                        style={{
                                            backgroundColor: eventsToRender?.[3].cp_event
                                                ? "var(--AccentColor)"
                                                : eventsToRender?.[3]?.subject_id
                                                ? "var(--cpGreen)"
                                                : "var(--cpNavy)",
                                        }}
                                    />
                                )}
                            </OverlayTrigger>
                        )}
                    </div>
                </>
            );
        },

        [props.events, selectedMonth.current, selectedDay.current],
    );

    return (
        <div className={props.className}>
            <Calendar
                tileContent={renderEvents}
                tileClassName={`${styles.tileContainer} no-tracking`}
                className={styles.calendarContainer}
                onClickDay={handleChangeDay}
                next2Label={null}
                prev2Label={null}
                minDetail="year"
                onActiveStartDateChange={handleChangeMonth}
                onClickMonth={handleChangeMonthClick}
                value={selectedDate || null}
                calendarType="gregory"
            />
        </div>
    );
}
