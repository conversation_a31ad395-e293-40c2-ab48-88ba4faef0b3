import {IPlanTypes} from "../../Interfaces/profiles.interface";
import {Card, CardContent, Grid} from "@mui/material";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";

interface IPlanTypesForm {
    planType: IPlanTypes;
    selected: boolean;
    onSelectPlan: Function;
    loading: boolean;
}
export function PlanTypesForm(props: IPlanTypesForm) {
    return (
        <Grid item xs={12} sm={12} md={4}>
            <Card sx={{minWidth: 180, height: "100%", borderRadius: "8px"}}>
                <CardContent
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        flexDirection: "column",
                        backgroundColor: "#F5F6F9",
                    }}>
                    <Typography textAlign={"center"} variant={"subtitle2"}>
                        {props.planType.label}
                    </Typography>
                    <MuiButtonComponent
                        size={"xs"}
                        color={props?.selected ? "blue" : "grey"}
                        style={{marginTop: "20px", color: props?.selected ? "White" : "#0B2656"}}
                        onClick={() => props.onSelectPlan(props.planType)}
                        id="submit"
                        type="button"
                        disabled={props?.loading}>
                        Select
                    </MuiButtonComponent>
                </CardContent>
            </Card>
        </Grid>
    );
}
