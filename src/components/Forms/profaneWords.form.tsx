import BrandConnector from "../Logo/brandConnector.component";
import Logo from "../Logo/logo.component";

export const ProfaneWordsForm = () => {
    return (
        <div className="p-relative">
            <div className="dottedBg" style={{backgroundImage: 'url("/Media/backgrounds/dot_bg.jpg")'}}>
                <Logo version="verticalColor" />
            </div>
            <BrandConnector className="profaneConnector" size="150" color="Navy" />
            <div className="text-center mt-4 textBg">
                <p className="p-2">Let's keep it clean and positive! Remember that many of your</p>
                <p className="p-2">colleagues and professionals in the IT Channel will be able to see your </p>
                <p className="p-2">comments. Let's clean it up before you save.</p>
            </div>
        </div>
    );
};
