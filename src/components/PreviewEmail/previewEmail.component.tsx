import {RichTextSanitizer} from "../../utils/miscellaneous";
import Autolinker from "autolinker";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import OutboundLink from "../Links/OutboundLink.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";

interface IProps {
    subject: string;
    header_text: string;
    intro_text: string;
    footer_text: string;
    is_customer_email?: boolean;
    email_header?: string;
}

export default function PreviewEmailComponent({
    subject,
    header_text,
    intro_text,
    footer_text,
    is_customer_email = false,
    email_header,
}: IProps) {
    // ALL THE STYLES ARE INLINE TO SIMULATE THE SAME BEHAVIOUR OF THE EMAIL
    // DO NOT REMOVE THEM. IF YOU NEED TO MODIFY THEM, DO IT INLINE
    return (
        <div className="preview-email-container">
            <Typography size={16} color={"neutral.800"} weight={700}>
                Preview
            </Typography>
            <div className="d-flex justify-content-start mt-4 mb-4">
                <SubtitleContainer as="p">
                    <strong>Subject:</strong> {subject}
                </SubtitleContainer>
            </div>
            <div
                style={{
                    boxSizing: "border-box",
                    position: "relative",
                    backgroundColor: "var(--cpGrayBg6)",
                    color: "var(--cpTextGray)",
                    lineHeight: "1.4",
                    padding: "10px",
                }}>
                <table
                    role="presentation"
                    className="header"
                    cellSpacing="0"
                    cellPadding="0"
                    style={{
                        position: "relative",
                        boxSizing: "border-box",
                        borderRadius: "2px",
                        borderWidth: "1px",
                        margin: "0 auto",
                        padding: !email_header ? "6px 15px" : "",
                        height: "auto",
                        backgroundColor: "#FFFFFF",
                        paddingBottom: !email_header ? "16px" : "0",
                        justifyContent: "center",
                        marginBottom: "1px",
                    }}>
                    <tbody>
                        <tr>
                            <td
                                style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    margin: "auto",
                                    paddingTop: !email_header ? "16px" : "0",
                                }}>
                                {!email_header && !is_customer_email ? (
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="184.767"
                                        height="49.995"
                                        viewBox="0 0 184.767 49.995">
                                        <g id="Grupo_2329" data-name="Grupo 2329" transform="translate(0 0)">
                                            <path
                                                id="Trazado_14"
                                                data-name="Trazado 14"
                                                d="M58.038,40.479a23.068,23.068,0,1,0,0,4.357H46.094a11.142,11.142,0,1,1,0-4.357Z"
                                                transform="translate(-12.36 -19.59)"
                                                fill="#ff6120"></path>
                                            <path
                                                id="Trazado_15"
                                                data-name="Trazado 15"
                                                d="M312.369,112.74a5.939,5.939,0,0,0-5.245,2.965,4.885,4.885,0,0,0-4.762-2.965,5.36,5.36,0,0,0-4.647,2.423v-2.08h-4.1V128.76h4.1l-.028-8.238c0-2.479,1.14-3.905,3.279-3.905,1.968,0,2.709,1.112,2.709,3.108v9.038h4.077v-8.238c0-2.479,1.112-3.905,3.249-3.905,1.968,0,2.709,1.112,2.709,3.108v9.038h4.077v-9.919c0-3.8-2.083-6.107-5.419-6.107m-28.862,12.488a4.319,4.319,0,0,1,0-8.608,4.314,4.314,0,0,1,0,8.608m4.021-12.146v2.2a5.894,5.894,0,0,0-4.961-2.538c-4.02,0-7.126,3.335-7.126,8.181,0,4.818,3.049,8.181,7.154,8.181a5.824,5.824,0,0,0,4.9-2.451v2.109h4.077l.028-15.678ZM273.4,112.74a5.093,5.093,0,0,0-4.9,3.077V113.08h-4.077v15.678H268.5v-6.985c0-3.706,2.224-4.846,4.818-4.846a9.107,9.107,0,0,1,1,.056v-4.189c-.258-.022-.542-.053-.913-.053M254.315,125a4.194,4.194,0,1,1,4.021-4.189A3.958,3.958,0,0,1,254.315,125m4.021-11.916v2.167a5.892,5.892,0,0,0-4.933-2.507c-4.049,0-7.154,3.335-7.154,8.066,0,4.7,3.049,8.066,7.154,8.066a5.919,5.919,0,0,0,4.933-2.451v2.252c0,2.65-1.311,3.933-3.79,3.933-1.682,0-3.164-.626-3.622-2.395H246.7c.6,4.1,4.161,6.014,7.954,6.014,4.731,0,7.755-3.021,7.755-7.668V113.083Zm-21.509,3.535a4.312,4.312,0,1,1-4.049,4.3,4.042,4.042,0,0,1,4.049-4.3m0-3.877a8.181,8.181,0,1,0,8.181,8.181,8.033,8.033,0,0,0-8.181-8.181m-9.392,0a5.093,5.093,0,0,0-4.9,3.077V113.08h-4.077v15.678h4.077v-6.985c0-3.706,2.224-4.846,4.818-4.846a9.107,9.107,0,0,1,1,.056v-4.189c-.258-.022-.542-.053-.912-.053m-18.688,12.488a4.319,4.319,0,1,1,3.962-4.3,4,4,0,0,1-3.962,4.3m.941-12.488a5.924,5.924,0,0,0-4.961,2.538v-2.2H200.65v22.8h4.077v-9.237a5.934,5.934,0,0,0,4.961,2.451c4.077,0,7.154-3.364,7.154-8.181,0-4.843-3.133-8.179-7.154-8.179"
                                                transform="translate(-146.481 -86.233)"
                                                fill="#0c2556"></path>
                                            <path
                                                id="Trazado_16"
                                                data-name="Trazado 16"
                                                d="M301.536,41.83h4.077V19.88h-4.077Zm-9.692-12.657a3.651,3.651,0,0,1,3.706,2.878h-7.5a3.8,3.8,0,0,1,3.79-2.878m.028-3.364a7.929,7.929,0,0,0-8.182,8.21,7.905,7.905,0,0,0,8.266,8.153c3.364,0,6.5-1.626,7.668-5.332H295.4a3.624,3.624,0,0,1-3.479,1.968,3.888,3.888,0,0,1-4.077-3.762H299.88c.4-5.16-2.819-9.237-8.007-9.237m-15.448,0a5.632,5.632,0,0,0-4.874,2.479V26.149h-4.077V41.827h4.077V33.59c0-2.566,1.227-3.905,3.535-3.905,2.167,0,2.937,1.14,2.937,3.192v8.951H282.1V31.995c0-3.849-2.139-6.185-5.674-6.185m-16.593,0a5.632,5.632,0,0,0-4.874,2.479V26.149h-4.077V41.827h4.077V33.59c0-2.566,1.227-3.905,3.535-3.905,2.168,0,2.937,1.14,2.937,3.192v8.951h4.077V31.995c0-3.849-2.139-6.185-5.674-6.185M240.678,38.3a3.976,3.976,0,0,1-3.962-4.3,3.976,3.976,0,0,1,3.962-4.3,4.02,4.02,0,0,1,4.021,4.3,4.006,4.006,0,0,1-4.021,4.3M244.7,26.149v2.2a5.894,5.894,0,0,0-4.961-2.538c-4.021,0-7.126,3.335-7.126,8.181,0,4.818,3.049,8.181,7.154,8.181a5.82,5.82,0,0,0,4.9-2.451v2.109h4.077l.028-15.678H244.7Zm-19.328-.34a5.632,5.632,0,0,0-4.874,2.479V19.88h-4.077V41.83h4.077V33.59c0-2.566,1.227-3.905,3.507-3.905,2.137,0,2.937,1.14,2.937,3.192v8.951h4.077V31.995c0-3.849-2.139-6.185-5.646-6.185M206.983,42.173c3.762,0,7.213-2.2,7.839-6.328h-4.192a3.571,3.571,0,0,1-3.65,2.566c-2.395,0-4.049-1.8-4.021-4.419-.028-2.594,1.567-4.419,4.049-4.419a3.529,3.529,0,0,1,3.619,2.538h4.192c-.6-3.992-3.99-6.3-7.808-6.3a7.973,7.973,0,0,0-8.181,8.181,7.965,7.965,0,0,0,8.153,8.181"
                                                transform="translate(-145.172 -19.799)"
                                                fill="#0c2556"></path>
                                            <path
                                                id="Trazado_527"
                                                data-name="Trazado 527"
                                                d="M2.24,0h1.2V-5.59H5.48v-.99H.2v.99H2.24ZM6.32,0H7.45L7.43-5.64h.02L9.46,0h1l2.01-5.64h.02L12.46,0h1.2V-6.58H11.79L10.02-1.61H10L8.24-6.58H6.32Z"
                                                transform="translate(171.107 8.58)"
                                                fill="#0c2556"></path>
                                        </g>
                                    </svg>
                                ) : (
                                    <img
                                        src={email_header ?? "/Media/bettertracker_new/<EMAIL>"}
                                        style={{
                                            width: "100%",
                                            height: "100%",
                                            maxHeight: "80px",
                                            objectFit: "cover",
                                        }}
                                    />
                                )}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <table
                    role="presentation"
                    className="header"
                    cellSpacing="0"
                    cellPadding="0"
                    style={{
                        position: "relative",
                        boxSizing: "border-box",
                        borderRadius: "2px",
                        borderWidth: "1px",
                        margin: "0 auto",
                        padding: "20px",
                        height: "auto",
                        backgroundColor: "#FFFFFF",
                        paddingBottom: "16px",
                        justifyContent: "center",
                        marginBottom: "1px",
                    }}>
                    <tbody>
                        <tr>
                            <td>
                                <div
                                    className="d-flex align-content-center undefined"
                                    style={{
                                        width: "36px",
                                        height: "12.8571px",
                                        margin: "auto",
                                    }}></div>
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    margin: "auto",
                                    paddingTop: "16px",
                                }}>
                                <div
                                    className="text mb-0"
                                    dangerouslySetInnerHTML={RichTextSanitizer(Autolinker.link(header_text))}
                                />
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    margin: "auto",
                                    paddingTop: "16px",
                                }}>
                                <div
                                    className="text mb-0"
                                    dangerouslySetInnerHTML={RichTextSanitizer(Autolinker.link(intro_text))}
                                />
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    margin: "auto",
                                    paddingTop: "16px",
                                    textAlign: "center",
                                    fontSize: "24px",
                                    color: "var(--cpOrange)",
                                }}>
                                NON EDITABLE CONTENT GOES HERE
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    margin: "auto",
                                    paddingTop: "16px",
                                }}>
                                <div
                                    className="text mb-0"
                                    dangerouslySetInnerHTML={RichTextSanitizer(Autolinker.link(footer_text))}
                                />
                            </td>
                        </tr>
                    </tbody>
                </table>
                {!is_customer_email && (
                    <table
                        role="presentation"
                        className="footer"
                        cellSpacing="0"
                        cellPadding="0"
                        style={{
                            position: "relative",
                            boxSizing: "border-box",
                            borderRadius: "2px",
                            borderWidth: "1px",
                            margin: "0 auto",
                            padding: "20px",
                            width: "100%",
                            height: "auto",
                            background: "var(--cpNavyShade3) no-repeat padding-box",
                            paddingBottom: "16px",
                            justifyContent: "center",
                            marginBottom: "1px",
                        }}>
                        <tbody>
                            <tr>
                                <td
                                    style={{
                                        boxSizing: "border-box",
                                        position: "relative",
                                        margin: "auto",
                                        textAlign: "center",
                                        padding: "27px 0 15px 0",
                                        background: "var(--cpNavy) no-repeat padding-box",
                                        color: "#FFFFFF",
                                    }}>
                                    <OutboundLink
                                        href="https://www.facebook.com/Channel-Program-113031624351535"
                                        style={{
                                            border: "none",
                                            background: "var(--cpNavyShade3)",
                                            padding: "16px",
                                            textAlign: "center",
                                            textDecoration: "none",
                                            display: "inline-block",
                                            fontSize: "18px",
                                            borderRadius: "80%",
                                            boxSizing: "border-box",
                                            position: "relative",
                                            margin: "8px",
                                            color: "var(--cpGrayShade5)",
                                        }}>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="#FFFFFF">
                                            <path d="M13.397 20.997v-8.196h2.765l.411-3.209h-3.176V7.548c0-.926.258-1.56 1.587-1.56h1.684V3.127A22.336 22.336 0 0 0 14.201 3c-2.444 0-4.122 1.492-4.122 4.231v2.355H7.332v3.209h2.753v8.202h3.312z"></path>
                                        </svg>
                                    </OutboundLink>
                                    <OutboundLink
                                        href="https://twitter.com/bethechannel"
                                        style={{
                                            border: "none",
                                            background: "var(--cpNavyShade3)",
                                            padding: "16px",
                                            textAlign: "center",
                                            textDecoration: "none",
                                            display: "inline-block",
                                            fontSize: "18px",
                                            borderRadius: "80%",
                                            boxSizing: "border-box",
                                            position: "relative",
                                            margin: "8px",
                                            color: "var(--cpGrayShade5)",
                                        }}>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="#FFFFFF">
                                            <path d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z"></path>
                                        </svg>
                                    </OutboundLink>
                                    <OutboundLink
                                        href="https://www.linkedin.com/company/channel-program/"
                                        style={{
                                            border: "none",
                                            background: "var(--cpNavyShade3)",
                                            padding: "16px",
                                            textAlign: "center",
                                            textDecoration: "none",
                                            display: "inline-block",
                                            fontSize: "18px",
                                            borderRadius: "80%",
                                            boxSizing: "border-box",
                                            position: "relative",
                                            margin: "8px",
                                            color: "var(--cpGrayShade5)",
                                        }}>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="#FFFFFF">
                                            <circle cx="4.983" cy="5.009" r="2.188"></circle>
                                            <path d="M9.237 8.855v12.139h3.769v-6.003c0-1.584.298-3.118 2.262-3.118 1.937 0 1.961 1.811 1.961 3.218v5.904H21v-6.657c0-3.27-.704-5.783-4.526-5.783-1.835 0-3.065 1.007-3.568 1.96h-.051v-1.66H9.237zm-6.142 0H6.87v12.139H3.095z"></path>
                                        </svg>
                                    </OutboundLink>
                                    <OutboundLink
                                        href="https://www.youtube.com/channel/UCQAgOa07oc3CN_IZpuS00wg"
                                        style={{
                                            border: "none",
                                            background: "var(--cpNavyShade3)",
                                            padding: "16px",
                                            textAlign: "center",
                                            textDecoration: "none",
                                            display: "inline-block",
                                            fontSize: "18px",
                                            borderRadius: "80%",
                                            boxSizing: "border-box",
                                            position: "relative",
                                            margin: "8px",
                                            color: "var(--cpGrayShade5)",
                                        }}>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="#FFFFFF">
                                            <path d="M21.593 7.203a2.506 2.506 0 0 0-1.762-1.766C18.265 5.007 12 5 12 5s-6.264-.007-7.831.404a2.56 2.56 0 0 0-1.766 1.778c-.413 1.566-.417 4.814-.417 4.814s-.004 3.264.406 4.814c.23.857.905 1.534 1.763 1.765 1.582.43 7.83.437 7.83.437s6.265.007 7.831-.403a2.515 2.515 0 0 0 1.767-1.763c.414-1.565.417-4.812.417-4.812s.02-3.265-.407-4.831zM9.996 15.005l.005-6 5.207 3.005-5.212 2.995z"></path>
                                        </svg>
                                    </OutboundLink>
                                </td>
                            </tr>
                            <tr>
                                <td
                                    style={{
                                        boxSizing: "border-box",
                                        position: "relative",
                                        margin: "auto",
                                        textAlign: "center",
                                        padding: "16px",
                                        fontSize: "12px",
                                    }}>
                                    © 2023 Channel Program. All rights reserved.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                )}
            </div>
        </div>
    );
}
