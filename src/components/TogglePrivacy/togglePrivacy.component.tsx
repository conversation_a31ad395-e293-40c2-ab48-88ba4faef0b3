import {useState} from "react";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import {getErrorFromArray} from "../../utils/error.util";
import ToggleInput from "../FormControls/toggle.component";
import {AxiosError} from "axios";
import {profileService} from "../../services/profile.service";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import styles from "../../styles/components/togglePrivacy.module.sass";
import {Tooltip, OverlayTrigger} from "react-bootstrap";

interface ITogglePrivacyComponent {
    showTooltip?: boolean;
    tooltipText?: string;
    displayLabel?: boolean;
    big?: boolean;
}

export function TogglePrivacyComponent(props: ITogglePrivacyComponent) {
    const [updatingPrivacy, setUpdatingPrivacy] = useState<boolean>(false);

    const {authState, updateAuthState} = useAuthState();
    const notify = useNotification();

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setUpdatingPrivacy(false);
    };

    const handleSuccessPrivChange = (isPrivate: boolean) => {
        if (!isPrivate) {
            notify("Your content and profile information is now public and searchable.", "Success");
        } else {
            notify("Your content and profile information are now private and cannot be searched.", "Success");
        }
        setUpdatingPrivacy(false);
        updateAuthState(UPDATE_AUTH, {is_private: isPrivate});
    };

    const handleTogglePrivacy = (checked: boolean) => {
        setUpdatingPrivacy(true);
        if (checked) {
            profileService.updateUser(
                {id: authState.id, is_private: false},
                () => handleSuccessPrivChange(!checked),
                handleError,
            );
            return;
        } else {
            profileService.updateUser(
                {id: authState.id, is_private: true},
                () => handleSuccessPrivChange(!checked),
                handleError,
            );
        }
    };

    return (
        <>
            {props.showTooltip ? (
                <OverlayTrigger
                    placement="bottom"
                    overlay={
                        <Tooltip id="tooltip-discord">
                            {props.tooltipText ? props.tooltipText : "Profile visibility"}
                        </Tooltip>
                    }>
                    <div className="d-flex justify-content-between align-items-center">
                        {props.displayLabel && <span className={styles.visibilityText}>Profile visibility</span>}
                        <ToggleInput
                            id="togglePrivacy"
                            checked={!authState.is_private}
                            onToggle={handleTogglePrivacy}
                            beforeText="Private"
                            afterText="Public"
                            disabled={updatingPrivacy}
                            big={props.big}
                        />
                    </div>
                </OverlayTrigger>
            ) : (
                <div className="d-flex justify-content-between align-items-center">
                    {props.displayLabel && <span className={styles.visibilityText}>Profile visibility</span>}
                    <ToggleInput
                        id="togglePrivacy"
                        checked={!authState.is_private}
                        onToggle={handleTogglePrivacy}
                        beforeText="Private"
                        afterText="Public"
                        disabled={updatingPrivacy}
                        big={props.big}
                    />
                </div>
            )}
        </>
    );
}
