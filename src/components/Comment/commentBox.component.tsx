import {useState} from "react";
import {Form<PERSON>rovider, useForm} from "react-hook-form";
import {CODE_PROFANITY_ERROR} from "../../constants/errorMessages.constant";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {commentService} from "../../services/comment.service";
import styles from "../../styles/components/commentBox.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../FormControls/textBox.component";

export function CommentBox(props) {
    const {profilePic, subject_id, subject_type} = props;
    const methods = useForm();
    const {authState} = useAuthState();
    const {settings} = useSettings();
    const [sendingComment, setSendingComment] = useState(false);
    const notify = useNotification();
    const author_company_id = settings?.post_profile?.id === authState?.id ? null : settings.post_profile?.id;

    const sendComment = data => {
        setSendingComment(true);

        const newComment = {
            comment: data.commentMsg,
            subject_id: subject_id,
            subject_type: subject_type,
            author_company_id,
        };
        commentService.postComment(newComment, handleSuccessComment, handleError);
        methods.reset();
    };

    const handleSuccessComment = () => {
        props.handleShowCommentsApi(subject_id, subject_type, true);
        setSendingComment(false);
    };

    const handleError = error => {
        setSendingComment(false);
        const errorCodes = getErrorFromArray(error);
        if (errorCodes.includes(CODE_PROFANITY_ERROR)) {
            notify(CODE_PROFANITY_ERROR, "Error");
        } else {
            notify((error.response as any)?.data?.message || "", "Error");
        }
    };

    return (
        <div className={styles.chatInput}>
            <FormProvider {...methods}>
                <form onSubmit={methods.handleSubmit(sendComment)} className={styles.commentForm}>
                    <span className={styles.commentMedia}>
                        <img className="me-3 rounded-circle" alt="Profile Avatar" src={profilePic} />
                    </span>
                    <TextBoxComponent
                        id="commentMsg"
                        placeholder="Enter your Comment"
                        isRequired
                        className={styles.inputPadding}
                        containerClassName="mt-1 mb-1"
                        allowEmojis
                        maxLength={1000}
                        emojiPicketPlacement={"bottom-end"}
                    />
                    <ButtonComponent
                        id="commentBtn"
                        className="cpBlueBtnThin ms-3 text-center"
                        type="submit"
                        disabled={sendingComment}>
                        <Loader inline version="iconWhite" loading={sendingComment} />
                        {!sendingComment && "Post"}
                    </ButtonComponent>
                </form>
            </FormProvider>
        </div>
    );
}
