import {useState} from "react";
import {Modal} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {CODE_PROFANITY_ERROR} from "../../constants/errorMessages.constant";
import useNotification from "../../hooks/useNotification";
import {commentService} from "../../services/comment.service";
import styles from "../../styles/components/flagCommentBox.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../FormControls/textBox.component";

export function EditCommentBox(props) {
    const {subject_id, toggle, subject_type, api_subject_id} = props;

    const methods = useForm({
        defaultValues: {
            comment: props.defaultValues?.comment || "",
        },
    });

    const [sendingEditComment, setSendingEditComment] = useState(false);
    const notify = useNotification();
    const editComment = data => {
        setSendingEditComment(true);

        const editCommentData = {
            comment: data.comment,
            subject_id: subject_id,
        };
        commentService.editComment(editCommentData, handleSuccessEditComment, handleError);
        methods.reset();
    };

    const handleSuccessEditComment = () => {
        setSendingEditComment(false);
        props.handleShowCommentsApi(api_subject_id, subject_type, true);
        props.setToggle(false);
        notify(`Successfully updated comment.`, "Success");
    };

    const handleError = error => {
        setSendingEditComment(false);
        const errorCodes = getErrorFromArray(error);
        if (errorCodes.includes(CODE_PROFANITY_ERROR)) {
            notify(CODE_PROFANITY_ERROR, "Error");
        } else {
            notify((error.response as any)?.data?.message || "", "Error");
        }
    };

    const resetValues = () => {
        if (props.defaultValues) {
            return methods.reset(props.defaultValues);
        }
        methods.reset({comment: ""});
    };

    const onCancel = () => {
        props.setToggle(false);
        resetValues();
    };

    return (
        <Modal show={toggle} enforceFocus={false} autoFocus={false} onHide={() => onCancel()} centered className="show">
            <Modal.Header closeButton>
                <Modal.Title>Edit Comment</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="my-2 mx-2">
                    <div className={styles.chatInput}>
                        <FormProvider {...methods}>
                            <form onSubmit={methods.handleSubmit(editComment)} className={styles.commentForm}>
                                <TextBoxComponent
                                    id="comment"
                                    placeholder="Edit Comment"
                                    isRequired
                                    className={styles.inputPadding}
                                    containerClassName="mt-1 mb-1"
                                    allowEmojis
                                    maxLength={1000}
                                />
                                <ButtonComponent id="editCommentBtn" type="submit" disabled={sendingEditComment}>
                                    <Loader inline version="iconWhite" loading={sendingEditComment} />
                                    {!sendingEditComment && "Update"}
                                </ButtonComponent>
                            </form>
                        </FormProvider>
                    </div>
                </div>
            </Modal.Body>
        </Modal>
    );
}
