import {useEffect, useRef, useState} from "react";
import {Column, usePagination, useRowSelect, useTable} from "react-table";
import {IPaginationData} from "../../Interfaces/pagination.interface";
import {IColumns} from "../../Interfaces/tableColumns.interface";
import {CPSortType} from "../../enums/sortType.enum";
import {IObject} from "../../models";
import styles from "../../styles/components/table.module.sass";
import Loader from "../../utils/loader";
import DraggableTableRow from "./draggableTableRow.component";

interface IProps {
    columns: Column<IColumns>[];
    data: any;
    loading: boolean;
    setLoading: (loading: boolean) => void;
    serviceHandler: Function;
    onServiceSuccess: Function;
    onServiceError: Function;
    customParameters?: IObject;
    hiddenColumns?: Array<string>;
    pagination?: IPaginationData;
    searchWord?: string;
    sortType?: "asc" | "desc";
    orderBy?: string;
    handleDrag?: any;
    handleDragEnd?: any;
}

const DraggableTable = (props: IProps) => {
    const initialState = {hiddenColumns: props.hiddenColumns ? props.hiddenColumns : []};
    const [sortType, setSortType] = useState<CPSortType.ASC | CPSortType.DESC>(
        props.sortType === "desc" ? CPSortType.DESC : CPSortType.ASC,
    );
    const [orderBy, setOrderBy] = useState<string>(props.orderBy ? props.orderBy : "");
    const [firstTime, setFirstTime] = useState<boolean>(true);
    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        rows,
        prepareRow,
        state: {pageIndex},
    } = useTable(
        {
            columns: props.columns,
            data: props.data,
            initialState,
        },
        usePagination,
        useRowSelect,
    );
    const reorderedData = useRef<any[] | null>(null);
    const currentPage =
        props.pagination && props.pagination?.meta?.current_page ? props.pagination.meta.current_page : 1;
    const lastPage = props.pagination && props.pagination?.meta?.last_page ? props.pagination.meta.last_page : 1;

    const handleHeaderClick = column => {
        return !column.sortable
            ? null
            : {
                  onClick: () => {
                      if (props.loading) return;
                      setFirstTime(false);
                      if (orderBy === column.id) {
                          if (sortType === CPSortType.ASC) setSortType(CPSortType.DESC);
                          else setSortType(CPSortType.ASC);
                      } else {
                          setSortType(CPSortType.ASC);
                          setOrderBy(column.id);
                      }
                  },
              };
    };

    const handlePreviousPage = () => {
        if (currentPage === 1) return;
        const previousPage = props.pagination?.meta.current_page! - 1;
        const firstPage = 1;
        let page = "";
        if (props.pagination && previousPage > 1) {
            page = previousPage.toString();
        } else {
            page = firstPage.toString();
        }
        let requestObj: IObject = {
            page,
            search_word: props.searchWord,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        props.setLoading(true);
        props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
    };

    const handleNextPage = () => {
        const nextPage = props.pagination?.meta.current_page! + 1;
        const lastPage = props.pagination?.meta.last_page!;
        if (currentPage === lastPage) return;
        let page = "";
        if (props.pagination && nextPage < props.pagination?.meta.last_page) {
            page = nextPage.toString();
        } else {
            page = lastPage.toString();
        }
        let requestObj: IObject = {
            page,
            search_word: props.searchWord,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        props.setLoading(true);
        props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
    };

    const handleGoToPage = (page: number) => {
        if (page === currentPage) return;
        let requestObj: IObject = {
            page: page.toString(),
            search_word: props.searchWord,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        props.setLoading(true);
        props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
    };

    useEffect(() => {
        if (props.loading) return;
        const searchVideos = setTimeout(() => {
            let requestObj: IObject = {
                search_word: props.searchWord,
                sortType,
                orderBy,
            };
            if (props.customParameters) {
                requestObj = {...requestObj, ...props.customParameters};
            }
            props.setLoading(true);
            props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
            setFirstTime(false);
        }, 500);
        return () => {
            clearTimeout(searchVideos);
        };
        // eslint-disable-next-line
    }, [props.searchWord]);

    useEffect(() => {
        if (props.loading) return;
        let requestObj: IObject = {
            search_word: props.searchWord,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        props.setLoading(true);
        props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
        setFirstTime(false);
        // eslint-disable-next-line
    }, [props.customParameters]);

    useEffect(() => {
        if (firstTime) return;
        props.setLoading(true);
        let requestObj: IObject = {
            search_word: props.searchWord,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        props.serviceHandler && props.serviceHandler(requestObj, props.onServiceSuccess, props.onServiceError);
        // eslint-disable-next-line
    }, [orderBy, sortType]);

    const assertSortableColumn = (column: any) => {
        return !column.sortable ? " cursor-text" : "";
    };

    const moveRow = (dragIndex, hoverIndex) => {
        const dragRecord = props.data[dragIndex];
        const copy: any[] = [...props.data];
        copy.splice(dragIndex, 1);
        copy.splice(hoverIndex, 0, dragRecord);
        reorderedData.current = copy;
        props.handleDrag &&
            props.handleDrag({
                orderedData: copy,
                fromIndex: dragIndex,
                toIndex: hoverIndex,
                row: dragRecord,
            });
    };

    const handleEndMoveRow = item => {
        props.handleDragEnd(item, reorderedData.current);
    };

    return (
        <div className="table-responsive">
            {/* @ts-ignore */}

            <table
                className={`table table-striped table-bordered table-hover ${styles.tableStyle}`}
                {...getTableProps()}>
                <thead className="table-light">
                    {headerGroups.map(headerGroup => (
                        <tr {...headerGroup.getHeaderGroupProps()} key={headerGroup.id}>
                            <th></th>
                            {headerGroup.headers.map(column => (
                                <th
                                    {...handleHeaderClick(column)}
                                    {...column.getHeaderProps()}
                                    className={`fw-500 text-center ${assertSortableColumn(column)}`}
                                    key={column.id}>
                                    {column.render("Header")}
                                    <span>
                                        {column.id === orderBy ? (sortType === CPSortType.DESC ? " 🔽" : " 🔼") : ""}
                                    </span>
                                </th>
                            ))}
                        </tr>
                    ))}
                </thead>
                <tbody {...getTableBodyProps()}>
                    {!props.loading &&
                        rows.map((row, index) => {
                            prepareRow(row);
                            return (
                                <DraggableTableRow
                                    index={index}
                                    row={row}
                                    moveRow={moveRow}
                                    handleEnd={handleEndMoveRow}
                                    {...row.getRowProps()}
                                    key={row.id}
                                />
                            );
                        })}
                    {props.loading && (
                        <tr className="d-flex justity-content-center w-100">
                            <td className="align-middle">
                                <Loader inline={true} loading={props.loading} version="iconOrange" />
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
            <nav aria-label="Page navigation d-flex example align-items-center">
                <ul className="pagination align-items-center paginationLinks">
                    <li className="page-item">
                        <span className="page-link" onClick={() => handleGoToPage(1)}>
                            {"<<"}
                        </span>
                    </li>
                    <li className="page-item">
                        <span className="page-link" onClick={handlePreviousPage}>
                            Previous
                        </span>
                    </li>
                    <li className="page-item">
                        <span className="page-link" onClick={handleNextPage}>
                            Next
                        </span>
                    </li>
                    <li className="page-item">
                        <span className="page-link" onClick={() => handleGoToPage(lastPage)}>
                            {">>"}
                        </span>
                    </li>
                    <li>
                        <span className="mt-2 mx-3">
                            Page{" "}
                            <strong>
                                {currentPage} of {lastPage}
                            </strong>
                        </span>
                    </li>

                    <li>
                        <span>| Go to Page</span>
                    </li>
                    <li>
                        <input
                            type="number"
                            className="form-control mx-3"
                            min={1}
                            max={lastPage}
                            disabled={props.loading}
                            defaultValue={pageIndex + 1}
                            onChange={e => {
                                const goToPage = setTimeout(() => {
                                    const page = e.target.value ? Number(e.target.value) : 1;
                                    if (page === currentPage || page > lastPage || page < 1) return;
                                    handleGoToPage(page);
                                }, 2000);
                                return () => {
                                    clearTimeout(goToPage);
                                };
                            }}
                        />
                    </li>
                </ul>
            </nav>
        </div>
    );
};

export default DraggableTable;
