import {Column, useTable} from "react-table";
import {IColumns} from "../../Interfaces/tableColumns.interface";

interface ITable {
    columns: Column<IColumns>[];
    data: any;
    hiddenColumns?: Array<string>;
    updateTableData?: Function;
}

function InputTable(props: ITable) {
    const initialState = {hiddenColumns: props.hiddenColumns ? props.hiddenColumns : []};

    const {getTableProps, getTableBodyProps, headerGroups, footerGroups, prepareRow, rows} = useTable({
        columns: props.columns,
        data: props.data,
        initialState,
        updateTableData: props.updateTableData,
    });

    return (
        <div className="table-responsive">
            <table className={`table table-striped table-bordered table-hover`} {...getTableProps()}>
                <thead className="table-light">
                    {headerGroups.map((headerGroup, idx) => (
                        <tr {...headerGroup.getHeaderGroupProps()} key={idx}>
                            {headerGroup.headers.map((column, index) => (
                                <th {...column.getHeaderProps()} key={index}>
                                    {column.render("Header")}
                                </th>
                            ))}
                        </tr>
                    ))}
                </thead>
                <tbody {...getTableBodyProps()}>
                    {rows.map((row, idx) => {
                        prepareRow(row);
                        return (
                            <tr {...row.getRowProps()} key={idx}>
                                {row.cells.map((cell, index) => {
                                    return (
                                        <td {...cell.getCellProps()} className="align-middle" key={index}>
                                            {cell.render("Cell")}
                                        </td>
                                    );
                                })}
                            </tr>
                        );
                    })}
                </tbody>
                <tfoot>
                    {footerGroups.map((group, idx) => (
                        <tr {...group.getFooterGroupProps()} key={idx}>
                            {group.headers.map((column, index) => (
                                <td {...column.getFooterProps()} key={index}>
                                    {column.render("Footer")}
                                </td>
                            ))}
                        </tr>
                    ))}
                </tfoot>
            </table>
        </div>
    );
}

export default InputTable;
