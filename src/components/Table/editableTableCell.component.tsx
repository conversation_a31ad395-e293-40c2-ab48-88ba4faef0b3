import {useEffect, useState} from "react";

interface IEditableCellProps {
    data: any;
    updateTableData: Function;
    type: "percent";
}

const EditableCell = (props: IEditableCellProps) => {
    const [value, setValue] = useState(parseFloat(props.data.value).toFixed());

    const onChange = e => {
        const float: number = parseFloat(e.target.value === "" ? "0" : e.target.value);
        const val: string = float > 100 ? "100" : `${float}`;
        const fixedValue = parseFloat(val).toFixed();
        setValue(fixedValue);
    };

    const onBlur = () => {
        props.updateTableData(props.data.row.index, props.data.column.id, value);
    };

    useEffect(() => {
        setValue(parseFloat(props.data.value).toFixed());
    }, [props.data.value]);

    return (
        <>
            {props.type === "percent" && (
                <div className="d-flex align-items-center">
                    <input
                        className="border rounded p-2"
                        type="number"
                        id={props.data.row.index}
                        min="1"
                        max="100"
                        value={value}
                        onChange={onChange}
                        onBlur={onBlur}
                    />
                    <p className="mx-2">%</p>
                </div>
            )}
        </>
    );
};

export default EditableCell;
