import {useQuery, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosPromise, AxiosResponse} from "axios";
import {ReactNode, forwardRef, useEffect, useMemo, useRef, useState} from "react";
import {useParams} from "react-router-dom";
import {Column, useTable} from "react-table";
import {ILinks, IMeta} from "../../Interfaces/pagination.interface";
import {IColumns} from "../../Interfaces/tableColumns.interface";
import {STALE_TIME} from "../../constants/commonStrings.constant";
import {CPSortType} from "../../enums/sortType.enum";
import {IObject} from "../../models";
import styles from "../../styles/components/table.module.sass";
import Loader from "../../utils/loader";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
interface ITable {
    columns: Column<IColumns>[];
    id: string;
    serviceHandler?: (requestObj: any) => AxiosPromise<PaginatedTable>;
    customParameters?: IObject;
    hiddenColumns?: Array<string>;
    searchWord?: string;
    sortType?: "asc" | "desc" | "ASC" | "DESC";
    orderBy?: string;
    tableClassName?: string;
    noResultsMessage?: string | ReactNode;
    customData?: IObject[];
    paginated?: boolean;
    customResponseKey?: string;
    selectableRows?: boolean;
    selectedRows?: any[];
    selectedRowsHandler?: Function;
    isLoading?: boolean;
    meta?: any;
    handleParameterChange?: any;
    middlewareCallback?: Function;
    refetchKey?: number;
    queryKeyCallback?: Function;
}

export interface PaginatedTable {
    data: any;
    links: ILinks;
    meta: IMeta;
}

export const IndeterminateCheckbox = forwardRef<any, any>(({indeterminate, ...rest}, ref: any) => {
    const defaultRef = useRef();
    const resolvedRef = ref || defaultRef;

    useEffect(() => {
        resolvedRef.current.indeterminate = indeterminate;
    }, [resolvedRef, indeterminate]);

    return (
        <>
            <input type="checkbox" ref={resolvedRef} {...rest} />
        </>
    );
});

export function TableComponent(props: ITable) {
    const [sortType, setSortType] = useState<CPSortType.ASC | CPSortType.DESC>(
        props.sortType === "desc" ? CPSortType.DESC : CPSortType.ASC,
    );
    const [orderBy, setOrderBy] = useState<string>(props.orderBy ? props.orderBy : "");
    const [page, setPage] = useState(1);
    const initialState = {hiddenColumns: props.hiddenColumns ? props.hiddenColumns : []};
    const {company_id} = useParams<{company_id?: string}>();
    const companyId = company_id?.split("?")[0];
    const queryClient = useQueryClient();
    const previousSearchWord = useRef<string>("");
    const tableRef = useRef<HTMLTableElement>(null);

    const scrollToTopTable = () => {
        if (tableRef.current) {
            const offset = 100;
            const topPosition = tableRef.current.getBoundingClientRect().top + window.scrollY - offset;
            window.scrollTo({top: topPosition, behavior: "smooth"});
        }
    };

    const queryKey = props.searchWord
        ? props.customParameters
            ? ["table-" + props.id, page, sortType, orderBy, props.searchWord, props.customParameters]
            : ["table-" + props.id, page, sortType, orderBy, props.searchWord]
        : props.customParameters
        ? ["table-" + props.id, page, sortType, orderBy, props.customParameters]
        : ["table-" + props.id, page, sortType, orderBy];

    const fetchTableData = (pageToFetch = 1) => {
        let requestObj = {
            page: pageToFetch,
            sortType,
            orderBy,
            paged: true,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
            if (props.id === "productReviewSearchResults" && props.customParameters?.productReviewRating) {
                requestObj = {...requestObj, paged: false};
            }
        }
        if (companyId) {
            requestObj = {...requestObj, ...{subjects: [companyId]}};
        }
        if (props.searchWord) {
            requestObj = {
                ...requestObj,
                ...{
                    searchWord: props.searchWord,
                    search_word: props.searchWord,
                    page: previousSearchWord.current === props.searchWord ? pageToFetch : 1,
                },
            };
            previousSearchWord.current = props.searchWord;
        }
        const promiseToReturn: Promise<PaginatedTable> = new Promise((resolve, reject) => {
            props
                .serviceHandler?.(requestObj)
                .then(async (r: AxiosResponse<PaginatedTable>) => {
                    queryClient.setQueryData(["table-" + props.id, "active-key"], () => {
                        return queryKey;
                    });
                    props.middlewareCallback?.(r);
                    return r;
                })
                .then(async (r: AxiosResponse<PaginatedTable>) => {
                    if (props.customResponseKey) {
                        resolve(r.data[props.customResponseKey]);
                    }
                    resolve(r.data);
                })
                .catch((e: AxiosError<any>) => reject(e));
        });
        return promiseToReturn;
    };

    const {isLoading, data, isFetching} = useQuery<PaginatedTable>(queryKey, () => fetchTableData(page), {
        enabled: !!props.serviceHandler,
        keepPreviousData: true,
        staleTime: STALE_TIME,
        refetchOnWindowFocus: false,
    });

    const tableData = useMemo(() => (props.customData ? props.customData : data), [data, props.customData]);
    const {getTableProps, getTableBodyProps, headerGroups, rows, prepareRow} = useTable({
        columns: props.columns,
        data:
            (Array.isArray(tableData)
                ? tableData.map(d => {
                      return {...d, page};
                  })
                : tableData?.data.map(d => {
                      return {...d, page};
                  })) || [],
        initialState,
        manualPagination: true,
        manualSortBy: true,
        manualFilters: true,
    });

    const currentPage =
        data?.data?.length || props.customData?.length
            ? props.meta
                ? props.meta.current_page
                : data?.meta?.current_page ?? 1
            : 1;
    const lastPage =
        data?.data?.length || props.customData?.length
            ? props.meta
                ? props.meta.last_page
                : data?.meta?.last_page ?? 1
            : 1;
    const isLoadingQueryData = props.customData === undefined ? isLoading || isFetching : false;

    const handleHeaderClick = column => {
        return !column.sortable
            ? null
            : {
                  onClick: () => {
                      if (isLoadingQueryData || props.isLoading) return;
                      if (orderBy === column.id) {
                          if (sortType === CPSortType.ASC) setSortType(CPSortType.DESC);
                          else setSortType(CPSortType.ASC);
                      } else {
                          setSortType(CPSortType.ASC);
                          setOrderBy(column.id);
                      }
                  },
              };
    };

    const handlePreviousPage = () => {
        setPage(old => Math.max(old - 1, 1));
        scrollToTopTable();
    };

    const handleNextPage = () => {
        setPage(old => old + 1);
        scrollToTopTable();
    };

    const handleGoToPage = (pageToGo: number) => {
        setPage(pageToGo);
        scrollToTopTable();
    };

    const customParameter = JSON.stringify(props.customParameters);

    const handleParamChange = () => {
        let requestObj = {
            page,
            sortType,
            orderBy,
        };
        if (props.customParameters) {
            requestObj = {...requestObj, ...props.customParameters};
        }
        if (companyId) {
            requestObj = {...requestObj, ...{subjects: [companyId]}};
        }
        if (props.searchWord) {
            requestObj = {...requestObj, ...{searchWord: props.searchWord, search_word: props.searchWord}};
        }
        props.handleParameterChange?.(requestObj);
    };

    useEffect(() => {
        setPage(1);
    }, [props.searchWord]);

    useEffect(() => {
        if (props.handleParameterChange) {
            handleParamChange();
        }
        // eslint-disable-next-line
    }, [page]);

    useEffect(() => {
        if (!!(Array.isArray(tableData) ? tableData : tableData?.data) || isLoading || isLoadingQueryData) {
            if (props.handleParameterChange) {
                handleParamChange();
            }
            return;
        }
        const previousPage = page;
        setPage(1);
        queryClient.resetQueries(["table-" + props.id]);
        if (previousPage === 1 && props.handleParameterChange) {
            handleParamChange();
        }
        // eslint-disable-next-line
    }, [orderBy, sortType, props.searchWord, customParameter]);

    useEffect(() => {
        queryClient.refetchQueries(queryKey);
    }, [props.refetchKey]);

    useEffect(() => {
        props.queryKeyCallback && props.queryKeyCallback(queryKey);
    }, [queryKey]);

    const assertSortableColumn = (column: any) => {
        return !column.sortable ? " cursor-text" : "";
    };

    return (
        <div className="table-responsive">
            <table
                ref={tableRef}
                className={`table table-striped table-bordered table-hover ${styles.tableStyle} ${props.tableClassName}`}
                {...getTableProps()}>
                <thead className="table-light">
                    {headerGroups.map((headerGroup, idx) => (
                        <tr {...headerGroup.getHeaderGroupProps()} key={idx}>
                            {headerGroup.headers.map((column, index) => (
                                <th
                                    {...handleHeaderClick(column)}
                                    {...column.getHeaderProps()}
                                    className={`fw-500 text-center ${assertSortableColumn(column)}`}
                                    key={index}>
                                    {column.render("Header")}
                                    <span>
                                        {column.id === orderBy ? (
                                            sortType === CPSortType.DESC ? (
                                                <ArrowDropDownIcon />
                                            ) : (
                                                <ArrowDropUpIcon />
                                            )
                                        ) : (
                                            ""
                                        )}
                                    </span>
                                </th>
                            ))}
                        </tr>
                    ))}
                </thead>
                <tbody className="position-relative" {...getTableBodyProps()}>
                    {((!isLoading && !props.isLoading) || (props.customData && !props.isLoading)) &&
                        rows.map(row => {
                            prepareRow(row);
                            return (
                                <tr {...row.getRowProps()} key={row.id}>
                                    {row.cells.map((cell, index) => {
                                        return (
                                            <td {...cell.getCellProps()} className="align-middle" key={index}>
                                                {cell.render("Cell")}
                                            </td>
                                        );
                                    })}
                                </tr>
                            );
                        })}
                    {(isLoading || isFetching) && !props.customData && (
                        <>
                            {tableData ? (
                                <tr role="row" className={styles.hidden}>
                                    <td>
                                        <Loader fullComponent loading version="iconOrange" />
                                    </td>
                                </tr>
                            ) : (
                                <tr role="row">
                                    <td colSpan={props.columns.length} className="w-100 py-2 text-center">
                                        <Loader inline loading version="iconOrange" />
                                    </td>
                                </tr>
                            )}
                        </>
                    )}
                    {props.isLoading && (
                        <tr role="row">
                            <td colSpan={props.columns.length} className="w-100 py-3">
                                <Loader fullComponent loading version="iconOrange" style={{width: "100%"}} />
                            </td>
                        </tr>
                    )}
                    {!isLoadingQueryData &&
                        !props.isLoading &&
                        !props.customData?.length &&
                        (Array.isArray(tableData) ? !!!tableData?.length : !!!tableData?.data?.length) && (
                            <tr>
                                <td colSpan={props.columns.length} className="text-center">
                                    {props.noResultsMessage || "No Results Found..."}
                                </td>
                            </tr>
                        )}
                </tbody>
            </table>
            {props.paginated && (
                <nav aria-label="Page navigation d-flex example align-items-center">
                    <ul className="pagination align-items-center paginationLinks">
                        <li className="page-item">
                            <span className="page-link" onClick={() => handleGoToPage(1)}>
                                {"<<"}
                            </span>
                        </li>
                        <li className="page-item">
                            <span className="page-link" onClick={handlePreviousPage}>
                                Previous
                            </span>
                        </li>
                        <li className="page-item">
                            <span className="page-link" onClick={handleNextPage}>
                                Next
                            </span>
                        </li>
                        <li className="page-item">
                            <span className="page-link" onClick={() => handleGoToPage(lastPage)}>
                                {">>"}
                            </span>
                        </li>
                        <li>
                            <span className="mt-2 mx-3">
                                Page{" "}
                                <strong>
                                    {currentPage} of {lastPage}
                                </strong>
                            </span>
                        </li>

                        <li>
                            <span>| Go to Page</span>
                        </li>
                        <li>
                            <input
                                type="number"
                                className="form-control mx-3"
                                min={1}
                                max={lastPage}
                                disabled={isLoading || props.isLoading}
                                defaultValue={page}
                                onChange={e => {
                                    const goToPage = setTimeout(() => {
                                        const page = e.target.value ? Number(e.target.value) : 1;
                                        if (page === currentPage || page > lastPage || page < 1) return;
                                        handleGoToPage(page);
                                    }, 2000);
                                    return () => {
                                        clearTimeout(goToPage);
                                    };
                                }}
                            />
                        </li>
                    </ul>
                </nav>
            )}
        </div>
    );
}
