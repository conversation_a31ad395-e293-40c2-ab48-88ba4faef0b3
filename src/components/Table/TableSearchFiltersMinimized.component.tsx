import useTheme from "@mui/material/styles/useTheme";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {Dispatch, useEffect, useRef, useState} from "react";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import Accordion from "../../uicomponents/Atoms/Accordion/Accordion.component";
import AccordionSummary from "../../uicomponents/Atoms/Accordion/AccordionSummary.component";
import AccordionDetails from "../../uicomponents/Atoms/Accordion/AccordionDetails.component";
import {IPageInnerHeadFilter} from "../../Interfaces/filters.interface";
import ErrorBoundary from "../../utils/errorBoundary.util";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import MenuButton, {IMenuButtonItem} from "../../uicomponents/Molecules/MenuButton/menuButton.component";
import Sort from "@mui/icons-material/Sort";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import {FormProvider, useForm} from "react-hook-form";
import DatePicker from "../../uicomponents/Atoms/DatePicker/DatePicker.component";
import ArrowDropDownOutlinedIcon from "@mui/icons-material/ArrowDropDownOutlined";
import AutoCompleteComponent from "../../uicomponents/Molecules/Autocomplete/Autocomplete.component";
import {useDebounce} from "../../hooks/useDebounce";
import CustomPopover from "../../uicomponents/Molecules/CustomPopover/CustomPopover";

interface IProps<TFilter extends object> {
    search?: string;
    setSearch?: Dispatch<string>;
    filters?: IPageInnerHeadFilter;
    setFilters?: Dispatch<any>;
    filterKeysOrder?: string[];
    hideFilterKeys?: string[];
    filterState?: [TFilter | undefined, (filters: TFilter | undefined) => void];
}

const TableSearchFiltersMinimized = ({
    search,
    setSearch,
    filters,
    filterState,
    filterKeysOrder,
    hideFilterKeys,
}: IProps<any>) => {
    const [selectedFilters, setSelectedFilters] = filterState || [{}, () => null];
    const [filtersOpen, setFiltersOpen] = useState<boolean>(false);
    const [clearKey, setClearKey] = useState(0);
    const theme = useTheme();
    const hasSearch = !!setSearch;
    const hasFilters = !!filters;
    const methods = useForm({mode: "onChange"});
    const searchMethods = useForm();
    const searchWatcher = searchMethods?.watch("minimized-search");
    const debouncedSearch = useDebounce(searchWatcher, 500);
    const anchorElement = useRef<HTMLDivElement | null>(null);

    const handleApplyFilters = async () => {
        const newFilters = {...selectedFilters};
        let shouldCheckValidation = false;
        Object.keys(filters || {})?.forEach(async filterKey => {
            const filter = filters?.[filterKey];
            if (!filter) return;
            const values = methods.getValues();
            const is_date = filter.type === "date";
            const is_date_range = filter.type === "date_range";
            const is_number_range = filter.type === "number_range";
            if (is_number_range) {
                shouldCheckValidation = true;
                const minValue = values?.["min__" + filterKey];
                const maxValue = values?.["max__" + filterKey];
                if (minValue && maxValue) {
                    newFilters[filterKey] = {
                        min: minValue,
                        max: maxValue,
                    };
                } else {
                    delete newFilters["min__" + filterKey];
                    delete newFilters["max__" + filterKey];
                }
            }
            if (is_date_range) {
                const startValue = values?.["start__" + filterKey];
                const endValue = values?.["end__" + filterKey];
                if (startValue && endValue) {
                    newFilters[filterKey] = {
                        start_date: startValue,
                        end_date: endValue,
                    };
                } else {
                    delete newFilters["start__" + filterKey];
                    delete newFilters["end__" + filterKey];
                }
            }
            if (filter.items?.length) {
                if (values[filterKey]) {
                    newFilters[filterKey] = Array.isArray(values[filterKey])
                        ? values[filterKey]?.map(v => (typeof v === "string" ? v : v.id))
                        : values[filterKey];
                } else if (newFilters[filterKey]) {
                    delete newFilters[filterKey];
                }
            }
        });
        if (shouldCheckValidation) {
            const passedValidation = await methods.trigger();
            if (!passedValidation) return;
        }
        setSelectedFilters(newFilters);
        setFiltersOpen(false);
    };

    const handleClearFilters = () => {
        setSelectedFilters({});
        methods.reset();
        setClearKey(clearKey + 1);
        setFiltersOpen(false);
    };

    const tooltipContent = (
        <Box
            display="flex"
            flexDirection="column"
            gap={1}
            maxWidth={416}
            minWidth={300}
            sx={{zIndex: 1501}}
            padding={2}>
            <Typography variant="16" fontInter fontWeight={700} color={theme.palette.neutral[600]}>
                FILTERS
            </Typography>
            <Box display="flex" flexDirection="column">
                {Object.entries(filters || {})
                    ?.sort(([a], [b]) => {
                        // Sort by filterKeysOrder, if provided
                        // If we provide  filterKeysOrder but the filter is not in the list, it will be sorted last
                        // if it is sorting, it will be sorted first'
                        if (filters?.[a].is_sorting) return -1;
                        if (filters?.[b].is_sorting) return 1;
                        if (filterKeysOrder) {
                            const aIndex = filterKeysOrder.indexOf(a);
                            const bIndex = filterKeysOrder.indexOf(b);
                            if (aIndex === -1 && bIndex === -1) return 0;
                            if (aIndex === -1) return 1;
                            if (bIndex === -1) return -1;
                            return aIndex - bIndex;
                        }
                        return 0;
                    })
                    .map(([id, filter]) => {
                        if (!filter.is_public) return null;
                        if (hideFilterKeys?.includes(id)) return null;
                        const is_date = filter.type === "date";
                        const is_date_range = filter.type === "date_range";
                        const isSorting = filter.is_sorting;
                        const selected = selectedFilters?.[id];
                        const filterSelected = filters?.[id].items?.find(
                            item => item.id === (Array.isArray(selected) ? selected[0] : selected),
                        );
                        const is_number_range = filter.type === "number_range";
                        return (
                            <Accordion key={id} disableGutters sx={{boxShadow: "none"}}>
                                <AccordionSummary
                                    expandIcon={<ArrowDropDownOutlinedIcon />}
                                    sx={{
                                        paddingY: 0.5,
                                        paddingX: 1,
                                        margin: 0,
                                        minHeight: "fit-content",
                                        ".MuiAccordionSummary-content": {margin: 0},
                                    }}>
                                    <Typography
                                        variant="14"
                                        fontInter
                                        fontWeight={700}
                                        color={theme.palette.neutral[600]}>
                                        {filter.placeholder}
                                    </Typography>
                                </AccordionSummary>
                                <AccordionDetails sx={{padding: 1}}>
                                    <FormProvider {...methods}>
                                        {is_number_range ? (
                                            <Box display="flex" flexDirection="row" gap={1}>
                                                <TextBoxComponent
                                                    id={"min__" + id}
                                                    type="number"
                                                    label="Min Amount"
                                                    placeholder="Min"
                                                    validateOnTheFly
                                                    validateOnKeyUp
                                                    max={methods.watch("max__" + id) || undefined}
                                                    keepRegistered
                                                    key={"min__" + id + clearKey}
                                                    validationSchema={v => (!!methods.watch("max__" + id) ? !!v : true)}
                                                />
                                                <TextBoxComponent
                                                    id={"max__" + id}
                                                    type="number"
                                                    label="Max Amount"
                                                    placeholder="Max"
                                                    validateOnTheFly
                                                    validateOnKeyUp
                                                    min={methods.watch("min__" + id) || undefined}
                                                    keepRegistered
                                                    key={"max__" + id + clearKey}
                                                    validationSchema={v => (!!methods.watch("min__" + id) ? !!v : true)}
                                                />
                                            </Box>
                                        ) : is_date_range ? (
                                            <Box display="flex" flexDirection="row" gap={1}>
                                                <DatePicker label="Start Date" id={"start__" + id} />
                                                <DatePicker label="End Date" id={"end__" + id} />
                                            </Box>
                                        ) : filter?.items?.length ? (
                                            <AutoCompleteComponent
                                                id={id}
                                                options={
                                                    filter.items?.map(item => ({
                                                        id: item.id,
                                                        label: item.children as string,
                                                        item,
                                                    })) || []
                                                }
                                                sx={{
                                                    width: "100%",
                                                    "& .MuiAutocomplete-popper": {
                                                        zIndex: 1202,
                                                    },
                                                }}
                                                label={filter.placeholder}
                                                multiple={filter.multiple}
                                                limitTagsToSingleLine
                                                getOptionKey={o => (typeof o === "string" ? o : o.id)}
                                                getOptionLabel={o => (typeof o === "string" ? o : o.label)}
                                                disablePortal
                                            />
                                        ) : (
                                            <MenuButton
                                                id={id}
                                                key={id + Math.random()}
                                                items={filter.items}
                                                buttonVariant="chip"
                                                isFilter
                                                multiselect={!!filter.multiple as false}
                                                datepicker={is_date || is_date_range}
                                                ratingFilter={filter.is_rating}
                                                numberRangeFilter={is_number_range}
                                                iconBefore={isSorting && <Sort color="secondary" fontSize="small" />}
                                                showDropdownIcon
                                                selectedItem={selected}
                                                onlyPopoverContent
                                                onChange={item => {
                                                    const newFilters = {
                                                        ...(selectedFilters || {}),
                                                    };
                                                    if ("start_date" in (item || {}) || "end_date" in (item || {})) {
                                                        if (
                                                            (
                                                                item as {
                                                                    start_date: string;
                                                                    end_date: string;
                                                                }
                                                            )?.start_date === null &&
                                                            (
                                                                item as {
                                                                    start_date: string;
                                                                    end_date: string;
                                                                }
                                                            )?.end_date === null
                                                        ) {
                                                            delete newFilters[id];
                                                            setSelectedFilters(newFilters);
                                                            return;
                                                        }
                                                        newFilters[id] = item;
                                                        setSelectedFilters(newFilters);
                                                        return;
                                                    }
                                                    if ("min" in (item || {}) || "max" in (item || {})) {
                                                        if (
                                                            (item as any)?.min === null &&
                                                            (item as any)?.max === null
                                                        ) {
                                                            delete newFilters[id];
                                                            setSelectedFilters(newFilters);
                                                            return;
                                                        }
                                                        newFilters[id] = item;
                                                        setSelectedFilters(newFilters);
                                                        return;
                                                    }
                                                    const newFilterValue = Array.isArray(item)
                                                        ? item.map(i => i.id)
                                                        : (item as IMenuButtonItem<boolean>)?.id;
                                                    newFilters[id] = newFilterValue;
                                                    setSelectedFilters(newFilters);
                                                }}
                                                dateRangeValue={is_date_range ? selected : undefined}
                                                defaultMultiSelected={Array.isArray(selected) ? selected : undefined}
                                                children={null}
                                            />
                                        )}
                                    </FormProvider>
                                </AccordionDetails>
                            </Accordion>
                        );
                    })}
            </Box>
            <Box display="flex" flexDirection="row" gap={1} justifyContent="space-between">
                <Button
                    id="apply-filters"
                    color="blue"
                    variant="text"
                    sx={{color: theme.palette.blue[600]}}
                    onClick={handleApplyFilters}>
                    <CheckOutlinedIcon sx={{height: 18, width: 18}} htmlColor={theme.palette.blue[600]} />
                    Apply Filters
                </Button>
                <Button
                    id="clear-filters"
                    color="blue"
                    variant="text"
                    sx={{color: theme.palette.blue[600]}}
                    onClick={handleClearFilters}>
                    <CloseOutlinedIcon sx={{height: 18, width: 18}} htmlColor={theme.palette.blue[600]} />
                    Clear Filters
                </Button>
            </Box>
        </Box>
    );

    useEffect(() => {
        debouncedSearch !== search && setSearch?.(debouncedSearch);
    }, [debouncedSearch]);

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="row" gap={2}>
                {hasSearch && (
                    <Box
                        sx={{cursor: "pointer"}}
                        tooltip={{
                            title: (
                                <FormProvider {...searchMethods}>
                                    <Box>
                                        <TextBoxComponent
                                            id="minimized-search"
                                            startAdornment={
                                                <SearchOutlinedIcon
                                                    sx={{height: 24, width: 24}}
                                                    htmlColor={theme.palette.blue[800]}
                                                />
                                            }
                                            containerSx={{
                                                "#minimized-search": {
                                                    borderRadius: "35px !important",
                                                    overflow: "hidden",
                                                    border: "none !important",
                                                    padding: "10px 16px 10px 40px !important",
                                                },
                                                ".MuiInput-root::before": {
                                                    display: "none !important",
                                                },
                                            }}
                                        />
                                    </Box>
                                </FormProvider>
                            ),
                            light: true,
                            disableHoverListener: true,
                            maxWidth: 360,
                        }}>
                        <SearchOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
                    </Box>
                )}
                {hasFilters && (
                    <Box
                        sx={{cursor: "pointer"}}
                        ref={anchorElement}
                        tooltip={{title: "Filters"}}
                        onClick={() => {
                            setFiltersOpen(true);
                        }}>
                        <FilterAltOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
                    </Box>
                )}
                <CustomPopover
                    id={`popover-min-filters`}
                    open={filtersOpen}
                    anchorEl={anchorElement.current}
                    onClose={() => setFiltersOpen(false)}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                    }}
                    content={tooltipContent}
                />
            </Box>
        </ErrorBoundary>
    );
};

export default TableSearchFiltersMinimized;
