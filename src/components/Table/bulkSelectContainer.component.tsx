import useTheme from "@mui/material/styles/useTheme";
import BorderedBox from "../../uicomponents/Atoms/Box/BorderedBox.component";
import {useRef} from "react";
import {useElementVisibility} from "../../hooks/useElementVisibility";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {pluralizeString} from "../../utils/formatString.util";
import useElementSize from "../../hooks/useElementSize";

const BulkSelectContainer = ({
    selectedRows,
    itemText = "item",
    children,
    containerRef,
}: {
    selectedRows: any[];
    itemText?: string;
    children: React.ReactNode;
    containerRef: any;
}) => {
    const theme = useTheme();
    const bulkContainerRef = useRef<HTMLDivElement | null>(null);
    const isVisible = useElementVisibility(selectedRows?.length ? bulkContainerRef : null);
    const {width} = useElementSize(containerRef, {ignoreInnerHtmlChange: true});

    if (!selectedRows?.length) return null;
    return (
        <div>
            <div ref={bulkContainerRef} style={{height: 0}} />
            <BorderedBox
                sx={{
                    display: "flex",
                    flexDirection: "row",
                    padding: 1,
                    alignItems: "center",
                    gap: 1,
                    alignSelf: "stretch",
                    borderRadius: 2,
                    bgcolor: theme.palette.neutral[200],
                    boxShadow: 1,
                    border: "none",
                    position: isVisible ? "relative" : "fixed",
                    bottom: isVisible ? 0 : 8,
                    left: "auto",
                    width: isVisible ? "auto" : width,
                    zIndex: 2,
                }}>
                <Typography
                    textAlign="center"
                    variant="14"
                    fontWeight={700}
                    color={theme.palette.blue[800]}
                    paddingX={2}
                    marginRight="auto">
                    {selectedRows.length} {pluralizeString(itemText || "item", selectedRows.length)} selected
                </Typography>
                {children}
            </BorderedBox>
        </div>
    );
};

export default BulkSelectContainer;
