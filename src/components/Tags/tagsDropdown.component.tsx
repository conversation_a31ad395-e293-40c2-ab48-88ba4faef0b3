import {AxiosError, AxiosResponse} from "axios";
import {useEffect} from "react";
import {Typeahead} from "react-bootstrap-typeahead";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {ITag} from "../../Interfaces/tags.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {mediaService} from "../../services/media.service";
import {getErrorFromArray} from "../../utils/error.util";

interface IProps {
    isRequired?: boolean;
    selected?: Array<string | ITag>;
    onChange?: Function;
    className?: string;
    max?: number;
    isInvalid?: boolean;
}

const TagsDropdown = (props: IProps) => {
    const {settings, updateSettings} = useSettings();
    const notify = useNotification();
    const tags = settings.tags || [];

    const onSuccessGetTags = (res: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {tags: res.data.filter(t => !t?.is_hidden)});
    };

    const onErrorGetTags = (err: AxiosError<any>) => {
        notify(getErrorFromArray(err), "Error");
    };

    const handleSelectTag = (selected: any) => {
        if (props.max && selected && selected.length > props.max)
            return notify(`You can only select ${props.max} tags`, "Error");
        if (props.max && props.selected && props.selected.length >= props.max) {
            props.onChange && props.onChange(selected);
            props.onChange && props.onChange(selected.slice(0, props.max));
            if (props.selected.length >= props.max) return;
        }
        props.onChange && props.onChange(selected);
    };

    useEffect(() => {
        mediaService.getAllTags(onSuccessGetTags, onErrorGetTags);
    }, []);

    return (
        <div className={`d-flex flex-column mb-2 ${props.className}`}>
            <label htmlFor="tag-dropdown" className="cpLabel">
                Tags {props.isRequired && "*"}
            </label>
            <Typeahead
                multiple
                id="tag-dropdown"
                clearButton
                onChange={e => handleSelectTag(e)}
                options={tags?.filter((tag: ITag) => {
                    const found = props.selected?.find((t: any) => {
                        return typeof t === "string" ? t === tag.id : t.id === tag.id;
                    });
                    return !found;
                })}
                placeholder="Select Tags"
                selected={props.selected?.map((t: ITag | string) =>
                    typeof t === "string" ? tags?.find(tag => tag.id === t) || "" : t,
                )}
                isLoading={!!!tags?.length}
                labelKey={"name"}
                disabled={!!!tags?.length}
                isInvalid={props.isInvalid}
            />
        </div>
    );
};

export default TagsDropdown;
