import React, {useState, useEffect, useRef} from "react";

interface ISuscribeButton {
    channelId: string;
    count?: string;
    layout?: string;
}

const SuscribeButton = (props: ISuscribeButton) => {
    const [initialized, setInitialized] = useState<boolean>(false);

    const youtubeSubscribeNode = useRef<HTMLDivElement>(null);

    const onInitialized = () => {
        setInitialized(true);
    };

    useEffect(() => {
        const configureYoutubeService = () => {
            if (initialized) return;

            const youtubeScript = document.createElement("script");
            youtubeScript.src = "https://apis.google.com/js/platform.js";
            if (youtubeSubscribeNode.current?.parentNode) {
                youtubeSubscribeNode.current.parentNode.appendChild(youtubeScript);
                onInitialized();
            }
        };
        configureYoutubeService();
    }, [initialized]);

    return (
        <div className="py-2" style={{textAlign: "right"}}>
            <div
                ref={youtubeSubscribeNode}
                className="g-ytsubscribe"
                data-channelid={props.channelId}
                data-layout={props.layout ? props.layout : "full"}
                data-count={props.count ? props.count : "default"}
            />
        </div>
    );
};

export default SuscribeButton;
