import {IPeople} from "../../Interfaces/people.interface";
import {ICompany} from "../../Interfaces/company.interface";
import Carousel from "../Carousel/carousel.component";
import ExplorerCard from "./explorerCard.component";

interface IProps {
    entities: IPeople[] | ICompany[];
    profile_type: "company" | "user";
}

export default function ExplorerCarousel(props: IProps) {
    const carouselItems = () => props.entities.map(entity => <ExplorerCard key={entity.id} video={entity} />);
    return (
        <>
            <Carousel show={3} infiniteLoop items={carouselItems} />
        </>
    );
}
