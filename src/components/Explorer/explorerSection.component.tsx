import {Link} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import {ICategoriesData, IPeopleData, IVideosSearch} from "../../Interfaces/search.interface";
import styles from "../../styles/components/explorerSection.module.sass";
import {getContrastColor} from "../../utils/color.util";
import TitleContainer from "../Titles/titleContainer.component";
import ExplorerCard from "./explorerCard.component";

export interface IExplorerData {
    top_categories: ICategoriesData[];
    influencers: IPeopleData[];
    trending_videos: IVideosSearch[];
    latest_videos: IVideosSearch[];
}

interface IProps {
    data: IExplorerData;
    setData: Function;
    section_titles: {
        top_categories: string;
        influencers: string;
        trending_videos: string;
        latest_videos: string;
    };
}

export default function ExplorerSections(props: IProps) {
    const limit = {
        top_categories: 20,
        influencers: 4,
        trending_videos: 4,
        latest_videos: 4,
    };

    const links = {
        top_categories: {path: routeConfig.Categories.path, text: "Explore all categories"},
        influencers: {path: routeConfig.ProfileExplorer.path, text: "Explore People and Vendors"},
        trending_videos: {path: routeConfig.VideoExplorer.path, text: "Explore all Videos"},
        latest_videos: {path: routeConfig.VideoExplorer.path, text: "Explore all Videos"},
    };

    const handleFollow = user_id => {
        props.setData((old: IExplorerData) => {
            const influencers = old.influencers.map(influencer => {
                if (influencer.id === user_id) {
                    influencer.current_user_is_following = !influencer.current_user_is_following;
                }
                return influencer;
            });
            return {...old, influencers};
        });
    };

    return (
        <>
            {Object.keys(props.data)?.map((key: string) => {
                return (
                    <div key={key} className={styles.parentContainer}>
                        <div className="d-flex align-items-center">
                            <TitleContainer
                                as="h2"
                                text={props.section_titles?.[key]}
                                className="mb-2"
                                noConnector
                                position="start"
                            />
                            <Link to={links?.[key].path} className="ms-2 linkStyle">
                                {links?.[key].text}
                            </Link>
                        </div>
                        <div className={styles.container}>
                            {key === "top_categories" &&
                                props.data?.[key]?.slice(0, limit?.[key]).map((item: ICategoriesData) => {
                                    return (
                                        <Link to={`${routeConfig.Categories.path}/${item.friendly_url}`} key={item.id}>
                                            <div
                                                className={styles.categoryItem}
                                                style={{
                                                    backgroundColor: item.color === "#000000" ? "#fff" : item.color,
                                                    borderColor: item.color === "#000000" ? "#000" : item.color,
                                                }}>
                                                <span
                                                    style={{
                                                        color: getContrastColor(
                                                            item.color === "#000000" ? "#ffffff" : item.color,
                                                        ),
                                                    }}>
                                                    {item.name}
                                                </span>
                                            </div>
                                        </Link>
                                    );
                                })}
                            {key === "influencers" &&
                                props.data?.[key]?.slice(0, limit?.[key]).map((item: IPeopleData) => {
                                    const entityProp = {...item, type: item.profile_type?.value};
                                    return <ExplorerCard entity={entityProp} key={item.id} onFollow={handleFollow} />;
                                })}
                            {key === "trending_videos" &&
                                props.data?.[key]?.slice(0, limit?.[key]).map((item: IVideosSearch) => {
                                    const videoProps = {
                                        ...item,
                                        company_handle: item.parent?.handle,
                                        user_handle: item.parent?.handle,
                                        user_name: item.parent?.name,
                                        parent_avatar: item.parent?.avatar,
                                        thumbnail: item.thumbnail_url,
                                        parent: item.parent,
                                    };
                                    return (
                                        <ExplorerCard video={videoProps} key={item.id} className={styles.videoCard} />
                                    );
                                })}
                            {key === "latest_videos" &&
                                props.data?.[key]?.slice(0, limit?.[key]).map((item: IVideosSearch) => {
                                    const videoProps = {
                                        ...item,
                                        company_handle: item.parent?.handle,
                                        user_handle: item.parent?.handle,
                                        user_name: item.parent?.name,
                                        parent_avatar: item.parent?.avatar,
                                        thumbnail: item.thumbnail_url,
                                        parent: item.parent,
                                    };
                                    return (
                                        <ExplorerCard video={videoProps} key={item.id} className={styles.videoCard} />
                                    );
                                })}
                        </div>
                    </div>
                );
            })}
        </>
    );
}
