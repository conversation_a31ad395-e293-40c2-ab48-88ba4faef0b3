import {IExplorerInfo} from "../../Interfaces/company.interface";
import ExplorerCard from "./explorerCard.component";
import styles from "../../styles/components/explorerCategory.module.sass";
import {useEffect, useState} from "react";
import ButtonComponent from "../FormControls/button.component";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import _ from "lodash";

interface IProps {
    info: IExplorerInfo;
}

export default function ExplorerCategory(props: IProps) {
    const [active, setActive] = useState(true);
    const [breakpoint, setBreakpoint] = useState(6);
    const navigate = useNavigate();

    const handleSeeMore = () => {
        navigate(`${routeConfig.Explorer.path}?category=${props.info.id}`);
    };

    const handleToggle = () => {
        setActive(!active);
    };

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 520) {
                setBreakpoint(1);
            } else if (window.innerWidth < 768) {
                setBreakpoint(2);
            } else if (window.innerWidth < 1360) {
                setBreakpoint(3);
            } else if (window.innerWidth < 1600) {
                setBreakpoint(4);
            } else if (window.innerWidth < 1920) {
                setBreakpoint(5);
            } else setBreakpoint(6);
        };
        window.addEventListener("resize", handleResize);
        handleResize();
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <div className={styles.container}>
            <div className={active ? styles.stripHeaderActive : styles.stripHeader}>
                <div className="d-flex align-items-center gap-3">
                    <span onClick={handleToggle}>{props.info.name}</span>
                    <span className={styles.controlArrow} onClick={handleToggle} />
                </div>
                {(props.info.videos.length >= breakpoint ||
                    props.info.profiles?.length! >= (breakpoint === 6 ? 5 : breakpoint)) && (
                    <ButtonComponent
                        type="button"
                        className="cpMainBtnThin"
                        onClick={handleSeeMore}
                        id={`seeMore-${_.camelCase(props.info.name)}`}>
                        See more
                    </ButtonComponent>
                )}
            </div>
            {!!props.info?.videos?.length && (
                <>
                    <div className={active ? styles.contentStripActive : styles.contentStrip}>
                        <span className={styles.badge}>Videos</span>
                        {props.info?.videos?.slice(0, breakpoint).map(video => {
                            return <ExplorerCard video={video} key={video.id} />;
                        })}
                    </div>
                </>
            )}
            {!!props.info?.profiles?.length && (
                <>
                    <div className={active ? styles.contentStripActive : styles.contentStrip}>
                        <span className={styles.badgeProfile}>Profiles</span>
                        {props.info?.profiles?.slice(0, breakpoint === 6 ? 5 : breakpoint).map(profile => {
                            return <ExplorerCard entity={profile} key={profile.id} />;
                        })}
                    </div>
                </>
            )}
            <hr />
        </div>
    );
}
