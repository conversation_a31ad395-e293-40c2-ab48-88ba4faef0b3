.hoveredContainer
    position: fixed
    bottom: 20px
    left: 20px
    border: 1px solid var(--cpNavy)
    border-radius: var(--BtnRadius)
    width: 190px
    height: 60px
    z-index: 1000
    text-align: center
    background: var(--cpWhite)
    padding: 12px
    transition: all 0.3s ease-in-out
    display: flex
    justify-content: center
    overflow: hidden
    h4
        font-size: 16px!important
        margin-bottom: 4px!important
        white-space: nowrap
    span
        font-size: 14px!important

.container
    position: fixed
    bottom: 20px
    left: 20px
    border: 1px solid var(--cpBlue)
    border-radius: 50%
    width: 60px
    height: 60px
    z-index: 1000
    display: flex
    justify-content: center
    background: var(--cpBlue)
    padding: 12px
    transition: all 0.3s ease-in-out
    svg
        fill: var(--cpWhite)

.indicator
    position: absolute
    top: 0
    right: 0
    background: var(--cpGreen)
    width: 22px
    height: 22px
    border-radius: 50%
    display: flex
    justify-content: center
    align-items: center
    span
        color: var(--cpWhite)
        font-weight: 600
@media print
    .container
        display: none
    .indicator 
        display: none
