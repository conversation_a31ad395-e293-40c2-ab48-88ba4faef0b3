import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import useAuthState from "../../../hooks/useAuthState";
import usePermissions from "../../../hooks/usePermissions";
import usePusher from "../../../hooks/usePusher";
import usePusherState from "../../../hooks/usePusherState";
import {UPDATE_PUSHER} from "../../../reducers/pusher.reducer";
import {adminService} from "../../../services/admin.service";
import gZipInflate from "../../../utils/gZipInflate.util";
import {useIsAdminPage} from "../../../utils/url.util";
import ReactIcon from "../../Icons/reactIcon.component";
import TitleContainer from "../../Titles/titleContainer.component";
import styles from "./activeUsers.module.sass";

interface IUserCount {
    active_user_count: number;
    logged_user_count: number;
    user_count: number;
}

export default function ActiveUsers() {
    const [isHovered, setIsHovered] = useState(false);
    const {authState} = useAuthState();
    const {isAdmin} = usePermissions();
    const {pusherState, updatePusherState} = usePusherState();
    const usersOnline = pusherState.usersOnline;
    const {BindEvents, UnsubscribePusher, UnbindEvents} = usePusher();
    const isAdminPage = useIsAdminPage();

    const handleNewCount = (data: any) => {
        const parsed: IUserCount = gZipInflate(data.message);
        updatePusherState(UPDATE_PUSHER, {
            usersOnline: parsed.user_count,
        });
    };

    const onSuccessGetInitial = (res: AxiosResponse) => {
        updatePusherState(UPDATE_PUSHER, {
            usersOnline: (res.data as IUserCount).active_user_count,
            usersLoggedIn: (res.data as IUserCount).logged_user_count,
        });
    };

    useEffect(() => {
        if (!pusherState.privateChannel || !isAdmin) return;
        adminService.getAdminUserCounts(onSuccessGetInitial);
        setTimeout(() => {
            BindEvents(["UserCountNotification"], handleNewCount);
        }, 250);
        return () => {
            if (!pusherState.privateChannel || !isAdmin) return;
            UnbindEvents(["UserCountNotification"], true);
        };
    }, [pusherState.privateChannel, isAdmin]);

    return authState.authenticated && isAdmin && isAdminPage ? (
        <div
            className={isHovered ? styles.hoveredContainer : styles.container}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}>
            {isHovered ? (
                <div className="d-flex flex-column position-relative">
                    <TitleContainer
                        as="h4"
                        text={`Online Users: ${usersOnline}`}
                        noConnector
                        position="start"
                        className="mb-0"
                    />
                </div>
            ) : (
                <>
                    <ReactIcon iconName="FaUsers" size="30" />
                    <div className={styles.indicator}>
                        <span>{usersOnline}</span>
                    </div>
                </>
            )}
        </div>
    ) : null;
}
