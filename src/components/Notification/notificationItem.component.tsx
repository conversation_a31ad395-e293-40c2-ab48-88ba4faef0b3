import PersonAddIcon from "@mui/icons-material/PersonAdd";
import useTheme from "@mui/material/styles/useTheme";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import {COMPANY_TYPE} from "../../enums/companyTypes.enum";
import {MediaSubjectTypes} from "../../enums/mediaTypes.enum";
import {NotificationStatuses} from "../../enums/notificationStatuses.enum";
import {USERS_TYPE} from "../../enums/usersTypes.enum";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import usePusherState from "../../hooks/usePusherState";
import {useSubdomain} from "../../hooks/useSubdomain";
import {INotification} from "../../Interfaces/notifications.interface";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {oneToOneChatService} from "../../services/oneToOneChat.service";
import styles from "../../styles/components/notificationPopover.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import {toBoolean} from "../../utils/boolean.util";
import {DATE_TIME_FORMAT, DATE_TIME_FULL_FORMAT, formatDate, getFriendlyDate} from "../../utils/formatDate";
import formatString, {LENGTH} from "../../utils/formatString.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {getSubdomain} from "../../utils/url.util";
import CustomLink from "../Links/CustomLink.component";
import {COMPANY_EXPENSES_TEXT} from "../../constants/siteFlags";

interface INotificationItemProps {
    notification: INotification;
}

export default function NotificationItem(props: INotificationItemProps) {
    const {notification} = props;
    const {updatePusherState} = usePusherState();
    const {authState} = useAuthState();
    const {activeCompany, setActiveCompany} = useActiveCompany();
    const {invalidateMatchedQueries} = useQueryHelper();
    const navigate = useNavigate();
    const theme = useTheme();
    const subdomainObj = useSubdomain();

    const getNotifcationLink = subjectType => {
        const subjectParentType = notification.subject_parent_type?.split("\\").pop();
        switch (subjectType) {
            case MediaSubjectTypes.Blog:
                return routeConfig.PublicBlog.path.replace(
                    ":friendly_url",
                    notification.subject?.friendly_url || notification.subject_parent?.friendly_url,
                );
            case MediaSubjectTypes.Media:
                return routeConfig.WatchVideo.path.replace(":id", notification.subject_id!);
            case MediaSubjectTypes.MediaGallery:
                return (
                    (subjectParentType === "User"
                        ? routeConfig.UserProfile.path.replace(":id", notification.subject_parent?.friendly_url)
                        : routeConfig.VendorProfile.path.replace(":id", notification.subject_parent?.friendly_url)) +
                    "?tab=content&subTab=images"
                );

            case MediaSubjectTypes.Document:
                return (
                    routeConfig.VendorProfile.path.replace(":id", notification.subject_parent.friendly_url) +
                    "?tab=content&subTab=docs"
                );
            case MediaSubjectTypes.ShoutOut:
                return subjectParentType === "User"
                    ? routeConfig.UserProfile.path.replace(":id", notification.subject_parent?.friendly_url)
                    : routeConfig.VendorProfile.path.replace(":id", notification.subject_parent?.friendly_url);
            default:
                return "";
        }
    };

    const handleOnetoOneNotificationClick = (custom_properties: {
        creator_company_id: string;
        receiver_company_id: string;
        redirect_url: string;
    }) => {
        const {author} = notification;
        if (author) {
            oneToOneChatService.toggleChatVisibility({
                receiver_user_id: author.id,
                receiver_company_id: custom_properties.creator_company_id, //custom_properties.receiver_company_id,
                sender_user_id: authState?.id || "",
                sender_company_id: custom_properties.receiver_company_id, //custom_properties.creator_company_id,
                is_hidden: false,
            });
            const isVendor = authState?.type_is_of_vendor;
            const redirectUrl = custom_properties?.redirect_url;
            if (subdomainObj.company) {
                if (isVendor && redirectUrl) {
                    window.location.href = redirectUrl;
                } else {
                    window.location.href = getSubdomain({
                        defaultValues: {
                            pathname: authState.type_is_of_vendor
                                ? routeConfig.CompanyPortal.path.replace(":tab", "my-channel")
                                : routeConfig.MyStack.path,
                        },
                    })
                        .stripSubdomain()
                        .toString();
                }
                setTimeout(() => {
                    updatePusherState(UPDATE_PUSHER, {
                        selectedPortalChat: {
                            userId: author.id,
                            userCompanyId: custom_properties.creator_company_id,
                        },
                    });
                }, 1000);
            } else {
                if (isVendor && redirectUrl) {
                    const url = new URL(redirectUrl);
                    const path = url.pathname;
                    const splitPath = path.split("/").filter(Boolean);
                    if (splitPath?.[1] === activeCompany?.friendly_url) {
                        if (location.pathname !== "/company-portal/my-channel") {
                            navigate(routeConfig.CompanyPortal.path.replace(":tab", "my-channel"));
                        }
                    } else {
                        navigate(path);
                    }
                } else {
                    const path = isVendor
                        ? routeConfig.CompanyPortal.path.replace(":tab", "my-channel")
                        : routeConfig.MyStack.path;
                    navigate(path);
                }
                setTimeout(() => {
                    updatePusherState(UPDATE_PUSHER, {
                        selectedPortalChat: {
                            userId: author.id,
                            userCompanyId: custom_properties.creator_company_id,
                        },
                    });
                }, 1000);
            }
        }
    };

    let author_name: string = "";
    let subdomain: string = "";
    let isPartnerContent: boolean = false;
    let to: string = "";
    const custom_properties = notification.custom_properties;
    switch (notification.action) {
        case "videoProcessed":
            return (
                <CustomLink
                    subdomain={
                        notification?.subject?.custom_properties?.is_partner_content
                            ? notification?.subject_parent?.subdomain
                            : ""
                    }
                    to={
                        notification?.subject?.custom_properties?.is_partner_content == "1"
                            ? routeConfig.VendorPortal.path
                            : routeConfig.WatchVideo.path.replace(":id", notification.subject_id!)
                    }>
                    <div
                        className={styles.notification}
                        style={{
                            backgroundColor:
                                notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        }}>
                        <div className={styles.notificationImg}>
                            <img
                                src={
                                    notification.subject_parent?.avatar ||
                                    createImageFromInitials(
                                        50,
                                        notification.subject_parent?.first_name +
                                            " " +
                                            notification.subject_parent?.last_name,
                                        "",
                                        notification.author.handle === notification.subject_parent?.handle
                                            ? "user"
                                            : "company",
                                    )
                                }
                                alt=""
                            />
                        </div>
                        <div className={styles.notificationText}>
                            <span>
                                Your video: "
                                {formatString(notification.subject?.custom_properties?.title || "", LENGTH, 40)}" is
                                ready to be shared.
                            </span>
                            <span>{getFriendlyDate(notification.created_at || "")}</span>
                        </div>
                    </div>
                </CustomLink>
            );
        case "like":
            const authorImage =
                notification.author.avatar ||
                createImageFromInitials(
                    50,
                    notification.author.first_name + " " + notification.author.last_name,
                    "",
                    "user",
                );
            const subjectType = MediaSubjectTypes[notification.subject_type?.split("\\").pop()!];
            const notificationMsg = !!!notification.otherUsers?.length
                ? `${notification.author.first_name} ${notification.author.last_name} likes your ${
                      subjectType === "images" ? "image" : subjectType
                  } ${
                      notification.subject
                          ? `"${formatString(
                                notification.subject?.custom_properties?.title ||
                                    notification.subject?.title ||
                                    notification.subject?.shout_out ||
                                    notification.subject?.name ||
                                    "",
                                LENGTH,
                                25,
                            )}"`
                          : ""
                  }`
                : notification.otherUsers.length === 1
                  ? `${notification.author.first_name} ${notification.author.last_name} and ${
                        notification.otherUsers[0].name
                    } like your ${subjectType} ${
                        notification.subject
                            ? `"${formatString(
                                  notification.subject?.custom_properties?.title ||
                                      notification.subject?.title ||
                                      notification.subject?.shout_out ||
                                      notification.subject?.name ||
                                      "",
                                  LENGTH,
                                  25,
                              )}"`
                            : ""
                    }`
                  : `${notification.author.first_name} ${notification.author.last_name} and ${
                        notification.otherUsers.length
                    } others like your ${subjectType} ${
                        notification.subject
                            ? `"${formatString(
                                  notification.subject?.custom_properties?.title ||
                                      notification.subject?.title ||
                                      notification.subject?.shout_out ||
                                      notification.subject?.name ||
                                      "",
                                  LENGTH,
                                  25,
                              )}"`
                            : ""
                    }`;
            return (
                <CustomLink to={getNotifcationLink(subjectType)}>
                    <div
                        className={styles.notification}
                        style={{
                            backgroundColor:
                                notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        }}>
                        <div className={styles.notificationImg}>
                            <img src={notification.image || authorImage} alt="" />
                        </div>
                        <div className={styles.notificationText}>
                            <span>{notificationMsg}</span>
                            <span>{getFriendlyDate(notification.created_at!)}</span>
                        </div>
                    </div>
                </CustomLink>
            );
        case "oneToOneChat":
            author_name = notification.author.first_name + " " + notification.author.last_name;
            return (
                <div
                    className={styles.notification}
                    onClick={() => handleOnetoOneNotificationClick(notification?.custom_properties)}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div className={styles.notificationImg}>
                        <img
                            src={notification.author?.avatar || createImageFromInitials(24, author_name, "", "user")}
                            alt=""
                        />
                    </div>
                    <div className={styles.notificationText}>
                        <span>{author_name} sent you a message.</span>
                        <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                    </div>
                </div>
            );
        case "fileUploadSummary":
            author_name = notification.author.first_name + " " + notification.author.last_name;
            isPartnerContent =
                toBoolean(notification?.subject?.custom_properties?.is_partner_content) ||
                !!toBoolean(notification?.custom_properties?.is_partner_content);
            subdomain = isPartnerContent
                ? (notification?.subject_parent?.subdomain ??
                  notification?.custom_properties?.company_data?.subdomain ??
                  "")
                : "";
            to = isPartnerContent ? routeConfig.VendorPortal.path : "";
            return (
                <CustomLink subdomain={subdomain} to={to}>
                    <div
                        className={styles.notification}
                        style={{
                            backgroundColor:
                                notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        }}>
                        <div className={styles.notificationImg}>
                            <img
                                src={
                                    notification.author?.avatar || createImageFromInitials(24, author_name, "", "user")
                                }
                                alt=""
                            />
                        </div>
                        <div className={styles.notificationText}>
                            <span>{notification?.custom_properties?.message}</span>
                            <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                        </div>
                    </div>
                </CustomLink>
            );
        case "fileUploadError":
            author_name = notification.author.first_name + " " + notification.author.last_name;
            isPartnerContent =
                toBoolean(notification?.subject?.custom_properties?.is_partner_content) ||
                !!toBoolean(notification?.custom_properties?.is_partner_content);
            subdomain = isPartnerContent
                ? (notification?.subject_parent?.subdomain ??
                  notification?.custom_properties?.company_data?.subdomain ??
                  "")
                : "";
            to = isPartnerContent ? routeConfig.VendorPortal.path : "";
            return (
                <CustomLink subdomain={subdomain} to={to}>
                    <div
                        className={styles.notification}
                        style={{
                            backgroundColor:
                                notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        }}>
                        <div className={styles.notificationImg}>
                            <img
                                src={
                                    notification.author?.avatar || createImageFromInitials(24, author_name, "", "user")
                                }
                                alt=""
                            />
                        </div>
                        <div className={styles.notificationText}>
                            <span>{notification?.custom_properties?.message}</span>
                            <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                        </div>
                    </div>
                </CustomLink>
            );
        case "fileUploadRejected":
            author_name = notification.author.first_name + " " + notification.author.last_name;
            isPartnerContent =
                toBoolean(notification?.subject?.custom_properties?.is_partner_content) ||
                !!toBoolean(notification?.custom_properties?.is_partner_content);
            subdomain = isPartnerContent
                ? (notification?.subject_parent?.subdomain ??
                  notification?.custom_properties?.company_data?.subdomain ??
                  "")
                : "";
            to = isPartnerContent ? routeConfig.VendorPortal.path : "";
            return (
                <CustomLink subdomain={subdomain} to={to}>
                    <div
                        className={styles.notification}
                        style={{
                            backgroundColor:
                                notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        }}>
                        <div className={styles.notificationImg}>
                            <img
                                src={
                                    notification.author?.avatar || createImageFromInitials(24, author_name, "", "user")
                                }
                                alt=""
                            />
                        </div>
                        <div className={styles.notificationText}>
                            <span>{notification?.custom_properties?.message}</span>
                            <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                        </div>
                    </div>
                </CustomLink>
            );
        case "massMessagingStatus":
            author_name = notification.author.first_name + " " + notification.author.last_name;
            return (
                <CustomLink
                    to={routeConfig.CompanyPortal.path.replace(":tab", "mass-messaging")}
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div className={styles.notificationImg}>
                        <img
                            src={notification.author?.avatar || createImageFromInitials(24, author_name, "", "user")}
                            alt=""
                        />
                    </div>
                    <div className={styles.notificationText}>
                        <span>{notification?.custom_properties?.message}</span>
                        <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                    </div>
                </CustomLink>
            );
        case "partnerPortalRequestInvite":
            const requested_author_name = notification.author.first_name + " " + notification.author.last_name;
            return (
                <CustomLink
                    to={routeConfig.CompanyPortal.path.replace(":tab", "pending-requests")}
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div className={styles.notificationImg}>
                        <img
                            src={
                                notification.author?.avatar ||
                                createImageFromInitials(24, requested_author_name, "", "user")
                            }
                            alt=""
                        />
                    </div>
                    <div className={styles.notificationText}>
                        <span>
                            {requested_author_name} requested access to {notification.subject_parent?.name || ""}.
                        </span>
                        <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                    </div>
                </CustomLink>
            );
        case "distributorUploadComplete":
            return (
                <div
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                        cursor: "default",
                    }}>
                    <div className={styles.notificationImg}>
                        <img
                            src={
                                notification.author?.avatar ||
                                createImageFromInitials(
                                    50,
                                    notification.author?.first_name + " " + notification.author?.last_name,
                                    "",
                                    "user",
                                )
                            }
                            alt=""
                        />
                    </div>
                    <div className={styles.notificationText}>
                        <span>
                            {formatString(notification?.custom_properties?.title || "File", LENGTH, 40)} has been
                            successfully uploaded.
                        </span>
                        <span>{getFriendlyDate(notification.created_at || "")}</span>
                    </div>
                </div>
            );
        case "user-company-request":
            return (
                <CustomLink
                    to={routeConfig.CompanyUsersManagement.path + "?tab=user-requests"}
                    onClick={() => {
                        if (custom_properties.friendly_url !== activeCompany?.friendly_url) {
                            setActiveCompany(custom_properties.friendly_url, custom_properties.company_id);
                        }
                        custom_properties?.company_id &&
                            invalidateMatchedQueries(["user-company-requests", custom_properties.company_id]);
                    }}
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div
                        className={styles.notificationImg}
                        style={{display: "flex", alignItems: "center", justifyContent: "center"}}>
                        <PersonAddIcon />
                    </div>
                    <div className={styles.notificationText}>
                        <span>New user(s) requesting access to your profile.</span>
                        <span>{formatDate(notification.created_at!, DATE_TIME_FORMAT)}</span>
                    </div>
                </CustomLink>
            );
        case "portal-new-follower":
            const followerCompany = notification.custom_properties.follower;
            return (
                <CustomLink
                    to={routeConfig.CompanyPortal.path.replace(":tab", "my-channel")}
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div className={styles.notificationImg}>
                        <AvatarComponentV2
                            isCompany
                            user={{
                                avatar: followerCompany?.avatar ?? "",
                                type: followerCompany?.company_type || USERS_TYPE.MSP,
                                profile_type: "msp",
                                friendly_url: followerCompany?.friendly_url ?? "",
                                name: followerCompany?.name ?? "",
                                is_distributor: false,
                            }}
                            size={40}
                            showProfileType={followerCompany?.company_type !== COMPANY_TYPE.MSP_CLIENT}
                            profileTypeMinimized={followerCompany?.company_type !== COMPANY_TYPE.MSP_CLIENT}
                            disableLink
                            style={{
                                pointerEvents: "none",
                            }}
                        />
                    </div>
                    <div className={styles.notificationText}>
                        <span>
                            {custom_properties.follower?.name} has accepted your invite to your Channel Command.
                        </span>
                    </div>
                </CustomLink>
            );
        case "expenses-synced":
            return (
                <Box display="flex" gap={2} alignItems="center" padding="10px 16px" sx={{cursor: "pointer"}}>
                    <AvatarComponentV2
                        isCompany
                        user={notification.subject_parent}
                        size={40}
                        showProfileType={false}
                        disableLink
                        style={{
                            pointerEvents: "none",
                        }}
                    />
                    <Box display="flex" flexDirection="column" gap={1}>
                        <Typography variant="12" color={theme.palette.neutral[600]} fontWeight={600}>
                            {formatDate(notification.created_at!, DATE_TIME_FULL_FORMAT)}
                        </Typography>
                        <Typography variant="16" fontWeight={600}>
                            Expenses Synced
                        </Typography>
                        <Typography variant="16">
                            Your expenses are ready for review.{" "}
                            <strong style={{color: theme.palette.blue[600]}}>Open {COMPANY_EXPENSES_TEXT}</strong>
                        </Typography>
                    </Box>
                </Box>
            );
        case "renewal-contracts":
            return (
                <Box display="flex" gap={2} alignItems="center" padding="10px 16px" sx={{cursor: "pointer"}}>
                    <AvatarComponentV2
                        isCompany
                        user={notification.subject_parent}
                        size={40}
                        showProfileType={false}
                        disableLink
                        style={{
                            pointerEvents: "none",
                        }}
                    />
                    <Box display="flex" flexDirection="column" gap={1}>
                        <Typography variant="12" color={theme.palette.neutral[600]} fontWeight={600}>
                            {formatDate(notification.created_at!, DATE_TIME_FULL_FORMAT)}
                        </Typography>
                        <Typography variant="16">
                            Your contract <b>{notification.custom_properties.contract_name}</b> with
                            product <b>{notification.custom_properties.product_name}</b> was renewed.{" "}
                            <strong style={{color: theme.palette.blue[600]}}>Open Contracts</strong>
                        </Typography>
                    </Box>
                </Box>
            );
        default:
            return (
                <div
                    title={!notification.date || !notification.title ? notification.action : ""}
                    className={styles.notification}
                    style={{
                        backgroundColor:
                            notification?.status === NotificationStatuses.Read ? "transparent" : "var(--cpGrayBg)",
                    }}>
                    <div className={styles.notificationImg}>
                        <img src={notification.image} alt="default" />
                    </div>
                    <div className={styles.notificationText}>
                        <span>{notification.title}</span>
                        <span>{formatDate(notification.date!, DATE_TIME_FORMAT)}</span>
                    </div>
                </div>
            );
    }
}
