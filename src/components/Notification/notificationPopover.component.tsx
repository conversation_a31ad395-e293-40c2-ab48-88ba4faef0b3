import {NotificationsOutlined} from "@mui/icons-material";
import {
    QueryFunction,
    useInfiniteQuery,
    UseInfiniteQueryResult,
    useMutation,
    useQueryClient,
} from "@tanstack/react-query";
import {AxiosError, AxiosPromise, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useEffect, useRef, useState} from "react";
import {OverlayTrigger, Popover} from "react-bootstrap";
import {useNavigate} from "react-router-dom";
import {INotification, IPaginatedNotification} from "../../Interfaces/notifications.interface";
import {CACHE_TIME, STALE_TIME} from "../../constants/commonStrings.constant";
import CompanyType from "../../constants/companyType.constant";
import routeConfig from "../../constants/routeConfig";
import {NotificationStatuses} from "../../enums/notificationStatuses.enum";
import {PusherEventsEnum} from "../../enums/pusherEvents.enum";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import {useInfinite} from "../../hooks/useInfinite";
import usePusher from "../../hooks/usePusher";
import usePusherState from "../../hooks/usePusherState";
import {useSubdomain} from "../../hooks/useSubdomain";
import {IPusherData} from "../../models/Pusher.interface";
import {userService} from "../../services/user.service";
import styles from "../../styles/components/notificationPopover.module.sass";
import IconButton from "../../uicomponents/Atoms/IconButton/IconButton.Component";
import gZipInflate from "../../utils/gZipInflate.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import Loader from "../../utils/loader";
import {getSubdomain} from "../../utils/url.util";
import NotificationItem from "./notificationItem.component";
import useTheme from "@mui/material/styles/useTheme";
import {IS_DIRECT_IT_SITE} from "../../constants/siteFlags";

interface INotificationProps {
    showNotification: boolean;
    setShowNotification: Function;
}

export function NotificationPopover(props: INotificationProps) {
    const [notifications, setNotifications] = useState<INotification[]>([]);
    const {pusherState} = usePusherState();
    const selectedPortalChat = useRef<any>(null);
    if (pusherState.selectedPortalChat) {
        selectedPortalChat.current = pusherState.selectedPortalChat;
    }

    const {authState} = useAuthState();
    const {activeCompany, setActiveCompany} = useActiveCompany();
    const navigate = useNavigate();
    const subdomainObj = useSubdomain();
    const {BindEvents} = usePusher();
    const queryClient = useQueryClient();
    const {invalidateMatchedQueries} = useQueryHelper();
    const theme = useTheme();
    const pusherEvents = [`${PusherEventsEnum.NotificationListener}_${authState?.id}`];
    const meta: any = useRef({last_page: 1});
    const hasClearedNotifications = useRef(false);

    const userImage =
        authState.avatar || createImageFromInitials(50, authState.firstName + " " + authState.lastName, "", "user");
    const unreadNotifications = notifications.filter(notif => notif.status !== NotificationStatuses.Read);

    const randomId = () => {
        // generate a random id
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    };

    const handleClick = (data: INotification) => {
        if (!authState.id) return;
        if (data.action === "profile-setup" || data.action === "claim-business") {
            const removeNotification = notifications.filter(notif => notif.id !== data.id);
            setNotifications(removeNotification);
        }
        props.setShowNotification(false);
        const hasSubdomain = subdomainObj.company;
        const baseURL = getSubdomain({url: window.location.origin}).stripSubdomain().host;
        switch (data.action) {
            case "profile-setup":
                if (hasSubdomain) {
                    window.location.href =
                        baseURL + routeConfig.UserProfile.path.replace(":id", authState.friendly_url!);
                }
                return navigate(routeConfig.UserProfile.path.replace(":id", authState.friendly_url!), {
                    state: {entityId: authState.id},
                });
            case "portal-new-follower":
                return navigate(routeConfig.CompanyPortal.path.replace(":tab", "my-channel"));
            case "like":
                const readPromiseList: AxiosPromise<any>[] = [];
                if (!!data.otherUsers?.length) {
                    data.otherUsers.forEach(otherUser => {
                        if (authState.id) {
                            readPromiseList.push(
                                userService.updateUserNotification(authState.id, {
                                    id: otherUser.notif_id,
                                    status: NotificationStatuses.Read,
                                }),
                            );
                        }
                    });
                }
                return Promise.all([
                    ...readPromiseList,
                    userService.updateUserNotification(authState.id, {id: data.id, status: NotificationStatuses.Read}),
                ])
                    .then(() => {
                        const notificationIndex = notifications.findIndex(notif => notif.id === data.id);
                        if (notificationIndex !== -1) {
                            const newNotifications = [...notifications];
                            newNotifications[notificationIndex].status = NotificationStatuses.Read;
                            setNotifications(newNotifications);
                        }
                    })
                    .catch(err => {
                        console.error(err);
                    });
            case "expenses-synced":
                if (activeCompany?.id !== data?.custom_properties?.company_id) {
                    setActiveCompany(data?.custom_properties?.friendly_url, data?.custom_properties?.company_id);
                }
                return navigate(routeConfig.CompanyExpenses.path);
            case "renewal-contracts":
                return navigate(routeConfig.CompanyContracts.path);
            default:
                props.setShowNotification(false);
                return;
        }
    };

    const handlePusherNotification = (data: IPusherData) => {
        if (data.event == PusherEventsEnum.NotificationListener + "_" + authState?.id) {
            userNotifications.refetch();
        }
        if (selectedPortalChat.current?.userId) {
            const parsed = gZipInflate(data.message);
            if (parsed.author?.id === selectedPortalChat.current?.userId) {
                userService.updateUserNotification(authState?.id || "", {
                    id: parsed.id,
                    status: NotificationStatuses.Read,
                });
                return;
            }
        }
    };

    const refetchQueries = () => {
        queryClient.refetchQueries(["all-content-feed"]);
        queryClient.refetchQueries(["user-feed"]);
        queryClient.resetQueries(["company-feed"]);
        queryClient.invalidateQueries(["company-videos"]);
        queryClient.invalidateQueries(["company-counts"]);
        queryClient.resetQueries(["people-feed"]);
        queryClient.invalidateQueries(["people-videos"]);
        queryClient.invalidateQueries(["people-counts"]);
        queryClient.refetchQueries(["partnerPageData"]);
    };

    const addSystemNotifications = () => {
        let newNotifs: INotification[] = [];
        if (
            !authState.type_is_of_vendor &&
            !authState?.claimer_responses?.length &&
            authState.company_type?.value !== CompanyType.MSP_CLIENT
        ) {
            const id: string = randomId();
            const newNotif: INotification = {
                id: id,
                title: authState.firstName + ", manage your business profile!",
                date: DateTime.now().toISO(),
                image: userImage,
                action: "claim-business",
                status: NotificationStatuses.NotRead,
            };
            newNotifs = [...newNotifs, newNotif];
        }
        if (authState.need_to_setup_profile && authState.company_type?.value !== CompanyType.MSP_CLIENT) {
            const id: string = randomId();
            const newNotif: INotification = {
                id: id,
                title: "Setup Profile",
                date: DateTime.now().toISO(),
                image: userImage,
                action: "profile-setup",
                status: NotificationStatuses.NotRead,
            };
            newNotifs = [...newNotifs, newNotif];
        }
        return newNotifs;
    };

    useEffect(() => {
        if (pusherState.privateChannel && authState.id) {
            BindEvents(pusherEvents, handlePusherNotification);
        }
    }, [pusherState.privateChannel]);

    useEffect(() => {
        if (!authState.authenticated || !authState.id) return;
        if (!!addSystemNotifications()?.length) {
            setNotifications(addSystemNotifications() || []);
        }
        // eslint-disable-next-line
    }, [authState.authenticated]);

    const getNotifications: QueryFunction<IPaginatedNotification, (string | number)[]> = ({pageParam = 1}) =>
        new Promise((resolve, reject) => {
            return userService
                .getUserNotifications({userId: authState.id!, page: pageParam})
                .then((res: AxiosResponse<IPaginatedNotification>) => {
                    meta.current = res.data?.meta;
                    if (
                        res.data?.data
                            ?.slice(0, 3)
                            ?.find(n => n.action === "videoProcessed" && n.status === "NotRead") &&
                        userNotifications.isFetchedAfterMount
                    ) {
                        refetchQueries();
                    }
                    const foundExpenseSyncNotification = res.data?.data
                        ?.slice(0, 3)
                        ?.find(n => n.action === "expenses-synced" && n.status === "NotRead");

                    if (!!foundExpenseSyncNotification) {
                        const company_id = foundExpenseSyncNotification?.custom_properties?.company_id;
                        if (!!company_id) {
                            invalidateMatchedQueries(["all-company-expenses-linked-accounts", company_id]);
                            invalidateMatchedQueries(["all-expenses-chart-data", company_id]);
                            invalidateMatchedQueries(["company-expenses-linked-accounts", company_id]);
                            invalidateMatchedQueries(["company-expenses-counts", company_id]);
                            invalidateMatchedQueries(["company-expenses-over-time", company_id]);
                            invalidateMatchedQueries(["company-expenses-summary", company_id]);
                            invalidateMatchedQueries(["company-expenses-breakdown", company_id]);
                            invalidateMatchedQueries(["company-expenses-breakdown-filters", company_id]);
                            invalidateMatchedQueries(["company-expenses-subscription-costs", company_id]);
                            invalidateMatchedQueries(["company-expenses-all-expenses-breakdown", company_id]);
                            invalidateMatchedQueries(["all-expenses-chart-data", company_id]);
                            invalidateMatchedQueries(["company-expenses-transactions", company_id]);
                            invalidateMatchedQueries(["company-expenses-transactions-filters", company_id]);
                            invalidateMatchedQueries(["company-expenses-prior-transactions-filters", company_id]);
                            invalidateMatchedQueries(["company-expenses-subscriptions-filters", company_id]);
                            invalidateMatchedQueries(["company-expenses-subscriptions", company_id]);
                            invalidateMatchedQueries(["company-expenses-infinite-subscriptions", company_id]);
                            invalidateMatchedQueries(["search-company-transactions", company_id]);
                            invalidateMatchedQueries(["search-company-subscriptions", company_id]);
                        }
                    }
                    if (res.data?.data?.find(n => n.status === "NotRead")) {
                        hasClearedNotifications.current = false;
                    }
                    resolve(res.data);
                })
                .catch((e: AxiosError<any>) => {
                    reject(e);
                });
        });

    const userNotifications: UseInfiniteQueryResult<IPaginatedNotification, any> = useInfiniteQuery(
        ["user-notifications"],
        getNotifications,
        {
            getNextPageParam: notifs => {
                return notifs.meta?.last_page > notifs.meta?.current_page ? notifs.meta?.current_page + 1 : null;
            },
            staleTime: STALE_TIME,
            cacheTime: CACHE_TIME,
            refetchOnWindowFocus: false,
        },
    );

    const {lastElementRef, pageNum} = useInfinite(meta.current?.last_page || 0, userNotifications.isFetchingNextPage);

    const handleUpdateReactQueryNotifications: any = useMutation(
        (options: any) => new Promise(resolve => resolve(options)),
        {
            onMutate: (options: any) => {
                if (options.mutate_type === "ReadAll") {
                    const ignoredActions: string[] = ["claim-business", "profile-setup"];
                    queryClient.setQueryData(["user-notifications"], (old: any) => {
                        if (!old) return {pages: [{feed: [], last_date: ""}], pageParams: []};
                        return {
                            pages: old.pages.map(page => ({
                                ...page,
                                data: page.data.map(noti =>
                                    ignoredActions.includes(noti.type)
                                        ? noti
                                        : {...noti, status: NotificationStatuses.Read},
                                ),
                            })),
                        };
                    });
                }
            },
        },
    );

    const bulkReadNotifications = () => {
        userService.bulkUpdateUserNotification(
            authState?.id || "",
            {
                modifyAll: true,
                status: NotificationStatuses.Read,
            },
            () => {
                handleUpdateReactQueryNotifications.mutate({mutate_type: "ReadAll"});
                userNotifications.refetch();
            },
        );
    };

    useEffect(() => {
        if (!authState.authenticated || !authState.id) return;
        if (!props.showNotification) return;
        if (userNotifications.hasNextPage && pageNum > 1) {
            userNotifications.fetchNextPage({pageParam: pageNum});
        }
        //eslint-disable-next-line
    }, [pageNum, props.showNotification]);

    useEffect(() => {
        if (!!!userNotifications.data?.pages?.[0]?.data?.length) return;
        const hasNotif = {};
        const engagmentNotifs: any = [];
        if (userNotifications.data?.pages?.length) {
            userNotifications.data?.pages?.forEach(page => {
                page.data.forEach(notif => {
                    if (hasNotif[notif.subject_id!] && notif.action === "like") {
                        const index = engagmentNotifs.findIndex(n => n.subject_id === notif.subject_id);
                        engagmentNotifs[index].otherUsers.push({
                            name: (notif.author?.first_name || "") + " " + (notif.author?.last_name || ""),
                            notif_id: notif.id,
                        });
                        return;
                    }
                    notif.otherUsers = [];
                    hasNotif[notif.subject_id!] = true;
                    engagmentNotifs.push(notif);
                });
            });
        }
        const notifsReturned = [...engagmentNotifs, ...(addSystemNotifications() || [])];
        setNotifications(notifsReturned);
        //eslint-disable-next-line
    }, [userNotifications.data, authState.authenticated]);

    return (
        <>
            <OverlayTrigger
                placement="bottom-end"
                trigger="click"
                onToggle={() => {
                    props.setShowNotification(!props.showNotification);
                    if (!props.showNotification && !hasClearedNotifications.current) {
                        hasClearedNotifications.current = true;
                        bulkReadNotifications();
                    } else {
                        userNotifications.refetch();
                    }
                }}
                show={props.showNotification}
                rootClose
                overlay={
                    <Popover id="popover-positioned" className={styles.popoverOverride}>
                        <Popover.Body
                            className={styles.popoverContainer}
                            onMouseLeave={() => props.setShowNotification(false)}>
                            {notifications.length > 0 &&
                                notifications.map(n => (
                                    <div key={n.id} onClick={() => handleClick(n)}>
                                        <NotificationItem notification={n} />
                                    </div>
                                ))}
                            {!notifications.length && <span className={styles.noNotification}>No notifications</span>}
                            <div ref={lastElementRef} className="text-center">
                                {userNotifications.isFetchingNextPage && (
                                    <div className="d-flex justify-content-center align-items-center w-100">
                                        <Loader inline loading />
                                    </div>
                                )}
                            </div>
                        </Popover.Body>
                    </Popover>
                }>
                <IconButton
                    className="position-relative"
                    data-track
                    id="notificationBell"
                    sx={{
                        padding: "4px!important",
                        width: "32px",
                        height: "32px",
                        svg: {
                            width: "24px",
                            height: "24px",
                        },
                        ...(IS_DIRECT_IT_SITE && {
                            ":hover, :focus": {
                                backgroundColor: "rgba(0, 0, 0, 0.4) !important",
                            },
                        }),
                    }}>
                    <NotificationsOutlined
                        htmlColor={IS_DIRECT_IT_SITE ? theme.palette.neutral[100] : theme.palette.neutral["main"]}
                    />
                    {unreadNotifications.length > 0 && (
                        <span className={`${styles.numberDot} ${IS_DIRECT_IT_SITE && styles.btNumberDot}`}>
                            {unreadNotifications.length}
                        </span>
                    )}
                </IconButton>
            </OverlayTrigger>
        </>
    );
}
