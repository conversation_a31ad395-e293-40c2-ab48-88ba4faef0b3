import {useState} from "react";
import {AxiosError} from "axios";
import {genericErrorString} from "../../utils/error.util";
import {REQUIRED_TEXT} from "../../constants/commonStrings.constant";
import ButtonComponent from "../FormControls/button.component";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {useNavigate} from "react-router-dom";
import {authService} from "../../services/auth.service";
import PasswordTextBoxComponent from "../FormControls/passwordTextbox.component";
import TitleContainer from "../Titles/titleContainer.component";
import useSettings from "../../hooks/useSettings";
import routeConfig from "../../constants/routeConfig";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import useTheme from "@mui/material/styles/useTheme";

interface IChangePassword {
    old_password: string;
    password: string;
    password_confirmation: string;
}

type builderData = {
    builder?: {
        tabLabel?: string;
        confirmPasswordField?: string;
        currentPasswordField?: string;
        newPasswordField?: string;
        title?: string;
        buttonText?: string;
    };
};

export function ChangePassword({builder}: builderData) {
    const passwordMethods = useForm<IChangePassword>({mode: "onBlur"});
    const [isChanged, setIsChanged] = useState<boolean>(false);
    const [error, setError] = useState<string>("");
    const navigate = useNavigate();
    const watcher = useWatch({control: passwordMethods.control});
    const {settings, updateSettings} = useSettings();
    const theme = useTheme();

    const sendInfo = (data: IChangePassword) => {
        setError("");
        updateSettings(UPDATE_SETTINGS, {loading: true});
        authService.changePassword(
            data.old_password,
            data.password,
            data.password_confirmation,
            handleError,
            handleSuccess,
        );
    };
    const handleSuccess = () => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setIsChanged(true);
        passwordMethods.reset();
        navigate(routeConfig.Account.path);
    };

    const handleError = (error: AxiosError<any>) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setError(genericErrorString(error));
    };
    const checkEmptyField = (val: any) => {
        if (Object.values(val).length < 1) {
            return true;
        }
        const fieldValues = Object.values(val).findIndex(fV => {
            return fV === "";
        });
        if (fieldValues >= 0) {
            return true;
        }
        return false;
    };

    return (
        <div className="d-flex align-items-center justify-content-center col-12 col-lg-4 my-4 my-lg-0">
            <FormProvider {...passwordMethods}>
                <form
                    id="updatePasswordForm"
                    onSubmit={passwordMethods.handleSubmit(sendInfo)}
                    className="mx-3 w-100 w-md-75">
                    <div className="d-flex flex-column align-items-center">
                        <TitleContainer
                            noConnector
                            as="h2"
                            text={builder?.title || "Update password"}
                            className="mb-5"
                        />
                        <PasswordTextBoxComponent
                            showError={passwordMethods.formState.errors.old_password}
                            helperText={REQUIRED_TEXT}
                            id="old_password"
                            placeholder="Your current password"
                            label={builder?.currentPasswordField || "Current password"}
                            isRequired
                            validateOnTheFly
                            successfull={isChanged}
                        />
                        <PasswordTextBoxComponent
                            showError={passwordMethods.formState.errors.password}
                            id="password"
                            placeholder="Your new password"
                            label={builder?.newPasswordField || "Enter new password"}
                            isRequired
                            validateOnTheFly
                            helperText="The new password must be different"
                            successfull={isChanged}
                            criteriaHelper
                            tooltip
                            validationSchema={v =>
                                /((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W]).{8,})/.test(v) &&
                                v !== passwordMethods.getValues("old_password")
                            }
                        />
                        <PasswordTextBoxComponent
                            showError={passwordMethods.formState.errors.password_confirmation}
                            id="password_confirmation"
                            placeholder="New password confirmation"
                            label={builder?.confirmPasswordField || "Confirm new password"}
                            isRequired
                            validateOnTheFly
                            helperText="Passwords do not match"
                            successfull={isChanged}
                            validationSchema={v => v === passwordMethods.getValues("password")}
                        />

                        {error !== "" && <p className="errorText errorColor">{error}</p>}
                        {isChanged && <p className="successColor">Password changed successfully</p>}
                        <div className="d-flex justify-content-center">
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                disabled={checkEmptyField(watcher)}
                                useWhiteLabeling
                                id="updatePassword"
                                sx={{padding: 2, paddingX: 4, bgcolor: theme.palette.primary.main, color: "#fff"}}>
                                {settings.loading
                                    ? "Updating password"
                                    : builder?.buttonText
                                      ? builder.buttonText
                                      : "Update password"}
                            </Button>
                        </div>
                    </div>
                </form>
            </FormProvider>
        </div>
    );
}
