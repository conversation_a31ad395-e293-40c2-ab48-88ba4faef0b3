import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import {PRODUCT_LIMIT} from "../../../constants/errorMessages.constant";
import {useCompany} from "../../../hooks/fetches/useCompany";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {ICategory} from "../../../Interfaces/categories.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {categoriesService} from "../../../services/category.service";
import styles from "../../../styles/components/productList.module.sass";
import Loader from "../../../utils/loader";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import ButtonComponent from "../../FormControls/button.component";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import ProductCard from "../../VendorProfile/productCard.component";
import {ProductsModal} from "../../VendorProfile/productsModal.component";

interface IProps {
    profile_type: "user" | "company";
}

export default function ProductList(props: IProps) {
    const [categoriesOptions, setCategoriesOptions] = useState<Array<[string, string]>>([["", ""]]);
    const [showProductModal, setShowProductModal] = useState<boolean>(false);
    const [savedProductId, setSavedProductId] = useState<string | null>(null);
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const notify = useNotification();
    const {settings, updateSettings} = useSettings();
    const {authState} = useAuthState();
    const {company, companyRules, companyProducts} = useCompany(friendly_url, {
        isCompany: true,
        calls: {companyRules: authState.authenticated, companyProducts: true, company: true},
        companyParams: ["id"],
    });
    const companyId = company.data?.id;
    const products = companyProducts;
    const savedProduct =
        savedProductId && companyId ? products.data?.find(product => product.id === savedProductId) : null;
    const {hasPermissions, isSuperAdmin} = usePermissions({overrideCompanyId: companyId});
    const isEditable = authState?.id
        ? props.profile_type === "company" && hasPermissions([PERMISSION_GROUPS.PRODUCTS_UPDATE], {id: companyId})
        : false;

    const handleError = () => {
        notify("Error fetching categories", "Error");
    };

    const getRemainingProductCount = () => {
        let count = 0;
        const foundRule = companyRules.data?.find(rule => rule.profile_rule === PRODUCT_LIMIT);
        if (foundRule) {
            const {rule_value} = foundRule;
            count = (rule_value as number) - (products?.data?.length || 0);
        }
        if (count < 0) count = 0;
        return count;
    };

    const onSuccessCategories = (response: AxiosResponse<Array<ICategory>>) => {
        if (!response.data || response.data.length === 0) return;

        let newCategories: Array<[string, string]> = [];
        response.data
            .filter(cat => cat.parent_id !== null && !cat.is_hidden)
            .forEach(item => {
                newCategories = [...newCategories, [item.id, item.name]];
            });
        setCategoriesOptions(newCategories);
        updateSettings(UPDATE_SETTINGS, {cp_categories: newCategories});
    };

    const fetchCategories = async () => {
        categoriesService.getAllCategories(onSuccessCategories, handleError);
    };

    useEffect(() => {
        if (categoriesOptions.length > 1 || props.profile_type === "user") return;
        const tempSettings = sessionStorage.settings && JSON.parse(sessionStorage.settings);
        if (settings.cp_categories) {
            return setCategoriesOptions(settings.cp_categories);
        }
        if (tempSettings?.cp_categories) {
            return setCategoriesOptions(tempSettings);
        }
        fetchCategories();
        // eslint-disable-next-line
    }, []);

    return (
        <>
            <div className="d-flex align-items-center mt-4">
                {isEditable && (
                    <>
                        <ButtonComponent
                            id="addProduct"
                            type="button"
                            onClick={() => {
                                if (getRemainingProductCount() <= 0 && !isSuperAdmin) {
                                    notify(
                                        "You've reached your plan limit for adding products. Upgrade your plan to add more products.",
                                        "Error",
                                    );
                                    return;
                                }
                                setShowProductModal(true);
                            }}
                            className="cpBlueBtnThin">
                            + Add Product
                        </ButtonComponent>
                    </>
                )}
            </div>
            <div className={styles.productsContainer}>
                {products && !!products?.data?.length ? (
                    <>
                        {products?.data?.map(item => (
                            <ProductCard
                                product={item}
                                key={item.id}
                                isEditable={isEditable}
                                company_friendly_url={friendly_url}
                                categoriesOptions={categoriesOptions}
                                showReviewQuote
                                className="my-3"
                                maxReviews={3}
                                disableInfiniteLoad
                            />
                        ))}
                    </>
                ) : (
                    <div className="mt-4">
                        {products.isLoading ? (
                            <Loader inline loading big />
                        ) : (
                            <SubtitleContainer as="span" text="No products set." position="start" />
                        )}
                    </div>
                )}
            </div>
            {companyId && showProductModal && (
                <ProductsModal
                    companyId={companyId}
                    onClose={() => {
                        products.refetch();
                        setShowProductModal(false);
                    }}
                    company_friendly_url={friendly_url}
                    setSavedProductId={setSavedProductId}
                    defaultValues={savedProduct && savedProductId ? savedProduct : undefined}
                />
            )}
        </>
    );
}
