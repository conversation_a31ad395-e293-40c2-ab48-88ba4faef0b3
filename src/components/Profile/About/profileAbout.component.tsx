import {AxiosError} from "axios";
import DOMPurify from "dompurify";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation} from "react-router-dom";
import {CODE_PROFANITY_ERROR} from "../../../constants/errorMessages.constant";
import routeConfig from "../../../constants/routeConfig";
import {VENDOR_TYPES} from "../../../constants/vendorProfiles.constants";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {usePeople} from "../../../hooks/fetches/usePeople";
import {useAccess} from "../../../hooks/useAccess";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import {ICompany} from "../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IUserProfile} from "../../../Interfaces/people.interface";
import {IMutateData, MutateTypes} from "../../../Interfaces/queries.interface";
import styles from "../../../styles/components/profileAbout.module.sass";
import {shuffle} from "../../../utils/array.util";
import {TCompanyAPIParametersArray} from "../../../utils/company.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {removeATags} from "../../../utils/formatString.util";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import Loader from "../../../utils/loader";
import {LinkAndSanitizeRichText} from "../../../utils/miscellaneous";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import ButtonComponent from "../../FormControls/button.component";
import {XIcon} from "../../Icons";
import {ModalComponent} from "../../Modal";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import TitleContainer from "../../Titles/titleContainer.component";
import ProfileButtons, {IEditContent} from "../../VendorProfile/profileButtons.component";
import UpdateInput from "../UpdateInput/updateInput.component";
import FeaturedVideos from "../Videos/featuredVideos.component";

interface IProps {
    profile_type: "user" | "company" | "msp";
}

export default function ProfileAbout(props: IProps) {
    const [contentEdit, setContentEdit] = useState<IEditContent>();
    const [editing, setEditing] = useState<boolean>(false);
    const [deleteConfirmationModal, setDeleteConfirmationModal] = useState(false);
    const [selectedDelUser, setSelectedDelUser] = useState<IUserProfile>();

    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const methods = useForm();
    const {authState} = useAuthState();
    const isCompany = props.profile_type === "company" || props.profile_type === "msp";
    const companyParams = ["users", "company_profile_type"] satisfies TCompanyAPIParametersArray;
    const {
        company,
        updateCompany,
        companyRules,
        isUpdating: isUpdatingCompany,
    } = useCompany(friendly_url, {
        isCompany,
        isMsp: props.profile_type === "msp",
        calls: {company: true, companyRules: true},
        companyParams,
    });
    const {people, updatePeople, peopleRules, isUpdating: isUpdatingPeople} = usePeople(friendly_url, {isCompany});
    const {hasAccess} = useAccess();
    const notify = useNotification();
    const info = isCompany ? company.data : people.data;
    const {hasPermissions, isSuperAdmin} = usePermissions(isCompany ? {overrideCompanyId: info?.id} : undefined);
    const isFreeVendor = (info as ICompany)?.company_profile_type?.value === VENDOR_TYPES.VENDOR_FREE;
    const infoRules = isCompany ? companyRules.data : peopleRules.data;
    const isEditable = authState?.id
        ? isCompany
            ? hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE], {id: info?.id})
            : authState.id === info?.id || isSuperAdmin
        : false;
    const contentSection = {description: "description"};
    const shuffledUsers: IUserProfile[] = (shuffle(info?.users || []) as IUserProfile[]).filter(
        u => u.show_on_vendor_related_section,
    );
    const isUpdating = isCompany ? isUpdatingCompany?.[MutateTypes.Update] : isUpdatingPeople?.[MutateTypes.Update];
    const isDeleting = isCompany ? isUpdatingCompany?.[MutateTypes.HideUser] : false;

    const onSubmit = data => {
        if (data.description === info?.description) return;
        const requestObj: IMutateData = {
            id: info?.id,
            description: isFreeVendor ? removeATags(data.description) : data.description,
            companyParams,
            mutate_type: MutateTypes.Update,
            onSuccess: handleResetContent,
            onError: handleError,
        };
        if (props.profile_type === "company" || props.profile_type === "msp") {
            updateCompany.mutate(requestObj);
        }
        if (props.profile_type === "user") {
            updatePeople.mutate(requestObj);
        }
    };

    const handleError = (err: AxiosError) => {
        const errorCodes = getErrorFromArray(err);
        if (errorCodes.includes(CODE_PROFANITY_ERROR)) {
            notify(CODE_PROFANITY_ERROR, "Error");
        } else {
            notify((err.response as any)?.data?.message || "", "Error");
        }
    };

    const handleResetContent = () => {
        setContentEdit({editCategories: false, editDescription: false, editIndustry: false});
        setEditing(false);
    };

    const resetValueToCurrent = () => {
        methods.reset({
            description: info?.description,
        });
        setEditing(false);
    };

    const handleHideUserSuccess = () => {
        setDeleteConfirmationModal(false);
        setSelectedDelUser(undefined);
    };

    const handleRemoveUser = (userId: string) => {
        updateCompany.mutate({
            hide_user_id: userId,
            company_id: info?.id,
            onSuccess: handleHideUserSuccess,
            mutate_type: MutateTypes.HideUser,
        });
    };

    return (
        <>
            {info?.id ? (
                <div>
                    <FormProvider {...methods}>
                        <form onSubmit={methods.handleSubmit(onSubmit)}>
                            <div className="d-flex align-items-center mb-2">
                                {!contentEdit?.description && (
                                    <div className="d-flex text-center text-md-start align-items-center">
                                        <div className="position-relative">
                                            {!info?.description ? (
                                                "No description set"
                                            ) : (
                                                <div
                                                    dangerouslySetInnerHTML={
                                                        isFreeVendor
                                                            ? {__html: DOMPurify.sanitize(info.description)}
                                                            : LinkAndSanitizeRichText(info.description, {
                                                                  referredLinks: true,
                                                              })
                                                    }
                                                />
                                            )}
                                            {isEditable && infoRules && hasAccess("DESCRIPTION_EDITING", infoRules) && (
                                                <span className={styles.editBtn}>
                                                    <ProfileButtons
                                                        id="editAbout"
                                                        type="edit"
                                                        disabled={isUpdating || editing}
                                                        setEditing={setEditing}
                                                        option={contentSection.description}
                                                        resetValueToCurrent={resetValueToCurrent}
                                                        setContentEdit={setContentEdit}
                                                        contentEdit={contentEdit!}
                                                    />
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                )}
                                {contentEdit?.description && (company.data || people.data) && (
                                    <>
                                        <UpdateInput
                                            id={contentSection.description}
                                            onContentEdit={setContentEdit}
                                            type="rte"
                                            onReset={handleResetContent}
                                            contentEdit={contentEdit!}
                                            maxLength={1500}
                                            placeholder={info?.description ? info.description : "Description"}
                                            loading={isUpdating}
                                            entityInfo={info}
                                        />
                                    </>
                                )}
                            </div>
                            <hr className="fadedBorder my-4" />
                            {props.profile_type !== "msp" && (
                                <FeaturedVideos
                                    entityId={info?.id}
                                    profileType={props.profile_type}
                                    friendlyUrl={friendly_url}
                                />
                            )}
                        </form>
                    </FormProvider>
                    {props.profile_type === "company" && !!shuffledUsers?.length && (
                        <>
                            <hr className="fadedBorder my-4" />
                            <TitleContainer as="h2" text="People" noConnector position="start" />
                            <div className="d-flex flex-wrap gap-2 gap-xl-4 justify-content-evenly justify-content-lg-start">
                                {shuffledUsers.map(
                                    user =>
                                        user.friendly_url && (
                                            <div key={user.id} className="col-auto position-relative">
                                                {isEditable && (
                                                    <ButtonComponent
                                                        id={`removeUser${user.id}`}
                                                        className={styles.deleteBtn}
                                                        disabled={isDeleting}
                                                        type="button"
                                                        onClick={() => {
                                                            setSelectedDelUser(user);
                                                            setDeleteConfirmationModal(true);
                                                        }}>
                                                        {isDeleting ? <Loader inline loading /> : <XIcon size={16} />}
                                                    </ButtonComponent>
                                                )}
                                                <Link
                                                    to={routeConfig.UserProfile.path.replace(":id", user.friendly_url)}>
                                                    <div className={styles.avatarContainer}>
                                                        <img
                                                            src={
                                                                user.avatar ||
                                                                createImageFromInitials(170, user.name, "", "user")
                                                            }
                                                            alt={user.name}
                                                        />
                                                    </div>
                                                    <TitleContainer
                                                        as="h4"
                                                        text={user.name}
                                                        noConnector
                                                        className={styles.userName}
                                                    />
                                                    {user.handle && (
                                                        <SubtitleContainer
                                                            as="span"
                                                            text={`@${user.handle}`}
                                                            className={styles.userHandle}
                                                        />
                                                    )}
                                                </Link>
                                            </div>
                                        ),
                                )}
                            </div>
                        </>
                    )}
                </div>
            ) : (
                <div>
                    <Loader inline loading />
                </div>
            )}
            {selectedDelUser && deleteConfirmationModal && (
                <ModalComponent
                    modalSwitcher={deleteConfirmationModal}
                    modalSwitcherCallback={setDeleteConfirmationModal}
                    showHeader
                    headerTitle="Remove user"
                    form={
                        <div className="d-block my-5 text-center">
                            <p className="fw-400">
                                Are you sure you want to remove <b>{selectedDelUser.name}</b> from <b>{info?.name}</b>?
                            </p>
                        </div>
                    }
                    showCancelButton
                    showOkButton
                    okButtonText={isDeleting ? "Removing..." : "Yes"}
                    cancelButtonText="No"
                    onSuccess={() => handleRemoveUser(selectedDelUser.id)}
                    okButtonDisabled={isDeleting}
                    closeDisabled={isDeleting}
                />
            )}
        </>
    );
}
