import styles from "../../styles/components/profileCard.module.sass";
import {IProfile} from "../../Interfaces/people.interface";
import TitleContainer from "../Titles/titleContainer.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import routeConfig from "../../constants/routeConfig";
import {Link} from "react-router-dom";
import useAppConfig from "../../hooks/useAppConfig";
import {CHANNEL_PROGRAM_COMPANY_ID} from "../../constants/commonStrings.constant";
import ReactIcon from "../Icons/reactIcon.component";
import FollowBtn from "../Buttons/Follow/followBtn.component";
import useAuthState from "../../hooks/useAuthState";
import AvatarComponentV2 from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import {Box} from "@mui/material";

interface IProps {
    user: IProfile;
    smallCard?: boolean;
    className?: string;
    onFollow?: Function;
}

export default function ProfileCard(props: IProps) {
    const {authState} = useAuthState();
    const minimumCount = useAppConfig({config_key: "MINIMUM_FOLLOWING_COUNT"});
    const isChannelProgram = props.user.id === CHANNEL_PROGRAM_COMPANY_ID;
    const isFollowing = props.user.current_user_is_following || authState.following?.find(f => f.id === props.user?.id);
    return (
        <div className={`${styles.container} ${props.className}`}>
            <div className="col-auto d-flex flex-column align-items-center justify-content-center">
                <Link
                    to={
                        props.user.is_company
                            ? routeConfig.VendorProfile.path.replace(":id", props.user?.friendly_url || "")
                            : routeConfig.UserProfile.path.replace(":id", props.user?.friendly_url || "")
                    }>
                    <AvatarComponentV2
                        isCompany={props.user.is_company}
                        user={{
                            avatar: props.user?.avatar || "",
                            profile_type: props.user.profile_type?.value,
                            friendly_url: props.user?.friendly_url || "",
                            name: props.user?.name || "",
                            type: props.user?.type || "",
                            is_distributor: props.user?.is_distributor,
                        }}
                        size={90}
                        showProfileType
                        aligned="bottom"
                        isRedirectEnabled={false}
                    />
                </Link>
            </div>
            <div className={styles.infoColumn}>
                <div className="d-flex flex-column gap-2">
                    <Link
                        to={
                            props.user.is_company
                                ? routeConfig.VendorProfile.path.replace(":id", props.user?.friendly_url || "")
                                : routeConfig.UserProfile.path.replace(":id", props.user?.friendly_url || "")
                        }>
                        <div className={styles.nameContainer}>
                            <TitleContainer
                                as="h3"
                                text={props.user?.name || ""}
                                noConnector
                                position="start"
                                className="mb-0"
                            />
                            {props.user?.handle && (
                                <SubtitleContainer as="p" text={"@" + props.user.handle} position="start" />
                            )}
                        </div>
                    </Link>
                    {props.user?.job_title_name && (
                        <TitleContainer
                            as="h4"
                            text={props.user?.job_title_name}
                            position="start"
                            noConnector
                            className="mb-0"
                        />
                    )}
                    {(props.user?.followers_count ?? 0) > (parseInt(minimumCount.config?.value ?? "") || 50) && (
                        <Box
                            className="d-flex gap-2"
                            sx={{
                                alignItems: "center",
                                "& svg": {
                                    width: "18px",
                                    height: "18px",
                                    fill: "var(--cpNavy)",
                                },
                                "& h4": {
                                    paddingTop: "3px!important",
                                    lineHeight: "20px",
                                },
                            }}>
                            <ReactIcon iconName="HiUserGroup" />
                            <TitleContainer
                                as="h4"
                                text={`${props.user?.followers_count ?? 0} Follower${
                                    props.user?.followers_count === 1 ? "" : "s"
                                }`}
                                position="start"
                                className={"mb-0 " + styles.followersCount}
                                noConnector
                            />
                        </Box>
                    )}
                </div>
                {props.user?.id && props.user.friendly_url && !isFollowing && (
                    <div className={styles.followBtn}>
                        <FollowBtn
                            entityId={props.user?.id}
                            friendlyUrl={props.user?.friendly_url}
                            profile_type={props.user.is_company ? "company" : "user"}
                            isFollowing={isFollowing}
                            preventGlobal
                            variant="cpBlueBtnThin"
                            onHandleSuccess={props.onFollow}
                            showLoader
                        />
                    </div>
                )}
                {(props.user?.current_user_is_following ||
                    isChannelProgram ||
                    authState.following?.find(f => f.id === props.user?.id)) && (
                    <div className={styles.followingHelper}>
                        <ReactIcon iconName="AiOutlineCheckCircle" />
                        <p>Following</p>
                    </div>
                )}
            </div>
        </div>
    );
}
