import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useEffect, useState} from "react";
import {Nav, Tab} from "react-bootstrap";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {useLocation, useNavigate, useSearchParams} from "react-router-dom";
import {ICategory} from "../../../Interfaces/categories.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IPeople} from "../../../Interfaces/people.interface";
import {IVideosLoad} from "../../../Interfaces/profiles.interface";
import {IVideosResponse, MutateTypes} from "../../../Interfaces/queries.interface";
import {MediaTypes} from "../../../enums/mediaTypes.enum";
import {USERS_TYPE} from "../../../enums/usersTypes.enum";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {usePeople} from "../../../hooks/fetches/usePeople";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {categoriesService} from "../../../services/category.service";
import styles from "../../../styles/components/mediaDocs.module.sass";
import {getArrayOfErrors} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import ButtonComponent from "../../FormControls/button.component";
import DropdownComponent from "../../FormControls/dropdown.component";
import AddContentModal from "../../Modal/addContentModal.component";
import PrivacyWarning from "../../Modal/privacyWarning.component";
import {PaginationButton} from "../../Pagination/paginationButton.component";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import ActivityTimeline from "../../VendorProfile/ActivityTimeline.component";
import {VendorDocuments} from "../../VendorProfile/vendorDocuments.component";
import GalleryTab from "../Gallery/GalleryTab.component";
import {BlogTab} from "../blogTab.component";
import VideoCard from "./videoCard.component";
import routeConfig from "../../../constants/routeConfig";
import NewFileDialog from "../../../uicomponents/Organism/NewFileDialog/NewFileDialog.component";

interface IProps {
    profileType: "user" | "company";
    vendorType?: string;
    claimerId?: string;
    parentTabKey?: number;
}

export default function ProfileMediaDocs(props: IProps) {
    const [editVideoModal, setEditVideoModal] = useState(false);
    const [addContentModal, setAddContentModal] = useState(false);
    const [privWarning, setPrivWarning] = useState(false);
    const [editVideoId, setEditVideoId] = useState("");
    const [sorting, setSorting] = useState<"old" | "new">("new");
    const [categoriesOptions, setCategoriesOptions] = useState<Array<ICategory>>([]);
    const [tabKey, setTabKey] = useState<string>("activities");
    const [params, setParams] = useSearchParams();

    const location = useLocation();
    const subTabKey = params.get("subTab");
    const addNew = params.get("add");
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const isCompany = props.profileType === "company";
    const {authState} = useAuthState();
    const {company, updateCompany, companyBlogs, companyDocs, companyGalleries, companyVideos, companyCounts} =
        useCompany(friendly_url, {
            isCompany,
            calls: {
                companyVideos: true,
                companyDocs: true,
                companyBlogs: true,
                companyGalleries: true,
                companyCounts: true,
                company: true,
            },
            companyParams: ["profile_images"],
        });
    const {people, peopleCounts, peopleBlogs, peopleGalleries, peopleVideos, updatePeople} = usePeople(friendly_url, {
        isCompany,
    });
    const navigate = useNavigate();
    const {settings, updateSettings} = useSettings();
    const sortingMethods = useForm();
    const watcher = useWatch({control: sortingMethods.control});
    const {hasPermissions, isSuperAdmin} = usePermissions(
        isCompany ? {overrideCompanyId: company.data?.id} : undefined,
    );
    const info = isCompany ? company?.data : people?.data;
    const counts = isCompany ? companyCounts.data : peopleCounts.data;
    const videos = (isCompany ? companyVideos?.data?.data : peopleVideos?.data?.data) || [];
    const videosPagination: IVideosLoad = (isCompany ? companyVideos?.data : peopleVideos?.data) || ({} as IVideosLoad);
    const documents = (isCompany ? companyDocs.data : []) || [];
    const blogs = (isCompany ? companyBlogs.data : peopleBlogs.data) || [];
    const galleries = (isCompany ? companyGalleries.data : peopleGalleries.data) || [];
    const noContent = !videos?.length && !documents.length && !blogs.length && !galleries.length;
    const sortOptions: Array<[string, string]> = [
        ["new", "Newest first"],
        ["old", "Oldest first"],
    ];
    const isEditable =
        isSuperAdmin ||
        (isCompany
            ? hasPermissions([PERMISSION_GROUPS.MANAGE_CONTENT_UPDATE], {
                  id: info?.id,
              })
            : info?.id === authState?.id);
    const notify = useNotification();

    const handleError = (error: AxiosError<any>) => {
        notify(getArrayOfErrors(error)[0], "Error");
    };

    const onSuccessLoadMore = (response: IVideosResponse) => {
        if (isCompany) {
            updateCompany.mutate({
                mutate_type: MutateTypes.LoadMoreVideos,
                new_videos: response,
            });
        } else {
            updatePeople.mutate({
                mutate_type: MutateTypes.LoadMoreVideos,
                new_videos: response,
            });
        }
    };

    const onSuccessCategories = (response: AxiosResponse<Array<ICategory>>) => {
        if (!response.data || response.data.length === 0) return;

        let newCategories: Array<[string, string]> = [];
        response.data
            .filter(cat => cat.parent_id !== null && !cat.is_hidden)
            .forEach(item => {
                newCategories = [...newCategories, [item.id, item.name]];
            });
        setCategoriesOptions(response.data);
        updateSettings(UPDATE_SETTINGS, {cp_categories: newCategories, all_categories: response.data});
    };

    const fetchCategories = async () => {
        categoriesService.getAllCategories(onSuccessCategories, handleError);
    };

    const loadCategories = async () => {
        if (categoriesOptions.length > 1 || props.profileType === "user") return;
        const tempSettings = sessionStorage.settings && JSON.parse(sessionStorage.settings);
        if (settings.cp_categories) {
            const optionsCategories = settings.cp_categories.map(item => {
                return {id: item[0], name: item[1]};
            });
            return setCategoriesOptions(optionsCategories);
        }
        if (tempSettings?.cp_categories) {
            const optionsCategories = tempSettings.cp_categories.map(item => {
                return {id: item[0], name: item[1]};
            });
            return setCategoriesOptions(optionsCategories);
        }
        fetchCategories();
    };

    const handleEditVideo = (videoId: string) => {
        setEditVideoId(videoId);
        setEditVideoModal(true);
    };

    const handleAddBlogPost = () => {
        navigate(routeConfig.UpsertBlog.path);
        setAddContentModal(false);
    };

    const handleBlogEdit = (blogFriendlyUrl: string) => {
        navigate(routeConfig.UpsertBlog.path + `?blogToEdit=${blogFriendlyUrl}`);
    };

    useEffect(() => {
        setTabKey("activities");
    }, [props.parentTabKey]);

    useEffect(() => {
        if (subTabKey) {
            const subTabObj = {
                docs: "docs",
                videos: "videos",
                blogs: "blogs",
                activities: "activities",
                images: "images",
            };
            setTabKey(subTabObj?.[subTabKey] || "activities");
            return;
        }
    }, [subTabKey]);

    useEffect(() => {
        if (watcher.sortingOrder === "old") {
            setSorting("old");
        }
        if (watcher.sortingOrder === "new") {
            setSorting("new");
        }
    }, [watcher.sortingOrder]);

    useEffect(() => {
        if (!info?.id) return;
        loadCategories();
        // eslint-disable-next-line
    }, [info?.id]);

    useEffect(() => {
        if (addNew === "blog") {
            navigate(routeConfig.UpsertBlog.path);
            setAddContentModal(false);
        } else if (!!addNew) {
            setAddContentModal(true);
        }
    }, [addNew]);

    return (
        <>
            <Tab.Container
                id="vendor-profile-tabs"
                activeKey={tabKey}
                onSelect={k => {
                    const key = !k ? "activities" : k;
                    setTabKey(key);
                    setParams({tab: "content", subTab: key});
                }}>
                <div className={styles.tabSelector}>
                    <Nav className={styles.tabSelector}>
                        <Nav.Item as="li">
                            <Nav.Link as="button" eventKey="videos">
                                Videos ({counts?.videos ? counts.videos : "0"})
                            </Nav.Link>
                        </Nav.Item>
                        {props.profileType === "company" && (
                            <Nav.Item as="li">
                                <Nav.Link as="button" eventKey="docs">
                                    Documents ({counts?.documents ? counts.documents : "0"})
                                </Nav.Link>
                            </Nav.Item>
                        )}
                        {(props.profileType === "company" ||
                            (info as IPeople)?.profile_type === USERS_TYPE.INFLUENCER) && (
                            <Nav.Item as="li">
                                <Nav.Link as="button" eventKey="blogs">
                                    Blogs ({counts?.blogs ? counts.blogs : "0"})
                                </Nav.Link>
                            </Nav.Item>
                        )}
                        <Nav.Item as="li">
                            <Nav.Link as="button" eventKey="images">
                                Images ({counts?.galleries ? counts.galleries : "0"})
                            </Nav.Link>
                        </Nav.Item>
                        {isEditable && (
                            <ButtonComponent
                                onClick={() => setAddContentModal(true)}
                                id="create-content-btn"
                                className={styles.createContentBtn + " cpMainBtnThin"}>
                                Create Content
                            </ButtonComponent>
                        )}
                    </Nav>
                    {info?.id ? (
                        <Tab.Content>
                            <Tab.Pane eventKey="videos" className={styles.tabContent} unmountOnExit mountOnEnter>
                                {noContent ? (
                                    <NoContentDisplay />
                                ) : (
                                    <div className={styles.tabContainer}>
                                        <div className={styles.actionsContainer}>
                                            <div className={styles.filter}>
                                                <FormProvider {...sortingMethods}>
                                                    <DropdownComponent
                                                        id="sortingOrder"
                                                        options={sortOptions}
                                                        containerClassName="m-0"
                                                    />
                                                </FormProvider>
                                            </div>
                                        </div>
                                        <div className={styles.videosContainer}>
                                            {!videos ? (
                                                <div>
                                                    <Loader inline loading />
                                                </div>
                                            ) : videos.length === 0 ? (
                                                <SubtitleContainer as="span" text="No videos" />
                                            ) : sorting === "new" ? (
                                                videos
                                                    ?.sort((a, b) =>
                                                        DateTime.fromISO(b.created_at)
                                                            .diff(DateTime.fromISO(a.created_at))
                                                            .as("minutes"),
                                                    )
                                                    ?.map((video, index) => (
                                                        <VideoCard
                                                            key={index}
                                                            video={video}
                                                            entityId={info?.id}
                                                            isEditable={isEditable}
                                                            ownerType={props.profileType}
                                                            friendlyUrl={friendly_url}
                                                            onEdit={handleEditVideo}
                                                        />
                                                    ))
                                            ) : (
                                                videos
                                                    ?.sort((a, b) =>
                                                        DateTime.fromISO(a.created_at)
                                                            .diff(DateTime.fromISO(b.created_at))
                                                            .as("minutes"),
                                                    )
                                                    ?.map((video, index) => (
                                                        <VideoCard
                                                            key={index}
                                                            video={video}
                                                            entityId={info?.id}
                                                            isEditable={isEditable}
                                                            ownerType={props.profileType}
                                                            friendlyUrl={friendly_url}
                                                            onEdit={handleEditVideo}
                                                        />
                                                    ))
                                            )}
                                        </div>
                                        <div className="text-center mt-4">
                                            {videosPagination?.meta?.current_page !==
                                                videosPagination?.meta?.last_page && (
                                                <PaginationButton
                                                    text="Load More"
                                                    id="loadMoreVideos"
                                                    url={videosPagination?.links?.next!}
                                                    onGetResult={onSuccessLoadMore}
                                                    className="cpMainBtn"
                                                    method="get"
                                                />
                                            )}
                                        </div>
                                    </div>
                                )}
                            </Tab.Pane>
                            {props.profileType === "company" && (
                                <Tab.Pane eventKey="docs" className={styles.tabContent} unmountOnExit mountOnEnter>
                                    {noContent ? (
                                        <NoContentDisplay />
                                    ) : (
                                        <div className={styles.tabContainer}>
                                            {props.profileType === "company" && (
                                                <div className="mt-4">
                                                    <VendorDocuments />
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </Tab.Pane>
                            )}
                            {(props.profileType === "company" ||
                                (info as IPeople)?.profile_type === USERS_TYPE.INFLUENCER) && (
                                <Tab.Pane eventKey="blogs" className={styles.tabContent} unmountOnExit mountOnEnter>
                                    <BlogTab
                                        isCompany={props.profileType === "company" ? true : false}
                                        onEditBlog={handleBlogEdit}
                                    />
                                </Tab.Pane>
                            )}
                            <Tab.Pane eventKey="images" className={styles.tabContent} unmountOnExit mountOnEnter>
                                <div className={styles.tabContainer}>
                                    <GalleryTab entityInfo={info} isCompany={props.profileType === "company"} />
                                </div>
                            </Tab.Pane>
                            <Tab.Pane eventKey="activities" className={styles.tabContent}>
                                <ActivityTimeline
                                    entityInfo={info}
                                    isCompany={props.profileType === "company"}
                                    isEditable={isEditable}
                                />
                            </Tab.Pane>
                        </Tab.Content>
                    ) : (
                        <div className="d-flex justify-content-center">
                            <Loader inline loading />
                        </div>
                    )}
                </div>
            </Tab.Container>
            {editVideoModal && (
                <NewFileDialog
                    onClose={setEditVideoModal}
                    entityId={info?.id || ""}
                    maxSize="500"
                    hasDescription
                    type={MediaTypes.Video}
                    profile_type={props.profileType}
                    fileId={editVideoId}
                    friendlyUrl={friendly_url}
                    vendorType={props.vendorType}
                    useRte
                />
            )}
            {privWarning && <PrivacyWarning privWarning={privWarning} setPrivWarning={setPrivWarning} />}
            {addContentModal && !!info && (
                <AddContentModal
                    show={addContentModal}
                    setShow={setAddContentModal}
                    showAsModal
                    entity={info}
                    isCompany={props.profileType === "company"}
                    onAddBlogPost={handleAddBlogPost}
                />
            )}
        </>
    );
}

const NoContentDisplay = () => (
    <div className="text-center mt-5">
        <span>There’s no content available for now. Come back soon to check when new content is available.</span>
    </div>
);
