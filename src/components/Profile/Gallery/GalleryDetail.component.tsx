import Autolinker from "autolinker";
import React, {useState} from "react";
import {CONTENT_SUBJECT_TYPES} from "../../../constants/subjectTypes.constant";
import {IMediaGallery, IMediaItem} from "../../../Interfaces/mediaGallery.interface";
import styles from "../../../styles/components/galleryTab.module.sass";
import {RichTextSanitizer} from "../../../utils/miscellaneous";
import LikeButton from "../../Buttons/Like/likeBtn.component";
import ImagePreviewModal from "../../Modal/ImagePreviewModal.component";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import TitleContainer from "../../Titles/titleContainer.component";

interface IProps {
    gallery: IMediaGallery;
    goBack: Function;
    friendlyUrl: string;
    isCompany?: boolean;
}

const GalleryDetail: React.FC<IProps> = (props: IProps) => {
    const {gallery} = props;
    const [previewImage, setPreviewImage] = useState<any>();
    return (
        <div className={styles.selectedGalleryContainer}>
            <div className={styles.selectedGalleryTitleSection}>
                <span className={styles.backToGalleriesBtn} onClick={() => props.goBack()}>
                    Back to Galleries
                </span>
                <TitleContainer
                    className={styles.galleryTitle}
                    as="h2"
                    text={gallery.custom_properties.title}
                    noConnector
                />
                <SubtitleContainer
                    className={styles.galleryDate}
                    as="span"
                    text={new Date(gallery.created_at).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                    })}
                />
            </div>
            <SubtitleContainer
                className={styles.postedByText}
                as="span"
                text={"Posted By: " + gallery.author?.author_name}
            />
            <div
                dangerouslySetInnerHTML={RichTextSanitizer(Autolinker.link(gallery.custom_properties.description))}
                className="rteContent"
            />
            <div className={styles.selectedGalleryImagesContainer}>
                {gallery.media?.map((image: IMediaItem) => {
                    return (
                        <div key={image.id} className={styles.imgContainer}>
                            <img
                                src={image.url}
                                alt={image.title}
                                className={styles.galleryImage}
                                onClick={() => {
                                    setPreviewImage({
                                        url: image.url,
                                        title: image.title,
                                        description: gallery.custom_properties.description,
                                    });
                                }}
                            />
                        </div>
                    );
                })}
            </div>
            <div
                className="d-flex flex-row align-items-center justify-content-between py-3 ms-3"
                style={{maxWidth: "400px"}}>
                <LikeButton
                    id="likeGalleryBtn"
                    likedLabel="Like"
                    dislikedLabel="Like"
                    subjectType={CONTENT_SUBJECT_TYPES.MEDIA_GALLERY}
                    subjectId={gallery.id}
                    friendlyUrl={props.friendlyUrl}
                    isCompany={props.isCompany}
                    isActive={gallery.user_has_liked}
                />
                <LikeButton
                    id="likeCountGalleryBtn"
                    showLikes
                    count={gallery.likes_count}
                    subjectId={gallery.id}
                    subjectType={CONTENT_SUBJECT_TYPES.MEDIA_GALLERY}
                    shouldOnlyOpenLikeModal
                    contentType="gallery"
                    isCompany={props.isCompany}
                    friendlyUrl={props.friendlyUrl}
                />
            </div>
            <ImagePreviewModal
                selectedImage={previewImage}
                images={gallery.media.map(i => ({
                    url: i.url,
                    title: i.title,
                    description: gallery.custom_properties.description,
                }))}
                show={!!previewImage}
                setShow={() => setPreviewImage(null)}
            />
        </div>
    );
};

export default GalleryDetail;
