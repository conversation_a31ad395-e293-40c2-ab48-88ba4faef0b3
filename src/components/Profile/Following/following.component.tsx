import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useLocation} from "react-router-dom";
import {IFollowers} from "../../../Interfaces/followers.interface";
import {ILinks, IMeta, IPagination} from "../../../Interfaces/pagination.interface";
import {IProfile} from "../../../Interfaces/people.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {usePeople} from "../../../hooks/fetches/usePeople";
import useNotification from "../../../hooks/useNotification";
import {followUserService} from "../../../services/followUser.service";
import styles from "../../../styles/components/following.module.sass";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import CheckBoxComponent from "../../FormControls/checkBox.component";
import TextBoxComponent from "../../FormControls/textBox.component";
import {PaginationButton} from "../../Pagination/paginationButton.component";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import ProfileCard from "../profileCard.component";

interface IFollowing {
    entityId: string;
    profileType: "user" | "company";
    friendlyUrl?: string;
    claimerId?: string;
}
interface IFollowingResponse {
    data: Array<IFollowers>;
    meta: IMeta;
    links: ILinks;
}
enum userTypes {
    User = "1",
    Influencer = "2",
    Vendor = "3",
}

export default function Following(props: IFollowing) {
    const [loadingRequest, setLoadingRequest] = useState<boolean>(false);
    const [following, setFollowing] = useState<Array<IFollowers>>([]);
    const [pagination, setPagination] = useState<IPagination>();
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const isCompany = props.profileType === "company";
    const {people, updatePeople} = usePeople(friendly_url, {isCompany});
    const info = people.data;
    const notify = useNotification();
    const methods = useForm();
    const searchWatcher = methods.watch("search");
    const userWatcher = methods.watch(userTypes.User);
    const influencerWatcher = methods.watch(userTypes.Influencer);
    const vendorWatcher = methods.watch(userTypes.Vendor);
    const setDefaultFollowings = () => {
        const peopleFollowings = info?.followers;
        if (!peopleFollowings || !peopleFollowings.length) {
            setLoadingRequest(true);
            followUserService.SearchFollowings(
                props.entityId,
                null,
                null,
                handleSuccessGetFollowersInitials,
                handleError,
            );
        } else {
            const currentFollowing: Array<IFollowers> = peopleFollowings;
            setFollowing(currentFollowing);
        }
    };

    const handleSuccessGetFollowersInitials = (response: AxiosResponse<IFollowingResponse>) => {
        setPagination({meta: response.data.meta, links: response.data.links});
        setFollowing(response.data.data);
        setLoadingRequest(false);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setLoadingRequest(false);
    };

    const loadRecords = async () => {
        setLoadingRequest(true);
        switch (props.profileType) {
            case "user":
                followUserService.SearchFollowings(
                    props.entityId,
                    null,
                    null,
                    handleSuccessGetFollowersInitials,
                    handleError,
                );
                break;
            case "company":
                break;
            default:
                break;
        }
    };

    const handleSuccessSearch = (response: AxiosResponse<IFollowingResponse>) => {
        setPagination({meta: response.data.meta, links: response.data.links});
        setLoadingRequest(false);
        setFollowing(response.data.data);
    };

    const onSuccessLoadMore = (response: IFollowingResponse) => {
        const newFollowers: Array<IFollowers> = [...following, ...response.data];
        updatePeople.mutate({followers: newFollowers, mutate_type: MutateTypes.LocalUpdate});
        setLoadingRequest(false);
        setPagination({meta: response.meta, links: response.links});
        setFollowing(newFollowers);
    };

    const buildFilter = () => {
        const filters: Array<string> = [];
        if (userWatcher) filters.push(userTypes.User);
        if (influencerWatcher) filters.push(userTypes.Influencer);
        if (vendorWatcher) filters.push(userTypes.Vendor);
        return filters.length > 0 ? filters : null;
    };

    const checkKeyDown = e => {
        if (e.code === "Enter") e.preventDefault();
    };

    useEffect(() => {
        setLoadingRequest(true);
        if (!props.entityId || props.entityId === "") return;
        const loadDependencies = async () => {
            loadRecords();
        };
        loadDependencies();
        return () => {
            methods.setValue("search", "");
            methods.setValue("1", false);
            methods.setValue("2", false);
            methods.setValue("3", false);
        };
        // eslint-disable-next-line
    }, [props.entityId]);

    useEffect(() => {
        if (!props.entityId || props.entityId === "") return;
        const searchRecords = setTimeout(() => {
            if (!influencerWatcher && !userWatcher && !vendorWatcher && !searchWatcher) {
                return setDefaultFollowings();
            }

            if (props.profileType === "user") {
                setLoadingRequest(true);
                const filter = buildFilter();
                followUserService.SearchFollowings(
                    props.entityId,
                    searchWatcher,
                    filter,
                    handleSuccessSearch,
                    handleError,
                );
            }
        }, 500);
        return () => {
            clearTimeout(searchRecords);
        };
        // eslint-disable-next-line
    }, [searchWatcher, userWatcher, influencerWatcher, vendorWatcher]);

    return (
        <>
            <FormProvider {...methods}>
                <form onKeyDown={e => checkKeyDown(e)}>
                    <div className="d-flex flex-wrap">
                        <div className="d-flex col-12 col-lg-5">
                            <CheckBoxComponent id="2" className={styles.checkBoxInput}>
                                <SubtitleContainer as="p" text="Influencer" />
                            </CheckBoxComponent>
                            <CheckBoxComponent id="1" className={styles.checkBoxInput}>
                                <SubtitleContainer as="p" text="User" />
                            </CheckBoxComponent>
                            <CheckBoxComponent id="3" className={styles.checkBoxInput}>
                                <SubtitleContainer as="p" text="Vendor" />
                            </CheckBoxComponent>
                        </div>
                        <div className="col-12 col-lg-7 text-center text-lg-end mt-4 mt-lg-0">
                            <TextBoxComponent
                                className="mb-2 col-12"
                                containerClassName="d-inline-block w-100"
                                type="text"
                                placeholder="Search"
                                id="search"
                                autoFocus
                            />
                        </div>
                    </div>
                </form>
            </FormProvider>
            {loadingRequest && (
                <div className={styles.loadingRow}>
                    <Loader inline loading />
                </div>
            )}
            {props.entityId && !!following?.length && !loadingRequest && (
                <div className={styles.followingContainer}>
                    <div className="d-flex flex-wrap gap-3 justify-content-center justify-content-md-start">
                        {following.map(item => {
                            const user: IProfile = {
                                id: item.id,
                                name: item.name,
                                avatar: item.avatar?.src || "",
                                type: item.type,
                                is_company: item.is_company,
                                handle: item.handle,
                                friendly_url: item.friendly_url,
                                type_is_of_vendor: item.type_is_of_vendor,
                                job_title_name: item.job_title_name,
                                current_user_is_following: item.current_user_is_following,
                            };
                            return <ProfileCard key={item.id} user={user} className={styles.profileCard} />;
                        })}
                    </div>
                    <div className="text-center mt-4">
                        {pagination?.meta.current_page !== pagination?.meta.last_page && (
                            <PaginationButton
                                text="Load More"
                                id="loadMoreFollow"
                                url={pagination?.links.next!}
                                onGetResult={onSuccessLoadMore}
                                className="cpMainBtn"
                            />
                        )}
                    </div>
                </div>
            )}
            {props.entityId && !loadingRequest && !following?.length && (
                <div>
                    <span>No Followed Profiles</span>
                </div>
            )}
        </>
    );
}
