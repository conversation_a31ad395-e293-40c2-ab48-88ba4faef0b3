import {useEffect, useState} from "react";
import {Tab, Tabs} from "react-bootstrap";
import {useLocation, useSearchParams} from "react-router-dom";
import {usePeople} from "../../../hooks/fetches/usePeople";
import styles from "../../../styles/components/profileTabs.module.sass";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import ProfileAbout from "../About/profileAbout.component";
import Following from "../Following/following.component";
import ProfileMediaDocs from "../Videos/mediaDocs.component";

interface IProps {
    claimerId?: string;
    entityId?: string;
    friendlyUrl?: string;
}

export function UserTabs(props: IProps) {
    const [params, setParams] = useSearchParams();
    const [tabKey, setTabKey] = useState<string>("about");
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const tabQuery = params.get("tab");
    const [subTabRefreshKey, setSubTabRefreshKey] = useState<number>(0);

    const {people} = usePeople(friendly_url);
    const info = people.data;
    // const stackTab =
    //     isAdmin || (authState.authenticated && info && info.type === "ISP_ALL" && authState.id === props.claimerId);

    useEffect(() => {
        if (location.pathname.includes("tab=")) {
            return setTabKey(location.pathname.split("?tab=")[1]);
        }
        if (!tabQuery) {
            return setTabKey("about");
        }
        setTabKey(tabQuery);
    }, [tabQuery, location.pathname]);

    return (
        <div className={styles.tabsContainer}>
            <Tabs
                id="vendor-profile-tabs"
                activeKey={tabKey}
                onSelect={k => {
                    if (k === "about") {
                        setSubTabRefreshKey(subTabRefreshKey + 1);
                    }
                    const key = !k ? "about" : k;
                    setParams({tab: key});
                    setTabKey(key);
                }}
                className={styles.tabSelector}>
                <Tab eventKey="about" title="About" className={styles.tabContent}>
                    <ProfileAbout profile_type="user" />
                </Tab>
                <Tab eventKey="content" title="Content" mountOnEnter className={styles.tabContentAlt}>
                    <ProfileMediaDocs claimerId={props.claimerId} profileType="user" parentTabKey={subTabRefreshKey} />
                </Tab>
                <Tab eventKey="following" title="Following" mountOnEnter className={styles.tabContent}>
                    <Following
                        entityId={info?.id || ""}
                        claimerId={props.claimerId}
                        profileType="user"
                        friendlyUrl={friendly_url}
                    />
                </Tab>
                {/* {stackTab && (
                    <Tab eventKey="myStack" title="My Stack" mountOnEnter className={styles.tabContent}>
                        <MyStack entityId={info?.id || ""} profileType="user" entityHandle={friendly_url} />
                    </Tab>
                )} */}
            </Tabs>
        </div>
    );
}
