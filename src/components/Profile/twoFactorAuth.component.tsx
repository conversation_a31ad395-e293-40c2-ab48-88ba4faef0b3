import {useEffect, useState} from "react";
import useAuthState from "../../hooks/useAuthState";
import useSettings from "../../hooks/useSettings";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {authService} from "../../services/auth.service";
import {CPToast} from "../CPToast";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import OldTwoFactorChallenge from "../Auth/OldTwoFactorChallenge.component";
import {Button, useTheme} from "@mui/material";
import ReplayIcon from "@mui/icons-material/Replay";
import {TwoFactorAuthReset} from "./twoFactorAuthReset.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";

type builderData = {
    builder?: {
        tabLabel?: string;
        paragraph?: string;
        title?: string;
    };
};

export function TwoFactorAuth({builder}: builderData) {
    const [openActivation, setOpenActivation] = useState<boolean>(false);
    const [qrCode, setQRCode] = useState<string | undefined>();
    const [recoveryCodes, setRecoveryCodes] = useState<[]>([]);
    const [twoFAController, setTwoFAController] = useState<boolean>(false);
    const [codeSent, setCodeSent] = useState<boolean>(false);
    const [twoFARemoved, setTwoFARemoved] = useState<boolean>(false);
    const [resetTwoFAAuth, setResetTwoFAAuth] = useState<boolean>(false);
    const theme = useTheme();

    const {authState, updateAuthState} = useAuthState();
    const {updateSettings} = useSettings();

    const twoFAEnabled = authState.twoFA;

    useEffect(() => {
        if (twoFAEnabled) {
            setTwoFAController(true);
            authService.getTwoFARecoveryCodes(handleSuccessRecoveryCode, handleError);
        }
        // eslint-disable-next-line
    }, []);

    const handleChange = () => {
        if (!twoFAController) {
            setTwoFAController(!twoFAController);
            updateSettings(UPDATE_SETTINGS, {loading: true});
            authService.enableTwoFA(handleSuccessTfaEnabled, handleError);
        } else {
            setTwoFAController(!twoFAController);
            updateSettings(UPDATE_SETTINGS, {loading: true});
            authService.deleteTwoFA(handleSuccessTfaDeleted, handleError);
        }
    };

    const handleSuccessTfaEnabled = () => {
        updateSettings(UPDATE_SETTINGS, {loading: true});
        authService.getTwoFARecoveryCodes(handleSuccessRecoveryCode, handleError);
        authService.getTwoFAQRCode(handleSuccessQRCode, handleError);
    };

    const handleSuccessQRCode = (response: any) => {
        setQRCode(response.data?.svg);
        setOpenActivation(true);
        updateSettings(UPDATE_SETTINGS, {loading: false});
    };

    const handleSuccessRecoveryCode = (response: any) => {
        setRecoveryCodes(response.data);
    };

    const handleSuccessTfaDeleted = () => {
        updateAuthState(UPDATE_AUTH, {twoFA: false});
        setRecoveryCodes([]);
        setQRCode(undefined);
        setOpenActivation(false);
        setTwoFARemoved(true);
        updateSettings(UPDATE_SETTINGS, {loading: false});
    };

    const handleError = () => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
    };

    const handleActivationSuccess = () => {
        setOpenActivation(false);
        setCodeSent(true);
        updateAuthState(UPDATE_AUTH, {twoFA: true});
    };

    const onCancel = () => {
        setTwoFAController(false);
        handleChange();
    };

    return (
        <>
            {resetTwoFAAuth && (
                <TwoFactorAuthReset setResetTwoFAAuth={setResetTwoFAAuth} setParentRecoveryCodes={setRecoveryCodes} />
            )}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "column",
                    maxWidth: "100%",
                }}>
                <TitleContainer as="h2" text={builder?.title || "Two Factor Authentication"} />
                <div>
                    <SubtitleContainer
                        as="p"
                        text={
                            'Clicking "Reset" will reset your two-factor authentication, generate a new QR code, and provide new recovery codes.'
                        }
                        size="16"
                    />
                </div>
                <Button
                    variant="text"
                    startIcon={<ReplayIcon />}
                    sx={{
                        color: theme.palette.blue[600],
                        fontWeight: "bold",
                        fontSize: "16px",
                        textTransform: "uppercase",
                        "&:hover": {
                            backgroundColor: "transparent",
                        },
                    }}
                    onClick={() => setResetTwoFAAuth(true)}>
                    Reset
                </Button>

                {twoFAController && (
                    <div>
                        <div>
                            {openActivation && (
                                <OldTwoFactorChallenge
                                    access_token={authState.access_token || ""}
                                    decodedHeader={authState}
                                    qrCode={qrCode}
                                    firstConfig
                                    codeSent={codeSent}
                                    onSuccess={handleActivationSuccess}
                                    onCancel={onCancel}
                                />
                            )}
                        </div>
                        <div className="recoveryCodesContainer">
                            <p className="mb-3">
                                <b>Recovery Codes</b>
                            </p>
                            <SubtitleContainer as="p" size="18" className="mt-3 text-uppercase">
                                It's crucial to securely store your recovery codes in a location that is both safe and
                                easily accessible to you.
                            </SubtitleContainer>

                            {recoveryCodes.length > 1 &&
                                recoveryCodes.map((val: string, index: number) => (
                                    <SubtitleContainer
                                        key={index}
                                        text={val}
                                        as="p"
                                        position="start"
                                        className="my-2"
                                    />
                                ))}
                        </div>
                    </div>
                )}
                <CPToast
                    type="light"
                    toastShow={codeSent}
                    setToastShow={setCodeSent}
                    description="Two factor authentication set"
                    title="Settings"
                />
                <CPToast
                    type="light"
                    toastShow={twoFARemoved}
                    setToastShow={setTwoFARemoved}
                    description="Two factor authentication removed"
                    title="Settings"
                />
            </Box>
        </>
    );
}
