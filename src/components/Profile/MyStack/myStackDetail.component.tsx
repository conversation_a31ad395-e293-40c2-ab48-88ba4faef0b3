import styles from "../../../styles/components/myStack.module.sass";
import {Image} from "react-bootstrap";
import Loader from "../../../utils/loader";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import useAuthState from "../../../hooks/useAuthState";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../../constants/routeConfig";
import {IVendorStack} from "../../../Interfaces/myStack.interface";
import AddStackBtn from "../../Buttons/addStackBtn.component";

interface IMyStackDetail {
    vendor: IVendorStack;
    onHandleSuccess?: Function;
}
export default function MyStackDetail(props: IMyStackDetail) {
    const {authState} = useAuthState();

    const navigate = useNavigate();
    const followerPicture =
        props.vendor?.avatar?.src || createImageFromInitials(150, props.vendor?.name || props.vendor?.name, "#062556");

    const goToProfile = (entity: IVendorStack) => {
        return navigate(routeConfig.VendorProfile.path.replace(":id", entity.friendly_url || entity.id), {
            state: {entityId: entity.id},
        });
    };

    const onHandleSuccess = (vendorId: string) => {
        if (!props.onHandleSuccess) return;
        props.onHandleSuccess(vendorId);
    };

    return (
        <div className={styles.cardFollower}>
            <div className={`badge-minimal-container ${styles.badgeContainer} ${styles.badgeRegular}`}>
                <div className={`badge-minimal-type ${styles.badgeVendor}`}>
                    <span className="badge-minimal-span ">{"VENDOR"}</span>
                </div>
            </div>
            <div className={styles.profileInformation}>
                <div className={styles.followerImage} onClick={() => goToProfile(props.vendor)}>
                    {props.vendor.name ? (
                        <Image src={followerPicture} roundedCircle />
                    ) : (
                        <div className={styles.imageLoader}>
                            <Loader inline loading version="iconWhite" />
                        </div>
                    )}
                </div>
                <div className={styles.textContainer} onClick={() => goToProfile(props.vendor)}>
                    <span className={styles.followerName}>{props.vendor.name}</span>
                    {props.vendor.handle && <span className={styles.handlerText}>@{props.vendor.handle}</span>}
                </div>
            </div>
            <div className={styles.followBtnContainer}>
                {authState.authenticated && !authState.type_is_of_vendor && (
                    <AddStackBtn
                        profile_type="company"
                        entityId={props.vendor.id}
                        onHandleSuccess={onHandleSuccess}
                        loadingIcon="iconBlue"
                    />
                )}
            </div>
        </div>
    );
}
