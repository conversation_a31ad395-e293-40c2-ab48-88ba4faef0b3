import {useState, useEffect} from "react";
import styles from "../../../styles/components/myStack.module.sass";
import useNotification from "../../../hooks/useNotification";
import TextBoxComponent from "../../FormControls/textBox.component";
import {AxiosError, AxiosResponse} from "axios";
import {FormProvider, useForm} from "react-hook-form";
import {IVendorStack} from "../../../Interfaces/myStack.interface";
import {IPagination, IMeta, ILinks} from "../../../Interfaces/pagination.interface";
import Loader from "../../../utils/loader";
import MyStackDetail from "./myStackDetail.component";
import {PaginationButton} from "../../Pagination/paginationButton.component";
import {stackService} from "../../../services/stack.service";
import {getErrorFromArray} from "../../../utils/error.util";
import useAuthState from "../../../hooks/useAuthState";
interface IMyStack {
    entityId: string;
    profileType: "user" | "company";
    entityHandle?: string;
}
interface IStackResponse {
    data: IVendorStack[];
    meta: IMeta;
    links: ILinks;
}
export default function MyStack(props: IMyStack) {
    const [loadingRequest, setLoadingRequest] = useState<boolean>(false);
    const [myStack, setMyStack] = useState<Array<IVendorStack>>([]);
    const [filteredStack, setFilteredStack] = useState<Array<IVendorStack>>([]);
    const [pagination, setPagination] = useState<IPagination>();

    const {authState} = useAuthState();
    const methods = useForm();
    const notify = useNotification();
    const searchWatch = methods.watch("search");

    const handleSuccessLoadMore = (response: IStackResponse) => {
        let newStack: Array<IVendorStack> = [...myStack];
        response.data.map(f => {
            return (newStack = [...newStack, f]);
        });
        setMyStack(newStack);
        setFilteredStack(newStack);
        setLoadingRequest(false);
        setPagination({meta: response.meta, links: response.links});
    };

    const handleSuccessLoadStack = (response: AxiosResponse<IStackResponse>) => {
        setMyStack(response.data.data);
        setFilteredStack(response.data.data);
        setPagination({meta: response.data.meta, links: response.data.links});
        setLoadingRequest(false);
    };

    const handleSuccesRemoveStack = (vendorId: string) => {
        if (props.entityId !== authState.id) return;

        const newArray = myStack.filter(ms => ms.id !== vendorId);
        setMyStack(newArray);
        setFilteredStack(newArray);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setLoadingRequest(false);
    };

    const loadStack = () => {
        setLoadingRequest(true);
        stackService.getUserStack(props.entityId, handleSuccessLoadStack, handleError);
    };

    const filterRecords = () => {
        const criteria: string = searchWatch;
        const newStacks = myStack.filter(ms => ms.name.toLowerCase().startsWith(criteria.toLowerCase()));
        setFilteredStack(newStacks);
    };

    useEffect(() => {
        loadStack();
        // eslint-disable-next-line
    }, [props.entityId]);

    useEffect(() => {
        filterRecords();
        // eslint-disable-next-line
    }, [searchWatch]);

    return (
        <>
            <FormProvider {...methods}>
                <form>
                    <div className="col-12 text-end">
                        <TextBoxComponent
                            className="mb-2 col-12"
                            containerClassName="d-inline-block w-100"
                            type="text"
                            placeholder="Search"
                            id="search"
                            autoFocus
                            autoComplete="off"
                        />
                    </div>
                </form>
                {loadingRequest && (
                    <div className={styles.loadingRow}>
                        <Loader inline loading />
                    </div>
                )}
                {props.entityId && myStack && myStack?.length > 0 && !loadingRequest && (
                    <div className={styles.myStackContainer}>
                        <div className="d-flex flex-wrap">
                            {filteredStack.map(item => (
                                <MyStackDetail key={item.id} vendor={item} onHandleSuccess={handleSuccesRemoveStack} />
                            ))}
                        </div>
                        <div className="text-center mt-4">
                            {pagination?.meta.current_page !== pagination?.meta.last_page && (
                                <PaginationButton
                                    text="Load More"
                                    id="loadMoreStack"
                                    url={pagination?.links.next!}
                                    onGetResult={handleSuccessLoadMore}
                                    className="cpMainBtn"
                                />
                            )}
                        </div>
                    </div>
                )}
                {props.entityId && !loadingRequest && myStack.length === 0 && (
                    <div>
                        <span>No vendors</span>
                    </div>
                )}
            </FormProvider>
        </>
    );
}
