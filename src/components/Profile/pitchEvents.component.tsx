import {useEffect, useState} from "react";
import {Form} from "react-bootstrap";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {authService} from "../../services/auth.service";
import TitleContainer from "../Titles/titleContainer.component";

type builderData = {
    builder?: {
        tabLabel?: string;
        title?: string;
    };
};

export function PitchEvents({builder}: builderData) {
    const [registeredAllPitches, setRegisteredAllPitches] = useState<boolean>(false);
    const {authState, updateAuthState} = useAuthState();
    const {updateSettings} = useSettings();
    const notify = useNotification();

    useEffect(() => {
        if (authState.registered_all_pitches) {
            setRegisteredAllPitches(authState.registered_all_pitches);
        }
        // eslint-disable-next-line
    }, []);

    const handleChange = () => {
        const updatedRegister = !registeredAllPitches;
        setRegisteredAllPitches(updatedRegister);
        updateSettings(UPDATE_SETTINGS, {loading: true});
        authService.registerForAllFuturesPitches(
            updatedRegister,
            () => handleSuccessRegisteredAllPitches(updatedRegister),
            () => handleError(updatedRegister),
        );
    };

    const handleSuccessRegisteredAllPitches = updatedRegister => {
        updateAuthState(UPDATE_AUTH, {registered_all_pitches: updatedRegister});
        updateSettings(UPDATE_SETTINGS, {loading: false});
        notify(
            updatedRegister
                ? "Auto register for all pitch events enabled"
                : "Auto register for all pitch events disabled",
            "Success",
        );
    };

    const handleError = updatedRegister => {
        setRegisteredAllPitches(!updatedRegister);
        updateSettings(UPDATE_SETTINGS, {loading: false});
        notify("There was an error when trying to switch auto register", "Error");
    };

    return (
        <div className="d-flex align-items-center justify-content-center flex-column col-12 col-lg-6">
            <TitleContainer as="h2" text={builder?.title || "Auto registered for all pitch events"} className="mb-5" />

            <Form.Check
                type="switch"
                onChange={handleChange}
                id="registeredAllPitches"
                checked={registeredAllPitches}
                label={registeredAllPitches ? <p>On</p> : <p>Off</p>}
                className="switcher mb-3"
            />
        </div>
    );
}
