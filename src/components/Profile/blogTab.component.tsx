import {useLocation} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import {useCompany} from "../../hooks/fetches/useCompany";
import {usePeople} from "../../hooks/fetches/usePeople";
import useAuthState from "../../hooks/useAuthState";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import styles from "../../styles/components/blogTab.module.sass";
import Loader from "../../utils/loader";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import BlogCard from "../Cards/blogCard.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";

interface IProps {
    onEditBlog: (blogFriendlyUrl: string) => void;
    isCompany: boolean;
}

export function BlogTab(props: IProps) {
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const {authState} = useAuthState();
    const {companyBlogs} = useCompany(friendly_url, {isCompany: props.isCompany});
    const {peopleBlogs} = usePeople(friendly_url, {isCompany: props.isCompany});
    const {hasPermissions} = usePermissions();
    const blogs = props.isCompany ? companyBlogs.data : peopleBlogs.data;
    const isEditable = authState?.id
        ? hasPermissions([PERMISSION_GROUPS.MANAGE_CONTENT_UPDATE], {id: friendly_url})
        : false;
    const isLoading = props.isCompany ? companyBlogs.isLoading : peopleBlogs.isLoading;

    return !!blogs?.length ? (
        <div className="d-flex gap-4 flex-wrap mt-4 justify-content-evenly justify-content-lg-start mt-5">
            {blogs?.map(blog => (
                <BlogCard
                    info={blog}
                    link={
                        blog.friendly_url ? routeConfig.PublicBlog.path.replace(":friendly_url", blog.friendly_url) : ""
                    }
                    key={blog.id}
                    className={styles.blogCard}
                    onEdit={() => props.onEditBlog(blog.friendly_url!)}
                    isEditable={isEditable}
                    isCompany={props.isCompany}
                    entityId={blog.subject?.id || blog.author.id}
                />
            ))}
        </div>
    ) : isLoading ? (
        <div className="my-5 text-center">
            <Loader inline loading big />
        </div>
    ) : (
        <div className="my-5">
            <SubtitleContainer as="span" text="No blogs posted" position="start" />
        </div>
    );
}
