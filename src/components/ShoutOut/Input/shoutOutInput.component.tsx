import {FormProvider, useForm} from "react-hook-form";
import {useLocation, useSearchParams} from "react-router-dom";
import {ICompany} from "../../../Interfaces/company.interface";
import {IShoutOut} from "../../../Interfaces/contentFeed.interface";
import {IPeople} from "../../../Interfaces/people.interface";
import {IMutateData, MutateTypes} from "../../../Interfaces/queries.interface";
import {IClaimedCompanies} from "../../../contexts/auth.context";
import useShoutOutMutation from "../../../hooks/mutations/useShoutoutMutation";
import useNotification from "../../../hooks/useNotification";
import {stripHtml} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";
import ButtonComponent from "../../FormControls/button.component";
import TextBoxComponent from "../../FormControls/textBox.component";
import styles from "./shoutOutInput.module.sass";

interface IProps {
    entity: Pick<ICompany | IPeople | IClaimedCompanies, "id" | "friendly_url">;
    isCompany: boolean;
    defaultShoutOut?: IShoutOut;
    className?: string;
    noEmojiSearch?: boolean;
    showCancel?: boolean;
    onCancel?: () => void;
    pageIndex?: number;
}

interface IShoutOutForm {
    shoutOut: string;
}

export default function ShoutOutInput(props: IProps) {
    const [, setParams] = useSearchParams();
    const notify = useNotification();
    const {updateShouOut, isUpdating} = useShoutOutMutation();
    const shoutOutMethods = useForm<IShoutOutForm>({
        defaultValues: {
            shoutOut: props.defaultShoutOut?.shout_out || "",
        },
    });
    const location = useLocation();
    const isProfilePage =
        location.pathname.includes("/v/") || location.pathname.includes("/u/") || location.pathname.includes("/i/");

    const handleStoreShoutOutSuccess = () => {
        shoutOutMethods.setValue("shoutOut", "");
        if (isProfilePage) {
            setParams({tab: "content"});
        }
        props.onCancel?.();
    };

    const handlePostShoutOut = (data: IShoutOutForm) => {
        const message: string = stripHtml(data.shoutOut);
        if (!message) {
            notify("Please enter a message.", "Error");
            return;
        }
        if (message.length > 255) {
            notify("Posts can only be 255 characters.", "Error");
            return;
        }
        //? handle as the key, profile link as the value
        const item: IMutateData = {
            ...props.entity,
            shout_out: message,
            id: props.defaultShoutOut?.id,
            shout_out_id: props.defaultShoutOut?.id,
            mutate_type: !!props.defaultShoutOut ? MutateTypes.UpdateShoutOut : MutateTypes.AddShoutOut,
            friendly_url: props.entity?.friendly_url,
            is_company: !!props.isCompany,
            pageIndex: props.pageIndex,
            subject_id: props.entity?.id,
            updated_at: new Date().toISOString(),
            onSuccess: handleStoreShoutOutSuccess,
        };
        updateShouOut.mutate(item);
    };

    return (
        <>
            <FormProvider {...shoutOutMethods}>
                <form
                    className={styles.shoutOutForm + " " + props.className}
                    onSubmit={shoutOutMethods.handleSubmit(handlePostShoutOut)}>
                    <TextBoxComponent
                        id="shoutOut"
                        defaultValue={props.defaultShoutOut?.shout_out || ""}
                        placeholder="Post to Your Timeline..."
                    />
                    <div className="d-flex justify-content-center gap-2">
                        {props.showCancel && (
                            <ButtonComponent
                                id="cancelPostBtn"
                                disabled={isUpdating}
                                onClick={() => {
                                    props.onCancel?.();
                                }}
                                className="cpBlueBtnThin ms-3 text-center">
                                Cancel
                            </ButtonComponent>
                        )}
                        <ButtonComponent
                            type="submit"
                            id="postBtn"
                            disabled={isUpdating}
                            className="cpBlueBtnThin ms-3 text-center">
                            {isUpdating ? (
                                <Loader inline loading version="iconWhite" style={{display: "block"}} />
                            ) : (
                                "Post"
                            )}
                        </ButtonComponent>
                    </div>
                </form>
            </FormProvider>
        </>
    );
}
