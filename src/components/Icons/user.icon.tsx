import {ILogoIcon} from "../../models/Icons.interface";

export function UserIcon(props: ILogoIcon) {
    const LogoSvg = {
        mono: (
            <svg xmlns="http://www.w3.org/2000/svg" width="15.75" height="18" viewBox="0 0 15.75 18">
                <path
                    id="user_icon"
                    data-name="User icon"
                    d="M11.039-8.051a4.307,4.307,0,0,1-3.164,1.3A4.336,4.336,0,0,1,4.693-8.068,4.336,4.336,0,0,1,3.375-11.25a4.336,4.336,0,0,1,1.318-3.182A4.336,4.336,0,0,1,7.875-15.75a4.336,4.336,0,0,1,3.182,1.318,4.336,4.336,0,0,1,1.318,3.182A4.365,4.365,0,0,1,11.039-8.051Zm0,2.426a4.533,4.533,0,0,1,3.322,1.389A4.533,4.533,0,0,1,15.75-.914V.563a1.627,1.627,0,0,1-.492,1.2,1.627,1.627,0,0,1-1.2.492H1.688a1.627,1.627,0,0,1-1.2-.492A1.627,1.627,0,0,1,0,.563V-.914A4.533,4.533,0,0,1,1.389-4.236,4.533,4.533,0,0,1,4.711-5.625h.6a6.105,6.105,0,0,0,2.566.563,6.105,6.105,0,0,0,2.566-.562Z"
                    transform="translate(0 15.75)"
                />
            </svg>
        ),
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" width="15.75" height="18" viewBox="0 0 15.75 18">
                <path
                    id="user_icon"
                    data-name="User icon"
                    d="M11.039-8.051a4.307,4.307,0,0,1-3.164,1.3A4.336,4.336,0,0,1,4.693-8.068,4.336,4.336,0,0,1,3.375-11.25a4.336,4.336,0,0,1,1.318-3.182A4.336,4.336,0,0,1,7.875-15.75a4.336,4.336,0,0,1,3.182,1.318,4.336,4.336,0,0,1,1.318,3.182A4.365,4.365,0,0,1,11.039-8.051Zm0,2.426a4.533,4.533,0,0,1,3.322,1.389A4.533,4.533,0,0,1,15.75-.914V.563a1.627,1.627,0,0,1-.492,1.2,1.627,1.627,0,0,1-1.2.492H1.688a1.627,1.627,0,0,1-1.2-.492A1.627,1.627,0,0,1,0,.563V-.914A4.533,4.533,0,0,1,1.389-4.236,4.533,4.533,0,0,1,4.711-5.625h.6a6.105,6.105,0,0,0,2.566.563,6.105,6.105,0,0,0,2.566-.562Z"
                    transform="translate(0 15.75)"
                    fill="#ff6120"
                />
            </svg>
        ),
    };
    return (
        <div className="d-flex justify-content-center mx-2" style={{maxWidth: props.size, minWidth: props.size}}>
            {LogoSvg[props.color || "mono"]}
        </div>
    );
}
