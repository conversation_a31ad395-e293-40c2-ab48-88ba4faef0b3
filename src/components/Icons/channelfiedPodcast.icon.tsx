import {ILogoIcon} from "../../models/Icons.interface";

export function ChannelfiedPodcastIcon(props: ILogoIcon) {
    const LogoSvg = {
        mono: (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 35.62 39.36" width="100%" height="100%">
                <g id="Camada_2" data-name="Camada 2">
                    <g id="Layer_1" data-name="Layer 1">
                        <path d="M11.88,34.53A17.75,17.75,0,0,1,.36,14.34,18.08,18.08,0,0,1,18,0c8.2.1,15.9,6.32,17.24,13.91,1.7,9.64-2.72,17.56-11.62,20.75-.95-2.44,1.13-2.86,2.5-3.77A15.36,15.36,0,0,0,32.39,13,15.06,15.06,0,0,0,17.17,2.65,15.13,15.13,0,0,0,2.74,15C1.43,21,4.22,27.32,9.83,31,11.14,31.82,12.94,32.34,11.88,34.53Z" />
                        <path d="M24.33,27.4c-.82-1.79.12-2.77.9-3.93,2.94-4.33,2.36-9.13-1.37-12.42a9.27,9.27,0,0,0-12-.19,9.13,9.13,0,0,0-1.76,12.4c.82,1.27,1.92,2.33,1.05,4C7,25,5.11,20.34,6.23,15.29A11.62,11.62,0,0,1,17,6.49,11.5,11.5,0,0,1,28.8,14.35,11,11,0,0,1,24.33,27.4Z" />
                        <path d="M13.53,28.81c0-2-.3-4,1.51-5.48,2.42-2,6.3-.89,6.75,2.19.58,4.06.41,8.2-1.69,11.91-1.46,2.59-3.2,2.56-4.7,0S13.63,31.79,13.53,28.81Z" />
                        <path d="M22,16.85c-.24,2.56-1.6,4-4.28,4s-4.18-1.47-4.18-4.13c0-2.82,1.6-4.34,4.44-4.26S21.91,14.2,22,16.85Z" />
                    </g>
                </g>
            </svg>
        ),
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 35.62 39.36" width="100%" height="100%">
                <g id="Camada_2" data-name="Camada 2">
                    <g id="Layer_1" data-name="Layer 1">
                        <path
                            fill="#fe6222"
                            d="M11.88,34.53A17.75,17.75,0,0,1,.36,14.34,18.08,18.08,0,0,1,18,0c8.2.1,15.9,6.32,17.24,13.91,1.7,9.64-2.72,17.56-11.62,20.75-.95-2.44,1.13-2.86,2.5-3.77A15.36,15.36,0,0,0,32.39,13,15.06,15.06,0,0,0,17.17,2.65,15.13,15.13,0,0,0,2.74,15C1.43,21,4.22,27.32,9.83,31,11.14,31.82,12.94,32.34,11.88,34.53Z"
                        />
                        <path
                            fill="#fe6323"
                            d="M24.33,27.4c-.82-1.79.12-2.77.9-3.93,2.94-4.33,2.36-9.13-1.37-12.42a9.27,9.27,0,0,0-12-.19,9.13,9.13,0,0,0-1.76,12.4c.82,1.27,1.92,2.33,1.05,4C7,25,5.11,20.34,6.23,15.29A11.62,11.62,0,0,1,17,6.49,11.5,11.5,0,0,1,28.8,14.35,11,11,0,0,1,24.33,27.4Z"
                        />
                        <path
                            fill="#fe6221"
                            d="M13.53,28.81c0-2-.3-4,1.51-5.48,2.42-2,6.3-.89,6.75,2.19.58,4.06.41,8.2-1.69,11.91-1.46,2.59-3.2,2.56-4.7,0S13.63,31.79,13.53,28.81Z"
                        />
                        <path
                            fill="#fe6323"
                            d="M22,16.85c-.24,2.56-1.6,4-4.28,4s-4.18-1.47-4.18-4.13c0-2.82,1.6-4.34,4.44-4.26S21.91,14.2,22,16.85Z"
                        />
                    </g>
                </g>
            </svg>
        ),
    };
    return (
        <div
            className="d-flex justify-content-center align-content-center mx-2"
            style={{maxWidth: props.size, minWidth: props.size, height: props.size}}>
            {LogoSvg[props.color]}
        </div>
    );
}
