interface IRecordCamera {
    size: number;
    className?: string;
}

export const ThreeQuarterStar = (props: IRecordCamera) => {
    return (
        <div className={`${props.className}`} style={{maxWidth: props.size, minWidth: props.size}}>
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="none" viewBox="0 0 16 16">
                <path
                    fill="var(--cpYellow)"
                    d="M10.2424 5.41369L8.44843 1.77872C8.35636 1.59217 8.17697 1.49926 7.99793 1.5C7.82028 1.50071 7.64296 1.59362 7.55161 1.77872L5.75765 5.41369L1.74621 5.99659C1.3361 6.05618 1.17235 6.56016 1.46911 6.84943L4.37181 9.67887L3.68657 13.6741C3.63115 13.9972 3.88785 14.2596 4.17912 14.2597C4.25608 14.2598 4.33546 14.2415 4.41213 14.2012L8.00002 12.3149L11.5879 14.2012C11.651 14.2343 11.7159 14.2526 11.7798 14.258C11.8075 14.2603 11.835 14.2603 11.862 14.258C12.1358 14.2346 12.3663 13.9819 12.3135 13.6741L11.6282 9.67887L14.5309 6.84943C14.8277 6.56016 14.6639 6.05618 14.2538 5.99659L10.2424 5.41369ZM10.0001 12.2366V6.389L13.1074 6.84051L10.7419 9.14626C10.6241 9.26113 10.5703 9.42663 10.5981 9.58883L11.1565 12.8446L10.0001 12.2366Z"
                />
            </svg>
        </div>
    );
};
