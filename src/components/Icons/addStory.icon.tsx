import {IIcons} from "../../models/Icons.interface";

export function AddStoryIcon(props: IIcons) {
    return (
        <div
            onClick={props.onClick}
            className={`${props.className} d-flex justify-content-center mx-2`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M15.436 0.5C19.063 0.5 21.5 3.046 21.5 6.835V15.665C21.5 19.454 19.063 22 15.436 22H6.064C2.437 22 0 19.454 0 15.665V6.835C0 3.046 2.437 0.5 6.064 0.5H15.436ZM15.436 2H6.064C3.292 2 1.5 3.897 1.5 6.835V15.665C1.5 18.603 3.292 20.5 6.064 20.5H15.436C18.209 20.5 20 18.603 20 15.665V6.835C20 3.897 18.209 2 15.436 2ZM10.75 6.8273C11.164 6.8273 11.5 7.1633 11.5 7.5773V10.49L14.4165 10.4902C14.8305 10.4902 15.1665 10.8262 15.1665 11.2402C15.1665 11.6542 14.8305 11.9902 14.4165 11.9902L11.5 11.99V14.9043C11.5 15.3183 11.164 15.6543 10.75 15.6543C10.336 15.6543 10 15.3183 10 14.9043V11.99L7.0835 11.9902C6.6685 11.9902 6.3335 11.6542 6.3335 11.2402C6.3335 10.8262 6.6685 10.4902 7.0835 10.4902L10 10.49V7.5773C10 7.1633 10.336 6.8273 10.75 6.8273Z"
                    fill="black"
                />
            </svg>
        </div>
    );
}
