import {IIcons} from "../../models/Icons.interface";

export function MuiCalendarIcon(props: IIcons) {
    return (
        <div
            className={`d-flex justify-content-center ${props.className}`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clipPath="url(#clip0_1915_6821)">
                    <path
                        d="M19 3H18V1H16V3H8V1H6V3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V9H19V19ZM5 7V5H19V7H5ZM10.56 17.46L16.49 11.53L15.43 10.47L10.56 15.34L8.45 13.23L7.39 14.29L10.56 17.46Z"
                        fill="white"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_1915_6821">
                        <rect width="24" height="24" rx="12" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
}
