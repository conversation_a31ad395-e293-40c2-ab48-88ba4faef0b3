interface ICalendar {
    size: number;
    className?: string;
}

export function CategoryIcon(props: ICalendar) {
    return (
        <div
            className={`d-flex justify-content-center mx-2 ${props.className}`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            <svg
                version="1.1"
                id={`categoryIcon${Math.random() * 100}`}
                xmlns="http://www.w3.org/2000/svg"
                xmlnsXlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 28 28"
                xmlSpace="preserve">
                <g>
                    <path d="M15,18v-4h-4v8l4,0.001V20h8v8h-8v-4H9V8H5V0h8v8h-2v4h4v-2h8v8H15z" />
                </g>
            </svg>
        </div>
    );
}
