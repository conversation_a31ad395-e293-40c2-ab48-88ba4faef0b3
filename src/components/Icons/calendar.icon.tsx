interface ICalendar {
    size: number;
    type?: "check" | "plus";
    className?: string;
}

export function CalendarIcon(props: ICalendar) {
    const CalendarSvg = {
        check: (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path d="M19 4h-2V2h-2v2H9V2H7v2H5c-1.103 0-2 .897-2 2v14c0 1.103.897 2 2 2h14c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm.002 16H5V8h14l.002 12z"></path>
                <path d="m11 17.414 5.707-5.707-1.414-1.414L11 14.586l-2.293-2.293-1.414 1.414z"></path>
            </svg>
        ),
        plus: (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path d="M8 15h3v3h2v-3h3v-2h-3v-3h-2v3H8z"></path>
                <path d="M19 4h-2V2h-2v2H9V2H7v2H5c-1.103 0-2 .897-2 2v14c0 1.103.897 2 2 2h14c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm.002 16H5V8h14l.002 12z"></path>
            </svg>
        ),
    };
    return (
        <div
            className={`d-flex justify-content-center mx-2 ${props.className}`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            {props.type ? (
                CalendarSvg[props.type]
            ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="15.75" height="18" viewBox="0 0 15.75 18">
                    <path
                        id="Calendar_icon"
                        data-name="Calendar icon"
                        d="M14.063-15.25a1.627,1.627,0,0,1,1.2.492,1.627,1.627,0,0,1,.492,1.2V-1.187a1.627,1.627,0,0,1-.492,1.2,1.627,1.627,0,0,1-1.2.492H1.688A1.627,1.627,0,0,1,.492.008,1.627,1.627,0,0,1,0-1.187V-13.563a1.627,1.627,0,0,1,.492-1.2,1.627,1.627,0,0,1,1.2-.492H3.375v-1.828A.373.373,0,0,1,3.8-17.5H5.2a.373.373,0,0,1,.422.422v1.828h4.5v-1.828a.373.373,0,0,1,.422-.422h1.406a.373.373,0,0,1,.422.422v1.828ZM13.852-1.187a.186.186,0,0,0,.211-.211V-11.875H1.688V-1.4a.186.186,0,0,0,.211.211Z"
                        transform="translate(0 17.5)"
                    />
                </svg>
            )}
        </div>
    );
}
