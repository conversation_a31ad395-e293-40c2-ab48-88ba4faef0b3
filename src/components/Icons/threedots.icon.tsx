import {IIcons} from "../../models/Icons.interface";

export function ThreeDotsIconBlack(props: IIcons) {
    return (
        <div
            onClick={props.onClick}
            className={`${props.className} d-flex justify-content-center mx-2`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="1" y="1" width="24" height="24" rx="12" fill="white" />
                <path
                    d="M8.99999 11.6666C8.26666 11.6666 7.66666 12.2666 7.66666 13C7.66666 13.7333 8.26666 14.3333 8.99999 14.3333C9.73332 14.3333 10.3333 13.7333 10.3333 13C10.3333 12.2666 9.73332 11.6666 8.99999 11.6666ZM17 11.6666C16.2667 11.6666 15.6667 12.2666 15.6667 13C15.6667 13.7333 16.2667 14.3333 17 14.3333C17.7333 14.3333 18.3333 13.7333 18.3333 13C18.3333 12.2666 17.7333 11.6666 17 11.6666ZM13 11.6666C12.2667 11.6666 11.6667 12.2666 11.6667 13C11.6667 13.7333 12.2667 14.3333 13 14.3333C13.7333 14.3333 14.3333 13.7333 14.3333 13C14.3333 12.2666 13.7333 11.6666 13 11.6666Z"
                    fill="black"
                />
                <rect x="1" y="1" width="24" height="24" rx="12" stroke="#94A3B8" />
            </svg>
        </div>
    );
}
