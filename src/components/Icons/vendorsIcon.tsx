import {ILogoIcon} from "../../models/Icons.interface";

export function VendorsIcon(props: ILogoIcon) {
    const LogoSvg = {
        mono: (
            <svg xmlns="http://www.w3.org/2000/svg" width="15.75" height="18" viewBox="0 0 15.75 18">
                <path
                    d="M15.328,6.125h-.7v2.25h.7a.373.373,0,0,1,.422.422V10.2a.373.373,0,0,1-.422.422h-.7v2.25h.7a.373.373,0,0,1,.422.422V14.7a.373.373,0,0,1-.422.422h-.7v1.688A1.68,1.68,0,0,1,12.938,18.5H1.688a1.627,1.627,0,0,1-1.2-.492A1.627,1.627,0,0,1,0,16.813V2.188A1.627,1.627,0,0,1,.492.992,1.627,1.627,0,0,1,1.688.5h11.25a1.627,1.627,0,0,1,1.2.492,1.627,1.627,0,0,1,.492,1.2V3.875h.7a.373.373,0,0,1,.422.422V5.7A.373.373,0,0,1,15.328,6.125ZM8.895,5.668a2.207,2.207,0,0,0-3.164,0,2.207,2.207,0,0,0,0,3.164,2.207,2.207,0,0,0,3.164,0,2.207,2.207,0,0,0,0-3.164Zm2.355,7.664v-.668a1.847,1.847,0,0,0-.686-1.441,2.453,2.453,0,0,0-1.67-.6H8.719a3.656,3.656,0,0,1-2.812,0H5.73a2.668,2.668,0,0,0-1.178.264,2.121,2.121,0,0,0-.861.738,1.832,1.832,0,0,0-.316,1.037v.668a.6.6,0,0,0,.229.475A.816.816,0,0,0,4.148,14h6.328a.816.816,0,0,0,.545-.193A.6.6,0,0,0,11.25,13.332Z"
                    transform="translate(0 -0.5)"
                    fill="#5f6b83"
                />
            </svg>
        ),
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" width="15.75" height="18" viewBox="0 0 15.75 18">
                <path
                    d="M15.328,6.125h-.7v2.25h.7a.373.373,0,0,1,.422.422V10.2a.373.373,0,0,1-.422.422h-.7v2.25h.7a.373.373,0,0,1,.422.422V14.7a.373.373,0,0,1-.422.422h-.7v1.688A1.68,1.68,0,0,1,12.938,18.5H1.688a1.627,1.627,0,0,1-1.2-.492A1.627,1.627,0,0,1,0,16.813V2.188A1.627,1.627,0,0,1,.492.992,1.627,1.627,0,0,1,1.688.5h11.25a1.627,1.627,0,0,1,1.2.492,1.627,1.627,0,0,1,.492,1.2V3.875h.7a.373.373,0,0,1,.422.422V5.7A.373.373,0,0,1,15.328,6.125ZM8.895,5.668a2.207,2.207,0,0,0-3.164,0,2.207,2.207,0,0,0,0,3.164,2.207,2.207,0,0,0,3.164,0,2.207,2.207,0,0,0,0-3.164Zm2.355,7.664v-.668a1.847,1.847,0,0,0-.686-1.441,2.453,2.453,0,0,0-1.67-.6H8.719a3.656,3.656,0,0,1-2.812,0H5.73a2.668,2.668,0,0,0-1.178.264,2.121,2.121,0,0,0-.861.738,1.832,1.832,0,0,0-.316,1.037v.668a.6.6,0,0,0,.229.475A.816.816,0,0,0,4.148,14h6.328a.816.816,0,0,0,.545-.193A.6.6,0,0,0,11.25,13.332Z"
                    transform="translate(0 -0.5)"
                    fill="#ff6120"
                />
            </svg>
        ),
    };
    return (
        <div
            className="d-flex justify-content-center align-content-center mx-2"
            style={{maxWidth: props.size, minWidth: props.size, height: props.size}}>
            {LogoSvg[props.color]}
        </div>
    );
}
