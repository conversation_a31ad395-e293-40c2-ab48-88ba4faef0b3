import {ILogoIcon} from "../../models/Icons.interface";

export function FollowingIcon(props: ILogoIcon) {
    const LogoSvg = {
        mono: (
            <svg xmlns="http://www.w3.org/2000/svg" width="20.25" height="14.175" viewBox="0 0 20.25 14.175">
                <path
                    d="M4.461,7.224a1.946,1.946,0,0,1-1.424.6,1.946,1.946,0,0,1-1.424-.6,1.987,1.987,0,0,1,0-2.848,1.946,1.946,0,0,1,1.424-.6,1.946,1.946,0,0,1,1.424.6,1.987,1.987,0,0,1,0,2.848Zm14.175,0a1.987,1.987,0,0,1-2.848,0,1.987,1.987,0,0,1,0-2.848,1.987,1.987,0,0,1,2.848,0,1.987,1.987,0,0,1,0,2.848Zm-.411,1.614a2.053,2.053,0,0,1,2.025,2.025v1.012a1.027,1.027,0,0,1-1.013,1.013H17.149a4.6,4.6,0,0,0-.411-1.392,4.425,4.425,0,0,0-.823-1.187,4.8,4.8,0,0,0-1.139-.87,1.946,1.946,0,0,1,1.424-.6Zm-5.6-1.044a3.514,3.514,0,0,1-5,0,3.41,3.41,0,0,1-1.044-2.5,3.41,3.41,0,0,1,1.044-2.5,3.514,3.514,0,0,1,5,0,3.41,3.41,0,0,1,1.044,2.5A3.41,3.41,0,0,1,12.625,7.793ZM12.561,9.85A3.619,3.619,0,0,1,16.2,13.489v.918a1.512,1.512,0,0,1-1.519,1.519H5.569A1.512,1.512,0,0,1,4.05,14.406v-.918A3.619,3.619,0,0,1,7.689,9.85h.285a4.825,4.825,0,0,0,4.3,0ZM5.474,9.439a4.442,4.442,0,0,0-1.582,1.424A4.687,4.687,0,0,0,3.1,12.888H1.012a.973.973,0,0,1-.712-.3.973.973,0,0,1-.3-.712V10.863A1.946,1.946,0,0,1,.6,9.439a1.946,1.946,0,0,1,1.424-.6H4.05A1.946,1.946,0,0,1,5.474,9.439Z"
                    transform="translate(0 -1.75)"
                    fill="#5f6b83"
                />
            </svg>
        ),
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" width="20.25" height="14.175" viewBox="0 0 20.25 14.175">
                <path
                    d="M4.461,7.224a1.946,1.946,0,0,1-1.424.6,1.946,1.946,0,0,1-1.424-.6,1.987,1.987,0,0,1,0-2.848,1.946,1.946,0,0,1,1.424-.6,1.946,1.946,0,0,1,1.424.6,1.987,1.987,0,0,1,0,2.848Zm14.175,0a1.987,1.987,0,0,1-2.848,0,1.987,1.987,0,0,1,0-2.848,1.987,1.987,0,0,1,2.848,0,1.987,1.987,0,0,1,0,2.848Zm-.411,1.614a2.053,2.053,0,0,1,2.025,2.025v1.012a1.027,1.027,0,0,1-1.013,1.013H17.149a4.6,4.6,0,0,0-.411-1.392,4.425,4.425,0,0,0-.823-1.187,4.8,4.8,0,0,0-1.139-.87,1.946,1.946,0,0,1,1.424-.6Zm-5.6-1.044a3.514,3.514,0,0,1-5,0,3.41,3.41,0,0,1-1.044-2.5,3.41,3.41,0,0,1,1.044-2.5,3.514,3.514,0,0,1,5,0,3.41,3.41,0,0,1,1.044,2.5A3.41,3.41,0,0,1,12.625,7.793ZM12.561,9.85A3.619,3.619,0,0,1,16.2,13.489v.918a1.512,1.512,0,0,1-1.519,1.519H5.569A1.512,1.512,0,0,1,4.05,14.406v-.918A3.619,3.619,0,0,1,7.689,9.85h.285a4.825,4.825,0,0,0,4.3,0ZM5.474,9.439a4.442,4.442,0,0,0-1.582,1.424A4.687,4.687,0,0,0,3.1,12.888H1.012a.973.973,0,0,1-.712-.3.973.973,0,0,1-.3-.712V10.863A1.946,1.946,0,0,1,.6,9.439a1.946,1.946,0,0,1,1.424-.6H4.05A1.946,1.946,0,0,1,5.474,9.439Z"
                    transform="translate(0 -1.75)"
                    fill="#ff6120"
                />
            </svg>
        ),
    };
    return (
        <div
            className="d-flex justify-content-center align-content-center mx-2"
            style={{maxWidth: props.size, minWidth: props.size, height: props.size}}>
            {LogoSvg[props.color]}
        </div>
    );
}
