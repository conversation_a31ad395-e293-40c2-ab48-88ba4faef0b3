import {ILogoIcon} from "../../models/Icons.interface";

export function ChannelPitchIcon(props: ILogoIcon) {
    const LogoSvg = {
        mono: (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 201.84 207.19" width="100%" height="100%">
                <g id="Camada_2" data-name="Camada 2">
                    <g id="Layer_1" data-name="Layer 1">
                        <path d="M103.32,30.6a73,73,0,0,0,0,146v-15.3a57.7,57.7,0,0,1,0-115.39Zm0,30.61a42.39,42.39,0,0,0,0,84.77v-15.3a27.09,27.09,0,0,1,0-54.17Zm-88,42.38a88.15,88.15,0,0,1,88-88.29V0C46.26,0,0,46.38,0,103.59s46.26,103.6,103.32,103.6v-15.3C54.59,191.89,15.3,152.25,15.3,103.59Z" />
                        <path d="M140.16,95.94A37.65,37.65,0,0,0,103.32,65.7V72a31.32,31.32,0,0,1,30.37,23.93Zm-36.84,45.55a37.66,37.66,0,0,0,36.84-30.24h-6.47a31.32,31.32,0,0,1-30.37,23.93Zm67.79-45.55A68.32,68.32,0,0,0,103.32,35.1v6.31a62,62,0,0,1,61.43,54.53Zm-67.79,76.15a68.32,68.32,0,0,0,67.79-60.84h-6.36a62,62,0,0,1-61.43,54.53Zm92.18-60.84a92.6,92.6,0,0,1-92.18,85.13v6.32c52.11,0,94.63-40.39,98.52-91.45ZM103.32,10.81A92.6,92.6,0,0,1,195.5,95.94h6.34c-3.9-51.06-46.41-91.45-98.52-91.45Z" />
                        <path d="M103.32,91.81a11.79,11.79,0,1,0,11.51,11.78,11.62,11.62,0,0,0-11.51-11.78" />
                    </g>
                </g>
            </svg>
        ),
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 201.84 207.19" width="100%" height="100%">
                <g id="Camada_2" data-name="Camada 2">
                    <g id="Layer_1" data-name="Layer 1">
                        <path
                            fill="#ff6120"
                            d="M103.32,30.6a73,73,0,0,0,0,146v-15.3a57.7,57.7,0,0,1,0-115.39Zm0,30.61a42.39,42.39,0,0,0,0,84.77v-15.3a27.09,27.09,0,0,1,0-54.17Zm-88,42.38a88.15,88.15,0,0,1,88-88.29V0C46.26,0,0,46.38,0,103.59s46.26,103.6,103.32,103.6v-15.3C54.59,191.89,15.3,152.25,15.3,103.59Z"
                        />
                        <path
                            fill="#0d2456"
                            fillRule="evenodd"
                            d="M140.16,95.94A37.65,37.65,0,0,0,103.32,65.7V72a31.32,31.32,0,0,1,30.37,23.93Zm-36.84,45.55a37.66,37.66,0,0,0,36.84-30.24h-6.47a31.32,31.32,0,0,1-30.37,23.93Zm67.79-45.55A68.32,68.32,0,0,0,103.32,35.1v6.31a62,62,0,0,1,61.43,54.53Zm-67.79,76.15a68.32,68.32,0,0,0,67.79-60.84h-6.36a62,62,0,0,1-61.43,54.53Zm92.18-60.84a92.6,92.6,0,0,1-92.18,85.13v6.32c52.11,0,94.63-40.39,98.52-91.45ZM103.32,10.81A92.6,92.6,0,0,1,195.5,95.94h6.34c-3.9-51.06-46.41-91.45-98.52-91.45Z"
                        />
                        <path
                            fill="#0d2456"
                            fillRule="evenodd"
                            d="M103.32,91.81a11.79,11.79,0,1,0,11.51,11.78,11.62,11.62,0,0,0-11.51-11.78"
                        />
                    </g>
                </g>
            </svg>
        ),
    };
    return (
        <div
            className="d-flex justify-content-center align-content-center mx-2"
            style={{maxWidth: props.size, minWidth: props.size, height: props.size}}>
            {LogoSvg[props.color]}
        </div>
    );
}
