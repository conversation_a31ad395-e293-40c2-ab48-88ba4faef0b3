interface ILocationIcon {
    size: number;
    className?: string;
}
export function LocationIcon(props: ILocationIcon) {
    return (
        <div className={`mx-2 ${props.className}`} style={{maxWidth: props.size, minWidth: props.size}}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path d="M12 22s8.029-5.56 8-12c0-4.411-3.589-8-8-8S4 5.589 4 9.995C3.971 16.44 11.696 21.784 12 22zM8 9h3V6h2v3h3v2h-3v3h-2v-3H8V9z"></path>
            </svg>
        </div>
    );
}
