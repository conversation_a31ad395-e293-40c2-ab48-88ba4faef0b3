import {IIcons} from "../../models/Icons.interface";

export function XIcon(props: IIcons) {
    return (
        <div
            className={`d-flex justify-content-center me-2 ${props.className}`}
            style={{maxWidth: props.size, minWidth: props.size}}>
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                <path
                    id="Trazado_822"
                    data-name="Trazado 822"
                    d="M9.7,10.625,13.681,14.6a1.19,1.19,0,0,1,.358.875,1.327,1.327,0,0,1-.358.915l-.875.875a1.327,1.327,0,0,1-.915.358,1.19,1.19,0,0,1-.875-.358L7.039,13.29,3.062,17.267a1.19,1.19,0,0,1-.875.358,1.327,1.327,0,0,1-.915-.358L.4,16.392a1.327,1.327,0,0,1-.358-.915A1.19,1.19,0,0,1,.4,14.6l3.977-3.977L.4,6.648a1.19,1.19,0,0,1-.358-.875A1.327,1.327,0,0,1,.4,4.858l.875-.875a1.327,1.327,0,0,1,.915-.358,1.19,1.19,0,0,1,.875.358L7.039,7.96l3.977-3.977a1.19,1.19,0,0,1,.875-.358,1.327,1.327,0,0,1,.915.358l.875.875a1.327,1.327,0,0,1,.358.915,1.19,1.19,0,0,1-.358.875Z"
                    transform="translate(-0.039 -3.625)"
                />
            </svg>
        </div>
    );
}
