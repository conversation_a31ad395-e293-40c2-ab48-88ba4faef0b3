interface IBrandConnector {
    size?: string;
    className?: string;
}
/**
 * Factory that generates custom sized connectors
 * @param {string} className className to apply at svg
 * @param {string} size width size in px as string (it will automatically scale height)
 */
export default function ChannelPitchLogo(props: IBrandConnector) {
    const height = props.size ? (parseInt(props.size) * 5) / 17 : "50";
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={props.size || "170"}
            height={height}
            viewBox="0 0 170.231 50"
            className={props.className}>
            <g id="Grupo_2357" data-name="Grupo 2357" transform="translate(-764.999 -493)">
                <g id="Grupo_2353" data-name="Grupo 2353" transform="translate(511.691 -3.405)">
                    <path
                        id="Trazado_572"
                        data-name="Trazado 572"
                        d="M569.931,526.416h3.382V508.382h-3.382Zm-9.2-10.728a3.616,3.616,0,0,1,2.4.822,3.773,3.773,0,0,1,1.266,2.236h-7.33a3.67,3.67,0,0,1,1.266-2.236A3.6,3.6,0,0,1,560.734,515.688Zm0-2.685a7.758,7.758,0,0,0-3.555.81,6.443,6.443,0,0,0-2.6,2.38,7.119,7.119,0,0,0,0,7.229,7.03,7.03,0,0,0,6.159,3.186,7.59,7.59,0,0,0,3.743-.95,6.3,6.3,0,0,0,2.6-2.593l-2.9-1.391a3.689,3.689,0,0,1-3.45,2.264,3.563,3.563,0,0,1-2.344-.837,3.889,3.889,0,0,1-1.315-2.24h10.688a7.664,7.664,0,0,0-1.819-5.759A7.055,7.055,0,0,0,560.734,513Zm-9.217,13.413v-6.6a9.042,9.042,0,0,0-1.187-5.07,4.344,4.344,0,0,0-3.883-1.74c-2.188,0-3.679.718-4.481,2.136l-.14-1.948h-3.242v13.225h3.382V519.8a4.412,4.412,0,0,1,.87-2.866,3.006,3.006,0,0,1,2.469-1.058c2.012,0,2.845,1.351,2.845,3.931v6.6Zm-15.557,0v-6.6a9.042,9.042,0,0,0-1.186-5.07A4.346,4.346,0,0,0,530.89,513c-2.188,0-3.679.718-4.48,2.136l-.14-1.948h-3.242v13.225h3.382V519.8a4.413,4.413,0,0,1,.87-2.866,3.006,3.006,0,0,1,2.468-1.058c2.012,0,2.845,1.351,2.845,3.931v6.6Zm-22.891-2.669a3.609,3.609,0,0,1-2.669-1.114,4.2,4.2,0,0,1,0-5.646,3.564,3.564,0,0,1,2.669-1.106,3.655,3.655,0,0,1,2.7,1.106,4.19,4.19,0,0,1,0,5.646A3.662,3.662,0,0,1,513.069,523.746ZM512.151,513a6.08,6.08,0,0,0-4.424,1.9,7.549,7.549,0,0,0,0,9.8,6.092,6.092,0,0,0,4.424,1.9c2.42,0,4-.717,4.713-2.132l.153,1.94h3.222V513.191h-3.222l-.153,1.94C516.1,513.788,514.359,513,512.151,513Zm-8.3,13.413v-6.6c0-4.212-1.4-6.809-5.31-6.809-2.224,0-3.9.774-4.669,2.136V508.39h-3.382v18.026h3.382v-6.6a4.09,4.09,0,0,1,.978-2.853,3.273,3.273,0,0,1,2.549-1.078c2.152,0,3.07,1.351,3.07,3.931v6.6Zm-21.777-2.6a3.572,3.572,0,0,1-2.673-1.126,4.323,4.323,0,0,1,0-5.767,3.62,3.62,0,0,1,2.689-1.118,3.392,3.392,0,0,1,3.17,2l3.1-1.266a6.037,6.037,0,0,0-2.525-2.585A7.557,7.557,0,0,0,482.1,513a7.862,7.862,0,0,0-3.571.81,6.445,6.445,0,0,0-2.6,2.384,7.113,7.113,0,0,0,0,7.225,7.045,7.045,0,0,0,6.172,3.186,7.557,7.557,0,0,0,3.743-.95,6.04,6.04,0,0,0,2.525-2.577l-3.1-1.258A3.43,3.43,0,0,1,482.079,523.819Z"
                        transform="translate(-168.146 -9.087)"
                        fill="#fff"
                    />
                    <path
                        id="Trazado_573"
                        data-name="Trazado 573"
                        d="M537.374,612.033v-6.6c0-4.212-1.4-6.809-5.31-6.809-2.224,0-3.9.774-4.669,2.136v-6.749h-3.383v18.026H527.4v-6.6a4.089,4.089,0,0,1,.978-2.853,3.273,3.273,0,0,1,2.549-1.078c2.152,0,3.07,1.351,3.07,3.931v6.6Zm-21.777-2.6a3.573,3.573,0,0,1-2.673-1.126,4.324,4.324,0,0,1,0-5.767,3.621,3.621,0,0,1,2.689-1.118,3.392,3.392,0,0,1,3.17,2l3.1-1.266a6.033,6.033,0,0,0-2.525-2.585,7.555,7.555,0,0,0-3.743-.95,7.861,7.861,0,0,0-3.571.81,6.447,6.447,0,0,0-2.6,2.385,7.114,7.114,0,0,0,0,7.225,7.046,7.046,0,0,0,6.172,3.186,7.556,7.556,0,0,0,3.743-.95,6.038,6.038,0,0,0,2.525-2.577l-3.1-1.259A3.43,3.43,0,0,1,515.6,609.436Zm-15.152-10.628h-1.8v2.645h1.8v6.115a5.35,5.35,0,0,0,.962,3.515,3.978,3.978,0,0,0,3.218,1.138,15.778,15.778,0,0,0,2.469-.212v-2.781a4.966,4.966,0,0,1-2.044.02c-.809-.116-1.222-.714-1.222-1.8v-6h3.266v-2.645h-3.266v-2.982h-3.382Zm-7.426,13.225H496.4V598.808h-3.382Zm1.7-14.96a1.926,1.926,0,0,0,1.411-.589,1.963,1.963,0,0,0,.585-1.431,2,2,0,0,0-2-2,1.971,1.971,0,0,0-1.435.585,1.937,1.937,0,0,0-.585,1.411,2,2,0,0,0,2.02,2.02Zm-11,4.424a3.625,3.625,0,0,1,2.669,1.114,4.2,4.2,0,0,1,.012,5.639,3.792,3.792,0,0,1-5.37,0,4.156,4.156,0,0,1,0-5.647A3.647,3.647,0,0,1,483.725,601.5Zm.918,10.728a6.165,6.165,0,0,0,4.444-1.9,7.56,7.56,0,0,0,0-9.81,6.177,6.177,0,0,0-4.444-1.9c-2.42,0-4,.718-4.713,2.136l-.14-1.948h-3.242v18.033h3.382v-6.748C480.684,611.44,482.435,612.225,484.643,612.225Z"
                        transform="translate(-169.367 -73.328)"
                        fill="#fff"
                    />
                    <path
                        id="Trazado_574"
                        data-name="Trazado 574"
                        d="M796.586,628.968h.975v-5.2h-.975Zm-2.651-3.092a1.044,1.044,0,0,1,.693.236,1.092,1.092,0,0,1,.365.645H792.88a1.059,1.059,0,0,1,.365-.645A1.043,1.043,0,0,1,793.935,625.876Zm0-.774a2.24,2.24,0,0,0-1.025.233,1.861,1.861,0,0,0-.751.687,2.052,2.052,0,0,0,0,2.084,2.026,2.026,0,0,0,1.776.918,2.188,2.188,0,0,0,1.079-.274,1.812,1.812,0,0,0,.75-.748l-.835-.4a1.064,1.064,0,0,1-.995.653,1.029,1.029,0,0,1-.676-.242,1.12,1.12,0,0,1-.378-.646h3.08a2.208,2.208,0,0,0-.524-1.66A2.034,2.034,0,0,0,793.935,625.1Zm-2.657,3.866v-1.9a2.6,2.6,0,0,0-.342-1.461,1.25,1.25,0,0,0-1.119-.5,1.352,1.352,0,0,0-1.292.615l-.041-.561h-.934v3.812h.975v-1.906a1.272,1.272,0,0,1,.251-.826.867.867,0,0,1,.712-.305c.58,0,.82.389.82,1.133v1.9Zm-4.485,0v-1.9a2.6,2.6,0,0,0-.342-1.461,1.251,1.251,0,0,0-1.119-.5,1.352,1.352,0,0,0-1.292.615l-.041-.561h-.935v3.812h.975v-1.906a1.272,1.272,0,0,1,.25-.826.867.867,0,0,1,.711-.305c.58,0,.82.389.82,1.133v1.9Zm-6.6-.769a1.042,1.042,0,0,1-.77-.321,1.211,1.211,0,0,1,0-1.627,1.028,1.028,0,0,1,.77-.319,1.054,1.054,0,0,1,.779.319,1.209,1.209,0,0,1,0,1.627A1.056,1.056,0,0,1,780.194,628.2Zm-.264-3.1a1.752,1.752,0,0,0-1.275.549,2.174,2.174,0,0,0,0,2.825,1.755,1.755,0,0,0,1.275.548,1.377,1.377,0,0,0,1.358-.615l.044.559h.929v-3.812h-.929l-.044.559A1.5,1.5,0,0,0,779.93,625.1Zm-2.392,3.866v-1.9c0-1.214-.4-1.962-1.53-1.962a1.457,1.457,0,0,0-1.346.615v-1.945h-.975v5.2h.975v-1.9a1.178,1.178,0,0,1,.282-.822.943.943,0,0,1,.735-.311c.62,0,.885.389.885,1.133v1.9Zm-6.759-.894a1.482,1.482,0,0,1-1.083-.437,1.557,1.557,0,0,1,0-2.184,1.485,1.485,0,0,1,1.083-.437,1.56,1.56,0,0,1,.762.193,1.416,1.416,0,0,1,.538.535l.917-.437a2.317,2.317,0,0,0-.9-.914,2.65,2.65,0,0,0-1.316-.331,2.62,2.62,0,0,0-1.81.693,2.534,2.534,0,0,0,0,3.576,2.605,2.605,0,0,0,1.81.693,2.656,2.656,0,0,0,1.316-.331,2.281,2.281,0,0,0,.9-.909l-.917-.437a1.373,1.373,0,0,1-.538.532A1.5,1.5,0,0,1,770.779,628.074Zm-7.182.894h.975v-1.9a1.046,1.046,0,0,1,.352-.827,1.469,1.469,0,0,1,.995-.305V625.1a2.336,2.336,0,0,0-.809.116,1.217,1.217,0,0,0-.538.42l-.041-.479H763.6Zm-4.526-3.812v1.908a2.6,2.6,0,0,0,.342,1.459,1.253,1.253,0,0,0,1.117.5,1.347,1.347,0,0,0,1.287-.615l.041.559h.935v-3.812h-.975v1.906a1.282,1.282,0,0,1-.25.828.855.855,0,0,1-.709.308c-.578,0-.817-.389-.817-1.135v-1.908Zm-2.692,3.043a1.037,1.037,0,0,1-.771-.321,1.2,1.2,0,0,1,0-1.627,1.037,1.037,0,0,1,.774-.319,1.048,1.048,0,0,1,.773.319,1.1,1.1,0,0,1,.318.812,1.106,1.106,0,0,1-.318.816A1.067,1.067,0,0,1,756.378,628.2Zm0,.825a2.278,2.278,0,0,0,1.026-.233,1.877,1.877,0,0,0,.756-.687,1.9,1.9,0,0,0,.284-1.039,1.919,1.919,0,0,0-.284-1.043,2.028,2.028,0,0,0-1.778-.92,2.262,2.262,0,0,0-1.028.233,1.861,1.861,0,0,0-.75.688,1.921,1.921,0,0,0-.284,1.043,1.9,1.9,0,0,0,.284,1.039A2.016,2.016,0,0,0,756.378,629.024Zm-4.017-2.712-1.086-2.2h-1.151l1.715,3.21v1.642h1.04v-1.642l1.716-3.21h-1.148Zm33.294-6.432a1.041,1.041,0,0,1-.769-.321,1.208,1.208,0,0,1,0-1.627,1.028,1.028,0,0,1,.769-.319,1.054,1.054,0,0,1,.779.319,1.113,1.113,0,0,1,.315.815,1.1,1.1,0,0,1-.319.812A1.052,1.052,0,0,1,785.656,619.88Zm-.264-3.1a1.759,1.759,0,0,0-1.278.549,2.161,2.161,0,0,0,0,2.824,1.756,1.756,0,0,0,1.278.55,1.378,1.378,0,0,0,1.359-.615l.04.559h.935v-5.2h-.975V617.4A1.493,1.493,0,0,0,785.391,616.783ZM783,620.649v-1.9a2.6,2.6,0,0,0-.342-1.461,1.251,1.251,0,0,0-1.119-.5,1.352,1.352,0,0,0-1.292.616l-.04-.561h-.935v3.812h.975v-1.906a1.272,1.272,0,0,1,.251-.826.867.867,0,0,1,.711-.3c.58,0,.82.389.82,1.133v1.9Zm-6.6-.769a1.041,1.041,0,0,1-.77-.321,1.211,1.211,0,0,1,0-1.627,1.028,1.028,0,0,1,.77-.319,1.052,1.052,0,0,1,.779.319,1.208,1.208,0,0,1,0,1.627A1.055,1.055,0,0,1,776.4,619.88Zm-.265-3.1a1.751,1.751,0,0,0-1.275.549,2.175,2.175,0,0,0,0,2.826,1.754,1.754,0,0,0,1.275.548,1.377,1.377,0,0,0,1.358-.615l.044.559h.929v-3.812h-.929l-.044.559A1.5,1.5,0,0,0,776.134,616.783Zm-3.816,0a1.467,1.467,0,0,0-1.456.807,1.208,1.208,0,0,0-1.255-.807,1.3,1.3,0,0,0-1.289.616l-.04-.561h-.935v3.812h.975v-1.906a1.273,1.273,0,0,1,.247-.823.834.834,0,0,1,.69-.308c.558,0,.8.389.8,1.133v1.9h.975v-1.9a1.277,1.277,0,0,1,.25-.826.834.834,0,0,1,.69-.308c.554,0,.794.389.794,1.133v1.9h.975v-1.9a2.7,2.7,0,0,0-.3-1.435A1.206,1.206,0,0,0,772.318,616.783Zm-7.155,0a1.467,1.467,0,0,0-1.456.807,1.208,1.208,0,0,0-1.254-.807,1.3,1.3,0,0,0-1.289.616l-.04-.561h-.935v3.812h.975v-1.906a1.272,1.272,0,0,1,.247-.823.834.834,0,0,1,.69-.308c.558,0,.8.389.8,1.133v1.9h.975v-1.9a1.276,1.276,0,0,1,.25-.826.833.833,0,0,1,.69-.308c.555,0,.794.389.794,1.133v1.9h.975v-1.9a2.7,2.7,0,0,0-.3-1.435A1.206,1.206,0,0,0,765.163,616.783Zm-7.661,3.1a1.036,1.036,0,0,1-.771-.321,1.2,1.2,0,0,1,0-1.627,1.036,1.036,0,0,1,.774-.319,1.048,1.048,0,0,1,.774.319,1.094,1.094,0,0,1,.318.812,1.106,1.106,0,0,1-.318.816A1.067,1.067,0,0,1,757.5,619.88Zm0,.825a2.276,2.276,0,0,0,1.026-.233,1.879,1.879,0,0,0,.755-.687,2.047,2.047,0,0,0,0-2.082,2.029,2.029,0,0,0-1.778-.921,2.263,2.263,0,0,0-1.028.233,1.859,1.859,0,0,0-.75.687,2.048,2.048,0,0,0,0,2.082A2.016,2.016,0,0,0,757.5,620.7Zm-4.684-.95a1.483,1.483,0,0,1-1.083-.436,1.557,1.557,0,0,1,0-2.185,1.485,1.485,0,0,1,1.083-.437,1.56,1.56,0,0,1,.762.193,1.417,1.417,0,0,1,.538.535l.917-.437a2.315,2.315,0,0,0-.9-.914,2.649,2.649,0,0,0-1.316-.331,2.621,2.621,0,0,0-1.81.693,2.533,2.533,0,0,0,0,3.576,2.6,2.6,0,0,0,1.81.693,2.654,2.654,0,0,0,1.316-.331,2.277,2.277,0,0,0,.9-.909l-.917-.437a1.376,1.376,0,0,1-.538.531A1.5,1.5,0,0,1,752.818,619.755Z"
                        transform="translate(-376.924 -90.319)"
                        fill="#ff6120"
                    />
                    <path
                        id="Trazado_575"
                        data-name="Trazado 575"
                        d="M278.241,503.79a17.615,17.615,0,0,0,0,35.229v-3.693a13.922,13.922,0,0,1,0-27.844Zm0,7.385a10.23,10.23,0,0,0,0,20.459v-3.693a6.537,6.537,0,0,1,0-13.074ZM257,521.4a21.273,21.273,0,0,1,21.24-21.307v-3.693a25,25,0,0,0,0,50v-3.692A21.273,21.273,0,0,1,257,521.4Z"
                        transform="translate(0 0)"
                        fill="#ff6120"
                    />
                    <path
                        id="Trazado_576"
                        data-name="Trazado 576"
                        d="M365.516,522.967a9.087,9.087,0,0,0-8.891-7.3v1.524a7.56,7.56,0,0,1,7.33,5.775Zm-8.891,10.992a9.088,9.088,0,0,0,8.891-7.3h-1.561a7.56,7.56,0,0,1-7.33,5.775Zm16.359-10.992a16.485,16.485,0,0,0-16.359-14.684v1.524a14.96,14.96,0,0,1,14.826,13.16Zm-16.359,18.377a16.485,16.485,0,0,0,16.359-14.684h-1.533a14.96,14.96,0,0,1-14.826,13.16Zm22.246-14.684a22.349,22.349,0,0,1-22.246,20.545v1.524A23.876,23.876,0,0,0,380.4,526.66Zm-22.246-24.238a22.35,22.35,0,0,1,22.246,20.545H380.4A23.876,23.876,0,0,0,356.625,500.9Z"
                        transform="translate(-78.384 -3.409)"
                        fill="#fff"
                    />
                    <path
                        id="Trazado_577"
                        data-name="Trazado 577"
                        d="M347.894,588.214a2.845,2.845,0,1,0,2.777,2.844,2.805,2.805,0,0,0-2.777-2.844"
                        transform="translate(-69.653 -69.653)"
                        fill="#fff"
                    />
                </g>
                <path
                    id="Trazado_578"
                    data-name="Trazado 578"
                    d="M1.75-1.58h.912V-5.828h1.55V-6.58H.2v.752H1.75Zm3.1,0h.859L5.694-5.866h.015L7.237-1.58H8L9.524-5.866h.015L9.516-1.58h.912v-5H9.007L7.662-2.8H7.647L6.309-6.58H4.851Z"
                    transform="translate(924.803 504.58)"
                    fill="#fff"
                />
            </g>
        </svg>
    );
}
