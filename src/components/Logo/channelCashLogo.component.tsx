interface IBrandConnector {
    size?: string;
    className?: string;
    colorMode?: "white" | "colored";
}
/**
 * Factory that generates custom sized connectors
 * @param {string} className className to apply at svg wrapper
 * @param {string} size width size in px as string (it will automatically scale height)
 */
export default function ChannelCashLogo(props: IBrandConnector) {
    const width = props.size + "px" || "344px";
    const height = props.size ? ((parseInt(props.size) * 25) / 86).toString() + "px" : "100px";
    const logosSvg = {
        colored: (
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 344 100">
                <g id="Grupo_2432" data-name="Grupo 2432" transform="translate(-312 -157)">
                    <g id="Grupo_2418" data-name="Grupo 2418" transform="translate(-552 -221)">
                        <g id="Grupo_2417" data-name="Grupo 2417" transform="translate(864 383)">
                            <path
                                id="Trazado_663"
                                data-name="Trazado 663"
                                d="M138.008,241.372a45.007,45.007,0,0,1,44.794,40.7H159.377a21.8,21.8,0,1,0-21.759,26.092c1.6,11.253-5.162,19.362-14.649,18.894a19.883,19.883,0,0,1-8.848-2.548,45,45,0,0,1,23.888-83.136Z"
                                transform="translate(-93.008 -241.372)"
                                fill="#0c2556"
                            />
                            <path
                                id="Trazado_664"
                                data-name="Trazado 664"
                                d="M149.249,310.976l-.015-.068,8.253-1.454a25.415,25.415,0,0,1,1.346,16.717c-2.054,7.31-5.315,12.012-9.331,14.889C154.773,333.345,155.946,322.416,149.249,310.976Z"
                                transform="translate(-106.078 -257.198)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_665"
                                data-name="Trazado 665"
                                d="M160.158,300.836l-.015-.068,23.048-4.126q.678,1.862,1.165,3.7H171.019l.173,3.341c.482,9.28-1.905,19.4-8.19,26.486A28.751,28.751,0,0,1,157.488,335C165.081,327.209,167.958,314.375,160.158,300.836Z"
                                transform="translate(-107.996 -254.219)"
                                fill="#0c2556"
                            />
                            <path
                                id="Trazado_666"
                                data-name="Trazado 666"
                                d="M168.759,305.606h23.426a45.025,45.025,0,0,1-58.814,38.466c25.127,1.963,36.424-18.533,35.389-38.466Zm9.278,7.9a5.91,5.91,0,0,0-1.418.129,5.134,5.134,0,0,0-1.478.564,4.171,4.171,0,0,0-1.171,1.012,3.71,3.71,0,0,0-.683,1.445,3.139,3.139,0,0,0,.139,1.965,3.551,3.551,0,0,0,.467.82,6.428,6.428,0,0,0,.625.725,9.578,9.578,0,0,0,.729.662q.313.257.63.508l-1.392,5.057a2.912,2.912,0,0,1-.592-.288,1.928,1.928,0,0,1-.541-.5,1.52,1.52,0,0,1-.261-.586,1.482,1.482,0,0,1,.018-.644,1.914,1.914,0,0,1,.121-.358,3.545,3.545,0,0,1,.2-.378l.084-.14-.163-.262-.051-.032a1.492,1.492,0,0,0-.454-.192,1.685,1.685,0,0,0-.617-.024,1.633,1.633,0,0,0-.593.213,1.66,1.66,0,0,0-.473.429,1.514,1.514,0,0,0-.292.619,2.082,2.082,0,0,0-.022.918,2.372,2.372,0,0,0,.324.791,3.076,3.076,0,0,0,.584.657,5.009,5.009,0,0,0,.754.513,6.951,6.951,0,0,0,.857.392q.367.134.744.231l-.418,1.521.409.124.964.114.418-1.519a6.765,6.765,0,0,0,1.525-.086,5.078,5.078,0,0,0,1.6-.558,4.232,4.232,0,0,0,1.286-1.086,4.183,4.183,0,0,0,.774-1.653,2.862,2.862,0,0,0-.021-1.52,3.83,3.83,0,0,0-.655-1.26,6.95,6.95,0,0,0-1.046-1.067c-.391-.324-.787-.642-1.19-.952l-.025-.019,1.292-4.691a2.778,2.778,0,0,1,.348.155,1.461,1.461,0,0,1,.454.352,1.225,1.225,0,0,1,.249.5,1.577,1.577,0,0,1-.029.708,1.615,1.615,0,0,1-.072.239c-.035.1-.077.2-.114.289l-.045.111.132.259.084.039a2.342,2.342,0,0,0,.429.148,1.655,1.655,0,0,0,.644.028,1.41,1.41,0,0,0,.537-.2,1.334,1.334,0,0,0,.4-.385,1.606,1.606,0,0,0,.225-.521,1.916,1.916,0,0,0-.015-.87,2.207,2.207,0,0,0-.48-.907,3.7,3.7,0,0,0-.98-.791,5.374,5.374,0,0,0-1.388-.538l.387-1.406-.115-.109a1,1,0,0,0-.223-.156,1.17,1.17,0,0,0-.27-.1,1.012,1.012,0,0,0-.427-.011.5.5,0,0,0-.395.358l-.329,1.214Zm-.956,8.68-1.225,4.456a3.763,3.763,0,0,0,.719-.044,3.02,3.02,0,0,0,.9-.281,2.171,2.171,0,0,0,.669-.528,1.831,1.831,0,0,0,.375-.768,1.908,1.908,0,0,0,.021-.858,2.557,2.557,0,0,0-.336-.786,3.942,3.942,0,0,0-.611-.735q-.248-.236-.51-.457Zm-.548-3.246,1.127-4.077a3.577,3.577,0,0,0-.634.06,2.724,2.724,0,0,0-.8.282,2.085,2.085,0,0,0-.585.481,1.626,1.626,0,0,0-.318.655,1.873,1.873,0,0,0-.028.791,2.279,2.279,0,0,0,.278.722,3.537,3.537,0,0,0,.536.682Q176.315,318.743,176.533,318.937Z"
                                transform="translate(-102.39 -256.303)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_667"
                                data-name="Trazado 667"
                                d="M364.769,297.857h8.184v15.18a11.668,11.668,0,0,1,4.12-3.69,12.105,12.105,0,0,1,5.665-1.288,11.956,11.956,0,0,1,6.066,1.5,9.814,9.814,0,0,1,3.891,4.262,15.155,15.155,0,0,1,1.373,6.659V340.22h-8.184V322.251a9.027,9.027,0,0,0-.572-3.491,4.312,4.312,0,0,0-1.888-2.175,6.893,6.893,0,0,0-3.433-.744,6.709,6.709,0,0,0-5.236,1.984,8.534,8.534,0,0,0-1.8,5.856V340.22h-8.184Z"
                                transform="translate(-156.178 -254.502)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_668"
                                data-name="Trazado 668"
                                d="M245.891,286.087a18.238,18.238,0,0,0,5.458-.82,15.557,15.557,0,0,0,4.753-2.419,13.927,13.927,0,0,0,3.581-3.972,14.962,14.962,0,0,0,1.925-5.478h-8.4a6.755,6.755,0,0,1-4.135,4.573,8.778,8.778,0,0,1-3.181.572,8.166,8.166,0,0,1-4.239-1.124,7.553,7.553,0,0,1-2.819-3.105,9.921,9.921,0,0,1-1-4.629,10.124,10.124,0,0,1,.972-4.611,7.421,7.421,0,0,1,2.819-3.105,8.159,8.159,0,0,1,4.324-1.143,8.785,8.785,0,0,1,3.144.553,6.675,6.675,0,0,1,4.116,4.534h8.4a14.667,14.667,0,0,0-1.877-5.354,14.086,14.086,0,0,0-3.534-3.962,15.394,15.394,0,0,0-4.743-2.468,17.858,17.858,0,0,0-5.506-.847,17.279,17.279,0,0,0-6.563,1.247,15.624,15.624,0,0,0-8.611,8.66,17.787,17.787,0,0,0,0,12.993,15.853,15.853,0,0,0,3.409,5.2,15.65,15.65,0,0,0,5.173,3.448,17,17,0,0,0,6.534,1.258Zm95.629-.686V268.284a8.017,8.017,0,0,1,1.8-5.25,6.774,6.774,0,0,1,5.277-1.981,6.952,6.952,0,0,1,3.458.743,4.229,4.229,0,0,1,1.867,2.171,9.227,9.227,0,0,1,.562,3.487V285.4h8.173V265.682a15.119,15.119,0,0,0-1.372-6.648,9.786,9.786,0,0,0-3.905-4.258,13.134,13.134,0,0,0-11.755-.21,11.655,11.655,0,0,0-4.115,3.687v-4.287h-8.173V285.4ZM282.755,253.28a12.092,12.092,0,0,0-5.658,1.286,11.65,11.65,0,0,0-4.116,3.687v-16.86h-8.173V285.4h8.173V268.883a8.528,8.528,0,0,1,1.8-5.849,6.7,6.7,0,0,1,5.229-1.981,6.886,6.886,0,0,1,3.429.743,4.3,4.3,0,0,1,1.886,2.171,9.038,9.038,0,0,1,.572,3.487V285.4h8.173V265.682a15.119,15.119,0,0,0-1.372-6.648,9.8,9.8,0,0,0-3.886-4.258,11.939,11.939,0,0,0-6.059-1.5Zm38.18.685v4.4a12.585,12.585,0,0,0-2.553-2.667,11.315,11.315,0,0,0-3.315-1.772,12.558,12.558,0,0,0-4.077-.648,13.449,13.449,0,0,0-10.164,4.553,15.741,15.741,0,0,0-3.03,5.192,20.823,20.823,0,0,0-.009,13.3,15.72,15.72,0,0,0,3,5.183,13.353,13.353,0,0,0,4.543,3.372,13.7,13.7,0,0,0,5.715,1.21,13.057,13.057,0,0,0,4.02-.61,11.382,11.382,0,0,0,3.276-1.715,12.352,12.352,0,0,0,2.534-2.59V285.4h8.173l.057-31.435Zm-8.059,24.348a7.868,7.868,0,0,1-4.067-1.086,7.5,7.5,0,0,1-2.82-2.991,10.308,10.308,0,0,1-.009-9.078,7.5,7.5,0,0,1,2.8-3.01,7.84,7.84,0,0,1,4.1-1.1,7.969,7.969,0,0,1,4.125,1.1,7.633,7.633,0,0,1,2.858,3.01,10.1,10.1,0,0,1,0,9.078A7.521,7.521,0,0,1,317,277.227a8.052,8.052,0,0,1-4.125,1.086Zm71.671-25.034a12.1,12.1,0,0,0-5.658,1.286,11.655,11.655,0,0,0-4.115,3.687v-4.287H366.6V285.4h8.173V268.883c.01-2.581.609-4.525,1.81-5.849a6.785,6.785,0,0,1,5.278-1.981,6.956,6.956,0,0,1,3.458.743,4.228,4.228,0,0,1,1.867,2.171,9.225,9.225,0,0,1,.563,3.487V285.4h8.172V265.682a15.131,15.131,0,0,0-1.371-6.648,9.792,9.792,0,0,0-3.906-4.258,12.059,12.059,0,0,0-6.1-1.5Zm30.978,0a17.423,17.423,0,0,0-6.611,1.247,15.292,15.292,0,0,0-5.191,3.449,15.625,15.625,0,0,0-3.391,5.21,17.837,17.837,0,0,0-1.21,6.553,17.4,17.4,0,0,0,1.21,6.506,15.182,15.182,0,0,0,8.63,8.6,18.157,18.157,0,0,0,6.734,1.239,19.1,19.1,0,0,0,4.915-.629,15.4,15.4,0,0,0,4.42-1.933,13.989,13.989,0,0,0,3.6-3.324,15.6,15.6,0,0,0,2.439-4.8h-8.458a6.014,6.014,0,0,1-1.591,2.143,7.28,7.28,0,0,1-2.42,1.334,9.113,9.113,0,0,1-2.962.467,8.866,8.866,0,0,1-3.886-.838,7.224,7.224,0,0,1-2.877-2.514,9.14,9.14,0,0,1-1.41-4.192h24.119a20.311,20.311,0,0,0-.705-7.258,16.084,16.084,0,0,0-3.124-5.877,14.684,14.684,0,0,0-5.22-3.944,17,17,0,0,0-7.011-1.438Zm-.058,6.744a8.017,8.017,0,0,1,3.477.734,6.884,6.884,0,0,1,2.5,2.019,8.346,8.346,0,0,1,1.458,3.02H407.865a8.978,8.978,0,0,1,1.543-3,7.243,7.243,0,0,1,2.534-2.029,8.238,8.238,0,0,1,3.525-.744ZM434.327,285.4H442.5V241.392h-8.173Z"
                                transform="translate(-124.746 -241.377)"
                                fill="#0c2556"
                            />
                            <path
                                id="Trazado_669"
                                data-name="Trazado 669"
                                d="M337.628,344a22.209,22.209,0,0,0,5.475-.649,14.005,14.005,0,0,0,4.454-1.945,9.351,9.351,0,0,0,2.995-3.272,9.613,9.613,0,0,0,1.1-4.607,7.817,7.817,0,0,0-.926-3.939,7.646,7.646,0,0,0-2.719-2.69,19.93,19.93,0,0,0-4.4-1.888q-2.6-.8-5.981-1.555c-1.516-.333-2.708-.62-3.594-.858a5.393,5.393,0,0,1-1.87-.792,1.34,1.34,0,0,1-.544-1.154,2.18,2.18,0,0,1,1.412-1.965,9.148,9.148,0,0,1,3.853-.668,11.105,11.105,0,0,1,3.128.381,4.692,4.692,0,0,1,1.965,1.1,3.983,3.983,0,0,1,1.03,1.727h8.3a12.588,12.588,0,0,0-1.441-4.034,10.69,10.69,0,0,0-2.746-3.2,12.731,12.731,0,0,0-4.2-2.089,20.346,20.346,0,0,0-5.751-.754,21.23,21.23,0,0,0-5.656.705,12.959,12.959,0,0,0-4.35,2.042,9.158,9.158,0,0,0-2.795,3.291,9.8,9.8,0,0,0-.992,4.434,7.91,7.91,0,0,0,.716,3.491,6.663,6.663,0,0,0,2.231,2.528,16.054,16.054,0,0,0,3.911,1.917,55.968,55.968,0,0,0,5.732,1.622c1.192.276,2.232.515,3.129.725a18.654,18.654,0,0,1,2.26.629,3.511,3.511,0,0,1,1.364.811,1.774,1.774,0,0,1,.457,1.269,2.043,2.043,0,0,1-1.306,1.936,9.561,9.561,0,0,1-4.016.639,13.391,13.391,0,0,1-4.1-.534,5.273,5.273,0,0,1-2.384-1.488,5.036,5.036,0,0,1-1.126-2.214h-8.3a12.1,12.1,0,0,0,5.112,8.117,15.477,15.477,0,0,0,4.73,2.174A21.729,21.729,0,0,0,337.628,344Z"
                                transform="translate(-146.225 -257.591)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_670"
                                data-name="Trazado 670"
                                d="M245.949,344a18.263,18.263,0,0,0,5.466-.82,15.574,15.574,0,0,0,4.759-2.422,13.939,13.939,0,0,0,3.586-3.977,14.986,14.986,0,0,0,1.927-5.484h-8.413a6.756,6.756,0,0,1-4.139,4.577,8.748,8.748,0,0,1-3.185.573,8.181,8.181,0,0,1-4.244-1.125,7.554,7.554,0,0,1-2.823-3.11,9.924,9.924,0,0,1-1-4.635,10.145,10.145,0,0,1,.972-4.616,7.426,7.426,0,0,1,2.823-3.109,8.172,8.172,0,0,1,4.33-1.145,8.82,8.82,0,0,1,3.148.553,6.677,6.677,0,0,1,4.12,4.541h8.413a14.693,14.693,0,0,0-1.879-5.361,14.128,14.128,0,0,0-3.539-3.967,15.443,15.443,0,0,0-4.75-2.471,17.875,17.875,0,0,0-5.513-.849,17.323,17.323,0,0,0-6.571,1.25,15.642,15.642,0,0,0-8.622,8.67,17.8,17.8,0,0,0,0,13.009,15.867,15.867,0,0,0,3.414,5.207,15.662,15.662,0,0,0,5.179,3.453A17.007,17.007,0,0,0,245.949,344Zm43.215-32.161v4.407a12.543,12.543,0,0,0-2.556-2.671,11.325,11.325,0,0,0-3.318-1.773,12.6,12.6,0,0,0-4.083-.649,13.477,13.477,0,0,0-10.177,4.559,15.783,15.783,0,0,0-3.033,5.2,20.838,20.838,0,0,0-.009,13.316,15.758,15.758,0,0,0,3,5.189,13.379,13.379,0,0,0,4.55,3.376A13.693,13.693,0,0,0,279.264,344a13.045,13.045,0,0,0,4.026-.61,11.462,11.462,0,0,0,3.281-1.717,12.359,12.359,0,0,0,2.537-2.594v4.234h8.184l.058-31.474ZM281.1,336.213a7.869,7.869,0,0,1-4.072-1.087,7.5,7.5,0,0,1-2.824-2.995,10.32,10.32,0,0,1-.009-9.089,7.5,7.5,0,0,1,2.8-3.014,7.848,7.848,0,0,1,4.1-1.1,7.974,7.974,0,0,1,4.13,1.1,7.647,7.647,0,0,1,2.861,3.014,10.128,10.128,0,0,1,0,9.089,7.536,7.536,0,0,1-2.861,2.995A8.057,8.057,0,0,1,281.1,336.213Z"
                                transform="translate(-124.754 -257.591)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_671"
                                data-name="Trazado 671"
                                d="M338.011,339.792h5.831v6.6h-5.831v-6.6Zm0-33.821h5.831v7.85h-5.831Z"
                                transform="translate(-149.958 -256.388)"
                                fill="#ff6120"
                            />
                        </g>
                        <path
                            id="Trazado_672"
                            data-name="Trazado 672"
                            d="M2.99,2.42H4.631V-5.226h2.79V-6.58H.2v1.354H2.99Zm5.58,0h1.546l-.027-7.714h.027L12.865,2.42h1.368l2.749-7.714h.027L16.969,2.42H18.61v-9H16.052L13.631.218H13.6L11.2-6.58H8.571Z"
                            transform="translate(1188.552 396.78)"
                            fill="#0c2556"
                        />
                    </g>
                    <rect
                        id="Rectángulo_2589"
                        data-name="Rectángulo 2589"
                        width="344"
                        height="100"
                        transform="translate(312 157)"
                        fill="#fff"
                        opacity="0"
                    />
                </g>
            </svg>
        ),
        white: (
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 344 100">
                <g id="Grupo_2432" data-name="Grupo 2432" transform="translate(-312 -157)">
                    <g id="Grupo_2418" data-name="Grupo 2418" transform="translate(-552 -221)">
                        <g id="Grupo_2417" data-name="Grupo 2417" transform="translate(864 383)">
                            <path
                                id="Trazado_663"
                                data-name="Trazado 663"
                                d="M138.008,241.372a45.007,45.007,0,0,1,44.794,40.7H159.377a21.8,21.8,0,1,0-21.759,26.092c1.6,11.253-5.162,19.362-14.649,18.894a19.883,19.883,0,0,1-8.848-2.548,45,45,0,0,1,23.888-83.136Z"
                                transform="translate(-93.008 -241.372)"
                                fill="#fff"
                            />
                            <path
                                id="Trazado_664"
                                data-name="Trazado 664"
                                d="M149.249,310.976l-.015-.068,8.253-1.454a25.415,25.415,0,0,1,1.346,16.717c-2.054,7.31-5.315,12.012-9.331,14.889C154.773,333.345,155.946,322.416,149.249,310.976Z"
                                transform="translate(-106.078 -257.198)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_665"
                                data-name="Trazado 665"
                                d="M160.158,300.836l-.015-.068,23.048-4.126q.678,1.862,1.165,3.7H171.019l.173,3.341c.482,9.28-1.905,19.4-8.19,26.486A28.751,28.751,0,0,1,157.488,335C165.081,327.209,167.958,314.375,160.158,300.836Z"
                                transform="translate(-107.996 -254.219)"
                                fill="#fff"
                            />
                            <path
                                id="Trazado_666"
                                data-name="Trazado 666"
                                d="M168.759,305.606h23.426a45.025,45.025,0,0,1-58.814,38.466c25.127,1.963,36.424-18.533,35.389-38.466Zm9.278,7.9a5.91,5.91,0,0,0-1.418.129,5.134,5.134,0,0,0-1.478.564,4.171,4.171,0,0,0-1.171,1.012,3.71,3.71,0,0,0-.683,1.445,3.139,3.139,0,0,0,.139,1.965,3.551,3.551,0,0,0,.467.82,6.428,6.428,0,0,0,.625.725,9.578,9.578,0,0,0,.729.662q.313.257.63.508l-1.392,5.057a2.912,2.912,0,0,1-.592-.288,1.928,1.928,0,0,1-.541-.5,1.52,1.52,0,0,1-.261-.586,1.482,1.482,0,0,1,.018-.644,1.914,1.914,0,0,1,.121-.358,3.545,3.545,0,0,1,.2-.378l.084-.14-.163-.262-.051-.032a1.492,1.492,0,0,0-.454-.192,1.685,1.685,0,0,0-.617-.024,1.633,1.633,0,0,0-.593.213,1.66,1.66,0,0,0-.473.429,1.514,1.514,0,0,0-.292.619,2.082,2.082,0,0,0-.022.918,2.372,2.372,0,0,0,.324.791,3.076,3.076,0,0,0,.584.657,5.009,5.009,0,0,0,.754.513,6.951,6.951,0,0,0,.857.392q.367.134.744.231l-.418,1.521.409.124.964.114.418-1.519a6.765,6.765,0,0,0,1.525-.086,5.078,5.078,0,0,0,1.6-.558,4.232,4.232,0,0,0,1.286-1.086,4.183,4.183,0,0,0,.774-1.653,2.862,2.862,0,0,0-.021-1.52,3.83,3.83,0,0,0-.655-1.26,6.95,6.95,0,0,0-1.046-1.067c-.391-.324-.787-.642-1.19-.952l-.025-.019,1.292-4.691a2.778,2.778,0,0,1,.348.155,1.461,1.461,0,0,1,.454.352,1.225,1.225,0,0,1,.249.5,1.577,1.577,0,0,1-.029.708,1.615,1.615,0,0,1-.072.239c-.035.1-.077.2-.114.289l-.045.111.132.259.084.039a2.342,2.342,0,0,0,.429.148,1.655,1.655,0,0,0,.644.028,1.41,1.41,0,0,0,.537-.2,1.334,1.334,0,0,0,.4-.385,1.606,1.606,0,0,0,.225-.521,1.916,1.916,0,0,0-.015-.87,2.207,2.207,0,0,0-.48-.907,3.7,3.7,0,0,0-.98-.791,5.374,5.374,0,0,0-1.388-.538l.387-1.406-.115-.109a1,1,0,0,0-.223-.156,1.17,1.17,0,0,0-.27-.1,1.012,1.012,0,0,0-.427-.011.5.5,0,0,0-.395.358l-.329,1.214Zm-.956,8.68-1.225,4.456a3.763,3.763,0,0,0,.719-.044,3.02,3.02,0,0,0,.9-.281,2.171,2.171,0,0,0,.669-.528,1.831,1.831,0,0,0,.375-.768,1.908,1.908,0,0,0,.021-.858,2.557,2.557,0,0,0-.336-.786,3.942,3.942,0,0,0-.611-.735q-.248-.236-.51-.457Zm-.548-3.246,1.127-4.077a3.577,3.577,0,0,0-.634.06,2.724,2.724,0,0,0-.8.282,2.085,2.085,0,0,0-.585.481,1.626,1.626,0,0,0-.318.655,1.873,1.873,0,0,0-.028.791,2.279,2.279,0,0,0,.278.722,3.537,3.537,0,0,0,.536.682Q176.315,318.743,176.533,318.937Z"
                                transform="translate(-102.39 -256.303)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_667"
                                data-name="Trazado 667"
                                d="M364.769,297.857h8.184v15.18a11.668,11.668,0,0,1,4.12-3.69,12.105,12.105,0,0,1,5.665-1.288,11.956,11.956,0,0,1,6.066,1.5,9.814,9.814,0,0,1,3.891,4.262,15.155,15.155,0,0,1,1.373,6.659V340.22h-8.184V322.251a9.027,9.027,0,0,0-.572-3.491,4.312,4.312,0,0,0-1.888-2.175,6.893,6.893,0,0,0-3.433-.744,6.709,6.709,0,0,0-5.236,1.984,8.534,8.534,0,0,0-1.8,5.856V340.22h-8.184Z"
                                transform="translate(-156.178 -254.502)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_668"
                                data-name="Trazado 668"
                                d="M245.891,286.087a18.238,18.238,0,0,0,5.458-.82,15.557,15.557,0,0,0,4.753-2.419,13.927,13.927,0,0,0,3.581-3.972,14.962,14.962,0,0,0,1.925-5.478h-8.4a6.755,6.755,0,0,1-4.135,4.573,8.778,8.778,0,0,1-3.181.572,8.166,8.166,0,0,1-4.239-1.124,7.553,7.553,0,0,1-2.819-3.105,9.921,9.921,0,0,1-1-4.629,10.124,10.124,0,0,1,.972-4.611,7.421,7.421,0,0,1,2.819-3.105,8.159,8.159,0,0,1,4.324-1.143,8.785,8.785,0,0,1,3.144.553,6.675,6.675,0,0,1,4.116,4.534h8.4a14.667,14.667,0,0,0-1.877-5.354,14.086,14.086,0,0,0-3.534-3.962,15.394,15.394,0,0,0-4.743-2.468,17.858,17.858,0,0,0-5.506-.847,17.279,17.279,0,0,0-6.563,1.247,15.624,15.624,0,0,0-8.611,8.66,17.787,17.787,0,0,0,0,12.993,15.853,15.853,0,0,0,3.409,5.2,15.65,15.65,0,0,0,5.173,3.448,17,17,0,0,0,6.534,1.258Zm95.629-.686V268.284a8.017,8.017,0,0,1,1.8-5.25,6.774,6.774,0,0,1,5.277-1.981,6.952,6.952,0,0,1,3.458.743,4.229,4.229,0,0,1,1.867,2.171,9.227,9.227,0,0,1,.562,3.487V285.4h8.173V265.682a15.119,15.119,0,0,0-1.372-6.648,9.786,9.786,0,0,0-3.905-4.258,13.134,13.134,0,0,0-11.755-.21,11.655,11.655,0,0,0-4.115,3.687v-4.287h-8.173V285.4ZM282.755,253.28a12.092,12.092,0,0,0-5.658,1.286,11.65,11.65,0,0,0-4.116,3.687v-16.86h-8.173V285.4h8.173V268.883a8.528,8.528,0,0,1,1.8-5.849,6.7,6.7,0,0,1,5.229-1.981,6.886,6.886,0,0,1,3.429.743,4.3,4.3,0,0,1,1.886,2.171,9.038,9.038,0,0,1,.572,3.487V285.4h8.173V265.682a15.119,15.119,0,0,0-1.372-6.648,9.8,9.8,0,0,0-3.886-4.258,11.939,11.939,0,0,0-6.059-1.5Zm38.18.685v4.4a12.585,12.585,0,0,0-2.553-2.667,11.315,11.315,0,0,0-3.315-1.772,12.558,12.558,0,0,0-4.077-.648,13.449,13.449,0,0,0-10.164,4.553,15.741,15.741,0,0,0-3.03,5.192,20.823,20.823,0,0,0-.009,13.3,15.72,15.72,0,0,0,3,5.183,13.353,13.353,0,0,0,4.543,3.372,13.7,13.7,0,0,0,5.715,1.21,13.057,13.057,0,0,0,4.02-.61,11.382,11.382,0,0,0,3.276-1.715,12.352,12.352,0,0,0,2.534-2.59V285.4h8.173l.057-31.435Zm-8.059,24.348a7.868,7.868,0,0,1-4.067-1.086,7.5,7.5,0,0,1-2.82-2.991,10.308,10.308,0,0,1-.009-9.078,7.5,7.5,0,0,1,2.8-3.01,7.84,7.84,0,0,1,4.1-1.1,7.969,7.969,0,0,1,4.125,1.1,7.633,7.633,0,0,1,2.858,3.01,10.1,10.1,0,0,1,0,9.078A7.521,7.521,0,0,1,317,277.227a8.052,8.052,0,0,1-4.125,1.086Zm71.671-25.034a12.1,12.1,0,0,0-5.658,1.286,11.655,11.655,0,0,0-4.115,3.687v-4.287H366.6V285.4h8.173V268.883c.01-2.581.609-4.525,1.81-5.849a6.785,6.785,0,0,1,5.278-1.981,6.956,6.956,0,0,1,3.458.743,4.228,4.228,0,0,1,1.867,2.171,9.225,9.225,0,0,1,.563,3.487V285.4h8.172V265.682a15.131,15.131,0,0,0-1.371-6.648,9.792,9.792,0,0,0-3.906-4.258,12.059,12.059,0,0,0-6.1-1.5Zm30.978,0a17.423,17.423,0,0,0-6.611,1.247,15.292,15.292,0,0,0-5.191,3.449,15.625,15.625,0,0,0-3.391,5.21,17.837,17.837,0,0,0-1.21,6.553,17.4,17.4,0,0,0,1.21,6.506,15.182,15.182,0,0,0,8.63,8.6,18.157,18.157,0,0,0,6.734,1.239,19.1,19.1,0,0,0,4.915-.629,15.4,15.4,0,0,0,4.42-1.933,13.989,13.989,0,0,0,3.6-3.324,15.6,15.6,0,0,0,2.439-4.8h-8.458a6.014,6.014,0,0,1-1.591,2.143,7.28,7.28,0,0,1-2.42,1.334,9.113,9.113,0,0,1-2.962.467,8.866,8.866,0,0,1-3.886-.838,7.224,7.224,0,0,1-2.877-2.514,9.14,9.14,0,0,1-1.41-4.192h24.119a20.311,20.311,0,0,0-.705-7.258,16.084,16.084,0,0,0-3.124-5.877,14.684,14.684,0,0,0-5.22-3.944,17,17,0,0,0-7.011-1.438Zm-.058,6.744a8.017,8.017,0,0,1,3.477.734,6.884,6.884,0,0,1,2.5,2.019,8.346,8.346,0,0,1,1.458,3.02H407.865a8.978,8.978,0,0,1,1.543-3,7.243,7.243,0,0,1,2.534-2.029,8.238,8.238,0,0,1,3.525-.744ZM434.327,285.4H442.5V241.392h-8.173Z"
                                transform="translate(-124.746 -241.377)"
                                fill="#fff"
                            />
                            <path
                                id="Trazado_669"
                                data-name="Trazado 669"
                                d="M337.628,344a22.209,22.209,0,0,0,5.475-.649,14.005,14.005,0,0,0,4.454-1.945,9.351,9.351,0,0,0,2.995-3.272,9.613,9.613,0,0,0,1.1-4.607,7.817,7.817,0,0,0-.926-3.939,7.646,7.646,0,0,0-2.719-2.69,19.93,19.93,0,0,0-4.4-1.888q-2.6-.8-5.981-1.555c-1.516-.333-2.708-.62-3.594-.858a5.393,5.393,0,0,1-1.87-.792,1.34,1.34,0,0,1-.544-1.154,2.18,2.18,0,0,1,1.412-1.965,9.148,9.148,0,0,1,3.853-.668,11.105,11.105,0,0,1,3.128.381,4.692,4.692,0,0,1,1.965,1.1,3.983,3.983,0,0,1,1.03,1.727h8.3a12.588,12.588,0,0,0-1.441-4.034,10.69,10.69,0,0,0-2.746-3.2,12.731,12.731,0,0,0-4.2-2.089,20.346,20.346,0,0,0-5.751-.754,21.23,21.23,0,0,0-5.656.705,12.959,12.959,0,0,0-4.35,2.042,9.158,9.158,0,0,0-2.795,3.291,9.8,9.8,0,0,0-.992,4.434,7.91,7.91,0,0,0,.716,3.491,6.663,6.663,0,0,0,2.231,2.528,16.054,16.054,0,0,0,3.911,1.917,55.968,55.968,0,0,0,5.732,1.622c1.192.276,2.232.515,3.129.725a18.654,18.654,0,0,1,2.26.629,3.511,3.511,0,0,1,1.364.811,1.774,1.774,0,0,1,.457,1.269,2.043,2.043,0,0,1-1.306,1.936,9.561,9.561,0,0,1-4.016.639,13.391,13.391,0,0,1-4.1-.534,5.273,5.273,0,0,1-2.384-1.488,5.036,5.036,0,0,1-1.126-2.214h-8.3a12.1,12.1,0,0,0,5.112,8.117,15.477,15.477,0,0,0,4.73,2.174A21.729,21.729,0,0,0,337.628,344Z"
                                transform="translate(-146.225 -257.591)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_670"
                                data-name="Trazado 670"
                                d="M245.949,344a18.263,18.263,0,0,0,5.466-.82,15.574,15.574,0,0,0,4.759-2.422,13.939,13.939,0,0,0,3.586-3.977,14.986,14.986,0,0,0,1.927-5.484h-8.413a6.756,6.756,0,0,1-4.139,4.577,8.748,8.748,0,0,1-3.185.573,8.181,8.181,0,0,1-4.244-1.125,7.554,7.554,0,0,1-2.823-3.11,9.924,9.924,0,0,1-1-4.635,10.145,10.145,0,0,1,.972-4.616,7.426,7.426,0,0,1,2.823-3.109,8.172,8.172,0,0,1,4.33-1.145,8.82,8.82,0,0,1,3.148.553,6.677,6.677,0,0,1,4.12,4.541h8.413a14.693,14.693,0,0,0-1.879-5.361,14.128,14.128,0,0,0-3.539-3.967,15.443,15.443,0,0,0-4.75-2.471,17.875,17.875,0,0,0-5.513-.849,17.323,17.323,0,0,0-6.571,1.25,15.642,15.642,0,0,0-8.622,8.67,17.8,17.8,0,0,0,0,13.009,15.867,15.867,0,0,0,3.414,5.207,15.662,15.662,0,0,0,5.179,3.453A17.007,17.007,0,0,0,245.949,344Zm43.215-32.161v4.407a12.543,12.543,0,0,0-2.556-2.671,11.325,11.325,0,0,0-3.318-1.773,12.6,12.6,0,0,0-4.083-.649,13.477,13.477,0,0,0-10.177,4.559,15.783,15.783,0,0,0-3.033,5.2,20.838,20.838,0,0,0-.009,13.316,15.758,15.758,0,0,0,3,5.189,13.379,13.379,0,0,0,4.55,3.376A13.693,13.693,0,0,0,279.264,344a13.045,13.045,0,0,0,4.026-.61,11.462,11.462,0,0,0,3.281-1.717,12.359,12.359,0,0,0,2.537-2.594v4.234h8.184l.058-31.474ZM281.1,336.213a7.869,7.869,0,0,1-4.072-1.087,7.5,7.5,0,0,1-2.824-2.995,10.32,10.32,0,0,1-.009-9.089,7.5,7.5,0,0,1,2.8-3.014,7.848,7.848,0,0,1,4.1-1.1,7.974,7.974,0,0,1,4.13,1.1,7.647,7.647,0,0,1,2.861,3.014,10.128,10.128,0,0,1,0,9.089,7.536,7.536,0,0,1-2.861,2.995A8.057,8.057,0,0,1,281.1,336.213Z"
                                transform="translate(-124.754 -257.591)"
                                fill="#ff6120"
                            />
                            <path
                                id="Trazado_671"
                                data-name="Trazado 671"
                                d="M338.011,339.792h5.831v6.6h-5.831v-6.6Zm0-33.821h5.831v7.85h-5.831Z"
                                transform="translate(-149.958 -256.388)"
                                fill="#ff6120"
                            />
                        </g>
                        <path
                            id="Trazado_672"
                            data-name="Trazado 672"
                            d="M2.99,2.42H4.631V-5.226h2.79V-6.58H.2v1.354H2.99Zm5.58,0h1.546l-.027-7.714h.027L12.865,2.42h1.368l2.749-7.714h.027L16.969,2.42H18.61v-9H16.052L13.631.218H13.6L11.2-6.58H8.571Z"
                            transform="translate(1188.552 396.78)"
                            fill="#fff"
                        />
                    </g>
                    <rect
                        id="Rectángulo_2589"
                        data-name="Rectángulo 2589"
                        width="344"
                        height="100"
                        transform="translate(312 157)"
                        fill="#fff"
                        opacity="0"
                    />
                </g>
            </svg>
        ),
    };
    return (
        <div
            className={`d-flex justify-content-center align-content-center ${props.className}`}
            style={{maxWidth: width, minWidth: width, height: height}}>
            {logosSvg[props.colorMode || "colored"]}
        </div>
    );
}
