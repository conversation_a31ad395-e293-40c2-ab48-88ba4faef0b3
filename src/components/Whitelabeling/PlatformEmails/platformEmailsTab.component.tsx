import {AxiosResponse} from "axios";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import usePermissions from "../../../hooks/usePermissions";
import {ICustomEmail} from "../../../Interfaces/customEmail.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {AxiosError} from "axios";
import {whitelabelingService} from "../../../services/whitelabeling.service";
import {useDebounce} from "../../../hooks/useDebounce";
import {useState} from "react";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {TableV2Component} from "../../Table/TableV2.component";
import CustomPagination from "../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {Link, useTheme} from "@mui/material";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {Column} from "react-table";
import ModeEditOutlinedIcon from "@mui/icons-material/ModeEditOutlined";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import RefreshOutlined from "@mui/icons-material/RefreshOutlined";
import VisibilityOutlined from "@mui/icons-material/VisibilityOutlined";
import Badge from "../../../uicomponents/Atoms/Badge/badge.component";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {WhitelabelingTabIds} from "../../../constants/tabs.constant";
import routeConfig from "../../../constants/routeConfig";
import {useNavigate} from "react-router-dom";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";
import CPTooltip from "../../../uicomponents/Atoms/Tooltip/tooltip.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import PreviewEmailComponent from "../../PreviewEmail/previewEmail.component";
import {useAtom} from "jotai";
import {platformEmailsAtom} from "./platformEmails.atoms";

interface IProps {
    email_header?: string;
}

export default function PlatformEmailsTab({email_header}: IProps) {
    const [_, setPlatformEmailsData] = useAtom(platformEmailsAtom);
    const {hasPermissions} = usePermissions();
    const notify = useNotification();
    const navigate = useNavigate();
    const canUpdateWhitelabeling = hasPermissions([PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_READ]);
    const [searchContents, setSearchContents] = useState<string>("");
    const [sortBy, setSortBy] = useState<INewFilters>({sort: "sorting_display_name__ASC"});
    const theme = useTheme();
    const [page, setPage] = useState(1);
    const [items_per_page, setItemsPerPage] = useState<number>(25);
    const debouncedSearch = useDebounce(searchContents, 500);
    const debouncedSort = useDebounce(sortBy, 500);
    const {activeCompany} = useActiveCompany();
    const queryClient = useQueryClient();
    const [openModal, setOpenModal] = useState(false);
    const [previewEmail, setPreviewEmail] = useState<ICustomEmail | null>(null);

    const handleError = async (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleResetedToDefaultSuccess = () => {
        notify(`Email successfully reseted to default`, "Success");
        queryClient.refetchQueries([WhitelabelingTabIds.PLATFORM_EMAILS]);
    };

    const customizableEmailTemplateTextsQuery = useQuery<IPaginationData<ICustomEmail[]>>(
        [WhitelabelingTabIds.PLATFORM_EMAILS, debouncedSearch, JSON.stringify(debouncedSort), page],
        () => {
            const promiseToReturn: Promise<IPaginationData<ICustomEmail[]>> = new Promise((resolve, reject) =>
                whitelabelingService
                    .loadAllCustomizableEmails(
                        {
                            page,
                            items_per_page,
                            search_word: debouncedSearch,
                            dynamic: debouncedSort,
                        },
                        activeCompany?.friendly_url || "",
                    )
                    .then((response: AxiosResponse<IPaginationData<ICustomEmail[]>>) => resolve(response.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.friendly_url,
        },
    );

    const metadata = customizableEmailTemplateTextsQuery.data?.meta || {total: 0, last_page: 0};

    const resetToDefault = async (emailId: string) => {
        if (!emailId) return;
        const answer = await getUserConfirmation("Are you sure you want to reset to default?" + emailId, {
            mui: true,
        });
        if (!answer.value) return;

        whitelabelingService.deleteCustomizableEmail(
            activeCompany?.friendly_url || "",
            emailId,
            handleResetedToDefaultSuccess,
            handleError,
        );
    };

    const handleGoToUpdatePage = (customizableEmail: ICustomEmail) => {
        setPlatformEmailsData({
            customizableEmail: customizableEmail,
            customizableEmailTemplateTextsQueryKey: WhitelabelingTabIds.PLATFORM_EMAILS,
            emailHeader: email_header,
        });
        navigate(routeConfig.UpdateCustomizableEmail.path);
    };

    const columns: Column<ICustomEmail>[] = [
        {
            id: "display_name",
            Header: "Email Name",
            accessor: "display_name",
            defaultCanSort: true,
            Cell: ({row}) =>
                canUpdateWhitelabeling ? (
                    <Link
                        sx={{cursor: "pointer", color: "blue.600"}}
                        onClick={() => {
                            handleGoToUpdatePage(row.original);
                        }}>
                        {row.original.display_name}
                    </Link>
                ) : (
                    row.original.display_name
                ),
        },
        {
            id: "customized",
            Header: "",
            accessor: "display_name",
            Cell: ({row}) =>
                !!row.original.customized_template_texts && (
                    <Badge sx={{ml: 5, cursor: "default", color: "blue.700"}} color={"blue"} padding="2px 6px">
                        Customized
                    </Badge>
                ),
        },
        {
            id: "description",
            Header: "Description",
            accessor: "description",
            Cell: tableData => tableData.row.original.description,
        },
        {
            id: "reset",
            Header: "Reset",
            accessor: "display_name",
            Cell: ({row}) =>
                !!row.original.customized_template_texts ? (
                    <Box
                        sx={{cursor: "pointer", color: "blue.600", ml: 1}}
                        onClick={() => {
                            resetToDefault(row.original.id || "");
                        }}>
                        <CPTooltip title="Reset to default">
                            <RefreshOutlined />
                        </CPTooltip>
                    </Box>
                ) : (
                    <Box sx={{cursor: "default", color: "blue.200", ml: 1}}>
                        <RefreshOutlined />
                    </Box>
                ),
        },
        ...(canUpdateWhitelabeling
            ? [
                  {
                      id: "select-customizable-email",
                      Header: "Edit",
                      Cell: ({row}) => (
                          <Link
                              sx={{cursor: "pointer", color: "blue.600"}}
                              onClick={() => {
                                  handleGoToUpdatePage(row.original);
                              }}>
                              <ModeEditOutlinedIcon htmlColor={theme.palette.blue[600]} />
                          </Link>
                      ),
                  },
              ]
            : []),
        {
            id: "preview-customizable-email",
            Header: "Preview",
            Cell: ({row}) => (
                <Box
                    sx={{cursor: "pointer", ml: 2}}
                    onClick={() => {
                        setPreviewEmail(row.original);
                        setOpenModal(true);
                    }}>
                    <VisibilityOutlined htmlColor={theme.palette.blue[600]} />
                </Box>
            ),
        },
    ];

    const handleSortChange = (sortBy: string, order_by: string) => {
        setSortBy({sort: `sorting_${order_by}__${sortBy}`});
    };

    return (
        <Box sx={{display: "flex", flexDirection: "column", gap: 3}}>
            <PageInnerHeader
                id="requests-customizable-emails-search"
                searchState={[searchContents, setSearchContents]}
                growSearch
                wrapperSx={{mt: 2}}
            />
            <TableV2Component
                id="customizable-emails"
                columns={columns}
                customData={customizableEmailTemplateTextsQuery?.data?.data || []}
                isLoading={
                    customizableEmailTemplateTextsQuery.isLoading || customizableEmailTemplateTextsQuery.isFetching
                }
                headerBackground={theme.palette.blue[100]}
                noResultsMessage={
                    searchContents === ""
                        ? "No customizable emails found."
                        : "No customizable emails found based on your search or filter criteria. Please update your search or filter and try again."
                }
                onSortChange={handleSortChange}
                orderBy="display_name"
                sortType="ASC"
                pinColumns={["edit"]}
            />
            <CustomPagination
                metadata={metadata}
                page={page}
                itemsPerPage={items_per_page}
                setPage={setPage}
                setItemsPerPage={setItemsPerPage}
            />
            <ModalComponent
                open={openModal}
                onClose={() => setOpenModal(false)}
                showOkButton={false}
                showCancelButton={false}
                content={
                    <PreviewEmailComponent
                        subject={
                            previewEmail?.customized_template_texts?.subject ??
                            previewEmail?.template_texts?.subject ??
                            ""
                        }
                        header_text={
                            previewEmail?.customized_template_texts?.header_text ??
                            previewEmail?.template_texts?.header_text ??
                            ""
                        }
                        intro_text={
                            previewEmail?.customized_template_texts?.intro_text ??
                            previewEmail?.template_texts?.intro_text ??
                            ""
                        }
                        footer_text={
                            previewEmail?.customized_template_texts?.footer_text ??
                            previewEmail?.template_texts?.footer_text ??
                            ""
                        }
                        is_customer_email
                        email_header={email_header}
                    />
                }
            />
        </Box>
    );
}
