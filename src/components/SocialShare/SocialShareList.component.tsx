import {useRef, useState} from "react";
import {Overlay, OverlayTrigger, Tooltip} from "react-bootstrap";
import {useLocation} from "react-router";
import {
    EmailIcon,
    EmailShareButton,
    FacebookIcon,
    FacebookShareButton,
    TwitterShareButton,
    WhatsappIcon,
    WhatsappShareButton,
} from "react-share";
import {SocialMediaEnum} from "../../enums/socialMedia.enum";
import styles from "../../styles/components/socialShare.module.sass";
import ButtonComponent from "../FormControls/button.component";
import {ShareIcon, TwitterNewRoundIcon} from "../Icons";
import OutboundLink from "../Links/OutboundLink.component";
import {ModalComponent} from "../Modal";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import LinkedInShareButton from "./LinkedInShareButton.component";

interface ISocialShareList {
    facebook?: boolean;
    linkedIn?: boolean;
    instagram?: boolean;
    twitter?: boolean;
    discord?: boolean;
    teams?: boolean;
    slack?: boolean;
    copyLink?: boolean;
    email?: boolean;
    url: string;
    buttonText?: string;
    whatsapp: boolean;
    hideShareText?: boolean;
    className?: string;
    customIconSize?: number;
    nativeIconSize?: number;
    displayTitle?: boolean;
}

export function SocialShareList(props: ISocialShareList) {
    const [isLinkCopied, setIsLinkCopied] = useState<boolean>(false);
    const [modalOpen, setModalOpen] = useState<boolean>(false);
    const [socialMediaCommand, setSocialMediaCommand] = useState<string>("");
    const [socialMediaUrl, setSocialMediaUrl] = useState<string>();
    const [titleManualShare, setTitleManualShare] = useState<string | null>(null);
    const targetCopyLink = useRef(null);
    const location = useLocation();
    const DEFAULT_NATIVE_ICON_SIZE = 40;
    const DEFAULT_CUSTOM_ICON_SIZE = 24;

    const onCopyLink = () => {
        const el = document.createElement("input");
        el.value = props.url;
        document.body.appendChild(el);
        el.select();
        document.execCommand("copy");
        document.body.removeChild(el);
    };

    const copyLink = () => {
        setIsLinkCopied(true);
        setTimeout(() => setIsLinkCopied(false), 5000);
    };

    const onClickButtonShareEventManually = (SocialMediaType: SocialMediaEnum) => {
        onCopyLink();

        if (SocialMediaType === SocialMediaEnum.CopyLink) {
            copyLink();
            return;
        }

        switch (SocialMediaType) {
            case SocialMediaEnum.Discord:
                setSocialMediaUrl("https://discord.com/login");
                setSocialMediaCommand("discord:///discord.app");
                break;

            case SocialMediaEnum.Instagram:
                setSocialMediaUrl("https://instagram.com");
                setSocialMediaCommand("");
                break;

            case SocialMediaEnum.Slack:
                setSocialMediaUrl("https://slack.com/workspace-signin");
                setSocialMediaCommand("slack://slack.com");
                break;

            case SocialMediaEnum.Teams:
                setSocialMediaUrl("https://www.microsoft.com/en-ww/microsoft-teams/log-in");
                setSocialMediaCommand("https://teams.microsoft.com/l/entity");
                break;
        }
        setTitleManualShare(SocialMediaType);
        setModalOpen(true);
    };

    return (
        <div className={props.className ? props.className : styles.shareContainer}>
            {!props.hideShareText && (
                <div className="d-flex col-12 col-md-auto justify-content-center">
                    <TitleContainer text="Share" as="h3" noConnector className="mb-0" />
                    <div className={styles.shareIcon}>
                        <ShareIcon size={27} />
                    </div>
                </div>
            )}
            {props.copyLink && (
                <>
                    <ButtonComponent
                        className={isLinkCopied ? `${styles.btnCopyLink}  ${styles.successCopy}` : styles.btnCopyLink}
                        onClick={() => onClickButtonShareEventManually(SocialMediaEnum.CopyLink)}
                        id="copyLink"
                        ref={targetCopyLink}
                        tooltip="Copy Link">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                            height={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                            viewBox="0 0 24 24">
                            <path d="M20 2H10a2 2 0 0 0-2 2v2h8a2 2 0 0 1 2 2v8h2a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2z"></path>
                            <path d="M4 22h10c1.103 0 2-.897 2-2V10c0-1.103-.897-2-2-2H4c-1.103 0-2 .897-2 2v10c0 1.103.897 2 2 2zm2-10h6v2H6v-2zm0 4h6v2H6v-2z"></path>
                        </svg>
                    </ButtonComponent>
                    {props.displayTitle && <SubtitleContainer as="span" text="Copy Link" />}
                    <Overlay target={targetCopyLink.current} show={isLinkCopied} placement="bottom">
                        {props => (
                            <Tooltip id="overlay-example" {...props}>
                                Link copied
                            </Tooltip>
                        )}
                    </Overlay>
                </>
            )}
            {props.discord && (
                <ButtonComponent
                    className={`${styles.btnCopyLink} ${styles.btnDiscord}`}
                    id="shareOnDiscord"
                    onClick={() => onClickButtonShareEventManually(SocialMediaEnum.Discord)}
                    tooltip="Share on Discord">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        height={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        viewBox={`0 0 ${DEFAULT_CUSTOM_ICON_SIZE.toString()} ${DEFAULT_CUSTOM_ICON_SIZE.toString()}`}>
                        <path d="M14.82 4.26a10.14 10.14 0 0 0-.53 1.1 14.66 14.66 0 0 0-4.58 0 10.14 10.14 0 0 0-.53-1.1 16 16 0 0 0-4.13 1.3 17.33 17.33 0 0 0-3 11.59 16.6 16.6 0 0 0 5.07 2.59A12.89 12.89 0 0 0 8.23 18a9.65 9.65 0 0 1-1.71-.83 3.39 3.39 0 0 0 .42-.33 11.66 11.66 0 0 0 10.12 0q.21.18.42.33a10.84 10.84 0 0 1-1.71.84 12.41 12.41 0 0 0 1.08 1.78 16.44 16.44 0 0 0 5.06-2.59 17.22 17.22 0 0 0-3-11.59 16.09 16.09 0 0 0-4.09-1.35zM8.68 14.81a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.93 1.93 0 0 1 1.8 2 1.93 1.93 0 0 1-1.8 2zm6.64 0a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.92 1.92 0 0 1 1.8 2 1.92 1.92 0 0 1-1.8 2z"></path>
                    </svg>
                </ButtonComponent>
            )}
            {props.facebook && (
                <OverlayTrigger placement="bottom" overlay={<Tooltip id="tooltip-discord">Share on Facebook</Tooltip>}>
                    <FacebookShareButton url={props.url ? props.url : window.location.origin + location.pathname}>
                        <FacebookIcon
                            size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}
                            round={true}
                        />
                        {props.displayTitle && <SubtitleContainer as="span" text="Facebook" />}
                    </FacebookShareButton>
                </OverlayTrigger>
            )}
            {props.linkedIn && (
                <LinkedInShareButton
                    url={props.url}
                    size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}>
                    {props.displayTitle && <SubtitleContainer as="span" text="LinkedIn" />}
                </LinkedInShareButton>
            )}
            {props.twitter && (
                <OverlayTrigger
                    placement="bottom"
                    overlay={<Tooltip id="tooltip-discord">Share on {SocialMediaEnum.Twitter}</Tooltip>}>
                    <TwitterShareButton
                        url={props.url ? props.url : window.location.origin + location.pathname}
                        id="shareOnTwitter">
                        <TwitterNewRoundIcon
                            size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}
                        />
                        {/* <TwitterIcon
                            size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}
                            round={true}
                        /> */}
                        {props.displayTitle && <SubtitleContainer as="span" text={SocialMediaEnum.Twitter} />}
                    </TwitterShareButton>
                </OverlayTrigger>
            )}
            {props.instagram && (
                <ButtonComponent
                    className={`${styles.btnCopyLink} ${styles.btnInstagram}`}
                    id="shareOnInstagram"
                    onClick={() => onClickButtonShareEventManually(SocialMediaEnum.Instagram)}
                    tooltip="Share on Instagram">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        height={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        viewBox="0 0 24 24">
                        <path d="M20.947 8.305a6.53 6.53 0 0 0-.419-2.216 4.61 4.61 0 0 0-2.633-2.633 6.606 6.606 0 0 0-2.186-.42c-.962-.043-1.267-.055-3.709-.055s-2.755 0-3.71.055a6.606 6.606 0 0 0-2.185.42 4.607 4.607 0 0 0-2.633 2.633 6.554 6.554 0 0 0-.419 2.185c-.043.963-.056 1.268-.056 3.71s0 2.754.056 3.71c.015.748.156 1.486.419 2.187a4.61 4.61 0 0 0 2.634 2.632 6.584 6.584 0 0 0 2.185.45c.963.043 1.268.056 3.71.056s2.755 0 3.71-.056a6.59 6.59 0 0 0 2.186-.419 4.615 4.615 0 0 0 2.633-2.633c.263-.7.404-1.438.419-2.187.043-.962.056-1.267.056-3.71-.002-2.442-.002-2.752-.058-3.709zm-8.953 8.297c-2.554 0-4.623-2.069-4.623-4.623s2.069-4.623 4.623-4.623a4.623 4.623 0 0 1 0 9.246zm4.807-8.339a1.077 1.077 0 0 1-1.078-1.078 1.077 1.077 0 1 1 2.155 0c0 .596-.482 1.078-1.077 1.078z"></path>
                        <circle cx="11.994" cy="11.979" r="3.003"></circle>
                    </svg>
                    {props.displayTitle && <SubtitleContainer as="span" text="Instagram" />}
                </ButtonComponent>
            )}
            {props.email && (
                <OverlayTrigger placement="bottom" overlay={<Tooltip id="tooltip-discord">Share on Email</Tooltip>}>
                    <EmailShareButton
                        url={props.url ? props.url : window.location.origin + location.pathname}
                        id="shareOnEmail">
                        <EmailIcon
                            size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}
                            round={true}
                        />
                        {props.displayTitle && <SubtitleContainer as="span" text="Email" />}
                    </EmailShareButton>
                </OverlayTrigger>
            )}
            {props.whatsapp && (
                <OverlayTrigger placement="bottom" overlay={<Tooltip id="tooltip-discord">Share on Whatsapp</Tooltip>}>
                    <WhatsappShareButton
                        url={props.url ? props.url : window.location.origin + location.pathname}
                        id="shareOnWhatsapp">
                        <WhatsappIcon
                            size={props.nativeIconSize ? props.nativeIconSize : DEFAULT_NATIVE_ICON_SIZE}
                            round={true}
                        />
                        {props.displayTitle && <SubtitleContainer as="span" text="WhatsApp" />}
                    </WhatsappShareButton>
                </OverlayTrigger>
            )}
            {props.slack && (
                <ButtonComponent
                    className={`${styles.btnCopyLink} ${styles.btnSlack}`}
                    id="shareOnSlack"
                    onClick={() => onClickButtonShareEventManually(SocialMediaEnum.Slack)}
                    tooltip="Share on Slack">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        height={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        viewBox="0 0 24 24">
                        <path d="M6.194 14.644c0 1.16-.943 2.107-2.103 2.107a2.11 2.11 0 0 1-2.104-2.107 2.11 2.11 0 0 1 2.104-2.106h2.103v2.106zm1.061 0c0-1.16.944-2.106 2.104-2.106a2.11 2.11 0 0 1 2.103 2.106v5.274a2.11 2.11 0 0 1-2.103 2.106 2.108 2.108 0 0 1-2.104-2.106v-5.274zm2.104-8.455c-1.16 0-2.104-.948-2.104-2.107s.944-2.106 2.104-2.106a2.11 2.11 0 0 1 2.103 2.106v2.107H9.359zm0 1.06a2.11 2.11 0 0 1 2.103 2.107 2.11 2.11 0 0 1-2.103 2.106H4.092a2.11 2.11 0 0 1-2.104-2.106 2.11 2.11 0 0 1 2.104-2.107h5.267zm8.447 2.107c0-1.16.943-2.107 2.103-2.107a2.11 2.11 0 0 1 2.104 2.107 2.11 2.11 0 0 1-2.104 2.106h-2.103V9.356zm-1.061 0c0 1.16-.944 2.106-2.104 2.106a2.11 2.11 0 0 1-2.103-2.106V4.082a2.11 2.11 0 0 1 2.103-2.106c1.16 0 2.104.946 2.104 2.106v5.274zm-2.104 8.455c1.16 0 2.104.948 2.104 2.107s-.944 2.106-2.104 2.106a2.11 2.11 0 0 1-2.103-2.106v-2.107h2.103zm0-1.06a2.11 2.11 0 0 1-2.103-2.107 2.11 2.11 0 0 1 2.103-2.106h5.268a2.11 2.11 0 0 1 2.104 2.106 2.11 2.11 0 0 1-2.104 2.107h-5.268z"></path>
                    </svg>
                    {props.displayTitle && <SubtitleContainer as="span" text="Slack" />}
                </ButtonComponent>
            )}
            {props.teams && (
                <ButtonComponent
                    className={`${styles.btnCopyLink} ${styles.btnTeams}`}
                    id="shareOnTeams"
                    onClick={() => onClickButtonShareEventManually(SocialMediaEnum.Teams)}
                    tooltip="Share on Teams">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        height={props.customIconSize ? props.customIconSize : DEFAULT_CUSTOM_ICON_SIZE.toString()}
                        viewBox="0 0 24 24">
                        <circle cx="20.288" cy="8.344" r="1.707"></circle>
                        <path d="M18.581 11.513h3.413v3.656c0 .942-.765 1.706-1.707 1.706h-1.706v-5.362zM2.006 4.2v15.6l11.213 1.979V2.221L2.006 4.2zm8.288 5.411-1.95.049v5.752H6.881V9.757l-1.949.098V8.539l5.362-.292v1.364zm3.899.439v8.288h1.95c.808 0 1.463-.655 1.463-1.462V10.05h-3.413zm1.463-4.875c-.586 0-1.105.264-1.463.673v2.555c.357.409.877.673 1.463.673a1.95 1.95 0 0 0 0-3.901z"></path>
                    </svg>
                    {props.displayTitle && <SubtitleContainer as="span" text="MS Teams" />}
                </ButtonComponent>
            )}
            <ModalComponent
                onCancel={() => setModalOpen(false)}
                showHeader
                headerTitle={`Share Event on ${titleManualShare} Manually`}
                modalSwitcher={modalOpen}
                modalSwitcherCallback={() => setModalOpen(false)}
                form={
                    <div className="text-center">
                        <SubtitleContainer as="p" size="18" className="mb-3">
                            This app does not support native sharing, this link must be shared manually.
                            <br />
                            <br />
                            {`The link has already been copied to your clipboard, please click on the button below to open ${titleManualShare}`}
                        </SubtitleContainer>
                        {socialMediaCommand && (
                            <OutboundLink
                                className="cpMainBtn m-2 d-inline-block"
                                style={{margin: 5}}
                                href={socialMediaCommand}
                                id="openApp">
                                Open App
                            </OutboundLink>
                        )}
                        {socialMediaUrl && (
                            <OutboundLink
                                className="cpMainBtn m-2 d-inline-block"
                                style={{margin: 5}}
                                href={socialMediaUrl}
                                id="openWeb">
                                Open on Web
                            </OutboundLink>
                        )}
                    </div>
                }
            />
        </div>
    );
}
SocialShareList.defaultProps = {
    facebook: true,
    linkedIn: true,
    instagram: true,
    twitter: true,
    discord: true,
    teams: true,
    slack: true,
    copyLink: true,
    email: true,
    whatsapp: false,
    hideShareText: false,
};
