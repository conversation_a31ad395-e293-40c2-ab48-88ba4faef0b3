import {AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {Overlay, Tooltip} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {ShortenerModelType, shortenerService} from "../../services/shortener.service";
import styles from "../../styles/components/shareModal.module.sass";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../FormControls/textBox.component";
import {LinkIcon} from "../Icons";
import {ModalComponent} from "../Modal";
import {SocialShareList} from "./SocialShareList.component";

interface IProps {
    isOpen: boolean;
    onClose: (isOpen: boolean) => void;
    modelForShortener?: ShortenerModelType;
    entityId?: string;
    url?: string;
    //? Custom url props
    createUrl?: boolean;
    customUrl?: string;
}

interface IShareResult {
    url_key: string;
    short_url: string;
}

export default function ShareModal(props: IProps) {
    const [isLinkCopied, setIsLinkCopied] = useState<boolean>(false);
    const [urlLoading, setUrlLoading] = useState<boolean>(false);
    const [urlToShare, setUrlToShare] = useState<string>(props.url || window.location.href);

    const shareMethods = useForm({
        defaultValues: {
            link: props.url || "Loading...",
        },
    });
    const targetCopyLink = useRef<HTMLButtonElement>(null);

    const handleError = () => {
        shareMethods.setValue("link", window.location.href);
        setUrlLoading(false);
    };

    const copyLink = () => {
        const el = document.createElement("input");
        el.value = urlToShare;
        document.body.appendChild(el);
        el.select();
        document.execCommand("copy");
        document.body.removeChild(el);
        setIsLinkCopied(true);
        setTimeout(() => setIsLinkCopied(false), 5000);
    };

    const handleSuccess = (response: AxiosResponse<IShareResult>) => {
        shareMethods.setValue("link", response?.data?.short_url);
        setUrlLoading(false);
        setUrlToShare(response?.data?.short_url);
    };

    useEffect(() => {
        if (props.url || !props.modelForShortener || !props.entityId || props.createUrl) return;
        setUrlLoading(true);
        shortenerService.getUrl(props.modelForShortener, props.entityId, handleSuccess, handleError);
        // eslint-disable-next-line
    }, [props.modelForShortener, props.entityId]);

    useEffect(() => {
        if (props.createUrl) {
            setUrlLoading(true);
            shortenerService.createUrl(
                props.customUrl ? props.customUrl : window.location.href,
                handleSuccess,
                handleError,
            );
            return;
        }
        // eslint-disable-next-line
    }, []);

    return (
        <>
            <ModalComponent
                modalSwitcher={props.isOpen}
                modalSwitcherCallback={() => props.onClose(false)}
                showHeader
                headerSeparator
                headerTitle="Share"
                headerPosition="start"
                height="30"
                form={
                    <div className="p-4">
                        <div className="mb-5">
                            <SocialShareList
                                className="d-flex justify-content-center gap-5"
                                hideShareText
                                copyLink={false}
                                discord={false}
                                email={false}
                                slack={false}
                                teams={false}
                                instagram={false}
                                url={urlToShare || props.url || window.location.href}
                                nativeIconSize={80}
                                displayTitle={true}
                            />
                        </div>
                        <hr className="mt-4" />
                        <>
                            <div className="d-flex mb-2">
                                <p className={styles.modalText}>Link</p>
                            </div>
                            <div className="row">
                                <div className="col-md-8 col-xs-12">
                                    <FormProvider {...shareMethods}>
                                        <TextBoxComponent
                                            className={`${styles.disabledBtnLink} w-100`}
                                            containerClassName={`${styles.disabledBtnLink} w-100`}
                                            id="link"
                                            disabled={true}
                                        />
                                    </FormProvider>
                                </div>
                                <div className="col-md-4 col-xs-12 justify-content-center">
                                    <ButtonComponent
                                        className={
                                            isLinkCopied
                                                ? `${styles.btnCopyLink}  ${styles.successCopy}`
                                                : styles.btnCopyLink
                                        }
                                        onClick={() => copyLink()}
                                        id="copyShortLink"
                                        ref={targetCopyLink}
                                        disabled={props.url ? urlLoading : false}
                                        tooltip="Copy Link">
                                        <LinkIcon size={24} />
                                    </ButtonComponent>
                                    <Overlay target={targetCopyLink.current} show={isLinkCopied} placement="bottom">
                                        {innerProps => (
                                            <Tooltip id="overlay-example" {...innerProps}>
                                                Link copied
                                            </Tooltip>
                                        )}
                                    </Overlay>
                                </div>
                            </div>
                        </>
                    </div>
                }
            />
        </>
    );
}
