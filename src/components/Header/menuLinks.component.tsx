import {builder, BuilderComponent} from "@builder.io/react";
import {useEffect, useState} from "react";
import {Image} from "react-bootstrap";
import {NavLink, useLocation, useNavigate} from "react-router-dom";
import {IFollowers} from "../../Interfaces/followers.interface";
import {IProfile} from "../../Interfaces/people.interface";
import routeConfig from "../../constants/routeConfig";
import {VENDOR_PROFILES} from "../../constants/vendorProfiles.constants";
import {USERS_TYPE} from "../../enums/usersTypes.enum";
import {usePeople} from "../../hooks/fetches/usePeople";
import useAuthState from "../../hooks/useAuthState";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import {
    ChannelExplorerIcon,
    ChannelPitchIcon,
    CompanyIcon,
    GaugeIcon,
    HomeIcon,
    LogoutIcon,
    MicIcon,
    PublicCategoryIcon,
    ReportIcon,
    UserGroupIcon,
    UserIcon,
    UserPlusIcon,
    VideoCameraIcon,
} from "../Icons";
import {FollowingIcon} from "../Icons/followingIcon";
import {MyVideosIcon} from "../Icons/myVideosIcon";
import ProfileTypeBadge from "../Profile/profileTypeBadge.component";

interface IMenuLink {
    isAdmin: boolean;
    isMobile?: boolean;
    isLogged?: boolean;
}

export default function MenuLinks(props: IMenuLink) {
    const [isAdminPage, setIsAdminPage] = useState(false);
    const {authState} = useAuthState();
    const {peopleFollowings} = usePeople(authState.friendly_url!, {isCompany: false});
    const userFollowers = peopleFollowings.data || [];
    const navigate = useNavigate();
    const location = useLocation();
    const MAX_FOLLOWING_LENGTH = 5;
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [builderIsLoading, setBuilderIsLoading] = useState<boolean>(true);

    useEffect(() => {
        if (location.pathname.includes("admin/")) {
            setIsAdminPage(true);
        } else {
            setIsAdminPage(false);
        }
    }, [location]);

    const goToUser = user => {
        if (user.type_is_of_vendor) {
            navigate(routeConfig.VendorProfile.path.replace(":id", user.friendly_url), {state: {entityId: user.id}});
        } else {
            navigate(routeConfig.UserProfile.path.replace(":id", user.friendly_url), {state: {entityId: user.id}});
        }
    };

    useEffect(() => {
        builder
            .get("w-sidebar-links", {url: "/links"})
            .promise()
            .then(data => {
                if (data) {
                    setBuilderContentJson(data);
                    setBuilderIsLoading(false);
                }
            });
    }, []);

    return (
        <div>
            {props.isMobile && props.isLogged ? (
                <>
                    <NavLink
                        id="myAccount"
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Account.path}>
                        <UserIcon size={25} color="mono" />
                        <span>My account</span>
                    </NavLink>
                    {authState.company_profile_claimer_id === authState.id && authState.company_friendly_url && (
                        <NavLink
                            to={routeConfig.VendorProfile.path.replace(":id", authState.company_friendly_url)}
                            className="menuLink popoverLink d-flex flex-column justify-content-center gap-2">
                            <div className="companyImage">
                                <Image
                                    src={
                                        authState.company_avatar
                                            ? authState.company_avatar
                                            : createImageFromInitials(
                                                  36,
                                                  authState?.company_display_name!,
                                                  "",
                                                  "company",
                                              )
                                    }
                                    roundedCircle
                                />
                            </div>
                            <span>Manage Business Profile</span>
                        </NavLink>
                    )}
                </>
            ) : props.isMobile ? null : (
                <NavLink
                    id="home"
                    className={({isActive}) => (isActive || location.pathname === "/" ? "menuLink active" : "menuLink")}
                    to={routeConfig.Home.path}>
                    <HomeIcon size={25} />
                    <span>Home</span>
                </NavLink>
            )}
            {props.isMobile && !props.isLogged ? (
                <>
                    <NavLink
                        id="logIn"
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Login.path}>
                        <UserIcon size={25} color="mono" />
                        Sign In
                    </NavLink>
                    <NavLink
                        id="createAccount"
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Register.path}>
                        <UserPlusIcon size={25} />
                        Create your account
                    </NavLink>
                </>
            ) : null}
            {!isAdminPage ? (
                <>
                    <NavLink
                        id="categoryDirectory"
                        to={routeConfig.Categories.path}
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}>
                        <PublicCategoryIcon
                            size={23}
                            color={location.pathname.includes(routeConfig.Categories.path) ? "colored" : "mono"}
                        />
                        <span>Categories</span>
                    </NavLink>
                    <NavLink
                        id="myProfile"
                        onClick={ev => {
                            if (!authState?.id) {
                                ev.preventDefault();
                                navigate(routeConfig.Login.path + "?redirect_url=" + routeConfig.UserProfile.path);
                            }
                        }}
                        state={{friendlyUrl: authState.friendly_url!, entityId: authState.id}}
                        to={routeConfig.UserProfile.path.replace(":id", authState.friendly_url!)}
                        className={({isActive}) =>
                            isActive ||
                            (location.pathname.includes(`/u/${authState?.friendly_url}`) &&
                                !location.search.includes("?tab=videos") &&
                                !location.search.includes("?tab=following"))
                                ? "menuLink active"
                                : "menuLink"
                        }>
                        <UserIcon
                            size={25}
                            color={
                                location.pathname ===
                                    routeConfig.UserProfile.path.replace(":id", authState.friendly_url!) &&
                                !location.search.includes("?tab=videos") &&
                                !location.search.includes("?tab=following")
                                    ? "colored"
                                    : "mono"
                            }
                        />
                        <span>My Profile</span>
                    </NavLink>
                    <NavLink
                        id="myVideos"
                        onClick={ev => {
                            if (!authState?.id) {
                                ev.preventDefault();
                                navigate(
                                    routeConfig.Login.path +
                                        "?redirect_url=" +
                                        routeConfig.UserProfile.path +
                                        "?tab=videos",
                                );
                            }
                        }}
                        className={({isActive}) =>
                            isActive || (location.pathname.includes("/u/") && location.search.includes("?tab=videos"))
                                ? "menuLink active"
                                : "menuLink"
                        }
                        state={{friendlyUrl: authState.friendly_url!, entityId: authState.id}}
                        to={{
                            pathname: routeConfig.UserProfile.path.replace(":id", authState.friendly_url!),
                            search: "?tab=videos",
                        }}>
                        <MyVideosIcon
                            size={25}
                            color={
                                `${location.pathname}${location.search}` ===
                                `${routeConfig.UserProfile.path.replace(":id", authState.friendly_url!)}?tab=videos`
                                    ? "colored"
                                    : "mono"
                            }
                        />
                        <span>My Videos</span>
                    </NavLink>
                    <NavLink
                        id="explorer"
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Explorer.path}>
                        <ChannelExplorerIcon
                            size={23}
                            color={location.pathname === routeConfig.Explorer.path ? "colored" : "mono"}
                        />
                        <span>Explorer</span>
                    </NavLink>
                    <NavLink
                        id="pitches"
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Pitch.path}>
                        <ChannelPitchIcon
                            size={23}
                            color={location.pathname === routeConfig.Pitch.path ? "colored" : "mono"}
                        />
                        <span>Pitches & Events</span>
                    </NavLink>
                    {/*<NavLink id="channelCash" className={({isActive}) => isActive ? "menuLink active" : "menuLink"} to={routeConfig.Cash.path}>*/}
                    {/*    <ChannelCashIcon*/}
                    {/*        size={23}*/}
                    {/*        color={location.pathname === routeConfig.Cash.path ? "colored" : "mono"}*/}
                    {/*    />*/}
                    {/*    <span>Channel Cash</span>*/}
                    {/*</NavLink>*/}
                    <NavLink
                        id="following"
                        className={({isActive}) =>
                            isActive ||
                            (location.pathname.includes("/u/") && location.search.includes("?tab=following"))
                                ? "menuLink active"
                                : "menuLink"
                        }
                        onClick={ev => {
                            if (!authState?.id) {
                                ev.preventDefault();
                                navigate(
                                    routeConfig.Login.path +
                                        "?redirect_url=" +
                                        routeConfig.UserProfile.path +
                                        "?tab=following",
                                );
                            }
                        }}
                        state={{entityId: authState.id, friendlyUrl: authState.friendly_url}}
                        to={{
                            pathname: routeConfig.UserProfile.path.replace(":id", authState.friendly_url!),
                            search: "?tab=following",
                        }}>
                        <FollowingIcon
                            size={23}
                            color={
                                `${location.pathname}${location.search}` ===
                                `${routeConfig.UserProfile.path.replace(":id", authState.friendly_url!)}?tab=following`
                                    ? "colored"
                                    : "mono"
                            }
                        />

                        <span>Following</span>
                    </NavLink>
                    <div className={`list-following`}>
                        {userFollowers.length > 0 && (
                            <>
                                {userFollowers.slice(0, MAX_FOLLOWING_LENGTH).map((user: IFollowers, index: number) => {
                                    const userProfile: IProfile = {
                                        id: user.id,
                                        name: user.name,
                                        avatar: user.avatar.src,
                                        handle: user.handle,
                                        friendly_url: user.friendly_url,
                                        job_title_name: "",
                                        followers_count: user.followersCount,
                                        is_company: user.is_company,
                                        current_user_is_following: user.current_user_is_following,
                                        type: user.type,
                                        type_is_of_vendor: user.type_is_of_vendor,
                                    };
                                    return (
                                        <div
                                            id={`follow${index + 1}`}
                                            onClick={() => goToUser(user)}
                                            key={user.id}
                                            className="following-item at-item">
                                            <ProfileTypeBadge
                                                profileType={
                                                    user.type === USERS_TYPE.INFLUENCER
                                                        ? "influencer"
                                                        : VENDOR_PROFILES.includes(user.type)
                                                        ? "company"
                                                        : "user"
                                                }
                                                profile={userProfile}
                                                shortMode
                                            />
                                            <div className="followerImgContainer">
                                                <img
                                                    src={
                                                        user.avatar?.src ??
                                                        createImageFromInitials(
                                                            30,
                                                            user.name,
                                                            "",
                                                            user.is_company ? "company" : "user",
                                                        )
                                                    }
                                                    alt={`${user.name} avatar`}
                                                    className="speakerImage"
                                                />
                                            </div>
                                            <span
                                                className={`followerName ${
                                                    VENDOR_PROFILES.includes(user.type)
                                                        ? "followerNameVendor"
                                                        : user.type === USERS_TYPE.INFLUENCER
                                                        ? "followerNameInfluencer"
                                                        : ""
                                                }`}>
                                                {user.name}
                                            </span>
                                        </div>
                                    );
                                })}
                                {userFollowers.length > MAX_FOLLOWING_LENGTH && (
                                    <ButtonComponent
                                        id="show-more-following"
                                        className="show-more-following"
                                        onClick={() => {
                                            navigate(
                                                {
                                                    pathname: routeConfig.UserProfile.path.replace(
                                                        "id",
                                                        authState.friendly_url!,
                                                    ),
                                                    search: "?tab=following",
                                                },
                                                {state: {entityId: authState.id}},
                                            );
                                        }}>
                                        {userFollowers.length > 5 && "See All ->"}
                                    </ButtonComponent>
                                )}
                            </>
                        )}
                    </div>
                </>
            ) : (
                <>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Dashboard.path}>
                        <GaugeIcon size={25} />
                        <span>Dashboard</span>
                    </NavLink>
                    <NavLink className="menuLink difPadding2" to={routeConfig.PitchDay.path}>
                        <VideoCameraIcon size={19} />
                        <span>Pitch day</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.PitchManagement.path}>
                        <MicIcon size={25} />
                        <span>Pitches</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.PitchVideoUpload.path}>
                        <VideoCameraIcon size={25} />
                        <span>Pitch video upload</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.PollManagement.path}>
                        <ReportIcon size={25} />
                        <span>Polls</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.CompanyManagement.path}>
                        <CompanyIcon size={25} />
                        <span>Companies</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.UsersManagement.path}>
                        <UserGroupIcon size={25} />
                        <span>Users</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.ChannelDealsManagement.path}>
                        <UserGroupIcon size={25} />
                        <span>Channel Deals</span>
                    </NavLink>
                    <NavLink
                        className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                        to={routeConfig.Reports.path}>
                        <ReportIcon size={25} />
                        <span>Reports</span>
                    </NavLink>
                </>
            )}

            {builderIsLoading && (
                <div className="d-flex justify-content-center">
                    <Loader inline loading />
                </div>
            )}
            {builderContentJson != null && !isAdminPage && (
                <>
                    <BuilderComponent model="w-sidebar-links" content={builderContentJson} />
                </>
            )}
            {props.isMobile && props.isLogged && (
                <NavLink
                    id="logOut"
                    className={({isActive}) => (isActive ? "menuLink active" : "menuLink")}
                    to={routeConfig.Logout.path}>
                    <LogoutIcon size={25} />
                    Sign Out
                </NavLink>
            )}
        </div>
    );
}
