{"v": "5.7.11", "fr": 30, "ip": 0, "op": 60, "w": 512, "h": 512, "nm": "<PERSON><PERSON><PERSON>", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "X line 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41, -3, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112, -74], [-30, 68]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 40, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.38823529411764707, 0.2784313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [100]}, {"t": 40, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 26, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "X line 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41, -3, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112, -74], [-30, 68]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 40, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.38823529411764707, 0.2784313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 40, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 26, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Circle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar opacityInertialBounce, opacityBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nopacityInertialBounce = effect('Bounce & Drop - ukramedia.com')(44);\nopacityBounceBack = effect('Bounce & Drop - ukramedia.com')(45);\ntry {\n    if (opacityInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(47);\n                freq = effect('Bounce & Drop - ukramedia.com')(48);\n                decay = effect('Bounce & Drop - ukramedia.com')(49);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (opacityBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(52);\n            g = effect('Bounce & Drop - ukramedia.com')(53);\n            nMax = effect('Bounce & Drop - ukramedia.com')(54);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar rotationInertialBounce, rotationBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nrotationInertialBounce = effect('Bounce & Drop - ukramedia.com')(30);\nrotationBounceBack = effect('Bounce & Drop - ukramedia.com')(31);\ntry {\n    if (rotationInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(33);\n                freq = effect('Bounce & Drop - ukramedia.com')(34);\n                decay = effect('Bounce & Drop - ukramedia.com')(35);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (rotationBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(38);\n            g = effect('Bounce & Drop - ukramedia.com')(39);\n            nMax = effect('Bounce & Drop - ukramedia.com')(40);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar positionInertialBounce, positionBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\npositionInertialBounce = effect('Bounce & Drop - ukramedia.com')(2);\npositionBounceBack = effect('Bounce & Drop - ukramedia.com')(3);\ntry {\n    if (positionInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(5);\n                freq = effect('Bounce & Drop - ukramedia.com')(6);\n                decay = effect('Bounce & Drop - ukramedia.com')(7);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (positionBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(10);\n            g = effect('Bounce & Drop - ukramedia.com')(11);\n            nMax = effect('Bounce & Drop - ukramedia.com')(12);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [140.061, 140.061, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 1.01]}, "t": 10, "s": [0, 0, 100]}, {"t": 20, "s": [140, 140, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar scaleInertialBounce, scaleBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nscaleInertialBounce = effect('Bounce & Drop - ukramedia.com')(16);\nscaleBounceBack = effect('Bounce & Drop - ukramedia.com')(17);\ntry {\n    if (scaleInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(19);\n                freq = effect('Bounce & Drop - ukramedia.com')(20);\n                decay = effect('Bounce & Drop - ukramedia.com')(21);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (scaleBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(24);\n            g = effect('Bounce & Drop - ukramedia.com')(25);\n            nMax = effect('Bounce & Drop - ukramedia.com')(26);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Bounce & Drop - ukramedia.com", "np": 70, "mn": "Pseudo/animationControl", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Position", "mn": "Pseudo/animationControl-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0005", "ix": 5, "v": {"a": 0, "k": 0.05, "ix": 5}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0006", "ix": 6, "v": {"a": 0, "k": 4, "ix": 6}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0007", "ix": 7, "v": {"a": 0, "k": 8, "ix": 7}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0008", "ix": 8, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0009", "ix": 9, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0010", "ix": 10, "v": {"a": 0, "k": 0.7, "ix": 10}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0011", "ix": 11, "v": {"a": 0, "k": 5000, "ix": 11}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0012", "ix": 12, "v": {"a": 0, "k": 9, "ix": 12}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0013", "ix": 13, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0014", "ix": 14, "v": 0}, {"ty": 6, "nm": "Scale", "mn": "Pseudo/animationControl-0015", "ix": 15, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0016", "ix": 16, "v": {"a": 0, "k": 1, "ix": 16}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0018", "ix": 18, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0019", "ix": 19, "v": {"a": 0, "k": 0.8, "ix": 19}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0020", "ix": 20, "v": {"a": 0, "k": 4, "ix": 20}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0021", "ix": 21, "v": {"a": 0, "k": 8, "ix": 21}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0022", "ix": 22, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0023", "ix": 23, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0024", "ix": 24, "v": {"a": 0, "k": 0.7, "ix": 24}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0025", "ix": 25, "v": {"a": 0, "k": 5000, "ix": 25}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0026", "ix": 26, "v": {"a": 0, "k": 9, "ix": 26}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0027", "ix": 27, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0028", "ix": 28, "v": 0}, {"ty": 6, "nm": "Rotation", "mn": "Pseudo/animationControl-0029", "ix": 29, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0030", "ix": 30, "v": {"a": 0, "k": 0, "ix": 30}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0031", "ix": 31, "v": {"a": 0, "k": 0, "ix": 31}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0032", "ix": 32, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0033", "ix": 33, "v": {"a": 0, "k": 0.05, "ix": 33}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0034", "ix": 34, "v": {"a": 0, "k": 4, "ix": 34}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0035", "ix": 35, "v": {"a": 0, "k": 8, "ix": 35}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0036", "ix": 36, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0037", "ix": 37, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0038", "ix": 38, "v": {"a": 0, "k": 0.7, "ix": 38}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0039", "ix": 39, "v": {"a": 0, "k": 5000, "ix": 39}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0040", "ix": 40, "v": {"a": 0, "k": 9, "ix": 40}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0041", "ix": 41, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0042", "ix": 42, "v": 0}, {"ty": 6, "nm": "Opacity", "mn": "Pseudo/animationControl-0043", "ix": 43, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0044", "ix": 44, "v": {"a": 0, "k": 0, "ix": 44}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0045", "ix": 45, "v": {"a": 0, "k": 0, "ix": 45}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0046", "ix": 46, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0047", "ix": 47, "v": {"a": 0, "k": 0.05, "ix": 47}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0048", "ix": 48, "v": {"a": 0, "k": 4, "ix": 48}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0049", "ix": 49, "v": {"a": 0, "k": 8, "ix": 49}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0050", "ix": 50, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0051", "ix": 51, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0052", "ix": 52, "v": {"a": 0, "k": 0.7, "ix": 52}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0053", "ix": 53, "v": {"a": 0, "k": 5000, "ix": 53}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0054", "ix": 54, "v": {"a": 0, "k": 9, "ix": 54}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0055", "ix": 55, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0056", "ix": 56, "v": 0}, {"ty": 6, "nm": "Global Inertial Bounce Options", "mn": "Pseudo/animationControl-0057", "ix": 57, "v": 0}, {"ty": 7, "nm": "Enable Global Inertial Bounce", "mn": "Pseudo/animationControl-0058", "ix": 58, "v": {"a": 0, "k": 0, "ix": 58}}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0059", "ix": 59, "v": {"a": 0, "k": 0.05, "ix": 59}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0060", "ix": 60, "v": {"a": 0, "k": 4, "ix": 60}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0061", "ix": 61, "v": {"a": 0, "k": 8, "ix": 61}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0062", "ix": 62, "v": 0}, {"ty": 6, "nm": "Global Bounce Back Options", "mn": "Pseudo/animationControl-0063", "ix": 63, "v": 0}, {"ty": 7, "nm": "Enable Global Bounce Back", "mn": "Pseudo/animationControl-0064", "ix": 64, "v": {"a": 0, "k": 0, "ix": 64}}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0065", "ix": 65, "v": {"a": 0, "k": 0.7, "ix": 65}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0066", "ix": 66, "v": {"a": 0, "k": 5000, "ix": 66}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0067", "ix": 67, "v": {"a": 0, "k": 9, "ix": 67}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0068", "ix": 68, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -77.215], [77.216, 0], [0, 77.215], [-77.215, 0]], "o": [[0, 77.215], [-77.215, 0], [0, -77.215], [77.216, 0]], "v": [[139.811, 0], [0, 139.811], [-139.811, 0], [0, -139.811]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.38823529411764707, 0.2784313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [140.061, 140.061], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Circle 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41.25, "s": [50]}, {"t": 55, "s": [0]}], "ix": 11, "x": "var $bm_rt;\nvar opacityInertialBounce, opacityBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nopacityInertialBounce = effect('Bounce & Drop - ukramedia.com')(44);\nopacityBounceBack = effect('Bounce & Drop - ukramedia.com')(45);\ntry {\n    if (opacityInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(47);\n                freq = effect('Bounce & Drop - ukramedia.com')(48);\n                decay = effect('Bounce & Drop - ukramedia.com')(49);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (opacityBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(52);\n            g = effect('Bounce & Drop - ukramedia.com')(53);\n            nMax = effect('Bounce & Drop - ukramedia.com')(54);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar rotationInertialBounce, rotationBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nrotationInertialBounce = effect('Bounce & Drop - ukramedia.com')(30);\nrotationBounceBack = effect('Bounce & Drop - ukramedia.com')(31);\ntry {\n    if (rotationInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(33);\n                freq = effect('Bounce & Drop - ukramedia.com')(34);\n                decay = effect('Bounce & Drop - ukramedia.com')(35);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (rotationBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(38);\n            g = effect('Bounce & Drop - ukramedia.com')(39);\n            nMax = effect('Bounce & Drop - ukramedia.com')(40);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar positionInertialBounce, positionBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\npositionInertialBounce = effect('Bounce & Drop - ukramedia.com')(2);\npositionBounceBack = effect('Bounce & Drop - ukramedia.com')(3);\ntry {\n    if (positionInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(5);\n                freq = effect('Bounce & Drop - ukramedia.com')(6);\n                decay = effect('Bounce & Drop - ukramedia.com')(7);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (positionBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(10);\n            g = effect('Bounce & Drop - ukramedia.com')(11);\n            nMax = effect('Bounce & Drop - ukramedia.com')(12);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [140.061, 140.061, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 16.794]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, -0.38]}, "t": 10, "s": [132, 132, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, -0.21]}, "t": 35, "s": [130, 130, 100]}, {"t": 55, "s": [175, 175, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar scaleInertialBounce, scaleBounceBack, n, n, t, t, v, amp, freq, decay, v, amp, freq, decay, e, g, nMax, e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\nscaleInertialBounce = effect('Bounce & Drop - ukramedia.com')(16);\nscaleBounceBack = effect('Bounce & Drop - ukramedia.com')(17);\ntry {\n    if (scaleInertialBounce == 1) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time) {\n                n--;\n            }\n        }\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n).time);\n        }\n        if (effect('Bounce & Drop - ukramedia.com')(58) == 1) {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(59);\n                freq = effect('Bounce & Drop - ukramedia.com')(60);\n                decay = effect('Bounce & Drop - ukramedia.com')(61);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        } else {\n            if (n > 0 && t < 1) {\n                v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n                amp = effect('Bounce & Drop - ukramedia.com')(19);\n                freq = effect('Bounce & Drop - ukramedia.com')(20);\n                decay = effect('Bounce & Drop - ukramedia.com')(21);\n                $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n            } else {\n                $bm_rt = value;\n            }\n        }\n    } else if (scaleBounceBack == 1) {\n        if (effect('Bounce & Drop - ukramedia.com')(64) == 1) {\n            e = effect('Bounce & Drop - ukramedia.com')(65);\n            g = effect('Bounce & Drop - ukramedia.com')(66);\n            nMax = effect('Bounce & Drop - ukramedia.com')(67);\n        } else {\n            e = effect('Bounce & Drop - ukramedia.com')(24);\n            g = effect('Bounce & Drop - ukramedia.com')(25);\n            nMax = effect('Bounce & Drop - ukramedia.com')(26);\n        }\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time).index;\n            if (key(n).time > time)\n                n--;\n        }\n        if (n > 0) {\n            t = $bm_sub(time, key(n).time);\n            v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n            vl = length(v);\n            if ($bm_isInstanceOfArray(value)) {\n                vu = vl > 0 ? normalize(v) : [\n                    0,\n                    0,\n                    0\n                ];\n            } else {\n                vu = v < 0 ? -1 : 1;\n            }\n            tCur = 0;\n            segDur = $bm_div($bm_mul(2, vl), g);\n            tNext = segDur;\n            nb = 1;\n            while (tNext < t && nb <= nMax) {\n                vl *= e;\n                segDur *= e;\n                tCur = tNext;\n                tNext = $bm_sum(tNext, segDur);\n                nb++;\n            }\n            if (nb <= nMax) {\n                delta = $bm_sub(t, tCur);\n                $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n            } else {\n                $bm_rt = value;\n            }\n        } else\n            $bm_rt = value;\n    } else {\n        $bm_rt = value;\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Bounce & Drop - ukramedia.com", "np": 70, "mn": "Pseudo/animationControl", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Position", "mn": "Pseudo/animationControl-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0005", "ix": 5, "v": {"a": 0, "k": 0.05, "ix": 5}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0006", "ix": 6, "v": {"a": 0, "k": 4, "ix": 6}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0007", "ix": 7, "v": {"a": 0, "k": 8, "ix": 7}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0008", "ix": 8, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0009", "ix": 9, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0010", "ix": 10, "v": {"a": 0, "k": 0.7, "ix": 10}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0011", "ix": 11, "v": {"a": 0, "k": 5000, "ix": 11}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0012", "ix": 12, "v": {"a": 0, "k": 9, "ix": 12}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0013", "ix": 13, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0014", "ix": 14, "v": 0}, {"ty": 6, "nm": "Scale", "mn": "Pseudo/animationControl-0015", "ix": 15, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0016", "ix": 16, "v": {"a": 0, "k": 1, "ix": 16}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0018", "ix": 18, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0019", "ix": 19, "v": {"a": 0, "k": 0.8, "ix": 19}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0020", "ix": 20, "v": {"a": 0, "k": 4, "ix": 20}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0021", "ix": 21, "v": {"a": 0, "k": 8, "ix": 21}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0022", "ix": 22, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0023", "ix": 23, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0024", "ix": 24, "v": {"a": 0, "k": 0.7, "ix": 24}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0025", "ix": 25, "v": {"a": 0, "k": 5000, "ix": 25}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0026", "ix": 26, "v": {"a": 0, "k": 9, "ix": 26}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0027", "ix": 27, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0028", "ix": 28, "v": 0}, {"ty": 6, "nm": "Rotation", "mn": "Pseudo/animationControl-0029", "ix": 29, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0030", "ix": 30, "v": {"a": 0, "k": 0, "ix": 30}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0031", "ix": 31, "v": {"a": 0, "k": 0, "ix": 31}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0032", "ix": 32, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0033", "ix": 33, "v": {"a": 0, "k": 0.05, "ix": 33}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0034", "ix": 34, "v": {"a": 0, "k": 4, "ix": 34}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0035", "ix": 35, "v": {"a": 0, "k": 8, "ix": 35}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0036", "ix": 36, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0037", "ix": 37, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0038", "ix": 38, "v": {"a": 0, "k": 0.7, "ix": 38}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0039", "ix": 39, "v": {"a": 0, "k": 5000, "ix": 39}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0040", "ix": 40, "v": {"a": 0, "k": 9, "ix": 40}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0041", "ix": 41, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0042", "ix": 42, "v": 0}, {"ty": 6, "nm": "Opacity", "mn": "Pseudo/animationControl-0043", "ix": 43, "v": 0}, {"ty": 7, "nm": "Enable Inertial Bounce", "mn": "Pseudo/animationControl-0044", "ix": 44, "v": {"a": 0, "k": 0, "ix": 44}}, {"ty": 7, "nm": "Enable <PERSON><PERSON><PERSON>", "mn": "Pseudo/animationControl-0045", "ix": 45, "v": {"a": 0, "k": 0, "ix": 45}}, {"ty": 6, "nm": "Inertial Bounce Options", "mn": "Pseudo/animationControl-0046", "ix": 46, "v": 0}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0047", "ix": 47, "v": {"a": 0, "k": 0.05, "ix": 47}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0048", "ix": 48, "v": {"a": 0, "k": 4, "ix": 48}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0049", "ix": 49, "v": {"a": 0, "k": 8, "ix": 49}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0050", "ix": 50, "v": 0}, {"ty": 6, "nm": "<PERSON><PERSON>ce Back Options", "mn": "Pseudo/animationControl-0051", "ix": 51, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0052", "ix": 52, "v": {"a": 0, "k": 0.7, "ix": 52}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0053", "ix": 53, "v": {"a": 0, "k": 5000, "ix": 53}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0054", "ix": 54, "v": {"a": 0, "k": 9, "ix": 54}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0055", "ix": 55, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0056", "ix": 56, "v": 0}, {"ty": 6, "nm": "Global Inertial Bounce Options", "mn": "Pseudo/animationControl-0057", "ix": 57, "v": 0}, {"ty": 7, "nm": "Enable Global Inertial Bounce", "mn": "Pseudo/animationControl-0058", "ix": 58, "v": {"a": 0, "k": 0, "ix": 58}}, {"ty": 0, "nm": "Amplitude", "mn": "Pseudo/animationControl-0059", "ix": 59, "v": {"a": 0, "k": 0.05, "ix": 59}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/animationControl-0060", "ix": 60, "v": {"a": 0, "k": 4, "ix": 60}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/animationControl-0061", "ix": 61, "v": {"a": 0, "k": 8, "ix": 61}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0062", "ix": 62, "v": 0}, {"ty": 6, "nm": "Global Bounce Back Options", "mn": "Pseudo/animationControl-0063", "ix": 63, "v": 0}, {"ty": 7, "nm": "Enable Global Bounce Back", "mn": "Pseudo/animationControl-0064", "ix": 64, "v": {"a": 0, "k": 0, "ix": 64}}, {"ty": 0, "nm": "Elasticity", "mn": "Pseudo/animationControl-0065", "ix": 65, "v": {"a": 0, "k": 0.7, "ix": 65}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/animationControl-0066", "ix": 66, "v": {"a": 0, "k": 5000, "ix": 66}}, {"ty": 0, "nm": "nMax", "mn": "Pseudo/animationControl-0067", "ix": 67, "v": {"a": 0, "k": 9, "ix": 67}}, {"ty": 6, "nm": "", "mn": "Pseudo/animationControl-0068", "ix": 68, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -77.215], [77.216, 0], [0, 77.215], [-77.215, 0]], "o": [[0, 77.215], [-77.215, 0], [0, -77.215], [77.216, 0]], "v": [[139.811, 0], [0, 139.811], [-139.811, 0], [0, -139.811]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.38823529411764707, 0.2784313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [140.061, 140.061], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}], "markers": []}