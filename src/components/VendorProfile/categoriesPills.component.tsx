import useTheme from "@mui/material/styles/useTheme";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useMemo, useState} from "react";
import {useForm} from "react-hook-form";
import {Link, useLocation} from "react-router-dom";
import {PRODUCT_LIMIT} from "../../constants/errorMessages.constant";
import routeConfig from "../../constants/routeConfig";
import {useCompany} from "../../hooks/fetches/useCompany";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import useSettings from "../../hooks/useSettings";
import {ICategory} from "../../Interfaces/categories.interface";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IProducts} from "../../Interfaces/products.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {categoriesService} from "../../services/category.service";
import styles from "../../styles/components/categoriesPills.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {getErrorFromArray} from "../../utils/error.util";
import ButtonComponent from "../FormControls/button.component";
import TitleContainer from "../Titles/titleContainer.component";
import {ProductsModal} from "./productsModal.component";

interface IProps {
    useMuiStyles?: boolean;
}

export default function CategoriesPills(props: IProps) {
    const [showProductModal, setShowProductModal] = useState<boolean>(false);
    const [productDefaultValue, setProductDefaultValue] = useState<IProducts>();
    const [productsMainCategories, setProductsMainCategories] = useState<ICategory[]>([]);
    const [loading, setLoading] = useState(false);

    const notify = useNotification();
    const location = useLocation();
    const friendly_url = location.pathname.replace("/v/", "").split("?")[0];
    const {settings, updateSettings} = useSettings();
    const {company, companyRules, companyProducts} = useCompany(friendly_url, {
        isCompany: true,
        calls: {
            company: true,
            companyRules: true,
            companyProducts: true,
        },
        companyParams: ["categories", "profile_claimer_user_id"],
    });
    const companyInfo = company.data;
    const newCatMethods = useForm<{newCategory: string}>();
    const newCategoryWatcher = newCatMethods.watch("newCategory");
    const companyCategories = useMemo(() => companyInfo?.categories || [], [companyInfo]);
    const {hasPermissions, isSuperAdmin} = usePermissions({overrideCompanyId: company.data?.id});
    const isEditable = hasPermissions([PERMISSION_GROUPS.PRODUCTS_UPDATE], {id: companyInfo?.id});
    const theme = useTheme();

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setLoading(false);
    };

    const resetValueToCurrent = () => {
        return newCatMethods.reset({
            newCategory: "0",
        });
    };

    const handleCloseProductModal = () => {
        setProductDefaultValue(undefined);
        setShowProductModal(false);
        resetValueToCurrent();
    };

    const handleSuccessCategories = (response: AxiosResponse<ICategory[]>) => {
        const allCategories: [string, string][] = response.data
            .filter(cat => cat.parent_id !== null && !cat.is_hidden)
            .map(c => [c.id, c.name]);
        updateSettings(UPDATE_SETTINGS, {cp_categories: allCategories, all_categories: response.data});
    };

    const getRemainingProductCount = () => {
        let count = 0;
        const foundRule = companyRules.data?.find(rule => rule.profile_rule === PRODUCT_LIMIT);
        if (foundRule) {
            const {rule_value} = foundRule;
            count = (rule_value as number) - (companyProducts.data?.length || 0);
        }
        if (count < 0) count = 0;
        return count;
    };

    useEffect(() => {
        let allProductCategories: any[] = [];
        if (!settings.all_categories) {
            categoriesService.getAllCategories(
                (res: AxiosResponse) => {
                    updateSettings(UPDATE_SETTINGS, {all_categories: res.data});
                },
                () => null,
            );
            return;
        }
        companyProducts.data?.forEach((p: IProducts) => {
            if (p.categoriesSelected) {
                p.categoriesSelected.forEach((categoryId: string) => {
                    const found = settings.all_categories?.find(c => c.id === categoryId);
                    if (found) {
                        allProductCategories.push(found);
                    }
                });
            } else if (p.categories) {
                allProductCategories = [...allProductCategories, ...p.categories];
            }
        });
        const parentCategories: any[] = [];
        allProductCategories.forEach(category => {
            if (category.parent_id === null) return;
            const found = settings.all_categories?.find((c: ICategory) => c.id === category.parent_id);
            if (found && parentCategories.findIndex(parentCategory => parentCategory.id === found?.id) === -1) {
                parentCategories.push(found);
            }
        });
        setProductsMainCategories(parentCategories);
        // eslint-disable-next-line
    }, [companyProducts.data, settings]);

    useEffect(() => {
        if (!settings.cp_categories) {
            categoriesService.getAllCategories(handleSuccessCategories, handleError);
            return;
        }
        // eslint-disable-next-line
    }, [newCategoryWatcher]);

    return (
        <>
            <div>
                {props.useMuiStyles ? (
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        gap={2}
                        sx={{marginTop: 2, marginBottom: 1}}>
                        <Typography variant="body2" fontWeight={500} color={theme.palette.neutral[700]}>
                            Product Segment
                        </Typography>
                    </Box>
                ) : (
                    <TitleContainer
                        as="h3"
                        text="Product Segment"
                        noConnector
                        position="start"
                        className="mb-2 mt-4"
                        style={{fontFamily: "var(--Inter)"}}
                    />
                )}
                <div className={styles.companyCategories}>
                    {productsMainCategories?.length
                        ? productsMainCategories.map((category, index) => (
                              <div key={index} className={styles.category}>
                                  <Link
                                      to={`${routeConfig.Categories.path}/${category.friendly_url}`}
                                      id={`categoryPath_${category.id}`}>
                                      <span>{category.name}</span>
                                  </Link>
                              </div>
                          ))
                        : null}
                    {productsMainCategories.length === 0 && companyCategories?.length
                        ? companyCategories.map((category, index) => (
                              <div key={index} className={styles.category}>
                                  <Link
                                      to={`${routeConfig.Categories.path}/${category.friendly_url}`}
                                      id={`categoryPath_${category.id}`}>
                                      <span>{category.name}</span>
                                  </Link>
                              </div>
                          ))
                        : null}
                    {isEditable && (
                        <ButtonComponent
                            disabled={loading || !companyInfo}
                            className={styles.addCategory}
                            id="header-add-product"
                            onClick={() => {
                                if (getRemainingProductCount() <= 0 && !isSuperAdmin) {
                                    notify(
                                        "You've reached your plan limit for adding products. Upgrade your plan to add more products.",
                                        "Error",
                                    );
                                    return;
                                }
                                setShowProductModal(true);
                            }}>
                            + Add Product
                        </ButtonComponent>
                    )}
                </div>
            </div>
            {showProductModal && companyInfo && (
                <ProductsModal
                    companyId={companyInfo?.id}
                    onClose={handleCloseProductModal}
                    company_friendly_url={friendly_url}
                    defaultValues={productDefaultValue}
                />
            )}
        </>
    );
}
