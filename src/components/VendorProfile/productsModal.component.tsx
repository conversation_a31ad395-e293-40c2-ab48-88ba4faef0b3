import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {Pagination, ProgressBar} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {ICategory} from "../../Interfaces/categories.interface";
import {IImage} from "../../Interfaces/images.interface";
import {IProductFeature, IProductPricing, IProducts} from "../../Interfaces/products.interface";
import {IVideos} from "../../Interfaces/profiles.interface";
import {DeleteMediaProduct, IMutateData, MutateTypes} from "../../Interfaces/queries.interface";
import {
    PRICING_DESCRIPTION_MAX_LENGTH,
    PRODUCT_FEATURE_DESCRIPTION_MAX_LENGTH,
    productDescriptionLimit,
    SUBCATEGORY_TEXT,
} from "../../constants/commonStrings.constant";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import {useCompany} from "../../hooks/fetches/useCompany";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {IObjAny} from "../../models";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {categoriesService} from "../../services/category.service";
import {enumService} from "../../services/enum.service";
import styles from "../../styles/components/productsModal.module.sass";
import RTE from "../../uicomponents/Atoms/RTE/rte.component";
import MuiSelect from "../../uicomponents/Molecules/MuiSelect/MuiSelect.component";
import {getErrorFromArray} from "../../utils/error.util";
import {stripHtml} from "../../utils/formatString.util";
import Loader from "../../utils/loader";
import {isAnythingBetweenTags} from "../../utils/rtevalidation.util";
import {validateURL} from "../../utils/validation.util";
import ButtonComponent from "../FormControls/button.component";
import DropdownComponent from "../FormControls/dropdown.component";
import TextBoxComponent from "../FormControls/textBox.component";
import {MinusIcon, PlusIcon} from "../Icons";
import {ModalComponent} from "../Modal";
import TitleContainer from "../Titles/titleContainer.component";
import {UploadComponent} from "../Upload/upload.component";

interface IProductsModal {
    companyId: string;
    onClose: Function;
    company_friendly_url: string;
    defaultValues?: IProducts;
    setSavedProductId?: Function;
}

export function ProductsModal(props: IProductsModal) {
    const defaultCategories: ICategory[] | undefined = props.defaultValues?.categories;
    const [productId, setProductId] = useState(props.defaultValues?.id);
    const {settings} = useSettings();
    const {updateSettings} = useSettings();
    const [update, setUpdate] = useState<boolean>(false);
    const [activePage, setActivePage] = useState<number>(1);
    const [file, setFile] = useState<File>();
    const [fileSentPercentage, setFileSentPercentage] = useState(0);
    const [thumbnail, setThumbnail] = useState<File | null>(null);
    const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
    const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
    const [pricingTypes, setPricingTypes] = useState<Array<[string, string]>>([]);
    const [productFeatures, setProductFeatures] = useState<IProductFeature[]>(props.defaultValues?.features || []);
    const [productPricing, setProductPricing] = useState<IProductPricing[]>(props.defaultValues?.pricing || []);
    const parentCategories = settings.all_categories?.filter(category => category.parent_id === null) || [];
    const childCategories = settings.all_categories?.filter(category => category.parent_id !== null) || [];
    const [selectedCategories, setSelectedCategories] = useState<Array<ICategory>>([]);
    const [selectedSubCategories, setSelectedSubCategories] = useState<Array<ICategory>>([]);
    const filteredChildCategories = childCategories.filter(item =>
        selectedCategories.some(selectedItem => selectedItem.id === item.parent_id),
    );
    const groups = {};
    filteredChildCategories.forEach(item => {
        const parent_cat = selectedCategories?.find(a => a.id === item?.parent_id);
        const groupId = parent_cat?.name || "root";
        if (!groups[groupId]) {
            groups[groupId] = {group: groupId, items: []};
        }
        groups[groupId].items.push({...item});
    });
    const groupedArray = Object.values(groups);
    const {company, updateCompany, isUpdating, companyProducts} = useCompany(props.company_friendly_url, {
        isCompany: true,
        calls: {company: true, companyProducts: true},
        companyParams: ["id"],
    });
    const companyInfo = company.data;
    const productData = companyProducts?.data?.find(product => product.id === productId);
    const methods2 = useForm({
        defaultValues: {
            name: props.defaultValues?.name || "",
            category: props.defaultValues?.categories?.[0]?.id || "0",
            description: props.defaultValues?.description || "",
            url: props.defaultValues?.url || "",
        },
    });

    const fileArray = productData
        ? [...(productData.images || []), ...(productData.videos || [])]
        : props.defaultValues?.images && props.defaultValues?.videos
        ? [...props.defaultValues?.images, ...props.defaultValues?.videos]
        : props.defaultValues?.images
        ? [...props.defaultValues?.images]
        : props.defaultValues?.videos
        ? [...props.defaultValues?.videos]
        : [];

    const fileToEdit = fileArray[activePage - 1] || null;

    const splitFileType = (file: File) => {
        return file.type.split("/")[0];
    };

    const fileType = file
        ? splitFileType(file) === MediaTypes.Image
            ? MediaTypes.Image
            : MediaTypes.Video
        : fileToEdit
        ? (fileToEdit as IImage).src
            ? MediaTypes.Image
            : (fileToEdit as IVideos).url
            ? MediaTypes.Video
            : null
        : null;

    const maxSizeImage = "200"; //200 MB
    const maxSizeVideo = "500"; //500 MB
    const notify = useNotification();

    const resetValues = () => {
        if (props.defaultValues) {
            return methods2.reset(props.defaultValues);
        }
        methods2.reset({name: "", description: "", url: "", category: "0"});
    };

    const onClickCancel = () => {
        resetValues();
        props.onClose();
    };

    const handleUploadProgress = (progressEvent: any) => {
        const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setFileSentPercentage(percentage);
    };

    const onClickDelete = () => {
        if (!props.companyId) return;
        if (!productData || !fileToEdit.id) {
            return notify("Please save your product information before uploading images or videos", "Error");
        }
        const media: DeleteMediaProduct = {
            company_id: props.companyId,
            product_id: productData.id,
            id: fileToEdit.id,
            imageOrVideo: fileType!,
        };
        const updateObj: IMutateData = {
            productMedia: media,
            mutate_type: MutateTypes.DelMediaProduct,
        };
        return updateCompany.mutate(updateObj);
    };

    const onClickUpload = () => {
        if (!props.companyId) return;
        if (!productData) {
            return notify("Please save your product information before uploading images or videos", "Error");
        }
        if (!file) {
            return notify("Please choose a file to upload", "Error");
        }
        if (fileType === MediaTypes.Image) {
            if (!file) {
                return notify("Please select or upload a thumbnail", "Error");
            }
            clearInputFile();
            return updateCompany.mutate({
                company_id: props.companyId,
                product_id: productData.id,
                image: file,
                handleUploadProgress,
                mutate_type: MutateTypes.AddProductMedia,
                media_type: MediaTypes.Image,
            });
        } else if (fileType === MediaTypes.Video) {
            if (!thumbnail) {
                return notify("Please select or upload a thumbnail", "Error");
            }
            clearInputFile();
            return updateCompany.mutate({
                company_id: props.companyId,
                product_id: productData.id,
                thumbnail,
                video: file,
                handleUploadProgress,
                mutate_type: MutateTypes.AddProductMedia,
                media_type: MediaTypes.Video,
            });
        }
    };

    const handleSuccessAddProduct = (res: AxiosResponse) => {
        setUpdate(true);
        setProductId(res.data.id);
        updateSettings(UPDATE_SETTINGS, {cp_categories: undefined, all_categories: undefined});
    };

    const handleSuccessEditProduct = () => {
        updateSettings(UPDATE_SETTINGS, {cp_categories: undefined, all_categories: undefined});
        onClickCancel();
    };

    const saveProduct = async data => {
        const subCategories = selectedSubCategories?.map(a => a?.id) || [];
        const categories = selectedCategories?.map(a => a?.id) || [];
        const allCategories = [...subCategories, ...categories];

        let product: Partial<IProducts> = {
            id: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
            company_id: companyInfo?.id!,
            name: data.name,
            description: data.description,
            overview: data.overview,
            categoriesSelected: allCategories,
            videos: data.videos || [],
            images: data.images || [],
            features: productFeatures.filter(f => f.title !== ""),
            pricing: productPricing.filter(f => f.title !== ""),
        };
        if (data?.url) {
            product = {...product, url: data.url};
        }
        if (update && productId) {
            const productObj = {...product, id: productId};
            delete productObj.videos;
            delete productObj.images;
            const updateObj: IMutateData = {
                product: productObj,
                mutate_type: MutateTypes.EditProduct,
                onSuccess: handleSuccessEditProduct,
            };
            return updateCompany.mutate(updateObj);
        }
        updateCompany.mutate({
            product: product,
            mutate_type: MutateTypes.AddProduct,
            onSuccess: handleSuccessAddProduct,
        });
    };

    const onClickSave = (data: Record<string, any>) => {
        if (!props.companyId) return;
    
        const keys = Object.keys(data);
        const featureKeys = keys.filter(key => key.includes("feature-description"));
        const pricingKeys = keys.filter(key => key.includes("pricing-description"));
    
        const notifyError = (msg: string) => {
            notify(msg, "Error");
            return true;
        };
    
        const isEmpty = (str: string) => !str || str.trim().length === 0;
    
        if (isEmpty(data.name)) {
            return notify("Please provide product name", "Error");
        }
    
        if (!isAnythingBetweenTags(data.description)) {
            return notify("Please provide product description", "Error");
        }
    
        const descriptionText = stripHtml(data.description);
        if (descriptionText.length > productDescriptionLimit) {
            return notify(
                `Please provide a description with a character length of less than ${productDescriptionLimit} characters.`,
                "Error"
            );
        }
    
        const hasFeatureError = featureKeys.some(key =>
            stripHtml(data[key]).length > productDescriptionLimit &&
            notifyError(`Please provide the feature-description character length less than ${productDescriptionLimit} characters.`)
        );
        if (hasFeatureError) return;
    
        const hasPricingError = pricingKeys.some(key =>
            stripHtml(data[key]).length > 125 &&
            notifyError("Please provide the pricing-description character length less than 125 characters.")
        );
        if (hasPricingError) return;
    
        if (data.url && !data.url.includes("http")) {
            data.url = `https://${data.url}`;
        }
    
        if (selectedCategories.length === 0) {
            return notify("Must select a minimum of 1 category.", "Error");
        }
    
        if (selectedSubCategories.length === 0) {
            return notify("Must select a minimum of 1 subcategory per selected category.", "Error");
        }
    
        return saveProduct(data);
    };

    const clearInputFile = () => {
        setFile(undefined);
        setThumbnail(null);
        setThumbnailUrl(null);
        setImagePreviewUrl(null);
    };

    const handleFeatureChange = (index, property, value) => {
        const newFeatures = [...productFeatures];
        newFeatures[index][property] = value;
        setProductFeatures(newFeatures);
    };

    const handlePricingChange = (index, property, value) => {
        const newPricing = [...productPricing];
        if (value) {
            newPricing[index][property] = value;
            setProductPricing(newPricing);
        }
    };

    const handleSuccessPricingTypes = (response: IObjAny) => {
        setPricingTypes(Object.values(response.data).map(item => [item, item]));
    };

    const getPricingTypeOptions = async () => {
        enumService.getPricingTypes(handleSuccessPricingTypes, () => {
            notify("Error fetching Price Types", "Error");
        });
    };

    const handleCategoryChange = (value: any, type: string) => {
        if (type === "category") {
            setSelectedCategories(typeof value === "string" ? value.split(",") : value);
        } else {
            setSelectedSubCategories(typeof value === "string" ? value.split(",") : value);
        }
    };

    const handleDelete = (value: string, type: string) => {
        if (type === "category") {
            const filteredCategories = selectedCategories.filter(a => a.id !== value);
            setSelectedCategories([...filteredCategories]);
        } else {
            const filteredSubcategories = selectedSubCategories.filter(a => a.id !== value);
            setSelectedSubCategories([...filteredSubcategories]);
        }
    };

    const onSuccessCategories = (response: AxiosResponse<Array<ICategory>>) => {
        if (!response.data || response.data.length === 0) return;
        updateSettings(UPDATE_SETTINGS, {all_categories: response.data});
    };

    useEffect(() => {
        if (props.defaultValues) {
            setUpdate(true);
            setActivePage(1);
        }
        //eslint-disable-next-line
    }, [props.defaultValues]);

    useEffect(() => {
        if (productFeatures) {
            productFeatures.forEach((feature: IProductFeature, index: number) => {
                methods2.setValue(`feature-${index}` as any, feature.title);
                methods2.setValue(`feature-description-${index}` as any, feature.description);
            });
        }
        //eslint-disable-next-line
    }, [productFeatures]);

    useEffect(() => {
        if (productPricing) {
            productPricing.forEach((pricing: IProductPricing, index: number) => {
                methods2.setValue(`pricing-title-${index}` as any, pricing.title);
                methods2.setValue(`pricing-description-${index}` as any, pricing.description);
                if (productId) {
                    methods2.setValue(`pricing-price-${index}` as any, pricing.price);
                    methods2.setValue(`pricing-type-${index}` as any, pricing.pricing_type);
                }
            });
        }
        //eslint-disable-next-line
    }, [productPricing, pricingTypes]);

    useEffect(() => {
        getPricingTypeOptions();
        if (parentCategories.length) return;
        categoriesService.getAllCategories(onSuccessCategories, error => {
            notify(getErrorFromArray(error) || "There was an error retrieving categories", "Error");
        });
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        clearInputFile();
    }, [activePage]);

    useEffect(() => {
        if (defaultCategories && settings) {
            const selectedCategories =
                settings?.all_categories?.filter(item =>
                    defaultCategories.some(selectedItem => selectedItem.parent_id === item.id),
                ) || [];

            const subCategories = defaultCategories.filter(a => a?.parent_id);

            setSelectedCategories(selectedCategories);
            setSelectedSubCategories(subCategories);
        }
    }, [defaultCategories, settings]);

    const validatePrice = (chosenPrice: string, index: number) => {
        return productPricing[index]["pricing_type"] !== "Free Trial" ? parseFloat(chosenPrice) > 0 : true;
    };

    return (
        <>
            <ModalComponent
                isStatic
                showHeader
                headerTitle={update ? "Edit Product" : "Add Product"}
                headerPosition="center"
                modalSwitcher={true}
                modalSwitcherCallback={props.onClose}
                form={
                    <>
                        <FormProvider {...methods2}>
                            <form onSubmit={methods2.handleSubmit(onClickSave)}>
                                <div className={styles.formContainer}>
                                    <div className="row mt-0 mt-md-3">
                                        <TextBoxComponent
                                            containerClassName="col-12 col-md-6"
                                            id="name"
                                            isRequired
                                            label="Name *"
                                            autoFocus
                                            placeholder="Enter the product name"
                                            validationSchema={v => v.trim() !== ""}
                                        />
                                        <TextBoxComponent
                                            containerClassName="col-12 col-md-6"
                                            id="url"
                                            label="URL"
                                            placeholder="Enter the product url"
                                            validationSchema={v => {
                                                if (v !== "") return validateURL(v);
                                                else return true;
                                            }}
                                        />
                                    </div>
                                    <div className="row mt-0 mt-md-2">
                                        {company.data ? (
                                            <RTE
                                                id="description"
                                                label="Description"
                                                isRequired
                                                defaultValue={productData?.description || ""}
                                                maxLength={productDescriptionLimit}
                                                useOldLabel
                                            />
                                        ) : (
                                            <Loader inline loading />
                                        )}
                                    </div>
                                    <div className="row mt-0 mt-md-2 my-2">
                                        <div className={`d-flex flex-column mb-2 mt-3`}>
                                            <label htmlFor="category" className="cpLabel">
                                                Category {"*"}
                                            </label>
                                            <MuiSelect
                                                id="category"
                                                onChange={e => handleCategoryChange(e, "category")}
                                                options={parentCategories}
                                                multiSelect
                                                value={selectedCategories}
                                                placeholder="Select Category(s)"
                                                className="cpInput mh-100"
                                                onClear={() => {
                                                    setSelectedCategories([]);
                                                    setSelectedSubCategories([]);
                                                }}
                                                removableChip
                                                onDelete={value => handleDelete(value, "category")}
                                                inputClassName={styles.selectInput}
                                                useCheckboxes
                                            />
                                        </div>
                                        <div className={`d-flex flex-column mb-3 mt-3`}>
                                            <label htmlFor="category" className="cpLabel">
                                                {SUBCATEGORY_TEXT} {"*"}
                                            </label>
                                            <MuiSelect
                                                id="subcategory"
                                                onChange={e => handleCategoryChange(e, "subcategory")}
                                                options={groupedArray as ICategory[]}
                                                key={selectedCategories?.length}
                                                multiSelect
                                                value={selectedSubCategories}
                                                placeholder="Select Sub Category(s)"
                                                className="cpInput mh-100"
                                                onClear={() => setSelectedSubCategories([])}
                                                disabled={!selectedCategories?.length}
                                                removableChip
                                                onDelete={value => handleDelete(value, "subcategory")}
                                                inputClassName={styles.selectInput}
                                                useCheckboxes
                                            />
                                        </div>
                                    </div>
                                    <div className="row mt-0 mt-md-2 d-flex flex-column align-items-start">
                                        <label className="cpLabel">Product Features</label>
                                        {productFeatures.map((feature: IProductFeature, index: number) => {
                                            return (
                                                <>
                                                    <div
                                                        className="col-12 d-flex flex-row align-items-center mb-1"
                                                        key={index}>
                                                        <div className="d-flex flex-column w-100">
                                                            <TextBoxComponent
                                                                id={`feature-${index}`}
                                                                label=""
                                                                placeholder="Enter feature name"
                                                                containerClassName="m-0 mb-2 w-100"
                                                                onTextChange={text =>
                                                                    handleFeatureChange(index, "title", text)
                                                                }
                                                            />
                                                            <RTE
                                                                id={`feature-description-${index}`}
                                                                label="Description"
                                                                placeholder="Enter Feature Description"
                                                                isRequired
                                                                defaultValue={feature.description}
                                                                maxLength={PRODUCT_FEATURE_DESCRIPTION_MAX_LENGTH}
                                                                useOldLabel
                                                                onChange={html =>
                                                                    handleFeatureChange(index, "description", html)
                                                                }
                                                            />
                                                        </div>
                                                        <ButtonComponent
                                                            type="button"
                                                            id={`remove-feature-${index}`}
                                                            disableTrack
                                                            className={styles.removeFeatureBtn}
                                                            onClick={() =>
                                                                setProductFeatures(
                                                                    productFeatures.filter(
                                                                        f => f.title !== feature.title,
                                                                    ),
                                                                )
                                                            }>
                                                            <MinusIcon size={18} />
                                                        </ButtonComponent>
                                                    </div>
                                                </>
                                            );
                                        })}
                                        <div className="col-12">
                                            <ButtonComponent
                                                id="addFeatureBtn"
                                                disableTrack
                                                type="button"
                                                className="cpBlueBtnThin d-flex flex-row align-items-center w-auto"
                                                onClick={() =>
                                                    setProductFeatures(prevFeatures => [
                                                        ...prevFeatures,
                                                        {
                                                            title: "",
                                                            description: "",
                                                        },
                                                    ])
                                                }
                                                disabled={
                                                    productFeatures.length > 0 &&
                                                    productFeatures[productFeatures.length - 1].title === ""
                                                }>
                                                <PlusIcon size={18} className={styles.plusIcon} />
                                                Add Feature
                                            </ButtonComponent>
                                        </div>
                                    </div>
                                    <div className="row mt-0 mt-md-2 d-flex flex-column align-items-start">
                                        <label className="cpLabel">Pricing</label>
                                        {productPricing.map((pricing: IProductPricing, index: number) => {
                                            return (
                                                <>
                                                    <div
                                                        className="col-12 d-flex flex-row align-items-center mb-1"
                                                        key={index}>
                                                        <div className="d-flex flex-column w-100">
                                                            <TextBoxComponent
                                                                id={`pricing-title-${index}`}
                                                                label=""
                                                                placeholder="Title *"
                                                                containerClassName="m-0 mb-2 w-100"
                                                                maxLength={50}
                                                                isRequired
                                                                validationSchema={v =>
                                                                    /^[^*|\":<>[\]{}`\\()';@&$]+$/.test(v)
                                                                }
                                                                validateOnTheFly
                                                                onTextChange={text =>
                                                                    handlePricingChange(index, "title", text)
                                                                }
                                                            />
                                                            <RTE
                                                                id={`pricing-description-${index}`}
                                                                label="Description"
                                                                defaultValue={pricing.description}
                                                                maxLength={PRICING_DESCRIPTION_MAX_LENGTH}
                                                                useOldLabel
                                                                onChange={html =>
                                                                    handlePricingChange(index, "description", html)
                                                                }
                                                            />
                                                            <DropdownComponent
                                                                id={`pricing-type-${index}`}
                                                                options={pricingTypes || []}
                                                                containerClassName="m-0 mb-2 w-100 mt-2"
                                                                placeholder="Pricing Type *"
                                                                selectedOption={pricing.pricing_type}
                                                                defaultValue={pricing.pricing_type}
                                                                isRequired
                                                                validationSchema={v => v !== "0"}
                                                                validateOnTheFly
                                                                onTextChange={text =>
                                                                    handlePricingChange(index, "pricing_type", text)
                                                                }
                                                            />
                                                            <TextBoxComponent
                                                                id={`pricing-price-${index}`}
                                                                label=""
                                                                placeholder="Price ($) *"
                                                                containerClassName="m-0 mb-2 w-100 mt-2"
                                                                type="number"
                                                                validationSchema={v => validatePrice(v, index)}
                                                                isRequired
                                                                validateOnTheFly
                                                                allowDecimals={true}
                                                                onTextChange={text =>
                                                                    handlePricingChange(index, "price", text)
                                                                }
                                                            />
                                                        </div>
                                                        <ButtonComponent
                                                            disableTrack
                                                            type="button"
                                                            id={`remove-pricing-${index}`}
                                                            className={styles.removeFeatureBtn}
                                                            onClick={() => {
                                                                setProductPricing(
                                                                    productPricing.filter(
                                                                        f => f.title !== pricing.title,
                                                                    ),
                                                                );
                                                            }}>
                                                            <MinusIcon size={18} />
                                                        </ButtonComponent>
                                                    </div>
                                                </>
                                            );
                                        })}
                                        <div className="col-12">
                                            <ButtonComponent
                                                id="addFeatureBtn"
                                                disableTrack
                                                type="button"
                                                className="cpBlueBtnThin d-flex flex-row align-items-center w-auto"
                                                onClick={() => {
                                                    setProductPricing(prevPricing => [
                                                        ...prevPricing,
                                                        {title: "", description: "", price: 0, pricing_type: "0"},
                                                    ]);
                                                }}
                                                disabled={
                                                    productPricing.length > 0 &&
                                                    (productPricing[productPricing.length - 1].title === "" ||
                                                        productPricing[productPricing.length - 1].price === 0 ||
                                                        productPricing[productPricing.length - 1].pricing_type === "0")
                                                }>
                                                <PlusIcon size={18} className={styles.plusIcon} />
                                                Add Pricing
                                            </ButtonComponent>
                                        </div>
                                    </div>
                                    {!isUpdating?.[MutateTypes.AddProductMedia] ? (
                                        productData ? (
                                            <>
                                                <div className="row mt-2 mt-md-3">
                                                    <UploadComponent
                                                        id="uploadFile"
                                                        fileId={fileToEdit?.id}
                                                        file={file}
                                                        setFile={setFile}
                                                        thumbnail={thumbnail}
                                                        setThumbnail={setThumbnail}
                                                        thumbnailUrl={thumbnailUrl}
                                                        setThumbnailUrl={setThumbnailUrl}
                                                        imagePreviewUrl={imagePreviewUrl}
                                                        setImagePreviewUrl={setImagePreviewUrl}
                                                        clearInputFile={clearInputFile}
                                                        maxSize={maxSizeImage}
                                                        maxSizeAlt={maxSizeVideo}
                                                        type={fileType || MediaTypes.ImageOrVideo}
                                                        fileToEdit={fileToEdit}
                                                    />
                                                </div>
                                                {(file || fileToEdit) && (
                                                    <div className="d-flex justify-content-center mt-0 mt-md-4 flex-wrap">
                                                        {fileToEdit && (
                                                            <ButtonComponent
                                                                id="deleteMedia"
                                                                disableTrack
                                                                onClick={() => onClickDelete()}
                                                                className="cpBlueBtn order-2 order-md-1"
                                                                disabled={!fileToEdit}
                                                                type="button">
                                                                {isUpdating?.[MutateTypes.DelMediaProduct] ? (
                                                                    <Loader inline version="iconWhite" loading />
                                                                ) : (
                                                                    "Delete " +
                                                                    (fileType === MediaTypes.Image ? "Image" : "Video")
                                                                )}
                                                            </ButtonComponent>
                                                        )}
                                                        {!fileToEdit && (
                                                            <ButtonComponent
                                                                disableTrack
                                                                id="uploadMedia"
                                                                onClick={() => onClickUpload()}
                                                                className="cpBlueBtn order-1 order-md-2"
                                                                type="button">
                                                                {isUpdating?.[MutateTypes.AddProductMedia] ? (
                                                                    <Loader inline version="iconWhite" loading />
                                                                ) : (
                                                                    "Confirm " + fileType
                                                                )}
                                                            </ButtonComponent>
                                                        )}
                                                    </div>
                                                )}
                                                <div className="d-flex justify-content-center mt-0 mt-md-4 flex-wrap">
                                                    <Pagination>
                                                        <Pagination.Prev
                                                            onClick={() => setActivePage(activePage - 1)}
                                                        />
                                                        <Pagination.Item
                                                            data-track={false}
                                                            key={1}
                                                            active={1 === activePage || fileArray?.length === 0}
                                                            onClick={() => setActivePage(1)}>
                                                            Upload {1}
                                                        </Pagination.Item>
                                                        <Pagination.Item
                                                            data-track={false}
                                                            key={2}
                                                            active={2 === activePage}
                                                            disabled={fileArray?.length < 1}
                                                            onClick={() => setActivePage(2)}>
                                                            Upload {2}
                                                        </Pagination.Item>
                                                        <Pagination.Item
                                                            data-track={false}
                                                            key={3}
                                                            active={3 === activePage}
                                                            disabled={fileArray?.length < 2}
                                                            onClick={() => setActivePage(3)}>
                                                            Upload {3}
                                                        </Pagination.Item>
                                                        <Pagination.Item
                                                            data-track={false}
                                                            key={4}
                                                            active={4 === activePage}
                                                            disabled={fileArray?.length < 3}
                                                            onClick={() => setActivePage(4)}>
                                                            Upload {4}
                                                        </Pagination.Item>
                                                        <Pagination.Next
                                                            onClick={() => setActivePage(activePage + 1)}
                                                        />
                                                    </Pagination>
                                                </div>
                                            </>
                                        ) : (
                                            <h3 className="d-flex justify-content-center mt-0 mt-md-4">
                                                Save Product to upload Images or Videos
                                            </h3>
                                        )
                                    ) : (
                                        <>
                                            <div className={styles.formContainer}>
                                                <div className={styles.loading}>
                                                    <TitleContainer
                                                        as="h2"
                                                        text={`Uploading... Don't close this window.`}
                                                        noConnector
                                                    />
                                                    <ProgressBar
                                                        now={fileSentPercentage}
                                                        animated
                                                        label={`${fileSentPercentage}%`}
                                                    />
                                                    <span>
                                                        {fileSentPercentage > 99 &&
                                                            "Finishing process, don't close this yet."}
                                                    </span>
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    <div className="d-flex justify-content-center mt-0 mt-md-4 flex-wrap gap-3">
                                        <ButtonComponent
                                            onClick={() => onClickCancel()}
                                            className="cpBlueAltBtn order-2 order-md-1"
                                            buttonText="Cancel"
                                            id="cancelProduct"
                                            type="button"
                                        />
                                        <ButtonComponent
                                            id="saveProduct"
                                            onClick={() => {
                                                setProductFeatures(
                                                    productFeatures.map(obj => {
                                                        if (obj.description === "") {
                                                            return {...obj, description: undefined};
                                                        }
                                                        if (
                                                            obj.description &&
                                                            /^(<(\w+)\s*>(\s*<br\s*\/?>)?\s*<\/\2>|<br\s*\/?>)$/i.test(
                                                                obj.description,
                                                            )
                                                        ) {
                                                            return {...obj, description: undefined};
                                                        } else {
                                                            const newDescription =
                                                                obj.description &&
                                                                obj.description
                                                                    .replace(/<(\w+)\s*>(\s*)/, "<$1>")
                                                                    .replace(/(\s*)<\/(\w+)\s*>/, "</$2>")
                                                                    .replace(/<(\w+)\s*><br\s*\/?>\s*<\/\1>/gi, "");
                                                            return {...obj, description: newDescription};
                                                        }
                                                    }),
                                                );
                                                setProductPricing(
                                                    productPricing.map(obj => {
                                                        if (obj.description === "") {
                                                            return {...obj, description: undefined};
                                                        }
                                                        if (
                                                            obj.description &&
                                                            /^(<(\w+)\s*>(\s*<br\s*\/?>)?\s*<\/\2>|<br\s*\/?>)$/i.test(
                                                                obj.description,
                                                            )
                                                        ) {
                                                            return {...obj, description: undefined};
                                                        } else {
                                                            const newDescription =
                                                                obj.description &&
                                                                obj.description
                                                                    .replace(/<(\w+)\s*>(\s*)/, "<$1>")
                                                                    .replace(/(\s*)<\/(\w+)\s*>/, "</$2>")
                                                                    .replace(/<(\w+)\s*><br\s*\/?>\s*<\/\1>/gi, "");
                                                            return {...obj, description: newDescription};
                                                        }
                                                    }),
                                                );
                                            }}
                                            className="cpBlueBtn order-1 order-md-2"
                                            type="submit"
                                            disabled={
                                                (productPricing.length > 0 &&
                                                    (productPricing[productPricing.length - 1].title === "" ||
                                                        productPricing[productPricing.length - 1].price === 0 ||
                                                        productPricing[productPricing.length - 1].pricing_type ===
                                                            "0")) ||
                                                (productFeatures.length > 0 &&
                                                    productFeatures[productFeatures.length - 1].title === "")
                                            }>
                                            {isUpdating?.[MutateTypes.AddProduct] ||
                                            isUpdating?.[MutateTypes.EditProduct] ? (
                                                <Loader inline version="iconWhite" loading />
                                            ) : (
                                                "Save"
                                            )}
                                        </ButtonComponent>
                                    </div>
                                </div>
                            </form>
                        </FormProvider>
                    </>
                }
            />
        </>
    );
}
