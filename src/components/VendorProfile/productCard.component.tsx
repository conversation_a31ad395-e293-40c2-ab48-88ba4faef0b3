import {Grid} from "@mui/material";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import {useQueryClient} from "@tanstack/react-query";
import {ReactNode, useState} from "react";
import {Tab, Tabs} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation} from "react-router-dom";
import {IProducts} from "../../Interfaces/products.interface";
import {IMutateData, MutateTypes} from "../../Interfaces/queries.interface";
import {
    CHANNEL_PROGRAM_COMPANY_ID,
    INVITE_TO_REVIEW_TEXT,
    WRITE_A_REVIEW_BUTTON_TEXT,
} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {useCompany} from "../../hooks/fetches/useCompany";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import usePermissions from "../../hooks/usePermissions";
import {adminService} from "../../services/admin.service";
import {reviewService} from "../../services/review.service";
import {ShortenerModelType} from "../../services/shortener.service";
import styles from "../../styles/components/productCard.module.sass";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import ProductSearch from "../../uicomponents/Molecules/ProductSearch/productSearch.component";
import ReferFriendDialog from "../../uicomponents/Organism/ReferFriendDialog/referFriendDialog.component";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {addLineBreaks} from "../../utils/formatString.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import Loader from "../../utils/loader";
import {LinkAndSanitizeRichText, RichTextSanitizer} from "../../utils/miscellaneous";
import FollowBtn from "../Buttons/Follow/followBtn.component";
import CardActionBtns from "../Buttons/cardActionBtns.component";
import ProductMediaCarousel from "../Carousel/productMediaCarousel.component";
import ControlledStarRating from "../FormControls/ControlledStarRating.component";
import ButtonComponent from "../FormControls/button.component";
import ReactIcon from "../Icons/reactIcon.component";
import OutboundLink from "../Links/OutboundLink.component";
import ProductReviews from "../Product/productReviews.component";
import ShareModal from "../SocialShare/shareModal.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import AddFeatureModal from "./addFeatureModal.component";
import {ProductsModal} from "./productsModal.component";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";

interface IProps {
    product: IProducts;
    isEditable: boolean;
    company_friendly_url: string;
    categoriesOptions: Array<[string, string]>;
    showReviewQuote?: boolean;
    className?: string;
    maxReviews?: number;
    disableInfiniteLoad?: boolean;
}

export default function ProductCard(props: IProps) {
    const [showEditProduct, setShowEditProduct] = useState(false);
    const [showMoveReviews, setShowMoveReviews] = useState(false);
    const [shareModal, setShareModal] = useState(false);
    const [addFeatureId, setAddFeatureId] = useState<string>("");
    const [productIdToMoveReviewFrom, setProductIdToMoveReviewFrom] = useState<string>("");
    const [referOpen, setReferOpen] = useState<boolean>(false);
    const location: any = useLocation();
    const {authState} = useAuthState();
    const {activeCompany, isVendor} = useActiveCompany();
    const {product} = props;
    const {company, updateCompany, isUpdating} = useCompany(props.company_friendly_url, {
        isCompany: true,
        calls: {all: false, company: true},
        companyParams: ["current_user_is_following", "profile_images"],
    });
    const queryClient = useQueryClient();
    const methods = useForm();
    const isDeleting = isUpdating?.[MutateTypes.DelProduct];
    const isUserFollowing = company.data?.current_user_is_following;
    const ratingText: ReactNode[] = [
        !!product?.total_reviews ? (
            <div className="d-flex align-items-center">
                <ControlledStarRating
                    id="starRating"
                    maxRating={5}
                    value={(Math.round(100 * (product?.rating as number)) / 100) as number}
                    iconSize={26}
                    showLabels={false}
                    containerClassName="m-0 me-1"
                    readOnly
                />
                <strong className="blackColor">{`${product?.total_reviews} ${
                    product?.total_reviews > 1 ? "reviews" : "review"
                }`}</strong>
            </div>
        ) : (
            <>No reviews yet, be the first to write a review!</>
        ),
    ];
    const productUrl = product?.url ? (product?.url?.includes("http") ? product?.url : `https://${product?.url}`) : "";
    const isProductPage = location.pathname.includes("/product/");
    const isChannelProgram = company.data?.id === CHANNEL_PROGRAM_COMPANY_ID;
    const canReviewAndFollow = activeCompany?.id !== company.data?.id && !isVendor;
    const {isSuperAdmin, hasPermissions} = usePermissions();
    const canMoveReviews = isSuperAdmin || hasPermissions([PERMISSION_GROUPS.ADMIN_MOVE_PRODUCT_REVIEWS_UPDATE]);
    const handleRemoveProduct = async (productId: string) => {
        if (product.total_reviews && product.total_reviews !== 0) {
            const answer = await getUserConfirmation(
                "Uh oh! This product cannot be deleted, please contact customer service or your sales representative for assistance.",
                {
                    showCancelButton: false,
                    okButtonText: "Ok",
                },
            );
            if (!answer.value) {
                return;
            }
        } else {
            const answer = await getUserConfirmation("Are you sure you want to delete this product?", {
                form: (
                    <div className="text-center">
                        <SubtitleContainer
                            as="p"
                            size="18"
                            text="This will also remove all product reviews associated with this product."
                        />
                        <SubtitleContainer as="p" size="18" text="This is immediate and cannot be undone." />
                    </div>
                ),
                confirmationInput: "DELETE",
                okButtonText: "Delete Product",
            });
            if (answer.value) {
                const removeObj: IMutateData = {
                    mutate_type: MutateTypes.DelProduct,
                    product_id: productId,
                    company_id: company.data?.id,
                };
                updateCompany.mutate(removeObj);
            }
        }
    };

    const tabTrackingProperties = JSON.stringify({
        product_id: props.product?.id,
        product_name: props.product?.name,
        company_friendly_url: props.company_friendly_url,
    });

    const showAllProducts = (productId: string) => {
        setProductIdToMoveReviewFrom(productId);
        setShowMoveReviews(true);
    };

    const handleReviewProductChange = async (product: any) => {
        if (!product) return;
        setShowMoveReviews(false);
        const confirmation = await getUserConfirmation(
            `Are you sure you want to move all reviews to ${product.label}?`,
        );
        if (confirmation.value) {
            // call api to move reviews
            reviewService.moveReviewsToProduct(productIdToMoveReviewFrom, product.id).then(() => {
                setShowMoveReviews(false);
                setProductIdToMoveReviewFrom("");

                adminService
                    .storeActivityLog({
                        action: "movedReviews",
                        additional_data: {
                            status: `Moved from product ${productIdToMoveReviewFrom} to product ${product.id}`,
                            approved_by: authState?.id,
                            approved_by_name: `${authState?.firstName} ${authState?.lastName}`,
                        },
                    })
                    .then(() => {
                        window.location.reload();
                    });
            });
        } else {
            setShowMoveReviews(true);
        }
    };

    return (
        <div className={`${!isProductPage && styles.cardContainer} ${props.className}`}>
            <div className={styles.container}>
                <div className="col-auto d-block mx-auto">
                    {product ? (
                        <Link
                            to={routeConfig.VendorProfile.path.replace(
                                ":id",
                                props.company_friendly_url ? props.company_friendly_url : company.data?.friendly_url!,
                            )}>
                            <div className={styles.avatarContainer}>
                                <img
                                    src={
                                        company.data?.profile_images?.CompanyAvatar?.[0]?.src ||
                                        product?.company?.avatar ||
                                        createImageFromInitials(
                                            200,
                                            (product?.company?.name || company.data?.name) ?? "Company",
                                            "",
                                            "company",
                                        )
                                    }
                                    alt=""
                                />
                            </div>
                        </Link>
                    ) : (
                        <div className={styles.avatarContainer}>
                            <div className="d-flex justify-content-center align-items-center h-100 w-100">
                                <Loader inline loading />
                            </div>
                        </div>
                    )}{" "}
                    {canReviewAndFollow && (
                        <Link
                            to={routeConfig.CreateReview.path.replace(":friendly_url", product?.friendly_url || "")}
                            className={styles.quickActions}>
                            <div className={styles.actionIconContainer}>
                                <ReactIcon iconName="AiOutlineStar" />
                            </div>
                            <SubtitleContainer as="span" text={WRITE_A_REVIEW_BUTTON_TEXT} elClassName="mb-0" />
                        </Link>
                    )}
                    {/* {product && (
                        <BookmarkBtn
                            product={product}
                            className={styles.quickActions}
                            iconContainerClass={styles.actionIconContainer}
                        />
                    )} */}
                    {company.data?.id && authState.authenticated && !isChannelProgram && canReviewAndFollow && (
                        <div className={styles.quickActions}>
                            <div className={styles.actionIconContainer}>
                                <ReactIcon
                                    iconName={isUserFollowing ? "AiOutlineUserDelete" : "AiOutlineUserAdd"}
                                    size="20px"
                                />
                            </div>
                            <FollowBtn
                                entityId={company.data?.id || ""}
                                friendlyUrl={company.data?.friendly_url || ""}
                                profile_type="company"
                                variant="textBtn"
                                followText="Follow Vendor"
                                unfollowText="Unfollow Vendor"
                            />
                        </div>
                    )}
                    {company.data?.id && (
                        <>
                            <Link
                                to="#"
                                className={styles.quickActions}
                                onClick={e => {
                                    e.preventDefault();
                                    setReferOpen(true);
                                }}>
                                <div className={styles.actionIconContainer}>
                                    <ReactIcon
                                        className={styles.noFill}
                                        style={{height: 30, width: 30}}
                                        iconName="HiOutlineMail"
                                        size="30px"
                                    />
                                </div>
                                <SubtitleContainer as="span" text={INVITE_TO_REVIEW_TEXT} elClassName="mb-0" />
                            </Link>
                            <ReferFriendDialog
                                type="review"
                                shareUrl={routeConfig.CreateReview.path.replace(":friendly_url", product.friendly_url)}
                                open={referOpen}
                                onClose={() => setReferOpen(false)}
                                product_id={product?.id}
                                multiline
                                nolabelInput
                            />
                        </>
                    )}
                    {canMoveReviews && product?.total_reviews && product?.total_reviews !== 0 && (
                        <div className={styles.quickActions} onClick={() => showAllProducts(product?.id)}>
                            <Link
                                to="#"
                                className={styles.quickActions}
                                onClick={e => {
                                    e.preventDefault();
                                }}>
                                <div className={styles.actionIconContainer}>
                                    <ReactIcon iconName="AiOutlineArrowRight" size="20px" />
                                </div>
                                <SubtitleContainer as="span" text="Move Reviews" elClassName="mb-0" />
                            </Link>
                        </div>
                    )}
                    {isProductPage && (
                        <div className="d-flex justify-content-center mt-3">
                            <ButtonComponent
                                id="shareBtn"
                                className="cpBlueBtnThin"
                                type="button"
                                onClick={() => setShareModal(true)}>
                                Share
                            </ButtonComponent>
                        </div>
                    )}
                </div>
                <div className="col-12 col-lg-auto">
                    <div className="d-flex">
                        <div className={product?.images?.length ? "col-8 pe-4" : "col-12 pe-4"}>
                            <TitleContainer
                                as="h1"
                                text={product?.name || ""}
                                noConnector
                                position="start"
                                className="mb-0"
                            />
                            <SubtitleContainer as="span" position="start" elClassName="mb-2">
                                {ratingText}
                            </SubtitleContainer>
                            <div className="d-flex gap-2 mb-3 flex-wrap">
                                {product?.categories?.map(cat => (
                                    <Link
                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                            ":friendly_url",
                                            cat.friendly_url!,
                                        )}
                                        key={cat.id}
                                        className="linkStyle">
                                        {cat.name}
                                    </Link>
                                ))}
                            </div>
                            <div
                                dangerouslySetInnerHTML={LinkAndSanitizeRichText(product?.description, {
                                    referredLinks: true,
                                })}
                                className="rteContent"
                            />
                            {productUrl && (
                                <OutboundLink href={productUrl} className={styles.productUrl} referred>
                                    <div>
                                        <ReactIcon iconName="GoLinkExternal" size="22px" />
                                    </div>
                                    <span>{productUrl}</span>
                                </OutboundLink>
                            )}
                        </div>
                        {product?.images && product?.images?.length > 0 && (
                            <div className="col-4">{product && <ProductMediaCarousel product={product} />}</div>
                        )}
                    </div>
                    <hr className="fadedBorder" />
                    <div className={styles.container_tab}>
                        <Tabs defaultActiveKey="reviews" mountOnEnter fill>
                            <Tab
                                eventKey="reviews"
                                title="Reviews"
                                tabAttrs={{
                                    "data-custom-properties": tabTrackingProperties,
                                }}>
                                <ProductReviews
                                    product={product}
                                    friendly_url={props.product?.friendly_url}
                                    company={company.data}
                                    showReviewQuote={props.showReviewQuote}
                                    maxReviews={props.maxReviews}
                                    disableInfiniteLoad={props.disableInfiniteLoad}
                                />
                                {!isProductPage && (
                                    <Link
                                        to={routeConfig.ProductDetails.path.replace(
                                            ":friendly_url",
                                            props.product?.friendly_url,
                                        )}>
                                        <SubtitleContainer
                                            as="p"
                                            size={"20"}
                                            text="More product reviews and details"
                                            className="linkStyle font-weight-bold"
                                            elClassName="my-2"
                                            position="start"
                                        />
                                    </Link>
                                )}
                            </Tab>
                            <Tab
                                eventKey="features"
                                title="Features"
                                tabAttrs={{
                                    "data-custom-properties": tabTrackingProperties,
                                }}>
                                {!!product?.features?.length ? (
                                    <>
                                        <TitleContainer
                                            as="h2"
                                            text="Summary of Features"
                                            noConnector
                                            position="start"
                                            className="mb-2 mt-4"
                                        />
                                        <Grid container spacing={{xs: 2, md: 3}} columns={{xs: 4, sm: 8, md: 12}}>
                                            {product?.features?.map((feat, index) => (
                                                <Grid item xs={2} sm={4} md={4} key={index}>
                                                    <Card sx={{minWidth: 275, height: "100%"}}>
                                                        <CardContent>
                                                            <Typography
                                                                variant="subtitle1"
                                                                fontSize="16px !important"
                                                                sx={{marginBottom: "10px !important"}}
                                                                uppercase>
                                                                {feat.title}
                                                            </Typography>
                                                            <Typography
                                                                variant="caption"
                                                                fontSize="14px !important"
                                                                dangerouslySetInnerHTML={{
                                                                    __html: addLineBreaks(feat.description),
                                                                }}
                                                            />
                                                            {/* {stripHtml(feat.description)} */}
                                                        </CardContent>
                                                    </Card>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </>
                                ) : (
                                    <div className="text-center mt-4">
                                        No features added for this product yet, check back soon!
                                    </div>
                                )}
                            </Tab>
                            <Tab
                                eventKey="pricing"
                                title="Pricing"
                                tabAttrs={{
                                    "data-custom-properties": tabTrackingProperties,
                                }}>
                                {!!product?.pricing?.length ? (
                                    <div className="container mt-4 text-center">
                                        <div className="row justify-content-start">
                                            {product.pricing.map(pricing => {
                                                return (
                                                    <div
                                                        key={pricing.title}
                                                        className="col-12 col-sm-8 col-md-6 col-lg-4 mb-3">
                                                        <div className={styles.single_price_plan} data-wow-delay="0.2s">
                                                            <div className={`${styles.card_header} py-3`}>
                                                                <h3 className="my-0 fw-normal">
                                                                    {pricing.pricing_type}
                                                                </h3>
                                                            </div>
                                                            <div>
                                                                <h3 className={`${styles.pricing_title} px-3 pt-2`}>
                                                                    {pricing.title}
                                                                </h3>
                                                                <div className={styles.card_body}>
                                                                    <div
                                                                        className="mt-1"
                                                                        dangerouslySetInnerHTML={RichTextSanitizer(
                                                                            pricing.description,
                                                                        )}
                                                                    />
                                                                </div>
                                                                <div className="mb-2">
                                                                    <h4>{`$${pricing.price}`}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center mt-4">
                                        No pricing added for this product yet, check back soon! {""}
                                    </div>
                                )}
                            </Tab>
                        </Tabs>
                    </div>
                </div>
            </div>
            {props.isEditable && (
                <CardActionBtns
                    info={props.product}
                    deleting={isDeleting}
                    onRemove={handleRemoveProduct}
                    onEdit={() => setShowEditProduct(true)}
                    onAddFeature={(id: string) => setAddFeatureId(id)}
                    showAddFeature
                />
            )}
            {showEditProduct && (
                <ProductsModal
                    companyId={company.data?.id || ""}
                    onClose={() => {
                        queryClient.refetchQueries(["company-products", props.company_friendly_url]);
                        setShowEditProduct(false);
                    }}
                    company_friendly_url={props.company_friendly_url}
                    defaultValues={props.product}
                />
            )}
            {addFeatureId && (
                <AddFeatureModal
                    companyId={addFeatureId}
                    product={props.product}
                    onClose={() => setAddFeatureId("")}
                    friendly_url={props.company_friendly_url}
                />
            )}
            <ShareModal
                isOpen={shareModal}
                onClose={() => setShareModal(false)}
                entityId={product?.id || ""}
                modelForShortener={ShortenerModelType.Product}
            />

            {showMoveReviews && (
                <ModalComponent
                    open={showMoveReviews}
                    onClose={() => setShowMoveReviews(false)}
                    showCancelButton={false}
                    showOkButton={false}
                    title={"Move Reviews"}
                    content={
                        <FormProvider {...methods}>
                            {/* eslint-disable-next-line react/jsx-no-undef */}
                            <div>Choose the product to move these reviews to by searching below.</div>
                            <ProductSearch
                                id="productReviewMove"
                                className="w-100"
                                onValueChange={(_: any, __: any, value) => handleReviewProductChange(value)}
                                validateOnTheFly
                                placeholder="Search Vendor Products"
                                variant="filled-rounded"
                            />
                        </FormProvider>
                    }
                    modalTheme={"success"}
                    justifyButtons={"center"}
                />
            )}
        </div>
    );
}
