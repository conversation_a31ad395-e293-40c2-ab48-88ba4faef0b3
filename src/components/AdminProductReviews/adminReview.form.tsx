import {FormProvider, useForm} from "react-hook-form";
import ReviewQuestionBuilder from "../../uicomponents/Organism/ReviewQuestionBuilder/reviewQuestionBuilder.component";
import {useEffect, useRef, useState} from "react";
import {IAdminProductReview} from "../../Interfaces/adminReview.interface";
import {AxiosError, AxiosResponse} from "axios";
import useQuery from "../../utils/query.util";
import {productService} from "../../services/products.service";
import {reviewService} from "../../services/review.service";
import {IProducts} from "../../Interfaces/products.interface";
import useNotification from "../../hooks/useNotification";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import {getErrorFromArray} from "../../utils/error.util";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import {IFormAnswer, ReviewQuestion} from "../../Interfaces/reviews.interface";
import {QUESTION_KEYS, QUESTION_TYPES} from "../../constants/reviews.constant";
import Loader from "../../utils/loader";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import useAppConfig from "../../hooks/useAppConfig";
import StarRating from "../FormControls/starRating.component";
import {REVIEW_TITLE_MIN_CHARACTERS, REVIEW_TITLE_REQUIRED} from "../../constants/commonStrings.constant";

interface IProps {
    answers: any[];
    friendly_url: string;
    review: IAdminProductReview;
    setIsValidState?: any;
    questions?: any;
}

const AdminReviewForm = (props: IProps) => {
    const {friendly_url, answers, review, questions} = props;
    const [productInfo, setProductInfo] = useState<IProducts>();
    const [isFetchingComplete, setIsFetchingComplete] = useState<boolean>(false);
    const [isUpdating, setIsUpdating] = useState<boolean>(false);
    const [isDirty, setIsDirty] = useState<boolean>(false);
    const methods = useForm();
    const query = useQuery();
    const navigate = useNavigate();
    const notify = useNotification();
    const {config} = useAppConfig({config_key: "PREVENT_EDIT_REVIEWS_BEFORE_DATE"});
    const defaultValues = useRef<any>({});
    const recommended_id = "recommended_by_reviewer_value";
    const job_title_id = "job_title_id";
    const additional_text_append = "_additional_text";
    const preventReviewsBefore = config?.value || "";
    const canEdit = new Date(preventReviewsBefore) < new Date(review?.created_at || "");

    const setDefaultAnswerValues = (questionsToCheck: any, answersToCheck: any) => {
        const newDefault: any = {};
        const recommended_value = Number(review?.recommended_by_reviewer_value);
        methods.setValue(recommended_id, recommended_value);
        newDefault[recommended_id] = recommended_value;
        const job_title_value = (review?.reviewer as any)?.job_title?.id;
        methods.setValue(job_title_id, job_title_value);
        newDefault[job_title_id] = job_title_value;
        newDefault["title"] = review?.title;
        questionsToCheck.forEach((question: ReviewQuestion) => {
            if (
                question.question_type === QUESTION_TYPES.STAR_RATING ||
                question.question_type === QUESTION_TYPES.SINGLE_ANSWER ||
                question.question_type === QUESTION_TYPES.RADIO_BUTTONS
            ) {
                const foundAnswer = answersToCheck?.find(a => a.review_question_id === question.id);
                const value = foundAnswer?.review_question_option_id;
                const additionalTextBoxValue = foundAnswer?.answer;
                if (additionalTextBoxValue) {
                    methods.setValue(question.id + additional_text_append, additionalTextBoxValue);
                    newDefault[question.id + additional_text_append] = additionalTextBoxValue;
                }
                methods.setValue(question.id, value);
                newDefault[question.id] = value;
            } else if (question.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                const foundAnswers = answersToCheck?.filter(a => a.review_question_id === question.id) || [];
                const newValue = {};
                const newAdditionalTextValues = {};
                question.options.forEach(o => {
                    const foundInPrevious = foundAnswers.find(a => a.review_question_option_id === o.id);
                    const value = foundInPrevious ? true : false;
                    newValue[o.id] = value;
                    if (foundInPrevious?.answer) {
                        newAdditionalTextValues[o.id + additional_text_append] = foundInPrevious.answer;
                        methods.setValue(o.id + additional_text_append, foundInPrevious.answer);
                        newDefault[o.id + additional_text_append] = foundInPrevious.answer;
                    }
                });
                methods.setValue(question.id, newValue);
                newDefault[question.id] = newValue;
            } else {
                const foundAnswer = answersToCheck?.find(a => a.review_question_id === question.id);
                const value = foundAnswer?.answer || foundAnswer?.review_question_option_id;
                methods.setValue(question.id, value);
                newDefault[question.id] = value;
            }
        });
        defaultValues.current = newDefault;
    };

    const generatePayloadAnswers = () => {
        const values = methods.getValues();
        const answers: IFormAnswer[] = [];
        questions.forEach((q: ReviewQuestion) => {
            const review_question_id = q.id;
            if (
                q.question_type === QUESTION_TYPES.SINGLE_ANSWER ||
                q.question_type === QUESTION_TYPES.STAR_RATING ||
                q.question_type === QUESTION_TYPES.RADIO_BUTTONS
            ) {
                if (q.question_key !== QUESTION_KEYS.JOB_TITLE) {
                    const isRecommended = q.question_key === QUESTION_KEYS.RECOMMENDATION_RATING;
                    const recommended_id_value = isRecommended
                        ? q.options.find(o => o.order === values[recommended_id])?.id
                        : undefined;
                    answers.push({
                        review_question_id,
                        review_question_option_ids: [isRecommended ? recommended_id_value : values[review_question_id]],
                        answer: values[`${review_question_id}${additional_text_append}`] || undefined,
                    });
                }
            } else if (q.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                const foundAnswers: any[] = [];
                const idsToSend = Object.keys(values[review_question_id] || {})?.filter(key => {
                    const valueAtKey = values[review_question_id][key];
                    const answerAtKey = values[`${key}${additional_text_append}`];
                    if (valueAtKey) {
                        if (answerAtKey) {
                            foundAnswers.push(answerAtKey);
                        } else {
                            foundAnswers.push(null);
                        }
                    }
                    return valueAtKey;
                });
                answers.push({
                    review_question_id,
                    review_question_option_ids: idsToSend,
                    answers: foundAnswers || undefined,
                });
            } else if (q.question_type === QUESTION_TYPES.SCALE) {
                answers.push({
                    review_question_id,
                    review_question_option_ids: q.options?.filter(o => values[o.id])?.map(o => o.id) || [],
                });
            } else if (
                q.question_type === QUESTION_TYPES.OPEN_TEXT ||
                q.question_type === QUESTION_TYPES.SINGLE_LINE_TEXT
            ) {
                answers.push({
                    review_question_id,
                    answer: values[review_question_id],
                });
            }
        });
        return answers.filter(a => {
            return (a.review_question_option_ids?.filter(v => Boolean(v))?.length || 0) > 0 || a.answer;
        });
    };

    const handleError = (err: AxiosError, productError?: boolean) => {
        if (productError) {
            notify("That product could not be found", "Error");
            navigate(routeConfig.ProductReviewSearch.path);
        } else {
            notify(getErrorFromArray(err), "Error");
        }
    };

    const handleSuccessGetProduct = (res: AxiosResponse) => {
        setProductInfo(res.data);
    };

    const handleSubmit = () => {
        setIsUpdating(true);
        const values = methods.getValues();
        Promise.all([
            reviewService.updateReview({
                id: review.id,
                title: values.title,
                recommended_by_reviewer_value: Number(values[recommended_id]),
            }),
            reviewService.syncAnswers(
                review?.id,
                values[job_title_id],
                generatePayloadAnswers(),
                values?.[recommended_id] || Number(review?.recommended_by_reviewer_value),
                false,
            ),
        ])
            .then(() => {
                notify("Successfully Updated Review", "Success");
                setIsUpdating(false);
                defaultValues.current = values;
                checkIsDirty();
            })
            .catch(errs => {
                handleError(errs[0]);
                handleError(errs[1]);
                setIsUpdating(false);
            });
    };

    const checkIsDirty = () => {
        const values = methods.getValues();
        let dirty = false;
        Object.keys(values)?.forEach(question_key => {
            if (
                JSON.stringify(values[question_key] || "") !==
                JSON.stringify(defaultValues.current?.[question_key] || "")
            ) {
                dirty = true;
            }
        });
        setIsDirty(dirty);
    };

    useEffect(() => {
        if (!friendly_url) return;
        const company_id = query.get("company_id");
        const getProduct: Promise<AxiosResponse<any>> = new Promise((resolve, reject) => {
            company_id
                ? productService.getProductById(company_id, friendly_url).then(resolve).catch(reject)
                : productService.getByFriendlyUrl(friendly_url).then(resolve).catch(reject);
        });
        Promise.all([getProduct])
            .then(([p]) => {
                handleSuccessGetProduct(p);
                setIsFetchingComplete(true);
            })
            .catch((e: AxiosError<any>) => {
                setIsFetchingComplete(true);
                if (e.response?.status === 404) {
                    return handleError(e, true);
                }
                handleError(e);
            });
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [friendly_url]);

    useEffect(() => {
        if (questions) {
            methods.setValue("title", review?.title);
            methods.setValue(recommended_id, String(review?.recommended_by_reviewer_value));
            methods.setValue(job_title_id, (review.reviewer as any)?.job_title?.id);
            setDefaultAnswerValues(questions, answers);
        }
    }, [questions]);

    if (!isFetchingComplete || !productInfo?.id)
        return (
            <div className="d-flex justify-content-center align-items-center w-100 h-100">
                <Loader inline loading big />
            </div>
        );
    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(handleSubmit)} onChange={checkIsDirty}>
                {canEdit && (
                    <div className="d-flex flex-row justify-content-end mb-2">
                        <MuiButtonComponent id="save" type="submit" disabled={isUpdating || !isDirty}>
                            {isUpdating ? <Loader inline loading version="iconOrange" /> : "Save"}
                        </MuiButtonComponent>
                    </div>
                )}
                <div className="w-100">
                    <TextBoxComponent
                        id="title"
                        placeholder="Review Title"
                        label="Give a title for your review"
                        helperText={REVIEW_TITLE_MIN_CHARACTERS}
                        minLengthErrorMessage={REVIEW_TITLE_MIN_CHARACTERS}
                        isRequired={REVIEW_TITLE_REQUIRED}
                        minLength={5}
                        maxLength={150}
                        validateOnTheFly
                        containerClassName="w-100 mb-2"
                        disabled={!canEdit}
                    />
                </div>
                {!canEdit && (
                    <StarRating
                        id={recommended_id}
                        label={`How likely are you to recommend ${productInfo?.name || ""} to a friend or colleague?`}
                        showLabels={false}
                        maxRating={5}
                        isRequired
                        errorMessage={"This field is required"}
                        name={recommended_id}
                        leftText={""}
                        rightText={""}
                        disabled
                    />
                )}
                <ReviewQuestionBuilder
                    questions={questions}
                    productInfo={productInfo}
                    isAdminPage
                    disabled={!canEdit}
                />
            </form>
        </FormProvider>
    );
};

export default AdminReviewForm;
