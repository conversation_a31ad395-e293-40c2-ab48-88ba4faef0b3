import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {Typeahead} from "react-bootstrap-typeahead";
import {FormProvider, useForm} from "react-hook-form";
import {useNavigate} from "react-router-dom";
import {CSSTransition, SwitchTransition} from "react-transition-group";
import useNotification from "../../hooks/useNotification";
import {IProducts} from "../../Interfaces/products.interface";
import {ReviewQuestion, ReviewQuestionOption} from "../../Interfaces/reviews.interface";
import {productService} from "../../services/products.service";
import {reviewService} from "../../services/review.service";
import {userService} from "../../services/user.service";
import ControlledCheckBox from "../FormControls/controlledCheckBox.component";
import DropdownComponent from "../FormControls/dropdown.component";
import RatingInput from "../FormControls/ratingInput.component";
import TextAreaComponent from "../FormControls/textArea.component";
import TextBoxComponent from "../FormControls/textBox.component";
import routeConfig from "../../constants/routeConfig";
import {getErrorFromArray} from "../../utils/error.util";
import useQuery from "../../utils/query.util";
import styles from "../../styles/pages/createReview.module.sass";
import {IAdminProductReview} from "../../Interfaces/adminReview.interface";
import Loader from "../../utils/loader";
import {useDebounce} from "../../hooks/useDebounce";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import StarRating from "../FormControls/starRating.component";
import {REVIEW_TITLE_MIN_CHARACTERS, REVIEW_TITLE_REQUIRED} from "../../constants/commonStrings.constant";

interface IProps {
    answers: any[];
    friendly_url: string;
    review: IAdminProductReview;
    handleSuccessUpdate: Function;
    handleSuccessUpdateAnswer: Function;
    setIsValidState: Function;
}

interface IReviewFormData {
    title: string;
    job_title_id: string;
    model_id: string;
    model_type: string;
    incentivize: number;
    hide_reviewer_name: number;
    recommended_by_reviewer_value: number;
    answers: IFormAnswer[];
}

interface IFormAnswer {
    review_question_id: string;
    review_question_option?: any;
    answer?: string;
    review_question_option_id: string;
    review_question_option_ids: string[];
}

const ReviewForm = (props: IProps) => {
    const {friendly_url, setIsValidState} = props;
    const [filledFields, setFilledFields] = useState<any>({});
    const [productInfo, setProductInfo] = useState<IProducts>();
    const [questions, setQuestions] = useState<any>([]);
    const [isFetchingComplete, setIsFetchingComplete] = useState<boolean>(false);
    const [jobTitles, setJobTitles] = useState<{id: string; name: string}[]>([]);
    const {settings, updateSettings} = useSettings();
    //@ts-ignore
    const defaultJobTitle: any = props.review.reviewer?.job_title;
    const [selectedJobTitle, setSelectedJobTitle] = useState<{id: string; name: string}[]>(
        defaultJobTitle && jobTitles ? jobTitles?.filter(j => j.id === defaultJobTitle.id) || [] : [],
    );
    const [multipleChoice, setMultipleChoice] = useState<any>({});
    const [currentFormFields, setCurrentFormFields] = useState<IReviewFormData>({
        job_title_id: defaultJobTitle?.id,
        model_type: "product",
        incentivize: 0,
        answers: [] as IFormAnswer[],
    } as IReviewFormData);

    const query = useQuery();
    const reviewForm: any = useRef({});
    const methods = useForm();
    const navigate = useNavigate();
    const notify = useNotification();
    const GoToOptions = questions.length && questions[3].options.map((option: ReviewQuestionOption) => option.id);
    const LikeAboutOptions = questions.length && questions[4].options.map((option: ReviewQuestionOption) => option.id);
    const debouncedTitle = useDebounce(currentFormFields.title, 500);
    const recommended_by_reviewer_value = currentFormFields.recommended_by_reviewer_value;

    const replaceProductName = (text: string) => {
        return productInfo?.name ? text.replace("the product", productInfo.name) : text;
    };

    const questionBuilder = (questions: ReviewQuestion[]) => {
        return (
            <>
                {questions.map((question: ReviewQuestion) => {
                    switch (question.question_type) {
                        case "StarRating":
                            return (
                                <StarRating
                                    id={question.id}
                                    testid={`starRating-${question.question}`}
                                    label={replaceProductName(question.question)}
                                    options={question.options}
                                    checked={filledFields}
                                    isRequired={question.is_required}
                                    errorMessage={"This field is required"}
                                    inputError={methods.formState.errors[question.id] !== undefined}
                                    name={question.id}
                                    showLabels={false}
                                    handleChange={handleFormChange}
                                    maxRating={5}
                                    allowHalfStars={question.options?.length > 5 || !question.options?.length}
                                    rightLabel={`${
                                        question.options?.length
                                            ? question.options?.find(o => o.id === methods.watch(question.id))
                                                  ?.display_value || 0
                                            : methods.watch(question.id) || 0
                                    } Stars`}
                                />
                            );
                        case "Scale":
                            return (
                                <div className="my-3" key={question.id}>
                                    <RatingInput
                                        id={question.id}
                                        label={replaceProductName(question.question)}
                                        options={question.options}
                                        maxRating={question.options.length}
                                        isRequired={question.is_required}
                                        errorMessage={"This field is required"}
                                        inputError={methods.formState.errors[question.id] !== undefined}
                                        name={question.id}
                                        disabled={question.is_about_you_question}
                                    />
                                </div>
                            );
                        case "OpenText":
                            return question.question === "Organization" ? (
                                <TextBoxComponent
                                    id={question.id}
                                    isRequired={question.is_required}
                                    validateOnTheFly={question.is_required}
                                    label="Organization"
                                    key={question.id}
                                    disabled
                                />
                            ) : (
                                <TextAreaComponent
                                    label={replaceProductName(question.question)}
                                    id={question.id}
                                    testid={`question-${question.question}`}
                                    minLength={150}
                                    maxLength={2500}
                                    className={styles.reviewTextArea}
                                    containerClassName="mw-100 my-2"
                                    isRequired={question.is_required}
                                    validateOnTheFly={true}
                                    validationSchema={v => {
                                        return v.length === 0 && !question.is_required
                                            ? true
                                            : v.length < 2500 && v.length > 150;
                                    }}
                                    key={question.id}
                                    disabled={question.is_about_you_question}
                                />
                            );
                        case "SingleLineText":
                            return (
                                <TextBoxComponent
                                    id={question.id}
                                    isRequired={question.is_required}
                                    validateOnTheFly={question.is_required}
                                    label={question.question}
                                    key={question.id}
                                    disabled={question.is_about_you_question}
                                />
                            );
                        case "MultipleAnswer":
                            return (
                                <div className="my-3" key={question.id}>
                                    <label className={styles.createReviewLabel}>
                                        {replaceProductName(question.question)}
                                        {" (Check all that apply) "}
                                        {question.is_required &&
                                            methods.formState.errors[question.id] !== undefined && (
                                                <span className="ms-5 errorColor">This field is required</span>
                                            )}
                                    </label>
                                    <div className="d-flex flex-column ms-4">
                                        {question.options.map((option: ReviewQuestionOption) => {
                                            return (
                                                <ControlledCheckBox
                                                    key={option.id}
                                                    id={option.id}
                                                    testid={`option-${option.display_value}`}
                                                    name={question.id}
                                                    value={multipleChoice[option.id] || false}
                                                    onChange={e => {
                                                        setMultipleChoice({
                                                            ...multipleChoice,
                                                            [option.id]: e.target.checked,
                                                        });
                                                    }}
                                                    isRequired={question.is_required}>
                                                    {option.display_value}
                                                </ControlledCheckBox>
                                            );
                                        })}
                                    </div>
                                </div>
                            );
                        case "SingleAnswer":
                            if (question.question === "JOB_TITLE") {
                                return (
                                    <div className="d-flex flex-column w-100 mb-4">
                                        <label className="cpLabel align-self-start">Job Title</label>
                                        <Typeahead
                                            id="job_title_id"
                                            options={jobTitles}
                                            clearButton
                                            onChange={(e: any) => {
                                                setSelectedJobTitle(e);
                                                if (e.length > 0) {
                                                    setCurrentFormFields({
                                                        ...currentFormFields,
                                                        job_title_id: e[0].id,
                                                    });
                                                }
                                            }}
                                            placeholder={
                                                !!jobTitles.length
                                                    ? "Select one from the list"
                                                    : "Loading job titles..."
                                            }
                                            //@ts-ignore
                                            selected={selectedJobTitle}
                                            isLoading={!!!jobTitles.length}
                                            labelKey={"name"}
                                            disabled
                                            className="cpInputContainer mb-0"
                                        />
                                    </div>
                                );
                            }
                            if (question.question === "RECOMMENDATION_RATING") {
                                return (
                                    <StarRating
                                        id={"recommended_by_reviewer_value"}
                                        label={`How likely are you to recommend ${
                                            productInfo?.name || ""
                                        } to a friend or colleague?`}
                                        showLabels={false}
                                        maxRating={5}
                                        checked={filledFields}
                                        isRequired
                                        errorMessage={"This field is required"}
                                        inputError={
                                            methods.formState.errors["recommended_by_reviewer_value"] !== undefined
                                        }
                                        name={"recommended_by_reviewer_value"}
                                        leftText={""}
                                        rightText={""}
                                        handleChange={handleFormChange}
                                        allowHalfStars
                                        rightLabel={`${methods.watch("recommended_by_reviewer_value") || 0} Stars`}
                                    />
                                );
                            }
                            return (
                                <DropdownComponent
                                    id={question.id}
                                    options={question.options}
                                    label={question.question}
                                    isRequired={question.is_required}
                                    validateOnTheFly={question.is_required}
                                    validationSchema={v => v !== "0"}
                                    showError={methods.formState.errors[question.id] !== undefined}
                                    helperText={question.is_required ? "This is required" : ""}
                                    placeholder="Select an option"
                                    key={question.id}
                                    disabled={question.is_about_you_question}
                                />
                            );
                        default:
                            return <></>;
                    }
                })}
            </>
        );
    };

    const handleFormChange = (e: any) => {
        if (!e.target.id) return;
        const {id, value, name, checked} = e.target;
        methods.clearErrors(id);
        if (GoToOptions.includes(id) || LikeAboutOptions.includes(id)) {
            setFilledFields({...filledFields, [name]: value ? true : false});
        } else {
            setFilledFields({...filledFields, [id]: value ? true : false});
        }
        const foundQuestion = questions.find((q: ReviewQuestion) => q.id === name || q.id === id);
        if (foundQuestion) {
            const foundAnswerIndex = currentFormFields.answers?.findIndex(
                (a: IFormAnswer) => a.review_question_id === name,
            );
            let newAnswers: any = [...currentFormFields.answers];
            if (foundQuestion.options.length) {
                const isSingleAnswer =
                    foundQuestion.question_type === "SingleAnswer" ||
                    foundQuestion.question_type === "Scale" ||
                    foundQuestion.question_type === "StarRating";
                if (isSingleAnswer) {
                    const newObject = {review_question_id: id, review_question_option_ids: [value]};
                    if (foundAnswerIndex !== -1) {
                        newAnswers[foundAnswerIndex] = newObject;
                    } else {
                        newAnswers = [...newAnswers, newObject];
                    }
                    setCurrentFormFields({
                        ...currentFormFields,
                        answers: newAnswers,
                    });
                } else {
                    const newObject = {review_question_id: name, review_question_option_ids: [id]};
                    if (foundAnswerIndex !== -1) {
                        let newOptionIds = [...newAnswers[foundAnswerIndex].review_question_option_ids!];
                        newOptionIds = newOptionIds.includes(id)
                            ? checked
                                ? newOptionIds
                                : newOptionIds.filter(i => i !== id)
                            : [...newOptionIds, id];
                        if (newOptionIds.length === 0) {
                            newAnswers.splice(foundAnswerIndex, 1);
                        } else {
                            newAnswers[foundAnswerIndex] = {
                                ...newObject,
                                review_question_option_ids: newOptionIds,
                            };
                        }
                    } else {
                        newAnswers = [...newAnswers, newObject];
                    }
                    setMultipleChoice({...multipleChoice, [id]: checked});
                    setCurrentFormFields({
                        ...currentFormFields,
                        answers: newAnswers,
                    });
                }
            } else {
                const newObject = {review_question_id: name, answer: value};
                if (foundAnswerIndex !== -1) {
                    newAnswers[foundAnswerIndex] = newObject;
                } else {
                    newAnswers = [...newAnswers, newObject];
                }
                setCurrentFormFields({
                    ...currentFormFields,
                    answers: newAnswers,
                });
            }
        } else {
            let newValue: any = null;
            switch (id) {
                case "hide_reviewer_name":
                    newValue = value === "Yes" ? 0 : 1;
                    break;
                case "title":
                    newValue = value;
                    break;
                case "recommended_by_reviewer_value":
                    newValue = parseInt(value);
                    break;
                default:
                    break;
            }
            newValue !== null && setCurrentFormFields({...currentFormFields, [id]: newValue});
        }
    };

    const handleError = (error: AxiosError) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleErrorGetProduct = () => {
        notify("That product could not be found", "Error");
        navigate(routeConfig.ProductReviewSearch.path);
    };

    const handleSuccessGetProduct = (res: AxiosResponse) => {
        setProductInfo(res.data);
        reviewForm.current = {...reviewForm.current, model_id: res.data.id};
        setCurrentFormFields({...currentFormFields, model_id: res.data.id});
    };

    const handleSuccessGetQuestions = (res: AxiosResponse) => {
        const ordered = res.data.sort((a: any, b: any) => a.order - b.order);
        setQuestions(ordered);
    };

    const handleUpdateReview = (checkOpenText?: boolean) => {
        const values = methods.getValues();
        const errors = methods.formState.errors;

        let foundFields = props.answers.filter(
            answer =>
                answer.review_question.question !== "Organization" && !answer.review_question.is_about_you_question,
        );
        foundFields = foundFields.filter(field => {
            if (field.review_question.question_type !== "OpenText") return true;
            return Boolean(checkOpenText);
        });
        const currentAnswers = currentFormFields.answers;
        const checkedMultipleAnswers: any = {};
        foundFields.forEach(field => {
            const {question_type} = field.review_question;
            const currentValue = currentAnswers.find(i => i.review_question_id === field.review_question_id);
            if (question_type === "OpenText" && checkOpenText) {
                if (
                    values[field.review_question_id] !== undefined &&
                    values[field.review_question_id] !== field.answer
                ) {
                    if (errors[field.review_question_id]) {
                        setIsValidState(false);
                        return;
                    }
                    setIsValidState(true);
                    updateSettings(UPDATE_SETTINGS, {loading: true});
                    reviewService.updateReviewAnswer(
                        {
                            review_id: props.review?.id,
                            question_id: field.review_question_id,
                            answer: values[field.review_question_id],
                            answer_id: field.id,
                        },
                        res => props.handleSuccessUpdateAnswer(res, field.id),
                        handleError,
                    );
                }
            } else if (
                question_type === "StarRating" ||
                question_type === "SingleAnswer" ||
                question_type === "Scale"
            ) {
                if (values[field.review_question_id] !== field.review_question_option_id) {
                    updateSettings(UPDATE_SETTINGS, {loading: true});
                    reviewService.updateReviewAnswer(
                        {
                            review_id: props.review?.id,
                            question_id: field.review_question_id,
                            answer: "",
                            review_question_option_ids: currentValue?.review_question_option_ids,
                            answer_id: field.id,
                        },
                        res => props.handleSuccessUpdateAnswer(res, field.id),
                        handleError,
                    );
                }
            } else if (question_type === "MultipleAnswer" && !checkedMultipleAnswers[field.review_question_id]) {
                checkedMultipleAnswers[field.review_question_id] = true;
                const oldOption = foundFields.filter(f => f.review_question_id === field.review_question_id);
                const currentOptions =
                    questions
                        .find(q => q.id === field.review_question_id)
                        ?.options.filter(option => multipleChoice[option.id]) || [];
                if (currentOptions?.length !== oldOption.length) {
                    updateSettings(UPDATE_SETTINGS, {loading: true});
                    reviewService.updateReviewAnswer(
                        {
                            review_id: props.review?.id,
                            question_id: field.review_question_id,
                            answer: "",
                            review_question_option_ids: currentOptions.map(o => o.id),
                            answer_id: field.id,
                        },
                        res => props.handleSuccessUpdateAnswer(res, field.id, field.review_question),
                        handleError,
                    );
                }
            }
        });
    };

    useEffect(() => {
        if (currentFormFields.answers && props.answers && !settings.loading && isFetchingComplete) {
            handleUpdateReview(false);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentFormFields.answers]);

    useEffect(() => {
        const updatedTitle = debouncedTitle && props.review?.id && debouncedTitle !== props.review?.title;
        const updatedRecommened =
            recommended_by_reviewer_value &&
            props.review.recommended_by_reviewer_value !== recommended_by_reviewer_value;
        if (updatedTitle || updatedRecommened) {
            updateSettings(UPDATE_SETTINGS, {loading: true});
            reviewService.updateReview(
                {id: props.review.id, title: debouncedTitle, recommended_by_reviewer_value},
                props.handleSuccessUpdate,
                handleError,
            );
        }
        //eslint-disable-next-line
    }, [debouncedTitle, recommended_by_reviewer_value]);

    useEffect(() => {
        if (!friendly_url) return;
        const company_id = query.get("company_id");
        const getProduct: Promise<AxiosResponse<any>> = new Promise((resolve, reject) => {
            company_id
                ? productService.getProductById(company_id, friendly_url).then(resolve).catch(reject)
                : productService.getByFriendlyUrl(friendly_url).then(resolve).catch(reject);
        });
        Promise.all([getProduct, reviewService.getReviewQuestions("product")])
            .then(([p, q]) => {
                handleSuccessGetProduct(p);
                handleSuccessGetQuestions(q);
                setIsFetchingComplete(true);
            })
            .catch((e: AxiosError<any>) => {
                setIsFetchingComplete(true);
                if (e.response?.status === 404) {
                    return handleErrorGetProduct();
                }
                handleError(e);
            });
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [friendly_url]);

    useEffect(() => {
        userService.getJobTitles(r => {
            if (defaultJobTitle) setSelectedJobTitle(r.data?.filter(j => j.id === defaultJobTitle.id) || []);
            setJobTitles(r.data);
        }, handleError);
    }, []);

    useEffect(() => {
        if (props.review?.title) {
            methods.setValue("title", props.review.title);
        }
        if (props.answers) {
            const newMultiple = {...multipleChoice};
            methods.setValue("recommended_by_reviewer_value", String(props.review.recommended_by_reviewer_value));
            props.answers.forEach((answer: IFormAnswer) => {
                if (answer.review_question_option_id) {
                    newMultiple[answer.review_question_option_id] = true;
                    methods.setValue(answer.review_question_option_id, true);
                }
                methods.setValue(answer.review_question_id, answer.answer || answer.review_question_option_id);
            });
            setMultipleChoice(newMultiple);
        }
    }, [props.answers, props.review]);

    if (!isFetchingComplete)
        return (
            <div className="d-flex justify-content-center align-items-center w-100 h-100">
                <Loader inline loading big />
            </div>
        );
    return (
        <>
            <FormProvider {...methods}>
                <SwitchTransition mode="out-in">
                    <CSSTransition
                        // key={props.answers?.length}
                        key={1}
                        component="form"
                        onChange={handleFormChange}
                        className="fadeLeftTransition"
                        timeout={200}>
                        <form
                            onChange={handleFormChange}
                            onBlur={e => {
                                if (e.target.tagName === "TEXTAREA") {
                                    //? Call update
                                    handleUpdateReview(true);
                                }
                            }}>
                            <TextBoxComponent
                                id="title"
                                placeholder="Review Title"
                                label="Give a title for your review"
                                showError={methods.formState.errors.title !== undefined}
                                helperText={
                                    methods.getValues("title")?.length === 0
                                        ? REVIEW_TITLE_REQUIRED
                                        : REVIEW_TITLE_MIN_CHARACTERS
                                }
                                minLength={5}
                                maxLength={150}
                                isRequired
                                validateOnTheFly
                            />
                            {questions.length !== 0 ? questionBuilder(questions) : null}
                        </form>
                    </CSSTransition>
                </SwitchTransition>
            </FormProvider>
        </>
    );
};

export default ReviewForm;
