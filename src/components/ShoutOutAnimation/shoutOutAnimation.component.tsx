import {AxiosError, AxiosResponse} from "axios";
import React, {useEffect, useRef, useState} from "react";
import {Link} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import useAppConfig from "../../hooks/useAppConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePusher from "../../hooks/usePusher";
import usePusherState from "../../hooks/usePusherState";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import {followCompanyService} from "../../services/followCompany.service";
import styles from "../../styles/components/shoutOutAnimation.module.sass";
import {getErrorFromArray} from "../../utils/error.util";

interface IProps {}

const ShoutOutAnimation: React.FC<IProps> = () => {
    const hasSubscribed = useRef(false);
    const shoutOutElmt = useRef<HTMLDivElement>(null);
    const shoutOutEvents = useRef<any[]>([]);
    const {config} = useAppConfig({config_key: "SHOW_SHOUTOUT_ON_WATCH_PITCH_PAGE", isPrivateKey: true});
    const SHOW_SHOUTOUT_ON_WATCH_PITCH_PAGE = config?.value?.trim().toLowerCase() === "true";
    const [shoutOutList, setShoutOutList] = useState<any[]>([]);
    const {authState, updateAuthState} = useAuthState();
    const [refreshKey, setRefreshKey] = useState(0);
    const {BindEvents, UnbindEvents} = usePusher();
    const {pusherState} = usePusherState();
    const notify = useNotification();

    const handleShoutOutEvent = (data: any) => {
        if (!shouldPreventShoutOut()) {
            setShoutOutList([...shoutOutList, data]);
        }
    };

    const successGetFollowingUsers = (response: AxiosResponse) => {
        if (response.data.data !== undefined) {
            updateAuthState(UPDATE_AUTH, {following: response.data.data});
        }
        if (!response.data.data) {
            shoutOutEvents.current = [`client-so-${import.meta.env.VITE_APP_CP_COMPANY_ID || ""}`];
            if (pusherState.privateChannel) {
                BindEvents(shoutOutEvents.current, handleShoutOutEvent);
            }
        }
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        return;
    };

    const handleAnimationEnd = () => {
        const updatedShoutOuts = [...shoutOutList];
        updatedShoutOuts.shift();
        setShoutOutList(updatedShoutOuts);
        setRefreshKey(refreshKey + 1);
    };

    useEffect(() => {
        if (authState.authenticated && authState.following) {
            shoutOutEvents.current =
                authState.following?.map((item: any) => {
                    return `client-so-${item.id}`;
                }) || [];
            if (pusherState.privateChannel) {
                BindEvents(shoutOutEvents.current, handleShoutOutEvent);
            }
        }
        //@ts-ignore
        return () => {
            if (shoutOutEvents.current) {
                UnbindEvents(shoutOutEvents.current, true);
            }
        };
    }, [authState.following]);

    useEffect(() => {
        if (!import.meta.env.VITE_APP_SHOW_SHOUTOUT) {
            return;
        }
        if (authState.authenticated && !hasSubscribed.current) {
            hasSubscribed.current = true;
            followCompanyService.getAllUserFollowers(authState?.id || "", successGetFollowingUsers, handleError);
        }
        const preloadedShort = new Image();
        preloadedShort.src = "/Media/backgrounds/shortChatBubble-bg.svg";
        const preloadedLong = new Image();
        preloadedLong.src = "/Media/backgrounds/longChatBubble-bg.svg";
        return () => {
            hasSubscribed.current = false;
        };
    }, [authState.authenticated]);

    const shouldPreventShoutOut = () => {
        let shouldSend: boolean = true;
        if (window.innerWidth < 1200) {
            shouldSend = false;
        }
        if (window.location.pathname === "/watch/pitch" && !SHOW_SHOUTOUT_ON_WATCH_PITCH_PAGE) {
            shouldSend = false;
        }
        return !shouldSend;
    };

    if (shoutOutList.length === 0) return null;
    const shoutOut = shoutOutList[0];
    return (
        <div
            className={`${styles.shoutOutNotification} ${
                shoutOut?.shout_out.length > 110 ? styles.long : styles.short
            }`}
            ref={shoutOutElmt}
            key={refreshKey}
            onAnimationEnd={handleAnimationEnd}
            style={{
                backgroundImage:
                    shoutOut?.shout_out.length > 110
                        ? "url(/Media/backgrounds/longChatBubble-bg.svg)"
                        : "url(/Media/backgrounds/shortChatBubble-bg.svg)",
            }}>
            <Link
                target="_blank"
                to={
                    shoutOut?.isCompany
                        ? routeConfig.VendorProfile.path.replace(":id", shoutOut?.friendly_url)
                        : routeConfig.UserProfile.path.replace(":id", shoutOut?.friendly_url)
                }
                className={
                    styles.shoutOutContent +
                    ` ${
                        shoutOut?.shout_out.length <= 50
                            ? styles.shoutOutTextLg
                            : shoutOut?.shout_out.length <= 125
                            ? styles.shoutOutTextMd
                            : styles.shoutOutTextSm
                    }`
                }>
                {shoutOut?.shout_out}
            </Link>
            <div className={styles.avatarContainer}>
                <img src={shoutOut?.profile_pic} alt="avatar" />
            </div>
        </div>
    );
};

export default ShoutOutAnimation;
