import {useEffect} from "react";
import {Col} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../hooks/useNotification";
import ButtonComponent from "../../FormControls/button.component";
import TextBoxComponent from "../../FormControls/textBox.component";
import {MinusIcon} from "../../Icons";
import styles from "../../../styles/pages/profileEnrichmentManagment.module.sass";
import Loader from "../../../utils/loader";

interface IProps {
    option: any;
    handleAdd: Function;
    handleUpdate: Function;
    handleDelete: Function;
    isDeleting: boolean;
    isSaving: boolean;
    index: number;
}

const QuestionOption: React.FC<IProps> = (props: IProps) => {
    const {option, handleAdd, handleDelete, handleUpdate, isSaving, isDeleting} = props;
    const notify = useNotification();
    const methods = useForm();

    const handleSubmit = data => {
        if (!data.option) {
            notify("Please enter an option", "Error");
            return;
        }
        if (option.id) {
            handleUpdate({...option, option: data.option});
        } else {
            handleAdd(data.option);
        }
    };

    const handleClickDelete = () => {
        if (option.id && !isDeleting) {
            handleDelete(option.id);
        }
    };

    useEffect(() => {
        methods.setValue("option", option.option);
        //eslint-disable-next-line
    }, [option]);

    return (
        <div className="d-flex flex-column">
            <FormProvider {...methods}>
                <form
                    className="d-flex flex-row flex-wrap align-items-center"
                    onSubmit={methods.handleSubmit(handleSubmit)}>
                    <Col lg={3} md={3} sm={12} xs={12} className="py-1">
                        <TextBoxComponent
                            id="option"
                            label={`Option #${props.index}`}
                            value={methods.watch("option")}
                        />
                    </Col>
                    <Col lg={1} md={1} sm={1} xs={1} className="py-1 justify-content-center d-flex">
                        <ButtonComponent
                            onClick={handleClickDelete}
                            type="button"
                            className={styles.removeBtn}
                            id={`deleteOption-${option.id}`}>
                            {isDeleting ? <Loader inline loading /> : <MinusIcon size={24} />}
                        </ButtonComponent>
                    </Col>
                    <Col lg={3} md={3} sm={12} xs={12} className="py-1">
                        <ButtonComponent
                            disabled={isSaving}
                            type="submit"
                            className={styles.editBtn}
                            id="savePollAnswer">
                            {isSaving ? <Loader inline loading /> : "Save Answer"}
                        </ButtonComponent>
                    </Col>
                </form>
            </FormProvider>
        </div>
    );
};

export default QuestionOption;
