import {AxiosError, AxiosResponse} from "axios";
import React, {useEffect, useState} from "react";
import useNotification from "../../../hooks/useNotification";
import {profileEnrichmentService} from "../../../services/profileEnrichment.service";
import {getErrorFromArray} from "../../../utils/error.util";
import ButtonComponent from "../../FormControls/button.component";
import QuestionOption from "./QuestionOption.component";
import styles from "../../../styles/pages/profileEnrichmentManagment.module.sass";
import Loader from "../../../utils/loader";
import {IOptionType} from "../../../Interfaces/profileEnrichment.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";

interface IProps {
    questionId: string;
}

const QuestionOptions: React.FC<IProps> = (props: IProps) => {
    const {questionId} = props;
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isSaving, setIsSaving] = useState<any>({});
    const [isDeleting, setIsDeleting] = useState<any>({});
    const [options, setOptions] = useState<any[]>([]);
    const notify = useNotification();

    const handleSuccessGetOptions = (res: AxiosResponse) => {
        setOptions([...options, ...res.data]);
        setIsLoading(false);
    };

    const handleSuccessAdd = (res: AxiosResponse) => {
        const optionsCopy: IOptionType[] = [...options];
        const foundIndex: number = optionsCopy.findIndex((option: IOptionType) => option.id === "");
        if (foundIndex > -1) {
            optionsCopy.splice(foundIndex, 1, res.data);
            setOptions(optionsCopy);
            setIsSaving({...isSaving, new_option: false});
            notify("Option added successfully", "Success");
        }
    };

    const handleSuccessUpdate = (res: AxiosResponse) => {
        const optionsCopy: IOptionType[] = [...options];
        const foundIndex: number = optionsCopy.findIndex((option: IOptionType) => option.id === res.data.id);
        if (foundIndex > -1) {
            optionsCopy.splice(foundIndex, 1, res.data);
            setOptions(optionsCopy);
            setIsSaving({...isSaving, [res.data.id]: false});
            notify("Option updated successfully", "Success");
        }
    };

    const handleError = (err: AxiosError<any>) => {
        setIsDeleting({});
        setIsSaving({});
        notify(getErrorFromArray(err), "Error");
    };

    const handleAddOption = (option: string) => {
        setIsSaving({...isSaving, new_option: true});
        profileEnrichmentService.storeQuestionOption(questionId, option, handleSuccessAdd, handleError);
    };

    const handleUpdateOption = (option: IOptionType) => {
        if (option.option === "") {
            notify("Please enter option", "Error");
            return;
        }
        setIsSaving({...isSaving, [option.id]: true});
        profileEnrichmentService.updateQuestionOption(questionId, option, handleSuccessUpdate, handleError);
    };

    const handleDeleteOption = async (optionId: string) => {
        const answer = await getUserConfirmation("Are you sure you want to delete this option?");
        if (answer.value) {
            setIsDeleting({...isDeleting, [optionId]: true});
            profileEnrichmentService.deleteQuestionOption(
                questionId,
                optionId,
                () => {
                    const foundIndex: number = options.findIndex(option => option.id === optionId);
                    if (foundIndex > -1) {
                        const optionCopy = [...options];
                        optionCopy.splice(foundIndex, 1);
                        setOptions(optionCopy);
                        setIsDeleting({...isDeleting, [optionId]: true});
                        notify("Option deleted successfully", "Success");
                    }
                },
                handleError,
            );
        }
    };

    const addNewEmptyOption = () => {
        setOptions([...options, {id: "", option: ""}]);
    };

    useEffect(() => {
        if (questionId !== "create-new" && questionId) {
            setIsLoading(true);
            profileEnrichmentService.getQuestionOptionsById(props.questionId, handleSuccessGetOptions, handleError);
        }
        //eslint-disable-next-line
    }, [questionId]);

    return (
        <div>
            <h3>Add Options</h3>
            {isLoading ? (
                <Loader loading inline />
            ) : questionId !== "create-new" && questionId ? (
                <>
                    {options.map((option, index) => {
                        const isLoadingKey: string = option.id ? option.id : "new_option";
                        return (
                            <QuestionOption
                                key={index}
                                index={index + 1}
                                option={option}
                                handleAdd={handleAddOption}
                                handleDelete={handleDeleteOption}
                                handleUpdate={handleUpdateOption}
                                isDeleting={isDeleting[isLoadingKey]}
                                isSaving={isSaving[isLoadingKey]}
                            />
                        );
                    })}
                    <ButtonComponent
                        disabled={isLoading || options[options.length - 1]?.id === ""}
                        onClick={addNewEmptyOption}
                        className={styles.addOptionBtn}
                        id="add-option-btn"
                        type="submit">
                        Add Option
                    </ButtonComponent>
                </>
            ) : (
                <span>Save first to add options.</span>
            )}
        </div>
    );
};

export default QuestionOptions;
