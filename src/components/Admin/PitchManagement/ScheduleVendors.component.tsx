import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {ICategory} from "../../../Interfaces/categories.interface";
import {
    IAvailableVendors,
    IScheduledVendor,
    InsertScheduleVendorType,
    UpdateScheduleVendorType,
} from "../../../Interfaces/vendor.interface";
import {categoriesService} from "../../../services/category.service";
import {scheduleVendorService} from "../../../services/scheduleVendor.service";
import styles from "../../../styles/components/scheduledVendor.module.sass";
import Loader from "../../../utils/loader";
import useQuery from "../../../utils/query.util";
import {CPToast} from "../../CPToast";
import ButtonComponent from "../../FormControls/button.component";
import {PlusIcon} from "../../Icons";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import VendorSpot from "./VendorSpot.component";

interface IProps {
    unsaved: boolean;
    setUnsaved: Function;
}

export default function ScheduleVendors(props: IProps) {
    const [records, setRecords] = useState<Array<IScheduledVendor>>([]);
    const [vendorsDropdown, setVendorsDropdown] = useState<Array<[string, string]>>([["", ""]]);
    const [allVendorsDropdown, setAllVendorsDropdown] = useState<Array<[string, string]>>([["", ""]]);
    const [vendors, setVendors] = useState<Array<IScheduledVendor>>([]);
    const [vendorsAvailable, setVendorsAvailable] = useState<Array<IAvailableVendors>>([]);
    const [showNotification, setShowNotification] = useState<boolean>(false);
    const [toastDescription, setToastDescription] = useState<string>("");
    const [toastTitle, setToastTitle] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(false);
    const [schedulingVendor, setSchedulingVendor] = useState<{[x: string]: boolean}>({});
    const [deletingVendor, setDeletingVendor] = useState<{[x: string]: boolean}>({});
    const [newIdIndex, setNewIdIndex] = useState<number>(0);
    const [categories, setCategories] = useState<Array<ICategory>>([]);

    const query = useQuery();
    const eventSelected: string = query.get("pitch_event_id")!;
    const newPitch = query.get("new");

    let recordToDelete = "";
    let vendorSelected: string = "";

    const renderVendorSpot = () => {
        return records.map((record, index) => (
            <div key={index} className={styles.vendorSpotContainer}>
                <VendorSpot
                    recordId={record.id}
                    breakoutLink={record.breakout_room_link}
                    eventId={record.pitch_event_id}
                    vendors={vendorsDropdown}
                    companyId={record.company_id}
                    companyName={record.vendor.name}
                    deleteScheduledVendor={deleteScheduledVendor}
                    saveScheduledVendor={saveScheduledVendor}
                    updateScheduledVendor={updateScheduledVendor}
                    index={index}
                    setSchedulingVendor={setSchedulingVendor}
                    schedulingVendor={schedulingVendor}
                    setDeletingVendor={setDeletingVendor}
                    deletingVendor={deletingVendor}
                    categories={categories}
                    category={record.category}
                    allVendors={allVendorsDropdown}
                />
            </div>
        ));
    };

    const removeVendorFromArray = (id?: string) => {
        const index = vendors.findIndex(v => v.id === id || vendorSelected);
        if (index === -1) return;

        const newVendors = [...vendorsAvailable];
        newVendors.splice(index, 1);

        let availableVendors: Array<[string, string]> = [];
        newVendors.forEach(function (item) {
            availableVendors = [...availableVendors, [item.id, item.name]];
        });
        setVendorsAvailable(newVendors);
        setVendorsDropdown(availableVendors);
    };

    const deleteRecordFromArray = (id: string) => {
        const updatedRecords = [...records];
        const index = updatedRecords.findIndex(ur => ur.id === id);
        if (index !== -1) {
            updatedRecords.splice(index, 1);
            setRecords(updatedRecords);
            removeVendorFromArray();
        }
        setNewIdIndex(newIdIndex - 1);
        props.setUnsaved(false);
    };

    const deleteScheduledVendor = async (id: string, deleteApi: boolean) => {
        if (!deleteApi) {
            deleteRecordFromArray(id);
        } else {
            setDeletingVendor({...deletingVendor, [id]: true});
            recordToDelete = id;
            scheduleVendorService
                .delete(id, onSuccessDeleteScheduledVendor, onErrorDeleteScheduledVendor)
                .finally(() => setDeletingVendor({...deletingVendor, [id]: false}));
        }
    };

    const onSuccessDeleteScheduledVendor = () => {
        deleteRecordFromArray(recordToDelete);
        setLoading(false);
        recordToDelete = "";
    };

    const onErrorDeleteScheduledVendor = () => {
        setToastDescription("A problem occurred while trying to delete the record");
        setToastTitle("Failed");
        setShowNotification(true);
        setLoading(false);
    };

    const updateScheduledVendor = (data: UpdateScheduleVendorType) => {
        setSchedulingVendor({...schedulingVendor, [data.id]: true});
        scheduleVendorService
            .update(data, onSuccessUpdateScheduledVendor, onErrorUpdateScheduledVendor)
            .finally(() => setSchedulingVendor({...schedulingVendor, [data.id]: false}));
    };

    const onSuccessUpdateScheduledVendor = () => {
        setToastDescription("Record updated successfully");
        setToastTitle("Updated");
        setShowNotification(true);
        setLoading(false);
    };

    const onErrorUpdateScheduledVendor = () => {
        setToastDescription("A problem occurred while trying to update the record");
        setToastTitle("Failed");
        setShowNotification(true);
        setLoading(false);
    };

    const onSuccessInsertScheduledVendor = (res: AxiosResponse, id: string) => {
        setToastDescription("Record saved successfully");
        setToastTitle("Saved");
        setShowNotification(true);
        updateRecord(id, res.data);
    };

    const updateRecord = (id: string, records: any) => {
        setSchedulingVendor({...schedulingVendor, [id]: false});
        setRecords(records);
        // setNewIdIndex(newIdIndex + 1);
        props.setUnsaved(false);
    };

    const saveScheduledVendor = (data: InsertScheduleVendorType, id: string) => {
        setSchedulingVendor({...schedulingVendor, [id]: true});
        vendorSelected = data.company_id;
        scheduleVendorService.insert(
            data,
            res => onSuccessInsertScheduledVendor(res, id),
            onErrorInsertScheduledVendor,
        );
    };

    const onErrorInsertScheduledVendor = (error: AxiosError<any>) => {
        if (error.response?.data.errors[0][0] === "VENDOR_ALREADY_SCHEDULED") {
            setToastDescription("The vendor has already been scheduled for this event");
            setToastTitle("Validation");
            setShowNotification(true);
            setLoading(false);
            return;
        }
        setToastDescription("A problem occurred while trying to update the record");
        setToastTitle("Failed");
        setShowNotification(true);
        setLoading(false);
    };

    const onClickAdd = () => {
        const newID = newIdIndex.toString();
        const newRecord: IScheduledVendor = {
            id: newID,
            pitch_event_id: eventSelected,
            breakout_room_link: "",
            company_id: newID,
            notification_user_ids: [],
            vendor: {name: ""},
        };

        const recordsCopy = [...records, newRecord];
        setRecords(recordsCopy);
        setNewIdIndex(newIdIndex + 1);
        props.setUnsaved(true);
    };

    const loadVendors = (triggerLoading?: boolean) => {
        props.setUnsaved(false);
        if (!eventSelected || eventSelected === "0") return;

        scheduleVendorService.listAvailable(eventSelected, onSuccessLoadVendors, onErrorLoadVendors).finally(() => {
            if (triggerLoading) setLoading(false);
        });
    };

    const onSuccessLoadVendors = (response: AxiosResponse<Array<IAvailableVendors>>) => {
        let availableVendors: any = [];
        const vendorsResponse = response.data.sort((a, b) => (a.name > b.name ? 1 : b.name > a.name ? -1 : 0));
        vendorsResponse.forEach(function (item) {
            availableVendors = [...availableVendors, [item.id, item.name]];
        });
        setVendorsAvailable(response.data);
        setVendorsDropdown(availableVendors);
        setAllVendorsDropdown(availableVendors);
    };

    const onErrorLoadVendors = () => {
        setToastTitle("Vendors Error");
        setToastDescription("Occurred an error while trying to load the vendors");
        setShowNotification(true);
    };

    const loadRecords = () => {
        setRecords([]);
        props.setUnsaved(false);
        if (!eventSelected || eventSelected === "0") return;

        setLoading(true);

        scheduleVendorService.showAll(eventSelected, onSuccessShowAll, onErrorShowAll);
    };

    const onSuccessShowAll = (response: AxiosResponse<Array<IScheduledVendor>>) => {
        if (response.data.length < 1) {
            const dummyRecord: IScheduledVendor = {
                id: newIdIndex.toString(),
                pitch_event_id: eventSelected,
                breakout_room_link: "",
                company_id: newIdIndex.toString(),
                notification_user_ids: [],
                vendor: {name: ""},
                scheduled: false,
            };
            setRecords([dummyRecord]);
            setNewIdIndex(newIdIndex + 1);
            return setLoading(false);
        }
        response.data.forEach(function (item) {
            item.scheduled = true;
        });
        setVendors(response.data);
        setRecords(response.data);
        setLoading(false);
    };

    const onErrorShowAll = () => {};

    const loadCategories = () => {
        categoriesService.getAllCategories(onSuccessLoadCategories, onErrorLoadCategories);
    };

    const onSuccessLoadCategories = (response: AxiosResponse) => {
        const filtered: Array<ICategory> = response.data.filter((category: ICategory) => {
            if (category.is_hidden) return false;
            if (!category.parent_id) {
                for (const cat of response.data) {
                    if (cat.parent_id === category.id) {
                        return true;
                    }
                }
                return false;
            }
            return true;
        });
        setCategories(filtered);
    };

    const onErrorLoadCategories = () => {
        setToastTitle("Error");
        setToastDescription("An error occurred loading the categories");
        setShowNotification(true);
    };

    useEffect(() => {
        Promise.all([loadCategories(), loadVendors()]).then(() => {
            loadRecords();
        });
    }, [eventSelected]);

    return (
        <>
            {newPitch && (
                <SubtitleContainer
                    as="p"
                    text="First save the event to start adding vendors."
                    size="20"
                    className="accentColor"
                    position="start"
                />
            )}
            {loading && (
                <div className="d-flex align-item-center justify-content-center">
                    <Loader loading inline />
                </div>
            )}
            {eventSelected && !loading && renderVendorSpot()}
            {eventSelected && !loading && (
                <div className="d-flex justify-content-end">
                    <ButtonComponent
                        onClick={onClickAdd}
                        className="cpBlueBtn d-flex justify-content-center align-items-center"
                        id="addNewVendor"
                        disabled={props.unsaved}>
                        <PlusIcon size={20} />
                        Add another vendor
                    </ButtonComponent>
                </div>
            )}
            <CPToast
                title={toastTitle}
                description={toastDescription}
                setToastShow={setShowNotification}
                toastShow={showNotification}
                type="light"
            />
        </>
    );
}
