import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {useNavigate} from "react-router";
import routeConfig from "../../../constants/routeConfig";
import useNotification from "../../../hooks/useNotification";
import usePitchesState from "../../../hooks/usePitchesState";
import {IPitchObject} from "../../../models/PitchObject.interface";
import {GET_NOT_STARTED, UPDATE_PITCHES} from "../../../reducers/pitches.reducer";
import {eventService} from "../../../services/event.service";
import styles from "../../../styles/components/pitchTable.module.sass";
import {getErrorFromArray} from "../../../utils/error.util";
import {DATE_TIME_FULL_FORMAT_NODAY, formatDate} from "../../../utils/formatDate";
import Loader from "../../../utils/loader";
import ButtonComponent from "../../FormControls/button.component";
import {EditIcon, PlusIcon} from "../../Icons";
import SubtitleContainer from "../../Titles/subtitleContainer.component";
import TitleContainer from "../../Titles/titleContainer.component";

export default function PitchTable() {
    const [removing, setRemoving] = useState<{[x: string]: boolean}>({});

    const notify = useNotification();
    const {pitchesState, updatePitchesState, isGettingNotStarted} = usePitchesState();
    const navigate = useNavigate();
    let pitchToRemove: string;

    const handleSuccessRemove = () => {
        let newPitches = [...pitchesState.pitches!];
        newPitches = newPitches.filter(pitch => pitch.id !== pitchToRemove);
        setRemoving({...removing, [pitchToRemove!]: false});
        updatePitchesState(UPDATE_PITCHES, {pitches: newPitches});
        notify("Pitch removed successfully", "Success");
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setRemoving({...removing, [pitchToRemove!]: false});
    };

    const handleRemove = async (eventId: string) => {
        setRemoving({...removing, [eventId]: true});
        pitchToRemove = eventId;
        eventService.delete(eventId, handleSuccessRemove, handleError);
    };

    const handleEdit = (eventId: string) => {
        navigate(routeConfig.PitchManagement.path + "?pitch_event_id=" + eventId);
    };

    const handleAdd = () => {
        navigate(routeConfig.PitchManagement.path + "?new=true");
    };

    useEffect(() => {
        if (pitchesState.pitches?.length) return;
        updatePitchesState(GET_NOT_STARTED);
    }, []);

    return (
        <>
            <div className="d-flex align-items-center justify-content-between">
                <TitleContainer as="h1" position="start" text="Engage Management" />
                <ButtonComponent id="newPitchBtn" className={styles.addBtn} onClick={handleAdd} type="button">
                    <PlusIcon size={20} />
                    Create new
                </ButtonComponent>
            </div>
            <div className={styles.tableHeader}>
                <TitleContainer as="h4" text="Name" position="start" noConnector className={styles.tableHeaderItem} />
                <TitleContainer
                    as="h4"
                    text="Date & Time"
                    position="start"
                    noConnector
                    className={styles.tableHeaderItem}
                />
                {/* <TitleContainer
                    as="h4"
                    text="Vendors Scheduled"
                    position="start"
                    noConnector
                    className={styles.tableHeaderItem}
                />
                <TitleContainer
                    as="h4"
                    text="Attendees"
                    position="start"
                    noConnector
                    className={styles.tableHeaderItem}
                /> */}
                <div className={styles.tableHeaderItem}></div>
            </div>
            {!!pitchesState.pitches?.length ? (
                pitchesState.pitches.map((pitch: IPitchObject) => {
                    return (
                        <div className={styles.tableRow} key={pitch.id}>
                            <TitleContainer
                                as="h5"
                                text={pitch.name}
                                position="start"
                                noConnector
                                className={styles.tableItem}
                            />
                            <SubtitleContainer
                                as="p"
                                text={formatDate(pitch.start_date, DATE_TIME_FULL_FORMAT_NODAY, true)}
                                position="start"
                                className={styles.tableItem}
                            />
                            {/* <SubtitleContainer as="p" text={"8"} position="center" className={styles.tableItem} />
                            <SubtitleContainer as="p" text={"175"} position="center" className={styles.tableItem} /> */}
                            <div className={styles.tableItem}>
                                <ButtonComponent
                                    id="removePitchBtn"
                                    className={styles.removeBtn}
                                    type="button"
                                    onClick={() => navigate(routeConfig.PitchDay.path + "?pitch_event_id=" + pitch.id)}>
                                    Pitch day
                                </ButtonComponent>
                                <ButtonComponent
                                    id="removePitchBtn"
                                    type="button"
                                    className={styles.removeBtn}
                                    onClick={() => handleRemove(pitch.id)}
                                    disabled={removing[pitch.id]}>
                                    {removing[pitch.id] ? <Loader inline loading /> : "Remove"}
                                </ButtonComponent>
                                <ButtonComponent
                                    id="editPitchBtn"
                                    type="button"
                                    className={styles.editBtn}
                                    onClick={() => handleEdit(pitch.id)}>
                                    <EditIcon size={20} />
                                    Edit
                                </ButtonComponent>
                            </div>
                        </div>
                    );
                })
            ) : isGettingNotStarted ? (
                <div className={styles.loadingRow}>
                    <Loader inline loading />
                </div>
            ) : (
                <div className="d-flex justify-content-center align-items-center w-100 h-100 py-3">
                    <span className="fw-bolder">No Engage detail available</span>
                </div>
            )}
        </>
    );
}
