import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useContext, useRef, useState} from "react";
import {AccordionContext, useAccordionButton} from "react-bootstrap";
import Accordion from "react-bootstrap/Accordion";
import {FormProvider, useForm} from "react-hook-form";
import {Link} from "react-router-dom";
import routeConfig from "../../../constants/routeConfig";
import {useDebounce} from "../../../hooks/useDebounce";
import useNotification from "../../../hooks/useNotification";
import {eventService} from "../../../services/event.service";
import {getErrorFromArray} from "../../../utils/error.util";
import {DATE_TIME_FULL_FORMAT_NODAY, formatDate} from "../../../utils/formatDate";
import TextBoxComponent from "../../FormControls/textBox.component";
import {TableComponent} from "../../Table/table.component";
import TitleContainer from "../../Titles/titleContainer.component";

const CustomAccordionToggle = (props: {eventKey: any; Element: any; callback?: any}) => {
    const {activeEventKey} = useContext(AccordionContext);
    const {Element} = props;
    const handleClick = useAccordionButton(props.eventKey, () => props.callback && props.callback(props.eventKey));
    const isCurrentEventKey = activeEventKey === props.eventKey;
    return <Element handleClick={handleClick} isActive={isCurrentEventKey} />;
};

const PreviousPitchTable = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [meta, setMeta] = useState<any>(null);
    const notify = useNotification();
    const hasFetched = useRef(false);
    const defaultSortType = "desc";
    const defaultOrderBy = "start_date";
    const methods = useForm();
    const search = methods.watch("search");
    const debouncedSearch = useDebounce(search, 500);

    const DateCell = useCallback(tableData => {
        const data = tableData.row.original;
        return <div>{formatDate(data.start_date, DATE_TIME_FULL_FORMAT_NODAY)}</div>;
    }, []);

    const ActionCell = useCallback(tableData => {
        const data = tableData.row.original;
        return (
            <div className="d-flex flex-row justify-content-center align-items-center">
                <Link
                    className="cpBlueBtnThin"
                    target="_blank"
                    to={routeConfig.PitchManagement.path + "?pitch_event_id=" + data.id + "&old=true"}>
                    View
                </Link>
            </div>
        );
    }, []);

    const columns: any[] = [
        {Header: "Name", accessor: "name", sortable: true},
        {Header: "Date & Time", accessor: "start_date", sortable: true, Cell: DateCell},
        {Header: "", accessor: "action-cell", sortable: false, Cell: ActionCell},
    ];

    const getPastPitchEvents = (options?: {order_by: string; sort: string; page?: number; search_word?: string}) => {
        setIsLoading(true);
        eventService.getPastEvents(options ? options : {}, onSuccessGetPastEvents, onError);
    };

    const onSuccessGetPastEvents = (res: AxiosResponse) => {
        setTableData(res.data.data);
        setMeta(res.data.meta);
        setIsLoading(false);
    };

    const onError = (err: AxiosError) => {
        setIsLoading(false);
        notify(getErrorFromArray(err), "Error");
    };

    const handleParamChange = (options: any) => {
        const {sortType, orderBy, page} = options;
        if (!hasFetched.current) return;
        getPastPitchEvents({sort: sortType, order_by: orderBy, page: page || 1, search_word: options.search_word});
    };

    const handleAccordion = (eventKey: any) => {
        if (eventKey === "pastEvents" && !tableData.length && !isLoading) {
            hasFetched.current = true;
            getPastPitchEvents({sort: defaultSortType, order_by: defaultOrderBy});
        }
    };

    return (
        <Accordion className="mt-4" onSelect={handleAccordion} defaultActiveKey="">
            <Accordion.Item eventKey="pastEvents" className="border-0">
                <CustomAccordionToggle
                    eventKey="pastEvents"
                    Element={({handleClick, isActive}) => (
                        <div className="d-flex flex-row align-items-center justify-content-between">
                            <div className="d-flex flex-row align-items-center">
                                <TitleContainer as="h1" position="start" text="Past Events" />
                                <span
                                    onClick={handleClick}
                                    style={{color: "var(--cpBlue)", cursor: "pointer"}}
                                    className="ms-3">
                                    {isActive ? "Hide" : "Show"}
                                </span>
                            </div>
                        </div>
                    )}></CustomAccordionToggle>
                <Accordion.Collapse as="div" eventKey="pastEvents" mountOnEnter className="position-relative">
                    <>
                        <FormProvider {...methods}>
                            <form className="position-absolute" style={{top: -70, right: 20}}>
                                <TextBoxComponent id="search" placeholder="Search" containerClassName="m-0" />
                            </form>
                        </FormProvider>
                        <TableComponent
                            id="previousPitchTable"
                            columns={columns}
                            isLoading={isLoading}
                            customData={tableData}
                            searchWord={debouncedSearch}
                            handleParameterChange={handleParamChange}
                            sortType={defaultSortType}
                            orderBy={defaultOrderBy}
                            meta={meta}
                            paginated
                        />
                    </>
                </Accordion.Collapse>
            </Accordion.Item>
        </Accordion>
    );
};

export default PreviousPitchTable;
