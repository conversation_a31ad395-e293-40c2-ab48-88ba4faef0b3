import {Fragment} from "react";
import {<PERSON>, useNavigate} from "react-router-dom";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IReviewForUserType} from "../../../Interfaces/reviews.interface";
import routeConfig from "../../../constants/routeConfig";
import usePermissions from "../../../hooks/usePermissions";
import {reviewService} from "../../../services/review.service";
import styles from "../../../styles/components/claimerReviewNotifications.module.sass";
import {formatDate} from "../../../utils/formatDate";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import Loader from "../../../utils/loader";
import ButtonComponent from "../../FormControls/button.component";

interface IProps {
    notifications: IReviewForUserType[];
    isLoading: boolean;
}

const ClaimerReviewNotifications = (props: IProps) => {
    const navigate = useNavigate();
    const {hasPermissions} = usePermissions();
    const canReadReviews = hasPermissions([
        PERMISSION_GROUPS.ADMIN_ALL_REVIEWS_READ,
        PERMISSION_GROUPS.PRODUCT_REVIEWS_UPDATE,
    ]);
    const handleClickViewReview = (notification: IReviewForUserType) => {
        reviewService.updateReview(
            {
                id: notification?.id,
                read: true,
            },
            () => null,
            () => null,
        );
        navigate({
            pathname: routeConfig.ProductDetails.path.replace(":friendly_url", notification?.model?.friendly_url || ""),
            search: `?review_id=${notification?.id}`,
        });
    };
    if (props.isLoading) {
        return (
            <div className="d-flex justify-content-center align-items-center w-100 h-100">
                <Loader inline loading big />
            </div>
        );
    }
    return (
        <div className={styles.reviewNotifications}>
            {props.notifications.length ? (
                props.notifications
                    .filter(n => !!n.reviewer)
                    .map((notification: IReviewForUserType, index: number) => {
                        const avatar =
                            notification?.reviewer?.avatar ||
                            createImageFromInitials(50, notification?.reviewer?.name, "", "company");
                        return (
                            <Fragment key={index}>
                                <div
                                    className="row p-2"
                                    style={{backgroundColor: notification?.read ? "transparent" : "var(--cpGrayBg)"}}>
                                    <div
                                        className={`col-12 col-md-1 d-flex justify-content-center ${styles.notificationAvatar}`}>
                                        <img
                                            src={avatar}
                                            alt="avatar"
                                            className="img-fluid rounded-circle"
                                            style={{maxHeight: 50}}
                                        />
                                    </div>
                                    <div className={`col-9 ${styles.notificationContent}`}>
                                        <h3 className={styles.notificationTitle}>
                                            {notification?.reviewer?.is_private ? (
                                                notification.reviewer.name
                                            ) : (
                                                <Link
                                                    to={routeConfig.UserProfile.path.replace(
                                                        ":id",
                                                        notification?.reviewer?.friendly_url,
                                                    )}>
                                                    {notification?.reviewer?.name}
                                                </Link>
                                            )}{" "}
                                            reviewed your product{" "}
                                            <Link
                                                to={routeConfig.ProductDetails.path.replace(
                                                    ":friendly_url",
                                                    notification?.model?.friendly_url || "",
                                                )}>
                                                {notification?.model?.name}
                                            </Link>
                                        </h3>
                                        <p>{formatDate(notification?.created_at, "D")}</p>
                                        <p className="mt-2 mb-4">
                                            View the review now and write a reply as well as verify the reviewer as a
                                            user of the product
                                        </p>
                                        <label>
                                            Email Address:{" "}
                                            <a href={`mailto:${notification?.reviewer?.email}`}>
                                                {notification?.reviewer?.email}
                                            </a>
                                        </label>
                                        <label>
                                            Organization:{" "}
                                            <Link
                                                to={
                                                    notification?.reviewer?.company?.type?.type_is_of_vendor
                                                        ? routeConfig.VendorProfile.path.replace(
                                                              ":id",
                                                              notification?.reviewer?.company?.friendly_url || "",
                                                          )
                                                        : routeConfig.MspProfile.path
                                                              .replace(
                                                                  ":id",
                                                                  notification?.reviewer?.company?.friendly_url || "",
                                                              )
                                                              .replace(":tab", "summary")
                                                }>
                                                {notification?.reviewer?.company?.name}
                                            </Link>
                                        </label>
                                    </div>
                                    <div className="col-12 col-md-2 d-flex justify-content-center align-items-center">
                                        {canReadReviews && (
                                            <ButtonComponent
                                                onClick={() => handleClickViewReview(notification)}
                                                id={`viewReview-${notification?.id}`}
                                                className="cpBlueBtnThin">
                                                View Review
                                            </ButtonComponent>
                                        )}
                                    </div>
                                </div>
                                <hr className="fadedBorder" />
                            </Fragment>
                        );
                    })
            ) : (
                <span>No Product Reviews. Check back later.</span>
            )}
        </div>
    );
};

export default ClaimerReviewNotifications;
