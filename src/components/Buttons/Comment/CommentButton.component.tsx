import React from "react";
import ButtonComponent from "../../FormControls/button.component";

interface IProps {
    btnClassName?: any;
    id: string; // Id of the component
    onClick?: (id: string) => void;
    isCompany?: boolean;
    disabled?: boolean;
    label?: string;
    icon?: any;
    //? Used for data tracking
    subjectId?: string;
    friendly_url?: string;
    contentType?: string;
}

const CommentButton: React.FC<IProps> = (props: IProps) => {
    const {id} = props;

    return (
        <>
            <ButtonComponent
                id={id}
                onClick={props.onClick}
                type="button"
                className={`cpCommentBtn ${props.btnClassName}`}
                data_custom_properties={JSON.stringify({
                    subjectId: props.subjectId,
                    friendly_url: props.friendly_url,
                    contentType: props.contentType,
                })}
                preventDefaultBehavior
                disabled={props.disabled}>
                <div className={"d-flex flex-row justify-content-center align-items-start py-1"}>
                    {props.icon}
                    <span className="ml-1">{props.label}</span>
                </div>
            </ButtonComponent>
        </>
    );
};

CommentButton.defaultProps = {
    label: "Comment",
};

export default CommentButton;
