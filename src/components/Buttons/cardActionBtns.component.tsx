import {IVideos} from "../../Interfaces/profiles.interface";
import ButtonComponent from "../FormControls/button.component";
import {PlusIcon} from "../Icons";
import styles from "../../styles/components/cardActionBtns.module.sass";
import Loader from "../../utils/loader";
import {IBlog} from "../../Interfaces/blog.interface";
import {IMediaGallery} from "../../Interfaces/mediaGallery.interface";
import {IProducts} from "../../Interfaces/products.interface";
import {IShoutOut} from "../../Interfaces/contentFeed.interface";
import {Dropdown} from "react-bootstrap";
import EditOffOutlined from "@mui/icons-material/EditOffOutlined";
import EditOutlined from "@mui/icons-material/EditOutlined";
import CloseIcon from "@mui/icons-material/Close";

interface IProps {
    onEdit?: (id: string) => void;
    onCancelEdit?: () => void;
    onRemove?: (id: string) => void;
    onAddFeature?: (id: string) => void;
    showAddFeature?: boolean;
    info: IVideos | IBlog | IMediaGallery | IProducts | IShoutOut;
    isEditing?: boolean;
    deleting?: boolean;
    editStyle?: React.CSSProperties;
    removeStyle?: React.CSSProperties;
    editClassName?: string;
    removeClassName?: string;
    showButtonInDropdown?: boolean;
}

export default function CardActionBtns(props: IProps) {
    return (
        <>
            {props.showAddFeature && (
                <ButtonComponent
                    onClick={() => {
                        props.onAddFeature && props.onAddFeature(props.info?.id);
                    }}
                    disabled={props.deleting}
                    type="button"
                    id={`addFeature-${props.info?.id}`}
                    preventDefaultBehavior
                    className={styles.addFeatureBtn}>
                    <PlusIcon size={12} className="" />
                </ButtonComponent>
            )}
            {props.showButtonInDropdown ? (
                <Dropdown className={styles.noDropdown}>
                    <Dropdown.Toggle variant="transparent" split={false} className="pt-0 pb-0">
                        <svg width="20" height="20" viewBox="0 0 16 16">
                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" />
                        </svg>
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                        <Dropdown.Item
                            onClick={() => {
                                if (props.isEditing) {
                                    props.onCancelEdit && props.onCancelEdit();
                                } else {
                                    props.onEdit && props.onEdit(props.info?.id);
                                }
                            }}
                            disabled={props.deleting}>
                            Edit
                        </Dropdown.Item>
                        <Dropdown.Item
                            onClick={() => {
                                props.onRemove && props.onRemove(props.info?.id);
                            }}
                            disabled={props.deleting}
                            preventDefaultBehavior>
                            Delete
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            ) : (
                <>
                    <ButtonComponent
                        onClick={() => {
                            if (props.isEditing) {
                                props.onCancelEdit && props.onCancelEdit();
                            } else {
                                props.onEdit && props.onEdit(props.info?.id);
                            }
                        }}
                        disabled={props.deleting}
                        type="button"
                        id={`editCard-${props.info?.id}`}
                        preventDefaultBehavior
                        style={props.editStyle}
                        className={`${styles.editBtn} ${props.editClassName}`}>
                        {props.isEditing ? (
                            <EditOffOutlined sx={{height: 15, width: 15}} />
                        ) : (
                            <EditOutlined sx={{height: 15, width: 15}} />
                        )}
                    </ButtonComponent>
                    <ButtonComponent
                        onClick={() => {
                            props.onRemove && props.onRemove(props.info?.id);
                        }}
                        disabled={props.deleting}
                        type="button"
                        id={`deleteCard-${props.info?.id}`}
                        preventDefaultBehavior
                        style={props.removeStyle}
                        className={`${styles.removeBtn} ${props.removeClassName}`}>
                        {props.deleting ? (
                            <Loader inline loading version="iconWhite" />
                        ) : (
                            <CloseIcon sx={{height: 15, width: 15}} />
                        )}
                    </ButtonComponent>
                </>
            )}
        </>
    );
}
