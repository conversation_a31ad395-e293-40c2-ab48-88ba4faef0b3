import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError} from "axios";
import {useState} from "react";
import {CHANNEL_PROGRAM_COMPANY_ID} from "../../../constants/commonStrings.constant";
import {USERS_TYPE} from "../../../enums/usersTypes.enum";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {usePeople} from "../../../hooks/fetches/usePeople";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import {IAvatar, IFollowers} from "../../../Interfaces/followers.interface";
import {IPeople} from "../../../Interfaces/people.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {UPDATE_AUTH} from "../../../reducers/auth.reducer";
import {followCompanyService} from "../../../services/followCompany.service";
import {followUserService} from "../../../services/followUser.service";
import Button from "../../../uicomponents/Atoms/Button/Button.Component";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import Loader from "../../../utils/loader";
import ButtonComponent from "../../FormControls/button.component";

interface IProps {
    entityId: string;
    profile_type: "user" | "company";
    friendlyUrl: string;
    onHandleSuccess?: Function;
    variant?: "cpBlueAltBtn" | "cpBlueBtn" | "cpMainBtn" | "cpAltBtn" | "textBtn" | "cpBlueBtnThin";
    showLoader?: boolean;
    preventGlobal?: boolean;
    // Pass these if preventing global
    isFollowing?: boolean;
    followersCount?: number;
    followText?: string | any;
    unfollowText?: string | any;
    hideOnFollow?: boolean;
    entityName?: string;
    entityHandle?: string | null;
    useMui?: boolean;
}
export default function FollowBtn(props: IProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [hidden, setHidden] = useState(false);
    const isCompany = props.profile_type === "company";
    const {company, updateCompany} = useCompany(props.friendlyUrl, {
        isCompany,
        calls: {
            company: props.preventGlobal ? false : true,
        },
        companyParams: ["current_user_is_following", "followers_count", "profile_images"],
        refreshAfterMutate: true,
    });
    const {people, updatePeople} = usePeople(props.friendlyUrl, {
        isCompany,
        calls: {
            people: props.preventGlobal ? false : true,
            peopleBlogs: false,
            peopleVideos: false,
            peopleRules: false,
            peopleGalleries: false,
            peopleCounts: false,
            peopleFollowings: false,
        },
        refreshAfterMutate: true,
    });
    const {authState, updateAuthState} = useAuthState();

    const notify = useNotification();
    const theme = useTheme();
    const info = isCompany ? company.data : people.data;
    const isFollowing = props.preventGlobal ? props.isFollowing : info?.current_user_is_following;
    const followersCount = props.preventGlobal ? props.followersCount : info?.followers_count;
    const isChannelProgram = props.entityId === CHANNEL_PROGRAM_COMPANY_ID || info?.id === CHANNEL_PROGRAM_COMPANY_ID;

    const handleError = (error: AxiosError<any>) => {
        notify(error.response?.data.message || error.message, "Error");
    };

    const handleFollow = () => {
        if (!info && !props.preventGlobal) return;
        setIsLoading(true);
        if (props.hideOnFollow) {
            setHidden(true);
        }
        let newFollowersCount: number = followersCount || 0;
        let tempIsFollowing: boolean = isFollowing || false;
        let tempFollowings: IFollowers[] = authState?.following || [];
        const followedImg: IAvatar = {
            src:
                props.profile_type === "user"
                    ? info?.profile_images?.UserAvatar
                        ? info?.profile_images?.UserAvatar![0]?.src!
                        : createImageFromInitials(
                              200,
                              info?.name ||
                                  (info as IPeople)?.first_name + " " + (info as IPeople)?.last_name! ||
                                  "User",
                              "",
                              "user",
                          )
                    : info?.profile_images?.CompanyAvatar
                    ? info?.profile_images?.CompanyAvatar![0]?.src!
                    : createImageFromInitials(200, info?.name || "Company", "", "company"),
            alt:
                props.profile_type === "user"
                    ? info?.profile_images?.UserAvatar
                        ? info?.profile_images?.UserAvatar![0]?.alt!
                        : ""
                    : info?.profile_images?.CompanyAvatar
                    ? info?.profile_images?.CompanyAvatar![0]?.alt!
                    : "",
        };
        if (!isFollowing) {
            newFollowersCount++;
            tempIsFollowing = true;
            tempFollowings.push({
                id: props.preventGlobal ? (props.entityId as string) : (info?.id as string),
                name: info?.name || (info as IPeople)?.first_name + " " + (info as IPeople)?.last_name!,
                handle: info?.handle!,
                friendly_url: props.preventGlobal ? (props.friendlyUrl as string) : (info?.friendly_url as string),
                type:
                    props.profile_type === "company"
                        ? USERS_TYPE.VENDOR
                        : (info as IPeople)?.type === "INFLUENCER"
                        ? USERS_TYPE.INFLUENCER
                        : USERS_TYPE.USER,
                type_is_of_vendor: props.profile_type === "company",
                is_company: props.profile_type === "company",
                avatar: followedImg,
                current_user_is_following: tempIsFollowing,
            });
        } else {
            newFollowersCount--;
            tempIsFollowing = false;
            tempFollowings = tempFollowings.filter(follower => follower.id !== info?.id);
        }
        const followObj = {current_user_is_following: tempIsFollowing, followers_count: newFollowersCount};
        const followerObjUpdate = {followers: tempFollowings};
        if (props.profile_type === "user") {
            if (tempIsFollowing) {
                if (props.hideOnFollow) {
                    updateAuthState(UPDATE_AUTH, {
                        following: [...(authState.following || []), {id: props.entityId}],
                    });
                }

                followUserService.followUser(
                    props.entityId,
                    () => {
                        updateAuthState(UPDATE_AUTH, {
                            following: [
                                ...(authState.following?.filter(f => f.id !== props.entityId) || []),
                                {id: props.entityId},
                            ],
                        });
                        props.onHandleSuccess?.(props.entityId);
                        setIsLoading(false);
                    },
                    handleError,
                );
            } else {
                if (props.hideOnFollow) {
                    updateAuthState(UPDATE_AUTH, {
                        following: [...(authState.following || [])].filter(item => item.id !== props.entityId),
                    });
                }

                followUserService.unFollowUser(
                    props.entityId,
                    () => {
                        updateAuthState(UPDATE_AUTH, {
                            following: [...(authState.following || [])].filter(item => item.id !== props.entityId),
                        });
                        props.onHandleSuccess?.(props.entityId);
                        setIsLoading(false);
                    },
                    handleError,
                );
            }
            return (
                info &&
                updatePeople.mutate({
                    people_id: props.entityId,
                    ...followObj,
                    ...followerObjUpdate,
                    mutate_type: MutateTypes.LocalUpdate,
                })
            );
        }
        if (tempIsFollowing) {
            if (props.hideOnFollow) {
                updateAuthState(UPDATE_AUTH, {
                    following: [...(authState.following || []), {id: props.entityId}],
                });
            }
            followCompanyService.followCompany(
                props.entityId,
                () => {
                    updateAuthState(UPDATE_AUTH, {
                        following: [...(authState.following || []), {id: props.entityId}],
                    });
                    props.onHandleSuccess?.(props.entityId);
                    setIsLoading(false);
                },
                handleError,
            );
        } else {
            if (props.hideOnFollow) {
                updateAuthState(UPDATE_AUTH, {
                    following: [...(authState.following || [])].filter(item => item.id !== props.entityId),
                });
            }
            followCompanyService.unFollowCompany(
                props.entityId,
                () => {
                    updateAuthState(UPDATE_AUTH, {
                        following: [...(authState.following || [])].filter(item => item.id !== props.entityId),
                    });
                    props.onHandleSuccess?.(props.entityId);
                    setIsLoading(false);
                },
                handleError,
            );
        }
        return (
            info &&
            updateCompany.mutate({
                company_id: props.entityId,
                ...followObj,
                ...followerObjUpdate,
                mutate_type: MutateTypes.LocalUpdate,
            })
        );
    };

    if (hidden) return null;
    return (
        <>
            {authState.authenticated && authState.friendly_url !== props.friendlyUrl && !isChannelProgram ? (
                props.useMui ? (
                    <Button
                        onClick={handleFollow}
                        type="button"
                        data_custom_properties={JSON.stringify({
                            following_action: isFollowing ? "Unfollow" : "Follow",
                            following_id: (props.entityId as string) || (info?.id as string),
                            following_name:
                                props.entityName ||
                                info?.name ||
                                (info as IPeople)?.first_name + " " + (info as IPeople)?.last_name!,
                            following_handle: props.entityHandle || info?.handle!,
                            following_friendly_url: props.preventGlobal
                                ? (props.friendlyUrl as string)
                                : (info?.friendly_url as string),
                        })}
                        id={`follow-${props.friendlyUrl}`}
                        variant="text"
                        color={"secondary"}
                        sx={{
                            color: theme.palette.blue[600],
                            fontWeight: "700 !important",
                        }}>
                        {isFollowing || isLoading ? (
                            <CancelOutlinedIcon htmlColor={theme.palette.blue[600]} />
                        ) : (
                            <AddOutlinedIcon htmlColor={theme.palette.blue[600]} />
                        )}
                        {isLoading && props.showLoader ? (
                            <Loader inline loading version="iconWhite" />
                        ) : isFollowing ? (
                            props.unfollowText || "Unfollow"
                        ) : (
                            props.followText || "Follow"
                        )}
                    </Button>
                ) : (
                    <ButtonComponent
                        onClick={handleFollow}
                        type="button"
                        data_custom_properties={JSON.stringify({
                            following_action: isFollowing ? "Unfollow" : "Follow",
                            following_id: (props.entityId as string) || (info?.id as string),
                            following_name:
                                props.entityName ||
                                info?.name ||
                                (info as IPeople)?.first_name + " " + (info as IPeople)?.last_name!,
                            following_handle: props.entityHandle || info?.handle!,
                            following_friendly_url: props.preventGlobal
                                ? (props.friendlyUrl as string)
                                : (info?.friendly_url as string),
                        })}
                        id={`follow-${props.friendlyUrl}`}
                        className={`${props.variant || (isFollowing ? "cpAltBtnThin" : "cpMainBtnThin")}`}>
                        {isLoading && props.showLoader ? (
                            <Loader inline loading version="iconWhite" />
                        ) : isFollowing ? (
                            props.unfollowText || "Unfollow"
                        ) : (
                            props.followText || "Follow"
                        )}
                    </ButtonComponent>
                )
            ) : null}
        </>
    );
}
