import {AxiosError} from "axios";
import {DateTime} from "luxon";
import {useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {PITCH_MINUTES_AFTER_END} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useAppConfig from "../../hooks/useAppConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {IPitchObject} from "../../models/PitchObject.interface";
import {GET_USER_PITCHES, UPDATE_AUTH} from "../../reducers/auth.reducer";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {eventService} from "../../services/event.service";
import {getErrorFromArray} from "../../utils/error.util";
import {SubstractMinutes} from "../../utils/formatDate";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";

interface IProps {
    pitch: IPitchObject;
    disabled?: boolean;
    classes?: string;
    registerUrl?: string;
}

export default function PitchRegisterButton(props: IProps) {
    const [beginning, setBeginning] = useState<boolean>(false);
    const [registering, setRegistering] = useState<boolean>(false);

    const notify = useNotification();
    const {authState, updateAuthState} = useAuthState();
    const {updateSettings} = useSettings();
    const {config, isLoadingConfig} = useAppConfig({config_key: "PITCH_MINUTES_WATCH"});
    const timeBeforePitch = config?.value || "30";
    const navigate = useNavigate();

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const unregisterPitch = (id: string) => {
        if (!authState.id) return;
        setRegistering(true);
        eventService
            .unsuscribeEvent(id, authState.id)
            .then(() => {
                handleSucessUnRegisterPitch(id);
                notify(
                    "You have unregistered successfully. You will no longer receive notifications for this event.",
                    "Success",
                );
            })
            .catch(error => {
                handleError(error);
            });
    };

    const handleSucessUnRegisterPitch = pitchId => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        const newUserPitches = authState?.userPitches?.filter(p => p !== pitchId);
        updateAuthState(UPDATE_AUTH, {userPitches: newUserPitches});
        setRegistering(false);
    };

    const checkLoggedIn = (upcommingPitch: IPitchObject, index: number) => {
        if (authState.authenticated && beginning && index === 0) {
            return navigate({
                pathname: routeConfig.WatchPitch.path,
                search: "?pitch_event_id=" + upcommingPitch.id,
            });
        }
        if (authState.authenticated && authState.userPitches.includes(upcommingPitch.id)) {
            return unregisterPitch(upcommingPitch.id);
        }

        if (authState.authenticated && !authState.userPitches.includes(upcommingPitch.id)) {
            return navigate({
                pathname: routeConfig.Attend.path,
                search: "?id=" + upcommingPitch.id,
            });
        }
        navigate(props.registerUrl || routeConfig.Register.path + "?pitch_event_id=" + upcommingPitch.id);
    };

    const displayWatchButton = (upcomingPitch: IPitchObject) => {
        let nextBeginning: boolean = false;
        let endDate: string;
        if (upcomingPitch.end_date) {
            endDate =
                DateTime.fromISO(upcomingPitch.end_date)
                    .plus({minutes: parseInt(PITCH_MINUTES_AFTER_END!)})
                    .toISO() ?? "";
        } else {
            endDate = DateTime.fromISO(upcomingPitch.start_date).plus({hours: 5}).toISO() ?? "";
        }
        const startDate = SubstractMinutes(upcomingPitch.start_date, parseInt(timeBeforePitch));
        if (DateTime.now() >= startDate && DateTime.now() <= DateTime.fromISO(endDate)) {
            nextBeginning = true;
        }
        setBeginning(nextBeginning);
    };

    useEffect(() => {
        if (props.pitch && props.pitch.id && timeBeforePitch && !isLoadingConfig) {
            displayWatchButton(props.pitch!);
        }
        // eslint-disable-next-line
    }, [props.pitch?.start_date, timeBeforePitch]);

    useEffect(() => {
        if (!authState.userPitches.length) {
            updateAuthState(GET_USER_PITCHES, {});
        }
        //eslint-disable-next-line
    }, []);

    return (
        <ButtonComponent
            className={`cpMainBtnThin ${props.classes}`}
            type="button"
            id="pitchAction"
            disabled={registering || props.disabled}
            onClick={() => checkLoggedIn(props.pitch!, 0)}>
            {beginning
                ? "Watch Now"
                : authState.userPitches.includes(props.pitch?.id!)
                ? "Unregister"
                : "Register now!"}
            {registering && <Loader inline loading version="iconWhite" />}
        </ButtonComponent>
    );
}
