import {useQueryClient} from "@tanstack/react-query";
import ButtonComponent from "../FormControls/button.component";
import ReactIcon from "../Icons/reactIcon.component";

interface IProps {
    id: string;
    queryKey: any[] | readonly unknown[];
    disabled?: boolean;
}

export default function RefreshTableDataBtn(props: IProps) {
    const queryClient = useQueryClient();
    return (
        <>
            <ButtonComponent
                id={props.id}
                onClick={() => queryClient.refetchQueries(props.queryKey)}
                disabled={props.disabled}
                tooltip="Refresh table data"
                className="cpBlueAltBtnThin blueColor">
                <ReactIcon iconName="FiRefreshCw" size="18px" style={{height: "20px"}} className="rotateHover" />
            </ButtonComponent>
        </>
    );
}
