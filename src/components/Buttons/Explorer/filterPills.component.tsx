import {Reducer, useReducer} from "react";
import Loader from "../../../utils/loader";
import ButtonComponent from "../../FormControls/button.component";
import styles from "../../../styles/components/filterPill.module.sass";
import {getContrastColor} from "../../../utils/color.util";
import {IFilter} from "../../../Interfaces/filters.interface";

interface IProps {
    filters: IFilter[];
    onSelect: Function;
    showAll?: boolean;
    selected?: IFilter;
    onSelectAll?: Function;
    style?: React.CSSProperties;
}

interface IHover {
    [id: string]: boolean;
}

interface IAction {
    type: string;
    payload: string;
}

const hoverReducer: Reducer<IHover, IAction> = (state, action) => {
    switch (action.type) {
        case "SET_HOVER":
            return {...state, [action.payload]: true};
        case "CLEAR_HOVER":
            return {...state, [action.payload]: false};
        default:
            return state;
    }
};

export default function FilterPills(props: IProps) {
    const [hover, hoverDispatch] = useReducer<Reducer<IHover, IAction>>(hoverReducer, {});

    const styleOverride = (filter: IFilter) => {
        const isSelected: boolean = props.selected?.id === filter.id;
        const backgroundColor: string =
            isSelected || hover?.[filter.id]
                ? filter.color === "#000000"
                    ? "#FF6120"
                    : filter.color || "#FF6120"
                : "#fff";
        const color: string = getContrastColor(backgroundColor);
        const fontWeight: string = isSelected ? "600" : "400";
        const border: string =
            isSelected || hover?.[filter.id]
                ? `1px solid ${filter.color === "#000000" ? "#FF6120" : filter.color || "#FF6120"}`
                : "1px solid #222";
        const style: React.CSSProperties = {
            backgroundColor,
            color,
            fontWeight,
            border,
        };
        return style;
    };

    return (
        <div className={styles.container} style={props.style}>
            {props.filters?.length ? (
                <>
                    {props.showAll && (
                        <ButtonComponent
                            id="all-filter-select"
                            className={props.selected?.id ? styles.allCategory : styles.allCategorySelected}
                            onClick={() => props.onSelectAll && props.onSelectAll()}>
                            All
                        </ButtonComponent>
                    )}
                    {props.filters?.map(filter => {
                        return (
                            <button
                                key={filter.id}
                                id={`filter-select-${filter.id}`}
                                type="button"
                                className={styles.pill}
                                style={styleOverride(filter)}
                                data-custom-properties={JSON.stringify(filter.data_custom_properties || {})}
                                onMouseEnter={() => hoverDispatch({type: "SET_HOVER", payload: filter.id})}
                                onMouseLeave={() => hoverDispatch({type: "CLEAR_HOVER", payload: filter.id})}
                                onClick={() => props.onSelect(filter)}>
                                {filter.name}
                            </button>
                        );
                    })}
                </>
            ) : (
                <div className="d-flex w-100 justify-content-center align-items-center h-100">
                    <Loader inline loading />
                </div>
            )}
        </div>
    );
}
