import {IVideos} from "../../Interfaces/profiles.interface";
import VideoCard from "../Profile/Videos/videoCard.component";
import {ModalComponent} from "./modal.component";
import styles from "../../styles/components/videoSelectionModal.module.sass";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import {useCompany} from "../../hooks/fetches/useCompany";
import {usePeople} from "../../hooks/fetches/usePeople";

interface IProps {
    entityVideos: IVideos[];
    isCompany: boolean;
    entityId: string;
    friendly_url: string;
    handleAddVideo: Function;
    showVideosModal: boolean;
    setShowVideosModal: Function;
    handleVideoSelection: Function;
    handleAddNewVideo: Function;
}

export default function VideoSelectionModal(props: IProps) {
    const {companyVideos} = useCompany(props.friendly_url, {isCompany: props.isCompany});
    const {peopleVideos} = usePeople(props.friendly_url, {isCompany: props.isCompany});

    const videos = props.isCompany ? companyVideos.data?.data : peopleVideos.data?.data;
    const isLoading = props.isCompany ? companyVideos.isLoading : peopleVideos.isLoading;

    return (
        <>
            <ModalComponent
                isStatic
                showHeader
                headerTitle={`Select one of your${props.isCompany ? " company's" : ""} videos`}
                onCancel={() => props.setShowVideosModal(false)}
                onSuccess={props.handleAddVideo}
                modalSwitcher={props.showVideosModal}
                modalSwitcherCallback={props.setShowVideosModal}
                form={
                    <>
                        <div className="d-flex gap-3 flex-wrap">
                            {videos?.length ? (
                                videos?.map(video => {
                                    return (
                                        <div
                                            className={styles.videoContainer}
                                            key={video.id}
                                            onClick={() => props.handleVideoSelection(video.url)}>
                                            <VideoCard
                                                video={video}
                                                className="pe-none"
                                                ownerType={props.isCompany ? "company" : "user"}
                                                friendlyUrl={props.friendly_url}
                                            />
                                        </div>
                                    );
                                })
                            ) : (
                                <div className="d-flex justify-content-center">
                                    {" "}
                                    {isLoading ? <Loader inline loading /> : "No videos found"}
                                </div>
                            )}
                        </div>
                        <div className="d-flex justify-content-center gap-3 mt-5 mb-3">
                            <ButtonComponent
                                id="cancelButton"
                                type="button"
                                onClick={() => props.setShowVideosModal(false)}
                                className="cpAltBtnThin">
                                Cancel
                            </ButtonComponent>
                            <ButtonComponent
                                id="addVideoTrigger"
                                type="button"
                                onClick={props.handleAddNewVideo}
                                className="cpMainBtnThin">
                                Add new video
                            </ButtonComponent>
                        </div>
                    </>
                }
            />
        </>
    );
}
