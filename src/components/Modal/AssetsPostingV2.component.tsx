import {AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useLocation} from "react-router-dom";
import {IAddAssetsPostData, IAddAssetsPostForm, SubjectTypes} from "../../Interfaces/assets.interface";
import {IMediaItem} from "../../Interfaces/mediaGallery.interface";
import {IPartnerSection, TPartnerPageLayouts} from "../../Interfaces/partnerPage.interface";
import {MediaGalleryItem, MutateTypes} from "../../Interfaces/queries.interface";
import {CHANNEL_PROGRAM_COMPANY_ID, maxImageUploadCount} from "../../constants/commonStrings.constant";
import {PORTAL_VIBILITY_GROUP_STRINGS} from "../../constants/visibilityLevels.constant";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import {useCompany} from "../../hooks/fetches/useCompany";
import {usePartnerPage} from "../../hooks/fetches/usePartnerPage";
import {usePeople} from "../../hooks/fetches/usePeople";
import useNotification from "../../hooks/useNotification";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import {CONTENT_SUBJECT_TYPES} from "../../constants/subjectTypes.constant";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import MuiUpload from "../../uicomponents/Molecules/MuiUpload/MuiUpload.component";
import RTE from "../../uicomponents/Atoms/RTE/rte.component";
import TagsAutocomplete from "../../uicomponents/Molecules/TagsAutocomplete/TagsAutocomplete.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import AddIcon from "@mui/icons-material/Add";
import PortalVisibilityRadios from "../../uicomponents/Molecules/PortalVisibilityRadios/PortalVisibilityRadios.component";
import {useSubdomain} from "../../hooks/useSubdomain";
import Skeleton from "@mui/material/Skeleton";

interface IProps<T extends TPartnerPageLayouts> {
    onClose: Function;
    isCompany: boolean;
    assetToEdit?: any;
    friendlyUrl?: string;
    partnerSectionData?: IPartnerSection<T>[T];
    onEditSuccess?: Function;
    contentData?: any;
    companyId?: string;
    prmSearchKey?: string;
    //? Dialog props
    open?: boolean;
    onSuccess?: Function;
    defaultPortalVisibility?: PORTAL_VIBILITY_GROUP_STRINGS;
}

export default function AssetPostingV2<T extends TPartnerPageLayouts>(props: IProps<T>) {
    const [files, setFiles] = useState<any[]>([]);
    const [removedImagesArray, setRemovedImagesArray] = useState<MediaGalleryItem[]>([]);
    const subdomainObj = useSubdomain();
    const subdomain = subdomainObj.company;
    const location = useLocation();
    const friendly_url = props.friendlyUrl || useGetFriendlyUrl(location.pathname);
    const {
        company,
        companyGalleries,
        updateCompany,
        isUpdating: isUpdatingCompany,
    } = useCompany(friendly_url, {
        isCompany: props.isCompany,
        calls: {
            all: false,
            company: true,
            companyGalleries: true,
        },
    });
    const {
        people,
        peopleGalleries,
        updatePeople,
        isUpdating: isUpdatingPeople,
    } = usePeople(friendly_url, {
        isCompany: props.isCompany,
        calls: {
            all: false,
            people: true,
            peopleGalleries: true,
        },
    });
    const {updatePartnerPage} = usePartnerPage(company?.data?.subdomain!, {
        isCompany: props.isCompany,
        searchType: props?.prmSearchKey,
        refreshAfterMutate: true,
        sectionId: props.partnerSectionData?.id,
        calls: {all: false, partnerPage: true, searchPartnerData: false, partnerPageData: true},
    });
    const notify = useNotification();
    let assetToEdit = props.isCompany
        ? companyGalleries.data?.find(b => b.id === props?.assetToEdit)
        : peopleGalleries.data?.find(b => b.id === props?.assetToEdit);
    const imagesRef = useRef<IMediaItem[]>([]);
    const filesRef = useRef<File[]>([]);
    if (props?.contentData && props.contentData?.length) {
        assetToEdit = props.contentData.find(b => b.id === props?.assetToEdit);
    }
    const assetMethods = useForm<IAddAssetsPostForm>({
        defaultValues: {
            title: assetToEdit?.custom_properties?.title || "",
            description: assetToEdit?.custom_properties?.description || "",
            tags: assetToEdit?.custom_properties?.tags || [],
        },
    });
    const MAX_IMAGE_COUNT = maxImageUploadCount;
    const subject_type = props.isCompany ? SubjectTypes.CompanyProfile : SubjectTypes.UserProfile;
    const subject_id = props?.partnerSectionData?.id
        ? props.companyId
        : props.isCompany
        ? company.data?.id
        : people.data?.id;
    const entityInfo = props.isCompany ? company.data : people.data;
    const isChannelProgram =
        props?.companyId === CHANNEL_PROGRAM_COMPANY_ID || company?.data?.id === CHANNEL_PROGRAM_COMPANY_ID;
    const visibilityTypeWatcher = assetMethods.watch("visibilityType");
    const isUpdating = props.isCompany
        ? isUpdatingCompany?.[MutateTypes.AddAssets] || isUpdatingCompany?.[MutateTypes.EditGallery]
        : isUpdatingPeople?.[MutateTypes.AddAssets] || isUpdatingPeople?.[MutateTypes.EditGallery];
    const isUploadingNewImages =
        isUpdatingCompany?.[MutateTypes.AddGalleryImgs] || isUpdatingPeople?.[MutateTypes.AddGalleryImgs];
    const isLoading = props.isCompany
        ? company.isLoading || companyGalleries.isLoading
        : people.isLoading || peopleGalleries.isLoading;

    const handleSuccessPost = (res: AxiosResponse) => {
        if (props.partnerSectionData?.id) {
            if (!props.assetToEdit) {
                updatePartnerPage.mutate({
                    subject_id: res?.data?.id,
                    mutate_type: MutateTypes.AddSectionData,
                    id: props?.partnerSectionData?.partner_page_id,
                    section_id: props?.partnerSectionData.id,
                    subject_type: CONTENT_SUBJECT_TYPES.MEDIA_GALLERY,
                });
            } else {
                //callback function
                props?.onEditSuccess!(res);
            }
        }

        props.onClose();
    };

    const handleClickSave = async () => {
        const passedValidation = await assetMethods.trigger();
        if (passedValidation) handleSubmit(assetMethods.getValues());
    };

    const handleSubmit = async (data: IAddAssetsPostForm) => {
        if (assetToEdit) {
            notify("Changes will be saved shortly.", "Success");
            if (removedImagesArray?.length) {
                removedImagesArray?.forEach((img: MediaGalleryItem) => {
                    if (props.isCompany) {
                        updateCompany.mutate({
                            gallery_id: assetToEdit?.id,
                            gallery_media_id: img.id,
                            mutate_type: MutateTypes.DelGalleryMedia,
                            onSuccess: () => removeFromArray(img),
                        });
                    } else {
                        updatePeople.mutate({
                            gallery_id: assetToEdit?.id,
                            gallery_media_id: img.id,
                            mutate_type: MutateTypes.DelGalleryMedia,
                            onSuccess: () => removeFromArray(img),
                        });
                    }
                });
            }

            if (assetToEdit && filesRef.current.length > 0) {
                if (props.isCompany) {
                    updateCompany.mutate({
                        gallery_id: assetToEdit.id,
                        gallery_new_images: filesRef.current,
                        onSuccess: handleSuccessAddImages,
                        mutate_type: MutateTypes.AddGalleryImgs,
                    });
                }
                updatePeople.mutate({
                    gallery_id: assetToEdit.id,
                    gallery_new_images: filesRef.current,
                    onSuccess: handleSuccessAddImages,
                    mutate_type: MutateTypes.AddGalleryImgs,
                });
            }

            const assetPostData: any = {
                ...data,
                description: data.description?.trim(),
                tags: data["tags"]?.map(t => (typeof t === "string" ? t : t.id)) || [],
                replace_tags: true,
                is_partner_content: assetToEdit.custom_properties?.is_partner_content && !!props.partnerSectionData?.id,
            };
            if (assetPostData && assetPostData.is_partner_content) {
                assetPostData["media_visibility"] = visibilityTypeWatcher;
            }
            if (props.isCompany) {
                return updateCompany.mutate({
                    gallery_id: assetToEdit.id,
                    edit_gallery: assetPostData,
                    onSuccess: handleSuccessPost,
                    mutate_type: MutateTypes.EditGallery,
                });
            }
            return updatePeople.mutate({
                gallery_id: assetToEdit.id,
                edit_gallery: assetPostData,
                onSuccess: handleSuccessPost,
                mutate_type: MutateTypes.EditGallery,
            });
        }
        const assetPostData: IAddAssetsPostData = {
            ...data,
            tags: data.tags?.map(t => (typeof t === "string" ? t : t.id)) || [],
            images: filesRef.current,
            subject_id: subject_id!,
            subject_type,
            is_partner_content: !!props.partnerSectionData?.id,
        };
        if (assetPostData && assetPostData.is_partner_content) {
            assetPostData["media_visibility"] = visibilityTypeWatcher;
        }

        if (props.isCompany) {
            updateCompany.mutate({
                assets: assetPostData,
                entity: entityInfo,
                mutate_type: MutateTypes.AddAssets,
                onSuccess: handleSuccessPost,
            });
        } else {
            updatePeople.mutate({
                assets: assetPostData,
                entity: entityInfo,
                mutate_type: MutateTypes.AddAssets,
                onSuccess: handleSuccessPost,
            });
        }

        return;
    };

    const handleSuccessAddImages = (res: AxiosResponse) => {
        if (props.partnerSectionData?.id) {
            if (!props.assetToEdit) {
                updatePartnerPage.mutate({
                    subject_id: res?.data?.id,
                    mutate_type: MutateTypes.AddSectionData,
                    id: props?.partnerSectionData?.partner_page_id,
                    section_id: props?.partnerSectionData.id,
                    subject_type: "media-gallery",
                });
            } else {
                //callback function
                props?.onEditSuccess!(res);
            }
        }
        props.onClose();
        setFiles([...files, ...res?.data]);
        filesRef.current = [];
    };

    const removeFromArray = (img: any) => {
        imagesRef.current = imagesRef.current.filter(image => image.id !== img.id);
        filesRef.current = filesRef.current.filter(file => file.name !== img.name);
        if (props.assetToEdit?.id) return;
        setFiles(files.filter(image => image.id !== img.id));
    };

    useEffect(() => {
        if (props.assetToEdit && assetToEdit) {
            if (isChannelProgram) {
                if (assetToEdit?.custom_properties?.media_visibility)
                    assetMethods.setValue("visibilityType", assetToEdit.custom_properties.media_visibility);
                else assetMethods.setValue("visibilityType", "all");
            }
            if (assetToEdit.media?.length) {
                const media = assetToEdit.media;
                setFiles(media);
            }
            assetMethods.setValue("title", assetToEdit?.custom_properties?.title || "");
            assetMethods.setValue("description", assetToEdit?.custom_properties?.description || "");
            assetMethods.setValue("tags", assetToEdit?.custom_properties?.tags || []);
        }
    }, [assetToEdit]);

    useEffect(() => {
        if (!assetToEdit && !!subdomain) {
            assetMethods.setValue("visibilityType", props.defaultPortalVisibility || "all");
        }
        if (isChannelProgram && assetToEdit) {
            if (assetToEdit?.custom_properties?.media_visibility)
                assetMethods.setValue("visibilityType", assetToEdit.custom_properties.media_visibility);
            else assetMethods.setValue("visibilityType", "all");
        }
    }, []);

    return (
        <>
            <ModalComponent
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
                }}
                open={!!props.open}
                onClose={() => props.onClose?.(null)}
                title={`${props.assetToEdit ? "Edit" : "Add"} Vendor Asset`}
                icon={<AddIcon />}
                justifyButtons="flex-start"
                content={
                    <Box display="flex" flexDirection="column" gap={2}>
                        <FormProvider {...assetMethods}>
                            <form
                                onSubmit={assetMethods.handleSubmit(handleSubmit)}
                                style={{gap: 16, display: "flex", flexDirection: "column"}}>
                                <TextBoxComponent
                                    id="title"
                                    placeholder="Choose a name"
                                    label="Asset Title"
                                    isRequired
                                    containerSx={{width: "100%"}}
                                    disabled={isLoading}
                                />
                                {isLoading ? (
                                    <Skeleton height={200} width="100%" />
                                ) : (
                                    <MuiUpload
                                        id="asset-uploader"
                                        isMultiple
                                        type={MediaTypes.Image}
                                        maxSize={"5"}
                                        variant="sideBySide"
                                        customRecommendations={{
                                            top: [`Up to ${MAX_IMAGE_COUNT} images allowed`],
                                        }}
                                        files={files}
                                        setFile={fileOrFiles => {
                                            const isArrayOfFiles = Array.isArray(fileOrFiles);
                                            const newFiles = isArrayOfFiles
                                                ? [...files, ...fileOrFiles]
                                                : [...files, fileOrFiles];
                                            setFiles(newFiles);
                                            filesRef.current = !!assetToEdit
                                                ? [...(isArrayOfFiles ? fileOrFiles : [fileOrFiles])]
                                                : newFiles;
                                        }}
                                        handleRemoveFile={(fileIndex, f) => {
                                            if (assetToEdit) {
                                                const imgToRemove = files[fileIndex];
                                                setRemovedImagesArray([...removedImagesArray, imgToRemove]);
                                            }
                                            const filtered = files.filter((_, index) => index !== fileIndex);
                                            setFiles(filtered);
                                            filesRef.current = !!assetToEdit
                                                ? filesRef.current.filter(file => file !== f)
                                                : filtered;
                                        }}
                                        maxFiles={MAX_IMAGE_COUNT}
                                        uploadSx={{minHeight: 200}}
                                    />
                                )}
                                <Typography variant="body2" fontInter fontWeight={600}>
                                    Asset Description
                                </Typography>
                                <RTE
                                    id="description"
                                    isRequired
                                    placeholder="Describe your image gallery"
                                    editorContainerSx={{marginBottom: "0px !important"}}
                                    defaultValue={assetToEdit?.custom_properties?.description || ""}
                                    isDisabled={isLoading}
                                    key={assetToEdit?.id}
                                />
                                <TagsAutocomplete
                                    id="tags"
                                    multiple
                                    filterSelectedOptions
                                    maxTags={3}
                                    validateOnTheFly
                                    label="Tags*"
                                    validationSchema={v => Array.isArray(v) && !!v.length}
                                    helperText={`Select up to ${3} tags`}
                                    disabled={isLoading}
                                />
                                {!!subdomain && <PortalVisibilityRadios isChannelProgram={isChannelProgram} />}
                            </form>
                        </FormProvider>
                    </Box>
                }
                okButtonText="SAVE"
                onSuccess={handleClickSave}
                loading={isUpdating || isUploadingNewImages}
                okButtonDisabled={props?.partnerSectionData?.id ? false : isUpdating || isLoading || !subject_id}
            />
        </>
    );
}
