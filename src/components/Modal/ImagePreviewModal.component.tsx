import DOMPurify from "dompurify";
import React, {useEffect} from "react";
import styles from "../../styles/components/imagePreviewModal.module.sass";
import ErrorBoundary from "../../utils/errorBoundary.util";
import {ChevronIcon} from "../Icons/chevron.icon";
import {ModalComponent} from "./modal.component";

interface IImageType {
    url: string;
    title?: string;
    description?: string;
}

interface IProps {
    show: boolean;
    setShow: Function;
    images: Array<IImageType | File>;
    selectedImage: {url: string} | File | undefined;
}

const ImagePreviewModal: React.FC<IProps> = (props: IProps) => {
    const {show, setShow} = props;
    const [imageToShow, setImageToShow] = React.useState<IImageType | File | null>(props.selectedImage || null);

    const handleModalSwitch = async (shouldOpen: boolean) => {
        if (shouldOpen) {
            setShow(true);
        } else {
            setShow(false);
        }
    };

    const navigate = (direction: "prev" | "next") => {
        const foundIndex = props.images?.findIndex(image => {
            if (image instanceof File) {
                return image.name === (imageToShow as File)?.name;
            } else {
                return image.url === (imageToShow as any)?.url;
            }
        });
        if (foundIndex !== -1) {
            let newIndex = foundIndex + (direction === "prev" ? -1 : 1);
            if (newIndex >= props.images.length && direction === "next") {
                newIndex = 0;
            } else if (newIndex < 0 && direction === "prev") {
                newIndex = props.images.length - 1;
            }
            if (newIndex >= 0 && newIndex < props.images.length) {
                setImageToShow(props.images[newIndex]);
            }
        }
    };

    useEffect(() => {
        if (props.selectedImage) {
            setImageToShow(props.selectedImage);
        }
    }, [props.selectedImage]);

    return (
        <ErrorBoundary>
            <ModalComponent
                isStatic
                modalSwitcher={show}
                modalSwitcherCallback={handleModalSwitch}
                showHeader={false}
                contentClassName={`${styles.imagePreviewModalContent}`}
                modalBodyClassName={styles.imagePreviewModalBody}
                form={
                    <div className={styles.imagePreviewModal}>
                        <div className={styles.imagePreviewImageSection}>
                            <ChevronIcon
                                direction="left"
                                size={50}
                                onClick={() => navigate("prev")}
                                className={styles.navigateBtnLeft}
                            />
                            <div className={styles.imageContainer}>
                                <img
                                    src={
                                        imageToShow instanceof File
                                            ? URL.createObjectURL(imageToShow)
                                            : imageToShow?.url || ""
                                    }
                                    alt=""
                                />
                            </div>
                            <ChevronIcon
                                direction="right"
                                size={50}
                                onClick={() => navigate("next")}
                                className={styles.navigateBtnRight}
                            />
                        </div>
                        {!(imageToShow instanceof File) && (
                            <div className={styles.imagePreviewMetaSection}>
                                <h2>{imageToShow?.title}</h2>
                                <p
                                    dangerouslySetInnerHTML={{
                                        __html: DOMPurify.sanitize(imageToShow?.description ?? ""),
                                    }}></p>
                            </div>
                        )}
                    </div>
                }
            />
        </ErrorBoundary>
    );
};

export default ImagePreviewModal;
