import {useQueryClient} from "@tanstack/react-query";
import {useEffect, useState} from "react";
import {useLocation, useNavigate, useSearchParams} from "react-router-dom";
import {ModalComponent} from ".";
import {ICompany} from "../../Interfaces/company.interface";
import {IPeople} from "../../Interfaces/people.interface";
import routeConfig from "../../constants/routeConfig";
import {IClaimedCompanies} from "../../contexts/auth.context";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import useAppConfig from "../../hooks/useAppConfig";
import useSettings from "../../hooks/useSettings";
import styles from "../../styles/components/addContentModal.module.sass";
import {TDynamicCompany} from "../../utils/company.util";
import ErrorBoundary from "../../utils/errorBoundary.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import CreateContent from "../Content/createContent.component";
import NewFileDialog from "../../uicomponents/Organism/NewFileDialog/NewFileDialog.component";
import AssetPostingV2 from "./AssetsPostingV2.component";
import {removeQueryParams} from "../../utils/url.util";

type TModalKeys = "video" | "document" | "blog" | "album";
type TCompany = TDynamicCompany<ICompany, ["profile_images"]>;

interface IProps {
    show?: boolean;
    setShow?: (show: boolean) => void;
    showAsModal?: boolean;
    entity?: TCompany | IPeople | IClaimedCompanies;
    onAddBlogPost?: Function;
    canSwitchProfile?: boolean;
    onSwitchProfile?: Function;
    isCompany?: boolean;
    defaultShoutOutInput?: string;
}

const AddContentModal = (props: IProps) => {
    const {setShow, entity, isCompany} = props;
    const [params] = useSearchParams();
    const [innerShow, setInnerShow] = useState<boolean>(true);
    const addNew = params?.get("add") || "";
    const [showVideoModal, setShowVideoModal] = useState<boolean>(false);
    const [showNewDocumentModal, setShowNewDocumentModal] = useState<boolean>(false);
    const [showNewAlbumModal, setShowNewAlbumModal] = useState<boolean>(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const queryClient = useQueryClient();
    const location = useLocation();
    const navigate = useNavigate();
    const {config} = useAppConfig({config_key: "MAX_VIDEO_SIZE_MB"});
    const friendly_url = entity?.friendly_url || useGetFriendlyUrl(location.pathname);
    const {settings} = useSettings();
    const isInfluencer = (entity as IPeople)?.profile_type === "INFLUENCER";

    const profileImgs = entity?.hasOwnProperty("profile_images")
        ? (entity as TCompany | IPeople)?.profile_images
        : (entity as IClaimedCompanies)?.avatar;
    const profilePic =
        typeof profileImgs === "string"
            ? profileImgs
            : profileImgs?.CompanyAvatar?.[0]?.src ||
              profileImgs?.UserAvatar?.[0]?.src ||
              createImageFromInitials(200, entity?.name || "User", "", isCompany ? "company" : "user");

    const handleOpenContent = (modal: TModalKeys) => {
        setInnerShow(false);
        setTimeout(() => {
            if (modal === "video") return setShowVideoModal(true);
            else if (modal === "document") return setShowNewDocumentModal(true);
            else if (modal === "blog" && props.onAddBlogPost) return props.onAddBlogPost(true);
            else if (modal === "album") return setShowNewAlbumModal(true);
            return props.onAddBlogPost && props.onAddBlogPost(false);
        }, 100);
    };

    const handleSecondaryModalClose = () => {
        navigate(removeQueryParams(location.search, ["add"]), {replace: true});
        if (showVideoModal) setShowVideoModal(false);
        if (showNewDocumentModal) setShowNewDocumentModal(false);
        if (showNewAlbumModal) setShowNewAlbumModal(false);
        if (props.isCompany) {
            queryClient.refetchQueries(["company - galleries", props?.entity?.friendly_url]);
        }
        const isProfiles =
            location.pathname.includes("/v/") || location.pathname.includes("/u/") || location.pathname.includes("/c/");
        if (props.entity?.friendly_url && !isProfiles && props.showAsModal) {
            navigate(
                props.isCompany
                    ? routeConfig.VendorProfile.path.replace(":id", props.entity?.friendly_url) + "?tab=content"
                    : routeConfig.UserProfile.path.replace(":id", props.entity?.friendly_url) + "?tab=content",
            );
        }
        setShow && setShow(false);
    };

    useEffect(() => {
        if (addNew === "video") setShowVideoModal(true);
        else if (addNew === "document") setShowNewDocumentModal(true);
        else if (addNew === "album") setShowNewAlbumModal(true);
    }, [addNew]);

    return (
        <ErrorBoundary>
            {innerShow &&
                !showNewAlbumModal &&
                !showNewDocumentModal &&
                !showVideoModal &&
                (props.showAsModal ? (
                    <ModalComponent
                        isStatic
                        modalSwitcher={innerShow}
                        modalSwitcherCallback={setInnerShow}
                        showHeader={false}
                        contentClassName={styles.addContentModalContainer}
                        modalBodyClassName={styles.addContentModalBody}
                        hideCloseBtn
                        form={
                            <CreateContent
                                profilePic={profilePic}
                                setShow={setShow}
                                entity={entity}
                                isCompany={props.isCompany}
                                handleOpenContent={handleOpenContent}
                                isInfluencer={isInfluencer}
                                noEmojiSearch
                            />
                        }
                        onCancel={() => setShow?.(false)}
                        onSuccess={() => null}
                    />
                ) : (
                    <CreateContent
                        profilePic={profilePic}
                        canSwitchProfile={props.canSwitchProfile}
                        onSwitchProfile={props.onSwitchProfile}
                        setShow={setShow}
                        hideCloseBtn
                        entity={entity}
                        isCompany={props.isCompany}
                        handleOpenContent={handleOpenContent}
                        isInfluencer={isInfluencer}
                        noEmojiSearch
                    />
                ))}
            {(showNewDocumentModal || showVideoModal) && !showNewAlbumModal && (
                <NewFileDialog
                    entityId={entity?.id || ""}
                    onClose={handleSecondaryModalClose}
                    categories={settings.all_categories}
                    maxSize={showVideoModal ? (config ? config?.value : "500") : "5"}
                    type={showVideoModal ? MediaTypes.Video : MediaTypes.Document}
                    hasDescription
                    profile_type={isCompany ? "company" : "user"}
                    friendlyUrl={friendly_url}
                    entity={entity}
                    useRte={showVideoModal}
                />
            )}
            {showNewAlbumModal && <AssetPostingV2 open onClose={handleSecondaryModalClose} isCompany={!!isCompany} />}
        </ErrorBoundary>
    );
};

export default AddContentModal;
