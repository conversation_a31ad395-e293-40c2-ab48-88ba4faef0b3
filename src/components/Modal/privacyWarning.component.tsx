import {AxiosError} from "axios";
import {useState} from "react";
import {ModalComponent} from ".";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import {profileService} from "../../services/profile.service";
import {getErrorFromArray} from "../../utils/error.util";

interface IProps {
    privWarning: boolean;
    setPrivWarning: (privWarning: boolean) => void;
}

export default function PrivacyWarning(props: IProps) {
    const [updatingPrivacy, setUpdatingPrivacy] = useState(false);

    const {updateAuthState, authState} = useAuthState();
    const notify = useNotification();

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        props.setPrivWarning(false);
    };

    const handleTogglePrivacy = () => {
        setUpdatingPrivacy(true);
        profileService.updateUser({id: authState.id, is_private: false}, () => handleSuccessPrivChange(), handleError);
    };

    const handleSuccessPrivChange = () => {
        updateAuthState(UPDATE_AUTH, {is_private: false});
        setUpdatingPrivacy(false);
        props.setPrivWarning(false);
        return notify("Your content and profile information is now public and searchable.", "Success");
    };

    return (
        <ModalComponent
            modalSwitcher={props.privWarning}
            modalSwitcherCallback={props.setPrivWarning}
            showHeader
            headerTitle="Private profile reminder"
            headerPosition="center"
            headerSeparator
            form={
                <div>
                    <p className="my-4 text-center">
                        Your profile is currently set to "Private". Your videos and profile are not viewable or
                        searchable.
                    </p>
                    <p className="my-4 text-center">
                        Switching to "Public" will allow your profile and videos to be viewable and found in searches.
                    </p>
                </div>
            }
            showCancelButton
            cancelButtonText="Remain Private"
            onCancel={() => props.setPrivWarning(false)}
            showOkButton
            okButtonText={updatingPrivacy ? "Switching..." : "Switch to Public"}
            onSuccess={() => handleTogglePrivacy()}
        />
    );
}
