import {AxiosResponse} from "axios";
import {lazy, Suspense, useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useLocation, useNavigate} from "react-router-dom";
import {CHANNEL_PROGRAM_COMPANY_ID, SUBCATEGORY_TEXT} from "../../constants/commonStrings.constant";
import {VISIBILITY_OPTIONS} from "../../constants/visibilityLevels.constant";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import {VENDOR_SECTION_TYPES} from "../../enums/vendorPortalContentTypes.enum";
import {useCompany} from "../../hooks/fetches/useCompany";
import {usePartnerPage} from "../../hooks/fetches/usePartnerPage";
import {usePeople} from "../../hooks/fetches/usePeople";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {IAddBlogPostData, IAddBlogPostForm, StatusTypes, SubjectTypes} from "../../Interfaces/blog.interface";
import {ICategory} from "../../Interfaces/categories.interface";
import {IPartnerSection, TPartnerPageLayouts} from "../../Interfaces/partnerPage.interface";
import {IMutateData, MutateTypes} from "../../Interfaces/queries.interface";
import {ITag} from "../../Interfaces/tags.interface";
import styles from "../../styles/components/blogPosting.module.sass";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import RadioWithLabel from "../../uicomponents/Molecules/RadioWithLabel/RadioWithLabel.component";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {stripHtml} from "../../utils/formatString.util";
import Loader from "../../utils/loader";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import {isAnythingBetweenTags} from "../../utils/rtevalidation.util";
import {supportText} from "../../utils/supportText.util";
import {CategoriesDropdown} from "../Categories/categoriesDropdown.component";
import ButtonComponent from "../FormControls/button.component";
import TextAreaComponent from "../FormControls/textArea.component";
import TextBoxComponent from "../FormControls/textBox.component";
import TagsDropdown from "../Tags/tagsDropdown.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import {UploadComponent} from "../Upload/upload.component";
import {removeQueryParams} from "../../utils/url.util";

interface IProps<T extends TPartnerPageLayouts> {
    onClose: Function;
    isCompany: boolean;
    blogToEdit?: string;
    friendlyUrl?: string;
    onNewsFeedPost?: Function;
    partnerSectionData?: IPartnerSection<T>[T];
    onEditSuccess?: Function;
    contentData?: any;
    companyId?: string;
    prmSearchKey?: string;
}

export interface IImageController {
    id: string;
    src: string;
}

const RTE = lazy(() => import(`../../uicomponents/Atoms/RTE/rte.component`));

export default function BlogPosting<T extends TPartnerPageLayouts>(props: IProps<T>) {
    const location = useLocation();
    const {settings} = useSettings();
    const friendly_url = props.friendlyUrl || useGetFriendlyUrl(location.pathname);
    const {
        company,
        companyBlogs,
        updateCompany,
        isUpdating: isUpdatingCompany,
    } = useCompany(friendly_url, {
        isCompany: props.isCompany,
        calls: {
            company: true,
            companyBlogs: true,
        },
    });
    const {
        people,
        peopleBlogs,
        updatePeople,
        isUpdating: isUpdatingPeople,
    } = usePeople(friendly_url, {
        isCompany: props.isCompany,
        calls: {
            people: true,
            peopleBlogs: true,
        },
    });
    const {updatePartnerPage} = usePartnerPage(company?.data?.subdomain!, {
        isCompany: props.isCompany,
        searchType: props?.prmSearchKey,
        refreshAfterMutate: true,
        sectionId: props.partnerSectionData?.id,
        calls: {all: false, partnerPage: true, searchPartnerData: false, partnerPageData: true},
    });

    let blogToEdit = props.isCompany
        ? companyBlogs.data?.find(b => b.friendly_url === props.blogToEdit)
        : peopleBlogs.data?.find(b => b.friendly_url === props.blogToEdit);

    if (props?.contentData && props.contentData?.length) {
        blogToEdit = props.contentData.find(b => b.friendly_url === props.blogToEdit);
    }

    const [categorySelected, setCategorySelected] = useState<ICategory[]>(
        blogToEdit ? settings.all_categories?.filter(c => c.id === blogToEdit?.categories?.[0]?.parent_id) || [] : [],
    );
    const [subCategorySelected, setSubCategorySelected] = useState<ICategory[]>(
        blogToEdit ? blogToEdit?.categories || [] : [],
    );

    const [tags, setTags] = useState<ITag[]>([]);

    useEffect(() => {
        setTags(blogToEdit?.tags || []);
    }, []);

    const [file, setFile] = useState<File>();
    const [imagePreview, setImagePreview] = useState<string>("");
    const navigate = useNavigate();

    const notify = useNotification();
    const subject_type = props.isCompany ? SubjectTypes.CompanyProfile : SubjectTypes.UserProfile;
    const subject_id = props?.partnerSectionData?.id
        ? props.companyId
        : props.isCompany
        ? company.data?.id
        : people.data?.id;
    const entityInfo = props.isCompany ? company.data : people.data;
    const isChannelProgram =
        props?.companyId === CHANNEL_PROGRAM_COMPANY_ID || company?.data?.id === CHANNEL_PROGRAM_COMPANY_ID;
    const blogMethods = useForm<IAddBlogPostForm>({
        defaultValues: {
            title: blogToEdit?.title || "",
            body: blogToEdit?.body || "<h2><strong>Start typing here</strong></h2>",
            excerpt: blogToEdit?.excerpt || "",
        },
    });
    const visibilityTypeWatcher = blogMethods.watch("visibilityType");
    const isUpdating = props.isCompany
        ? isUpdatingCompany?.[MutateTypes.AddBlog] || isUpdatingCompany?.[MutateTypes.EditBlog]
        : isUpdatingPeople?.[MutateTypes.AddBlog] || isUpdatingPeople?.[MutateTypes.EditBlog];
    const isLoading = props.isCompany ? company.isLoading : people.isLoading;

    const clearInputFile = () => {
        setFile(undefined);
    };
    const handleCancel = () => {
        props.onClose();
    };

    const handleSuccessPost = (res: AxiosResponse) => {
        if (props.partnerSectionData?.id) {
            if (!props.blogToEdit) {
                updatePartnerPage.mutate({
                    subject_id: res?.data?.id,
                    mutate_type: MutateTypes.AddSectionData,
                    id: props?.partnerSectionData?.partner_page_id,
                    section_id: props?.partnerSectionData.id,
                    subject_type: "blog",
                    image: file,
                });
            } else {
                //callback function
                props?.onEditSuccess!(res);
            }
        }
        if (props.onNewsFeedPost && res.data.status === StatusTypes.Published) {
            props.onNewsFeedPost({
                content: {...res.data, subject: {...res.data.author}},
                content_type: "Blog",
                created_at: res.data.created_at,
                id: res.data.id,
            });
        }
        navigate(removeQueryParams(location.search, ["add"]), {replace: true});
        props.onClose();
    };

    const handleSubmit = async (data: IAddBlogPostForm) => {
        if (!data.title || (data.title && data.title.trim().length <= 0)) {
            notify("Please provide post title", "Error");
            return;
        }
        if (!!categorySelected.length && !subCategorySelected.length) {
            notify(`Please select a ${SUBCATEGORY_TEXT}`, "Error");
            return;
        }
        if (data.body === "<h2><strong>Start typing here</strong></h2>" || !isAnythingBetweenTags(data.body)) {
            notify("Please provide post body", "Error");
            return;
        }
        if (!tags.length) {
            return notify("You need to add at least one tag.", "Error");
        }
        if (!!props.partnerSectionData?.id && isChannelProgram && !visibilityTypeWatcher) {
            return notify("Please select visibility type", "Error");
        }
        //add validation for tags
        if (blogToEdit) {
            if (!!subCategorySelected.length) data.categories = subCategorySelected.map(subCat => subCat.id);
            if (!!tags.length) data.tags = tags.map(tag => tag.id);
            if (data.body === blogToEdit.body) delete data.body;
            if (!!file && imagePreview !== blogToEdit.images?.[0]?.src) data.header_image = imagePreview;
            if (!data.excerpt)
                data.excerpt =
                    stripHtml(data.body).length > 197
                        ? stripHtml(data.body).substring(0, 197) + "..."
                        : stripHtml(data.body);
            if (blogToEdit.status === StatusTypes.Published) data.status = StatusTypes.Published;
            if (blogToEdit.status === StatusTypes.Draft) {
                await getUserConfirmation("Do you want to save as a draft or publish now?", {
                    okButtonText: "Publish",
                    cancelButtonText: "Save as Draft",
                    title: "Save or Publish Your Blog",
                }).then(answer => {
                    if (answer.value) {
                        data.status = StatusTypes.Published;
                    } else {
                        data.status = StatusTypes.Draft;
                    }
                });
            }
            const blogPost: IAddBlogPostData = {
                ...data,
                subject_id: subject_id!,
                subject_type,
                is_partner_content: blogToEdit?.is_partner_content && !!props.partnerSectionData?.id,
            };
            if (isChannelProgram && blogPost && blogPost.is_partner_content) {
                blogPost.media_visibility = visibilityTypeWatcher;
            }
            const saveEdition = Object.keys(data)
                .map(key => {
                    if (key === "categories") {
                        return blogToEdit?.[key]?.[0]?.id !== data?.[key]?.[0];
                    }
                    if (key === "tags") {
                        return !!data?.[key]?.map(tagID => blogToEdit?.[key]?.some(v => v.id === tagID)).some(v => v);
                    }
                    if (key === "body") {
                        return stripHtml(blogToEdit?.[key]) !== stripHtml(data[key]);
                    }
                    if (key === "header_image") return false;
                    return blogToEdit?.[key] !== data[key];
                })
                .some(v => v);
            if (saveEdition)
                props.isCompany
                    ? updateCompany.mutate({
                          blog_id: blogToEdit.id,
                          blog: blogPost,
                          mutate_type: MutateTypes.EditBlog,
                          onSuccess: handleSuccessPost,
                          is_partner_content: !!props.partnerSectionData?.id,
                      })
                    : updatePeople.mutate({
                          blog_id: blogToEdit.id,
                          blog: blogPost,
                          mutate_type: MutateTypes.EditBlog,
                          onSuccess: handleSuccessPost,
                      });
            if (!!file && imagePreview !== blogToEdit.images?.[0]?.src) {
                const updateHeaderObj: IMutateData = {
                    blog_id: blogToEdit.id,
                    blog_image: file,
                    company_id: subject_id,
                    people_id: subject_id,
                    mutate_type: MutateTypes.AddImgBlog,
                    onSuccess: handleSuccessPost,
                };
                props.isCompany ? updateCompany.mutate(updateHeaderObj) : updatePeople.mutate(updateHeaderObj);
            }
            return;
        }

        data.status = StatusTypes.Published;
        if (!!subCategorySelected.length) data.categories = subCategorySelected.map(subCat => subCat.id);
        if (!!tags.length) data.tags = tags.map(tag => tag.id);
        if (!!file) data.header_image = file;
        //just for time being I am passing as status publised always, in future we are going to change that. confirmed with krista
        if (!props?.partnerSectionData?.id) {
            await getUserConfirmation("Do you want to save as a draft or publish now?", {
                okButtonText: "Publish",
                cancelButtonText: "Save as Draft",
                title: "Save or Publish Your Blog",
            }).then(answer => {
                if (answer.value) {
                    data.status = StatusTypes.Published;
                } else {
                    data.status = StatusTypes.Draft;
                }
            });
        }
        if (!data.excerpt)
            data.excerpt =
                stripHtml(data.body).length > 197
                    ? stripHtml(data.body).substring(0, 197) + "..."
                    : stripHtml(data.body);
        const blogPost: IAddBlogPostData = {
            ...data,
            subject_id: subject_id!,
            subject_type,
            is_partner_content: !!props.partnerSectionData?.id,
        };
        if (isChannelProgram && blogPost && blogPost.is_partner_content) {
            blogPost["media_visibility"] = visibilityTypeWatcher;
        }
        if (props.isCompany) {
            updateCompany.mutate({
                blog: blogPost,
                entity: entityInfo || undefined,
                mutate_type: MutateTypes.AddBlog,
                onSuccess: handleSuccessPost,
            });
        } else {
            updatePeople.mutate({
                blog: blogPost,
                entity: entityInfo || undefined,
                mutate_type: MutateTypes.AddBlog,
                onSuccess: handleSuccessPost,
            });
        }

        return;
    };

    useEffect(() => {
        if (props.blogToEdit && blogToEdit) {
            if (blogToEdit.images?.[0]?.src) {
                setImagePreview(blogToEdit.images?.[0]?.src);
                const imageFile = new File([], blogToEdit.images?.[0]?.src, {type: "image/jpeg"});
                setFile(imageFile);
            }
            if (blogToEdit?.media_visibility) {
                blogMethods.setValue("visibilityType", blogToEdit.media_visibility);
            }
        }
    }, [props.blogToEdit]);

    return (
        <>
            <TitleContainer
                as="h2"
                text={
                    props.partnerSectionData?.id
                        ? blogToEdit
                            ? `Edit ${VENDOR_SECTION_TYPES.BLOGS}`
                            : `Add ${VENDOR_SECTION_TYPES.BLOGS}`
                        : blogToEdit
                        ? "Edit Blog Post"
                        : "Add Blog Post"
                }
                noConnector
            />
            <div className={styles.container}>
                <FormProvider {...blogMethods}>
                    <form onSubmit={blogMethods.handleSubmit(handleSubmit)}>
                        <TextBoxComponent
                            id="title"
                            containerClassName="mw-100 mb-4"
                            placeholder="Post title"
                            label="Title*"
                            isRequired
                        />
                        <div className={`mb-4 ${file && "d-flex flex-column align-items-center"}`}>
                            {!file && (
                                <SubtitleContainer
                                    as="span"
                                    text="Select header image"
                                    position="start"
                                    elClassName="ps-2 mb-0 cpLabel"
                                />
                            )}
                            <UploadComponent
                                id="header_image"
                                file={file}
                                setFile={setFile}
                                clearInputFile={clearInputFile}
                                maxSize="5"
                                type={MediaTypes.Image}
                                imagePreviewUrl={imagePreview}
                                setImagePreviewUrl={setImagePreview}
                                className={file && styles.imageHeaderPreview}
                                customResetBtn="Choose another header image"
                                customSupportText={supportText("blog", "5")}
                            />
                        </div>
                        <div className="mb-4">
                            <SubtitleContainer
                                as="span"
                                text="Post body*"
                                position="start"
                                elClassName="ps-2 mb-0 cpLabel"
                            />
                            <Suspense>
                                <RTE
                                    id="body"
                                    isRequired
                                    defaultValue={blogToEdit?.body}
                                    entityId={entityInfo?.id}
                                    isUserProfile={!props.isCompany}
                                />
                            </Suspense>
                        </div>
                        <TextAreaComponent
                            id="excerpt"
                            maxLength={255}
                            placeholder="Post excerpt. If not provided, it will be generated from the body."
                            label="Description"
                            rows="3"
                            containerClassName={`mb-4 ${styles.excerpt}`}
                        />
                        <CategoriesDropdown
                            categorySelected={categorySelected}
                            setCategorySelected={setCategorySelected}
                            subCategorySelected={subCategorySelected}
                            setSubCategorySelected={setSubCategorySelected}
                            className="mb-4"
                        />
                        <TagsDropdown selected={tags} onChange={setTags} className="mb-4" max={3} isRequired />
                        {props?.partnerSectionData?.id && isChannelProgram && (
                            <div className="d-flex flex-column">
                                <Typography variant="caption" fontSize={18} fontWeight={400}>
                                    Select the appropriate level of visibility
                                </Typography>
                                <div className="d-flex flex-row">
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.VENDORS_ONLY}
                                        id="visibilityType"
                                        value="vendor"
                                    />
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.MSP_ONLY}
                                        id="visibilityType"
                                        value="msp"
                                    />
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.ALL_USERS}
                                        id="visibilityType"
                                        value="all"
                                    />
                                </div>
                            </div>
                        )}
                        <div className="d-flex gap-3 justify-content-center">
                            <ButtonComponent
                                id="saveDraft"
                                type="button"
                                className="my-4 cpBlueAltBtn"
                                onClick={handleCancel}
                                disabled={isUpdating}>
                                Cancel
                            </ButtonComponent>
                            <ButtonComponent
                                id="submit"
                                type="submit"
                                className="my-4 cpBlueBtn"
                                disabled={
                                    props?.partnerSectionData?.id ? false : isUpdating || isLoading || !subject_id
                                }>
                                {isUpdating ? <Loader inline loading version="iconWhite" /> : "Save"}
                            </ButtonComponent>
                        </div>
                    </form>
                </FormProvider>
            </div>
        </>
    );
}
