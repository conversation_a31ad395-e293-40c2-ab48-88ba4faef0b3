import Grid from "@mui/material/Grid";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useLocation} from "react-router-dom";
import {
    acceptedImageFormats,
    CHANNEL_PROGRAM_COMPANY_ID,
    eventDescriptionLimit,
} from "../../constants/commonStrings.constant";
import {VENDOR_TYPES} from "../../constants/vendorProfiles.constants";
import {MediaTypes} from "../../enums/mediaTypes.enum";
import {IActiveCompany} from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import {useDebounce} from "../../hooks/useDebounce";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {ICompany, ICompanyProfileType} from "../../Interfaces/company.interface";
import {IndustryEventTypes} from "../../Interfaces/event.interface";
import {IIndustryEvent, IIndustryEventCreate} from "../../Interfaces/events.interface";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IVendorsResult} from "../../Interfaces/search.interface";
import {adminService} from "../../services/admin.service";
import {eventService} from "../../services/event.service";
import {searchService} from "../../services/search.service";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import AddressAutoComplete from "../../uicomponents/Molecules/AddressAutocomplete/addressAutoComplete.component";
import AutoCompleteComponent, {
    IAutocompleteOption,
} from "../../uicomponents/Molecules/Autocomplete/Autocomplete.component";
import {IAvatarV2Props} from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import CheckboxWithLabel from "../../uicomponents/Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import TimezoneSelect from "../../uicomponents/Molecules/TimezoneSelect/timezoneSelect.component";
import {EventInfoCard} from "../../uicomponents/Organism/EventInfoCard/EventInfoCard.component";
import {getErrorFromArray} from "../../utils/error.util";
import {
    convertToInputDateTimeLocal,
    DATE_FORMAT_Year_Month_Day,
    dateToTimezone,
    formatDate,
    INPUT_DATE_TIME,
} from "../../utils/formatDate";
import {stripHtml} from "../../utils/formatString.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {formatLink} from "../../utils/linkify.util";
import Loader from "../../utils/loader";
import {validateURL} from "../../utils/validation.util";
import InfoBanner from "../DescriptionBanner/infoBanner.component";
import ButtonComponent from "../FormControls/button.component";
import CheckBoxComponent from "../FormControls/checkBox.component";
import {InfoIcon} from "../Icons";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import {UploadComponent} from "../Upload/upload.component";
import styles from "./addIndEventModal.module.sass";

interface IProps {
    newIndEvent: boolean;
    setNewIndEvent: (newIndEvent: boolean) => void;
    onNewEvent: (newIndEvent: IIndustryEvent) => void;
    company?: ICompany | IActiveCompany;
    event?: IIndustryEvent;
    isAdmin?: boolean;
}

interface IIndustryEventForm {
    id: string;
    organization: string;
    event_name: string;
    description?: string;
    start_date: Date | string;
    end_date: Date | string;
    in_person_or_virtual: IndustryEventTypes | string;
    link: string;
    cp_event?: boolean;
    is_published?: boolean;
    location?: string;
    created?: string;
    updated?: string;
    companySelection?: string;
    feature_flag?: boolean;
    image?: File;
    feature_start_date?: string;
    feature_end_date?: string;
    feature_start_time?: string;
    feature_end_time?: string;
    time_zone_id?: string;
}

interface ICompanyInfo {
    id: string;
    name: string;
    avatar: string;
    type: ICompanyProfileType;
}

const options = [
    {id: "road_show", label: "Road Show", tooltip: "Event typically with less than five vendors, less than 100 MSPs"},
    {
        id: "regional_event",
        label: "Regional Event",
        tooltip: "Event with 50 or less vendors, ex ASCII, ChannelPro., typically 100-200 MSPs",
    },
    {id: "conference", label: "Conference", tooltip: "Large industry event with 50+ vendors, typically 500-3,000 MSPs"},
    {
        id: "virtual",
        label: "Virtual",
        tooltip: "Event that will be attended via the internet, typically a special event featuring multiple guests",
    },
    {
        id: "webinar",
        label: "Webinar",
        tooltip:
            "Event that will be attended via the internet, typically 30-60 mins, thought leadership, product updates, etc",
    },
    {
        id: "virtual_product_demo",
        label: "Virtual Product Demo",
        tooltip: "Event that will be attended via the internet, vendor is showing a demo of their product",
    },
];

export interface IVendorSearch extends IAutocompleteOption {
    friendly_url?: string;
    subscription_label?: string;
    subscription_value?: string;
    avatar?: IAvatarV2Props | undefined;
}
export default function AddIndEventModal(props: IProps) {
    const [sendingEvent, setSendingEvent] = useState(false);
    const [search, setSearch] = useState<string | undefined>(undefined);
    const [isSearching, setIsSearching] = useState(false);
    const [companyMatches, setCompanyMatches] = useState<ICompanyInfo[]>([]);
    const [searchOptions, setSearchOptions] = useState<IVendorSearch[]>([]);
    const isFirstSend = useRef(true);
    const isFirstLoad = useRef(true);
    const eventToSend = useRef<IIndustryEventCreate>();
    const linkedOrg = useRef<IVendorSearch>();
    const {authState} = useAuthState();
    const [headerThumbnail, setHeaderThumbnail] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>(props?.event?.image?.src || "");
    const [chosenVendorSubscription, setChosenVendorSubscription] = useState<string>("");
    const [chosenVendorSubscriptionValue, setChosenVendorSubscriptionValue] = useState<string>("");
    const [fileToEdit, setFileToEdit] = useState(props?.event?.image ? props?.event?.image : null);
    const debouncedSearch = useDebounce(search);
    const formattedStartDate = props.event?.start_date
        ? props.event?.time_zone
            ? convertToInputDateTimeLocal(props.event?.start_date, props.event?.time_zone)
            : formatDate(props.event.start_date, INPUT_DATE_TIME)
        : undefined;
    const formattedEndDate = props.event?.end_date
        ? props.event?.time_zone
            ? convertToInputDateTimeLocal(props.event?.end_date, props.event?.time_zone)
            : formatDate(props.event.end_date, INPUT_DATE_TIME)
        : undefined;
    const {hasPermissions} = usePermissions();
    const isAdmin = hasPermissions([PERMISSION_GROUPS.ADMIN_INDUSTRY_CALENDAR_UPDATE]);

    const needLocationEvents = ["road_show", "conference", "regional_event"];

    const newEventMethods = useForm<IIndustryEventForm>({
        defaultValues: {
            organization: props.company?.name || props.event?.organization || "",
            description: props.event?.description || "",
            event_name: props.event?.event_name || "",
            location: props.event?.location || "",
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            in_person_or_virtual: undefined,
            link: props.event?.link || "",
            cp_event: props.event?.cp_event || false,
            is_published: props.event?.status === "approved" || false,
            feature_flag: props.event?.feature_flag || false,
            image: props.event?.image || undefined,
            time_zone_id: String(props.event?.time_zone) || undefined,
        },
    });
    const cpEvent = newEventMethods.watch("cp_event");
    const locationWatcher = newEventMethods.watch("location");
    const cpDescription = newEventMethods.watch("description");
    const notify = useNotification();
    const location = useLocation();
    const isAdminPage = location.pathname.includes("admin");
    const startDateWatcher = newEventMethods.watch("start_date");
    const endDateWatcher = newEventMethods.watch("end_date");
    const linkWatcher = newEventMethods.watch("link");
    const endDateWatcherIso = endDateWatcher ? new Date(endDateWatcher).toISOString() : null;
    const timeZoneWatcher = newEventMethods.watch("time_zone_id");
    const eventTypeWatcher = newEventMethods.watch("in_person_or_virtual");
    const featuredEventStartDate = newEventMethods.watch("feature_start_date") || "";
    const featuredEventEndDate = newEventMethods.watch("feature_end_date") || "";
    const featuredEventStartTime = newEventMethods.watch("feature_start_time");
    const organizationWatcher = newEventMethods.watch("organization");
    const eventNameWatcher = newEventMethods.watch("event_name");
    let minEndDate: any = null;
    if (featuredEventStartDate) {
        minEndDate = new Date(featuredEventStartDate);
        minEndDate?.setDate(minEndDate.getDate() + 1);
    }
    const minEndDateString = minEndDate ? formatDate(minEndDate?.toISOString(), DATE_FORMAT_Year_Month_Day) : "";

    useEffect(() => {
        if (cpDescription && stripHtml(cpDescription).trim().length > eventDescriptionLimit) {
            newEventMethods.setError("description", {type: "maxLength"});
        }
    }, [cpDescription]);

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setSendingEvent(false);
        setIsSearching(false);
    };

    const handleSuccess = (res: AxiosResponse<IIndustryEvent | ICompanyInfo[]>) => {
        setSendingEvent(false);
        if (Array.isArray(res.data) && props.isAdmin) {
            setCompanyMatches(res.data);
            return;
        }
        if (props.isAdmin) notify(`Event ${props.event ? "updated" : "created"} successfully`, "Success");
        props.onNewEvent(res.data as IIndustryEvent);
        props.setNewIndEvent(false);
    };

    const handleSuccessCreate = (res: AxiosResponse<IIndustryEvent>) => {
        handleSuccess(res);
        if (isAdmin) {
            adminService.storeActivityLog({
                action: "createdIndustryEvent",
                name: `${authState?.firstName} ${authState?.lastName}`,
                author_id: authState?.id,
                author_type: "userType",
                subject_id: res.data?.id,
                subject_type: "industryEvent",
                additional_data: {
                    event_id: res.data?.id,
                    status: res.data?.status,
                    created_by: authState?.id,
                    created_by_name: `${authState?.firstName} ${authState?.lastName}`,
                },
            });
        }
    };
    const handleSubmit = async (data: IIndustryEventForm) => {
        if (data?.description && stripHtml(data.description).trim().length > eventDescriptionLimit) {
            notify(`Description shouldn't exceeds ${eventDescriptionLimit} character limit`, "Error");
            return;
        }
        const formattedStartDate = dateToTimezone(String(data.start_date), data.time_zone_id as string);
        const formattedEndDate = dateToTimezone(String(data.end_date), data.time_zone_id as string);
        const organization = props.company?.id ? props.company.name : linkedOrg.current ? linkedOrg.current.label : "";

        setSendingEvent(true);
        let eventData: IIndustryEventCreate = {
            organization: organization,
            event_name: data.event_name,
            location: data.location ? data.location : undefined,
            state: data["location_state"],
            city: data["location_city"],
            country: data["location_country"],
            zip: data["location_zip"],
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            in_person_or_virtual: data.in_person_or_virtual,
            link: formatLink(data.link),
            cp_event: data.cp_event ? "1" : "0",
            status: props.isAdmin
                ? props.event?.status || data.is_published
                    ? "approved"
                    : "draft"
                : props.event?.status === "archived"
                ? "archived"
                : "awaiting_approval",
            first_send: linkedOrg.current
                ? "0"
                : props.isAdmin && !props.event?.subject_id
                ? isFirstSend.current
                    ? "1"
                    : "0"
                : "0",
            feature_flag: data.feature_flag ? true : false,
            image: headerThumbnail,
            time_zone: data.time_zone_id,
        };
        if (data?.description && data.description.length > 0) {
            eventData = {...eventData, description: data.description.replace(/(<[^>]+>)\s+/g, "$1")};
        }
        if (data.feature_flag) {
            const formattedFeatureStartDate = data.feature_start_date + "T" + data.feature_start_time + ":00";
            const formattedFeatureEndDate = data.feature_end_date + "T" + data.feature_end_time + ":00";
            eventData.feature_start_date = dateToTimezone(
                String(formattedFeatureStartDate),
                data.time_zone_id as string,
            );
            eventData.feature_end_date = dateToTimezone(String(formattedFeatureEndDate), data.time_zone_id as string);
            let failed = false;
            const requestBody: any = {
                feature_flag: true,
                feature_start_date: eventData.feature_start_date,
                feature_end_date: eventData.feature_end_date,
                subject_id: props.company?.id
                    ? props.company.id
                    : linkedOrg?.current
                    ? linkedOrg.current.id
                    : props?.event?.subject_id
                    ? props.event.subject_id
                    : undefined,
            };
            if (props.event?.id) {
                requestBody.id = props.event?.id;
            }
            await eventService.checkFeatureFlag(requestBody).catch((err: AxiosError) => {
                failed = true;
                notify(getErrorFromArray(err), "Error");
                setSendingEvent(false);
            });
            if (failed) return;
        }
        eventToSend.current = eventData;
        const updateEvent = {...eventData, id: props.event ? props.event.id : "", isAdmin: props.isAdmin};
        if (!props.isAdmin && props.event?.status === "approved") {
            updateEvent.status = "awaiting_approval";
        }
        if (!data.is_published && props.isAdmin) {
            delete updateEvent.status;
        }
        props.event?.id
            ? eventService
                  .updateIndustryEvent(
                      updateEvent,
                      props.company?.id
                          ? props.company.id
                          : props.event.subject_id
                          ? props.event.subject_id
                          : undefined,
                  )
                  .then(res => {
                      if (isAdmin) {
                          adminService.storeActivityLog({
                              action: "updatedIndustryEvent",
                              name: `${authState?.firstName} ${authState?.lastName}`,
                              author_id: authState?.id,
                              author_type: "userType",
                              subject_id: res.data?.id,
                              subject_type: "industryEvent",
                              additional_data: {
                                  event_id: res.data?.id,
                                  status: res.data?.status,
                                  created_by: authState?.id,
                                  created_by_name: `${authState?.firstName} ${authState?.lastName}`,
                              },
                          });
                      }
                      handleSuccess(res);
                  })
                  .catch(handleError)
            : eventService.createIndustryEvent(
                  eventData,
                  handleSuccessCreate,
                  handleError,
                  props.company?.id ? props.company.id : linkedOrg.current ? linkedOrg.current.id : undefined,
                  props.isAdmin,
              );
    };

    const handleCompanyMatch = (id?: string) => {
        if (!eventToSend.current) return;
        setSendingEvent(true);
        const event: IIndustryEventCreate = {...eventToSend.current, first_send: "0"};
        eventService.createIndustryEvent(event, handleSuccess, handleError, id ? id : undefined, props.isAdmin);
    };

    const handleSelectOrg = option => {
        if (!option) return;
        linkedOrg.current = option;
        setChosenVendorSubscription(option?.subscription_label ?? "");
        setChosenVendorSubscriptionValue(option?.subscription_value ?? "");
    };

    const handleSearch = async value => {
        setIsSearching(true);
        searchService.simple(value, {only_companies: true, is_vendor_only: true}, handleSuccessSearch, handleError);
    };

    const handleSuccessSearch = async (response: any) => {
        const vendors: IVendorsResult = response.data;
        let results: IVendorSearch[] = [];
        if (vendors && vendors.data.length) {
            const vendorsResults: IVendorSearch[] = vendors.data.map(vendor => {
                return {
                    id: vendor.id,
                    label: vendor.name,
                    friendly_url: vendor.friendly_url,
                    avatar: {
                        user: {
                            avatar: vendor.avatar || createImageFromInitials(50, vendor.name, "", "company"),
                            type: "vendor",
                            friendly_url: vendor.friendly_url,
                            name: vendor.name,
                        },
                        isCompany: true,
                        showProfileType: false,
                        size: 30,
                        aligned: "bottom",
                    },
                    subscription_label: vendor.subscription_label,
                    subscription_value: vendor.subscription_value,
                };
            });
            results = results.concat(vendorsResults);
        }
        setSearchOptions(results);
        setIsSearching(false);
    };

    const parseDateAndTime = (date: string) => {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = ("0" + (dateObj.getMonth() + 1)).slice(-2);
        const day = ("0" + dateObj.getDate()).slice(-2);
        const hours = ("0" + dateObj.getHours()).slice(-2);
        const minutes = ("0" + dateObj.getMinutes()).slice(-2);
        return {
            date: year + "-" + month + "-" + day,
            time: hours + ":" + minutes,
        };
    };

    const formatDateTimeAsISO = (inputDate: string) => {
        const parts = inputDate.split(" ");
        const dateParts = parts[0].split("-");
        const time = parts[1];
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1;
        const day = parseInt(dateParts[2]) + 1;
        const newDate = new Date(year, month, day);
        const newYear = newDate.getFullYear();
        const newMonth = String(newDate.getMonth() + 1).padStart(2, "0");
        const newDay = String(newDate.getDate()).padStart(2, "0");
        const result = `${newYear}-${newMonth}-${newDay}T${time}.000000Z`;
        return result;
    };

    const handleInputChange = (e: any) => {
        if (!e?.target?.value) {
            setChosenVendorSubscription("");
            setChosenVendorSubscriptionValue("");
            return;
        }
        setSearch(e?.target?.value || "");
    };
    useEffect(() => {
        if (props.isAdmin) {
            if (cpEvent) {
                const cpObj: IVendorSearch = {
                    label: "Channel Program",
                    friendly_url: "channelprogram",
                    id: CHANNEL_PROGRAM_COMPANY_ID,
                    avatar: {
                        user: {
                            avatar: "/Media/short-logo.png",
                            type: "vendor",
                            name: "Channel Program",
                            friendly_url: "channelprogram",
                        },
                        isCompany: true,
                        showProfileType: false,
                        size: 30,
                        aligned: "bottom",
                    },
                };
                linkedOrg.current = cpObj;
                setSearchOptions([cpObj]);
                newEventMethods.setValue("organization", cpObj.label);
            } else {
                newEventMethods.setValue("organization", "");
                setSearchOptions([]);
            }
        }
    }, [cpEvent]);

    useEffect(() => {
        if (startDateWatcher && !isFirstLoad.current) {
            newEventMethods.setValue("end_date", startDateWatcher);
        }
        isFirstLoad.current = false;
        return () => {
            isFirstLoad.current = true;
        };
    }, [startDateWatcher]);

    useEffect(() => {
        newEventMethods.setValue("feature_end_time", featuredEventStartTime);
    }, [featuredEventStartTime]);

    useEffect(() => {
        if (props.event) {
            const vendorObj: IVendorSearch = {
                label: props.event.organization,
                friendly_url: "",
                id: props.company?.id ?? props.event?.subject_id ?? "",
            };
            linkedOrg.current = vendorObj;
            setSearchOptions([vendorObj]);
            newEventMethods.setValue("organization", props.event?.organization);
            newEventMethods.setValue("in_person_or_virtual", props.event?.in_person_or_virtual);
            if (props.event?.feature_flag && props.event.feature_start_date && props.event.feature_end_date) {
                const parsedStart = props?.event?.time_zone
                    ? parseDateAndTime(
                          convertToInputDateTimeLocal(
                              formatDateTimeAsISO(props.event?.feature_start_date),
                              props.event?.time_zone,
                              "yyyy-LL-dd HH:mm:ss",
                          ),
                      )
                    : parseDateAndTime(props.event?.feature_start_date);
                const parsedEnd = props?.event?.time_zone
                    ? parseDateAndTime(
                          convertToInputDateTimeLocal(
                              formatDateTimeAsISO(props.event?.feature_end_date),
                              props.event?.time_zone,
                              "yyyy-LL-dd HH:mm:ss",
                          ),
                      )
                    : parseDateAndTime(props.event?.feature_end_date);
                newEventMethods.setValue("feature_start_date", parsedStart?.date);
                newEventMethods.setValue("feature_start_time", parsedStart?.time);
                newEventMethods.setValue("feature_end_date", parsedEnd?.date);
                newEventMethods.setValue("feature_end_time", parsedEnd?.time);
            }
            if (props.event?.time_zone) {
                newEventMethods.setValue("time_zone_id", props.event.time_zone);
            }
        }
    }, []);

    useEffect(() => {
        if (debouncedSearch) {
            if ((debouncedSearch?.length || 0) <= 1) {
                notify("The search word field must be at least 2 characters.", "Error");
                return;
            }
            handleSearch(debouncedSearch);
        }
    }, [debouncedSearch]);
    return (
        <>
            {!companyMatches.length && (
                <ModalComponent
                    shouldCloseOnClickOutside={false}
                    open={props.newIndEvent}
                    onClose={() => props.setNewIndEvent(false)}
                    title="Industry Event"
                    cancelButtonText="CANCEL"
                    okButtonText="SAVE"
                    reverseBtnPosition
                    modalTheme="blue"
                    okButtonDisabled={sendingEvent}
                    loading={sendingEvent}
                    onSuccess={newEventMethods.handleSubmit(handleSubmit)}
                    content={
                        <Grid container display="flex" flexDirection="column">
                            <FormProvider {...newEventMethods}>
                                <form className="d-flex flex-column align-items-center">
                                    {props.isAdmin ? (
                                        <Grid item width={"100%"}>
                                            <CheckboxWithLabel
                                                key="cp_event"
                                                id="cp_event"
                                                label="It's a Channel Program event"
                                                disabled={!!props.event?.subject_id}
                                            />
                                            <AutoCompleteComponent
                                                id="organization"
                                                label="ORGANIZATION NAME"
                                                options={isSearching ? [] : searchOptions}
                                                loading={isSearching}
                                                placeholder="Search Organization"
                                                className="w-100 mw-100 my-2"
                                                onValueChange={(id, value1, value2) => handleSelectOrg(value2)}
                                                validationSchema={v => v !== "0"}
                                                onInputChange={handleInputChange}
                                                isRequired
                                                disabled={
                                                    !!props.company ||
                                                    !!props.event?.subject_id ||
                                                    newEventMethods.watch("cp_event")
                                                }
                                            />
                                            {chosenVendorSubscription !== "" && !cpEvent && (
                                                <>
                                                    <div className="w-100 pb-3">
                                                        <InfoBanner
                                                            type={
                                                                chosenVendorSubscriptionValue ===
                                                                VENDOR_TYPES.VENDOR_FREE
                                                                    ? "error"
                                                                    : "info"
                                                            }>
                                                            <div className="d-flex py-3 px-2">
                                                                <InfoIcon size={16} />
                                                                <p>
                                                                    {chosenVendorSubscription}
                                                                    {chosenVendorSubscriptionValue ===
                                                                        VENDOR_TYPES.VENDOR_FREE &&
                                                                        " - Proceed With Caution!"}
                                                                </p>
                                                            </div>
                                                        </InfoBanner>
                                                    </div>
                                                </>
                                            )}
                                        </Grid>
                                    ) : (
                                        <TextBoxComponent
                                            id="organization"
                                            label="Organization"
                                            isRequired
                                            containerClassName="my-2 w-100"
                                            disabled={
                                                !!props.company ||
                                                !!props.event?.subject_id ||
                                                newEventMethods.watch("cp_event")
                                            }
                                        />
                                    )}
                                    <TextBoxComponent
                                        id="event_name"
                                        label="EVENT NAME"
                                        isRequired
                                        containerClassName="my-2 w-100"
                                        maxLength={255}
                                        showCharacterCount
                                    />
                                    <TextBoxComponent
                                        containerClassName="my-2 w-100 flex-column"
                                        id="description"
                                        multiline
                                        maxRows={5}
                                        maxLength={eventDescriptionLimit}
                                        className="w-100"
                                        label="DESCRIPTION"
                                        showCharacterCount
                                        allowEmojis
                                    />
                                    <AutoCompleteComponent
                                        id="in_person_or_virtual"
                                        label="EVENT TYPE"
                                        options={options}
                                        placeholder="Select Event Type"
                                        className="w-100 mw-100 my-2"
                                        validationSchema={v => v !== "0"}
                                        isRequired
                                    />
                                    {needLocationEvents.includes(newEventMethods.watch("in_person_or_virtual")) && (
                                        <AddressAutoComplete
                                            id="location"
                                            renderInput={undefined}
                                            options={undefined}
                                            className={"w-100 mw-100 my-2"}
                                            placeholder="Event location"
                                            validateOnTheFly
                                            value={newEventMethods.watch("location" as any)}
                                            shouldFormat={false}
                                            registerDetails
                                        />
                                    )}
                                    <TimezoneSelect
                                        label="TIMEZONE"
                                        id="time_zone_id"
                                        className="w-100 my-2"
                                        autoSelectUsersTimezone={!props.event?.time_zone}
                                    />
                                    <TextBoxComponent
                                        id="start_date"
                                        type="datetime-local"
                                        label="START DATE & TIME"
                                        isRequired
                                        minDate={
                                            props.event?.id
                                                ? undefined
                                                : formatDate(new Date().toISOString(), INPUT_DATE_TIME)
                                        }
                                        containerClassName="my-2 w-100"
                                    />
                                    <TextBoxComponent
                                        id="end_date"
                                        type="datetime-local"
                                        label="END DATE & TIME"
                                        minDate={newEventMethods.watch("start_date") as string}
                                        isRequired
                                        containerClassName="my-2 w-100"
                                    />
                                    <TextBoxComponent
                                        id="link"
                                        label="EVENT LINK"
                                        placeholder="Event link url"
                                        isRequired
                                        containerClassName="my-2 w-100"
                                        validationSchema={v =>
                                            typeof v === "string" && !!v.trim().length && validateURL(v)
                                        }
                                    />
                                    <div className={`mb-2 w-100 d-flex flex-column`}>
                                        {!headerThumbnail && !fileToEdit && (
                                            <SubtitleContainer
                                                as="p"
                                                text="UPLOAD IMAGE"
                                                position="start"
                                                className="ps-2 mb-0 mt-2 cpLabel"
                                            />
                                        )}
                                        <UploadComponent
                                            id="header_image"
                                            file={headerThumbnail!}
                                            fileToEdit={fileToEdit}
                                            editBoolean={true}
                                            setFile={setHeaderThumbnail}
                                            clearInputFile={() => {
                                                setHeaderThumbnail(null);
                                                setFileToEdit(null);
                                            }}
                                            type={MediaTypes.Image}
                                            maxSize="5"
                                            hidePreview={true}
                                            imagePreviewUrl={imagePreview}
                                            setImagePreviewUrl={setImagePreview}
                                            className={headerThumbnail! && styles.imageHeaderPreview}
                                            customResetBtn="Choose another image"
                                            customSupportText={`Accepted file formats are ${acceptedImageFormats}. Recommended size 361px x 212px. Maximum file size is 5 MB`}
                                        />
                                        <div className={styles.previewContainer}>
                                            {eventNameWatcher &&
                                                startDateWatcher &&
                                                endDateWatcher &&
                                                linkWatcher &&
                                                (props.isAdmin && !props.event?.id
                                                    ? linkedOrg.current?.label || ""
                                                    : organizationWatcher) && (
                                                    <>
                                                        <SubtitleContainer
                                                            as="p"
                                                            text="Preview"
                                                            position="center"
                                                            className="ps-2 mb-0 mt-2 cpLabel"
                                                        />
                                                        <EventInfoCard
                                                            id={""}
                                                            event_id={""}
                                                            event_name={eventNameWatcher}
                                                            description={cpDescription}
                                                            link={linkWatcher}
                                                            organization={
                                                                props.isAdmin && !props.event?.id
                                                                    ? linkedOrg.current?.label || ""
                                                                    : organizationWatcher
                                                            }
                                                            start_date={startDateWatcher as string}
                                                            end_date={endDateWatcher as string}
                                                            in_person_or_virtual={
                                                                eventTypeWatcher as IndustryEventTypes
                                                            }
                                                            location={locationWatcher ? locationWatcher : ""}
                                                            cp_event={cpEvent ? cpEvent : false}
                                                            is_published={false}
                                                            created={""}
                                                            updated={""}
                                                            feature_start_date={featuredEventStartDate}
                                                            feature_end_date={featuredEventEndDate}
                                                            time_zone={timeZoneWatcher}
                                                            time_zone_id={timeZoneWatcher}
                                                            image={imagePreview ? {src: imagePreview} : ""}
                                                            subject_id={""}
                                                            subject_name={""}
                                                            subject_friendly_url={""}
                                                            avatar={
                                                                isAdminPage
                                                                    ? linkedOrg.current?.avatar
                                                                    : props.company?.profile_images?.CompanyAvatar?.[0]
                                                                          ?.src ||
                                                                      props.company?.avatar ||
                                                                      ""
                                                            }
                                                        />
                                                    </>
                                                )}
                                        </div>
                                    </div>
                                    {isAdminPage && (
                                        <Grid
                                            item
                                            display={"flex"}
                                            flexDirection={"column"}
                                            alignItems={"start"}
                                            width={"100%"}>
                                            <CheckboxWithLabel
                                                key="feature_flag"
                                                className="mb-1"
                                                id="feature_flag"
                                                label="Featured Event"
                                                disabled={new Date(props.event?.end_date || "") < new Date()}
                                            />
                                            {newEventMethods.watch("feature_flag") ? (
                                                <div className="d-flex flex-column justify-content-center align-items-center w-100 mt-2">
                                                    <div className="d-flex flex-row flex-wrap">
                                                        <TextBoxComponent
                                                            id="feature_start_date"
                                                            label="Start Date"
                                                            type="date"
                                                            isRequired
                                                            maxDate={
                                                                endDateWatcher
                                                                    ? formatDate(
                                                                          new Date(endDateWatcher).toISOString(),
                                                                          DATE_FORMAT_Year_Month_Day,
                                                                      )
                                                                    : undefined
                                                            }
                                                            className="mx-2"
                                                        />
                                                        <TextBoxComponent
                                                            id="feature_start_time"
                                                            type="time"
                                                            label="Start Time"
                                                            isRequired
                                                            className="mx-2"
                                                        />
                                                    </div>
                                                    <div className="d-flex flex-row flex-wrap my-2">
                                                        <TextBoxComponent
                                                            id="feature_end_date"
                                                            type="date"
                                                            label="End Date"
                                                            isRequired
                                                            className="mx-2"
                                                            containerStyles={{minWidth: 178}}
                                                            minDate={minEndDateString}
                                                            maxDate={
                                                                endDateWatcher
                                                                    ? timeZoneWatcher && endDateWatcherIso
                                                                        ? formatDate(
                                                                              dateToTimezone(
                                                                                  endDateWatcherIso,
                                                                                  timeZoneWatcher,
                                                                              ),
                                                                              DATE_FORMAT_Year_Month_Day,
                                                                          )
                                                                        : formatDate(
                                                                              new Date(endDateWatcher).toISOString(),
                                                                              DATE_FORMAT_Year_Month_Day,
                                                                          )
                                                                    : undefined
                                                            }
                                                        />
                                                        <TextBoxComponent
                                                            id="feature_end_time"
                                                            type="time"
                                                            label="End Time"
                                                            isRequired
                                                            readOnly
                                                            value={newEventMethods.watch("feature_start_time")}
                                                            className="mx-2"
                                                            // defaultValue={newEventMethods.watch("feature_start_time")}
                                                        />
                                                    </div>
                                                </div>
                                            ) : null}
                                            <CheckboxWithLabel
                                                key="is_published"
                                                className="mb-1"
                                                id="is_published"
                                                label="Publish this event immediately"
                                            />
                                        </Grid>
                                    )}
                                    {/* <ButtonComponent
                                        id="create-event"
                                        type="submit"
                                        className="cpMainBtn mt-4"
                                        disabled={sendingEvent}>
                                        {sendingEvent ? (
                                            <Loader inline loading version="iconWhite" />
                                        ) : props.event?.id ? (
                                            "Update event"
                                        ) : (
                                            "Create event"
                                        )}
                                    </ButtonComponent> */}
                                </form>
                            </FormProvider>
                        </Grid>
                    }
                />
            )}
            {!!companyMatches.length && (
                <ModalComponent
                    open={props.newIndEvent}
                    onClose={() => props.setNewIndEvent(false)}
                    title={`Company matches`}
                    content={
                        <>
                            <>
                                <FormProvider {...newEventMethods}>
                                    <form>
                                        <h2 className="text-center">
                                            {companyMatches?.length === 1
                                                ? "Please confirm this is the company you want to link to this event."
                                                : "Please select the company you want to link to this event."}
                                        </h2>
                                        <div className="d-flex flex-column gap-3">
                                            {companyMatches?.length > 1 ? (
                                                companyMatches?.map(company => (
                                                    <div key={company.id}>
                                                        <CheckBoxComponent
                                                            type="radio"
                                                            id="companySelection"
                                                            className="h-auto"
                                                            value={company.id}>
                                                            <div className="d-flex align-items-center">
                                                                <div
                                                                    className={`${styles.avatarCol} d-flex justify-content-end me-4`}>
                                                                    <div className={styles.avatarContainer}>
                                                                        <img
                                                                            src={
                                                                                company?.avatar
                                                                                    ? company?.avatar
                                                                                    : createImageFromInitials(
                                                                                          85,
                                                                                          company?.name,
                                                                                          "",
                                                                                          "company",
                                                                                      )
                                                                            }
                                                                            alt="company logo"
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <div className="col-8 col-md-auto d-flex flex-column">
                                                                    <h3>{company?.name}</h3>
                                                                    <SubtitleContainer
                                                                        as="span"
                                                                        text={company?.type.label}
                                                                        position="start"
                                                                        elClassName="m-0"
                                                                        size="16"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </CheckBoxComponent>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="py-5">
                                                    <div className="d-flex align-items-center justify-content-center flex-wrap">
                                                        {companyMatches?.[0] && (
                                                            <div className="col-12 col-md-6 d-flex justify-content-end pe-4">
                                                                <div className={styles.avatarContainer}>
                                                                    <img
                                                                        src={
                                                                            companyMatches?.[0]?.avatar
                                                                                ? companyMatches?.[0]?.avatar
                                                                                : createImageFromInitials(
                                                                                      150,
                                                                                      companyMatches?.[0]?.name,
                                                                                      "",
                                                                                      "company",
                                                                                  )
                                                                        }
                                                                        alt="company logo"
                                                                    />
                                                                </div>
                                                            </div>
                                                        )}
                                                        <div className="col-12 col-md-6 ps-4">
                                                            <h3>{companyMatches?.[0]?.name}</h3>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </form>
                                </FormProvider>
                                <div className="d-flex align-items-center gap-3 w-100 justify-content-center mt-5">
                                    <ButtonComponent
                                        id="cancelMatchCompany"
                                        disabled={sendingEvent}
                                        onClick={() => handleCompanyMatch()}
                                        className="cpAltBtn">
                                        {sendingEvent ? (
                                            <Loader inline loading />
                                        ) : companyMatches?.length > 1 ? (
                                            "None of these"
                                        ) : (
                                            "No"
                                        )}
                                    </ButtonComponent>
                                    <ButtonComponent
                                        id="confirmCompanyMatch"
                                        onClick={() => handleCompanyMatch(newEventMethods.watch("companySelection"))}
                                        disabled={
                                            sendingEvent ||
                                            (companyMatches?.length > 1
                                                ? !newEventMethods.watch("companySelection")
                                                    ? true
                                                    : false
                                                : false)
                                        }
                                        className="cpMainBtn">
                                        {sendingEvent ? <Loader inline loading version="iconWhite" /> : "Confirm"}
                                    </ButtonComponent>
                                </div>
                            </>
                        </>
                    }
                />
            )}
        </>
    );
}
