import {ModalComponent} from "./modal.component";
import loadable from "@loadable/component";
import AnimatedWarning from "../Animations/animatedWarning.component";

interface IProps {
    infoModal: boolean;
    setInfoModal: (value: boolean) => void;
    message: string;
    type?: "success" | "error" | "warning";
    title?: string;
}

export default function InfoModal(props: IProps) {
    const AnimatedSuccess = loadable(() => import(`../Animations/animatedSuccess.component`));
    const AnimatedError = loadable(() => import(`../Animations/animatedError.component`));

    return (
        <ModalComponent
            modalSwitcher={props.infoModal}
            modalSwitcherCallback={props.setInfoModal}
            showHeader={!!props.title}
            headerTitle={props.title && props.title}
            headerPosition="center"
            headerSeparator
            form={
                <div>
                    <div
                        style={{
                            width: "150px",
                            height: "150px",
                        }}
                        className="d-block mx-auto">
                        {props.type === "error" ? (
                            <AnimatedError />
                        ) : props.type === "warning" ? (
                            <AnimatedWarning />
                        ) : (
                            <AnimatedSuccess />
                        )}
                    </div>
                    <h4 className="my-4 text-center" dangerouslySetInnerHTML={{__html: props.message}}></h4>
                </div>
            }
            showOkButton
            okButtonText={"OK"}
            onSuccess={() => props.setInfoModal(false)}
        />
    );
}
