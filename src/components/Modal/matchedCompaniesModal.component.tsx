import {FormProvider, useForm} from "react-hook-form";
import {ICompanyInfo} from "../Auth/OldRegisterForm.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import RadioWithLabel from "../../uicomponents/Molecules/RadioWithLabel/RadioWithLabel.component";
import AvatarComponentV2 from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import ButtonComponent from "../../uicomponents/Atoms/Button/Button.Component";
import useTheme from "@mui/material/styles/useTheme";
import Divider from "@mui/material/Divider";

interface IProps {
    matchedCompanies: ICompanyInfo[];
    selectedCompanyName: string;
    selectedCompanyType: string;
    handleCompanyFound: (companyId: any, isMatched: boolean) => void;
    cancelRegistration: () => void;
    show: boolean;
    setShow?: Function;
}

const MatchedCompaniesModal = (props: IProps) => {
    const {matchedCompanies, handleCompanyFound, cancelRegistration, show, setShow} = props;
    const methods = useForm();
    const theme = useTheme();

    const canCreateNewCompany = !matchedCompanies.find(company => {
        return company.name === props.selectedCompanyName && company.type.value === props.selectedCompanyType;
    });

    return (
        <ModalComponent
            open={show}
            onClose={() => setShow?.(false)}
            hideCloseBtn
            title=""
            showBottom={false}
            showOkButton={false}
            showCancelButton={false}
            shouldCloseOnClickOutside={false}
            dialogSx={{
                ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
            }}
            content={
                <>
                    <FormProvider {...methods}>
                        <form
                            onSubmit={methods.handleSubmit(data => handleCompanyFound(data.companySelection, true))}
                            style={{padding: 16, display: "flex", flexDirection: "column", gap: 24}}>
                            <Typography
                                variant="h6"
                                fontSize="23px !important"
                                fontInter
                                fontWeight="600 !important"
                                color={theme.palette.blue[800]}>
                                Confirm Your Company
                            </Typography>
                            <Box
                                display="flex"
                                padding={2}
                                justifyContent="center"
                                alignItems="center"
                                gap={2}
                                alignSelf="stretch"
                                flexWrap="wrap"
                                bgcolor={theme.palette.neutral[200]}
                                borderRadius={2}
                                maxHeight={240}
                                sx={{overflowX: "hidden", overflowY: "auto"}}>
                                {matchedCompanies?.length > 0 &&
                                    matchedCompanies?.map(company => (
                                        <Box
                                            key={company.id}
                                            display="flex"
                                            minWidth={matchedCompanies.length === 1 ? undefined : 300}
                                            alignItems="center"
                                            gap={1}
                                            flex={matchedCompanies.length === 1 ? undefined : "1 0 0"}>
                                            {matchedCompanies.length > 1 && (
                                                <RadioWithLabel id="companySelection" value={company.id} />
                                            )}
                                            <AvatarComponentV2
                                                isCompany
                                                user={company as any}
                                                size={32}
                                                isRedirectEnabled={false}
                                            />
                                            <Box display="flex" flexDirection="column">
                                                <Typography
                                                    variant="body2"
                                                    fontWeight={600}
                                                    color={theme.palette.blue[800]}
                                                    fontInter>
                                                    {company.name}
                                                </Typography>
                                                <Typography
                                                    variant="body4"
                                                    fontInter
                                                    color={theme.palette.neutral[700]}>
                                                    {company.type?.label}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    ))}
                            </Box>
                            <Typography variant="body3" fontInter>
                                Before proceeding, please confirm that you’ve selected the correct company. Once
                                confirmed, the administrators will be notified of your request to join.
                            </Typography>
                        </form>
                    </FormProvider>
                    <Divider sx={{borderColor: theme.palette.neutral?.[500] || "#94A3B8", opacity: "0.7"}} />
                    <div className="d-flex align-items-center gap-3 w-100 justify-content-center mt-5">
                        {!!canCreateNewCompany ? (
                            <ButtonComponent
                                id="cancelMatchCompany"
                                onClick={() => handleCompanyFound("", false)}
                                variant="outlined"
                                color="blue"
                                disabled={!canCreateNewCompany}
                                sx={{flex: "1 0 0", paddingY: 1.5, fontWeight: 600, fontSize: "18px !important"}}>
                                Create a New Company
                            </ButtonComponent>
                        ) : (
                            <ButtonComponent
                                id="cancelRegistration"
                                onClick={() => cancelRegistration()}
                                variant="outlined"
                                color="blue"
                                sx={{flex: "1 0 0", paddingY: 1.5, fontWeight: 600, fontSize: "18px !important"}}>
                                Cancel Registration
                            </ButtonComponent>
                        )}
                        <ButtonComponent
                            id="confirmCompanyMatch"
                            disabled={
                                matchedCompanies?.length > 1
                                    ? !!!methods.watch("companySelection")
                                        ? true
                                        : false
                                    : false
                            }
                            onClick={() =>
                                matchedCompanies?.length > 1
                                    ? handleCompanyFound(methods.watch("companySelection"), true)
                                    : handleCompanyFound(matchedCompanies?.[0]?.id, false)
                            }
                            variant="tonal"
                            sx={{flex: "1 0 0", paddingY: 1.5, fontWeight: 600, fontSize: "18px !important"}}>
                            Proceed and Request Access
                        </ButtonComponent>
                    </div>
                </>
            }
        />
    );
};

export default MatchedCompaniesModal;
