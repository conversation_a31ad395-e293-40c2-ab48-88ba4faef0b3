import type {ReactNode} from "react";

/**
 * Formats a value based on plural rules using the Intl.PluralRules API.
 * If the plural rule matches a specific rule in the provided rules object, it returns the corresponding value.
 * If no matching rule is found, it returns the 'other' value.
 *
 * @param value The numerical value to format.
 * @param other The value to return if no specific plural rule matches.
 * @param rules An object containing specific plural rules and their corresponding values or React nodes.
 * @returns The formatted value based on plural rules.
 *
 * @example
 * ```tsx
 *
 *  const messages = {
 *   one: 'There is one item',
 *   other: 'There are # items',
 *  };
 *  ...
 *  const itemCount = 3;
 *  ...
 *     <div>
 *       {formatPlural(itemCount, 'No items', messages)}
 *     </div>
 *  ...
 * ```
 */
export function formatPlural(
    value: number,
    other: string | ReactNode,
    rules: Partial<Record<Intl.LDMLPluralRule, string | ReactNode>>,
): string | ReactNode {
    const pluralRules = new Intl.PluralRules(window.navigator.language);
    const rule = pluralRules.select(value);

    return rules[rule] || other;
}
