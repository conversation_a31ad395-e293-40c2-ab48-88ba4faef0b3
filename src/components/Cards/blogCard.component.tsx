import {Image} from "react-bootstrap";
import {Link, useLocation} from "react-router-dom";
import {IBlog} from "../../Interfaces/blog.interface";
import {IMutateData, MutateTypes} from "../../Interfaces/queries.interface";
import routeConfig from "../../constants/routeConfig";
import {useCompany} from "../../hooks/fetches/useCompany";
import {usePeople} from "../../hooks/fetches/usePeople";
import styles from "../../styles/components/blogCard.module.sass";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {getFriendlyDate} from "../../utils/formatDate";
import formatString, {LENGTH} from "../../utils/formatString.util";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import LikeButton from "../Buttons/Like/likeBtn.component";
import CardActionBtns from "../Buttons/cardActionBtns.component";
import TitleContainer from "../Titles/titleContainer.component";

interface IProps {
    info: IBlog;
    entityId: string;
    isCompany?: boolean;
    link?: string;
    className?: string;
    onEdit?: (blogId: string) => void;
    isEditable?: boolean;
    maxDescription?: number;
    hideLikeButton?: boolean;
    preventAllCalls?: boolean;
    pageIndex?: number;
}

export default function BlogCard(props: IProps) {
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const {updateCompany, isUpdating: isUpdatingCompany} = useCompany(friendly_url, {
        isCompany: props.isCompany ?? false,
    });
    const {updatePeople, isUpdating: isUpdatingPeople} = usePeople(friendly_url, {
        isCompany: props.isCompany ?? true,
        calls: props.preventAllCalls ? {all: false} : undefined,
    });
    const blogUrl = props.link || routeConfig.PublicBlog.path.replace(":friendly_url", props.info.friendly_url);
    const isDeleting = isUpdatingCompany?.[MutateTypes.DelBlog] || isUpdatingPeople?.[MutateTypes.DelBlog];

    const handleRemoveBlog = async () => {
        const answer = await getUserConfirmation("", {
            form: (
                <div style={{textAlign: "center", lineHeight: "1.7"}}>
                    <div>Are you sure you want to delete the blog,</div>
                    <div>
                        <strong>{props?.info?.title}</strong>
                    </div>
                    <div>This process cannot be undone.</div>
                </div>
            ),
            okButtonText: "DELETE",
            cancelButtonText: "CANCEL",
            title: "Delete Blog?",
            mui: true,
            modalTheme: "error",
        });
        if (answer.value) {
            const updateObj: IMutateData = {
                company_id: props.entityId || "",
                people_id: props.entityId || "",
                blog_id: props.info.id,
                mutate_type: MutateTypes.DelBlog,
                status: props.info.status,
            };
            props.isCompany ? updateCompany.mutate(updateObj) : updatePeople.mutate(updateObj);
        }
    };

    return (
        <div className={`${props.className ? props.className : ""} position-relative`}>
            <Link to={blogUrl} className={`${styles.blogContainer}`}>
                {!!props.info.images?.length && (
                    <div className={styles.blogImgWrapper}>
                        <Image
                            className={styles.blogImg}
                            alt={props.info.title + " cover image"}
                            src={props.info.images?.[0].src}
                        />
                    </div>
                )}
                <div className={styles.textContainer}>
                    <h4 className="text-center mb-2">{props.info.title}</h4>
                    <span className={styles.blogDescription}>
                        {props.maxDescription
                            ? formatString(props.info.excerpt, LENGTH, props.maxDescription)
                            : props.info.excerpt}
                    </span>
                    <span className="mt-2">Posted {getFriendlyDate(props.info.created_at)}</span>
                </div>
                {props.info && !props.hideLikeButton && (
                    <div className="d-flex align-items-center justify-content-between w-100 px-4">
                        <LikeButton
                            id={"likeBtn-" + props.info.id}
                            likedLabel="Like"
                            dislikedLabel="Like"
                            subjectType="blog"
                            subjectId={props.info.id}
                            isActive={props.info.user_has_liked}
                            count={props.info.likes_count}
                            isCompany={props.isCompany}
                            friendlyUrl={friendly_url}
                            contentType="Blog"
                            btnClassName="p-0"
                            page={props.pageIndex}
                        />
                        <LikeButton
                            id={"likeCountBtn-" + props.info.id}
                            showLikes
                            count={props.info.likes_count}
                            shouldOnlyOpenLikeModal
                            subjectType="blog"
                            subjectId={props.info.id}
                            contentType="Blog"
                            btnClassName="p-0"
                            friendlyUrl={friendly_url}
                        />
                    </div>
                )}
            </Link>
            {props.isEditable && (
                <div className={styles.blogStatus} data-status={props.info.status}>
                    <TitleContainer as="h4" text={props.info.status} noConnector />
                </div>
            )}
            {props.isEditable && (
                <CardActionBtns
                    info={props.info}
                    onEdit={props.onEdit}
                    onRemove={handleRemoveBlog}
                    deleting={isDeleting}
                />
            )}
        </div>
    );
}
