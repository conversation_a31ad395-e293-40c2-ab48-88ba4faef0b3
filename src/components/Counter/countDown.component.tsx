import {DateTime} from "luxon";
import {useEffect, useState} from "react";

interface IProps {
    label: string;
    hideAfterComplete?: boolean;
    timerEnds: string | null;
    onTimerEnd: any;
    className?: string;
}

export function CountDown(props: IProps) {
    const [now, setNow] = useState(DateTime.now());
    const [counter, setCounter] = useState("");
    const [timerComplete, setTimerComplete] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setNow(DateTime.now());
        }, 1000);
        if (props.timerEnds !== null) {
            timerComplete && setTimerComplete(false);
            const diff = DateTime.fromISO(props.timerEnds).diff(now, "seconds");
            if (diff.seconds < 0 || !diff.seconds) {
                setTimerComplete(true);
                props.onTimerEnd && props.onTimerEnd();
            }
            return setCounter(diff.toFormat("mm:ss"));
        } else {
            setTimerComplete(true);
        }
        return () => {
            clearTimeout(timer);
        };
        // eslint-disable-next-line
    }, [now, props.timerEnds]);

    if (props.hideAfterComplete && timerComplete) return null;
    return (
        <span className={props.className}>
            {props.label}
            {counter}
        </span>
    );
}
