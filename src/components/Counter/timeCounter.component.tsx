import {DateTime} from "luxon";
import {useEffect, useState} from "react";
import ErrorBoundary from "../../utils/errorBoundary.util";

interface IProps {
    eventEnded: boolean;
    counterStart?: string;
    beforeStartText?: string;
    afterStartText?: string;
    hideAfterComplete?: boolean;
}

export function TimeCounter(props: IProps) {
    const [now, setNow] = useState(DateTime.now());
    const [counter, setCounter] = useState("");
    const [started, setStarted] = useState(false);

    useEffect(() => {
        if (props.eventEnded) {
            return;
        }
        const timer = setTimeout(() => {
            setNow(DateTime.now());
        }, 1000);
        if (props.counterStart) {
            const diff = now.diff(DateTime.fromISO(props.counterStart), "seconds");
            if (!started && parseInt(diff.toFormat("s")) > 0) {
                setStarted(true);
            }
            return setCounter(diff.toFormat("mm:ss"));
        }
        setCounter(" not started");
        return () => {
            clearTimeout(timer);
        };
        // eslint-disable-next-line
    }, [now]);

    if (props.eventEnded && props.hideAfterComplete) return null;
    return (
        <ErrorBoundary>
            <span>
                {!props.eventEnded && (started ? props.afterStartText + " " : props.beforeStartText + " ")}
                {!props.eventEnded && counter}
                {props.eventEnded && "Event ended"}
            </span>
        </ErrorBoundary>
    );
}
