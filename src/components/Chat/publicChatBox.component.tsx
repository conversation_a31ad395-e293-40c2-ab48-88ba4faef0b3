import {AxiosError} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {BANNED_PUBLIC_USER} from "../../constants/errorMessages.constant";
import {PusherEventsEnum} from "../../enums/pusherEvents.enum";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {PRIVATE_CHANNEL} from "../../hooks/usePusher";
import usePusherState from "../../hooks/usePusherState";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IBroadcastMsg, IPublicChat} from "../../models/Pusher.interface";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {chatService} from "../../services/chat.service";
import styles from "../../styles/components/chatBox.module.sass";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../utils/error.util";
import {TIME_FORMAT, formatDate} from "../../utils/formatDate";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import TextBoxComponent from "../FormControls/textBox.component";
import {XIcon} from "../Icons";
import ReactIcon from "../Icons/reactIcon.component";
import ChatMessage from "./chatMessage.component";

interface IProps {
    eventId: string;
    pusherChannel?: string;
    tabActive?: string;
    allowEmojis?: boolean;
}

interface IChatForm {
    message: string;
}

export function PublicChatBox(props: IProps) {
    const [sendingMsg, setSendingMsg] = useState(false);

    const {authState} = useAuthState();
    const {pusherState, updatePusherState} = usePusherState();
    const methods = useForm<IChatForm>();
    const notify = useNotification();
    const scrollContainer = useRef<HTMLDivElement>(null);
    const {hasPermissions} = usePermissions();
    const hasAdminEditAccess = hasPermissions([PERMISSION_GROUPS.ADMIN_ENGAGE_MNGMT_UPDATE]);

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setSendingMsg(false);
    };

    const saveChat = (data: IChatForm) => {
        if (!data.message.trim()) {
            notify("Please enter a message.", "Error");
            return;
        }
        setSendingMsg(true);
        methods.reset();
        chatService.sendPublicMsg(
            {
                pitch_event_id: props.eventId,
                pusher_channel: PRIVATE_CHANNEL || "",
                message: data.message,
                event: PusherEventsEnum.PublicMsgListener,
            },
            () => {
                setSendingMsg(false);
                scrollToBottom();
            },
            (err: AxiosError<any>) => {
                if ((err.response as any)?.data?.message === BANNED_PUBLIC_USER) {
                    const currentChat = pusherState.publicChatHistory[props.eventId] || [];
                    const msg: IPublicChat = {
                        creator: authState.id || "",
                        message: data.message,
                        date: new Date().toISOString(),
                        message_type: "publicChat",
                        pusher_channel: props.pusherChannel || "",
                        receiver: authState.id || "",
                        id: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
                    };
                    currentChat.push(msg);
                    updatePusherState(UPDATE_PUSHER, {
                        publicChatHistory: {...pusherState.publicChatHistory, [props.eventId]: currentChat},
                    });
                    setSendingMsg(false);
                } else {
                    handleError(err);
                }
            },
        );
    };

    const deleteChat = async (message: IPublicChat) => {
        if (!message.id || !hasAdminEditAccess) return;
        const answer = await getUserConfirmation("Are you sure you want to delete this message?");
        if (!answer.value) return;
        chatService.deletePublicMsg(
            message.id,
            PRIVATE_CHANNEL,
            PusherEventsEnum.DeletePublicMsgListener,
            () => {
                const currentChat = pusherState.publicChatHistory[props.eventId] || [];
                const newChat = currentChat.filter(msg => msg.id !== message.id);
                updatePusherState(UPDATE_PUSHER, {
                    publicChatHistory: {...pusherState.publicChatHistory, [props.eventId]: newChat},
                });
            },
            handleError,
        );
    };

    const banUser = async (message: IPublicChat) => {
        if (!message.creator || !hasAdminEditAccess) return;
        const answer = await getUserConfirmation("Are you sure you want to ban this user?");
        if (!answer.value) return;
        chatService.banPublicUser(
            message.id,
            () => {
                notify("User banned successfully.", "Success");
                const currentChat = pusherState.publicChatHistory[props.eventId] || [];
                const newChat = currentChat.filter(msg => msg.id !== message.id);
                updatePusherState(UPDATE_PUSHER, {
                    publicChatHistory: {...pusherState.publicChatHistory, [props.eventId]: newChat},
                });
                pusherState?.privateChannel?.trigger(PusherEventsEnum.BanPublicUserListener, {
                    type: PusherEventsEnum.BanPublicUserListener,
                    message_id: message.id,
                });
            },
            handleError,
        );
    };

    const scrollToBottom = () => {
        if (scrollContainer.current) {
            scrollContainer.current.scrollTop = scrollContainer.current.scrollHeight;
        }
    };

    useEffect(() => {
        if (!methods.watch("message")) scrollToBottom();
    }, [pusherState.publicChatHistory]);

    useEffect(() => {
        if (props.tabActive === "chat") {
            scrollToBottom();
        }
    }, [props.tabActive]);

    return (
        <>
            <div className={styles.chatContainer}>
                <div className={styles.chatView} ref={scrollContainer}>
                    {pusherState.publicChatHistory &&
                        pusherState.publicChatHistory[props.eventId] &&
                        pusherState.publicChatHistory[props.eventId].map(
                            (message: IPublicChat | IBroadcastMsg, index) => {
                                return (
                                    <div
                                        className={
                                            message.message_type === "broadcastChat"
                                                ? styles.broadcastMessageContainer
                                                : message.creator === authState.id
                                                ? styles.messageContainerOwnPublic
                                                : styles.messageContainer + " w-75"
                                        }
                                        key={index}>
                                        <div className={styles.messageContent}>
                                            <div className="col-12">
                                                <ChatMessage
                                                    text={message.message}
                                                    position={
                                                        message.message_type === "broadcastChat" ? "center" : "start"
                                                    }
                                                    breakWord
                                                />
                                            </div>
                                            {message.message_type !== "broadcastChat" && (
                                                <div className="d-flex justify-content-end">
                                                    <ChatMessage
                                                        size="12"
                                                        text={formatDate(message.date, TIME_FORMAT)}
                                                        position="end"
                                                    />
                                                </div>
                                            )}
                                            {hasAdminEditAccess && message.message_type === "publicChat" && (
                                                <div className={styles.adminChatControls}>
                                                    <ButtonComponent
                                                        type="button"
                                                        id={`deleteChat-${message.id}`}
                                                        disabled={!props.pusherChannel}
                                                        className={styles.adminChatControl + " cpBlueBtnThin"}
                                                        tooltip="Delete Message"
                                                        onClick={() => deleteChat(message as IPublicChat)}>
                                                        <XIcon size={10} />
                                                    </ButtonComponent>
                                                    {(message as IPublicChat).can_ban && (
                                                        <ButtonComponent
                                                            type="button"
                                                            id={`banUser-${message.id}`}
                                                            disabled={!props.pusherChannel}
                                                            className={styles.adminChatControl + " cpBlueBtnThin"}
                                                            tooltip="Ban User"
                                                            onClick={() => banUser(message as IPublicChat)}>
                                                            <div
                                                                className="d-flex align-items-center justify-content-center"
                                                                style={{height: 14, width: 14}}>
                                                                <ReactIcon iconName="GoCircleSlash" size="18px" />
                                                            </div>
                                                        </ButtonComponent>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            },
                        )}
                </div>
                <div className={styles.chatInput}>
                    <FormProvider {...methods}>
                        <form onSubmit={methods.handleSubmit(saveChat)} className={styles.chatForm}>
                            <TextBoxComponent id="message" type="text" allowEmojis={props.allowEmojis} />
                            <ButtonComponent type="submit" id="send_msg" disabled={!props.pusherChannel}>
                                Send
                                {sendingMsg && <Loader inline version="iconWhite" loading={sendingMsg} />}
                            </ButtonComponent>
                        </form>
                    </FormProvider>
                </div>
            </div>
        </>
    );
}
