import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useEffect, useMemo, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {chatSound} from "../../constants/files.constant";
import {PusherEventsEnum, PusherEvtTypesEnum, userChatListener} from "../../enums/pusherEvents.enum";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePusherState from "../../hooks/usePusherState";
import {IChat} from "../../models/Pusher.interface";
import {IChatMessagesList} from "../../pages/Admin/PitchDay.page";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {chatService} from "../../services/chat.service";
import {eventService} from "../../services/event.service";
import styles from "../../styles/components/adminChat.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import {TIME_FORMAT, formatDate} from "../../utils/formatDate";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import Loader from "../../utils/loader";
import ButtonComponent from "../FormControls/button.component";
import CheckBoxComponent from "../FormControls/checkBox.component";
import TextBoxComponent from "../FormControls/textBox.component";
import SubtitleContainer from "../Titles/subtitleContainer.component";
import TitleContainer from "../Titles/titleContainer.component";
import ChatMessage from "./chatMessage.component";

interface IAdmChat {
    eventId: string;
    pusherEventToSend: string;
    pusherChannel?: string;
    setNewMsg: Function;
    newMsg?: IChat;
}

export function AdminChat(props: IAdmChat) {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [activeChat, setActiveChat] = useState<string>("");
    const [sendingMsg, setSendingMsg] = useState<boolean>(false);

    const {pusherState, updatePusherState} = usePusherState();
    const {authState} = useAuthState();
    const methods = useForm();
    const notify = useNotification();
    const scrollContainer = useRef<HTMLDivElement>(null);
    const unreadWatcher = methods.watch("unread");
    const currentChatList = useMemo(() => pusherState.chatList, [pusherState.chatList]);
    const currentChat: IChat[] = useMemo(
        () => pusherState.chatHistory[activeChat],
        [pusherState.chatHistory, activeChat],
    );
    let tempActiveUserId: string;
    const currentChatInfo = useMemo(
        () => currentChatList.find(chat => chat.id === activeChat),
        [currentChatList, activeChat],
    );
    const currentChatName: string | null = currentChatInfo
        ? currentChatInfo?.first_name + " " + currentChatInfo?.last_name
        : null;
    const modAvatar: string = "/Media/logo50.jpg";

    const handleSuccessHistory = (response: AxiosResponse) => {
        const chatHistory: IChat[] = response.data.chatHistory;
        updatePusherState(UPDATE_PUSHER, {chatHistory: {...pusherState.chatHistory, [tempActiveUserId]: chatHistory}});
        setIsLoading(false);
        setTimeout(scrollToBottom, 500);
    };

    const getActiveChat = (id: string) => {
        setActiveChat(id);
        tempActiveUserId = id;
        if (!pusherState.chatHistory[id] && props.pusherChannel) {
            setIsLoading(true);
            return eventService.loadModChatHistory(id, props.pusherChannel, handleSuccessHistory, handleError);
        }
        setIsLoading(false);
        setTimeout(scrollToBottom, 500);
    };

    const sendMsg = (data: any) => {
        if (!data.message.trim()) {
            notify("Please enter a message.", "Error");
            return;
        }
        if (!props.pusherChannel) return;
        methods.reset();
        const msg: IChat = {
            creator: props.eventId,
            message: data.message,
            date: DateTime.now().toISO(),
            message_type: "moderatorChat",
            user_avatar: "/Media/logo50.jpg",
            id: DateTime.now().toISO(),
            receiver: activeChat,
            sender_first_name: authState.firstName!,
            sender_last_name: authState.lastName!,
            company_name: "",
            company_type: "",
            is_admin: true,
        };
        triggerPusher(msg);
        props.setNewMsg(msg);
        chatService.sendModMsgToUser(
            activeChat,
            props.eventId,
            data.message,
            props.pusherChannel,
            props.pusherEventToSend,
            handleSuccessMsgSend,
            handleError,
        );
    };

    const triggerPusher = async (msg: IChat) => {
        const chatEvent: string = await userChatListener(activeChat);
        await pusherState.privateChannel?.trigger(chatEvent, {
            type: PusherEvtTypesEnum.MsgListener,
            message: JSON.stringify(msg),
        });
        await pusherState.privateChannel?.trigger(PusherEventsEnum.AdmMsgListener, {
            type: PusherEvtTypesEnum.MsgListener,
            message: JSON.stringify(msg),
        });
    };

    const handleSuccessMsgSend = () => {
        setSendingMsg(false);
    };

    const handleError = (error: AxiosError<any>) => {
        setSendingMsg(false);
        setIsLoading(false);
        notify(getErrorFromArray(error), "Error");
    };

    const scrollToBottom = () => {
        if (scrollContainer.current) {
            scrollContainer.current.scrollTop = scrollContainer.current.scrollHeight;
        }
    };

    useEffect(() => {
        if (activeChat) {
            if (pusherState.unreadCount[activeChat]) {
                const newUnread = {...pusherState.unreadCount};
                newUnread[activeChat] = 0;
                updatePusherState(UPDATE_PUSHER, {unreadCount: newUnread});
            }
        }
        //eslint-disable-next-line
    }, [activeChat]);

    useEffect(() => {
        if (!!props.newMsg) {
            const identifier = props.newMsg.is_admin ? props.newMsg?.receiver : props.newMsg?.creator;
            const now = DateTime.now().toISO();
            const newChatMessages = {...pusherState.chatHistory};
            const chatList = pusherState.chatList;
            const currentChat: IChat[] | any = newChatMessages[identifier];
            const tempUnreadCount = pusherState.unreadCount;
            if (currentChat) {
                currentChat.push(props.newMsg);
                newChatMessages[identifier] = currentChat;
            }
            let existingChat = chatList.find(user => user.id === identifier);
            const newChat: IChatMessagesList = {
                id: identifier,
                first_name:
                    props.newMsg?.is_admin && existingChat ? existingChat?.first_name : props.newMsg?.sender_first_name,
                last_name:
                    props.newMsg?.is_admin && existingChat ? existingChat?.last_name : props.newMsg?.sender_last_name,
                company_name:
                    props.newMsg?.is_admin && existingChat ? existingChat?.company_name : props.newMsg?.company_name,
                company_type:
                    props.newMsg?.is_admin && existingChat ? existingChat?.company_type : props.newMsg?.company_type,
                user_avatar: props.newMsg?.user_avatar,
                max_date: now,
                last_message: props.newMsg?.is_admin ? `CP Admin: ${props.newMsg?.message}` : props.newMsg?.message,
            };
            if (props.newMsg?.is_admin) {
                delete newChat.user_avatar;
            }
            let newChatList: IChatMessagesList[] | undefined = existingChat
                ? [newChat, ...(chatList.filter(user => user.id !== identifier) || [])]
                : [newChat, ...chatList];
            if (identifier !== activeChat && !props.newMsg?.is_admin) {
                tempUnreadCount[identifier] = (tempUnreadCount[identifier] || 0) + 1;
            }
            updatePusherState(UPDATE_PUSHER, {
                chatHistory: newChatMessages,
                chatList: newChatList,
                unreadCount: tempUnreadCount,
            });
            if (!props?.newMsg?.is_admin) chatSound.play();
            props.setNewMsg(undefined);
            setTimeout(scrollToBottom, 200);
            newChatList = undefined;
            existingChat = undefined;
        }
        // eslint-disable-next-line
    }, [props.newMsg]);

    return (
        <>
            <FormProvider {...methods}>
                <div className={styles.chatContainer}>
                    <div className={styles.chatListContainer}>
                        <div className={styles.chatListHeader}>
                            <div className={styles.unreadCheckContainer}>
                                <CheckBoxComponent id="unread" className="my-0">
                                    <SubtitleContainer as="p" text="Unread only" size="16" />
                                </CheckBoxComponent>
                            </div>
                        </div>
                        <div className={styles.chatList}>
                            {currentChatList
                                .filter(item => {
                                    if (unreadWatcher) {
                                        return pusherState.unreadCount[item.id] || activeChat === item.id;
                                    }
                                    return true;
                                })
                                .map(item => (
                                    <div
                                        className={`${styles.chatListItem} ${
                                            activeChat === item.id ? styles.activeChat : ""
                                        } mt-3`}
                                        key={item.id}
                                        onClick={() => {
                                            if (activeChat !== item.id) {
                                                getActiveChat(item.id);
                                            }
                                        }}>
                                        <div className={styles.chatListItemAvatar}>
                                            <img
                                                src={
                                                    item.user_avatar
                                                        ? item.user_avatar
                                                        : createImageFromInitials(
                                                              50,
                                                              `${item.first_name} ${item.last_name}`,
                                                              "",
                                                              "user",
                                                          )
                                                }
                                                alt="avatar"
                                                className={styles.chatListItemAvatarImg}
                                            />
                                        </div>
                                        <div className={styles.chatListItemContent}>
                                            <div className={styles.chatListFirstRow}>
                                                <SubtitleContainer
                                                    as="p"
                                                    text={`${item.first_name} ${item.last_name}`}
                                                />
                                                <SubtitleContainer
                                                    as="p"
                                                    text={formatDate(item.max_date, TIME_FORMAT)}
                                                />
                                            </div>
                                            <span className={styles.companyTypeText}>{item.company_type}</span>
                                            <div className={styles.chatListSecRow}>
                                                <SubtitleContainer
                                                    as="p"
                                                    text={
                                                        item.last_message.length > 35
                                                            ? item.last_message.substring(0, 35) + "..."
                                                            : item.last_message
                                                    }
                                                    className={styles.lastMessageText}
                                                    size="16"
                                                />
                                                <div
                                                    className={
                                                        pusherState.unreadCount[item.id] ? styles.newMsgSign : ""
                                                    }>
                                                    {pusherState.unreadCount[item.id]
                                                        ? pusherState.unreadCount[item.id]
                                                        : ""}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </div>
                    <div className={styles.chatViewContainer}>
                        {isLoading ? (
                            <div className="d-flex justify-content-center align-items-center h-100">
                                <Loader inline loading />
                            </div>
                        ) : !activeChat ? (
                            <div className={styles.chatPlaceholder}>
                                <TitleContainer as="h2" text="Select a chat to start messaging" noConnector />
                            </div>
                        ) : (
                            <>
                                <div className={styles.chatView} ref={scrollContainer}>
                                    <>
                                        {currentChat.map((message: IChat, index: number) => {
                                            return (
                                                <div
                                                    className={
                                                        message.creator === props.eventId
                                                            ? styles.messageContainerOwn
                                                            : styles.messageContainer
                                                    }
                                                    key={index}>
                                                    <div className={styles.messageAvatar}>
                                                        <img
                                                            src={
                                                                message.user_avatar && message.user_avatar !== ""
                                                                    ? message.user_avatar
                                                                    : message.creator === props.eventId
                                                                    ? modAvatar
                                                                    : createImageFromInitials(
                                                                          50,
                                                                          currentChatName ? currentChatName : "",
                                                                          "",
                                                                          "user",
                                                                      )
                                                            }
                                                            alt="avatar"
                                                        />
                                                    </div>
                                                    <div className={styles.messageContent}>
                                                        <div className="col-12">
                                                            <ChatMessage
                                                                text={message.message}
                                                                position="start"
                                                                breakWord
                                                            />
                                                        </div>
                                                        <div className="d-flex justify-content-end gap-2">
                                                            {message.is_admin && message.sender_first_name && (
                                                                <ChatMessage
                                                                    size="12"
                                                                    text={`${message.sender_first_name} - `}
                                                                    position="end"
                                                                />
                                                            )}
                                                            <ChatMessage
                                                                size="12"
                                                                text={formatDate(message.date, TIME_FORMAT)}
                                                                position="end"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </>
                                </div>
                                <div className={styles.chatInput}>
                                    <form onSubmit={methods.handleSubmit(sendMsg)} className={styles.chatForm}>
                                        <TextBoxComponent id="message" placeholder="Enter your message" allowEmojis />
                                        <ButtonComponent id="messageBtn" type="submit" className={styles.chatButton}>
                                            {sendingMsg && <Loader inline version="iconWhite" loading />}
                                            Send
                                        </ButtonComponent>
                                    </form>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </FormProvider>
        </>
    );
}
