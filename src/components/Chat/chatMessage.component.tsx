import {linkify} from "../../utils/linkify.util";

interface ITitle {
    text?: string;
    position?: "center" | "start" | "end";
    size?: string;
    className?: string;
    children?: React.ReactNode[] | string;
    breakWord?: boolean;
}

/**
 * Factory to generate subtitles
 * @param {string} text [optional] text to show at the subtitle
 * @param {ReactNode[]} children [optional] substitute for text has higher priority
 * @param {string} position [optional] position of the subtitle, ex: "center", "start", "end"
 * @param {string} size [optional] size in px of the subtitle
 * @param {string} className [optional] className applied to the wrapper
 */
export default function ChatMessage(props: ITitle) {
    const sizeInPx = props.size + "px";
    const elProps = {className: "chatMessage", style: {fontSize: sizeInPx}};

    return (
        <span className={`${props.className}`}>
            {/* use this code if tag needs to be created mannualy  */}
            {/* {createElement("span", elProps, linkify(props?.children) || linkify(props?.text))} */}
            <span
                {...elProps}
                dangerouslySetInnerHTML={{
                    __html: props?.children ? linkify(props?.children) : props?.text ? linkify(props?.text) : "",
                }}
            />
            {props.breakWord && <span />}
        </span>
    );
}
