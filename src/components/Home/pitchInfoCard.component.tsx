import {useEffect, useRef, useState} from "react";
import useAuthState from "../../hooks/useAuthState";
import usePitchesState from "../../hooks/usePitchesState";
import {IPitchObject} from "../../models/PitchObject.interface";
import {GET_USER_PITCHES} from "../../reducers/auth.reducer";
import {GET_NOT_STARTED} from "../../reducers/pitches.reducer";
import PitchSpeakers from "./pitchSpeakers.component";
import {UpcomingPitch} from "./upcomingPitch.component";
import styles from "../../styles/pages/homeNewsFeed.module.sass";
import TitleContainer from "../Titles/titleContainer.component";
import PitchInfoCardLoader from "../ContentLoaders/PitchInfoCardLoader.component";

interface IProps {
    title?: string;
}

const PitchInfoCard = (props: IProps) => {
    const [nextPitch, setNextPitch] = useState<IPitchObject>({} as IPitchObject);

    const {authState, updateAuthState} = useAuthState();
    const {pitchesState, updatePitchesState} = usePitchesState();
    const hasFetched = useRef(false);

    useEffect(() => {
        if (pitchesState.pitches && pitchesState.pitches.length > 0) {
            setNextPitch(pitchesState.pitches![0]);
            localStorage.nextEventId = pitchesState.pitches![0].id;
            return;
        }
        if (hasFetched.current) return;
        hasFetched.current = true;
        updatePitchesState(GET_NOT_STARTED);
        // eslint-disable-next-line
    }, [pitchesState.pitches]);

    useEffect(() => {
        if (authState?.userPitches?.length < 1 && authState.id) {
            updateAuthState(GET_USER_PITCHES);
        }

        // eslint-disable-next-line
    }, []);
    return (
        <div className="pitch-info-card">
            <TitleContainer as="h2" noConnector text={props.title || "Channel Pitch"} position="start" />
            {nextPitch.id ? (
                <>
                    <UpcomingPitch nextPitch={nextPitch} hideImage />
                    <hr className="fadedBorder" />
                    <div className={styles.pitchSpeakersContainer}>
                        <PitchSpeakers
                            pitch={nextPitch}
                            speakerClassName={styles.speakerCard + " col-12 col-sm-6 col-md-12 col-xl-6 px-1 mb-1"}
                            smallCard
                        />
                    </div>
                </>
            ) : (
                <PitchInfoCardLoader />
            )}
        </div>
    );
};

export default PitchInfoCard;
