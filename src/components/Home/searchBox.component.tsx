import styles from "../../styles/pages/home.module.sass";
import SearchInput from "../Search/searchInput.component";

interface IProps {
    searchBoxTitle?: string;
    backgroundImage?: string;
}

export default function SearchBox(props: IProps) {
    return (
        <div
            className={styles.searchboxWrapper}
            style={{
                backgroundImage: `url(${
                    props.backgroundImage ? props.backgroundImage : "/Media/backgrounds/WhiteSplatter.png"
                })`,
            }}>
            <div className={styles.searchboxTitle}>
                {props.searchBoxTitle ? props.searchBoxTitle : "Influence. Connect. Grow."}
            </div>
            <div className={styles.searchbox}>
                <SearchInput />
            </div>
        </div>
    );
}
