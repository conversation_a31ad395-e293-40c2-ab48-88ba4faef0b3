import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

interface StoreShoutOutType {
    shout_out: string;
    subject_id: number | string;
    subject_type: "userProfile" | "companyProfile" | string;
    tags?: any;
}

interface UpdateShoutOutType {
    shout_out: string;
    id: string;
}

class ShoutOutService {
    showAllForSubject(subject_id: number, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `shout-out?subject_id=${subject_id}`,
            onError,
            onSuccess,
        });
    }

    storeShoutOut(data: StoreShoutOutType, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `shout-out/store`,
            data,
            onError,
            onSuccess,
        });
    }

    updateShoutOut(data: UpdateShoutOutType, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `shout-out/update`,
            data,
            onError,
            onSuccess,
        });
    }

    deleteShoutOut(shout_out_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + `shout-out/delete`,
            data: {id: shout_out_id},
            onError,
            onSuccess,
        });
    }
}

export const shoutOutService = new ShoutOutService();
