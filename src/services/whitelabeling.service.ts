import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";

class WhitelabelingService {
    loadBySubdomain(subdomain: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}whitelabeling/${subdomain}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadByCompanyId(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${companyId}/whitelabeling`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    update(companyId: string, data: Object | null, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}company/${companyId}/whitelabeling/update`,
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    uploadImage(companyId: string, imageType: string, image?: File, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("image_type", imageType);
        if (image) {
            data.append("image", image);
        }

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}company/${companyId}/whitelabeling/upload-image`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadAllCustomizableEmails(
        options: IDynamicFiltersOptions,
        friendlyUrl: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${friendlyUrl}/emails${filters}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    updateCustomizableEmail(
        friendlyUrl: string,
        emailId: string,
        data: Object | null,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}company/${friendlyUrl}/emails/${emailId}/update`,
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deleteCustomizableEmail(friendlyUrl: string, emailId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}company/${friendlyUrl}/emails/${emailId}/delete`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const whitelabelingService = new WhitelabelingService();
