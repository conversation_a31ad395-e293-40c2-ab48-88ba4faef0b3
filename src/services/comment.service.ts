import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

export interface IComment {
    comment: string;
    image?: object;
    subject_id: string;
    subject_type: string;
    author_company_id?: string | null;
}

class CommentService {
    postComment(comment: IComment, onSuccess: Function, onError: Function) {
        const json: any = {
            comment: comment.comment,
            image: comment.image,
            subject_id: comment.subject_id,
            subject_type: comment.subject_type,
        };
        if (comment.author_company_id) {
            json.author_company_id = comment.author_company_id;
        }

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "comments/store",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    showComments(
        options: {subject_id?: string; subject_type?: string; with_replies?: boolean},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const {subject_id, subject_type, with_replies} = options;
        let parameters = "?";
        if (subject_id) {
            parameters += `subject_id=${subject_id}`;
        }
        if (subject_type) {
            parameters += `&subject_type=${subject_type}`;
        }
        // if (with_replies) {
        parameters += `&with_replies=${with_replies ? 1 : 0}`;
        // }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "comments" + parameters,
            onSuccess,
            onError,
        });
    }

    flagComment(options: {subject_id?: string; reason?: string}, onSuccess?: Function, onError?: Function) {
        const json = {
            reason: options.reason,
            id: options.subject_id,
        };

        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "comments/flag",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    editComment(options: {subject_id?: string; comment?: string}, onSuccess?: Function, onError?: Function) {
        const json = {
            comment: options.comment,
            id: options.subject_id,
        };

        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "comments/update",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deleteComment(id?: string, onSuccess?: Function, onError?: Function) {
        const json = {
            id: id,
        };

        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + "comments/delete",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const commentService = new CommentService();
