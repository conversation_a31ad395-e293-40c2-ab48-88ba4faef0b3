import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import type {TCompanyType} from "../constants/companyType.constant";
import {TPermissionGroupsKeysArr} from "../Interfaces/features/features.interface";
import type {
    TRoleStore,
    TRoleUpdate,
    TTemplateRolesStore,
    TTemplateRolesUpdate,
} from "../Interfaces/permissions.interface";
import {buildApiFilters, type IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";

class RolesService {
    getRoles(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const apiFilters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/permission/role${apiFilters}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    storeRole(data: TRoleStore, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `admin/permission/role/store`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateRole(data: TRoleUpdate, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `admin/permission/role/update`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deleteRole(data: {id: string; role_id?: string}, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + `admin/permission/role/delete`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getTemplates(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const apiFilters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/permission/template-role${apiFilters}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    storeTemplates(data: TTemplateRolesStore, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `admin/permission/template-role/store`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateTemplates(
        data: TTemplateRolesUpdate & {company_types: string[]; permission_group_keys: TPermissionGroupsKeysArr},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `admin/permission/template-role/update`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deleteTemplate(id: string, onSuccess?: Function, onError?: Function) {
        const data = {id};
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + `admin/permission/template-role/delete`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPermissionFeatureGroups(company_type: TCompanyType, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/permission/ctpfgf?company_type=${company_type}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getUserPermissions(companyId?: string, token?: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `user/permissions${companyId ? `?as_company_id=${companyId}` : ""}`,
            onError: onError,
            onSuccess: onSuccess,
            id: companyId,
            headers: token
                ? {
                      Authorization: `Bearer ${token}`,
                  }
                : undefined,
        });
    }

    duplicateRole(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "admin/permission/role/duplicate",
            data: {id: companyId},
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPermissions(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const apiFilters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/permission${apiFilters}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const rolesService = new RolesService();
