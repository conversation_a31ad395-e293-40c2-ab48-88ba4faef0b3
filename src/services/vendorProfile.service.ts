import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IContactInformation, ICountry, ILang} from "../Interfaces/company.interface";
import {ImageTypes} from "../enums/imageTypes.enum";
import {DeleteImage, DocUpload} from "../Interfaces/queries.interface";
import {IFileQueue} from "../hooks/useChunkUpload";

class VendorProfileService {
    async uploadVideo(
        options: {
            id: string;
            video: File;
            thumbnail: File;
            title: string;
            description: string;
            duration: string;
            categories: string | null;
            tags: string[];
            handleUploadProgress: Function;
            is_partner_content?: boolean;
            partner_page_section_id?: string;
            partner_page_id?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        if (options.title) data.append("title", options.title);
        if (options.description) data.append("description", options.description);
        data.append("duration", options.duration);
        if (options.categories) data.append("categories[]", options.categories);
        options.tags.forEach((tagId: string) => {
            data.append("tags[]", tagId);
        });
        if (options.thumbnail) data.append("thumbnail", options.thumbnail);
        if (options.video) data.append("video", options.video);
        if (options?.is_partner_content) data.append("is_partner_content", options.is_partner_content ? "1" : "0");
        if (options?.partner_page_section_id) data.append("partner_page_section_id", options.partner_page_section_id);
        if (options?.partner_page_id) data.append("partner_page_id", options.partner_page_id);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + `profiles/vendor-basic/${options.id}/video/store`,
            data: data,
            onSuccess,
            onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    async loadContactInformation(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/contact-info`,
            onSuccess,
            onError,
            id: companyId,
        });
    }

    async updateContactInformation(
        companyId: string,
        data: IContactInformation,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/contact-info/update`,
            data: data,
            onSuccess,
            onError,
        });
    }

    async storeManagerPicture(
        options: {companyId: string; image: File; image_type: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("image", options.image);
        data.append("image_type", options.image_type);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}admin/user-content/companies/${options.companyId}/avatar/store`,
            data: data,
            onSuccess,
            onError,
        });
    }

    async adminStoreVendorProfilePicture(
        options: {companyId: string; image: File; image_type: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("image", options.image);
        data.append("image_type", options.image_type);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${options.companyId}/image/store`,
            data: data,
            onSuccess,
            onError,
        });
    }

    async updateImage(
        updatedFields: {companyId: string; image_type: ImageTypes; image: File},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("image", updatedFields?.image);
        data.append("image_type", updatedFields?.image_type);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${updatedFields?.companyId}/image/store`,
            data: data,
            onSuccess,
            onError,
        });
    }

    async loadVideos(companyId: string, show_prm_content?: boolean, onSuccess?: Function, onError?: Function) {
        let url = `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/video`;
        if (show_prm_content) {
            url = url + "?show_prm_content=1";
        }
        return apiService({
            method: "get",
            url: url,
            onSuccess,
            onError,
            id: companyId,
        });
    }

    async loadOverview(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/overview`,
            onSuccess,
            onError,
            id: companyId,
        });
    }

    async loadAllCountries(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}countries`,
            onSuccess,
            onError,
        });
    }

    async loadAllLangs(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}languages`,
            onSuccess,
            onError,
        });
    }

    async syncLangs(languages_supported: ILang[], companyId: string, onSuccess?: Function, onError?: Function) {
        const json = {
            languages_supported: languages_supported.map(x => x.id),
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/overview/sync-languages`,
            data: json,
            onSuccess,
            onError,
        });
    }

    async syncCountries(countries_supported: ICountry[], companyId: string, onSuccess?: Function, onError?: Function) {
        const json = {
            countries_supported: countries_supported.map(x => x.id),
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/overview/sync-countries`,
            data: json,
            onSuccess,
            onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    async loadDocuments(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/document`,
            onSuccess,
            onError,
            id: companyId,
        });
    }

    async downloadDoc(id: string, companyId: string, mime_type: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            headers: {
                "Content-Type": mime_type,
            },
            responseType: "blob",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/document/${id}`,
            onSuccess,
            onError,
        });
    }

    async downloadMediaGallery(id: string, image_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}media-gallery/${id}/${image_id}/download`,
            onSuccess,
            onError,
        });
    }

    async uploadDocument(options: DocUpload, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("document", options.document);
        data.append("title", options.title);
        if (options?.media_visibility) {
            data.append("media_visibility", options?.media_visibility);
        }
        if (options?.is_partner_content) {
            if (options?.thumbnail) {
                data.append("thumbnail", options.thumbnail!);
            }
            data.append("is_partner_content", options.is_partner_content ? "1" : "0");
        }
        if (options.description) data.append("description", options.description);
        options.tags.forEach((tagId: string) => {
            data.append("tags[]", tagId);
        });
        if (options.categories) data.append("categories[]", options.categories);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${options.companyId}/document/store`,
            data: data,
            onSuccess,
            onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    async uploadBrandableDocument(options: DocUpload, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("document", options.document);
        data.append("title", options.title);
        if (options?.media_visibility) {
            data.append("media_visibility", options?.media_visibility);
        }
        if (options?.is_partner_content) {
            if (options?.thumbnail) {
                data.append("thumbnail", options.thumbnail!);
            }
            data.append("is_partner_content", options.is_partner_content ? "1" : "0");
        }
        if (options.description) data.append("description", options.description);
        options.tags.forEach((tagId: string) => {
            data.append("tags[]", tagId);
        });
        if (options.categories) data.append("categories[]", options.categories);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${options.companyId}/brandable/store`,
            data: data,
            onSuccess,
            onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    async removeDoc(
        isBrandable: boolean = false,
        companyId: string,
        document: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const json = {
            document,
        };
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/${
                isBrandable ? "brandable" : "document"
            }/delete`,
            data: json,
            onSuccess,
            onError,
        });
    }

    async removeVideo(companyId: string, video: string, onSuccess?: Function, onError?: Function) {
        const json = {
            video,
        };
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/video/delete`,
            data: json,
            onSuccess,
            onError,
        });
    }

    async removeImage(removeData: DeleteImage, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}company/${removeData.id}/image/delete/${removeData.image_id}`,
            onSuccess,
            onError,
        });
    }

    getContentFeedCounts = async (id: string, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/company/${id}/content-feed/counts`,
            onSuccess,
            onError,
            id,
        });
    };

    getContentFeed = async (options: {id: string; page: number}, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/company/${options.id}/content-feed?paged=1&page=${options.page}`,
            onSuccess,
            onError,
        });
    };

    getProducts = async (id: string, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${id}/product`,
            onSuccess,
            onError,
        });
    };

    uploadBigFile(data: IFileQueue, id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${id}/video/store`,
            data,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }

    async editBrandableMedia(
        companyId: string,
        options: {
            id: string;
            thumbnail: File | null;
            title: string;
            description: string;
            duration: string;
            is_partner_content?: string | null;
            categories: string | null;
            tags: string[];
            handleUploadProgress: Function;
            media_visibility?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const formData = new FormData();
        if (options.title) formData.append("title", options.title);
        if (options.description) formData.append("description", options.description);
        if (options.thumbnail) formData.append("thumbnail", options.thumbnail);
        if (options.duration) formData.append("duration", options.duration);
        if (options.categories) formData.append("categories[]", options.categories);
        if (options.is_partner_content) formData.append("is_partner_content", options.is_partner_content);
        if (options?.media_visibility) formData.append("media_visibility", options.media_visibility);
        options.tags.forEach((tagId: string) => {
            formData.append("tags[]", tagId);
        });

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${companyId}/brandable/${options.id}/update`,
            data: formData,
            onSuccess: onSuccess,
            onError: onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    getAllContacts(company_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${company_id}/contacts`,
            onSuccess,
            onError,
        });
    }

    storeContact(
        company_id: string,
        data: {title: string; email: string; phone: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${company_id}/contacts/store`,
            onSuccess,
            onError,
            data,
        });
    }

    updateContact(
        company_id: string,
        data: {id: string; title: string; email: string; phone: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${company_id}/contacts/update`,
            onSuccess,
            onError,
            data,
        });
    }

    deleteContact(company_id: string, contact_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}profiles/vendor-basic/${company_id}/contacts/delete`,
            onSuccess,
            onError,
            data: {id: contact_id},
            options: {ignoreAbortController: true},
        });
    }
}

export const vendorProfileService = new VendorProfileService();
