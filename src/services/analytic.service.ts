import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IPaginatedTableRequest} from "../models";
import {apiService} from "./api.service";

class AnalyticService {
    store(action: string, subjectId: string, userAgent: string, onSuccess?: Function, onError?: Function) {
        const json = {
            action: action,
            subject_id: subjectId,
            custom_properties: {
                user_agent: userAgent,
            },
        };

        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics",
            data: json,
            onSuccess,
            onError,
        });
    }

    storeBasic(
        action: string,
        subject_id: string | undefined,
        userAgent: string,
        custom_properties?: any,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const json = {
            action: action,
            subject_id,
            custom_properties: {
                user_agent: userAgent,
                ...(custom_properties || {}),
            },
        };

        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics",
            data: json,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }

    storeBasicReferral(
        action: string,
        subject_id: string,
        subject_type: string,
        userAgent: string,
        custom_properties?: any,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const json = {
            action: action,
            subject_id,
            subject_type,
            custom_properties: {
                user_agent: userAgent,
                ...(custom_properties || {}),
            },
        };

        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics",
            data: json,
            onSuccess,
            onError,
        });
    }

    storeBulk(
        analyticObjects: Array<{action: string; subject_id: string; custom_properties: any}>,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics/bulk-store",
            data: {
                analytics: analyticObjects,
            },
            onSuccess,
            onError,
        });
    }

    add(action: string, subject_id: string, custom_properties?: any, onSuccess?: Function, onError?: Function) {
        const json = {
            action: action,
            subject_id: subject_id,
            custom_properties,
        };

        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics",
            data: json,
            onSuccess,
            onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    getVideoViews(requestObj: IPaginatedTableRequest, onSuccess?: Function, onError?: Function) {
        let parameters = "?paged=1";
        if (requestObj?.sortType) parameters = parameters.concat(`&sort=${requestObj?.sortType}`);
        if (requestObj?.page) parameters = parameters.concat(`&page=${requestObj?.page}`);
        if (requestObj?.orderBy) parameters = parameters.concat(`&order_by=${requestObj?.orderBy}`);
        if (requestObj?.search_word)
            parameters = parameters.concat(`&search_word=${requestObj?.search_word.replaceAll(" ", "%20")}`);
        if (requestObj?.categories)
            requestObj?.categories?.map(category => (parameters = parameters.concat(`&categories[]=${category}`)));
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/analytics/video-views" + parameters,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
    getVideoViewsDetail(videoId: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/analytics/video-views/${videoId}/details`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
    getVideoViewsDetailByDate(
        videoId: string,
        start_date: string | null,
        end_date: string | null,
        onSuccess: Function,
        onError: Function,
    ) {
        const data = Object.assign({}, start_date && {start_date}, end_date && {end_date});
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/analytics/video-views/${videoId}/views-by-date`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
    downloadCsvFileVideoDetail(
        videoId: string,
        start_date: string | null,
        end_date: string | null,
        mime_type: string,
        onSuccess: Function,
        onError: Function,
    ) {
        const data = Object.assign({}, start_date && {start_date}, end_date && {end_date});
        return apiService({
            method: "post",
            headers: {
                "Content-Type": mime_type,
            },
            url: `${SERVER_API_BASE_V1}admin/analytics/video-views/${videoId}/generate-csv`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    // Must be one of the following: blog, media, mediaGallery, shoutOut, userProfile, vendorProfile, mspProfile
    likeContent(subjectId: string, subject_type: "userProfile" | string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}analytics`,
            data: {
                subject_id: subjectId,
                subject_type: subject_type,
                action: "like",
            },
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    dislikeContent(subjectId: string, subject_type: "userProfile" | string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}analytics`,
            data: {
                subject_id: subjectId,
                subject_type: subject_type,
                action: "dislike",
            },
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    getProfilesWhoLiked(
        subject_id: string,
        subject_type: string,
        pageNum: number,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}analytics/profiles-who-liked?subject_id=${subject_id}&subject_type=${subject_type}&paged=1&page=${
                pageNum || 0
            }`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    storePartnerAnalytic(
        action:
            | "partnerPortalView"
            | "partnerPortalContentClick"
            | "partnerPortalContentDownload"
            | "partnerPortalChatOpen",
        subjectId: string,
        options?: {
            partner_page_section_content_type?: "mediaType" | "blog";
            partner_page_section_type?: "templates_layout" | "articles_layout" | "videos_layout" | "documents_layout";
            partner_page_section_content_id?: string;
            partner_page_section_id?: string;
            company_id?: string | null;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let custom_properties: any = null;
        if (options) {
            custom_properties = {
                partner_page_section_content_type: options.partner_page_section_content_type,
                partner_page_section_content_id: options.partner_page_section_content_id,
                partner_page_section_id: options.partner_page_section_id || "",
                partner_page_section_type: options.partner_page_section_type,
                company_id: options.company_id || "",
            };
        }
        const json = {
            action: action,
            subject_id: subjectId,
        };
        if (custom_properties) {
            json["custom_properties"] = custom_properties;
        }

        return apiService({
            method: "POST",
            url: SERVER_API_BASE_V1 + "analytics",
            data: json,
            onSuccess,
            onError,
        });
    }
}

export const analyticService = new AnalyticService();
