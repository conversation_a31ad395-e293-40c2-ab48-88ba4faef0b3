import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IObjAny} from "../models";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";

class CategoriesService {
    getAllCategories(
        onSuccess?: Function,
        onError?: Function,
        parent_id: string = "",
        is_hidden: number = 0,
        company_type_categories?: "include" | "exclude" | "restrict",
        default_categories?: "1" | "0",
        section?: "PLAID",
        asCompanyIdOverwrite?: string,
    ) {
        let params = "?";
        if (parent_id) {
            params += params === "?" ? `parent_id=${parent_id}` : `&parent_id=${parent_id}`;
        }
        if (is_hidden) {
            params += params === "?" ? `is_hidden=${is_hidden}` : `&is_hidden=${is_hidden}`;
        }
        if (company_type_categories !== undefined) {
            params +=
                params === "?"
                    ? `company_type_categories=${company_type_categories}`
                    : `&company_type_categories=${company_type_categories}`;
        }
        if (default_categories !== undefined) {
            params +=
                params === "?"
                    ? `default_categories=${default_categories}`
                    : `&default_categories=${default_categories}`;
        }
        if (section !== undefined) {
            params += params === "?" ? `section=${section}` : `&section=${section}`;
        }
        if (asCompanyIdOverwrite) {
            params +=
                params === "?" ? `as_company_id=${asCompanyIdOverwrite}` : `&as_company_id=${asCompanyIdOverwrite}`;
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories` + (params !== "?" ? `${params}` : ""),
            onSuccess: onSuccess,
            onError: onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    getAllAdmin(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/categories?paged=0&order_by=name`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getAllAdminPaged(filters: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        if (filters?.dynamic) {
            Object.keys(filters.dynamic).forEach(key => {
                if (filters?.dynamic?.[key] === undefined) {
                    delete filters?.dynamic?.[key];
                }
            });
        }
        const search = buildApiFilters(filters);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/categories${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getAllAdminFilters(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/categories/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getAllParentCategories(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories/show-parents`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCategoryVendors(id: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories/${id}/vendors`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCategoryProducts(parent_id: string, id: string, order_by: any, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories/${parent_id}/products?subcategory_id=${id}${
                order_by ? `&order_by=${order_by}` : ""
            }`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    delete(id: string) {
        const data = {id};
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}admin/categories/delete`,
            data,
        });
    }

    update(updatedCategory: IObjAny) {
        if (!updatedCategory.description) delete updatedCategory.description;
        if (!updatedCategory.parent_id) delete updatedCategory.parent_id;
        if (!updatedCategory.icon_name) delete updatedCategory.icon_name;
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}admin/categories/update`,
            data: updatedCategory,
        });
    }

    create(updatedCategory: IObjAny) {
        if (!updatedCategory?.parent_id) delete updatedCategory.parent_id;
        if (!updatedCategory.icon_name) delete updatedCategory.icon_name;
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/categories/store`,
            data: updatedCategory,
        });
    }

    getPopularCategories(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories/popular`,
            onSuccess,
            onError,
        });
    }

    getTrendingProducts(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products/top-trending`,
            onSuccess,
            onError,
        });
    }

    getNaviStackCategories(config?: {companyTypes?: string}, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `categories?load_my_stack_categories=1${
                    config?.companyTypes ? `&company_type=${config.companyTypes}&default_categories=1` : ""
                }`,
            onSuccess,
            onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }
}

export const categoriesService = new CategoriesService();
