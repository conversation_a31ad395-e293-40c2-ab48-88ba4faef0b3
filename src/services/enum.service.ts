import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class EnumService {
    getInquiryTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/inquirytype",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getQuestionTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/pollquestionType",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPollCategory(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/pollCategory",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getUserProfileTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/UserProfileTypes",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getReviewOrderFilter(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/ReviewOrderFilter",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPricingTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/ProductPricingType",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getLayoutTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/PartnerPageSectionLayout",
            onError,
            onSuccess,
        });
    }

    getProductReviewCSVColumns(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/ReviewQuestionCSVColumn",
            onError,
            onSuccess,
        });
    }

    getAdditionalWebsiteType(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/ProfileAdditionalWebsiteTypes",
            onError,
            onSuccess,
        });
    }

    getFocusTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/FocusTypes",
            onError,
            onSuccess,
        });
    }

    getTangoCatalogType(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/TangoCatalogType",
            onError,
            onSuccess,
        });
    }

    getReviewStatusType(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/ReviewStatus",
            onError,
            onSuccess,
        });
    }

    getIncentiveStatusType(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/private/IncentiveStatus",
            onError,
            onSuccess,
        });
    }

    getContractRecurrence(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/ContractRecurrence",
            onError,
            onSuccess,
        });
    }

    getCompanyTypeGroups(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "enum/PrmMediaVisibility",
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const enumService = new EnumService();
