import axios from "axios";
import Row from "react-bootstrap/Row";
import EpisodeComponent from "../components/Podcast/Episode.component";
import {DateTime} from "luxon";
import {PLAYLIST_ID, YT_API_BASE, YT_KEY} from "../constants/commonStrings.constant";

const ytFetchVideos = async (maxResults: number = 25) => {
    return axios.get(
        YT_API_BASE +
            "playlistItems?part=snippet%2CcontentDetails&maxResults=" +
            maxResults +
            "&playlistId=" +
            PLAYLIST_ID +
            "&key=" +
            YT_KEY,
    );
};

export const renderVideoGrid = (data: any = []) => {
    const newGrid: JSX.Element = (
        <Row>
            {data?.map((vid: any) => (
                <EpisodeComponent
                    key={vid.id}
                    thumbnail={vid.snippet.thumbnails.high}
                    title={vid.snippet.title}
                    publishDate={vid.contentDetails.videoPublishedAt}
                    description={vid.snippet.description}
                    channel={vid.snippet.channelTitle}
                    videoUrl={vid.snippet.resourceId.videoId}
                    pid={vid.id}
                    hideBtn
                />
            ))}
        </Row>
    );
    return newGrid;
};

export const getVideosGrid = async () => {
    let localPlaylist: string = localStorage.videoPlaylist;
    const playlistDue: string = localStorage.playlistDue;
    const now: DateTime = DateTime.now();
    if (playlistDue !== undefined && DateTime.fromISO(playlistDue) < now) {
        localPlaylist = "";
    }
    if (localPlaylist! && localPlaylist.length > 2) {
        return JSON.parse(localPlaylist);
    }
    try {
        const response = await ytFetchVideos();
        localStorage.videoPlaylist = await JSON.stringify(response?.data?.items?.reverse());
        localStorage.playlistDue = await now.plus({hours: 24}).toString();
        return response?.data?.items;
    } catch (error) {
        return error;
    }
};
