import {apiService} from "./api.service";
import {CHANNEL_PROGRAM_COMPANY_ID, SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IPaginatedEventTable} from "../models";
import {IIndustryEventBulk, IIndustryEventCreate, IIndustryEventUpdate} from "../Interfaces/events.interface";
import {buildApiFilters} from "../utils/apiHelpers.util";

class EventService {
    getFeaturedSpeakers(id: string) {
        return apiService.get(`${SERVER_API_BASE_V1}pitch-events/featured-speakers/${id}`);
    }

    getNotStarted(onSuccess: Function, onError: Function, page: string = "") {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "pitch-events/not-started" + (page !== "" ? `?page=${page}` : ""),
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getAll(onSuccess: Function, onError: Function, page: string = "") {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "pitch-events" + (page !== "" ? `?page=${page}` : ""),
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getNames(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-events-names",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPitchDetails(id: string, onSuccess: Function, onError: Function) {
        const json = {
            id,
        };
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/detail",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    attendEvent(pitch_event_id: string, user_id: string, onSuccess: Function, onError: Function) {
        const json = {
            pitch_event_id,
            user_id,
        };
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/attend-the-event",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    registerForPitchEvent(pitch_event_id: string, user_id: string) {
        const json = {
            pitch_event_id,
            user_id,
        };
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/attend-the-event",
            data: json,
        });
    }

    getUserPitches(user_id: string, onSuccess: Function, onError: Function) {
        const json = {
            user_id,
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}pitch-events/show-pitches-user-is-attending`,
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    unsuscribeEvent(pitch_event_id: string, user_id: string) {
        const json = {
            pitch_event_id,
            user_id,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/unsubscribe-the-event",
            data: json,
        });
    }

    unsubscribeEmailWithToken(unsubscription_token: string) {
        const json = {unsubscription_token};
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "industry-events/unsubscribe-with-token",
            data: json,
        });
    }

    delete(id: string, onSuccess: Function, onError: Function) {
        const json = {
            id,
        };

        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + "pitch-events/delete",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    create(
        name: string,
        description: string,
        start_date: string,
        banner_image: any,
        speaker_link: string,
        viewer_link: string,
        third_party_id: string,
        hubspot_internal_id: string | null,
        addEvent_id: string | null,
        onSuccess: Function,
        onError: Function,
    ) {
        const data = new FormData();
        data.append("name", name);
        data.append("description", description);
        data.append("start_date", start_date);
        data.append("third_party_id", third_party_id);
        data.append("hubspot_internal_id", hubspot_internal_id ?? "");
        data.append("speaker_link", speaker_link);
        data.append("viewer_link", viewer_link);
        data.append("addevent_id", addEvent_id ?? "");
        if (banner_image) {
            data.append("banner_image", banner_image);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + "pitch-events/store",
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    update(
        id: string,
        name: string,
        description: string,
        start_date: string,
        speaker_link: string,
        viewer_link: string,
        banner_image: any,
        third_party_id: string,
        hubspot_internal_id: string | null,
        addEvent_id: string | null,
        onSuccess: Function,
        onError: Function,
    ) {
        const data = new FormData();
        data.append("id", id);
        data.append("name", name);
        data.append("description", description);
        data.append("start_date", start_date);
        data.append("third_party_id", third_party_id);
        data.append("hubspot_internal_id", hubspot_internal_id ?? "");
        data.append("speaker_link", speaker_link);
        data.append("viewer_link", viewer_link);
        data.append("addevent_id", addEvent_id ?? "");
        if (banner_image) {
            data.append("banner_image", banner_image);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + "pitch-events/update",
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadModInfo(pitch_event_id: string, onSuccess: Function, onError: Function) {
        const json = {
            pitch_event_id,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/load-moderator-info",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadModChatHistory(user_id: string, pusher_channel: string, onSuccess: Function, onError: Function) {
        const json = {
            user_id,
            pusher_channel,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/load-user-mod-msgs",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadPublicChatHistory(pusherChannel: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/load-public-chat-msgs",
            data: {pusher_channel: pusherChannel},
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    loadViewerInfo(pitch_event_id: string, onSuccess: Function, onError: Function) {
        const json = {
            pitch_event_id,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/load-info",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    end(pitch_event_id: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `pitch-events/${pitch_event_id}/end`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPreviousSpeakers(onSuccess: Function, onError: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `pitch-events/last-pitch-scheduled-vendors`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getIndustry(
        params?: {
            start_date?: string;
            end_date?: string;
            past_events?: boolean;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let qsParams = `?past_events=${!!params?.past_events ? "true" : "false"}`;
        qsParams +=
            !!params?.start_date && !!params?.end_date
                ? `&start_date=${params.start_date}&end_date=${params.end_date}`
                : "";

        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `industry-events${qsParams}`,
            onError: onError,
            onSuccess: onSuccess,
            options: {ignoreAbortController: true},
        });
    }

    getAdminIndustry(requestObj: IPaginatedEventTable, onSuccess?: Function, onError?: Function) {
        let parameters = "?paged=1";
        if (requestObj?.status)
            requestObj.status?.forEach(status => (parameters = parameters.concat(`&status[]=${status}`)));
        if (requestObj?.sortType) parameters = parameters.concat(`&sort=${requestObj?.sortType}`);
        if (requestObj?.page) parameters = parameters.concat(`&page=${requestObj?.page}`);
        if (requestObj?.orderBy) parameters = parameters.concat(`&order_by=${requestObj?.orderBy}`);
        if (requestObj?.display_past_events) parameters = parameters.concat(`&display_past_events=1`);
        if (requestObj?.search_word)
            parameters = parameters.concat(`&search_word=${requestObj?.search_word.replaceAll(" ", "%20")}`);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/industry-events${parameters}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getIndustryFiltered(company_id: string, requestObj: IPaginatedEventTable) {
        let parameters = "";
        if (requestObj?.sortType) parameters = parameters.concat(`&sort=${requestObj?.sortType}`);
        if (requestObj?.page) parameters = parameters.concat(`&page=${requestObj?.page}`);
        if (requestObj?.orderBy) parameters = parameters.concat(`&order_by=${requestObj?.orderBy}`);
        if (requestObj?.search_word)
            parameters = parameters.concat(`&search_word=${requestObj?.search_word.replaceAll(" ", "%20")}`);
        if (requestObj?.display_past_events) parameters = parameters.concat(`&display_past_events=1`);
        if (requestObj?.status)
            requestObj.status?.forEach(status => (parameters = parameters.concat(`&status[]=${status}`)));
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `industry-events/show-all?subject_id=${company_id}&subject_type=companyProfile${parameters}`,
        });
    }

    createIndustryEvent(
        data: IIndustryEventCreate,
        onSuccess: Function,
        onError: Function,
        subject_id?: string,
        is_admin?: boolean,
    ) {
        const formData = new FormData();
        if (data.cp_event) {
            formData.append("cp_event", data.cp_event);
        }
        if (data.image) {
            formData.append("image", data.image);
        }
        if (data.description) {
            formData.append("description", data.description);
        }
        if (data.end_date) {
            formData.append("end_date", data.end_date.toString());
        }
        if (data.event_name) {
            formData.append("event_name", data.event_name);
        }
        if (data.feature_flag) {
            formData.append("feature_flag", data.feature_flag ? "1" : "0");
        }
        if (data.feature_start_date) {
            formData.append("feature_start_date", data.feature_start_date);
        }
        if (data.feature_end_date) {
            formData.append("feature_end_date", data.feature_end_date);
        }
        if (data.first_send) {
            formData.append("first_send", data.first_send);
        }
        if (data.in_person_or_virtual) {
            formData.append("in_person_or_virtual", data.in_person_or_virtual);
        }
        if (data.link) {
            formData.append("link", data.link);
        }
        if (data.location) {
            formData.append("location", data.location);
        }
        if (data.organization) {
            formData.append("organization", data.organization);
        }
        if (data.start_date) {
            formData.append("start_date", data.start_date.toString());
        }
        if (data.status) {
            formData.append("status", data.status);
        }
        if (data.time_zone) {
            formData.append("time_zone", data.time_zone);
        }
        if (data.state) {
            formData.append("state", data.state);
        }
        if (data.city) {
            formData.append("city", data.city);
        }
        if (data.country) {
            formData.append("country", data.country);
        }
        if (data.zip) {
            formData.append("zip", data.zip);
        }
        if (subject_id) {
            data["subject_id"] = subject_id;
            data["subject_type"] = "companyProfile";
            formData.append("subject_id", subject_id);
            formData.append("subject_type", "companyProfile");
        } else if (data.cp_event === "1") {
            data["subject_id"] = CHANNEL_PROGRAM_COMPANY_ID;
            data["subject_type"] = "companyProfile";
            formData.append("subject_id", CHANNEL_PROGRAM_COMPANY_ID);
            formData.append("subjct_type", "companyProfile");
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + `${is_admin ? "admin/" : ""}industry-events/store`,
            data: formData,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateIndustryEvent(data: IIndustryEventUpdate, subject_id?: string) {
        const formData = new FormData();
        if (data.cp_event) {
            formData.append("cp_event", data.cp_event);
        }
        if (data.id) {
            formData.append("id", data.id);
        }
        if (data.image) {
            formData.append("image", data.image);
        }
        if (data.description) {
            formData.append("description", data.description);
        }
        if (data.end_date) {
            formData.append("end_date", data.end_date.toString());
        }
        if (data.event_name) {
            formData.append("event_name", data.event_name);
        }
        if (data.feature_flag !== undefined) {
            formData.append("feature_flag", data.feature_flag ? "1" : "0");
        }
        if (data.feature_start_date) {
            formData.append("feature_start_date", data.feature_start_date);
        }
        if (data.feature_end_date) {
            formData.append("feature_end_date", data.feature_end_date);
        }
        if (data.in_person_or_virtual) {
            formData.append("in_person_or_virtual", data.in_person_or_virtual);
        }
        if (data.link) {
            formData.append("link", data.link);
        }
        if (data.location) {
            formData.append("location", data.location);
        }
        if (data.organization) {
            formData.append("organization", data.organization);
        }
        if (data.start_date) {
            formData.append("start_date", data.start_date.toString());
        }
        if (data.status) {
            formData.append("status", data.status);
        }
        if (data.time_zone) {
            formData.append("time_zone", data.time_zone);
        }
        if (subject_id) {
            data["subject_id"] = subject_id;
            data["subject_type"] = "companyProfile";
            formData.append("subject_id", subject_id);
            formData.append("subject_type", "companyProfile");
        }
        if (data.reason) {
            formData.append("reason", data.reason);
        }
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `${data.isAdmin ? "admin/" : ""}industry-events/update`,
            data: formData,
        });
    }

    archiveToggle(data: IIndustryEventUpdate) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `industry-events/update`,
            data,
        });
    }

    bulkAction(data: IIndustryEventBulk, action: "update-status" | "delete") {
        return apiService({
            method: action === "delete" ? "delete" : "put",
            url: SERVER_API_BASE_V1 + `admin/industry-events/bulk/${action}`,
            data,
        });
    }

    runPollCSVReport(eventId: string, onSuccess: Function, onError: Function) {
        const json = {
            pitch_event_id: eventId,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `pitch-events/poll-answer/download-csv-file`,
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    runMSPEnteredCSVReport(eventId: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/reports/pitch/mspusers/${eventId}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    runVendorEnteredCSVReport(eventId: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/reports/pitch/vendorusers/${eventId}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    runVendorReport(eventId: string, vendorId: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/pitch-events/${eventId}/report/complete/${vendorId}`,
            responseType: "blob",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getAttendeeCount(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/analytics/events/attendeecount",
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPollQuestionByEventId(pitch_event_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/pitch-events/questions?pitch_event_id=${pitch_event_id}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateMSPAttendants(
        data: {
            pitch_event_id: string;
            poll_question_id: string;
            user_id: string;
            is_spam: boolean;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "admin/pitch-events/msp-attendants/update",
            data,
            onError: onError,
            onSuccess: onSuccess,
            options: {ignoreAbortController: true},
        });
    }

    getMSPAttendants(
        data: {
            pitch_id: string;
            question_id: string;
            search_word?: string;
            page?: number;
            sortType?: string;
            orderBy?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "&paged=1";
        if (data.search_word) {
            search += `&search_word=${data.search_word}`;
        }
        if (data.page) {
            search += `&page=${data.page}`;
        }
        if (data.sortType) {
            search += `&sort=${data.sortType}`;
        }
        if (data.orderBy) {
            search += `&order_by=users.${data.orderBy}`;
        }
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `admin/pitch-events/msp-attendants?pitch_event_id=${data.pitch_id}&poll_question_id=${data.question_id}${search}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getPastEvents(
        options: {
            paged?: boolean;
            items_per_page?: number;
            page?: number;
            order_by?: string;
            sort?: string;
            search_word?: string;
            start_date?: string;
            end_date?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = `?paged=1`;
        if (options.page) {
            search += `&page=${options.page}`;
        }
        if (options.search_word) {
            search += `&search_word=${options.search_word}`;
        }
        if (options.sort) {
            search += `&sort=${options.sort.toUpperCase()}`;
        }
        if (options.order_by) {
            search += `&order_by=${options.order_by}`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/pitch-events/past" + search,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    searchIndustryCalendar(
        options: {
            search_word?: string;
            start_date?: string;
            end_date?: string;
            event_type?: string;
            location?: string;
            past_events?: boolean;
            presenters?: string[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        const prependValue = (searchValue?: string) => (searchValue?.includes("?") ? "&" : "?");
        if (options.search_word) {
            search += prependValue(search) + `search_word=${options.search_word}`;
        }
        if (options.start_date) {
            search += prependValue(search) + `start_date=${options.start_date}`;
        }
        if (options.end_date) {
            search += prependValue(search) + `end_date=${options.end_date}`;
        }
        if (!!options.past_events) {
            search += prependValue(search) + `past_events=true`;
        }
        if (options.location) {
            search += prependValue(search) + `location=${options.location}`;
        }
        if (options.event_type) {
            if (options.event_type === "cp") {
                search += prependValue(search) + `cp_event=1`;
            } else {
                search += prependValue(search) + `event_type=${options.event_type}&cp_event=0`;
            }
        }
        if (options.presenters) {
            options.presenters.forEach(s => {
                search += prependValue(search) + `&presenters[]=${s}`;
            });
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `industry-events${search}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    exportIndustryEvents(
        options: {
            search?: string;
            sort?: string;
            order_by?: string;
            start_date?: string;
            end_date?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        if (options.start_date) {
            search += `?start_date=${options.start_date}`;
        }
        if (options.end_date) {
            search += `&end_date=${options.end_date}`;
        }
        if (options.search) {
            search += `${search ? "&" : "?"}search[event_name]=${options.search}`;
        }
        if (options.sort) {
            search += `&sort=${options.sort.toUpperCase()}`;
        }
        if (options.order_by) {
            search += `&order_by=${options.order_by}`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "industry-events/export-calendar-to-csv" + search,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    checkFeatureFlag(
        data: {
            feature_flag: boolean;
            feature_start_date: string;
            feature_end_date: string;
            subject_id?: string;
            id?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "admin/industry-events/check-feature-flag",
            onError: onError,
            onSuccess: onSuccess,
            data,
        });
    }

    getPartnersEventData(
        company_id: string,
        options: {
            event_company_id?: string;
            exclude_cp_events?: boolean;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const searchOptions = {...options, exclude_cp_events: options.exclude_cp_events ? "1" : "0"};
        if (!searchOptions.event_company_id) {
            delete searchOptions.event_company_id;
        }
        const search = buildApiFilters({
            dynamic: searchOptions,
        });
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partners/${company_id}/industry-events${search}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    searchPresenters(search_word: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `filters/industry-calendar-presenters?search_word=${search_word}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const eventService = new EventService();
