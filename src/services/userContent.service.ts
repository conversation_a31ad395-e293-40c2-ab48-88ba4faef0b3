import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {TFiltersQueryParams} from "../hooks/fetches/useFilters";
import {buildFilterQuery} from "../utils/filters.util";
import {IRejectUserContentForm, TStatusScopeEntityTypes} from "../Interfaces/statusScope.interface";
import {IApproveUserContentCompanyForm, IApproveUserContentProductForm} from "../Interfaces/adminUserContent.interface";

class UserContentService {
    // Get all available filter options for entities created by users
    getAdminUserContentFilter(queryParams: TFiltersQueryParams = {}, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: buildFilterQuery(`${SERVER_API_BASE_V1}admin/user-content/filters`, queryParams),
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    // Get all available reasons to reject an entity created by a user
    getAdminUserContentRejectOptions(entity: TStatusScopeEntityTypes, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/user-content/reject-options?entity=${entity}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    // Reject a list of entityes created by a user
    adminRejectUserContent(data: IRejectUserContentForm, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/user-content/reject`,
            data,
            onSuccess,
            onError,
        });
    }

    /**
     * Methods for companies
     */
    // Get all Companies created by users
    getAdminUserContentCompanies(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/user-content/companies/show-all${filters}`,
            onSuccess: onSuccess,
            onError: onError,
            options: {ignoreAbortController: true},
        });
    }

    // Approve a company created by a user
    adminApproveUserContentCompany(
        companyId: string,
        data: IApproveUserContentCompanyForm,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/user-content/companies/${companyId}/approve`,
            data,
            onSuccess,
            onError,
        });
    }

    /**
     * Methods for products
     */
    // Get all Products created by users
    getAdminUserContentProducts(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/user-content/products/show-all${filters}`,
            onSuccess: onSuccess,
            onError: onError,
            options: {ignoreAbortController: true},
        });
    }

    // Approve a product created by a user
    adminApproveUserContentProduct(
        productId: string,
        data: IApproveUserContentProductForm,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/user-content/products/${productId}/approve`,
            data,
            onSuccess,
            onError,
        });
    }
}

export const userContentService = new UserContentService();
