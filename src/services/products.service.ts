import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {MediaTypes} from "../enums/mediaTypes.enum";
import {IProductFeature, IProductPricing} from "../Interfaces/products.interface";
import {DeleteMediaProduct} from "../Interfaces/queries.interface";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";

class ProductService {
    getProductsByVendorId(company_id: string, onSuccess, onError) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${company_id}/product`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getProductById(company_id: string, product_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${company_id}/product/${product_id}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getByFriendlyUrl(friendly_url: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products/${friendly_url}`,
            onSuccess,
            onError,
        });
    }

    storeProduct(
        options: {
            company_id: string;
            name: string;
            description: string;
            overview: string;
            url: string;
            categories: string[];
            features?: IProductFeature[];
            pricing?: IProductPricing[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {
            name: options.name,
            overview: options.overview,
            description: options.description,
            url: options.url,
            product_categories_ids: options.categories,
            features: options.features,
            pricing: options.pricing,
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}company/${options.company_id}/product/store`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    delete(options: {company_id: string; id: string}, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}company/${options.company_id}/product/delete/${options.id}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    update(
        options: {
            company_id: string;
            id: string;
            name: string;
            description: string;
            overview: string;
            url: string;
            categories: string[];
            features?: IProductFeature[];
            pricing?: IProductPricing[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const json = {
            id: options.id,
            name: options.name,
            description: options.description,
            overview: options.overview,
            url: options.url,
            product_categories_ids: options.categories,
            features: options.features,
            pricing: options.pricing,
        };
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}company/${options.company_id}/product/update`,
            data: json,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    uploadImage(
        options: {company_id: string; product_id: string; image: File; handleUploadProgress: Function},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("image", options.image);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}company/${options.company_id}/product/${options.product_id}/image/upload`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    uploadVideo(
        company_id: string,
        product_id: string,
        video: File,
        thumbnail: File,
        handleUploadProgress: Function,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("video", video);
        data.append("thumbnail", thumbnail);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}company/${company_id}/product/${product_id}/video/upload`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
            onUploadProgress: function (progressEvent: any) {
                handleUploadProgress(progressEvent);
            },
        });
    }

    deleteImageOrVideo(options: DeleteMediaProduct, onSuccess?: Function, onError?: Function) {
        const data = {
            id: options.id,
        };
        const deleteType = options.imageOrVideo === MediaTypes.Image ? "image" : "video";
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}company/${options.company_id}/product/${options.product_id}/${deleteType}/delete`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getReviews(
        friendly_url: string,
        meta: {page?: number; items_per_page?: number; review_id?: string | null; review_order?: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products/${friendly_url}/reviews?paged=1&page=${meta.page ?? 1}${
                meta.items_per_page ? "&items_per_page=" + meta.items_per_page : ""
            }${meta.review_id ? "&review_id=" + meta.review_id : ""}
            ${meta.review_order ? "&review_order=" + meta.review_order : ""}`,
            onSuccess,
            onError,
        });
    }

    addProductFeature(
        companyId: string,
        productId: any,
        data?: IProductFeature,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            data,
            url: `${SERVER_API_BASE_V1}company/${companyId}/product/${productId}/features/store`,
            onSuccess,
            onError,
        });
    }

    getProductList(
        data: {
            paged?: boolean;
            page?: number;
            items_per_page?: number;
            order_by?: string;
            sort?: string;
            categories?: string[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        const prependValue = (searchValue?: string) => (searchValue?.includes("?") ? "&" : "?");
        if (data.paged) {
            search += prependValue(search) + `paged=1`;
        }
        if (data.page) {
            search += prependValue(search) + `page=${data.page}`;
        }
        if (data.items_per_page) {
            search += prependValue(search) + `items_per_page=${data.items_per_page}`;
        }
        if (data.categories) {
            data.categories.forEach((c: string) => {
                search += prependValue(search) + `categories[]=${c}`;
            });
        }
        if (data.order_by) {
            search += prependValue(search) + `order_by=${data.order_by}`;
        }
        if (data.sort) {
            search += prependValue(search) + `sort=${data.sort}`;
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products/graph-listing${search ? search : ""}`,
            onSuccess,
            onError,
            options: {
                ignoreAbortController: false,
            },
        });
    }

    searchProducts(
        options?: {
            paged?: number;
            page?: number;
            items_per_page?: number;
            search_word?: string;
            order_by?: string;
            sort?: string;
            rating?: number;
            category_id?: string;
            company_id?: string | undefined;
            msp_company_id?: string | undefined;
            use_fuzzy_search?: boolean | undefined;
            ignore_ids?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        if (!options?.search_word) {
            delete options?.search_word;
        }
        if (!options?.category_id) {
            delete options?.category_id;
        }
        const search = buildApiFilters({dynamic: options || {}} as any);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products/search${search}`,
            onSuccess,
            onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    searchCustomerProducts(
        company_id: string,
        options: IDynamicFiltersOptions,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const search = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}customer/${company_id}/products${search}`,
            onSuccess,
            onError,
        });
    }
}

export const productService = new ProductService();
