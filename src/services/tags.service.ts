import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {ITag} from "../Interfaces/tags.interface";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";

class TagsService {
    getAll(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}tags`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    search(filters: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        if (filters?.dynamic?.created_at === undefined) {
            delete filters.dynamic?.created_at;
        }
        if (filters?.dynamic?.is_hidden === undefined) {
            delete filters.dynamic?.is_hidden;
        }
        const search = buildApiFilters(filters);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/tags/search${search}`,
            onSuccess: onSuccess,
            onError: onError,
            id: JSON.stringify(filters),
        });
    }

    searchFilters(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/tags/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    create(name: string, is_hidden: boolean, onSuccess?: Function, onError?: Function) {
        const data = {name, is_hidden};
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/tags/store`,
            data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    update(updateObj: ITag, onSuccess?: Function, onError?: Function) {
        const data = {...updateObj};
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}admin/tags/update`,
            data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const tagsService = new TagsService();
