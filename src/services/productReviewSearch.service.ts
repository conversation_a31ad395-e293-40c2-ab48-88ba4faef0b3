import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class ProductReviewSearchService {
    getAllProducts(
        options: {
            paged?: boolean;
            page?: number;
            items_per_page?: number;
            search_word?: string;
            sortType?: string;
            orderBy?: string;
            productReviewRating?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let parameters = "?paged=1";
        if (!options?.paged) parameters = "?paged=0";
        if (options?.sortType) parameters = parameters.concat(`&sort=${options?.sortType}`);
        if (options?.page) parameters = parameters.concat(`&page=${options?.page}`);
        if (options?.orderBy) parameters = parameters.concat(`&order_by=${options?.orderBy}`);
        if (options?.items_per_page) parameters = parameters.concat(`&items_per_page=${options?.items_per_page}`);
        if (options?.search_word)
            parameters = parameters.concat(`&search_word=${encodeURIComponent(options?.search_word)}`);
        if (options?.productReviewRating) parameters = parameters.concat(`&rating=${options?.productReviewRating}`);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}products${parameters}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const productReviewSearchService = new ProductReviewSearchService();
