import type {AxiosResponse} from "axios";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import type {TPageInnerFiltersResponse} from "../Interfaces/filters.interface";
import type {IFolderContentStoreData} from "../Interfaces/folders.interface";
import {buildApiFilters, type IDynamicFiltersOptions} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";
import {PORTAL_VIBILITY_GROUP_STRINGS, PORTAL_VISIBILITY_GROUPS} from "../constants/visibilityLevels.constant";

class FolderServices {
    async postMethod() {
        return new Promise(resolve => setInterval(resolve, 2000));
    }

    async createFolder(
        companyId: string,
        folderDetails: {folder_name: string; description: string | null; tags: string[]; color?: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const {folder_name, description, tags, color} = folderDetails;
        const data = {folder_name, description, tags, ...(color && {color})};

        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/store`,
            data,
            onSuccess,
            onError,
            id: companyId + "createFolder",
        });
    }

    async updateFolder(
        companyId: string,
        folderDetails: {folder_name: string; description: string | null; tags: string[]; color?: string; id?: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const {folder_name, description, tags, color, id} = folderDetails;
        const data = {folder_name, description, tags, id, ...(color && {color})};

        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/update`,
            data,
            onSuccess,
            onError,
            id: companyId + "updateFolder",
        });
    }

    async deleteFolder(companyId: string, folderId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/delete`,
            data: {id: folderId},
            onSuccess,
            onError,
            id: companyId + "deleteFolder",
        });
    }

    async getAllFolders<T>(
        companyId: string,
        options: IDynamicFiltersOptions & {media_visibility?: PORTAL_VIBILITY_GROUP_STRINGS},
        onSuccess?: (r: T) => void,
        onError?: Function,
    ) {
        let filters = buildApiFilters(options);
        if (options?.media_visibility) {
            filters += `${filters ? "&" : "?"}media_visibility[]=${options.media_visibility}&media_visibility[]=${
                PORTAL_VISIBILITY_GROUPS.ALL
            }`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `folder/${companyId}${filters}`,
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    async getFolderFilters<T>(companyId: string, onSuccess?: (r: T) => void, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `folder/${companyId}/filters`,
            onError: onError,
            onSuccess: onSuccess,
            id: companyId + "getFolderFilters",
        });
    }

    async storeFiles(
        companyId: string,
        data: {contents: IFolderContentStoreData[]},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/content/store-multiple`,
            data,
            onSuccess,
            onError,
            id: companyId + "storeFiles",
        });
    }

    async reorderFolders(companyId: string, data, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/reorder`,
            data,
            onSuccess,
            onError,
            id: companyId + "reorderFolders",
        });
    }

    async storeMultipleFiles(
        companyId: string,
        data: {contents: IFolderContentStoreData[]},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/content/store-multiple`,
            data,
            onSuccess,
            onError,
            id: companyId + "storeMultipleFiles",
        });
    }

    async downloadFolder(companyId: string, folderId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/content/download?folder_id=${folderId}`,
            responseType: "arraybuffer",
            onSuccess,
            onError,
            id: companyId + "downloadFolder",
        });
    }

    async getFolderFiles<T>(
        companyId: string,
        options: IDynamicFiltersOptions & {media_visibility?: PORTAL_VIBILITY_GROUP_STRINGS},
        onSuccess?: (r: T) => void,
        onError?: Function,
    ) {
        let filters = buildApiFilters(options);
        if (options?.media_visibility) {
            filters += `${filters ? "&" : "?"}media_visibility[]=${options.media_visibility}&media_visibility[]=${
                PORTAL_VISIBILITY_GROUPS.ALL
            }`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `folder/${companyId}/content${filters}`,
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    async getFolderContentsFilters<T>(folderId: string, onSuccess?: (r: T) => void, onError?: Function) {
        return apiService<TPageInnerFiltersResponse, AxiosResponse<TPageInnerFiltersResponse>>({
            method: "get",
            url: SERVER_API_BASE_V1 + `folder/${folderId}/content/filters`,
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    async deleteSubjectFromFolder(
        companyId: string,
        item_id: string,
        folderId: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}folder/${companyId}/content/delete`,
            data: {content_id: item_id, folder_id: folderId},
            onSuccess,
            onError,
            id: companyId + "deleteSubjectFromFolder",
        });
    }

    async updateStatus(
        data: {
            companyId: string;
            contentId: string; // Not the item id, id from the relation
            folderId: string;
            status: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}folder/${data.companyId}/content/update-status`,
            data: {id: data.contentId, company_folder_id: data.folderId, status: data.status},
            onSuccess,
            onError,
            id: data.companyId + "updateStatus",
        });
    }
}

export const folderServices = new FolderServices();
