import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IObjectLogs} from "../models/ObjectAny.interface";
import {LogActionTypes, LogEntityType} from "../Interfaces/logs.interface";

class IntegrationService {
    getIntegrations(companyId, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations?as_company_id=${companyId}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractFields(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations/contract-fields`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractBillingTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations/contract-billing-types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractRecurrenceIntervals(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations/contract-recurrence-intervals`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractBillingContacts(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations/contract-billing-contacts`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractInvoicingCycle(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}integrations/contract-agreements`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    updateIntegration(data, onSuccess: Function, onError: Function) {
        return apiService({
            method: "post",
            data: data,
            url: `${SERVER_API_BASE_V1}integrations`,
            onError: onError,
            onSuccess: onSuccess,
            options: {ignoreAbortController: true},
        });
    }
}

export const integrationService = new IntegrationService();
