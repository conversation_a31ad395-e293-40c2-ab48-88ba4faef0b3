import {AxiosResponse} from "axios";
import type {TPageInnerFiltersResponse} from "../Interfaces/filters.interface";
import type {
    IPaginatedPartnerPageDataResponse,
    IPartnerPageDataResponse,
    ISectionResponse,
    TPartnerPageLayouts,
} from "../Interfaces/partnerPage.interface";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {PORTAL_VISIBILITY_GROUPS} from "../constants/visibilityLevels.constant";
import {IDynamicFiltersOptions, buildApiFilters} from "../utils/apiHelpers.util";
import {apiService} from "./api.service";

class PartnerPageServices {
    async searchPartnerData(
        pageId: string,
        sectionId: string,
        type?: string,
        searchWord?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = `${SERVER_API_BASE_V1}partner/page/${pageId}/search?partner_page_section_id=${sectionId}`;
        if (type) {
            url = `${SERVER_API_BASE_V1}partner/page/${pageId}/search?search[type][]=${type}&partner_page_section_id=${sectionId}`;
            if (searchWord) {
                url = url.concat(`&search_word=${searchWord}`);
            }
        }
        return apiService({
            method: "get",
            url,
            onSuccess,
            onError,
        });
    }

    async getPartnerDataWithFilters(
        options: IDynamicFiltersOptions & {
            pageId: string;
            sectionId?: string;
            as_company_id?: string;
            sectionType?: string; //TODO: Check this
            media_visibility?: string;
        },
        route: "blog" | "assets" | "document" | "template" | "video",
        onSuccess?: Function,
        onError?: Function,
    ) {
        let filters = buildApiFilters(options, true);
        if (options.as_company_id) {
            filters += `${filters ? "&" : "?"}as_company_id=${options.as_company_id}`;
        }
        if (options.sectionId) {
            filters += `${filters ? "&" : "?"}partner_page_section_id=${options.sectionId}`;
        }
        if (options.media_visibility) {
            filters += `${filters ? "&" : "?"}media_visibility[]=${options.media_visibility}&media_visibility[]=${
                PORTAL_VISIBILITY_GROUPS.ALL
            }`;
        }
        const url = `${SERVER_API_BASE_V1}partner/page/${options.pageId}/search/${route}${filters}`;

        return apiService<IPaginatedPartnerPageDataResponse, AxiosResponse<IPaginatedPartnerPageDataResponse>>({
            method: "get",
            url,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }

    async getPartnerDataWithFiltersPaginated(
        options: {
            pageId: string;
            sectionType: TPartnerPageLayouts;
            sectionId?: string;
            filters?: {
                [type: string]: string | string[];
            };
            searchWord?: string;
        },
        page: number,
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = `${SERVER_API_BASE_V1}partner/page/${options.pageId}/search`;
        let filters = `?search[type][]=${options.sectionType}&paged=1&items_per_page=20&page=${page}`;
        if (!!options.filters) {
            Object.keys(options.filters).forEach((key: string) => {
                const filterValue = options.filters?.[key];
                if (Array.isArray(filterValue)) {
                    (options.filters?.[key] as string[]).forEach((item: string) => {
                        filters += `&search[${key}][]=${item}`;
                    });
                } else if (key === "sort") {
                    const [orderBy, sortValue] = filterValue?.replaceAll("sorting_", "").split("__") as [
                        string,
                        string,
                    ];
                    filters += filters
                        ? `&order_by=${orderBy}&sort=${sortValue}`
                        : `?order_by=${orderBy}&sort=${sortValue}`;
                } else {
                    filters += `&search[${key}]=${filterValue}`;
                }
            });
        }
        url = url.concat(filters);
        if (options.searchWord) {
            url = url.concat(`&search_word=${options.searchWord}`);
        }
        if (options.sectionId) {
            url = url.concat(`&partner_page_section_id=${options.sectionId}`);
        }

        return apiService<IPaginatedPartnerPageDataResponse, AxiosResponse<IPaginatedPartnerPageDataResponse>>({
            method: "get",
            url,
            onSuccess,
            id: options.sectionType,
            onError,
        });
    }

    async getAllPartnerData(
        options: {
            pageId: string;
            sectionId?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = `${SERVER_API_BASE_V1}partner/page/${options.pageId}/search`;
        if (options.sectionId) {
            url = url.concat(`?partner_page_section_id=${options.sectionId}`);
        }

        return apiService<IPartnerPageDataResponse, AxiosResponse<IPartnerPageDataResponse>>({
            method: "get",
            url,
            onSuccess,
            id: "allData" + options.pageId,
            onError,
        });
    }

    async getPartnerFilters(pageId: string, onSuccess?: Function, onError?: Function) {
        return apiService<TPageInnerFiltersResponse, AxiosResponse<TPageInnerFiltersResponse>>({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/page/${pageId}/filters`,
            onSuccess,
            onError,
        });
    }

    async updateHeaderImage(updatedFields: {pageId: string; image: File}, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("id", updatedFields?.pageId);
        data.append("header_image", updatedFields?.image);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}partner/page/header-image/update`,
            data: data,
            onSuccess,
            onError,
        });
    }

    async removeHeaderImage(ImageId: string, onSuccess?: Function, onError?: Function) {
        const data = {
            id: ImageId,
        };
        return apiService({
            method: "delete",
            data: data,
            url: `${SERVER_API_BASE_V1}partner/page/header-image/delete`,
            onSuccess,
            onError,
        });
    }

    async addContent(
        options: {
            pageId: string;
            subjectId: string;
            sectionId: string;
            subject_type: string;
            media_visibility?: string;
            isChannelProgram?: boolean;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("subject_id", options.subjectId);
        data.append("partner_page_section_id", options.sectionId);
        data.append("subject_type", options.subject_type);
        if (options?.isChannelProgram && options?.media_visibility) {
            data.append("media_visibility", options?.media_visibility);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}partner/page/${options.pageId}/section/content/store`,
            data,
            onSuccess,
            onError,
        });
    }

    async toggleHideContent(
        options: {
            pageId: string;
            subjectId: string;
            sectionId: string;
            subject_type: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {id: options?.subjectId, partner_page_section_id: options.sectionId};
        return apiService({
            method: "put",
            data: data,
            url: `${SERVER_API_BASE_V1}partner/page/${options?.pageId}/section/content/toggle-hide`,
            onSuccess,
            onError,
        });
    }

    async removeContent(
        options: {
            pageId: string;
            subjectId: string;
            sectionId: string;
            subject_type: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {
            subject_id: options?.subjectId,
            subject_type: options.subject_type,
            partner_page_section_id: options.sectionId,
        };
        return apiService({
            method: "delete",
            data: data,
            url: `${SERVER_API_BASE_V1}partner/page/${options?.pageId}/section/content/delete`,
            onSuccess,
            onError,
        });
    }

    async updateContent(
        options: {
            pageId: string;
            sectionId: string;
            subjectId: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {id: options.sectionId, main_content_id: options.subjectId};
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}partner/page/${options.pageId}/section/update`,
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async getSections(pageId: string, as_company_id?: string, onSuccess?: Function, onError?: Function) {
        let url = `${SERVER_API_BASE_V1}partner/page/${pageId}/section`;
        if (as_company_id) {
            url = url + `?as_company_id=${as_company_id}`;
        }
        return apiService<ISectionResponse[], AxiosResponse<ISectionResponse[]>>({
            method: "get",
            url: url,
            onSuccess,
            onError,
        });
    }

    loadChangeOverTime(
        data: {
            partnerPageId: string;
            startDate?: string;
            endDate?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        if (data.startDate) {
            search += `?start_date=${data.startDate}`;
        }
        if (data.endDate) {
            search += search ? `&end_date=${data.endDate}` : `?end_date=${data.endDate}`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${data.partnerPageId}/engagement/change-over-time${search}`,
            onError,
            onSuccess,
        });
    }

    loadContentData(partnerPageId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${partnerPageId}/engagement/content`,
            onError,
            onSuccess,
        });
    }

    loadPartnerData(partnerPageId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${partnerPageId}/engagement/partner`,
            onError,
            onSuccess,
        });
    }

    loadTypeData(partnerPageId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${partnerPageId}/engagement/type`,
            onError,
            onSuccess,
        });
    }

    loadPartnerInvites<T>(
        companyId: string,
        options: IDynamicFiltersOptions,
        onSuccess?: (r: T) => void,
        onError?: Function,
    ) {
        const filters = buildApiFilters(options, true);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/${companyId}/invites${filters}`,
            onError,
            onSuccess,
            options: {
                ignoreAbortController: false,
            },
        });
    }

    loadSavedMSPTemplates(
        vendorId: string,
        mspId: string,
        option: {page?: number},
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        if (option.page) {
            search += `&page=${option.page}`;
        }
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `partner/page/${vendorId}/template/saved-by-msp?company_id=${mspId}&paged=1${search}`,
            onSuccess,
            onError,
        });
    }

    saveTemplateCopy(
        vendorId: string,
        data: {document: any; title: string; MSPId: string; source_media_id: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const formData = new FormData();
        formData.append("document", data.document);
        formData.append("title", data.title);
        formData.append("company_id", data.MSPId);
        if (data?.source_media_id) {
            formData.append("source_media_id", data.source_media_id);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + `partner/page/${vendorId}/template/store`,
            data: formData,
            onSuccess,
            onError,
        });
    }

    loadSavedTempaltes(vendorId: string, mspId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${vendorId}/template/saved-by-msp?company_id=${mspId}`,
            onError,
            onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    loadVendorSavedTempaltes(channelCompanyID: string, vendorId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${channelCompanyID}/template/saved-by-msp?company_id=${vendorId}`,
            onError,
            onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    deleteTemplateCopy(partnerTemplateId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + `partner/page/${partnerTemplateId}/template/delete`,
            onSuccess,
            onError,
        });
    }

    getPartnerPageCompany(subdomain: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/get-company/${subdomain}`,
            onError,
            onSuccess,
        });
    }

    getPartnerPageBySubdomain(subdomain: string, as_company_id?: string, onSuccess?: Function, onError?: Function) {
        let url = SERVER_API_BASE_V1 + `subdomain/${subdomain}`;
        if (as_company_id) {
            url = url + `?as_company_id=${as_company_id}`;
        }
        return apiService({
            method: "get",
            url,
            onError,
            onSuccess,
        });
    }

    getTemplatePDF(vendorId: string, mspId: string, pdfId: string, onSuccess?: Function, onError?: Function) {
        return apiService<string, AxiosResponse<string>>({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/page/${vendorId}/template/saved-by-msp/${pdfId}?company_id=${mspId}`,
            onError,
            onSuccess,
        });
    }

    acceptPartnerInvite(
        inviteId: string,
        partnerShipType?: string,
        otherReason?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = SERVER_API_BASE_V1 + `partner/invitation/${inviteId}/accept`;
        if (partnerShipType) {
            url = url + `?accepted_reason=${partnerShipType}`;
        }
        if (otherReason) {
            url = url + `${partnerShipType ? "&" : "?"}accepted_reason_other=${otherReason}`;
        }
        return apiService({
            method: "get",
            url,
            onError,
            onSuccess,
        });
    }

    acceptMultiplePartnerRequests(
        vendor_id: string,
        invite_ids: string[],
        accepted_reason?: string,
        accepted_reason_other?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {
            invite_ids,
            accepted_reason,
            accepted_reason_other,
        };
        !accepted_reason_other && delete data.accepted_reason_other;
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `partner/invitation/${vendor_id}/accept-multiple`,
            data,
            onError,
            onSuccess,
        });
    }

    updatePartnership(
        inviteId: string,
        partnerShipType?: string,
        otherReason?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = SERVER_API_BASE_V1 + `partner/invitation/${inviteId}/update-partnership`;
        if (partnerShipType) {
            url = url + `?accepted_reason=${partnerShipType}`;
        }
        if (otherReason) {
            url = url + `&accepted_reason_other=${otherReason}`;
        }
        return apiService({
            method: "put",
            url,
            onError,
            onSuccess,
        });
    }

    declinePartnerInvite(
        vendorId: string,
        requestByMsp: boolean,
        request_ids: string[],
        declineReason: string,
        otherReason?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const payload = {
            request_ids,
            requested_by_msp: requestByMsp,
            rejected_reason: declineReason,
            rejected_reason_other: otherReason || undefined,
        };
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `partner/${vendorId}/remove-invite`,
            data: payload,
            onError,
            onSuccess,
        });
    }

    resendPartnerInvite(vendorId: string, inviteId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `partner/${vendorId}/invite/resend-invite/${inviteId}`,
            onError,
            onSuccess,
        });
    }

    bulkResendPartnerInvite(vendorId: string, inviteIds: string[], onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `partner/${vendorId}/invite/resendBulkInvite`,
            data: {invites: inviteIds},
            onError,
            onSuccess,
        });
    }

    invitePartnersByCsv(
        vendorId: string,
        file: File,
        handleUploadProgress: Function,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("vendorId", vendorId);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            data: formData,
            url: SERVER_API_BASE_V1 + `partner/${vendorId}/invite/csv`,
            onError: onError,
            onSuccess: onSuccess,
            onUploadProgress: function (progressEvent: any) {
                handleUploadProgress(progressEvent);
            },
        });
    }

    getCompanyPartners(company_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partners/${company_id}/users`,
            onError,
            onSuccess,
        });
    }

    getPagedCompanyPartners<T>(
        companyId: string,
        options: IDynamicFiltersOptions,
        onSuccess?: (r: T) => void,
        onError?: Function,
    ) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partners/${companyId}/users${filters}`,
            onError,
            onSuccess,
        });
    }

    getPagedCompanyPartnersWithContacts<T>(
        companyId: string,
        options: IDynamicFiltersOptions,
        onSuccess?: (r: T) => void,
        onError?: Function,
    ) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partners/${companyId}/partners-with-contacts${filters}`,
            onError,
            onSuccess,
        });
    }

    getBase64Media(ownerId: string, mediaId: string, mspCompanyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `partner/page/${ownerId}/template/saved-by-msp/${mediaId}?company_id=${mspCompanyId}`,
            onError,
            onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    updateSavedFile(partnerTemplateId: string, newTitle: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `partner/page/${partnerTemplateId}/template/update`,
            data: {
                title: newTitle,
            },
            onError,
            onSuccess,
        });
    }

    getSectionContent(
        pageId: string,
        sectionId: string,
        option: {page?: number; items_per_page?: number; search_word?: string},
        onSuccess?: Function,
        onError?: Function,
        as_company_id?: string,
    ) {
        const search = `?paged=1&page=${option?.page ? option?.page : 1}&items_per_page=${
            option?.items_per_page ? option.items_per_page : 50
        }${
            option?.search_word ? `&search_word=${option.search_word}` : ""
        }&search[partner_page_section_id]=${sectionId}`;
        let url = SERVER_API_BASE_V1 + `partner/page/${pageId}/section/content` + search;
        if (as_company_id) {
            url = url + `&as_company_id=${as_company_id}`;
        }
        return apiService({
            method: "get",
            url: url,
            onSuccess,
            onError,
        });
    }

    getInvitationAcceptReasons(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `lookups/partner_invitation_accepting_reasons/options`,
            onError,
            onSuccess,
        });
    }

    getInvitationRejectReasons(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `lookups/partner_invitation_rejecting_reasons/options`,
            onError,
            onSuccess,
        });
    }

    removeInvite(
        msp_id: string,
        vendor_id: string,
        requested_by_msp: boolean,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + `partner/remove-invite`,
            data: {msp_id, vendor_id, requested_by_msp},
            onError,
            onSuccess,
        });
    }

    downloadCSV(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partners/${companyId}/download`,
            onError,
            onSuccess,
        });
    }

    getSyncedPartners(companyId: string, options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const filters = buildApiFilters(options);
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `partner/${companyId}/customer${filters}`,
            onError,
            onSuccess,
        });
    }

    toggleIgnoreSyncedPartner(
        companyId: string,
        syncIds: string[],
        is_ignored: boolean,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {
            ids: syncIds,
            is_ignored,
        };
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `partner/${companyId}/customer/is-ignored`,
            data,
            onError,
            onSuccess,
        });
    }
}

export const PartnerPageService = new PartnerPageServices();
