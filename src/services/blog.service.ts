import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IAddBlogPostData} from "../Interfaces/blog.interface";

class BlogService {
    store(blogData: IAddBlogPostData, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("title", blogData.title);
        data.append("status", blogData.status);
        data.append("subject_id", blogData.subject_id);
        data.append("subject_type", blogData.subject_type);
        data.append("is_partner_content", blogData.is_partner_content ? "1" : "0");
        if (blogData?.media_visibility) {
            data.append("media_visibility", blogData.media_visibility);
        }
        if (blogData.body) data.append("body", blogData.body);
        if (!!blogData.categories?.length) {
            blogData.categories.forEach(category => data.append("categories[]", category));
        }
        if (!!blogData.tags?.length) {
            blogData.tags.forEach(tag => data.append("tags[]", tag));
        }
        if (blogData.excerpt) data.append("excerpt", blogData.excerpt);
        if (blogData.header_image) data.append("header_image", blogData.header_image);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}blog/store`,
            data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    update(blogId: string, blogData: IAddBlogPostData, onSuccess?: Function, onError?: Function) {
        const data = {
            id: blogId,
            title: blogData.title,
            status: blogData.status,
            subject_id: blogData.subject_id,
            subject_type: blogData.subject_type,
            body: blogData.body,
            categories: blogData.categories,
            tags: blogData.tags,
            excerpt: blogData.excerpt,
            is_partner_content: blogData.is_partner_content,
        };
        if (blogData?.media_visibility) {
            data["media_visibility"] = blogData.media_visibility;
        }
        if (!blogData.body) delete data.body;
        if (!!!blogData.categories?.length) delete data.categories;
        if (!!!blogData.tags?.length) delete data.tags;
        if (!blogData.excerpt) delete data.excerpt;

        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}blog/update`,
            data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    updateHeaderImage(
        blog_id: string,
        image: File,
        subject_id: string,
        subject_type: "userProfile" | "companyProfile",
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        if (image) {
            data.append("image", image);
        }
        data.append("blog_id", blog_id);
        data.append("subject_id", subject_id);
        data.append("subject_type", subject_type);
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}blog/image/upload/header`,
            data,
            onSuccess,
            onError,
        });
    }

    getPost(friendly_url: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}blog/${friendly_url}`,
            onSuccess,
            onError,
        });
    }

    delete(id: string, onSuccess?: Function, onError?: Function) {
        const data = {id};
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}blog/delete`,
            data,
            onSuccess,
            onError,
        });
    }
}

export const blogService = new BlogService();
