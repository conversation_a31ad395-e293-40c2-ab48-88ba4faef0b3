import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {ICustomView} from "../Interfaces/customView.interface";

class CustomViewsService {
    getAllViews(handleSuccess: Function, handleError: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/customview",
            onError: handleError,
            onSuccess: handleSuccess,
        });
    }

    createReport(id: string, handleSuccess: Function, handleError: Function) {
        const data = {id};
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/customview/createreport`,
            data,
            onError: handleError,
            onSuccess: handleSuccess,
        });
    }

    addView(customView: ICustomView, handleError?: Function, handleSuccess?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/customview/store`,
            data: customView,
            onError: handleError,
            onSuccess: handleSuccess,
        });
    }

    deleteView(id: string, handleError?: Function, handleSuccess?: Function) {
        const data = {id};
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}admin/customview/delete`,
            data,
            onError: handleError,
            onSuccess: handleSuccess,
        });
    }
}

export const customViewsService = new CustomViewsService();
