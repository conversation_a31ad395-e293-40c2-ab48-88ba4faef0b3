import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {QuestionFormTypeEnum} from "../enums/questionFormTypes.enum";
import {IQuestionAnswers} from "../Interfaces/questions.interface";

class QuestionsService {
    getQuestions(form_type: QuestionFormTypeEnum, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}question/questions?question_form_type=${form_type.toString()}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    saveQuestionAnswer(
        question_id: string,
        question_answers: IQuestionAnswers,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = question_answers;

        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}question/${question_id}/answer/store`,
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getForm(form_type: QuestionFormTypeEnum, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}question/form?form_type=${form_type.toString()}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const questionService = new QuestionsService();
