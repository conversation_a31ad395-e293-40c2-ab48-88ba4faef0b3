import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class ChatService {
    sendMsgToMod(
        pitch_event_id: string,
        message: string,
        pusher_channel: string,
        event: string,
        onSuccess: Function,
        onError: Function,
    ) {
        const json = {
            pitch_event_id,
            message,
            pusher_channel,
            event,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/send-message-to-moderator",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    sendModMsgToUser(
        user_id: string,
        pitch_event_id: string,
        message: string,
        pusher_channel: string,
        event: string,
        onSuccess: Function,
        onError: Function,
    ) {
        const json = {
            user_id,
            pitch_event_id,
            message,
            pusher_channel,
            event,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/send-mod-message-to-user",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    sendBroadcast(
        pitch_event_id: string,
        message: string,
        pusher_channel: string,
        event: string,
        onSuccess: Function,
        onError: Function,
    ) {
        const json = {
            pitch_event_id,
            message,
            pusher_channel,
            event,
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/send-broadcast-message",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    editBroadcast(
        data: {
            pitch_event_id: string;
            message: string;
            pusher_channel: string;
            event: string;
            message_id: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/update-broadcast-message",
            data: {
                pitch_event_id: data.pitch_event_id,
                message: data.message,
                pusher_channel: data.pusher_channel,
                event: data.event,
                id: data.message_id,
            },
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    sendPublicMsg(
        data: {pitch_event_id: string; message: string; pusher_channel: string; event: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "pitch-events/pitch-day/send-message-to-public",
            data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deletePublicMsg(message_id: string, channel: string, event: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "admin/pitch-day/delete-public-chat-msg",
            data: {msg_id: message_id, pusher_channel: channel, event},
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    banPublicUser(message_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "admin/pitch-day/ban-user-public-chat-msgs",
            data: {msg_id: message_id},
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}

export const chatService = new ChatService();
