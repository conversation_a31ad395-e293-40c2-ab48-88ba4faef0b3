import type {AxiosResponse} from "axios";
import type {CardTypesResponse, IAdvertisementBody} from "../Interfaces/adminAdvertisements.interface";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import type {IPaginatedTableRequest} from "../models";
import {apiService} from "./api.service";

class AdminAdvertisementService {
    showAll(
        // options: {
        //     paged?: boolean;
        //     page?: number;
        //     order_by?: string;
        //     sort?: string;
        //     search_word?: string;
        //     start_date?: string;
        //     end_date?: string;
        //     status?: string;
        // },
        options: IPaginatedTableRequest,
        onSuccess?: Function,
        onError?: Function,
    ): any {
        const {page, orderBy, sortType, search_word, start_date, status, advertisement_card_type} = options;
        let params = "?paged=1";
        if (page) {
            params += `&page=${page}`;
        }
        if (orderBy) {
            params += `&order_by=${orderBy}`;
        }
        if (sortType) {
            params += `&sort=${sortType}`;
        }
        if (start_date) {
            params += `&start_date=${start_date}`;
        }
        if (status) {
            params += `&status=${status}`;
        }
        if (search_word) {
            params += `&search_word=${search_word}`;
        }
        if (advertisement_card_type) {
            advertisement_card_type.forEach(card_type_id => {
                params += `&advertisement_card_types[]=${card_type_id}`;
            });
        }
        return apiService({
            method: "get",
            data: options,
            url: SERVER_API_BASE_V1 + `admin/advertisement${params}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    storeAdvertisement(data: IAdvertisementBody, onSuccess?: Function, onError?: Function) {
        const formData = new FormData();
        formData.append("name", data.name);
        formData.append("description", data.description || "");
        formData.append("subject_id", data.subject_id);
        formData.append("subject_type", data.subject_type);
        formData.append("advertisement_card_type_id", data.advertisement_card_type_id);
        if (data.link) {
            formData.append("link", data.link);
        }
        formData.append("additional_data", data.additional_data);
        formData.append("start_date", data.start_date);
        formData.append("end_date", data.end_date);
        formData.append("status", data.status);
        if (data?.category_id) {
            formData.append("category_id", data.category_id);
        }
        data.locations.forEach(locationId => {
            formData.append("locations[]", locationId);
        });
        if (data.image) {
            formData.append("image", data.image);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            data: formData,
            url: SERVER_API_BASE_V1 + `admin/advertisement/store`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateAdvertisement(data: any, onSuccess?: Function, onError?: Function) {
        if (!data.id) return;
        const formData = new FormData();
        formData.append("id", data.id);
        if (data.name) formData.append("name", data.name);
        if (data.description) formData.append("description", data.description || "");
        if (data.subject_id) formData.append("subject_id", data.subject_id);
        if (data.subject_type) formData.append("subject_type", data.subject_type);
        if (data.advertisement_card_type_id)
            formData.append("advertisement_card_type_id", data.advertisement_card_type_id);
        if (data.link) formData.append("link", data.link);
        if (data.additional_data) formData.append("additional_data", data.additional_data);
        if (data.start_date) formData.append("start_date", data.start_date);
        if (data.end_date) formData.append("end_date", data.end_date);
        if (data.status) formData.append("status", data.status);
        if (data.category_id) {
            formData.append("category_id", data.category_id || "");
        }
        data.locations?.forEach(locationId => {
            formData.append("locations[]", locationId);
        });
        if (data.image) {
            formData.append("image", data.image);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            data: formData,
            url: SERVER_API_BASE_V1 + `admin/advertisement/update`,
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    deleteAdvertisements(ids: number[], onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            data: {ids: ids},
            url: SERVER_API_BASE_V1 + `admin/advertisement/delete`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    searchEvents(search_word: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/advertisement/find-events?search_word=${search_word}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    searchVendors(search_word: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/advertisement/find-vendors?search_word=${search_word}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getAvailableLocations(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `advertisement/available-location`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getCardTypes(onSuccess?: Function, onError?: Function) {
        return apiService<CardTypesResponse[], AxiosResponse<CardTypesResponse[]>>({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/advertisement/card-type`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    getRandomAdvertisement(
        options: {
            pageId: string;
            category_id?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "get",
            url:
                SERVER_API_BASE_V1 +
                `advertisement?location_id=${options.pageId}${
                    options.category_id ? `&category_id=${options.category_id}` : ""
                }`,
            onError: onError,
            onSuccess: onSuccess,
            options: {
                ignoreAbortController: true,
            },
        });
    }
}

export default new AdminAdvertisementService();
