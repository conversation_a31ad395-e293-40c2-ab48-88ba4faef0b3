import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {apiService} from "./api.service";

class StackService {
    async getUserStack(userId: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}user/${userId}/my-stack`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async storeToMyStack(userId: string, companyId: string[], onSuccess: Function, onError: Function) {
        const data = {
            stack_companies_ids: companyId,
        };
        return apiService({
            method: "post",
            data,
            url: `${SERVER_API_BASE_V1}user/${userId}/my-stack/store`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async removeFromMyStack(userId: string, companyId: string[], onSuccess: Function, onError: Function) {
        const data = {
            stack_companies_ids: companyId,
        };
        return apiService({
            method: "delete",
            data,
            url: `${SERVER_API_BASE_V1}user/${userId}/my-stack/delete`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async addToCustomerStack(
        company_id: string,
        payload: {
            [category_id: string]: {
                stack_company_name?: string;
                stack_company_id?: string;
                product_name?: string;
                product_id?: string;
                product_description?: string | null;
                partner_status?: string;
            }[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const stackData = Object.entries(payload).reduce((acc, [category_id, products]) => {
            const updatedProducts = products.map(product => {
                if (product.product_description === null) {
                    delete product.product_description;
                }
                product.partner_status = "I am a current partner";
                return product;
            });
            acc[category_id] = updatedProducts;
            return acc;
        }, {});
        const data = {
            stack: stackData,
        };
        return apiService({
            method: "post",
            data,
            url: `${SERVER_API_BASE_V1}customer/${company_id}/my-stack/store`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async updateCustomerStack(
        company_id: string,
        payload: {id: string} & Partial<{
            stack_company_id: string;
            product_id: string;
            category_id: string;
            product_description: string;
            partner_status?: "I am a current partner";
        }>,
    ) {
        return apiService({
            method: "put",
            data: payload,
            url: `${SERVER_API_BASE_V1}customer/${company_id}/my-stack/update`,
        });
    }

    async deleteCustomerStack(
        company_id: string,
        payload: {
            id: string;
        },
    ) {
        return apiService({
            method: "delete",
            data: payload,
            url: `${SERVER_API_BASE_V1}customer/${company_id}/my-stack/delete`,
        });
    }

    generateCustomerCSV(company_id: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}customer/${company_id}/my-stack/generate-csv`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    generateCustomerPDF(company_id: string, onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            headers: {
                "Content-Type": "application/pdf",
            },
            responseType: "blob",
            url: `${SERVER_API_BASE_V1}customer/${company_id}/my-stack/generate-pdf`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const stackService = new StackService();
