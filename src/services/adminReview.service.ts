import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class AdminReviewService {
    getProductReviews(
        options: {
            paged: boolean;
            page: number;
            items_per_page: number;
            search_word: string;
            status: string | string[];
            incentive_status: string | string[];
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const {page, items_per_page, search_word, status, incentive_status} = options;
        const search = `?paged=1&page=${page ? page : 1}&items_per_page=${items_per_page ? items_per_page : 50}${
            search_word ? `&search_word=${search_word}` : ""
        }${status ? (typeof status === "string" ? `&status[]=${status}` : status.map(s => `&status[]=${s}`)) : ""}${
            incentive_status
                ? typeof incentive_status === "string"
                    ? `&incentive_status[]=${incentive_status}`
                    : incentive_status.map(s => `&incentive_status[]=${s}`)
                : ""
        }`;
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/review/products" + search,
            onSuccess,
            onError,
        });
    }

    approveReview(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "admin/review/approve",
            data: {id},
            onSuccess,
            onError,
        });
    }

    flagReview(id: string, reason: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "admin/review/flag",
            onSuccess,
            onError,
            data: {
                id,
                reason,
            },
        });
    }

    exportReviewsToCSV(options: {search_word?: string; status?: string}, onSuccess?: Function, onError?: Function) {
        const {search_word, status} = options;
        const search = `${search_word ? `?search_word=${search_word}` : ""}${
            status ? (search_word ? `&status=${status}` : `?status=${status}`) : ""
        }`;
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/review/products/export" + search,
            onSuccess,
            onError,
            responseType: "blob",
        });
    }

    getReviewById(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/review/" + id,
            onSuccess,
            onError,
        });
    }

    // Dates sent in this format YYYY-MM-DD HH:MM:SS
    exportProductReviews(
        options: {search_word?: string; status?: string[]; from_date?: string; to_date?: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const {status, search_word, from_date, to_date} = options;
        let search = "";
        if (status) {
            status.forEach(s => {
                search += search ? `&status[]=${s}` : `?status[]=${s}`;
            });
        }
        if (from_date) {
            search += `${search ? "&" : "?"}from_date=${from_date}`;
        }
        if (to_date) {
            search += `${search ? "&" : "?"}to_date=${to_date}`;
        }
        if (search_word) {
            search += `${search ? "&" : "?"}search_word=${search_word}`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "admin/review/products/export-to-csv" + search,
            onSuccess,
            onError,
            responseType: "blob",
        });
    }

    reviewIncentiveUpdate(payload: any, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "admin/review/incentive-update",
            onSuccess,
            onError,
            data: payload,
        });
    }

    underReviewUpdate(payload: any, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "admin/review/under-review",
            onSuccess,
            onError,
            data: payload,
        });
    }

    getTotalIncentivePaid(user_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `admin/review/incentives/${user_id}`,
            onSuccess,
            onError,
        });
    }

    deleteAbandoneReview(ids: number[], onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}admin/review/delete-abandoned-archived-review`,
            data: {ids: ids},
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }
    bulkUpdateStatus(ids: number[], status: string, reason?: string, onSuccess?: Function, onError?: Function) {
        const requestBody: any = {
            status,
            ids,
        };
        if (status === "flagged") {
            requestBody.reason = reason;
        }
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/review/update-review-status`,
            data: requestBody,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }
}

export const adminReviewService = new AdminReviewService();
