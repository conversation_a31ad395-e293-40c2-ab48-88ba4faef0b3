import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class OneToOneChatService {
    sendOneToOneMessage(
        data: {
            receiver_user_id: string;
            message: string;
            redirect_url?: string;
            creator_company_id: string;
            receiver_company_id: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}chat/one-to-one/send`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    getOneToOneChatList(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}chat/one-to-one/list/${companyId}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getChatByReceiverId(
        receiver_user_id: string,
        other_user_company_id: string,
        my_company_id: string,
        options: {
            paged?: boolean;
            page?: number;
            end_date?: string;
            items_per_page?: number;
            order_by?: string;
            sort?: string;
            search_word?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = "";
        if (options.paged) {
            search += `&paged=${options.paged ? "1" : "0"}`;
        }
        if (options.page) {
            search += `&page=${options.page}`;
        }
        if (options.end_date) {
            search += `&end_date=${options.end_date}`;
        }
        if (options.items_per_page) {
            search += `&items_per_page=${options.items_per_page}`;
        }
        if (options.order_by) {
            search += `&order_by=${options.order_by}`;
        }
        if (options.sort) {
            search += `&sort=${options.sort}`;
        }
        if (options.search_word) {
            search += `&search_word=${options.search_word}`;
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}chat/one-to-one?other_user_id=${receiver_user_id}&other_user_company_id=${other_user_company_id}&my_company_id=${my_company_id}${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    toggleChatVisibility(
        data: {
            sender_user_id: string;
            sender_company_id: string;
            receiver_user_id: string;
            receiver_company_id: string;
            is_hidden: boolean;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}chat/one-to-one/toggle-visibility`,
            onSuccess: onSuccess,
            onError: onError,
            data: {...data, is_hidden: data.is_hidden ? 1 : 0},
        });
    }
}

export const oneToOneChatService = new OneToOneChatService();
