import axios, {AxiosError, AxiosResponse} from "axios";
import {HUBSPOT_VENDOR_CONTACT_FORM_ID, HUBSPOT_VENDOR_CONTACT_PORTAL_ID} from "../constants/commonStrings.constant";

class Hubspot {
    contactThisVendor(
        {hubspot_json, formId = HUBSPOT_VENDOR_CONTACT_FORM_ID, portalId = HUBSPOT_VENDOR_CONTACT_PORTAL_ID},
        onSuccess: Function,
        onError: Function,
    ) {
        // Using axios here since using the api service produces a CORS issue
        return axios({
            method: "post",
            headers: {
                "Content-Type": "application/json",
            },
            url: `https://api.hsforms.com/submissions/v3/integration/submit/${portalId}/${formId}`,
            data: hubspot_json,
        })
            .then((res: AxiosResponse) => onSuccess(res))
            .catch((error: AxiosError<any>) => onError(error));
    }
}

export const hubspotService = new Hubspot();
