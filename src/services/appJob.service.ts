import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class AppJobService {
    getAllAppJobs(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/cpjobs/list`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    update(id: string, cron: string, onSuccess: Function, onError: Function) {
        const json = {
            id: id,
            cron: cron,
        };

        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}admin/cpjobs/update`,
            data: json,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    execute(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/cpjobs/execute/${id}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    flushCPJobs(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/cpjobs/flush`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const appJobService = new AppJobService();
