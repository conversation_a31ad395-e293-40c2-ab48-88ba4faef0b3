import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IUpdateHeader} from "../Interfaces/company.interface";
import {ImageTypes} from "../enums/imageTypes.enum";
import {DeleteImage} from "../Interfaces/queries.interface";
import {IFileQueue} from "../hooks/useChunkUpload";

class ProfileService {
    async changeEmail(email: string, onError?: Function, onSuccess?: Function) {
        const json: Object = {
            email: email.trim().toLowerCase(),
        };

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "changeemail",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async changeName(id: string, firstName: string, lastName: string, onError?: Function, onSuccess?: Function) {
        const json: Object = {
            id: id,
            first_name: firstName.trim(),
            last_name: lastName.trim(),
        };

        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "user",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async changePersonalInfo(
        id: string,
        phone: string,
        jobTitle: string,
        first_name: string,
        last_name: string,
        city,
        state,
        country,
        onError?: Function,
        onSuccess?: Function,
    ) {
        const json: Object = {
            id,
            phone: phone.trim(),
            job_title_id: jobTitle,
            first_name: first_name.trim(),
            last_name: last_name.trim(),
            city,
            state,
            country,
        };

        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "user",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async subscribeToEventEmail(userId: string, shouldSubscribe: boolean, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "user",
            data: {
                id: userId,
                event_email_subscribed: shouldSubscribe,
            },
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async loadInfo(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `user/${id}/profile`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async loadByHandle(handle: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `user/handle/${handle}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    async loadByFriendlyUrl(friendly_url: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `user/friendly-url/${friendly_url}`,
            onError: onError,
            onSuccess: onSuccess,
            id: friendly_url,
        });
    }

    async loadUserVideos(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `user/${id}/profile/video`,
            onError: onError,
            onSuccess: onSuccess,
            id,
        });
    }

    async updateUser(updatedFields: IUpdateHeader, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + `user`,
            data: updatedFields,
            onError: onError,
            onSuccess: onSuccess,
            id: updatedFields?.id,
        });
    }

    async updateImage(
        updatedFields: {id: string; image_type: ImageTypes; image: File},
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("image", updatedFields.image);
        data.append("image_type", updatedFields.image_type);

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}profiles/user/${updatedFields.id}/image/store`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async uploadVideo(
        options: {
            user_id: string;
            video: File;
            thumbnail: File;
            title: string;
            description: string;
            duration: string;
            categories: string | null;
            tags: string[];
            handleUploadProgress: Function;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = new FormData();
        data.append("title", options.title);
        data.append("description", options.description);
        data.append("duration", options.duration);
        if (options.categories) data.append("categories[]", options.categories);
        options.tags.forEach((tagId: string) => {
            data.append("tags[]", tagId);
        });
        data.append("video", options.video);
        data.append("thumbnail", options.thumbnail);
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + `user/${options.user_id}/profile/video/store`,
            data: data,
            onSuccess: onSuccess,
            onError: onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    async removeVideo(id: string, video: string, onSuccess?: Function, onError?: Function) {
        const json = {
            video,
        };
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}user/${id}/profile/video/delete`,
            data: json,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    async editMedia(
        options: {
            id: string;
            thumbnail: File | null;
            title: string;
            description: string;
            duration: string;
            is_partner_content?: string | null;
            categories: string | null;
            tags: string[];
            handleUploadProgress: Function;
            media_visibility?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const formData = new FormData();
        if (options.title) formData.append("title", options.title);
        if (options.description) formData.append("description", options.description);
        if (options.thumbnail) formData.append("thumbnail", options.thumbnail);
        if (options.duration) formData.append("duration", options.duration);
        if (options.categories) formData.append("categories[]", options.categories);
        if (options.is_partner_content) formData.append("is_partner_content", options.is_partner_content);
        if (options?.media_visibility) formData.append("media_visibility", options.media_visibility);
        options.tags.forEach((tagId: string) => {
            formData.append("tags[]", tagId);
        });

        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}media/update/${options.id}`,
            data: formData,
            onSuccess: onSuccess,
            onError: onError,
            onUploadProgress: function (progressEvent: any) {
                options.handleUploadProgress(progressEvent);
            },
        });
    }

    async removeImage(deleteObj: DeleteImage, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}profiles/user/${deleteObj.id}/image/delete/${deleteObj.image_id}`,
            onSuccess,
            onError,
        });
    }

    getUserContentFeed = async (options: {id: string; page: number}, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}user/${options.id}/profile/content-feed?paged=1&page=${options.page}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    };

    getUserContentCounts = async (id: string, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}user/${id}/profile/content-feed/counts`,
            onSuccess: onSuccess,
            onError: onError,
            id,
        });
    };

    getBlogs(friendly_url: string, isCompany: boolean, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}${isCompany ? "company" : "user"}/${
                isCompany ? "friendly_url" : "friendly-url"
            }/${friendly_url}/blogs`,
            onSuccess,
            onError,
            id: friendly_url,
        });
    }

    getRules(friendly_url: string, isCompany: boolean, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/rule/value/${isCompany ? "company" : "user"}/${friendly_url}`,
            onSuccess,
            onError,
            id: friendly_url,
        });
    }

    getAllSubscriptionRules(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}profiles/rule/value`,
            onSuccess,
            onError,
        });
    }

    getNewsFeed(last_date: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}news-feed/user${last_date ? `?last_date=${last_date}` : ""}${
                last_date ? "&only_show_following_content=1" : "?only_show_following_content=1"
            }`,
            onSuccess,
            onError,
        });
    }

    getAllNewsFeed(last_date: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}news-feed/user${
                !!last_date ? `?last_date=${last_date}&only_show_following_content=0` : "?only_show_following_content=0"
            }&days_back=15`,
            onSuccess,
            onError,
        });
    }

    updateStatus(id: string, status: string, onSuccess?: Function, onError?: Function) {
        const data = {
            id,
            status,
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/user/change-status`,
            data,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }

    updateEmail(id: string, email: string, onSuccess?: Function, onError?: Function) {
        const data = {
            id,
            email,
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/user/change-email`,
            data,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }

    uploadBigFile(data: IFileQueue, id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}user/${id}/profile/video/store`,
            data,
            onSuccess,
            onError,
            options: {ignoreAbortController: true},
        });
    }
}

export const profileService = new ProfileService();
