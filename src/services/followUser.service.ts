import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

interface ISearchFields {
    word?: string | null;
    filters?: Array<string> | null;
}
interface ISearchFilter {
    search: ISearchFields;
}
interface ISearchWidget {
    search_word: string;
    quantity: number;
    offset: number;
}

class FollowUserService {
    SearchFollowings(
        userId: string,
        word: string | null,
        filters: string[] | null,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const newfilter: ISearchFilter = {
            search: {
                word,
                filters,
            },
        };

        if (!word) delete newfilter?.search?.word;
        if (!filters) delete newfilter?.search?.filters;

        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}user/${userId}/following`,
            data: !newfilter.search.filters && !newfilter.search.word ? null : newfilter,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    SearchFollowingsWidget(
        userId: string,
        search_word: string,
        quantity: number,
        offset: number,
        onSuccess: Function,
        onError: Function,
    ) {
        const searchData: ISearchWidget = {
            search_word,
            quantity,
            offset,
        };
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}user/${userId}/following-widget`,
            data: searchData,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    followUser(userId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}user/${userId}/follow`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    unFollowUser(userId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}user/${userId}/stop-following`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}
export const followUserService = new FollowUserService();
