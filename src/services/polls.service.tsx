import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {
    IPollsInsert,
    IPolls,
    IPollsOptionInsert,
    IPollsOptionUpdate,
    IPollsOptionReorder,
} from "../Interfaces/polls.interfaces";

class PollsService {
    showAllPolls(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "polls/questions",
            onError: onError,
            onSuccess: onSuccess,
        });
    }
    loadPollById(id: string, onSuccess: Function, onError: Function) {
        const data = {id: id};
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/detail",
            data: data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
    insertPoll(data: IPollsInsert, onSuccess: Function, onError: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/store",
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updatePoll(data: IPolls, onSuccess: Function, onError: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "polls/questions/update",
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    deletePoll(id: string, onSuccess: Function, onError: Function) {
        const json = {id: id};
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + "polls/questions/delete",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    showAllOptions(questionId: string, onSuccess?: Function, onError?: Function, options?: any) {
        const json = {poll_question_id: questionId};

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/options",
            data: json,
            options: options,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    insertOption(data: IPollsOptionInsert, onSuccess: Function, onError: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/options/store",
            data: data,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    updateOption(data: IPollsOptionUpdate, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: SERVER_API_BASE_V1 + "polls/questions/options/update",
            data: data,
            onError,
            onSuccess,
            id: data.id,
        });
    }

    reorderOption(options: IPollsOptionReorder[], onSuccess?: Function, onError?: Function) {
        const data = {options};
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/options/reorder",
            data,
            onError,
            onSuccess,
        });
    }

    deleteOption(id: string, onSuccess: Function, onError: Function) {
        const json = {id: id};
        return apiService({
            method: "delete",
            url: SERVER_API_BASE_V1 + "polls/questions/options/delete",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    archiveQuestion(id: string, archive: boolean, onSuccess: Function, onError: Function) {
        const json = {poll_question_id: id, archive: archive};

        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "polls/questions/archive",
            data: json,
            onError: onError,
            onSuccess: onSuccess,
        });
    }
}
export const pollsService = new PollsService();
