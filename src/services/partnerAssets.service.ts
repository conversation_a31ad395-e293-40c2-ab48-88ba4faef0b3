import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class PartnerAssetServices {
    showByMSP = async (options: {ownerId: string; page?: number}, onSuccess?: Function, onError?: Function) => {
        let search = "";
        if (options.page) {
            search = `?paged=1&page=${options.page}`;
            search += `&items_per_page=6`;
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/page/${options.ownerId}/asset/by-msp${search}`,
            onSuccess,
            onError,
        });
    };

    storePartnerAsset = async (
        partnerPageId: string | number,
        data: {
            images: File[];
            title: string;
            description: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) => {
        const formData = new FormData();
        formData.append("title", data.title);
        formData.append("description", data.description);
        data.images.forEach((image: File) => {
            formData.append("images[]", image);
        });
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}partner/page/${partnerPageId}/asset/store`,
            data: formData,
            onSuccess,
            onError,
        });
    };

    deletePartnerAssetMedia = async (
        partner_asset_id: string,
        mediaId: string,
        onSuccess?: Function,
        onError?: Function,
    ) => {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}partner/page/${partner_asset_id}/asset/${mediaId}/delete`,
            onSuccess,
            onError,
        });
    };

    deletePartnerAsset = async (partner_asset_id: string, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}partner/page/${partner_asset_id}/asset/delete`,
            onSuccess,
            onError,
        });
    };

    getBrandableContentInfo = (
        company_id: string,
        options: {
            paged?: boolean;
            page?: number;
            items_per_page?: number;
            order_by?: string;
            sort?: string;
            search?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) => {
        let search = "";
        if (options.paged) {
            search += "?paged=1";
        }
        if (options.page) {
            search += `&page=${options.page}`;
        }
        if (options.items_per_page) {
            search += `&items_per_page=${options.items_per_page}`;
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/${company_id}/brandable-contact-info${search}`,
            onSuccess,
            onError,
        });
    };

    storeBrandableContentInfo = (
        company_id: string,
        data: {title: string; content: string},
        onSuccess?: Function,
        onError?: Function,
    ) => {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}partner/${company_id}/brandable-contact-info/store`,
            data,
            onSuccess,
            onError,
        });
    };

    updateBrandableContentInfo = (
        company_id: string,
        data: {id: string; title?: string; content?: string},
        onSuccess?: Function,
        onError?: Function,
    ) => {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}partner/${company_id}/brandable-contact-info/update`,
            data,
            onSuccess,
            onError,
        });
    };

    deleteBrandableContentInfo = (company_id: string, data: {id: string}, onSuccess?: Function, onError?: Function) => {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}partner/${company_id}/brandable-contact-info/delete`,
            data,
            onSuccess,
            onError,
        });
    };
}

export const partnerAssetService = new PartnerAssetServices();
