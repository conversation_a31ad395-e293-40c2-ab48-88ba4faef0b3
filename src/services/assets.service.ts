import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IAddAssetsPostData} from "../Interfaces/assets.interface";

class AssetsService {
    store(assetsData: IAddAssetsPostData, onSuccess?: Function, onError?: Function) {
        const data = new FormData();
        data.append("title", assetsData.title);
        data.append("owner_id", assetsData.subject_id);
        data.append("owner_type", assetsData.subject_type);
        data.append("is_partner_content", assetsData.is_partner_content ? "1" : "0");
        if (assetsData?.media_visibility) {
            data.append("media_visibility", assetsData.media_visibility);
        }
        if (assetsData.description) data.append("description", assetsData.description);
        if (!!assetsData.tags?.length) {
            assetsData.tags.forEach(tag => data.append("tags[]", tag));
        }
        if (!!assetsData.images?.length) {
            assetsData.images.forEach(image => data.append("images[]", image));
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: `${SERVER_API_BASE_V1}media-gallery/store`,
            data,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const assetsService = new AssetsService();
