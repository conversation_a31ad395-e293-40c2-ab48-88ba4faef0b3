import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class CyclrService {
    getEmbedUrl(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}cyclr/get-embed-url/${companyId}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const cyclrService = new CyclrService();
