import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {apiService} from "./api.service";

class CurrencyService {
    getConversions(currency: string, start_date?: string, end_date?: string) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "currencies/" + currency,
            params: start_date || end_date ? {start_date, end_date} : {},
        });
    }

    getAllCurrencies(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}currencies`,
            onSuccess: onSuccess,
            onError: onError,
            id: "all-currencies",
        });
    }
}

export const currencyService = new CurrencyService();
