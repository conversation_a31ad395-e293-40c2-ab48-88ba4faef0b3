import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {IStoreDealRegistration, IUpdateDealRegistration} from "../Interfaces/dealRegistrations.interface";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";

class DealRegistrationService {
    getDeals(companyId: string, filters: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const search = buildApiFilters(filters);
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/search${search}`,
            onSuccess: onSuccess,
            onError: onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    getDealsFilters(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getDeal(companyId: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    storeDeal(companyId: string, data: IStoreDealRegistration, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/store`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    updateDeal(companyId: string, data: IUpdateDealRegistration, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/update`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    deleteDeal(companyId: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/delete`,
            onSuccess: onSuccess,
            onError: onError,
            data: {id: dealId},
        });
    }

    createDealDocument(
        companyId: string,
        dealId: string,
        documentName: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}/documents/store`,
            onSuccess: onSuccess,
            onError: onError,
            data: {name: documentName},
            options: {
                ignoreAbortController: true,
            },
        });
    }

    uploadDealDocument(
        companyId: string,
        dealId: string,
        formData: FormData,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}/documents/upload`,
            onSuccess: onSuccess,
            onError: onError,
            data: formData,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    updateDealDocument(
        companyId: string,
        dealId: string,
        data: {id: string; name: string},
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}/documents/update`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    getDealDocuments(companyId: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}/documents`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    deleteDealDocument(companyId: string, dealId: string, docId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/deal/${dealId}/documents/delete`,
            onSuccess: onSuccess,
            onError: onError,
            data: {id: docId},
        });
    }

    getDealsTotals(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${companyId}/totals`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getDealRejectOptions(option: "withdraw" | "reject", onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/interactions/options`,
            onSuccess: onSuccess,
            onError: onError,
            data: {option},
            id: option,
        });
    }

    approveDeal(company_id: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/approve`,
            onSuccess: onSuccess,
            onError: onError,
            data: {id: dealId},
        });
    }

    declineDeal(
        company_id: string,
        data: {
            id: string;
            reason: string;
            reason_description: string | null;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/decline`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    withdrawDeal(
        company_id: string,
        data: {
            id: string;
            reason: string;
            reason_description: string | null;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/withdraw`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    requestInfoDeal(
        company_id: string,
        data: {
            id: string;
            requested_info: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/request-info`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    //? Deal contacts
    getCompanyDealContacts(company_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/contacts`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getDealContacts(company_id: string, deal_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/contacts?deal_id=${deal_id}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    storeDealContact(
        company_id: string,
        dealId: string | null, //? Pass null if you are storing contacts globally
        contacts: Array<{user_id: string; email: string}>,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {contacts};
        if (dealId) {
            data["deal_id"] = dealId;
        }
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/contacts/store`,
            onSuccess: onSuccess,
            onError: onError,
            data,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    deleteDealContacts(
        company_id: string,
        dealId: string | null,
        contactsToRemove: string[],
        onSuccess?: Function,
        onError?: Function,
    ) {
        const data = {contacts: contactsToRemove};
        if (dealId) {
            data["deal_id"] = dealId;
        }
        return apiService({
            method: "delete",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/contacts/delete`,
            onSuccess: onSuccess,
            onError: onError,
            data,
        });
    }

    getDealHistory(
        company_id: string,
        dealId: string,
        filters: IDynamicFiltersOptions,
        onSuccess?: Function,
        onError?: Function,
    ) {
        const search = buildApiFilters(filters);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/deal/${dealId}/logs${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getDealHistoryFilters(company_id: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/deal/${dealId}/logs/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getDealHistoryCSV(company_id: string, dealId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/deal/${dealId}/logs/csv`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    updateDealRegistrationExpirationDays(
        company_id: string,
        dealId: string,
        expiration_days: number | null,
        onSuccess?: Function,
        onError?: Function,
    ) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}deals/${company_id}/config-expiration`,
            onSuccess: onSuccess,
            onError: onError,
            data: {id: dealId, expiration_days},
        });
    }
}

export const dealRegistrationService = new DealRegistrationService();
