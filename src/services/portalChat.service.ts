import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {ContactListTypes} from "../enums/contactListTypes.enum";
import {apiService} from "./api.service";

class PortalChatService {
    getChatList(
        options: {
            receiver_user_id: string;
            paged?: boolean;
            page?: number;
            start_date?: string;
            end_date?: string;
            items_per_page?: number;
            order_by?: string;
            sort?: string;
            search_word?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        let search = `?receiver_user_id=${options.receiver_user_id}`;
        if (options.paged) {
            search += `&paged=${options.paged ? "1" : "0"}`;
        }
        if (options.page) {
            search += `&page=${options.page}`;
        }
        if (options.start_date) {
            search += `&start_date=${options.start_date}`;
        }
        if (options.end_date) {
            search += `&end_date=${options.end_date}`;
        }
        if (options.items_per_page) {
            search += `&items_per_page=${options.items_per_page}`;
        }
        if (options.order_by) {
            search += `&order_by=${options.order_by}`;
        }
        if (options.sort) {
            search += `&sort=${options.sort}`;
        }
        if (options.search_word) {
            search += `&search_word=${options.search_word}`;
        }
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + "chat/one-to-one/list" + search,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    searchChatList(receiver_user_id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: SERVER_API_BASE_V1 + `chat/one-to-one?reciever_user_id=${receiver_user_id}`,
            onError: onError,
            onSuccess: onSuccess,
        });
    }

    sendPortalChatMessage(
        data: {
            receiver_user_email: any;
            message: string;
            redirect_url?: string;
        },
        onSuccess?: Function,
        onError?: Function,
    ) {
        const formData = new FormData();
        formData.append("receiver_user_email", "<EMAIL>");
        formData.append("message", data.message);
        if (data.redirect_url) {
            formData.append("redirect_url", data.redirect_url);
        }
        return apiService({
            method: "post",
            headers: {"Content-Type": "multipart/form-data"},
            url: SERVER_API_BASE_V1 + "chat/one-to-one/send",
            onError: onError,
            onSuccess: onSuccess,
            data,
        });
    }

    sendMassMessageToMSP(
        message: string,
        company_id: string,
        visibilityType?: string,
        onSuccess?: Function,
        onError?: Function,
    ) {
        let url = SERVER_API_BASE_V1 + "chat/one-to-one/send-message-to-msps";
        if (visibilityType) {
            url = url + `?media_visibility=${visibilityType}`;
        }
        return apiService({
            method: "post",
            url,
            onError: onError,
            onSuccess: onSuccess,
            data: {
                message,
                creator_company_id: company_id,
            },
        });
    }

    sendMassMessageToContactList(
        message: string,
        company_id: string,
        contact_list_type: ContactListTypes,
        contact_list_id: string,
        message_type_option: string,
        selected_partners?: string[],
        onSuccess?: Function,
        onError?: Function,
    ) {
        const url = SERVER_API_BASE_V1 + "chat/one-to-one/send-message-to-contact-list";
        return apiService({
            method: "post",
            url,
            onError: onError,
            onSuccess: onSuccess,
            data: {
                message,
                creator_company_id: company_id,
                contact_list_type,
                contact_list_id,
                selected_partners: selected_partners ?? null,
                message_type_option,
            },
        });
    }
}

export default new PortalChatService();
