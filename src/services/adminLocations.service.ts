import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import {buildApiFilters, IDynamicFiltersOptions} from "../utils/apiHelpers.util";

class AdminLocationsService {
    getLocations(companyId: string, options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const search = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/company/locations${search}${search ? "&" : "?"}parent_id=${companyId}`,
            onSuccess,
            onError,
        });
    }

    storeLocation(data: any, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}admin/company/locations/store`,
            onSuccess,
            onError,
            data,
        });
    }

    getLocationAvailableRoles(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/company/locations/available-roles`,
            onSuccess,
            onError,
        });
    }

    getLocationsAvailableParents(options: IDynamicFiltersOptions, onSuccess?: Function, onError?: Function) {
        const search = buildApiFilters(options);
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/company/locations/available-parents${search}`,
            onSuccess,
            onError,
        });
    }
}

export const adminLocationsService = new AdminLocationsService();
