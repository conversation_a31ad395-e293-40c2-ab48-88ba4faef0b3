import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

class FeatureFlagService {
    getAllFeatureFlags(needThisForTableComponentButItsNothing: any, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}feature-flag`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getFeatureFlag(id: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}feature-flag/find-by-id/${id}`,
            onSuccess: onSuccess,
            onError: onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    getFeatureFlagByName(name: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}feature-flag/find-by-name/${name}`,
            onSuccess: onSuccess,
            onError: onError,
            options: {
                ignoreAbortController: true,
            },
        });
    }

    update(id: string, name: string, activated: boolean, onSuccess: Function, onError: Function) {
        const json = {
            id: id,
            name: name.toUpperCase(),
            activated: activated,
        };

        return apiService({
            method: "put",
            url: `${SERVER_API_BASE_V1}feature-flag/update`,
            data: json,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    create(name: string, activated: boolean, onSuccess?: Function, onError?: Function) {
        const json = {
            name: name.toUpperCase(),
            activated: activated,
        };

        return apiService({
            method: "post",
            url: `${SERVER_API_BASE_V1}feature-flag/store`,
            data: json,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const featureFlagService = new FeatureFlagService();
