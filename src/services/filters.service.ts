import {AxiosResponse} from "axios";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";
import type {TFiltersQueryParams} from "../hooks/fetches/useFilters";
import {buildFilterQuery} from "../utils/filters.util";
import {apiService} from "./api.service";

class FilterService {
    GetUserTypes(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/user-types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetProfilesTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/all-profile-types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetProductsCategories(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/products-categories`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetVideoCategories(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/videos-categories`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetUsedCategories(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/used-categories`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetAllCategories(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}categories`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    GetTopVideoCategoriesAndTags(onSuccess: Function, onError: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/top-video-categories-and-tags`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchBubbleMetrics(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/metrics`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchBubbleDates(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/pitch-poll/dates`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchVendorsByDate(dates: string[], onSuccess?: Function, onError?: Function) {
        let search = "";
        if (dates.length) {
            dates.forEach((date, index) => (search += `${index === 0 ? "?" : "&"}dates[]=${date}`));
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/pitch-poll/vendors${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchPollProductCategoriesByVendor(vendors: string[], onSuccess?: Function, onError?: Function) {
        let search = "";
        if (vendors.length) {
            vendors.forEach((date, index) => (search += `${index === 0 ? "?" : "&"}vendors[]=${date}`));
        }
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/pitch-poll/products-categories${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchEventsWithAnswers(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/pitch-events`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getQuadrantProductCategories(pitch_events: string[], onSuccess?: Function, onError?: Function) {
        let search = "";
        pitch_events.forEach(
            (event, index) => (search += index === 0 ? `?pitch_events[]=${event}` : `&pitch_events[]=${event}`),
        );
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/quadrants/products-categories${search}`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPitchPollCompanies(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/chart/pitch-poll/companies`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getNavistackDocumentsFilters(companyId: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/page/${companyId}/template/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getMspContractBillingTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/contracts/billing-types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getMyPartnersFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: buildFilterQuery(`${SERVER_API_BASE_V1}partners/filters`, queryParams),
            onSuccess: onSuccess,
            onError: onError,
            id: "myPartnerFilters",
        });
    }

    getMyContactListPartnersFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: buildFilterQuery(`${SERVER_API_BASE_V1}partners/filters`, queryParams),
            onSuccess: onSuccess,
            onError: onError,
            id: "myPartnerContactList",
        });
    }

    getMspContractTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/contracts/types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCurrencyTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}currencies`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractNotificationTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/contracts/notification-types`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getContractBillingTypeOptions(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/contracts/billing-type-options`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPartnerInvitationsFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: buildFilterQuery(`${SERVER_API_BASE_V1}partners/filters`, {
                ...queryParams,
                type: "invite",
            }),
            onSuccess: onSuccess,
            onError: onError,
            id: "myPartnerInvites",
        });
    }

    getFrequencyOptions(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}filters/recurrences`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getBrandCompanies(
        options: {
            name: string;
            company_type: string;
            type_is_of_vendor: boolean;
            ignore_ids?: string;
            has_main_company?: "1" | "0";
        },
        onSuccess?: Function,
        onError?: Function,
    ): Promise<AxiosResponse<any, any>> {
        return apiService({
            method: "get",
            url: buildFilterQuery(`${SERVER_API_BASE_V1}filters/affiliate-brands`, {
                ...options,
                type_is_of_vendor: options.type_is_of_vendor ? "1" : "0",
            }),
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getMspClientsFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${queryParams?.friendlyUrl ?? ""}/clients/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getAdminCompanyUsersFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/company/${queryParams?.company_id ?? ""}/users-and-claimers/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCompanyAffiliatesFilters(queryParams?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${queryParams?.friendly_url ?? ""}/affiliates/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getMassMessageTypes(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}lookups/mass_messaging_type_option/options`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getUsersFilters(_?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/user/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getAdminTemplateRolesFilters(_?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/permission/template-role/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getChannelDealsStatuses(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}lookups/channel_deals_statuses/options`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCompanyRolesFilters(config?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${config?.friendly_url}/roles/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getCompanyUsersFilters(config?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}company/${config?.id}/user-management/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getPermissionFilters(_?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}admin/permission/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    companySyncedPartners(config?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/${config?.vendor_id}/customer/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getInviteFilters(config?: TFiltersQueryParams, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}partner/${config?.vendor_id}/invite/filters`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }

    getChannelDealRequestStatuses(onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "get",
            url: `${SERVER_API_BASE_V1}lookups/channel_deals_claimed/options`,
            onSuccess: onSuccess,
            onError: onError,
        });
    }
}

export const filterService = new FilterService();
