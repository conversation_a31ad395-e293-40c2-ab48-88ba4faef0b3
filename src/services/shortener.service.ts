import {apiService} from "./api.service";
import {SERVER_API_BASE_V1} from "../constants/commonStrings.constant";

export enum ShortenerModelType {
    Video = "Video",
    Document = "Document",
    User = "User",
    Company = "Company",
    Product = "Product",
}

class ShortenerService {
    async getUrl(model_type_enum: ShortenerModelType, model_id: string, onSuccess: Function, onError: Function) {
        const data = {
            model_type_enum,
            model_id,
        };
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "shortener/get-for-model",
            data,
            onError,
            onSuccess,
        });
    }

    async createUrl(destinationUrl: string, onSuccess?: Function, onError?: Function) {
        return apiService({
            method: "post",
            url: SERVER_API_BASE_V1 + "shortener/create",
            data: {destination: destinationUrl},
            onError,
            onSuccess,
        });
    }
}

export const shortenerService = new ShortenerService();
