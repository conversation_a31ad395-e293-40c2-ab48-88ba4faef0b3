.companyPortalContainer
    min-height: 100vh,
    max-width: 100vw,
    overflow-x: hidden,
    margin: 40px 120px,
    border-radius: 10px !important
    @media (max-width: 1200px)
        margin: 20px 40px
    @media (max-width: 599px)
        margin: 10px 10px
.borderedContent
    border: 1px solid rgba(0, 0, 0, 0.1)
    border-radius: var(--Radius)
    overflow: hidden
    padding: 24px 24px 0px 34px
    margin-bottom: 25px
    background-color: var(--cpWhite)
.navItem
    cursor: pointer
    width: 100%
    a
        padding: 20px 0px 10px 0px
        border-bottom: 1px solid rgba(0, 0, 0, 0.1)
        width: 100%
    p
        display: block
        padding: 0.5rem 1rem
    &:hover a, & a[aria-selected="true"]
        border-bottom: 2px solid var(--cpBlack)
        p
            color: var(--cpBlack)
            padding-bottom: calc(0.5rem - 1px)

.claimerContainer
    display: flex
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    padding-bottom: 12px
    &:hover span
        text-decoration: underline

.avatarContainer
    width: 75px
    height: 75px
    border-radius: 50%
    border: 1px solid rgba(0, 0, 100, 0.1)
    overflow: hidden
    img
        width: 100%
        height: 100%
        object-fit: cover

.claimerName
    display: flex
    align-items: center
    margin-left: 12px
    span
        font-size: 18px
        color: var(--AccentColor)

.disabledLink
    pointer-events: none
    *
        color: var(--cpNavy)!important
