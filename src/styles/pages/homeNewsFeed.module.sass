.homeCardGrid
    background-color: var(--cpRouteBg) !important
    height: 100%

.upcomingPitchInfo
    margin: 10px 10px 0 10px
    @media (min-width: 768px)
        margin: 10px 0 0 10px
    @media (min-width: 992px)
        margin: 0

.newsFeedContainer
    margin: 10px 10px 0 10px
    @media (min-width: 768px)
        margin: 10px 10px 0 10px
    @media (min-width: 992px)
        margin: 0 0 0 10px

.cardContainer, .cardContainerCustom
    padding: 10px
    margin-bottom: 15px
    border-radius: 10px
    border: 1px solid var(--cpBlueBorder)

.cardContainer
    background-color: var(--cpGrayBg5) !important

.cardContainerCustom
    h2, h5
        color: inherit !important

.pitchSpeakersContainer
    @media (min-width: 768px)
        max-height: 460px
        overflow-y: auto

.speakerCard
    background: var(--cpWhite)
    width: 100% !important
    @media (min-width: 1700px)
        width: 49% !important
    a div div h3
        word-break: break-word!important
        font-size: 16px!important
    a div div span
        font-size: 13px!important

.profileImageContainer
    width: 120px
    height: 120px
    img
        width: 100%
        height: 100%
        object-fit: cover

.switchTabBtn
    border: 0
    border-bottom:2px solid transparent
    margin-right: 0
    color: #999999 !important
    padding: 10px 15px
    margin-right: 12px
    font-weight: 400 !important
    text-transform: capitalize

.switchTabBtn:hover
        color: var(--cpNavy) !important

.activeTabBtn
    border-bottom:3px solid rgba(255,97,32, 1)
    text-decoration: none !important
    color: black !important
    font-weight: normal !important
    cursor: default !important
    font-weight: 400 !important

.techStackBtn
    margin-top: 20px



  

    
