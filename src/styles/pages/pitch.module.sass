.heroPitch
  display: flex
  flex-wrap: wrap
  justify-content: center
  align-items: stretch
  height: auto
  @media (min-width: 992px)
    width: calc(100vw - 270px)
  @media (min-width: 1500px)
    flex-wrap: nowrap
    justify-content: flex-start

.pitchDesc
  flex: 0 0 100%
  max-width: 90vw
  width: 90vw
  height: calc(90vw * 55 / 96)
  background-size: cover
  background-position: center
  display: flex
  align-items: center
  justify-content: flex-end
  flex-wrap: wrap
  @media (min-width: 992px)
    max-width: clamp(500px, calc(50vw - 270px), 800px)
    width: clamp(500px, calc(50vw - 270px), 800px)
    height: calc(clamp(500px, calc(50vw - 270px), 800px) * 55 / 96)
  @media (min-width: 1500px)
    flex: 0 0 auto
    margin-right: 16px

.descContainer
  max-width: 40%
  text-align: center
  margin-right: 10%
  p
    line-height: 1
    font-size: 14px
    color: var(--cpNavy)!important
    text-align: center
    display: none
  hr
    display: none
  img
    width: 37vw
    height: auto
  @media (min-width: 992px)
    margin-right: 9%
    hr
      display: inline-block
      margin: 20px 0
    img
      width: clamp(174.21px, calc(13.94vw - 270px), 278.74px)
      height: auto
    p
      font-size: clamp(12px, .9vw, 18px)
      line-height: 1.4
      display: inline-block
  @media (min-width: 1500px)
    max-width: 41%
    hr
      margin: 1vw 0
    p
      line-height: 1
      display: inline-block

.mobileText
  flex: 1 0 100%
  display: block
  width: 90vw
  text-align: center
  line-height: 1.3
  padding: 24px 0 0
  color: var(--cpNavy)
  @media (min-width: 992px)
    display: none

.nextPitchContainer
  flex: 1 0 100%
  max-width: 90vw
  width: 90vw
  height: auto
  display: flex
  flex-wrap: wrap
  background-color: var(--cpGrayBg4)
  align-items: center
  justify-content: center
  @media (min-width: 992px)
    padding: 24px
    max-width: clamp(500px, 50%, 800px)
    width: clamp(500px, 50%, 800px)
    margin-top: 24px
  @media (min-width: 1500px)
    padding: 24px
    flex: 0 0 auto
    margin-left: 16px
    margin-top: 0
    align-items: center

.nextPitch
  flex: 0 0 100%
  max-width: 90vw
  width: 90vw
  @media (min-width: 992px)
    max-width: clamp(500px, 40vw, 800px)
    width: clamp(500px, 40vw, 800px)
    min-height: calc(clamp(500px, 40vw, 800px) * 17 / 48)

.pitchInfoContainer
  display: flex
  align-items: center
  justify-content: center
  @media (min-width: 992px)
    justify-content: flex-start
.pitchImg
  flex: 0 1 120px
  width: 120px
  @media (min-width: 992px)
    flex: 0 0 auto
    width: 33%

.pitchInfo
  flex: 0 0 auto
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  max-width: 75%
  p
    line-height: 1
    color: var(--cpGrayShade4)
    font-size: 16px
  svg
    fill: var(--cpGrayShade4)
  @media (min-width: 992px)
    align-items: center
    flex: 0 1 auto
    width: 100%
    p
      font-size: clamp(12px, .9vw, 18px)

.pitchCta
  flex: 0 0 100%
  max-width: 90vw
  width: 90vw
  height: auto
  padding: 25px
  text-align: center
  background-size: cover
  background-position: 50%
  border-radius: 20px
  overflow: hidden
  display: flex
  align-items: center
  justify-content: center
  flex-flow: column
  margin: 0 auto 48px
  p
    color: var(--cpBg)
    padding-bottom: 10px
  a
    line-height: 0.7!important
    padding: 16px 0!important
    width: 100%
  @media (min-width: 992px)
    padding: 0
    margin-top: 27px
    max-width: clamp(500px,50vw,800px)
    width: clamp(500px,50vw,800px)
    height: calc(clamp(500px, 50vw, 800px)*133/768)
    a
      line-height: 0.7!important
      padding: 16px 92px!important
      width: auto
  @media (min-width: 1500px)
    padding: 0
    margin-top: 27px
    max-width: clamp(500px, 40vw, 800px)
    width: clamp(500px, 40vw, 800px)
    height: calc(clamp(500px, 40vw, 800px) * 133 / 768)
    a
      line-height: 0.7!important
      padding: 16px 92px!important
      width: auto

.sectionHeader
  svg
    fill: var(--cpOrange)
  > svg, & > div
    flex: 1 1 100%
    margin: 10px 0
    @media (min-width: 992px)
      flex: 0 0 auto
      margin: 0

.pitchInfoList
  margin: 0
  @media (min-width: 768px)
    margin: 0 auto

.pitchInfoItem
  display: flex
  justify-content: flex-start
  text-align: left
  margin-bottom: 40px
  align-items: center
  flex-wrap: wrap
  > div:first-child
    padding-top: 2px
  > div
    flex: 1 1 100%
  p
    text-align: center!important
    line-height: 1
    color: var(--cpGray)
    font-size: 16px
  svg
    width: 24px
    height: 8px
  @media (min-width: 992px)
    align-items: flex-start
    p
      text-align: left!important
    > div
      flex: 1 1 auto
      text-align: left

.pitchCard
  border-left: 8px solid var(--cpNavy)
  background: var(--cpGrayBg)
  border-radius: 10px
  min-width: 300px
  max-width: 350px
  display: flex
  flex-flow: column
  align-items: center
  justify-content: center
  padding: 0 40px
  margin: 20px auto
  h3
    font-size: 20px!important
    font-weight: 500!important
  p
    padding: 3px 0
  svg
    fill: var(--cpGray)
  @media (min-width: 768px)
    margin: 20px 0

.addToCalendarWrapper
  position: relative
  position: relative
  display: inline

button.addToCalendarButton
  white-space: nowrap
  padding: 12px 16px!important
  min-width: 20px!important
  width: auto!important
  font-size: clamp(14px, .9vw, 16px)!important

button.unregisterBtn
  background-color: var(--cpBg)!important
  color: var(--cpOrange)!important
  border: 1px solid var(--cpOrange)!important
  padding: 12px 16px!important
  min-width: 20px!important
  width: auto!important
  font-size: clamp(14px, .9vw, 16px)!important
  &:hover
    color: var(--cpOrange)!important

.featuredSpeakers
  padding: 15px 0
  .title
    text-align: left
    letter-spacing: 0px
    color: #062556
    text-transform: capitalize
    font-weight: 500
  .speakers
    padding: 0 !important
    margin-top: 30px
  .speakerWrapper
    margin-bottom: 20px
    @media (min-width: 1900px)
      width: 25% !important
