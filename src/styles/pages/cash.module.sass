.podcastWrapper 
    padding: 4rem
    @media only screen and (max-width: 800px)
        padding: 4rem 0rem

.connector
    display: block
    margin: 0 auto

.pageTitle
    text-align: center
    font-size: 3.5rem !important
    margin: 2.5rem auto !important

.podcastFooter
    background: #ff6120
    max-width: unset
    padding: 4rem 0
    position: relative

.podcastFooterImg
    width: 50%
    display: block
    margin: 0 auto
    position: relative
    z-index: 2
    @media only screen and (max-width: 810px)
        width: 75%

.podcastFooterBG
    position: absolute
    z-index: 1
    bottom: -15%

.podcastFooterBtn
    position: relative
    z-index: 2
    margin: 4rem auto 0
    padding: .75rem 1.5rem
    border-radius: 50px
    background: var(--cpNavy)
    display: block
    a
        color: white
        font-weight: 600 !important