.contentContainer
    display: flex
    flex-wrap: wrap
    gap: 24px
    @media (min-width: 1500px)
        flex-wrap: nowrap

.videoContainer
    flex: 0 0 auto
    width: 100%
    svg
        fill: var(--cpBlue)
    iframe
        width: 100%
        height: auto
        aspect-ratio: 16/9
    @media (min-width: 1500px)
        width: 59%

.chatContainer
    flex: 0 0 100%
    width: 100%
    margin-top: 20px
    @media (min-width: 1500px)
        margin-top: 0
        flex: 0 0 auto
        width: 39%

.endMessage
    svg
        fill: var(--cpBlue)
        width: 100%
        height: 100%

.breakoutContainer
    display: flex
    flex-wrap: wrap
    margin-top: 20px

.breakoutHeader
    flex: 0 0 auto
    width: auto
    display: flex
    align-items: center
    justify-content: center
    color: var(--cpBlue)!important
    background: var(--cpGrayBg5)
    border-radius: 10px 10px 0 0
    padding: 16px 18px
    border: 1px solid var(--cpGrayShade3)
    border-bottom: none!important
    margin-right: auto!important
    margin-bottom: -1px!important
    z-index: 2
    position: relative
    svg
        fill: var(--cpBlue)!important
        margin: 0!important
    h3
        line-height: 1.2!important
        font-size: 16px!important

.roomsPanel
    flex: 0 0 100%
    width: 100%
    padding: 30px 24px
    display: flex
    flex-wrap: wrap
    background: var(--cpGrayBg5)
    border: 1px solid var(--cpGrayShade3)
    border-radius: 0 10px 10px 10px

.breakoutRoom
    flex: 0 0 auto
    width: calc(50% - 10px)
    background: var(--cpBg)
    border-radius: 10px
    display: flex
    align-items: center
    padding: 16px
    margin-top: 16px
    .breakoutRoomAvatar
        flex: 0 0 auto
        width: 50px
        height: 50px
        border-radius: 50%
        margin-right: 12px
        > img
            width: 100%
            height: 100%
            border-radius: 50%
            object-fit: cover
    > div
        white-space: pre-wrap
        word-break: break-word
    h4
        font-size: 16px!important
        line-height: 1.2!important

.breakoutRoom:nth-child(even)
    margin-left: 10px

.breakoutRoom:nth-child(odd)
    margin-right: 10px

.breakoutBtn
    color: var(--cpBlue)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    padding: 8px 16px!important
    margin-left: auto
    &:hover
        color: var(--cpBlue)!important

.pitchForm
    display: flex
    flex-direction: row
    justify-content: space-between
    padding: 0 10px
    margin-top: 10px
    @media (min-width: 1200px)
        padding-right: 60px
    div:first-child
        width: calc(100% - 70px)
        max-width: 100% !important
        margin: 0
        position: unset