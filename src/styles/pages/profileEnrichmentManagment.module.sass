.linkText
    color: #0d6efd
    cursor: pointer

.cardContainer
    background: #F8F9FA 0% 0% no-repeat padding-box
    border: 1px solid #E8EAEE
    border-radius: 10px
    opacity: 1
    padding: 15px
    margin-bottom: 20px

.addOptionBtn
    background: var(--cpBlue) !important
    border-radius: 20px !important
    padding: 8px 32px !important
    margin: 0px 16px !important
    color: var(--cpBg) !important
    border: 1px solid var(--cpBlue) !important
    display: inline-flex !important
    align-items: center
    justify-content: center

.removeBtn
    display: inline-flex!important
    align-items: center
    background: var(--cpBlue) !important
    border-radius: 20px!important
    padding: 5px!important
    color: var(--cpBg)
    margin: 5px !important
    > div
        margin-left: 0!important
        margin-right: 0!important
    svg
        fill: var(--cpBg)

.editBtn
    background: var(--cpBlue)!important
    border-radius: 20px!important
    padding: 8px 32px!important
    margin: 0px 16px !important
    color: var(--cpBg)!important
    border: 1px solid var(--cpBlue)!important
    display: inline-flex!important
    align-items: center
    justify-content: center
    > div
        margin: 0 8px 0 0 !important
    svg
        fill: var(--cpBg)!important

.cancelBtn
    background: var(--cpBg)!important
    border-radius: 20px!important
    padding: 8px 32px!important
    margin: 0px 16px !important
    color: var(--cpBlue)!important
    border: 1px solid var(--cpBlue)!important