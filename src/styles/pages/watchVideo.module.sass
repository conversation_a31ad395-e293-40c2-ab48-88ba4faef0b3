.descriptionText, .companyText
    span
        margin-bottom: 0!important
        text-align: left

.companyText
    display: flex
    align-items: center
    justify-content: center
    flex-wrap: wrap
    flex: 0 0 auto
    width: 100%
    margin-top: 12px
    @media (min-width: 768px)
        flex: 0 0 auto
        width: auto
        max-width: 65%
        margin-left: 16px
        flex-flow: column nowrap
        align-items: flex-start
        justify-content: flex-start

.companyTitle
    display: flex
    cursor: pointer
    align-items: center

.companyName
    padding: 0!important
    margin: 0!important
    h3
        padding: 0!important

.followBtn
    margin-top: 12px
    flex: 0 0 auto
    width: 100%
    text-align: center
    @media (min-width: 768px)
        margin-left: auto
        width: auto
        text-align: left

.companyImageContainer
    position: relative
    flex: 0 0 auto
    width: 100%
    display: flex
    flex-flow: column
    align-items: center
    justify-content: center
    > div:last-child
        position: static !important
        margin-top: 8px
    @media (min-width: 768px)
        width: auto

.companyImage
    max-width: 150px
    min-width: 150px
    max-height: 150px
    height: 150px
    border-radius: 50%
    border: 1px solid var(--cpBlueBorder)
    overflow: hidden
    cursor: pointer
    img
        width: 100%
        height: 100%
        object-fit: cover

.videoColumn
    flex: 0 0 auto
    width: 100%
    @media (min-width: 1300px)
        flex: 0 0 auto
        width: 70%
        padding-right: 24px

.listColumn
    flex: 0 0 auto
    width: 100%
    margin-top: 32px
    @media (min-width: 1300px)
        flex: 0 0 auto
        width: 30%
        padding-left: 8px
        margin-top: 0

.videosContainer
    display: flex
    flex-wrap: wrap

.videoContainer
    flex: 0 0 100%
    display: flex
    cursor: pointer
    margin-bottom: 24px
    max-width: 500px
    &:last-child
        margin-right: 0
    img
        width: 100%
        aspect-ratio: 16/9
        object-fit: cover
    @media (min-width: 992px)
        flex: 0 0 50%
    @media (min-width: 1300px)
        flex: 0 0 100%

.videoThumbnail
    flex: 0 0 auto
    width: 40%

.videoInfo
    padding-left: 8px
    max-width: 60%
    h4
        font-size: 18px!important
        font-weight: bold!important
        color: var(--cpBlue)!important
        font-family: var(--Nimbus)!important
        padding: 0!important
    span
        font-size: 16px!important
        margin: 0!important
.videoCategoriesWrapper
    display: flex
    flex-wrap: wrap
    padding-left: 0.5rem
    margin-top: 10px
    gap: 20px
.categoryLink
    color: var(--cpBlue)!important
    font-weight: 400
    text-decoration: underline!important
    cursor: pointer

.latestTitle
    padding: 0!important
    margin: 0!important
    margin-bottom: 16px!important
    h3
        padding: 0!important

.handle
    cursor: pointer
    font-weight: 600
    span
        color: var(--cpBlue)!important

.fullpageLoader
    width: 100%
    height: calc(100vh - 100px)
    display: flex
    justify-content: center
    align-items: center
    > span
        width: 150px
        height: 150px
        display: flex
        justify-content: center
        align-items: center
        a, svg
            width: 100%
            height: 100%
