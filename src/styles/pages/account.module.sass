.borderedContent
    border: 1px solid rgba(0, 0, 0, 0.1)
    border-radius: var(--Radius)
    padding: 12px 18px

.navItem
    cursor: pointer
    border: 1px solid rgba(0, 0, 0, 0.1)
    a, h4
        color: var(--blue-800)
        font-size: 20px !important
    h4
        display: block
        padding: 0.5rem 1rem
        line-height: var(--bs-body-line-height)
        font-weight: var(--bs-body-font-weight)
        cursor: default
    &:hover a, & a[aria-selected="true"]
        color: var(--blue-800)
        font-weight: 800
    &:first-child
        border-top-left-radius: var(--Radius)
        border-top-right-radius: var(--Radius)
    &:last-child
        border-bottom-left-radius: var(--Radius)
        border-bottom-right-radius: var(--Radius)

.personalInfo
    max-width: 800px
    margin: 24px auto 0

.infoIcon
    max-width: 20px
