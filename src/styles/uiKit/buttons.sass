button
    &[disabled]
        opacity: 0.65!important
        cursor: not-allowed!important

.cpFontFamily
    font-family: var(--bs-btn-font-family)
    font-size: 16px !important

.arrowSizeLogin
    height: 15px
    margin:0 10px 0 10px

.cpMainBtnLoginMd
    background-color: var(--cpOrange)!important
    color: var(--cpBg)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    width: 66%
    font-family: var(--bs-btn-font-family)

.cpMainBtnLoginMdBack
    background-color: #ffffff !important
    color: #0c2556 !important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: solid 1px #0c2556 !important
    border-radius: var(--BtnRadius)!important
    width: 66%
    font-family: var(--bs-btn-font-family)

.cpMainFormBtnActive
    background-color: #ffffff !important
    color: #0C2556 !important
    padding: 6px 20px !important
    border: solid 1px #0c2556 !important
    border-radius: var(--BtnRadius)!important
    font-size: 14px !important

.cpMainFormBtnBlue
    background-color: #0C2556 !important
    color: #ffffff !important
    padding: 6px 20px !important
    border: solid 1px #ffffff !important
    border-radius: var(--BtnRadius)!important
    font-size: 14px !important

.cpMainFormBtn
    background-color: #ffffff !important
    color: #0c2556 !important
    padding: 6px 20px !important
    border: solid 0px #0c2556 !important
    border-radius: var(--BtnRadius)!important
    font-size: 14px !important

.errorPasswordRules
    list-style: none
    padding: 0
    margin: 0

.errorPasswordRules li
    display: flex
    align-items: center
    margin-bottom: 8px
    font-size: 16px
    &.valid
        color: #3B8439 !important
    &.invalid
        color: #B82828 !important

.errorPasswordRules li span
            margin-left: 8px !important

.cpMainBtnLoginBack
    background-color: #ffffff !important
    color: #0c2556 !important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: solid 1px #0c2556 !important
    border-radius: var(--BtnRadius)!important
    width: 100%
    font-family: var(--bs-btn-font-family)

.react-tel-input .flag
    border-radius: 50%
    zoom: 130%

.cpMainBtnLogin
    background-color: var(--cpOrange)!important
    color: var(--cpBg)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    min-width: 100%
    font-family: var(--bs-btn-font-family)

.cpMainBtn, .btn-cpMainBtn
    background-color: var(--cpOrange)
    color: var(--cpBg)!important
    font: var(--BtnFont)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpBg)!important
    @media (min-width: 992px)
        min-width: var(--BtnMinWidth)

.cpAltBtn, .btn-cpAltBtn
    background: none!important
    color: var(--cpOrange)!important
    font: var(--BtnFont)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: 1px solid var(--cpOrange)!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpOrange)!important
    @media (min-width: 992px)
        min-width: var(--BtnMinWidth)

.cpBlueBtn, .btn-cpBlueBtn
    background-color: var(--cpBlue)!important
    color: var(--cpBg)!important
    font: var(--BtnFont)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpBg)!important
    @media (min-width: 992px)
        min-width: var(--BtnMinWidth)

.cpBlueAltBtn, .btn-cpBlueAltBtn
    background: none!important
    color: var(--cpBlue)!important
    font: var(--BtnFont)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpBlue)!important
    @media (min-width: 992px)
        min-width: var(--BtnMinWidth)

.btn-login
    display: none!important
    background-color: var(--cpOrange)!important
    color: var(--cpWhite)!important
    font: var(--BtnFont)!important
    padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    min-width: 165px
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    @media (min-width: 992px)
        display: flex!important

.btn-register
    display: none!important
    background-color: var(--cpBg)!important
    color: var(--cpOrange)!important
    font: var(--BtnFont)!important
    padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important
    border: 1px solid var(--cpOrange)!important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    min-width: 165px
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    @media (min-width: 992px)
        display: flex!important

.cpPodcastBtn
    background-color: var(--cpOrange)!important
    color: var(--cpBg)!important
    font: var(--BtnFont)!important
    padding: var(--MainBtnPaddingY) var(--BtnPaddingX)!important
    border: none!important
    border-radius: var(--BtnRadius)

    &:hover
        color: #fcfcfc!important
    & svg
        margin-left: 5px

.buttonAsLink
    background: none!important
    padding: 0!important
    border: none!important
    span
        color: #315EC3 !important
        text-decoration: none !important

.textBtn
    background: none!important
    color: var(--cpGray)!important
    padding: 0!important
    border: none!important
    span
        color: var(--AccentColor)!important
        text-decoration: underline!important

.highlightTextBtn
    background: none!important
    padding: 0!important
    border: none!important
    font-size: 22px!important
    color: var(--cpNavy)!important
    *
        fill: var(--cpNavy)!important

.btn-themeTogglerLight
    background-color: var(--cpNavy)!important
    color: #fcfcfc!important
    svg
        fill: #fcfcfc!important

.btn-themeTogglerDark
    background-color: #fcfcfc!important
    color: var(--cpNavy)!important
    svg
        fill: var(--cpNavy)!important

.cpMainBtnThin, .btn-cpMainBtnThin
    background-color: var(--cpOrange)!important
    color: var(--cpBg)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    &:hover
        color: var(--cpBg)!important

.cpAltBtnThin, .btn-cpAltBtnThin
    background: none!important
    color: var(--cpOrange)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: 1px solid var(--cpOrange)!important
    border-radius: var(--BtnRadius)!important
    &:hover
        color: var(--cpOrange)!important

.cpBlueBtnThin, .btn-cpBlueBtnThin
    background-color: var(--cpBlue)!important
    color: var(--cpBg)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: none!important
    border-radius: var(--BtnRadius)!important
    &:hover
        color: var(--cpBg)!important
    &[disabled]
        opacity: 0.65!important
        cursor: not-allowed!important

.cpBlueAltBtnThin, .btn-cpBlueAltBtnThin
    background: none!important
    color: var(--cpBlue)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    &:hover
        color: var(--cpBlue)!important

.cpStackBtn
    background-color: var(--cpBlue)!important
    color: var(--cpBg)!important
    font: var(--BtnFont)!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpBg)!important

.cpUnstackBtn
    background: none!important
    color: var(--cpBlue)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 2) calc(var(--BtnPaddingX) / 2)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    &:hover
        color: var(--cpBlue)!important

.show-more-following
    background: none!important
    color: var(--cpBlue)!important
    font: 18px!important
    line-height: 18px!important
    padding: calc(var(--MainBtnPaddingY) / 3) calc(var(--BtnPaddingX) / 3)!important
    border: 1px solid var(--cpBlue)!important
    border-radius: var(--BtnRadius)!important
    min-width: 150px
    margin: 8px auto 0!important
    &:hover
        color: var(--cpBlue)!important

.paginationLinks
    .page-link
        cursor: pointer

.cpLikeBtn
    background: none !important
    background-color: none !important
    font-size: 18px !important
    padding: 5px 0px !important
    border: none !important
    span
        color: var(--cpNavy) !important
        font-size: 18px !important
    span.likeCountOpenModal
        color: var(--cpNavy) !important
        font-size: 18px !important
        text-decoration: none
        &:hover
            text-decoration: underline
            font-weight: 600
    &:hover
        svg
            fill: var(--cpNavy)

.cpLikeBtnAnimation
    --webkit-animation: cpLikeAnimation 0.3s ease-in-out 1
    --moz-animation: cpLikeAnimation 0.3s ease-in-out 1
    --oanimation: cpLikeAnimation 0.3s ease-in-out 1
    --ms-animation: cpLikeAnimation 0.3s ease-in-out 1
    animation: cpLikeAnimation 0.3s ease-in-out 1
    transform-origin: center bottom


.cpCommentBtn
    background: none !important
    background-color: none !important
    font-size: 18px !important
    padding: 5px 0px !important
    border: none !important
    span
        color: var(--cpNavy) !important
        font-size: 18px !important
    &:hover
        svg
            fill: var(--cpNavy)

.cpCommentBtnAnimation
    --webkit-animation: cpLikeAnimation 0.3s ease-in-out 1
    --moz-animation: cpLikeAnimation 0.3s ease-in-out 1
    --oanimation: cpLikeAnimation 0.3s ease-in-out 1
    --ms-animation: cpLikeAnimation 0.3s ease-in-out 1
    animation: cpLikeAnimation 0.3s ease-in-out 1
    transform-origin: center bottom

@keyframes cpLikeAnimation
    0%
        transform: scale(0.8)
    35%
        transform: scale(1.2)
    100%
        transform: scale(1.0)

.addeventatc
    color: var(--cpWhite) !important
    z-index: 1 !important
    .addeventatc_icon
        display: none !important
    #addEventInnerText
        color: var(--cpWhite) !important
        font: var(--BtnFont) !important
        font-weight: 500 !important
        font-size: 16px !important
    em
        display: none !important

.rteEmoji
    cursor: pointer
    position: absolute
    top: 13px
    left: 145px
    z-index: 5
    border: 1px solid rgb(222, 226, 230)
    border-radius: 4px
    height: 28px
    width: 28px
    display: flex
    align-items: center
    justify-content: center
    &:hover
        background-color: #f8f9fa

// New UI Styles
.btn-uiBtnGray
    font-family: var(--BasisGrotesquePro) !important
    font-weight: 400 !important
    font-size: 14px !important
    border-radius: 16px !important
    padding: 8px 16px !important
    border: 1px solid var(--uiGray30) !important
    display: flex !important
    flex-direction: row !important
    align-items: center !important


@keyframes iconBtnAnimation
    0%
        overflow-x: hidden
        transform: translateX(200%)
        opacity: 100%
    50%
        overflow-x: hidden
        transform: translateX(200%)
        opacity: 100%
    50%
        overflow-x: hidden
        transform: translateX(-200%)
        opacity: 0%
    55%
        overflow-x: hidden
        transform: translateX(-100%)
        opacity: 100%
    100%
        overflow-x: hidden
        transform: translateX(0%)
        opacity: 100%

.my-stack-button
  border: none !important
  box-shadow: none
  margin-left: 20px
  padding: 0px !important

  &:hover, &:focus, &:active
    background-color: transparent !important
    color: inherit !important
    text-decoration: none !important
    box-shadow: none !important