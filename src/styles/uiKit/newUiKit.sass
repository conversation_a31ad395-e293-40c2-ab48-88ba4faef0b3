@use "sass:map"
@use "sass:list"
//  --  --  --  --  --  --  --  --  //
    // New variables Jan, 09th 2024
//  --  --  --  --  --  --  --  --  //
:root
    /* Primary */
    --primary-100: #FFF0EB
    --primary-200: #FFC5AD
    --primary-300: #FF9970
    --primary-400: #FF7B47
    --primary-500: #FF6120
    --primary-600: #F54500
    --primary-700: #CC3A00
    --primary-800: #521700
    --primary-light: #FF6120
    --primary-dark: #FF6120
    --primary-main: #FF6120
    --primary-contrastText: #fff

    /* Error */
    --error-100: #FBEEEE
    --error-200: #F4CDCD
    --error-300: #E99B9B
    --error-400: #DA5858
    --error-500: #D33636
    --error-600: #B82828
    --error-700: #972121
    --error-800: #430F0F
    --error-light: #B82828
    --error-dark: #B82828
    --error-main: #B82828
    --error-contrastText: #fff

    /* Warning */
    --warning-100: #FFEFD7
    --warning-200: #FEDEAE
    --warning-300: #FECE86
    --warning-400: #FDBD5D
    --warning-500: #FDA521
    --warning-600: #F29202
    --warning-700: #CA7A02
    --warning-800: #513101
    --warning-light: #FDA521
    --warning-dark: #FDA521
    --warning-main: #FDA521

    /* Neutral */
    --neutral-100: #FFFFFF
    --neutral-200: #F4F6F9
    --neutral-300: #DADFE7
    --neutral-400: #B6C0CE
    --neutral-500: #94A3B8
    --neutral-600: #5A6C87
    --neutral-700: #313B49
    --neutral-800: #101418
    --neutral-900: #000000
    --neutral-light: #94A3B8
    --neutral-dark: #94A3B8
    --neutral-main: #94A3B8

    /* Success */
    --success-100: #E8F4E8
    --success-200: #C7E3C7
    --success-300: #A4D0A4
    --success-400: #67B266
    --success-500: #50A54E
    --success-600: #3B8439
    --success-700: #32732E
    --success-800: #24571A
    --success-light: #3B8439
    --success-dark: #3B8439
    --success-main: #3B8439
    --success-contrastText: #fff

    /* Sky */
    --sky-200: #99DFFF
    --sky-500: #33BFFF
    --sky-700: #008CCC

    /* Purple */
    --purple-50: #DAB2FF
    --purple-100: #FFEBFC
    --purple-200: #FFC2F7
    --purple-300: #FF85EF
    --purple-400: #FF5CE9
    --purple-500: #E000C2
    --purple-600: #A3008D
    --purple-700: #7E22CE
    --purple-800: #3D0035
    --purple-light: #7E22CE
    --purple-dark: #7E22CE
    --purple-main: #7E22CE
    --purple-contrastText: #fff

    /* Green */
    --green-100: #E8F4E8
    --green-200: #BBF7D0
    --green-300: #A4D0A4
    --green-600: #16A34A

    /* Blue */
    --blue-100: #E5F1FE
    --blue-200: #C1DBFC
    --blue-300: #9AC6FA
    --blue-400: #73B0F9
    --blue-500: #428FF7
    --blue-600: #315ec3
    --blue-700: #263fa4
    --blue-800: #0c2556
    --blue-900: #071736
    --blue-light: #0C2556
    --blue-dark: #263fa4
    --blue-main: #0C2556
    --blue-contrastText: #fff

    /* Secondary */
    --secondary-100: #E5F1FE
    --secondary-200: #C1DBFC
    --secondary-300: #9AC6FA
    --secondary-400: #73B0F9
    --secondary-500: #428FF7
    --secondary-600: #315ec3
    --secondary-700: #263fa4
    --secondary-800: #0c2556
    --secondary-900: #071736
    --secondary-light: #0C2556
    --secondary-dark: #263fa4
    --secondary-main: #0C2556
    --secondary-contrastText: #fff

    // Font family and size
    --cp-font: var(--Inter)

$colorPalette: ()
$colors: ('primary', 'secondary', 'error', 'warning', 'success', 'neutral', 'sky', 'purple', 'green', 'blue')
$tones: ('100', '200', '300', '400', '500', '600', '700', '800', '900', 'light', 'dark', 'main', 'contrastText')
@for $i from 1 through list.length($colors)
    $color: list.nth($colors, $i)
    @for $j from 1 through list.length($tones)
        $tone: list.nth($tones, $j)
        $value: var(--#{$color}-#{$tone})
        $colorPalette: map.merge($colorPalette, (('#{$color}-#{$tone}'): $value))

@function color($color, $tone: 'main')
    @return map.get($colorPalette, $color + '-' + $tone)

@mixin generate-color-classes
    @each $color, $value in $colorPalette
        .text-#{$color}
            color: $value
        .bg-#{$color}
            background-color: $value
        .border-#{$color}
            border-color: $value
        .fill-#{$color}
            fill: $value
        .stroke-#{$color}
            stroke: $value
        .\!text-#{$color}
            color: $value !important
        .\!bg-#{$color}
            background-color: $value !important
        .\!border-#{$color}
            border-color: $value !important
        .\!fill-#{$color}
            fill: $value !important
        .\!stroke-#{$color}
            stroke: $value !important

@include generate-color-classes

.newHeroSearch
    width: 100%
