:root
    //Colors
    --cpNavy: #062556
    --cpNavyShade2: #0D2335
    --cpNavyShade3: #04193B
    --cpNavyShade4: #292E36
    --cpOrange: #FF6120
    --cpTextGray: #718096
    --cpGray: #5F6B83
    --cpGrayShade2: #758CA0
    --cpGrayShade2Border: #5F6B831f
    --cpGrayShade3: #E3E5E9
    --cpGrayShade4: #90A3B3
    --cpGrayShade5: #B0ADC5
    --cpBorderColor: #B9C5CF
    --cpBg: #fff
    --cpWhite: #fff
    --cpBlack: #000
    --cpGrayBg: #F2F3F5
    --cpGrayBg2: #f2f4f633
    --cpGrayBg3: #FCFDFD
    --cpGrayBg4: #FBFBFC
    --cpGrayBg5: #F8F9FA
    --cpGrayBg6: #EDF2F7
    --cpRed: #B72121
    --cpRedBg: #FDF6F6
    --cpGreen: #00B14C
    --cpBlue: #004DCE
    --cpYellow: #FFB900
    --cpBlueBackground: #EFF5FC
    --cpBlueBgButton: #004DCE
    --cpBlueBorder: #E8EAEE
    --cpPurple: #DDA0DD
    --cpRouteBg: #ECEDF0
    --cpDarkBlue: #0C2556

    --CompanyColor: #1C8068
    --UserColor: #2661DD
    --InfluencerColor: #7411AD
    --AccentColor: var(--cpOrange)

    //FontSizes
    --size-300: clamp(0.7rem, calc(0.66rem + 0.2vw), 0.8rem)
    --size-400: clamp(0.88rem, calc(0.83rem + 0.24vw), 1rem)
    --size-500: clamp(1.09rem, calc(1rem + 0.47vw), 1.33rem)
    --size-600: clamp(1.37rem, calc(1.21rem + 0.8vw), 1.78rem)
    --size-700: clamp(1.71rem, calc(1.45rem + 1.29vw), 2.37rem)
    --size-800: clamp(2.14rem, calc(1.74rem + 1.99vw), 3.16rem)
    --size-900: clamp(2.67rem, calc(2.07rem + 3vw), 4.21rem)
    --size-1000: clamp(3.34rem, calc(2.45rem + 4.43vw), 5.61rem)

    //General
    --Radius: 10px
    --MainTitleSize: 38px
    --LabelSize: 18px
    --PaddingXMenuItem: 32px
    --PaddingYMenuItem: 12px
    --SectionPaddingY: clamp(12px, 4vw, 24px)
    --RouteSpacingMobile: 0
    --RouteSpacing: 32px clamp(1rem, 0.286rem + 1.905vw, 2rem)
    --SmallRouteSpacing: 24px 12px
    // Gutters
    --gutterLgSize: 48px
    --gutterMdSize: 30px

    //Fonts with Fallbacks
    --Poppins: "Poppins", serif
    --RocGrotesk: "roc-grotesk", arial, verdana, helvetica, sans-serif
    --RocGroteskCompressed: "roc-grotesk-compressed", arial, verdana, helvetica, sans-serif
    --RocGroteskCondensed: "roc-grotesk-condensed", arial, verdana, helvetica, sans-serif
    --RocGroteskExtrawide: "roc-grotesk-extrawide", arial, verdana, helvetica, sans-serif
    --RocGroteskWide: "roc-grotesk-wide", arial, verdana, helvetica, sans-serif
    --Nimbus: "nimbus-sans", arial, verdana, helvetica, sans-serif
    --NimbusCondensed: "nimbus-sans-condensed", arial, verdana, helvetica, sans-serif
    --NimbusExtend: "nimbus-sans-extended", arial, verdana, helvetica, sans-serif
    --Inter: "inter", arial, verdana, helvetica, sans-serif
    --BasisGrotesquePro: "basis-grotesque-pro", arial, verdana, helvetica, sans-serif

    //Button
    --BtnRadius: 100px
    --BtnMinWidth: 200px
    --BtnPaddingX: 32px
    --MainBtnPaddingY: 16px
    --SecondaryBtnPaddingY: 12px
    --BtnFont: normal normal 500 18px/1 var(--RocGrotesk)
    --BtnLoginFont: normal normal 500 18px/0.9 var(--RocGrotesk)
    --BtnFontWeight: 500

    //Label
    --LabelFontFamily: var(--RocGrotesk)
    --LabelFontSize: 18px
    --LabelFontWeight: 400
    --LabelLineHeight: .8

    //Input
    --PaddingInput: clamp(8px, 0.4em, 12px)
    --MaxInputWidth: 575px
    --InputBorderWidth: 1px
    --InputFontSize: 18px
    --InputFontWeight: 400
    --PlaceholderSize: 18px
    --PlaceholderColor: var(--Gray)

    //Titles
    --h1Weight: 500
    --h1Size: clamp(25px, 3vw, 32px)

    --h2Weight: 500
    --h2Size: clamp(20px, 1.7vw, 28px)

    --h3Weight: 500
    --h3Size: clamp(18px, 1.6vw, 22px)

    --h4Weight: 400
    --h4Size: clamp(17px, 1.5vw, 20px)

    --h5Weight: 400
    --h5Size: clamp(16px, 1.3vw, 19px)

    --h6Weight: 400
    --h6Size: clamp(16px, 1.2vw, 18px)
    //UnderTitle Bar
    --UnderTitleHeight: 2px
    --UnderTitleRadius: 1000px

    // Subtitles
    --subtitleFamily: var(--Nimbus)
    --subtitleSize: 18px
    --subtitleLineHeight: 26px

    //Errors
    --ErrorBg: var(--cpRedBg)
    --ErrorText: #333333
    --ErrorColor: var(--cpRed)
    --ErrorBorder: var(--cpRed)
    --ErrorRadius: 0 var(--Radius) var(--Radius) 0
    --ErrorBoxShadow: 0px 0px 6px var(--cpRed)
    // New UI Styling
    --uiGray30: #B4B4B8
    --vendorColor: #C1DBFC
    --influencerColor: #FFDDFB
    --userColor: #2661DD
    --mspColor: #FF7B45
    scroll-behavior: smooth
    width: 100vw

html
    box-sizing: border-box
    -webkit-font-smoothing: antialiased

*
    box-sizing: inherit
    &:before, &:after
        box-sizing: inherit

html, body
    font-family: var(--RocGrotesk)
    max-width: 100vw
    overflow-x: hidden
    background-color: var(--) !important

body a
    color: var(--blue-600)
    &:hover
        color: var(--blue-800)

body
    max-height: 100vh

a
    text-decoration: none !important

.linkStyle
    color: var(--AccentColor)!important
    text-decoration: underline!important
    cursor: pointer
    *
        color: var(--AccentColor)!important
        fill: var(--AccentColor)!important

h1,h2,h3,h4,h5,h6,.h1,.h2,.h3,.h4,.h5,.h6
    color: var(--cpNavy)
    margin: 0!important
    font-family: var(--RocGrotesk)

h1, .h1
    font-weight: var(--h1Weight)!important
    font-size: var(--h1Size)!important

h2, .h2
    font-weight: var(--h2Weight)!important
    font-size: var(--h2Size)!important

h3, .h3
    font-weight: var(--h3Weight)!important
    font-size: var(--h3Size)!important

h4, .h4
    font-weight: var(--h4Weight)!important
    font-size: var(--h4Size)!important

h5, .h5
    font-weight: var(--h5Weight)!important
    font-size: var(--h5Size)!important

h6, .h6
    font-weight: var(--h6Weight)!important
    font-size: var(--h6Size)!important

input, select, textarea
    border: none
    margin: 0
    border-radius: 0

input, select
    height: clamp(40px, 3.2vw, 50px)!important
    &:focus
        box-shadow: none!important
        outline: 0!important

input[type=number]::-webkit-outer-spin-button, input[type=number]::-webkit-inner-spin-button
    -webkit-appearance: none
    margin: 0

input[type=number]
    -moz-appearance: textfield

select.cpInput
    -webkit-appearance: none
    -moz-appearance: none
    appearance: none

select:disabled
    background: var(--cpGrayBg)!important

.selectContainer
    position: relative
    &::after
        content: "\25b6"
        position: absolute
        right: clamp(10px, 7%, 30px)
        bottom: -8px
        z-index: 3
        text-align: center
        width: 10px
        height: 100%
        color: var(--cpGray)
        display: flex
        align-items: center
        justify-content: center
        transform: rotate(90deg)
        pointer-events: none

.selectContainerNoLabel
    position: relative
    &::after
        content: "\25b6"
        position: absolute
        right: clamp(10px, 7%, 15px)
        bottom: 0
        z-index: 3
        text-align: center
        width: 10px
        height: 100%
        color: var(--cpGray)
        display: flex
        align-items: center
        justify-content: center
        transform: rotate(90deg)
        pointer-events: none

.inputLoader
    position: absolute
    bottom: 25px
    right: 20px
    z-index: 4

.switcher
    display: inline-flex!important
    align-items: center!important
    input
        height: 32px!important
        width: 56px!important
        margin-right: 12px
        margin-top: 0!important
        &:checked
            background-color: var(--cpGreen)
            border-color: var(--cpGreen)

button:not(.MuiButtonBase-root)
    font-weight: var(--BtnFontWeight)!important
    &:focus, &:focus-visible
        outline: 0!important
        box-shadow: none!important

p
    margin: 0!important
    font-size: 18px
    line-height: 24px

li
    font-size: 18px
    line-height: 24px

.main-container
    @media (min-width: 992px)
        &>div[data-collapsed="true"] + div.newRoute
            max-width: calc(100vw - 64px)
            transition: max-width 0.3s
        &>div[data-collapsed="false"] + div.newRoute
            max-width: calc(100vw - 328px)
            transition: max-width 0.3s

.routePadding
    overflow: hidden
    padding: var(--RouteSpacing)
    background-color: var(--cpWhite)
    gap: 24px
    @media (min-width: 992px)
        border-radius: 10px!important
    @media (min-width: 1500px)
        padding: var(--RouteSpacing)
        width: auto

.strictRoutePadding
    max-width: 100vw
    overflow: hidden
    padding-bottom: 120px
    border: 1px solid rgba(0,0,0,0.1)
    background-color: var(--cpWhite)
    padding: var(--RouteSpacing)
    background-color: var(--cpWhite)
    @media (min-width: 992px)
        width: calc(100vw - 24px)
        border-radius: 10px!important
    @media (min-width: 1500px)
        width: 1500px

.collapsibleRoute
    padding-top: 30px
    padding-bottom: 120px
    padding-left: var(--SectionPaddingY)
    padding-right: var(--SectionPaddingY)
    background-color: var(--cpWhite)
    @media (min-width: 992px)
        padding-top: 70px

.fullSection
    min-height: 100vh
    max-width: 100vw
    overflow-x: hidden
    @media (min-width: 992px)
        min-height: 80vh

.fullAdminSection
    min-height: 89vh
    max-width: 100vw
    overflow-x: hidden
    @media (min-width: 768px)
        min-height: 92vh

footer
    z-index: 10
    position: relative

.cPointer
    cursor: pointer

.blackColor
    color: var(--cpBlack)!important
    > *
        color: var(--cpBlack)!important

.blueColor
    color: var(--cpBlue)!important
    fill: var(--cpBlue)!important
    > *
        color: var(--cpBlue)!important
        fill: var(--cpBlue)!important

.accentColor
    color: var(--AccentColor)!important
    > *
        color: var(--AccentColor)!important

.successColor
    color: var(--cpGreen)!important
    svg
        fill: var(--cpGreen)!important

.errorColor
    color: var(--cpRed)!important
    svg
        fill: var(--cpRed)!important

.grayColor
    color: var(--cpGray)!important
    svg
        fill: var(--cpGray)!important

.white, .whiteColor
    color: var(--cpWhite)!important
    fill: var(--cpWhite)!important
    > *
        color: var(--cpWhite)!important
        fill: var(--cpWhite)!important

.fadedBorder
    border: none
    background: transparent linear-gradient(90deg, #0C255600 0%, #071634 50%, #0C255600 100%) 0% 0% no-repeat padding-box
    height: 1px
    width: 100%!important

svg
    width: 100%
    height: 100%

.bigLoader
    width: 100px
    height: 100px
    div, svg
        width: 100%!important
        height: 100%!important

.cursor-text
    cursor: default!important

.globalConfirmationModal
    max-height: 400px !important
    min-height: 300px !important

body > div > div[dir="ltr"]
    z-index: 9999999!important
    & > div[class*="mantine-"]
        z-index: 9999999!important

.rteContent
    a
        color: var(--cpOrange) !important
    img
        max-width: 100%
    p
        color: var(--cpGray) !important

.flex-1-0-auto
    flex: 1 0 auto

.flex-1-1-auto
    flex: 1 1 auto

.fs-12
    font-size: 12px!important
    p, span, li
        font-size: 12px!important

.fs-14
    font-size: 14px!important
    p, span, li
        font-size: 14px!important

.fs-16
    font-size: 16px!important
    p, span, li
        font-size: 16px!important

.fs-18
    font-size: 18px!important
    p, span, li
        font-size: 18px!important

.fs-20
    font-size: 20px!important
    p, span, li
        font-size: 20px!important

.fs-22
    font-size: 22px!important
    p, span, li
        font-size: 22px!important

.lh-1-2
    line-height: 1.2!important

.lh-1-3
    line-height: 1.3!important

.lh-1-5
    line-height: 1.5!important

.lh-16
    line-height: 16px!important

.lh-22
    line-height: 22px!important

.rotateHover
    transition: transform 0.4s ease-in-out
    &:hover
        transform: rotate(360deg)

.react-resizable-handle
    z-index: 2

//  --  --  --  --  --  --  --  --  //
    // NEW UI Styling
//  --  --  --  --  --  --  --  --  //

.uiRoute
    min-height: 92vh
    max-width: calc(100vw - 240px)
    overflow-x: hidden
    margin: 40px 120px
    border-radius: 10px !important
    background-color: #fff
    padding: 5px
    @media (max-width: 1200px)
        margin: 20px 40px
        max-width: calc(100vw - 80px)
    @media (max-width: 599px)
        margin: 10px 10px
        max-width: calc(100vw - 20px)

div[data-id="new-input"]
    &>div>div
        padding: 0
        border-radius: 28px
        background-color: #F4F6F9
        overflow: hidden
        max-height: 44px!important
        & input
            max-height: 16px !important

    & fieldset
        border: none
    & div.MuiAutocomplete-endAdornment
        display: none

    /* Global scrollbar styles for all elements except body */
*:not(body)
    &::-webkit-scrollbar
        width: 8px
        height: 8px
    &::-webkit-scrollbar-track
        border-radius: 8px
        background: #F4F6F9
    &::-webkit-scrollbar-thumb
        background: #B6C0CE
        border-radius: 8px
        width: 8px
        max-width: 8px
        background-clip: content-box !important
    &::-webkit-scrollbar-thumb:hover
        background: #c9d0d9

.newRoute
    flex: 1 0 auto
    max-width: 100vw
    overflow: hidden
    padding: var(--RouteSpacing)
    //background-color: var(--neutrals-200, #F4F6F9)
    background-color: var(--new-route-bg)
    display: flex
    flex-direction: column-reverse
    align-items: flex-start
    gap: 24px
    position: relative
    min-height: calc(100vh - 122px)
    & *
        font-family: var(--Inter) !important
        letter-spacing: 0
    & > *
        max-width: 100%
        overflow: hidden
    &:nth-child(2)
        flex-grow: 1
    & .newContainer
        padding: 16px
        display: flex
        gap: 24px
        flex-direction: column
        border: 1px solid var(--neutrals-300, #D4DAE2)
        border-radius: 8px
        background-color: var(--neutrals-100, #FFF)
        flex-grow: 1
    & .newAltContainer
        display: flex
        gap: 24px
        flex-direction: column
        flex-grow: 1
    &>div.grow
        flex: 1 0 auto
    @media screen and (min-width: 992px)
        flex-direction: row

div.direct-it-site  .newRoute .newContainer
    border: none 

.MuiAutocomplete-noOptions, .MuiAutocomplete-loading
    font-family: var(--Inter) !important
    font-size: 14px !important
    color: var(--secondary-800, #0c2556)!important

.dropdown-menu.show
    // display: flex!important
    .dropdown-item
        color: var(--secondary-800)!important
        &:hover, &:active
            background-color: var(--secondary-100)!important

//  --  --  --  --  --  --  --  --  //
    // Adding new RTE Icons
//  --  --  --  --  --  --  --  --  //

.new-rte-icons
    div.ql-editor
        padding: 24px !important
    div.mantine-RichTextEditor-toolbarInner
        gap: 32px
        @media (max-width: 600px)
            gap: 0px
    div.mantine-RichTextEditor-toolbarGroup
        gap: 16px
        button
            border: none !important
            width: 24px
            height: 24px
            background-size: 18px
            background-repeat: no-repeat
            background-position: center
            position: relative
            & > svg
                display: none !important
            &::before
                width: 18px
                height: 18px
                position: absolute
                top: 50%
                left: 50%
                transform: translate(-50%, -50%)
                content: ""
                background-size: 18px
                filter: invert(11%) sepia(26%) saturate(5287%) hue-rotate(206deg) brightness(95%) contrast(97%)
            &.ql-bold::before
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' %3E%3Cpath d='M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-italic::before
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatItalicOutlinedIcon'%3E%3Cpath d='M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-underline::before
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatUnderlinedOutlinedIcon' tabindex='-1' title='FormatUnderlinedOutlined'%3E%3Cpath d='M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-strike::before
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='StrikethroughSOutlinedIcon'%3E%3Cpath d='M7.24 8.75c-.26-.48-.39-1.03-.39-1.67 0-.61.13-1.16.4-1.67.26-.5.63-.93 1.11-1.29.48-.35 1.05-.63 1.7-.83.66-.19 1.39-.29 2.18-.29.81 0 1.54.11 2.21.34.66.22 1.23.54 1.69.94.47.4.83.88 1.08 1.43s.38 1.15.38 1.81h-3.01c0-.31-.05-.59-.15-.85-.09-.27-.24-.49-.44-.68-.2-.19-.45-.33-.75-.44-.3-.1-.66-.16-1.06-.16-.39 0-.74.04-1.03.13s-.53.21-.72.36c-.19.16-.34.34-.44.55-.1.21-.15.43-.15.66 0 .48.25.88.74 1.21.38.25.77.48 1.41.7H7.39c-.05-.08-.11-.17-.15-.25zM21 12v-2H3v2h9.62c.18.07.4.14.55.2.37.17.66.34.87.51s.35.36.43.57c.07.2.11.43.11.69 0 .23-.05.45-.14.66-.09.2-.23.38-.42.53-.19.15-.42.26-.71.35-.29.08-.63.13-1.01.13-.43 0-.83-.04-1.18-.13s-.66-.23-.91-.42c-.25-.19-.45-.44-.59-.75s-.25-.76-.25-1.21H6.4c0 .55.08 1.13.24 1.58s.37.85.65 1.21c.28.35.6.66.98.92.37.26.78.48 1.22.65.44.17.9.3 1.38.39.48.08.96.13 1.44.13.8 0 1.53-.09 2.18-.28s1.21-.45 1.67-.79c.46-.34.82-.77 1.07-1.27s.38-1.07.38-1.71c0-.6-.1-1.14-.31-1.61-.05-.11-.11-.23-.17-.33H21V12z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-list
                &[value="bullet"]::before
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatListBulletedOutlinedIcon'%3E%3Cpath d='M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.68-1.5 1.5s.68 1.5 1.5 1.5 1.5-.68 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z' %3E%3C/path%3E%3C/svg%3E")
                &[value="ordered"]::before
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatListNumberedOutlinedIcon'%3E%3Cpath d='M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-align
                &::before
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatAlignLeftOutlinedIcon' tabindex='-1' title='FormatAlignLeftOutlined'%3E%3Cpath d='M15 15H3v2h12v-2zm0-8H3v2h12V7zM3 13h18v-2H3v2zm0 8h18v-2H3v2zM3 3v2h18V3H3z' %3E%3C/path%3E%3C/svg%3E")
                &[value="center"]::before
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatAlignCenterOutlinedIcon' tabindex='-1' title='FormatAlignCenterOutlined'%3E%3Cpath d='M7 15v2h10v-2H7zm-4 6h18v-2H3v2zm0-8h18v-2H3v2zm4-6v2h10V7H7zM3 3v2h18V3H3z' %3E%3C/path%3E%3C/svg%3E")
                &[value="right"]::before
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' data-testid='FormatAlignRightOutlinedIcon' tabindex='-1' title='FormatAlignRightOutlined'%3E%3Cpath d='M3 21h18v-2H3v2zm6-4h12v-2H9v2zm-6-4h18v-2H3v2zm6-4h12V7H9v2zM3 3v2h18V3H3z' %3E%3C/path%3E%3C/svg%3E")
            &.ql-clean::before
                background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDI0IDI0IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIGZpbGw9Im5vbmUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggc3Ryb2tlPSJub25lIiBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIj48L3BhdGg+PHBhdGggZD0iTTE3IDE1bDQgNG0wIC00bC00IDQiPjwvcGF0aD48cGF0aCBkPSJNNyA2di0xaDExdjEiPjwvcGF0aD48bGluZSB4MT0iNyIgeTE9IjE5IiB4Mj0iMTEiIHkyPSIxOSI+PC9saW5lPjxsaW5lIHgxPSIxMyIgeTE9IjUiIHgyPSI5IiB5Mj0iMTkiPjwvbGluZT48L3N2Zz4=')

// Animations
@keyframes fadeInLeftUp
    0%
        opacity: 0
        transform: translateY(48px) translateX(16px)
    100%
        opacity: 1
        transform: translateY(0px) translateX(0px)

.cellShadow
    position: relative
    &::after
        content: ""
        position: absolute
        top: 0
        right: -4px
        bottom: 0
        width: 4px
        height: 100%
        box-shadow: -4px 0 11px rgba(0,0,0,.3)!important

div[data-show-when-collapsed='true']
    display: none
