@media print 
    .print
        visibility: visible !important
    .printHidden 
        display: none !important
        visibility: hidden !important
        *
            display: none !important
            visibility: hidden !important
    .header
        display: none !important
    .secondaryMenuLoggedIn
        display: none !important
    [data-name=footer]
        display: none !important
    .ReactQueryDevtoolsPanel
        display: none !important
    [aria-controls=ReactQueryDevtoolsPanel]
        display: none !important
    // Hides the hubspot chat in production
    .widget-app-container
        display: none !important
        *
            display: none !important
    