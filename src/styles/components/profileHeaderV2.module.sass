.headerContainer
    position: relative

.coverContainer
    width: 100%
    height: 175px
    position: relative
    img
        width: 100%
        height: 100%
        object-fit: cover

.shareButtonsContainer
    width: auto
    height: 75px
    display: flex
    justify-content: flex-end
    position: absolute
    right: 0
    top: 155px
    > div
        border: none
        background-color: rgba(255,255,255,.7)
        border-radius: var(--Radius) 0 0 var(--Radius)
        > button
            margin: 0!important
            margin-right: 8px!important
            margin-left: 8px!important
        > button:first-child
            margin-left: 0!important
        > button:last-child
            margin-right: 0!important

.userContainer
    display: flex
    align-items: center
    justify-content: center
    flex-wrap: wrap
    margin-top: -20px
    position: relative
    z-index: 5
    input
        padding: 4px!important
        min-height: 1px!important
        height: auto!important
    @media (min-width: 1400px)
        justify-content: flex-start
        padding-top: 15px

.imageLoader
    width: 200px
    height: 200px
    border-radius: 50%
    background-color: var(--cpGray)
    display: flex
    justify-content: center
    align-items: center
    @media (min-width: 1400px)
        position: absolute
        top: -50px

.profileImage
    margin-top: 0px
    margin-bottom: 20px
    flex: 0 0 auto
    min-width: 100px
    min-height: 100px
    width: 100px
    height: 100px
    text-align: center
    position: relative
    z-index: 5
    > span
        top: 0
        position: absolute
        z-index: 5
        display: grid
        place-items: center
        width: 100%
        height: 100%
        border-radius: 50%
        background: rgba(0,0,0,.5)
        cursor: pointer
        opacity: 0
        transition: opacity .3s ease-in-out
        svg
            fill: var(--cpWhite)
        &:hover
            opacity: 1
        input
            display: none
        > span
            display: grid
            place-items: center
            width: 100%
            height: 100%
            border-radius: 50%
            background: rgba(0,0,0,.5)
            pointer-events: none
            margin: 0!important

    > div
        position: absolute
        top: 0
        width: 100%
        height: 100%
        img
            width: 100%
            height: 100%
            object-fit: cover
            border-radius: 50%
    > div:last-child
        > div
            top: calc(100% + 0px)!important
            left: calc(50% - 47.5px)!important
            width: 95px!important
    @media (min-width: 992px)
        min-width: 200px
        min-height: 200px
        width: 200px
        height: 200px
        margin-top: -50px
        margin-bottom: 30px
    @media (min-width: 1400px)
        margin: 0 24px 0 48px
        width: auto
        text-align: start
        > div
            position: absolute
            top: -50px
        > span
            top: -50px

.userText
    flex: 0 0 auto
    width: 100%
    display: flex
    align-items: center
    justify-content: center
    text-align: center
    margin-bottom: 8px
    @media (min-width: 992px)
        margin-bottom: 24px
    @media (min-width: 1400px)
        justify-content: flex-start
        width: auto
        text-align: start
    .companyHandle
        color: var(--cpBlue)!important
        font-size: 15px!important
        font-weight: bold!important
        @media (min-width: 992px)
            font-size: 18px!important

.checkIcon
    margin: 0!important
    margin-left: 4px!important

.buttonFollow
    flex: 0 0 auto
    text-align: center
    margin: 10px 4px
    @media (min-width: 1400px)
        width: auto
        text-align: start
        margin-top: 0px
        margin-bottom: 60px

.helperImageText
    color: var(--cpWhite)!important
    font-weight: 600!important
    font-size: 14px!important
    text-align: center

button.removeBtn
    width: 16px
    height: 16px
    border-radius: 50%!important
    position: absolute
    top: -8px
    right: -8px
    cursor: pointer
    display: flex!important
    align-items: center
    justify-content: center
    padding: 0!important
    margin: 0!important
    background-color: var(--cpBlue)
    opacity: 1!important
    > div
        margin: 0!important
        padding: 2px
        svg
            fill: var(--cpBlueBackground)
            width: 100%
            height: 100%
            vertical-align: text-top
    &:disabled
        cursor: not-allowed!important

.additionalBtns
    flex: 0 0 100%
    display: flex
    gap: 0.5rem
    justify-content: center
    margin-left: 0
    margin-right: 0
    height: 50%
    *:not(button)
        font-size: 14px!important
        padding: 0!important
        line-height: 1!important
    span
        display: inline-flex
        align-items: center
        justify-content: center
    span:last-child
        margin-top: 8px
    @media (min-width: 992px)
        span
            justify-content: flex-start
        *
            font-size: 17px!important
    @media (min-width: 1400px)
        flex: 0 0 auto
        margin: 45px 24px auto auto
        justify-content: flex-end

.cp
    cursor: pointer

.imgGradient::after
    content: ''
    position: absolute
    left: 0
    top: 0
    width: 100%
    height: 175px
    background: linear-gradient(90.07deg, #000000 0.42%, rgba(0, 0, 0, 0) 91.78%)
