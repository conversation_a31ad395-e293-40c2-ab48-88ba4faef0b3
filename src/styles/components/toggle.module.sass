.switch
    position: relative
    display: flex
    width: 35px
    height: 20px

.bigSwitch
    width: calc(35px * 1.5)
    height: calc(20px * 1.5)

.switch input
    opacity: 0!important
    width: 0!important
    height: 0!important

/* The toggle */
.toggle
    position: absolute
    cursor: pointer
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: var(--cpRed)
    -webkit-transition: .4s
    transition: .4s
    border-radius: 34px
    &:before
        border-radius: 50%
        position: absolute
        content: ""
        height: 12px
        width: 12px
        left: 4px
        bottom: 4px
        background-color: white
        -webkit-transition: .4s
        transition: .4s

.bigToggle
    &:before
        height: calc(12px * 1.75)
        width: calc(12px * 1.75)
        left: 5px
        bottom: 5px

.checkbox:checked + .toggle
    background-color: var(--cpGreen)

.checkbox:focus + .toggle
    box-shadow: 0 0 1px var(--cpGreen)

.checkbox:checked + .toggle:before
    -webkit-transform: translateX(16px)
    -ms-transform: translateX(16px)
    transform: translateX(16px)

.checkbox:checked + .bigToggle:before
    -webkit-transform: translateX(24px)
    -ms-transform: translateX(24px)
    transform: translateX(24px)

.toggleDisabled
    position: absolute
    cursor: pointer
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: var(--cpGray)
    -webkit-transition: .4s
    transition: .4s
    border-radius: 34px
    cursor: not-allowed
    &:before
        border-radius: 50%
        position: absolute
        content: ""
        height: 12px
        width: 12px
        left: 4px
        bottom: 4px
        background-color: var(--cpGrayBg5)
        -webkit-transition: .4s
        transition: .4s

.bigToggleDisabled
    &:before
        height: calc(12px * 1.75)
        width: calc(12px * 1.75)
        left: 5px
        bottom: 5px

.checkbox:checked + .toggleDisabled
    background-color: var(--cpGray)

.checkbox:focus + .toggleDisabled
    box-shadow: 0 0 1px var(--cpGray)

.checkbox:checked + .toggleDisabled:before
    -webkit-transform: translateX(16px)
    -ms-transform: translateX(16px)
    transform: translateX(16px)

.checkbox:checked + .bigToggleDisabled:before
    -webkit-transform: translateX(24px)
    -ms-transform: translateX(24px)
    transform: translateX(24px)

.beforeText
    padding-right: 4px

.afterText
    padding-left: 4px

.bigText
    font-size: 20px
    margin: 0 12px
