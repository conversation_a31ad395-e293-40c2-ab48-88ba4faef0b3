.headerContainer
    position: relative

.coverContainer
    height: 240px
    position: relative
    img
        width: 100%
        height: 100%
        object-fit: cover
    > span
        top: 0
        position: absolute
        display: grid
        place-items: center
        width: 100%
        height: 240px
        background: rgba(0,0,0,.5)
        cursor: pointer
        opacity: 0
        transition: opacity .3s ease-in-out
        svg
            fill: var(--cpWhite)
        &:hover
            opacity: 1
        input
            display: none
        > span
            display: grid
            place-items: center
            width: 100%
            height: 240px
            background: rgba(0,0,0,.5)
            pointer-events: none
            margin: 0!important

.shareButtonsContainer
    width: auto
    height: 75px
    display: flex
    justify-content: flex-end
    position: absolute
    top: 10px
    right: 0
    > div
        border: none
        background-color: rgba(255,255,255,.7)
        border-radius: var(--Radius) 0 0 var(--Radius)
        > button
            margin: 0!important
            margin-right: 8px!important
            margin-left: 8px!important
        > button:first-child
            margin-left: 0!important
        > button:last-child
            margin-right: 0!important
    @media (min-width: 768px)
        top: 155px

.userContainer
    display: flex
    align-items: center
    justify-content: center
    flex-wrap: wrap
    margin-top: -50px
    position: relative
    z-index: 5
    input
        padding: 4px!important
        min-height: 1px!important
        height: auto!important
    @media (min-width: 768px)
        justify-content: flex-start
        padding-top: 15px

.imageLoader
    width: 200px
    height: 200px
    border-radius: 50%
    background-color: var(--cpGray)
    display: flex
    justify-content: center
    align-items: center
    @media (min-width: 768px)
        position: absolute
        top: -50px

.profileImage
    margin-top: -50px
    margin-bottom: 24px
    flex: 0 0 auto
    min-width: 200px
    width: 200px
    min-height: 200px
    height: 200px
    text-align: center
    position: relative
    z-index: 5
    > span
        top: 0
        position: absolute
        display: grid
        place-items: center
        width: 200px
        height: 200px
        border-radius: 50%
        background: rgba(0,0,0,.5)
        cursor: pointer
        opacity: 0
        transition: opacity .3s ease-in-out
        svg
            fill: var(--cpWhite)
        &:hover
            opacity: 1
        input
            display: none
        > span
            display: grid
            place-items: center
            width: 200px
            height: 200px
            border-radius: 50%
            background: rgba(0,0,0,.5)
            pointer-events: none
            margin: 0!important

    > div
        position: absolute
        top: 0
        width: 100%
        height: 100%
        img
            width: 100%
            height: 100%
            object-fit: cover
            border-radius: 50%
    @media (min-width: 768px)
        margin: 0 24px 0 48px
        width: auto
        text-align: start
        > div
            position: absolute
            top: -50px
        > span
            top: -50px

.userText
    flex: 0 0 auto
    width: 100%
    display: flex
    align-items: center
    justify-content: center
    text-align: center
    margin-bottom: 24px
    @media (min-width: 768px)
        justify-content: flex-start
        width: auto
        text-align: start
    .companyHandle
        color: var(--cpBlue)!important
        font-size: 18px!important
        font-weight: bold!important

.checkIcon
    margin: 0!important
    margin-left: 4px!important

.buttonFollow
    flex: 0 0 auto
    width: 100%
    text-align: center
    @media (min-width: 768px)
        width: auto
        text-align: start
        margin-bottom: 60px
