.container
    max-width: 90%
    display: block
    margin: 0 auto

.imagesPreviewContainer
    display: flex
    flex-direction: row
    flex-wrap: wrap
    justify-content: flex-start
    align-items: center
    width: 100%
    .imagePreviewWrapper
        width: 25%
        height: 0px
        padding-bottom: calc(25% - 20px)
        margin-bottom: 20px
        background-size: cover
        background-position: center
        position: relative
        margin-left: calc(25%/6)
        margin-right: calc(25%/6)
        .removeImageBtn
            position: absolute
            top: 10px
            right: 10px
            background: rgba(255, 255, 255, 0.6)!important
            border-radius: 10px
            border: none!important
            margin: 0!important
            padding: 5px!important
            z-index: 5
            width: 30px
            height: 30px
            svg
                max-width: 17px
                fill: var(--cpRed) !important

.imageHeaderPreview
    max-width: 900px
    > div
        display: flex
        flex-flow: column
        margin: 0!important
        width: 100%!important
        max-width: 100vw!important
        aspect-ratio: 0!important
        > div:first-child
            order: 2
        > div:last-child
            width: 100%!important
            margin: 0!important
            max-width: 100vw!important
            aspect-ratio: 128/43!important
            border-radius: var(--Radius)
            overflow: hidden
            order: 1
            > span
                display: none

.excerpt
    textarea
        max-height: none
    > div
        line-height: 16px!important
        span
            font-size: 16px!important
            font-weight: 400!important

.prmDescriptionSection
    max-height: -webkit-max-content !important
    min-height: 40px !important

.supportText
    font-size: 14px!important
    color: var(--cpGray)!important
    font-weight: 400!important
    margin: 0!important
    line-height: 1!important
