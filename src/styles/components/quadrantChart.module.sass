.customPoint
    z-index: 1 !important
    &:hover
        z-index: 2 !important

.multiPoint
    fill: #fff
    background-fill: #fff
    outline: 2px solid var(--cpNavy)
    z-index: 5 !important
    border-radius: 50%
    .numberText
        display: block
        text-align: center
        color: var(--cpNavy)
        font-weight: 800
        font-size: 24px !important
        border-radius: 50%
        line-height: 50px
    .matchesContainerWrapper
        background-color: #fff
    .matchesContainer
        display: none !important
    img
        visibility: hidden
        border-radius: 50%
    .multiPointContent
        img
            visibility: visible

.multiPointActive
    transition: all 0.5s ease-in-out
    // background: #fff
    z-index: 2000 !important
    // border: 1px solid transparent
    overflow: visible !important
    img
        visibility: visible
        border-radius: 50%
        border: 1px solid var(--cpNavy)
        margin-top: 1px
    .multiPointContent
        display: none
        color: transparent
        img, div, span, h1, h2, h3, h4, h5, h6, svg, foreignobject
            display: none
    .numberText
        display: none !important
        color: transparent
    .matchesContainer
        display: block !important

.errorText
    position: absolute
    // background-color: rgb(6, 37, 86, 0.3)
    // backdrop-filter: blur(5px)
    pointer-events: none
    z-index: 3
    span
        background-color: #ffffff
        padding: 10px
        text-align: center
        color: #000
        font-weight: 700
        border-radius: 5px
        

.multiPointsWrapper
    z-index: 2 !important
    &:hover
        z-index: 3 !important
// .pointFade
//     -webkit-animation: fade-in 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both
//     animation: fade-in 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both
.matchesContainer
    position: relative
    overflow: visible !important

.quadrantLabel
    position: absolute
    border-radius: 10px
    padding: 0px 5px
    opacity: 80%
    white-space: nowrap
.quadrantLabel_topRight
    top: 25px
    right: 20px
.quadrantLabel_topLeft
    top: 25px
    left: 45px
.quadrantLabel_bottomRight
    bottom: 30px
    right: 20px
.quadrantLabel_bottomLeft
    bottom: 30px
    left: 50px
@media (max-width: 600px)
    .quadrantLabel_topRight
        transform: rotate(90deg)
        top: 100px
        right: -50px
    .quadrantLabel_topLeft
        transform: rotate(-90deg)
        top: 100px
        left: -30px
    .quadrantLabel_bottomRight
        transform: rotate(90deg)
        bottom: 80px
        right: -40px
    .quadrantLabel_bottomLeft
        transform: rotate(-90deg)
        bottom: 50px
        left: 10px
.xAxisCustomLabels
    position: absolute
    bottom: 0
    left: 40px
    display: flex
    align-items: center
    justify-content: space-between
    width: calc(100% - 60px)
    max-width: calc(100% - 60px)

.yAxisCustomLabels
    position: absolute
    top: 20px
    left: 10px
    display: flex
    flex-direction: column
    justify-content: space-between
    align-items: center
    height: calc(100% - 40px)
    max-height: calc(100% - 40px)
    span
        writing-mode: vertical-lr
        transform: rotate(180deg)
        white-space: nowrap

.xAxisCenterLabel
    color: #4D78CF !important
.yAxisCenterLabel
    color: #4D78CF !important

.horizontalLine
    display: flex
    align-items: center
    overflow: hidden
    margin: 0px 5px
    letter-spacing: 5px
.verticalLine
    display: flex
    align-items: center
    overflow: hidden
    writing-mode: vertical-lr
    margin: 5px 0px
    letter-spacing: 5px


@-webkit-keyframes fade-in 
    0% 
        opacity: 0
        transform: scale(0)
    100% 
        opacity: 1px
        transform: scale(1)

    @keyframes fade-in 
    0%
        opacity: 0
        transform: scale(0)
    100% 
        opacity: 1
        transform: scale(1)

@keyframes glow 
    0% 
        box-shadow: 0 0 var(--cpOrange)
    100% 
        box-shadow: 0 0 5px 4px transparent
