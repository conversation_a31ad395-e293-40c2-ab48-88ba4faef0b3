.stackChartDesc
    margin-bottom: 10px
    @media (min-width: 900px)
        margin-bottom: 20px
.multiPoint
    height: 50px !important
    width: 50px !important
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    .numberText
        font-size: 14px !important
        margin: 3px 0px !important
.previewSection
    width: 100% !important
    height: 20px !important
    display: flex
    justify-content: center
    overflow: hidden !important
.customPointPreview
    height: 20px !important
    width: 20px !important
    img
        height: 20px!important
        width: 20px!important
.filterHeader
    width: 100%
    background-color: var(--cpNavy)
    padding: 5px 10px
    cursor: pointer
    border-radius: 10px
    justify-content: center
    h2
        color: #fff !important
    &:hover
        background-color: var(--cpOrange)
        transition: all 0.2s ease-in-out
.filterTitle
    white-space: nowrap
    color: #000 !important
    font-weight: 700 !important
    font-size: 16px !important

.selectAllBtn
    color: var(--cpBlue)
    text-decoration: underline
    cursor: pointer
    font-weight: normal
    white-space: nowrap

.checkboxLabel
    white-space: nowrap
    text-overflow: ellipsis
    overflow: hidden

.filterSearchBar
    margin-bottom: 10px
    input
        height: 24px !important

.filterSearchIcon
    cursor: pointer
    margin-left: 0.75rem
    svg 
        height: 20px
        width: 20px
        color: var(--cpNavy)
    &:hover
        svg
            color: var(--cpOrange)
@media (max-width: 768px)
    .multiPoint
        width: 30px !important
        height: 30px !important
    .customPointPreview
        width: 10px !important
        height: 10px !important
        img
            height: 10px!important
            width: 10px!important
    .companyAvatar
        width: 30px !important
        height: 30px !important
    .previewSection
        width: 100% !important
        height: 10px !important
        display: flex
        justify-content: center
        img
            height: 10px!important
            width: 10px!important