.parentContainer
    flex: 0 1 100%
    @media (min-width: 1200px)
        flex: 1 1 auto
        max-width: calc(50% - 3rem)
        min-width: 40%

.container
    display: flex
    flex-wrap: wrap
    gap: 12px
    > div
        flex: 1 0 auto
        width: 100%
        @media (min-width: 1200px)
            width: calc(50% - 12px)

.categoryItem
    padding: 8px 12px
    border: 1px solid #000
    border-radius: 100px

.videoCard
    flex: 1 0 auto!important
    width: 100%!important
    @media (min-width: 1200px)
        width: calc(50% - 12px)!important
        max-width: 50%!important
