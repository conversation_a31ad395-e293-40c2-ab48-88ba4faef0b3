.tabDescription
    padding: 20px

.videosContainer
    display: flex
    align-items: stretch
    margin-top: 32px
    flex-wrap: wrap
    gap: 20px

.videoContainer
    flex: 0 0 auto
    width: 100%
    cursor: pointer
    &:last-child
        margin-right: 0
    img
        width: 100%
        height: 200px
        object-fit: cover
    @media (min-width: 768px)
        width: calc(50% - 24px)
        margin-right: 24px
    @media (min-width: 992px)
        width: calc(33% - 24px)
    @media (min-width: 1200px)
        width: calc(25% - 24px)

.videoInfo
    margin-top: 16px
    margin-bottom: 32px
    h4
        font-size: 18px!important
        font-weight: bold!important
        color: var(--cpBlue)!important
        font-family: var(--Nimbus)!important
        margin-bottom: 8px!important
    span
        font-size: 16px!important
        margin: 0!important

.productsContainer
    display: flex
    align-items: stretch
    flex-wrap: wrap
    margin-top: 8px

.productLink
    display: flex
    align-items: center
    flex: 0 0 auto
    width: auto
    position: relative
    margin-right: 8px
    a
        cursor: pointer
        text-decoration: underline!important
        color: var(--cpGray)!important
        font-size: 16px!important
        margin: 0!important

.removeBtn
    background: var(--cpRed)
    width: 18px
    height: 18px
    font-size: 12px
    text-align: center
    line-height: 18px
    display: block
    border-radius: 50%
    font-weight: 500
    cursor: pointer
    margin-left: 4px
    color: var(--cpWhite)

.deleteVideoConfirmationModal
    height: 300px !important
    min-height: 300px !important

.featuredVideosVideoCard
    width: 100%
    @media (min-width: 470px)
        width: calc(50% - 24px)
    @media (min-width: 768px)
        width: calc(33% - 24px)
    h4
        margin-bottom: 0!important
        font-size: 17px !important
