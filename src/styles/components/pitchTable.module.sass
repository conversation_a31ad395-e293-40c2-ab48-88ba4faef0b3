.addBtn
    display: inline-flex!important
    background: var(--cpBlue)!important
    border-radius: 20px!important
    padding: 13px 32px!important
    color: var(--cpBg)
    > div
        margin-left: 0!important
    svg
        fill: var(--cpBg)

.tableHeader
    display: grid
    grid-template-columns: 2fr 3fr 7fr
    border: 1px solid var(--cpBlueBorder)
    border-radius: 10px 10px 0 0
    padding: 24px 32px!important
    > .tableHeaderItem:first-child
        margin: 0 16px 0 0!important
    > .tableHeaderItem
        margin: 0 16px!important

.tableRow
    display: grid
    grid-template-columns: 2fr 3fr 7fr
    border: 1px solid var(--cpBlueBorder)
    border-top: none!important
    padding: 24px 32px!important
    &:nth-child(2n + 1)
        background: var(--cpGrayBg5)!important
        &:hover
            background: var(--cpGrayShade3)!important
    &:nth-child(2n)
        background: var(--cpBg)!important
        &:hover
            background: var(--cpGrayShade3)!important
    > .tableItem:first-child
        margin: 0 16px 0 0!important
    > .tableItem
        margin: 0 16px!important
        &:nth-child(3n+1)
            text-align: center
    &:last-child
        border-radius: 0 0 10px 10px

.tableItem:last-child
    display: flex
    align-items: center
    justify-content: flex-end
    gap: 8px

.removeBtn
    background: var(--cpBg)!important
    border-radius: 20px!important
    padding: 8px 32px!important
    color: var(--cpBlue)!important
    border: 1px solid var(--cpBlue)!important

.editBtn
    background: var(--cpBlue)!important
    border-radius: 20px!important
    padding: 8px 32px!important
    color: var(--cpBg)!important
    border: 1px solid var(--cpBlue)!important
    display: inline-flex!important
    align-items: center
    justify-content: center
    > div
        margin: 0 8px 0 0 !important
    svg
        fill: none!important
        stroke: var(--cpBg)!important

.loadingRow
    display: flex
    align-items: center
    justify-content: center
    border: 1px solid var(--cpBlueBorder)
    border-top: none!important
    padding: 24px 32px!important
    > span, > span > a, > span > a > svg
        height: 50px
        width: 50px
