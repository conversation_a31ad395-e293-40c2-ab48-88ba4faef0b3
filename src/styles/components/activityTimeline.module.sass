.activityTimelineContainer
    margin-top: 24px
    padding-left: 12px

.activityCardAvatar
    width: 70px
    height: 70px
    @media (min-width: 576px)
        margin-right: 20px
    img
        height: 100%
        width: 100%
        object-fit: cover

.activityCardSubHeader
    display: flex
    align-items: center
    justify-content: center
    flex-direction: column
    height: 100%
    @media (min-width: 576px)
        height: auto
        display: initial
    h4
        font-weight: 600 !important
        font-size: 1.3rem !important
    .timeAwayText
        font-size: 16px !important
        color: #8c8c8c

.activityCardBody
    margin-top: 8px
    > div > div > div
        margin-top: 0
    @media (min-width: 576px)
        margin-top: 12px

.shoutOutContent
    font-size: 17px
    font-weight: 500
    color: var(--cpNavy) !important
    word-break: break-word

.blogContent
    max-width: 400px

.galleryContent
    max-width: 400px

.galleryImageWrapper
    flex: 1 1 auto
    width: 49%
    max-width: 400px

.galleryImage
    width: 100%
    height: auto

.imageCount
    color: var(--cpGray) !important
    font-size: 1rem !important

.videoContent
    width: 100%
    > div
        max-width: 350px
        width: 100%!important
        margin-top: 0!important

.documentContent
    display: flex
    flex-direction: row
    flex-wrap: no-wrap
    justify-content: flex-start
    align-items: flex-start

.productContent
    display: flex
    flex-direction: column
    flex-wrap: no-wrap
    justify-content: flex-start
    align-items: flex-start
    

.productImage
    width: 100%
    height: auto
    border-radius: var(--Radius)
    overflow: hidden
    @media (min-width: 576px)
        width: 50%
        max-width: 50%
        height: auto
        margin-right: 12px
    img
        object-fit: cover
        object-position: center
        width: 100%
        max-height: 150px
        height: auto

.roundedProductImage
    border-radius: 50% !important
    width: 150px !important
    height: 150px !important
    img
        width: 100%
        height: auto

.galleryDescription
    margin-top: 8px

.activityCardFooter
    display: flex
    flex-direction: row
    align-items: center
    justify-content: space-between
    max-width: 350px

.deletePostBtn
    color: var(--cpBlue)
    cursor: pointer
    text-decoration: underline
    position: absolute
    top: 0
    left: 350px
    background-color: transparent !important
    &:hover
        svg
            fill: var(--cpRed)
            transition: fill 0.2s ease-in-out

.deletePostBtnOffset
    color: var(--cpBlue)
    cursor: pointer
    text-decoration: underline
    position: absolute
    top: 0
    left: 400px
    background-color: transparent !important
    &:hover
        svg
            fill: var(--cpRed)
            transition: fill 0.2s ease-in-out


.followBtn
    background: none
    color: var(--cpNavy)
    display: flex
    flex-direction: row
    align-items: center
    border: none
    &:hover
        background: none
        color: var(--cpOrange)
        transition: all 0.2s ease-in-out
        border: none

.marginBottom10
    margin-bottom: 10px !important

.commentMedia
    display: flex
    align-items: flex-start
    img
        width: 55px
        height: 55px

.commentMediaBody
    flex: 1

.commentForm
        display: flex
        flex-direction: row
        justify-content: space-between
        padding: 0 10px
        margin-top: 10px
        @media (min-width: 1200px)
            padding-right: 60px
        div:first-child
            width: calc(100% - 70px)
            max-width: 100% !important
            margin: 0
            position: unset

.chatInput
    width: 100%
    border-radius: 0 0 10px 0px
    margin-top: auto
    > form
        display: flex
        justify-content: space-between
        align-items: center
        > div
            flex: 0 1 auto
            min-width: 80%
            margin: 0
        > button
            display: flex
            border-radius: 20px!important
            background: var(--cpBlue)!important
            margin-left: 16px
            padding: 12px 32px!important
            color: var(--cpBg)!important
            &:hover
                color: var(--cpBg)!important

.textCenter
    padding: 30px 30px 30px 30px !important
    text-align: center

.cardActions
    position : absolute
    right : 0px