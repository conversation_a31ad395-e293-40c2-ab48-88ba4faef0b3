.upcomingPitch
    background: #F8F9FA
    border: 1px solid #E8EAEE
    border-radius: 10px
    height: 100%
    display: flex
    flex-direction: column
    > div > img
        width: 100%

.pitchDateInfo
    background: white
    width: 90%
    margin: 0 auto
    letter-spacing: 0px
    color: #004DCE
    font-size: 15px
    display: flex
    justify-content: space-evenly
    padding: 10px 0
    > div > img
        margin-right: 5px
        margin-bottom: 2px

.pitchDescription
    width: 90%
    margin: auto
    letter-spacing: 0px
    color: #5F6B83
    font-family: "nimbus-sans"
    margin-top: 15px
    > span > a
        text-decoration: underline !important

.upcomingPitchBanner
    position: relative
    margin-bottom: 20px
    background-size: cover
    background-position: bottom
    background-repeat: no-repeat
    height: 30vw
    max-height: 250px
    border-top-left-radius: 10px
    border-top-right-radius: 10px
    @media (min-width: 765px)
        height: 20vw
        max-height: 250px

.statusBanner
    position: absolute
    right: -20px
    bottom: 10px
    color: white
    > img
        width: 250px
    .bannerLabel
        position: absolute
        top: 20px
        left: 0px
        font-size: 24px
        font-weight: 500
        width: 100%
        text-align: center

.upcomingLoader
    width: 100%
    height: 100%
    display: flex
    align-items: center
    justify-content: center

.buttonWrapper
    text-align: center
    padding: 20px 0px
    display: flex
    justify-content: center
    gap: 12px

.addToCalendarBtnWrapper
    display: flex
    justify-content: center
    width: 100%
    button
        max-width: 150px
