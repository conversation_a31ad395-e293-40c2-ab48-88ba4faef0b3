.container
    width: 100%
    padding: 0 2.5rem !important
    margin-bottom: 12px
    hr
        margin: 4px

.stripHeaderActive
    display: flex
    align-items: center
    justify-content: space-between
    span
        font-size: clamp(1.2rem, 1.2vw, 1.4rem)!important
        font-weight: 600
        color: var(--cpNavy)
        display: block
        cursor: pointer
    .controlArrow
        width: 24px
        height: 24px
        min-width: 24px
        margin: auto 0
        position: relative
        cursor: pointer
        &::after
            content: ""
            position: absolute
            top: 9px
            left: 7px
            transition: transform .2s ease-in-out
            border: solid var(--cpGray)
            border-width: 0 5px 5px 0
            display: inline-block
            padding: 5px
            transform: rotate(-135deg)
            -webkit-transform: rotate(-135deg)
            border-color: var(--cpNavy)

.stripHeader
    display: flex
    align-items: center
    justify-content: space-between
    span
        font-size: clamp(1.2rem, 1.2vw, 1.4rem)!important
        font-weight: 600
        color: var(--cpNavy)
        display: block
        cursor: pointer
    .controlArrow
        cursor: pointer
        width: 24px
        height: 24px
        min-width: 24px
        margin: auto 0
        position: relative
        &::after
            content: ""
            position: absolute
            top: 5px
            left: 7px
            transition: transform .2s ease-in-out
            border: solid var(--cpNavy)
            border-width: 0 5px 5px 0
            display: inline-block
            padding: 5px
            transform: rotate(45deg)
            -webkit-transform: rotate(45deg)

.contentStripActive
    margin-top: 12px
    position: relative
    display: flex
    align-items: stretch
    gap: 24px
    margin-bottom: 12px
    transition: transform .2s ease-in-out, opacity .2s ease-in-out, height .2s ease-in-out
    transform: translateY(0)
    opacity: 1
    height: 100%
    width: 100%

.videoStrip
    margin-top: 12px
    position: relative
    display: flex
    align-items: stretch
    gap: 10px
    margin-bottom: 12px
    transition: transform .2s ease-in-out, opacity .2s ease-in-out, height .2s ease-in-out
    transform: translateY(0)
    opacity: 1
    height: 100%
    width: 100%

.contentStrip
    position: relative
    display: flex
    align-items: stretch
    gap: 24px
    margin-bottom: 8px
    transition: transform .2s ease-in-out, opacity .2s ease-in-out, height .2s ease-in-out
    transform: translateY(-10%)
    opacity: 0
    height: 0
    pointer-events: none

.badge
    position: absolute
    top: -10px
    left: -15px
    width: 70px
    height: 30px
    z-index: 2
    font-weight: 500
    color: var(--cpWhite)
    display: flex
    align-items: center
    justify-content: center
    background: var(--cpOrange)
    border-radius: 4px

.badgeProfile
    position: absolute
    top: -10px
    left: -15px
    width: 70px
    height: 30px
    z-index: 2
    font-weight: 500
    color: var(--cpWhite)
    display: flex
    align-items: center
    justify-content: center
    background: var(--InfluencerColor) !important
    border-radius: 4px

.videoCategoryVideoCard
    width: 100% !important
    max-height: 225px !important
    min-width: 230px !important
    img
        aspect-ratio: 16/9
    @media (min-width: 992px)
        max-width: calc(100% / 6 - 10px) !important
        height: auto !important

.profileExplorerCard
    @media (min-width: 1000px)
        width: calc(32% - 10px)!important
    @media (min-width: 1400px)
        width: calc(24% - 10px)!important
