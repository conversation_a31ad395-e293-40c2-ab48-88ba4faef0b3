.dropBox
    border: 1px dashed #ccc
    background: var(--cpGrayBg4)
    width: 100%
    height: auto
    display: flex
    flex-flow: column
    justify-content: center
    align-items: center
    cursor: pointer
    padding: 24px 3%
    margin: 0 auto
    box-shadow: 0
    transition: border 0.3s ease-in-out, box-shadow 0.3s ease-in-out

    .cloudIcon
        width: 100px
        height: 100px
        margin-bottom: 18px
        border-radius: 50%
        background: var(--cpBlueBackground)
        display: flex
        justify-content: center
        align-items: center
    svg
        width: 100%
        height: 100%
        fill: var(--cpBlue)

.dropBoxText
    font-size: 16px
    font-weight: 500
    color: var(--cpGray)
    text-align: center
    margin-bottom: 18px
    white-space: nowrap
    span
        display: flex
        flex-wrap: wrap
        justify-content: center
        line-height: 24px
        > button
            font-size: 16px
            padding: 0!important
            background: none!important
            color: var(--cpBlue)!important
            text-decoration: underline!important
            margin: 0!important
            margin-left: 4px!important
        @media (min-width: 768px)
            justify-content: flex-start
            flex-wrap: nowrap

.inputFile
    display: none

.dragging
    border: 1px dashed var(--cpBlue)
    cursor: default
    box-shadow: 0 0 10px #0625563f

.supportText
    font-size: 14px!important
    color: var(--cpGray)!important
    font-weight: 400!important
    margin: 0!important
    line-height: 1!important
    margin-top: 8px !important
    margin-bottom: 16px !important

.imagePreviewContainer
    width: 100%
    max-width: 500px
    margin: 0 auto
    margin-top: 12px
    aspect-ratio: 16/9
    text-align: start
    img
        width: 100%
        height: 100%
        object-fit: cover
    span
        font-size: 18px
        font-weight: 400!important
