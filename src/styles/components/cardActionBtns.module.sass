.removeBtn
    width: 24px
    height: 24px
    border-radius: 50%!important
    position: absolute
    top: -10px
    right: 5px
    cursor: pointer
    display: flex!important
    align-items: center
    justify-content: center
    padding: 0!important
    margin: 0!important
    background: var(--cpBlue)!important
    opacity: 1!important
    z-index: 4
    transition: all .2s ease-in-out
    > div
        margin: 0!important
    svg
        fill: var(--cpWhite)!important
    a
        width: 12px
        height: 12px
        > svg
            width: 100%
            height: 100%
            vertical-align: text-top
    &:disabled
        cursor: not-allowed!important
    &:hover
        transform: scale(1.1)

.editBtn
    width: 24px
    height: 24px
    border-radius: 50%!important
    position: absolute
    top: -10px
    right: 35px
    cursor: pointer
    display: flex!important
    align-items: center
    justify-content: center
    padding: 0!important
    margin: 0!important
    background: var(--cpBlue)!important
    opacity: 1!important
    z-index: 4
    transition: all .2s ease-in-out
    > div
        margin: 0!important
    svg
        fill: var(--cpWhite)!important
        vertical-align: baseline !important
    a
        width: 12px
        height: 12px
        > svg
            width: 100%
            height: 100%
            vertical-align: baseline !important
    &:disabled
        cursor: not-allowed!important
    &:hover
        transform: scale(1.1)

.addFeatureBtn
    width: 24px
    height: 24px
    border-radius: 50%!important
    position: absolute
    top: -10px
    right: 65px
    cursor: pointer
    display: flex!important
    align-items: center
    justify-content: center
    padding: 0!important
    margin: 0!important
    background: var(--cpBlue)!important
    opacity: 1!important
    z-index: 4
    transition: all .2s ease-in-out
    > div
        margin: 0!important
    svg
        fill: var(--cpWhite)!important
    a
        width: 12px
        height: 12px
        > svg
            width: 100%
            height: 100%
            vertical-align: text-top
    &:disabled
        cursor: not-allowed!important
    &:hover
        transform: scale(1.1)

.noDropdown ::after
    display: none !important