.calendarContainer
    width: 100%
    border-radius: var(--Radius)!important
    border: 1px solid var(--cpBlueBorder)
    overflow: hidden
    position: relative
    > div:first-child
        border-bottom: 1px solid var(--cpBlueBorder)
        height: 75px
        > button
            background-color: var(--cpNavy)!important
            font-size: 22px
            text-transform: capitalize
            color: var(--cpWhite)
            &:hover
                background-color: var(--cpNavyShade3)!important
            > span
                color: var(--cpWhite)!important

.eventContainer
    border: 1px solid var(--cpNavy)
    border-radius: var(--Radius)
    padding: 8px
    margin: 8px 0
    background: white
    position: absolute

.eventDot
    width: 10px
    height: 10px
    border-radius: 50%
    margin-top: 5px

.tileContainer
    font: var(--BtnFont)
    font-weight: 400!important

.loaderContainer
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: 100%
    display: flex
    justify-content: center
    align-items: center
    z-index: 1
    > div
        position: unset !important
        background: rgba(255,255,255,0.3)!important
        svg
            width: 50px
            height: 50px
