.galleryTabCard
    position: relative
    > div > a
        border-radius: var(--Radius)
        overflow: hidden
        border: 1px solid rgba(0, 0, 0, 0.1)
        height: auto
        display: flex
        flex-direction: column
        .title span
            color: var(--cpNavy)
            font-size: 16px
            font-weight: 500
            margin-bottom: 0 !important
        .posted span
            font-size: 14px
            font-weight: 500
            margin-bottom: 0 !important

.textContainer
    padding: 10px 0

.imgContainer
    flex: 0 1 200px
    max-height: 200px
    img
        width: 100%
        height: 100%
        object-fit: cover

.likeGalleryBtn
    margin-right: 10px
