.commentMedia
    display: flex
    align-items: flex-start
    img
        width: 55px
        height: 55px

.rounded-circle 
    border-radius: 50% !important
    margin-right: 20px

.commentMediaBody
    flex: 1

.commentForm
        display: flex
        flex-direction: row
        justify-content: space-between
        @media (min-width: 1200px)
            padding-right: 60px
        div:first-child
            width: calc(100% - 70px)
            max-width: 100% !important
            margin: 0
            position: unset

.chatInput
    width: 100%
    border-radius: 0 0 10px 0px
    margin-top: auto
    > form
        display: flex
        justify-content: space-between
        align-items: center
        > div
            flex: 0 1 auto
            min-width: 80%
            margin: 0
            @media (max-width: 1200px)
             min-width : 45%
        > button
            display: flex
            border-radius: 20px!important
            background: var(--cpBlue)!important
            margin-left: 16px
            padding: 12px 32px!important
            color: var(--cpBg)!important
            &:hover
                color: var(--cpBg)!important

.closeBtn
  position: absolute
  top: 20px
  right: clamp(5%, 2vw , 7.5%)
  background: none!important
  border: none!important
  margin: 0!important
  padding: 0!important
  z-index: 5
  svg
    max-width: 17px