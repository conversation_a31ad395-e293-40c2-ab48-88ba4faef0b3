.imagePreviewModalContent
    min-height: auto !important
    height: auto !important
    @media(max-width: 768px)
        width: calc(100vw - 20px) !important
        max-width: calc(100vw - 20px) !important
    button:first-child
        top: 15px !important
        right: 15px !important
.imagePreviewModalBody
    width: auto
    height: auto !important
.imagePreviewModal
    height: 100%
    display: flex
    flex-direction: column
    .imagePreviewImageSection
        display: flex
        flex-direction: row
        justify-content: space-between
        align-items: center
        flex-wrap: nowrap
        height: 100%
        position: relative
        padding: 15px
        @media (min-width: 768px)
            padding: 35px
        .navigateBtnLeft
            position: absolute
            min-width: 25px!important
            max-width: 25px!important
            left: -15px
            top: calc(50% - 25px)
            @media(min-width: 768px)
                min-width: 50px!important
                max-width: 50px!important
                left: -25px
            svg
                cursor: pointer
                @media(max-width: 768px)
                    max-width: 25px
                    min-width: 25px
        .navigateBtnRight
            position: absolute
            min-width: 25px!important
            max-width: 25px!important
            right: -15px
            top: calc(50% - 25px)
            @media(min-width: 768px)
                min-width: 50px!important
                max-width: 50px!important
                right: -25px
            svg
                cursor: pointer
                @media(max-width: 768px)
                    max-width: 25px
                    min-width: 25px
    .imagePreviewMetaSection
        margin-left: 65px
        margin-top: 20px
        margin-bottom: 10px
        @media(max-width: 768px)
            margin-left: 25px

.imageContainer
    width: 100%
    overflow: hidden
    img
        width: 100%
        height: 100%
        max-height: 70vh
        object-fit: contain
        object-position: center
