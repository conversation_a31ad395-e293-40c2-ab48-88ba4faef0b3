.btnSecondary
    display: flex
    background-color: var(--cpBg)!important
    color: var(--cpOrange)!important
    font: var(--BtnFont)!important
    padding: 12px 16px !important
    border: 1px solid var(--cpOrange)!important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    &:hover
        background-color: var(--cpOrange)!important
        border-color: var(--cpOrange)!important
        color: white!important
    &:disabled
        background-color: #EDEEEF!important
        border-color: #EDEEEF!important
        color: #B0B0B5!important

    @media (min-width: 992px)
        padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important

.btnSecondaryAlternative
    display: flex
    background-color: var(--cpBg)!important
    color: #004DCE!important
    font: var(--BtnFont)!important
    padding: 12px 16px !important
    border: 1px solid #004DCE !important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    @media (min-width: 992px)
        padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important
    &:hover
        background-color: #004DCE!important
        color: var(--cpWhite)!important

.btnSecondaryAlternativeClear
    display: flex
    background-color: transparent !important
    color: #004DCE!important
    font: var(--BtnFont)!important
    padding: 12px 16px !important
    border: 1px solid #004DCE !important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    @media (min-width: 992px)
        padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important
    &:hover
        background-color: #004DCE!important
        color: var(--cpWhite)!important

.btnPrimary
    display: flex
    background-color: var(--cpOrange)!important
    color: var(--cpWhite)!important
    font: var(--BtnFont)!important
    padding: 12px 16px !important
    border: none!important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important

    &:hover
        background-color: #FFD0BE!important
        border-color: var(--cpOrange)!important
        color: var(--cpOrange)!important

    &:disabled
        background-color: #EDEEEF!important
        border-color: #EDEEEF!important
        color: #B0B0B5!important

    @media (min-width: 992px)
        padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important

.btnPrimaryAlternative
    display: flex
    background-color: #004DCE!important
    color: var(--cpWhite)!important
    font: var(--BtnFont)!important
    padding: 12px 16px !important
    border: none!important
    border-radius: var(--BtnRadius)!important
    font: var(--BtnLoginFont)!important
    flex-flow: column
    align-items: center
    justify-content: center
    svg
        fill: var(--cpNavy)!important
    @media (min-width: 992px)
        padding: var(--SecondaryBtnPaddingY) var(--BtnPaddingX)!important
        
.btnSmall
    min-width: unset !important
    font-size: clamp(14px,.9vw,16px) !important