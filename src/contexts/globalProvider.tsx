import {authReducer, initialAuthState} from "../reducers/auth.reducer";
import {initialPitchesState, pitchesReducer} from "../reducers/pitches.reducer";
import {SettingsProvider} from "./settings.context";
import {AuthProvider} from "./auth.context";
import {PitchesProvider} from "./pitches.context";
import {PusherStateProvider} from "./pusher.context";
import {initialSettingsState, settingsReducer} from "../reducers/settings.reducer";
import {initialPusherState, pusherReducer} from "../reducers/pusher.reducer";
import {NotificationsProvider} from "@mantine/notifications";
import {ReactQueryDevtools} from "@tanstack/react-query-devtools";
import {DndProvider} from "react-dnd";
import {HTML5Backend} from "react-dnd-html5-backend";
import {LocalizationProvider} from "@mui/x-date-pickers";
import {AdapterLuxon} from "@mui/x-date-pickers/AdapterLuxon";
import {CSVUploadProvider} from "./csvUploadContext.context";
import {csvUploadReducer, initialCSVUploadState} from "../reducers/csvUploadReducer.reducer";
import {ThemeProvider} from "../providers/ThemeProvider";
import {atomWithStorage} from "jotai/utils";
import {IWhitelabeling} from "../Interfaces/whitelabeling.interface";

export const whitelabelingAtom = atomWithStorage<IWhitelabeling | null>("whitelabeling", null);

interface IProps {
    children: React.ReactNode | React.ReactNode[];
}

export default function GlobalProvider(props: IProps) {
    return (
        <>
            <ThemeProvider>
                <ReactQueryDevtools initialIsOpen={false} />
                <NotificationsProvider position="top-right">
                    <SettingsProvider reducer={settingsReducer} initialState={initialSettingsState}>
                        <PusherStateProvider reducer={pusherReducer} initialState={initialPusherState}>
                            <AuthProvider reducer={authReducer} initialState={initialAuthState}>
                                <PitchesProvider reducer={pitchesReducer} initialState={initialPitchesState}>
                                    <DndProvider backend={HTML5Backend}>
                                        <LocalizationProvider dateAdapter={AdapterLuxon}>
                                            {/* ADD NEW PROVIDER AT THE END OF PROVIDERS WATERFALL */}
                                            <CSVUploadProvider
                                                reducer={csvUploadReducer}
                                                initialState={initialCSVUploadState}>
                                                {props.children}
                                            </CSVUploadProvider>
                                        </LocalizationProvider>
                                    </DndProvider>
                                </PitchesProvider>
                            </AuthProvider>
                        </PusherStateProvider>
                    </SettingsProvider>
                </NotificationsProvider>
            </ThemeProvider>
        </>
    );
}
