import React, {Reducer, useReducer} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {ICategory} from "../Interfaces/categories.interface";
import {IVendorStack} from "../Interfaces/myStack.interface";
import {ITypesProfile} from "../Interfaces/people.interface";
import {IProducts} from "../Interfaces/products.interface";
import CompanyType from "../constants/companyType.constant";
import routeConfig from "../constants/routeConfig";
import {USERS_TYPE} from "../enums/usersTypes.enum";
import {useSubdomain} from "../hooks/useSubdomain";
import {ITypes} from "../models";
import {IChat} from "../models/Pusher.interface";
import {IUserPitchObject} from "../models/UserPitchObject.interface";
import {CLEAR_AUTH, GET_USER_PITCHES, UPDATE_AUTH} from "../reducers/auth.reducer";
import {eventService} from "../services/event.service";
import {redirectWithoutSubdomain} from "../utils/url.util";

export interface IRoles {
    id: string;
    title: string;
    display_name: string;
    key: string;
    is_admin: boolean;
    description: string | null;
    company_id: string;
}
export interface IAuth {
    authenticated?: boolean;
    exp?: number;
    access_token?: string;
    firstName: string;
    lastName: string;
    email?: string;
    handle?: string;
    twoFA?: boolean;
    id?: string;
    avatar?: string;
    avatar_exp?: Date;
    company_display_name?: string;
    authenticatedForInMinutes?: string;
    userPitches: Array<string>;
    chats?: Array<IChat>;
    type_is_of_vendor?: boolean;
    company_id?: string;
    company_profile_claimer_id?: string;
    company_subdomain?: string;
    company_partner_flag?: boolean;
    stacks?: IVendorStack[];
    registered_all_pitches?: boolean;
    is_private?: boolean;
    need_to_setup_profile?: boolean;
    friendly_url: string;
    company_friendly_url?: string;
    company_avatar?: string;
    claimer_responses?: IClaimerResponses[];
    claimed_companies?: IClaimedCompanies[];
    showLoginModal?: boolean;
    user?: IUserInfo;
    profile_type?: USERS_TYPE;
    following?: any[];
    bookmarkedProducts?: IProducts[];
    phone?: string;
    mobile_phone?: string;
    job_title?: {id: string; name: string};
    email_verified_at?: string;
    phone_verified_at?: string;
    verication_code_verified?: boolean;
    prm_active?: boolean;
    company_profile_type?: {
        id: string;
        label: string;
        value: string;
        order: number;
        updated_at?: string | null;
        created_at?: string | null;
    } | null;
    company_type?: {
        id: string;
        label: string;
        value: string;
        order: number;
        type_is_of_vendor?: boolean;
        updated_at?: string | null;
        created_at?: string | null;
    };
    company?: {
        id: string;
        name: string;
        description: string | null;
        handle: string;
        friendly_url: string;
        avatar: string | null;
        type: ITypes;
        profile_type: ITypes;
        subcategories: ICategory[];
    };
    additional_info?: any;
    parent_subdomain?: string;
    event_email_subscribed?: boolean;
    permissions?: any;
    last_logged_in_at?: string | null;
    is_distributor?: boolean;
    is_affiliated?: boolean;
    affiliate_brand?: {
        id: string;
        name: string;
        main_company_id: string;
    };
    address?: string;
    show_distributor_banner?: boolean;
    show_affiliate_popup?: boolean;
    plaid_integration_enabled?: boolean | null;
}

export interface IAffiliateBrand {
    author_id: string;
    created_at: string;
    id: string;
    is_active: boolean;
    main_company_id: string;
    name: string;
    updated_at: string;
}

export interface IClaimedCompanies {
    id: string;
    name: string;
    friendly_url: string;
    profile_type: ITypesProfile;
    avatar?: string;
    type_is_of_vendor?: boolean;
    subdomain?: string;
    partner_flag?: boolean;
    is_distributor?: boolean;
    affiliate_brand?: IAffiliateBrand;
    parent_id?: string;
    company_type?: string;
}

export interface IClaimerResponses {
    id: string;
    response: string;
    status: string;
    referral: string;
    user_id?: string;
    company_id: string;
}

export interface IUserInfo {
    business_phone: string;
    job_title: string;
}

export interface IAuthContext {
    authState: IAuth;
    updateAuthState: (type: string, payload: Partial<IAuth>) => void;
    clearAuthState: (redirect?: boolean) => void;
}

const AuthContext = React.createContext<IAuthContext>({
    authState: {
        friendly_url: "",
        authenticated: false,
        userPitches: [],
        firstName: "",
        lastName: "",
        company_profile_type: null,
        user: {
            business_phone: "",
            job_title: "",
        },
    },
    updateAuthState: () => {},
    clearAuthState: () => {},
});

let loadingUserPitches = false;

export const AuthProvider = ({children, reducer, initialState}) => {
    const [globalAuthState, authDispatch] = useReducer<Reducer<IAuth, any>>(reducer, initialState);
    const navigate = useNavigate();
    const location = useLocation();
    const subdomainObj = useSubdomain();
    const subDomain = subdomainObj.company;
    const routesToIgnore = [
        routeConfig.Login.path,
        routeConfig.Logout.path,
        routeConfig.Register.path,
        routeConfig.Home.path,
    ];
    const clientRedirect = useSubdomain({
        defaultValues: {
            pathname: routeConfig.MSPClientLoginPage.path,
        },
    });

    const updateAuthState = (type: string, payload: Partial<IAuth>) => {
        switch (type) {
            case GET_USER_PITCHES: {
                if (!globalAuthState.authenticated) break;
                getUserPitches(globalAuthState.id!);
                break;
            }
            default:
                authDispatch({type, payload});
        }
    };

    const clearAuthState = (redirect?: boolean) => {
        authDispatch({type: CLEAR_AUTH});
        setTimeout(() => {
            if (globalAuthState.company_type?.value === CompanyType.MSP_CLIENT) {
                clientRedirect.navigate();
                return;
            }
            if (redirect && !routesToIgnore.includes(window.location.pathname)) {
                subDomain
                    ? redirectWithoutSubdomain(routeConfig.Login.path + "?redirect_url=" + window.location.pathname)
                    : navigate(routeConfig.Login.path + "?redirect_url=" + window.location.pathname);
                return;
            }
            if (location.pathname !== routeConfig.Logout.path) {
                subDomain
                    ? redirectWithoutSubdomain(routeConfig.Home.path)
                    : navigate(routeConfig.Home.path, {replace: true});
            }
        }, 100);
    };

    const getUserPitches = (id: string) => {
        if (loadingUserPitches) return;
        loadingUserPitches = true;
        const userPitches: string[] = [];
        eventService.getUserPitches(
            id,
            response => {
                response.data.forEach(function (item: IUserPitchObject) {
                    userPitches.push(item.id);
                });
                loadingUserPitches = false;
                authDispatch({type: UPDATE_AUTH, payload: {userPitches}});
            },
            () => {
                loadingUserPitches = false;
            },
        );
        return;
    };

    return (
        <AuthContext.Provider value={{authState: globalAuthState, updateAuthState, clearAuthState}}>
            {children}
        </AuthContext.Provider>
    );
};

export const AuthStateConsumer = AuthContext.Consumer;

export default AuthContext;
