import {QuestionFormTypeEnum} from "../../enums/questionFormTypes.enum";
import {Helmet} from "react-helmet";
import Survey from "../../uicomponents/Organism/Survey";
import {useEffect, useState} from "react";
import {questionService} from "../../services/questions.service";
import {AxiosError} from "axios";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../../hooks/useNotification";
import {IQuestionForms} from "../../Interfaces/questions.interface";

const ChannelMeetings = () => {
    const notify = useNotification();
    const [formData, setFormData] = useState<IQuestionForms>();

    useEffect(() => {
        questionService.getForm(QuestionFormTypeEnum.ChannelMeetings, handleSuccess, handleError);
    }, []);

    const handleSuccess = (response: any) => {
        setFormData(response.data);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    return (
        <>
            <Helmet>
                <title>{"Channel Meetings"}</title>
                <meta
                    name="description"
                    content={
                        "Interested in an upcoming Channel Meeting? Tell us more and we’ll match you with the best vendor for your needs."
                    }
                />
                <meta
                    property="og:description"
                    content={
                        "Interested in an upcoming Channel Meeting? Tell us more and we’ll match you with the best vendor for your needs."
                    }
                />
                <meta property="og:url" content="https://www.channelprogram.com" />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:title" content={"Channel Meetings"} />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta property="website:tag" content={"channel meetings, channel industry, meeting"} />
                <meta property="keywords" content={"channel meetings, channel industry, meeting"} />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={"/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta property="fb:admins" content="Channel-Program-113031624351535" />
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:site" content="@bethechannel" />
                <meta name="twitter:title" content={"Channel Meetings"} />
                <meta
                    name="twitter:description"
                    content={
                        "Interested in an upcoming Channel Meeting? Tell us more and we’ll match you with the best vendor for your needs."
                    }
                />
                <meta name="twitter:image" content={"/Media/seo/twitter-home.jpg"} />
            </Helmet>
            <Survey
                surveyType={QuestionFormTypeEnum.ChannelMeetings}
                headerTitle={formData?.header_text ?? "Channel Meetings"}
                descriptionLine1={formData?.subheader_text}
                descriptionLine2={formData?.instruction_text}
            />
        </>
    );
};

export default ChannelMeetings;
