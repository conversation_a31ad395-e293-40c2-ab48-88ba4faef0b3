import useTheme from "@mui/material/styles/useTheme";
import {memo, useEffect, useMemo, useState} from "react";
import {useNavigate, useSearchParams} from "react-router-dom";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS, SYNCING_PLAID} from "../../constants/commonStrings.constant";
import {useCompanyExpenses} from "../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../hooks/useActiveCompany";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import CompanyExpensesOverviewTab from "../../uicomponents/Organism/CompanyExpenses/CompanyExpensesOverviewTab.component";
import CompanyExpensesSubscriptionsTab from "../../uicomponents/Organism/CompanyExpenses/CompanyExpensesSubscriptionsTab.component";
import CompanyExpensesTransactionsTab from "../../uicomponents/Organism/CompanyExpenses/CompanyExpensesTransactionsTab.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import DownloadOutlinedIcon from "@mui/icons-material/DownloadOutlined";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import StackedLineChartIcon from "@mui/icons-material/StackedLineChart";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import Skeleton from "@mui/material/Skeleton";
import {useAtom} from "jotai";
import {DateTime} from "luxon";
import CustomLink from "../../components/Links/CustomLink.component";
import CompanyType from "../../constants/companyType.constant";
import routeConfig from "../../constants/routeConfig";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useCompanyProfile from "../../hooks/useCompanyProfile";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {companyService} from "../../services/company.service";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Switch from "../../uicomponents/Atoms/Switch/Switch.component";
import {ExpensesBreakdownDetailAtom} from "../../uicomponents/ModalManager/ExpensesModals/expenses.atoms";
import AlertBanner from "../../uicomponents/Molecules/AlertBanner/alertBanner.component";
import AvatarComponentV2 from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import PlaidLinkButton from "../../uicomponents/Molecules/Plaid/plaidLinkButton.component";
import CompanyExpensesLinkedAccountsTab from "../../uicomponents/Organism/CompanyExpenses/CompanyExpensesLinkedAccountsTab.component";
import {pluralizeString} from "../../utils/formatString.util";
import Loader from "../../utils/loader";
import {plaidService} from "../../services/plaid.service";
import {downloadDoc} from "../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../../hooks/useNotification";
import MenuButton from "../../uicomponents/Molecules/MenuButton/menuButton.component";
import {COMPANY_EXPENSES_TEXT, IS_DIRECT_IT_SITE, STACK_EXPENSES_TEXT} from "../../constants/siteFlags";
import usePricingLink from "../../hooks/usePricingLink";

interface ISyncInfo {
    open: boolean;
    sync: string[];
    notSync: string[];
}

const CompanyExpensesPage = memo(({clientPage}: {clientPage?: boolean}) => {
    const isClientPage = !!clientPage;
    const [isExporting, setIsExporting] = useState<boolean>(false);
    const [isTogglingHideExpenses, setIsTogglingHideExpenses] = useState(false);
    const [, setDetailsDrawerData] = useAtom(ExpensesBreakdownDetailAtom);
    const theme = useTheme();
    const {
        activeCompany,
        isLoading,
        hasExpensesAccess,
        isClientMsp,
        selectedFriendlyUrl,
        isParentMSPActingAsCustomer,
        isAffiliateMsp,
        isMSPLocation,
    } = useActiveCompany();
    const {
        expensesCount,
        isPlaidEnabled,
        isLoadingPlaidEnabled,
        hasNoAccounts,
        isInitialSyncInProgress,
        hasSomeAccountsSyncing,
    } = useCompanyExpenses(activeCompany?.id, {
        calls: {
            all: false,
            expensesCount: true,
            linkedAccounts: true,
        },
    });
    const [searchParams] = useSearchParams();
    const {updateMatchedQueries} = useQueryHelper();
    const navigate = useNavigate();
    const activeTab = searchParams.get("tab");
    const showSubscribeNow =
        !isLoading && (isClientMsp ? !activeCompany?.client_plaid_integration_enabled : !hasExpensesAccess);
    const link = usePricingLink();
    const {companyInfo} = useCompanyProfile({
        calls: {rules: false},
        companyFriendlyUrl: selectedFriendlyUrl,
        companyParams: ["client_parent", "parent", "followers_count"],
    });
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const notify = useNotification();
    const clientParent = companyInfo?.client_parent;
    const isExpensesHidden = isClientMsp ? companyInfo?.hide_expenses : false;
    const hasReadAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_READ]);
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const [syncInfo, setSyncInfo] = useState<ISyncInfo>({
        open: false,
        sync: [],
        notSync: [],
    });

    const handleExportClick = option => {
        setIsExporting(true);
        const days = Number(option.id);
        plaidService.exportExpensesToCSV(
            activeCompany?.id || "",
            days,
            res => {
                downloadDoc(res.data, `company_expenses_past_${days}_days.csv`);
                setIsExporting(false);
            },
            err => {
                notify(getErrorFromArray(err), "Error");
                setIsExporting(false);
            },
        );
    };

    const handleOpenStackExpenses = () => {
        const start = DateTime.now().startOf("month").toUTC().toISO();
        const end = DateTime.now().endOf("month").toUTC().toISO();
        setDetailsDrawerData({
            open: true,
            data: {
                type: "stack-categories",
                start_date: start,
                end_date: end,
                item: undefined,
            },
        });
    };

    const handleHideExpensesToggle = (checked: boolean) => {
        if (!activeCompany?.id || !activeCompany?.friendly_url) return;
        setIsTogglingHideExpenses(true);
        companyService
            .toggleHideExpenses(activeCompany?.id, checked)
            .then(res => {
                updateMatchedQueries(["company", activeCompany.friendly_url], old => {
                    if (!old) return old;
                    return {...old, ...res.data};
                });
            })
            .finally(() => setIsTogglingHideExpenses(false));
    };

    const overviewTabComponent = useMemo(() => <CompanyExpensesOverviewTab />, []);

    const tabs = useMemo(() => {
        return [
            {
                id: "overview",
                label: (
                    <Box display="flex" alignItems="center" gap={1}>
                        Overview
                    </Box>
                ),
                Component: overviewTabComponent,
            },
            {
                id: "subscriptions",
                label: (
                    <Box display="flex" alignItems="center" gap={1}>
                        Recurring Expenses
                        <Badge color="blue">{expensesCount.data?.subscriptions || 0}</Badge>
                    </Box>
                ),
                Component: <CompanyExpensesSubscriptionsTab />,
            },
            {
                id: "transactions",
                label: (
                    <Box display="flex" alignItems="center" gap={1}>
                        Transactions
                        <Badge color="blue">{expensesCount.data?.transactions || 0}</Badge>
                    </Box>
                ),
                Component: <CompanyExpensesTransactionsTab hiddenColumns={["account.name"]} />,
            },
            {
                id: "linked-accounts",
                label: (
                    <Box display="flex" alignItems="center" gap={1}>
                        Linked Accounts
                        <Badge color="blue">{expensesCount.data?.bank_accounts || 0}</Badge>
                    </Box>
                ),
                Component: <CompanyExpensesLinkedAccountsTab setSyncInfo={setSyncInfo} />,
            },
        ];
    }, [expensesCount?.dataUpdatedAt]);

    useEffect(() => {
        if ((!isLoadingPlaidEnabled && !isPlaidEnabled) || isParentMSPActingAsCustomer) {
            navigate(routeConfig.Home.path);
        }
    }, [isPlaidEnabled, isLoadingPlaidEnabled, isParentMSPActingAsCustomer]);

    if (
        (!showSubscribeNow && !isLoadingPermissions && !hasReadAccess) ||
        (isClientMsp && !activeCompany?.client_plaid_integration_enabled) ||
        ((isAffiliateMsp || isMSPLocation) && !activeCompany?.manage_expenses)
    ) {
        return (
            <Box className={isClientPage ? "" : ROUTE_CLASS} sx={{overflowY: "scroll", position: "relative"}}>
                <Box className={isClientPage ? "" : ROUTE_CONTAINER_CLASS} overflow="hidden" padding={2}>
                    <AlertBanner
                        id="test"
                        title={{text: "Feature not enabled yet."}}
                        subTitle={{
                            text: "Please reach out to your account administrator to get access to this feature.",
                        }}
                        variant="info"
                    />
                </Box>
            </Box>
        );
    }
    return (
        <Box className={isClientPage ? "" : ROUTE_CLASS} sx={{overflowY: "scroll", position: "relative"}}>
            <Box className={isClientPage ? "" : ROUTE_CONTAINER_CLASS} overflow="hidden" padding={2}>
                <PageInnerHeader
                    id="company-expenses"
                    wrapperSx={{
                        backgroundColor: theme.palette.background.default,
                    }}
                    title={{
                        text: COMPANY_EXPENSES_TEXT,
                        sx: {
                            fontSize: "26px !important",
                            fontWeight: "600 !important",
                            color: theme.palette.blue[800],
                        },
                    }}
                    hideDivider={IS_DIRECT_IT_SITE}
                    actions={{
                        primary: showSubscribeNow
                            ? {
                                  id: "upgrade-now",
                                  children: "Upgrade to Access",
                                  variant: "tonal",
                                  to: link,
                                  color: "primary",
                                  target: "_blank",
                                  disabled: !link,
                              }
                            : undefined,
                        custom: showSubscribeNow ? undefined : (
                            <>
                                {hasEditAccess && (
                                    <PlaidLinkButton
                                        variant="outlined"
                                        color="blue"
                                        children={
                                            <>
                                                <AddOutlinedIcon /> Add Account
                                            </>
                                        }
                                        sx={{gap: 1}}
                                    />
                                )}
                                {!hasNoAccounts && (
                                    <Button
                                        id="open-stack-expenses"
                                        variant={IS_DIRECT_IT_SITE ? "tonal" : "contained"}
                                        color="blue"
                                        sx={{display: "flex", alignItems: "center", gap: 1}}
                                        onClick={handleOpenStackExpenses}>
                                        <StackedLineChartIcon />
                                        {STACK_EXPENSES_TEXT}
                                    </Button>
                                )}
                            </>
                        ),
                    }}
                />
                {isInitialSyncInProgress ? (
                    <Box display="flex" alignItems="center" gap={2}>
                        <Loader mui inline loading size={40} />
                        <Box display="flex" flexDirection="column" gap={1}>
                            <Typography variant="16" fontWeight={700} color={theme.palette.neutral[700]}>
                                {SYNCING_PLAID}
                            </Typography>
                        </Box>
                    </Box>
                ) : showSubscribeNow ? (
                    <CustomLink to={link} target="_blank">
                        <img
                            src="/Media/banners/companyExpensesUpgradeBanner.png"
                            alt="Upgrade to Access"
                            style={{width: "100%", height: "auto"}}
                        />
                    </CustomLink>
                ) : (
                    <>
                        {isClientMsp && !isParentMSPActingAsCustomer && (
                            <AlertBanner
                                id="customer-privacy-settings"
                                variant="info"
                                title={{text: "Privacy Settings"}}
                                subTitle={{text: "Share your expenses data with your provider?"}}
                                icon={
                                    <VisibilityOutlinedIcon
                                        sx={{height: 24, width: 24}}
                                        htmlColor={theme.palette.blue[600]}
                                    />
                                }
                                customActions={
                                    <Box display="flex" gap={2} alignItems="center" marginTop={1}>
                                        <Box display="flex" gap={"10px"} alignItems="center">
                                            <Switch
                                                onChange={(_, checked) => {
                                                    handleHideExpensesToggle(!checked);
                                                }}
                                                checked={!isExpensesHidden}
                                                disabled={isTogglingHideExpenses || !hasEditAccess}
                                            />
                                            <Typography variant="14" fontWeight={700} textOverflow="ellipsis">
                                                Shared with Provider
                                            </Typography>
                                        </Box>
                                        <Box display="flex" gap={"10px"} alignItems="center">
                                            {!!clientParent ? (
                                                <AvatarComponentV2
                                                    isCompany
                                                    user={{
                                                        name: clientParent.name || "",
                                                        avatar: clientParent.avatar || "",
                                                        profile_type: "msp",
                                                        friendly_url: clientParent.friendly_url,
                                                        type: CompanyType.ISP_ALL,
                                                    }}
                                                    showProfileType
                                                    profileTypeMinimized
                                                    size={32}
                                                    disableLink
                                                />
                                            ) : (
                                                <Skeleton width={32} height={32} variant="circular" />
                                            )}
                                            <Typography variant="14" fontWeight={700} textOverflow="ellipsis">
                                                {clientParent?.name || "Loading..."}
                                            </Typography>
                                        </Box>
                                    </Box>
                                }
                            />
                        )}
                        {hasSomeAccountsSyncing && (
                            <Box display="flex" alignItems="center" gap={2}>
                                <Loader mui inline loading size={40} />
                                <Box display="flex" flexDirection="column" gap={1}>
                                    <Typography variant="16" fontWeight={700} color={theme.palette.neutral[700]}>
                                        {SYNCING_PLAID}
                                    </Typography>
                                </Box>
                            </Box>
                        )}
                        {syncInfo.open && (
                            <AlertBanner
                                id="sync-plaid-account"
                                variant="warning"
                                onClose={() => {
                                    setSyncInfo({...syncInfo, open: false});
                                }}
                                title={{
                                    text: `${syncInfo.sync.length} / ${
                                        syncInfo.sync.length + syncInfo.notSync.length
                                    } ${pluralizeString("account", syncInfo.sync.length)} ${
                                        syncInfo.sync.length == 1 ? `was` : `were`
                                    } synced`,
                                    sx: {fontSize: "16px !important"},
                                }}
                                subTitle={{
                                    html: `Daily account sync limit reached for these accounts. Please try again later.
                                        <ul>
                                            ${
                                                syncInfo.notSync.length > 0
                                                    ? `<li><b>Not synced</b>: ${syncInfo.notSync.join(", ")}</li>`
                                                    : ""
                                            }
                                            ${
                                                syncInfo.sync.length > 0
                                                    ? `<li><b>Synced</b>: ${syncInfo.sync.join(", ")}</li>`
                                                    : ""
                                            }
                                        </ul>
                                        `,
                                }}
                                iconSx={{
                                    alignSelf: "flex-start",
                                }}
                            />
                        )}

                        <BasicTabs
                            tabsContentSx={{paddingTop: 2, paddingRight: 2}}
                            hideBorder
                            shouldTrackRoute={!isClientPage}
                            key={activeTab}
                            tabs={tabs}
                            defaultTab={activeTab || "overview"}
                            variant="scrollable"
                            children={
                                <MenuButton
                                    id="export-expenses"
                                    buttonProps={{
                                        loading: false,
                                        variant: "outlined",
                                        color: "blue",
                                        sx: {
                                            fontSize: "12px",
                                            padding: "4px 12px",
                                            whiteSpace: "nowrap",
                                            display: "flex",
                                            alignItems: "center",
                                            gap: "8px",
                                            fontWeight: "600 !important",
                                            "@media (min-width: 768px)": {
                                                fontSize: "14px",
                                                padding: "6px 20px",
                                            },
                                        },
                                        disabled: hasNoAccounts || isLoading || isExporting,
                                    }}
                                    menuProps={{
                                        sx: {
                                            marginTop: "8px",
                                        },
                                    }}
                                    onChange={handleExportClick}
                                    showDropdownIcon
                                    items={[
                                        {id: "30", children: "Past 30 days"},
                                        {id: "60", children: "Past 60 days"},
                                        {id: "90", children: "Past 90 days"},
                                        {id: "180", children: "Past 180 days"},
                                        {id: "365", children: "Past 365 days"},
                                    ]}>
                                    <DownloadOutlinedIcon /> {isExporting ? "Exporting..." : "Export"}
                                </MenuButton>
                            }
                        />
                    </>
                )}
            </Box>
        </Box>
    );
});

export default CompanyExpensesPage;
