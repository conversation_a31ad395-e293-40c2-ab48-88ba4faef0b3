import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {useLocation} from "react-router";
import AddEventCalendarButton from "../../components/CalendarControls/AddEventCalendarButton.component";
import {SocialShareList} from "../../components/SocialShare/SocialShareList.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import usePitchesState from "../../hooks/usePitchesState";
import useSettings from "../../hooks/useSettings";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {eventService} from "../../services/event.service";
import {genericErrorString} from "../../utils/error.util";
import {DATE_TIME_FORMAT, formatDate} from "../../utils/formatDate";
import useQuery from "../../utils/query.util";

export default function PitchPage() {
    const [eventRegistered, setEventRegistered] = useState<string | undefined>();
    const [error, setError] = useState<string>("");
    const [title, setTitle] = useState<string>("");
    const [title2, setTitle2] = useState<string>("");

    const [message, setMessage] = useState<Array<string>>([]);

    const {authState, updateAuthState} = useAuthState();
    const {pitchesState} = usePitchesState();
    const {updateSettings} = useSettings();

    const query = useQuery();
    const eventID = query.get("id");
    const location = useLocation();
    let pitch: any = {};
    let pitchName: string = "";
    let pitchDate: string = "";
    if (pitchesState.pitches) {
        pitch = pitchesState.pitches.find(p => p.id === eventID);
        pitchName = pitchesState.pitches.filter(pitch => pitch.id === eventID)[0].name;
        pitchDate = formatDate(
            pitchesState.pitches.filter(pitch => pitch.id === eventID)[0].start_date,
            DATE_TIME_FORMAT,
            true,
        );
    }

    const handleSucess = (response: AxiosResponse) => {
        setEventRegistered(response?.data[0]?.name);
        sessionStorage.removeItem("eventID");
        sessionStorage.removeItem("redirect_url");
        setTitle("Congratulations!");
        setTitle2(`You have successfully registered for "${response.data[0].name}" event on ${pitchDate}.`);

        setMessage([
            "Thank you for joining the revolution. Get ready to be a channel influencer.",
            "We will be in touch with information leading up to the event.",
        ]);
        updateSettings(UPDATE_SETTINGS, {loading: false});
        if (authState?.userPitches && authState?.userPitches?.length > 0) {
            return updateAuthState(UPDATE_AUTH, {userPitches: [...authState?.userPitches, eventID]});
        }
        return updateAuthState(UPDATE_AUTH, {userPitches: [eventID]});
    };

    const handleError = (error: AxiosError<any>) => {
        if (error.response?.data?.errors?.message[0] === "ATTENDANCE_ALREADY_EXISTS") {
            setTitle("Congratulations!");
            setTitle2(`You have successfully registered for "${pitchName}" event on ${pitchDate}.`);

            setMessage([
                "Thank you for joining the revolution. Get ready to be a channel influencer.",
                "We will be in touch with information leading up to the event.",
            ]);
            updateSettings(UPDATE_SETTINGS, {loading: false});
            return setError("You're already registered for this event.");
        }
        setMessage(["You have not registered for this Event yet."]);
        setError(genericErrorString(error));
    };

    useEffect(() => {
        if (eventID && authState.id) {
            updateSettings(UPDATE_SETTINGS, {loading: true});
            eventService.attendEvent(eventID, authState.id, handleSucess, handleError);
        } else {
            setError("No Event was chosen.");
        }
        // eslint-disable-next-line
    }, [location]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                {}
                {eventRegistered ? (
                    <>
                        <TitleContainer as="h2" text={title} />
                        <TitleContainer as="h2" text={title2} noConnector />
                        {message.map((messageText: string) => (
                            <SubtitleContainer as="p" text={messageText} key={messageText} />
                        ))}
                    </>
                ) : error.length > 1 ? (
                    <>
                        <TitleContainer as="h2" text={title} />
                        <TitleContainer as="h2" text={title2} noConnector />
                        {message.map((messageText: string) => (
                            <SubtitleContainer as="p" text={messageText} key={messageText} />
                        ))}
                    </>
                ) : (
                    <TitleContainer as="h2" text={`Registering your attendance`} />
                )}
                {eventRegistered ? (
                    <div className="w-100 d-flex justify-content-center mt-5">
                        <AddEventCalendarButton id={pitch.name} addEventId={pitch.addevent_id} />
                    </div>
                ) : null}
                <div className="p-2 mt-5">
                    <p className="text-center">Share Channel Pitch Events on Social Media!</p>
                    <SocialShareList
                        facebook
                        instagram
                        twitter
                        linkedIn
                        email
                        slack
                        teams
                        copyLink
                        discord
                        url={`${window.location.origin}${routeConfig.Pitch.path}`}
                    />
                </div>
            </div>
        </div>
    );
}
