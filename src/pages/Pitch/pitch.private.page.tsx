import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useCallback, useEffect, useRef, useState} from "react";
import {Helmet} from "react-helmet";
import BroadcastChatTabs from "../../components/Admin/BroadcastChat.component";
import {RegisterAllPitchesBtn} from "../../components/Buttons/registerFuturesPitchesBtn.component";
import InfoBanner from "../../components/DescriptionBanner/infoBanner.component";
import {InfoIcon} from "../../components/Icons";
import BreakoutRooms from "../../components/Pitch/breakoutRooms.component";
import PollModal from "../../components/Polls/pollModal.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {VideoPlayer} from "../../components/VideoPlayer";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import {PusherEventsEnum, PusherEvtTypesEnum, userChatListener} from "../../enums/pusherEvents.enum";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePitchesState from "../../hooks/usePitchesState";
import usePusher, {PRIVATE_CHANNEL} from "../../hooks/usePusher";
import usePusherState from "../../hooks/usePusherState";
import {
    IBreakoutRooms,
    IBroadcastMsg,
    IChat,
    IPublicChat,
    IPusherData,
    IPusherPoll,
} from "../../models/Pusher.interface";
import {GET_NOT_STARTED, UPDATE_PITCHES} from "../../reducers/pitches.reducer";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {eventService} from "../../services/event.service";
import styles from "../../styles/pages/pitchPrivate.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import gZipInflate from "../../utils/gZipInflate.util";
import Loader from "../../utils/loader";
import useQuery from "../../utils/query.util";

const sortPublicChat = (a: IBroadcastMsg | IPublicChat, b: IBroadcastMsg | IPublicChat) => {
    const aDate = DateTime.fromISO(a.date);
    const bDate = DateTime.fromISO(b.date);
    return aDate === bDate ? 0 : aDate < bDate ? -1 : 1;
};

export default function PitchPrivatePage() {
    const [chatHistory, setChatHistory] = useState<IChat[]>([]);
    const [newPoll, setNewPoll] = useState<IPusherPoll | undefined>();
    const [pusherChannel, setPusherChannel] = useState<string>("");
    const [eventEnded, setEventEnded] = useState(false);
    const [eventEndDate, setEventEndDate] = useState<string>();
    const [breakoutRooms, setBreakoutRooms] = useState<IBreakoutRooms[]>();
    const [breakoutExpired, setBreakoutExpired] = useState(false);
    const [currentTime, setCurrentTime] = useState<DateTime>();
    const [newBMsg, setNewBMsg] = useState<string>("");
    const [newChatMsg, setNewChatMsg] = useState<IChat>();
    const [editedBMsg, setEditedBMsg] = useState<IBroadcastMsg>();
    const [newPublicMsg, setNewPublicMsg] = useState<IPublicChat>();

    const hasChecked: {current: boolean} = useRef(false);
    const {authState} = useAuthState();
    const {pitchesState, updatePitchesState} = usePitchesState();
    const {pusherState, updatePusherState, clearPState} = usePusherState();
    const notify = useNotification();
    const {BindEvents} = usePusher();
    const query = useQuery();

    const eventId = query.get("pitch_event_id") || pitchesState.pitches?.[0]?.id;
    const eventName = pitchesState.pitches && pitchesState.pitches.find(pitch => pitch.id === eventId)?.name;
    const eventInfo = pitchesState.pitches && pitchesState.pitches.find(pitch => pitch.id === eventId);
    const publicMessagesRef = useRef({});
    if (eventId && pusherState.publicChatHistory) {
        publicMessagesRef.current = pusherState.publicChatHistory || {};
    }
    const privateEvts = [
        userChatListener(authState?.id!),
        PusherEventsEnum.Broadcast,
        PusherEventsEnum.EndPitch,
        PusherEventsEnum.PollsListener,
        PusherEventsEnum.EndPitch,
        PusherEventsEnum.EditBroadcast,
        PusherEventsEnum.PublicMsgListener,
        PusherEventsEnum.DeletePublicMsgListener,
        PusherEventsEnum.BanPublicUserListener,
    ];
    const isVendor = authState.type_is_of_vendor;
    const exitFunc = document.exitFullscreen || (document as any).webkitExitFullscreen;
    const exitFullscreen = exitFunc?.bind(document);
    const nextPitchId = pitchesState.pitches && pitchesState.pitches.length > 1 ? pitchesState.pitches[1].id : null;

    const handleNewBroadcast = (msgToParse: string) => {
        setNewBMsg(msgToParse);
    };

    const handleEditBroadcast = (b: IBroadcastMsg) => {
        setEditedBMsg(b);
    };

    const handleChatMessage = (msgToParse: string) => {
        const newChatMessage: IChat = JSON.parse(msgToParse);
        setNewChatMsg(newChatMessage);
    };

    const handleNewPoll = (data: IPusherData) => {
        const pollToShow: IPusherPoll = JSON.parse(data.message);
        setNewPoll(pollToShow);
    };

    const handleEndEvent = () => {
        setEventEnded(true);
        const endDate = DateTime.now().toISO();
        setEventEndDate(endDate);
        const currentEvent = pitchesState.pitches?.find(pitch => pitch.id === eventId);
        const otherEvents = pitchesState.pitches?.filter(pitch => pitch.id !== eventId);
        if (currentEvent && otherEvents) {
            currentEvent.end_date = endDate;
            otherEvents.push(currentEvent);
            updatePitchesState(UPDATE_PITCHES, {pitches: otherEvents});
        }
    };

    const handleSuccess = (response: AxiosResponse) => {
        const channel = response.data.pusherChannel;
        const chatHistory: IChat[] = response.data.chatHistory;
        const broadcastHistory: IBroadcastMsg[] = response.data.broadcastHistory;
        const broadcastMsgs: IBroadcastMsg[] = broadcastHistory.sort((a, b) =>
            a.date === b.date ? 0 : a.date > b.date ? -1 : 1,
        );
        const publicChatHistory = [...response.data.publicChatHistory, ...broadcastMsgs].sort(sortPublicChat);
        setPusherChannel(channel);
        setTimeout(() => {
            updatePusherState(UPDATE_PUSHER, {
                broadcastMsgs,
                chatHistory: {...pusherState.chatHistory, [eventId!]: chatHistory},
                publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: publicChatHistory},
            });
        }, 300);
        setBreakoutRooms(response.data.vendors);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const deletePublicMessage = useCallback(
        (msg: any) => {
            const msgId = typeof msg === "string" ? msg : msg.message_id;
            const found = publicMessagesRef.current[eventId!]?.find(chat => chat.id === msgId);
            if (found?.creator === authState.id || !found) return;
            const newPublicChat = [...publicMessagesRef.current[eventId!]].filter(chat => chat.id !== msgId);
            updatePusherState(UPDATE_PUSHER, {
                publicChatHistory: {...publicMessagesRef.current, [eventId!]: newPublicChat},
            });
        },

        [eventId, pusherState.publicChatHistory, updatePusherState, authState.id],
    );

    const privateCallback = (data: IPusherData) => {
        const type = data.type || data.event;
        switch (type) {
            case PusherEvtTypesEnum.MsgListener:
                handleChatMessage(data.message);
                break;
            case PusherEventsEnum.Broadcast:
                handleNewBroadcast(data.message);
                break;
            case PusherEventsEnum.PollsListener:
                const parsedData = {...data};
                parsedData.message = gZipInflate(data.message, "string");
                handleNewPoll(parsedData);
                break;
            case PusherEventsEnum.EndPitch:
                handleEndEvent();
                break;
            case PusherEventsEnum.EditBroadcast:
                handleEditBroadcast(JSON.parse(data.message));
                break;
            case PusherEventsEnum.PublicMsgListener:
                const parsedMsg = gZipInflate(data.message, "parsed");
                const newMessage: IPublicChat = {
                    id: parsedMsg.message_id,
                    message: parsedMsg.message,
                    message_type: "publicChat",
                    creator: parsedMsg.creator_user_id,
                    date: parsedMsg.created_at,
                    pusher_channel: PRIVATE_CHANNEL,
                    receiver: eventId || "",
                    can_ban: parsedMsg.can_ban,
                };
                setNewPublicMsg(newMessage);
                break;
            case PusherEventsEnum.DeletePublicMsgListener:
                deletePublicMessage(gZipInflate(data.message, "parsed"));
                break;
            case PusherEventsEnum.BanPublicUserListener:
                deletePublicMessage((data as any).message_id);
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        //? Checking if pitchesState is loaded in through session storage if not then fetching from server
        if (
            (!pitchesState.pitches || pitchesState.pitches?.findIndex(pitch => pitch.id === eventId) === -1) &&
            !hasChecked.current
        ) {
            hasChecked.current = true;
            updatePitchesState(GET_NOT_STARTED);
        }
        if (!pusherChannel && eventId) {
            eventService.loadViewerInfo(eventId, handleSuccess, handleError);
        }
        const eventInfo = pitchesState.pitches && pitchesState.pitches.find(pitch => pitch.id === eventId)!;
        const tempEndDate = eventInfo?.end_date;
        if (tempEndDate) {
            setEventEnded(true);
            setEventEndDate(tempEndDate || "");
        }
    }, [pitchesState.pitches]);

    useEffect(() => {
        if (pusherState.privateChannel && authState.authenticated) {
            clearPState();
            BindEvents(privateEvts, privateCallback);
        }
    }, [pusherState.privateChannel]);

    useEffect(() => {
        if (eventEnded) {
            if (eventEndDate && currentTime) {
                const endDate = DateTime.fromISO(eventEndDate);
                if (endDate.diff(currentTime, "minutes").minutes > 60) {
                    setBreakoutExpired(true);
                }
            }
        }
    }, [eventEnded, currentTime]);

    useEffect(() => {
        const updateTime = setTimeout(() => {
            setCurrentTime(DateTime.local());
        }, 60000);
        return () => {
            clearTimeout(updateTime);
        };
    }, [currentTime]);

    useEffect(() => {
        if (newBMsg) {
            const parsed = JSON.parse(newBMsg);
            const newMsg: IBroadcastMsg = {...parsed, message_type: "broadcastChat"};
            const newBMsgsHistory: IBroadcastMsg[] = pusherState.broadcastMsgs && [...pusherState.broadcastMsgs];
            newBMsgsHistory.unshift(newMsg);
            const sortedPublicChat = [...pusherState.publicChatHistory[eventId!], newMsg].sort(sortPublicChat);
            updatePusherState(UPDATE_PUSHER, {
                broadcastMsgs: newBMsgsHistory,
                publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: sortedPublicChat},
            });
            setNewBMsg("");
        }
    }, [newBMsg]);

    useEffect(() => {
        if (editedBMsg) {
            const copy = [...pusherState.broadcastMsgs];
            const foundOldIndex = copy.findIndex(bMsg => bMsg.id === editedBMsg.edited_message_id);
            const newPublicChat = [...pusherState.publicChatHistory[eventId!]].map(chat => {
                if (chat.id === editedBMsg.edited_message_id) {
                    return {...editedBMsg};
                }
                return chat;
            });
            if (foundOldIndex > -1) {
                copy.splice(foundOldIndex, 1, editedBMsg);
                updatePusherState(UPDATE_PUSHER, {
                    broadcastMsgs: copy,
                    publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: newPublicChat},
                });
            }
            setEditedBMsg(undefined);
        }
    }, [editedBMsg]);

    useEffect(() => {
        if (newChatMsg) {
            const now = DateTime.now().toISO();
            const newMsg: IChat = {
                id: now,
                message_type: "moderatorMessage",
                message: newChatMsg.message,
                date: now,
                creator: eventId!,
                receiver: authState.id!,
                sender_first_name: newChatMsg.sender_first_name,
                sender_last_name: newChatMsg.sender_last_name,
                company_name: newChatMsg.company_name,
                company_type: newChatMsg.company_type,
            };
            const newChatHistory: IChat[] = pusherState.chatHistory[eventId!] && [...pusherState.chatHistory[eventId!]];
            newChatHistory.push(newMsg);
            updatePusherState(UPDATE_PUSHER, {chatHistory: {...pusherState.chatHistory, [eventId!]: newChatHistory}});
            setNewChatMsg(undefined);
        }
    }, [newChatMsg]);

    useEffect(() => {
        if (newPublicMsg) {
            const newChatHistory: IPublicChat[] = pusherState.publicChatHistory[eventId!] && [
                ...pusherState.publicChatHistory[eventId!],
            ];
            newChatHistory.push(newPublicMsg);
            updatePusherState(UPDATE_PUSHER, {
                publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: newChatHistory},
            });
            setNewPublicMsg(undefined);
        }
    }, [newPublicMsg]);

    useEffect(() => {
        if (newPoll) {
            updatePusherState(UPDATE_PUSHER, {polls: newPoll});
            setNewPoll(undefined);
            if (document.fullscreenElement) {
                exitFullscreen();
            }
        }
    }, [newPoll]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <Helmet>
                    <title>{eventName || "Channel Engage"} - Channel Program</title>
                    <meta name="description" content={eventInfo?.description} />
                </Helmet>
                <div className={styles.pitchContainer}>
                    <div className="d-flex justify-content-between">
                        <div>
                            <TitleContainer
                                as="h2"
                                text={eventName || "Channel Engage"}
                                position="start"
                                mobileCenter
                            />
                        </div>
                        {!authState.registered_all_pitches && nextPitchId && (
                            <div className="mb-1 mx-5">
                                <RegisterAllPitchesBtn
                                    variant="cpMainBtn"
                                    loadingIcon="iconWhite"
                                    inlineLoading={true}
                                    nextPitchId={nextPitchId}
                                />
                            </div>
                        )}
                    </div>
                    <div className={styles.contentContainer}>
                        <div className={styles.videoContainer}>
                            {!eventEnded ? (
                                <>
                                    {pitchesState.pitches ? (
                                        <VideoPlayer />
                                    ) : (
                                        <div className="d-flex justify-content-center align-items-center w-100 h-100">
                                            <Loader inline loading />
                                        </div>
                                    )}
                                    <InfoBanner type="info">
                                        <div className="d-flex py-3 px-2 align-items-center">
                                            <InfoIcon size={24} />
                                            <p className="m-0 ms-2">
                                                To view live event press play and check that the volume isn't muted
                                            </p>
                                        </div>
                                    </InfoBanner>
                                </>
                            ) : (
                                <BreakoutRooms breakoutExpired={breakoutExpired} breakoutRooms={breakoutRooms} />
                            )}
                        </div>
                        <div className={styles.chatContainer}>
                            <BroadcastChatTabs
                                chatHistory={chatHistory}
                                pusherChannel={pusherChannel}
                                setChatHistory={setChatHistory}
                                newChatMsg={newChatMsg}
                                newBMsg={newBMsg}
                                allowEmojis={true}
                                isVendor={isVendor}
                            />
                        </div>
                    </div>
                    {!isVendor && pusherState.polls && <PollModal eventId={eventId!} />}
                </div>
            </div>
        </div>
    );
}
