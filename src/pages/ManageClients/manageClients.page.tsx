import {
    AddOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EditOutlined,
    EmailOutlined,
    <PERSON><PERSON><PERSON>,
    StoreOutlined,
} from "@mui/icons-material";
import DeleteOutlineOutlined from "@mui/icons-material/DeleteOutlineOutlined";
import InputOutlinedIcon from "@mui/icons-material/InputOutlined";
import PictureAsPdfOutlinedIcon from "@mui/icons-material/PictureAsPdfOutlined";
import ViewListOutlinedIcon from "@mui/icons-material/ViewListOutlined";
import {useTheme} from "@mui/material";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import type {AxiosError} from "axios";
import {useCallback, useEffect, useState} from "react";
import {Link, useNavigate} from "react-router-dom";
import {IndeterminateCheckbox, TableV2Component} from "../../components/Table/TableV2.component";
import {
    DEFAULT_QUERY_CONFIGS,
    MSP_BRAND_TEXT,
    PERMANENT_ACTION_CONFIRMATION,
    ROUTE_CLASS,
    ROUTE_CONTAINER_CLASS,
} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {CustomerPartnershipTypeEnum} from "../../enums/customerPartnershipType.enum";
import {CPSortType} from "../../enums/sortType.enum";
import useFilters from "../../hooks/fetches/useFilters";
import useActiveCompany from "../../hooks/useActiveCompany";
import {useDebounce} from "../../hooks/useDebounce";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IMspClient, ISaveMspClient} from "../../Interfaces/msp.interface";
import {IMeta, IPaginationData} from "../../Interfaces/pagination.interface";
import {mspService} from "../../services/msp.service";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import CustomPagination from "../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import MenuButton from "../../uicomponents/Molecules/MenuButton/menuButton.component";
import InviteClientsFromCSVDialog from "../../uicomponents/Organism/InviteClientsFromCSVModal/InviteClientsFromCSVDialog.component";
import InviteClientsModal from "../../uicomponents/Organism/InviteClientsModal/InviteClients.component";
import ViewEditContractDrawer from "../../uicomponents/Organism/MSPContracts/ViewEditContractDrawer/ViewEditContractDrawer.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import SaveMspClientDialog from "../../uicomponents/Organism/SaveMspClientDialog/saveMspClientDialog.modal";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {downloadDoc} from "../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../utils/error.util";
import {clearFilters} from "../../utils/filters.util";
import {DATE_FORMAT, formatDate} from "../../utils/formatDate";
import {pluralizeString} from "../../utils/formatString.util";
import CustomerContractsPopover from "./customersContracts.component";
import MSPCustomerRegisterLink from "./mspCustomerRegisterLink.component";
import Switch from "../../uicomponents/Atoms/Switch/Switch.component";
import {useMspClients} from "../../hooks/fetches/useMspClients";
import {MutateTypes} from "../../Interfaces/queries.interface";
import Loader from "../../utils/loader";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useIsPlaidAvailable from "../../hooks/useIsPlaidAvailable";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import CustomerPendingInvitesTab from "../../uicomponents/Organism/ManageCustomers/customerPendingInvitesTab.component";

interface IClientKPIs {
    registered_clients: number;
    active_vendors_contracts: number;
    number_of_endpoints: number;
}

export default function ManageClientsPage() {
    const [inviteClientModal, setInviteClientModal] = useState<IMspClient>();
    const [saveClientModal, setSaveClientModal] = useState(false);
    const [clientToEdit, setClientToEdit] = useState<IMspClient>();
    const [search, setSearch] = useState<string>("");
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(20);
    const [isTogglingIntegration, setIsTogglingIntegration] = useState({});
    const {activeCompany, hasIntegrationsAccess} = useActiveCompany();
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const {filterState, filtersQuery} = useFilters({
        filterKey: "mspClients",
        queryParams: {friendlyUrl: activeCompany?.friendly_url},
        defaultFilters: {sort: "sorting_name__ASC"},
        enabled: !!activeCompany,
    });
    const [showImportCSVModal, setShowImportCSVModal] = useState<boolean>(false);
    const [filters, setFilterState] = filterState;
    const debouncedSearch = useDebounce(search, 500);
    const debouncedFilters = useDebounce(filters, 500);
    const theme = useTheme();
    const notify = useNotification();
    const editPermissions = [
        PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_UPDATE,
        PERMISSION_GROUPS.ADMIN_MSP_CLIENTS_MGNMT_READ,
    ];
    const {hasPermissions} = usePermissions();
    const hasPlaidEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const hasPlaidReadAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_READ]);
    const {updateMspClients} = useMspClients(activeCompany?.friendly_url, {
        calls: {
            all: false,
            mspClients: false,
        },
    });
    const {isLoading: isLoadingPlaidEnabled, isAvailable} = useIsPlaidAvailable();
    const isPlaidEnabled = !isLoadingPlaidEnabled && isAvailable;
    const {updateMatchedQueries} = useQueryHelper();

    const clientsQuery = useQuery<IPaginationData<IMspClient[]>>(
        [
            "clients",
            activeCompany?.friendly_url,
            debouncedSearch,
            JSON.stringify(clearFilters(debouncedFilters)),
            itemsPerPage,
            page,
        ],
        async () => {
            const promiseToReturn: Promise<IPaginationData<IMspClient[]>> = new Promise((resolve, reject) =>
                mspService.getClients(
                    activeCompany?.friendly_url!,
                    {
                        items_per_page: itemsPerPage,
                        page,
                        dynamic: clearFilters(debouncedFilters),
                        search_word: debouncedSearch,
                    },
                    r => {
                        resolve(r.data);
                    },
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            enabled: !!activeCompany?.friendly_url,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true, onWindowFocus: true}}),
        },
    );

    const KPIsQuery = useQuery<IClientKPIs>(
        [
            "clients-kpis",
            activeCompany?.friendly_url,
            debouncedSearch,
            JSON.stringify(clearFilters(debouncedFilters)),
            page,
        ],
        async () => {
            const promiseToReturn: Promise<IClientKPIs> = new Promise((resolve, reject) =>
                mspService.getClientKPIs(activeCompany?.friendly_url!, r => resolve(r.data), reject),
            );
            return promiseToReturn;
        },
        {
            enabled: !!activeCompany?.friendly_url,
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true, onWindowFocus: true}}),
        },
    );
    const tableData: IMspClient[] = clientsQuery.data?.data ?? ([] as IMspClient[]);
    const metadata: IMeta = clientsQuery.data?.meta ?? ({} as IMeta);
    const totalClients = KPIsQuery.data?.registered_clients ?? 0;
    const totalEndpoints = KPIsQuery.data?.number_of_endpoints ?? 0;
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    const onEditClick = (item: IMspClient) => {
        setClientToEdit(item);
        setSaveClientModal(true);
    };

    const onDeleteClick = async (client: IMspClient) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Selected Item?",
            justifyButtons: "flex-start",
            okButtonText: "Delete",
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" flexDirection="row" gap={2} marginBottom={1}>
                        <Typography fontInter variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                            {client.name}
                        </Typography>
                    </Box>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (answer.value) {
            mspService
                .deleteClient(activeCompany?.friendly_url!, client.id)
                .then(async () => {
                    KPIsQuery.refetch();
                    await updateMatchedQueries(
                        ["clients", activeCompany?.friendly_url],
                        (old: IPaginationData<IMspClient[]>) => {
                            if (!old) return old;
                            return {
                                ...old,
                                data: old.data.filter(c => c.id !== client.id),
                            };
                        },
                    );
                    notify(`${MSP_BRAND_TEXT.CUSTOMER} deleted successfully`, "Success");
                })
                .catch(err => {
                    notify(getErrorFromArray(err), "Error");
                });
        }
    };

    const handleDeleteMultipleClick = async () => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: `Delete Selected ${pluralizeString("Item", selectedRows.length)}?`,
            justifyButtons: "flex-start",
            okButtonText: "Delete",
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" flexDirection="row" gap={2} marginBottom={1}>
                        <Typography fontInter variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                            {selectedRows.length} customers selected.
                        </Typography>
                    </Box>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (answer.value) {
            mspService
                .deleteMultipleClients(activeCompany?.friendly_url!, selectedRows)
                .then(async () => {
                    KPIsQuery.refetch();
                    await updateMatchedQueries(
                        ["clients", activeCompany?.friendly_url],
                        (old: IPaginationData<IMspClient[]>) => {
                            if (!old) return old;
                            return {
                                ...old,
                                data: old.data.filter(c => !selectedRows.includes(c.id)),
                            };
                        },
                    );
                    notify(
                        `${pluralizeString(MSP_BRAND_TEXT.CUSTOMER, selectedRows.length)} deleted successfully`,
                        "Success",
                    );
                    setSelectedRows([]);
                })
                .catch(err => {
                    notify(getErrorFromArray(err), "Error");
                });
        }
    };

    const handleExportCSV = () => {
        mspService.downloadClientsCSV(activeCompany?.friendly_url!).then(res => {
            downloadDoc(res.data, (activeCompany?.name.replaceAll(" ", "_") ?? "") + "_Customers" + ".csv");
        });
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleExportPDF = () => {
        mspService.downloadClientsPDF(activeCompany?.friendly_url!).then(res => {
            downloadDoc(res.data, (activeCompany?.name.replaceAll(" ", "_") ?? "") + "_Customers" + ".pdf");
        }, handleError);
    };

    const handleSortChange = (sortBy: string, order_by: string) => {
        setFilterState({...filters, sort: `sorting_${order_by}__${sortBy}`});
    };

    const renderThreeDotIcon = (client: IMspClient) => {
        return (
            <MenuButton
                id={`more-actions-${client.id}`}
                buttonVariant="iconButton"
                buttonProps={{
                    sx: {
                        width: "24px",
                        minWidth: "24px!important",
                        height: "24px",
                        boxShadow: "none",
                        flexShrink: 0,
                        position: "relative",
                        zIndex: 10,
                    },
                }}
                items={[
                    {
                        id: "edit-info",
                        children: (
                            <>
                                <EditOutlined sx={{height: 24, width: 24}} /> Edit Information
                            </>
                        ),
                        onClick: () => onEditClick(client),
                        permissions: editPermissions,
                    },
                    {
                        id: "invite-user",
                        children: (
                            <>
                                <EmailOutlined sx={{height: 24, width: 24}} /> Invite User
                            </>
                        ),
                        onClick: () => setInviteClientModal(client),
                        permissions: editPermissions,
                    },
                    {
                        id: "delete-client",
                        children: (
                            <>
                                <DeleteOutlined sx={{height: 24, width: 24}} color="error" />
                                <Typography color="error" variant="body4">
                                    Delete {MSP_BRAND_TEXT.CUSTOMER}
                                </Typography>
                            </>
                        ),
                        onClick: () => onDeleteClick(client),
                        permissions: editPermissions,
                    },
                ]}
                aria-label="settings">
                <MoreVert color="neutral" />
            </MenuButton>
        );
    };

    const [contractDialogConfig, setContractDialogConfig] = useState<{
        companyId: string;
        contract_id: string;
        mode: "READ" | "EDIT";
    }>({
        companyId: "",
        contract_id: "",
        mode: "READ",
    });
    const handleViewCustomerContract = (companyId: string, contractId: string) => {
        setContractDialogConfig({
            companyId,
            contract_id: contractId,
            mode: "READ",
        });
    };
    const handleCloseCustomerContract = () => {
        setContractDialogConfig({
            companyId: "",
            contract_id: "",
            mode: "READ",
        });
    };

    const handleToggleRow = (rowData: any) => {
        const selectedRow = selectedRows.includes(rowData.id);
        if (selectedRow) {
            setSelectedRows(selectedRows.filter(row => row !== rowData.id));
        } else {
            setSelectedRows([...selectedRows, rowData.id]);
        }
    };

    const toggleIntegrationEnabled = useCallback(
        (company_id: string, enabled: boolean) => {
            setIsTogglingIntegration({...isTogglingIntegration, [company_id]: true});
            const requestBody: ISaveMspClient | any = {
                id: company_id,
                plaid_integration_enabled: enabled,
            };
            updateMspClients.mutate({
                mutate_type: MutateTypes.UpdateMspClient,
                body: requestBody,
                onSuccess: async () => {
                    await updateMatchedQueries(
                        ["clients", activeCompany?.friendly_url],
                        (old: IPaginationData<IMspClient[]>) => {
                            if (!old) return old;
                            return {
                                ...old,
                                data: old.data.map((client: IMspClient) => {
                                    if (client.id === company_id) {
                                        return {
                                            ...client,
                                            plaid_integration_enabled: enabled,
                                        };
                                    }
                                    return client;
                                }),
                            };
                        },
                    );
                },
                onSettled: () => {
                    setIsTogglingIntegration({...isTogglingIntegration, [company_id]: false});
                },
            });
        },
        [isTogglingIntegration],
    );

    const plaidIntegrationCell = useCallback(
        ({row}) => (
            <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                {isTogglingIntegration?.[row.original.id] ? (
                    <Loader inline loading />
                ) : (
                    <>
                        <Switch
                            checked={!!row.original.plaid_integration_enabled}
                            onChange={() => {
                                toggleIntegrationEnabled(row.original.id, !row.original.plaid_integration_enabled);
                            }}
                            disabled={!hasPlaidEditAccess}
                        />{" "}
                        {row.original.plaid_integration_enabled ? "On" : "Off"}
                    </>
                )}
            </Box>
        ),
        [isTogglingIntegration, hasPlaidEditAccess],
    );

    const columns = [
        {
            Header: useCallback(
                header => {
                    if (!hasPermissions(editPermissions)) return null;
                    return (
                        <IndeterminateCheckbox
                            indeterminate={
                                selectedRows && selectedRows.length > 0 && selectedRows.length < header.rows?.length
                            }
                            checked={!!selectedRows?.length}
                            onChange={() => {
                                if (!setSelectedRows) return;
                                if (!!selectedRows?.length) {
                                    setSelectedRows(() => {
                                        return [];
                                    });
                                } else {
                                    setSelectedRows(old => {
                                        return [...old, ...header.rows?.map((row: any) => row.original.id)];
                                    });
                                }
                            }}
                        />
                    );
                },
                [selectedRows],
            ),
            accessor: "action",
            Cell: useCallback(
                ({row}) => {
                    if (!hasPermissions(editPermissions)) return null;
                    return (
                        <IndeterminateCheckbox
                            indeterminate={false}
                            checked={selectedRows?.includes(row.original?.id)}
                            onChange={() => handleToggleRow(row.original)}
                        />
                    );
                },
                // eslint-disable-next-line
                [selectedRows],
            ),
        },
        {
            Header: "Name",
            accessor: "name",
            Cell: ({row}: any) => (
                <Link to={routeConfig.MspClientProfile.path.replace(":friendly_url", row.original.friendly_url)}>
                    <Typography
                        fontInter
                        sx={{
                            color: "blue.600",
                            fontSize: "14px!important",
                            fontWeight: "600!important",
                        }}>
                        {row.original.name}
                    </Typography>
                </Link>
            ),
            sortable: true,
        },
        {
            Header: "Location",
            accessor: "address",
            sortable: true,
        },
        {
            Header: "Start Date",
            accessor: "created_at",
            sortable: true,
            Cell: tableData => formatDate(tableData.row.original.created_at, DATE_FORMAT),
        },
        ...(isPlaidEnabled && (hasPlaidEditAccess || hasPlaidReadAccess)
            ? [
                  {
                      Header: "Plaid Integration",
                      accessor: "plaid_integration_enabled",
                      Cell: plaidIntegrationCell,
                  },
              ]
            : []),
        {
            Header: "Partnership Type",
            accessor: "partnership_type",
            sortable: true,
            Cell: ({row}: any) =>
                row.original.partnership_type_name == null ? (
                    "--"
                ) : (
                    <Badge
                        color={
                            row.original.partnership_type_name == CustomerPartnershipTypeEnum.CURRENT_PARTNER
                                ? "success"
                                : row.original.partnership_type_name == CustomerPartnershipTypeEnum.PROSPECT
                                  ? "warning"
                                  : "secondary"
                        }
                        sx={{
                            textTransform: "capitalize",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                        }}
                        style={{
                            maxWidth: "100%",
                            overflow: "hidden",
                            justifyContent: "flex-start",
                        }}
                        title={row.original.partnership_type_name}>
                        {row.original.partnership_type_name}
                    </Badge>
                ),
        },
        {
            Header: "Vendor Contracts",
            accessor: "contracts_count",
            sortable: true,
            Cell: ({row}: any) => {
                const hasContracts = (row.original.contracts_count ?? 0) > 0;
                const hasRecentContracts = (row.original.recent_contracts ?? 0) > 0;
                if (!hasContracts) {
                    return <Typography fontInter>{row.original.contracts_count}</Typography>;
                }
                return (
                    <CustomerContractsPopover
                        mspFriendlyUrl={activeCompany?.friendly_url ?? ""}
                        onContractClick={handleViewCustomerContract}
                        label={row.original.contracts_count}
                        companyId={row.original.id}
                        showNewTag={hasRecentContracts}
                    />
                );
            },
        },
        {
            Header: "Amount of Endpoints",
            accessor: "number_of_endpoints",
            sortable: true,
        },
        {
            Header: "# of NaviStack Products",
            accessor: "stack_count",
            sortable: true,
        },
        {
            Header: "Actions",
            id: "actions",
            Cell: ({row}: any) => renderThreeDotIcon(row.original),
        },
    ];

    useEffect(() => {
        queryClient.invalidateQueries(["active-company-api"]);
    }, []);

    useEffect(() => {
        if (activeCompany && !activeCompany.manage_clients) {
            navigate(routeConfig.Home.path);
        }
    }, [activeCompany?.manage_clients]);

    return (
        <>
            <div className={ROUTE_CLASS}>
                <div className={ROUTE_CONTAINER_CLASS}>
                    <PageInnerHeader
                        id="manage-clients-header"
                        title={{
                            text: "Manage Your Customers",
                        }}
                        actions={{
                            secondary: {
                                id: "export-client",
                                children: (
                                    <>
                                        <DownloadOutlined color="secondary" sx={{width: "18px", height: "18px"}} />
                                        Export
                                    </>
                                ),
                                items: [
                                    {
                                        id: "export-client-csv",
                                        children: (
                                            <Box display="flex" alignItems="center" gap="16px">
                                                <ViewListOutlinedIcon color="neutral" />
                                                <Typography
                                                    variant="subtitle4"
                                                    fontInter
                                                    fontWeight={400}
                                                    sx={theme => ({
                                                        color: theme.palette.secondary.main,
                                                    })}>
                                                    Download .CSV
                                                </Typography>
                                            </Box>
                                        ),
                                        onClick: handleExportCSV,
                                    },
                                    {
                                        id: "export-client-pdf",
                                        children: (
                                            <Box display="flex" alignItems="center" gap="16px">
                                                <PictureAsPdfOutlinedIcon color="neutral" />
                                                <Typography
                                                    variant="subtitle4"
                                                    fontInter
                                                    fontWeight={400}
                                                    sx={theme => ({
                                                        color: theme.palette.secondary.main,
                                                    })}>
                                                    Export PDF
                                                </Typography>
                                            </Box>
                                        ),
                                        onClick: handleExportPDF,
                                    },
                                ],
                            },
                        }}
                    />
                    <Typography
                        variant="body3"
                        fontInter
                        sx={{
                            color: "neutral.800",
                            fontSize: "16px",
                            fontWeight: "400!important",
                        }}>
                        Start enhancing your stack by adding your customers. This enables you to identify products
                        rolled out to customers, understand technology gaps, and effectively track customer expenses
                        using Vendor Contract Manager.
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 3,
                            flexWrap: "wrap",
                            "@media (min-width: 768px)": {
                                flexWrap: "nowrap",
                            },
                        }}>
                        <Box
                            sx={{
                                padding: 2,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 2,
                                flexGrow: 1,
                                maxWidth: "100%",
                                border: "1px solid",
                                borderColor: "neutral.300",
                                borderRadius: 1,
                                "@media (min-width: 768px)": {
                                    maxWidth: "calc(50% - 14px)",
                                },
                            }}>
                            <StoreOutlined color="neutral" sx={{width: "32px", height: "32px"}} />
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "32px!important",
                                    fontWeight: "600!important",
                                }}>
                                {totalClients ?? 0}
                            </Typography>
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "16px!important",
                                    fontWeight: "500!important",
                                    lineHeight: "17.6px!important",
                                }}>
                                Registered
                                <br />
                                {pluralizeString("Customer", totalClients)}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                padding: 2,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 2,
                                flexGrow: 1,
                                maxWidth: "100%",
                                border: "1px solid",
                                borderColor: "neutral.300",
                                borderRadius: 1,
                                "@media (min-width: 768px)": {
                                    maxWidth: "calc(50% - 14px)",
                                },
                            }}>
                            <InputOutlinedIcon color="neutral" sx={{width: "32px", height: "32px"}} />
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "32px!important",
                                    fontWeight: "600!important",
                                }}>
                                {totalEndpoints ?? 0}
                            </Typography>
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "16px!important",
                                    fontWeight: "500!important",
                                    lineHeight: "17.6px!important",
                                }}>
                                Total
                                <br />
                                {pluralizeString("Endpoint", totalEndpoints)}
                            </Typography>
                        </Box>
                        <MSPCustomerRegisterLink friendlyUrl={activeCompany?.friendly_url} />
                    </Box>
                    <BasicTabs
                        hideBorder
                        tabsSx={{paddingY: 3}}
                        tabs={[
                            {
                                label: MSP_BRAND_TEXT.CUSTOMERS,
                                id: "customers",
                                Component: (
                                    <>
                                        <PageInnerHeader
                                            id="client-table-header"
                                            title={{
                                                text: MSP_BRAND_TEXT.CUSTOMERS,
                                            }}
                                            indicator={totalClients ?? undefined}
                                            filterState={filterState}
                                            filters={filtersQuery.data}
                                            searchState={[search, setSearch]}
                                            fullWidthSearch
                                            searchWrapperSx={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                gap: "24px!important",
                                            }}
                                            filtersWrapperSx={{
                                                flexDirection: "row",
                                                flexWrap: "nowrap",
                                            }}
                                            actions={{
                                                secondary: {
                                                    id: "add-client",
                                                    children: (
                                                        <>
                                                            <AddOutlined
                                                                color="secondary"
                                                                sx={{width: "18px", height: "18px"}}
                                                            />
                                                            {MSP_BRAND_TEXT.ADD_CUSTOMER}
                                                        </>
                                                    ),
                                                    onClick: () => {
                                                        setSaveClientModal(true);
                                                    },
                                                    permissions: editPermissions,
                                                },
                                            }}
                                        />
                                        <Box>
                                            {!!selectedRows.length && (
                                                <Box sx={{display: "flex", gap: "24px", paddingBottom: "6px"}}>
                                                    <Typography
                                                        fontInter
                                                        sx={{
                                                            flex: 1,
                                                            display: "inline-flex",
                                                            alignItems: "center",
                                                            backgroundColor: theme.palette.neutral[200],
                                                            borderRadius: "8px",
                                                            padding: "8px",
                                                            fontWeight: 600,
                                                            fontSize: "16px",
                                                        }}>
                                                        {`${selectedRows.length} ${pluralizeString(
                                                            "customer",
                                                            selectedRows.length,
                                                        )} selected`}
                                                    </Typography>
                                                    <Button
                                                        id="btn-delete-customers"
                                                        variant="outlined"
                                                        disabled={!selectedRows.length}
                                                        onClick={() => handleDeleteMultipleClick()}
                                                        sx={{
                                                            borderColor: theme.palette.error[600],
                                                            color: theme.palette.error[700],
                                                            gap: "8px",
                                                            "& svg": {
                                                                width: "18px",
                                                                height: "18px",
                                                            },
                                                        }}>
                                                        <DeleteOutlineOutlined />
                                                        Delete All
                                                    </Button>
                                                </Box>
                                            )}
                                            <TableV2Component
                                                id={"clients"}
                                                columns={columns as any}
                                                pinColumns={["actions"]}
                                                hiddenColumns={[]}
                                                customData={tableData}
                                                isLoading={clientsQuery?.isLoading || clientsQuery?.isFetching}
                                                headerBackground={theme.palette.blue[100]}
                                                noResultsMessage="No customers found."
                                                sortType={CPSortType.ASC}
                                                orderBy="name"
                                                onSortChange={(sort: string, order_by: string) =>
                                                    handleSortChange(sort, order_by)
                                                }
                                            />
                                        </Box>
                                        <CustomPagination
                                            metadata={metadata}
                                            page={page}
                                            itemsPerPage={itemsPerPage}
                                            setPage={setPage}
                                            setItemsPerPage={setItemsPerPage}
                                        />
                                    </>
                                ),
                            },
                            {
                                label: "Pending Invitations",
                                id: "pending-invites",
                                Component: <CustomerPendingInvitesTab />,
                            },
                        ]}
                    />
                </div>
            </div>
            {activeCompany?.friendly_url && saveClientModal && (
                <SaveMspClientDialog
                    friendlyUrl={activeCompany?.friendly_url}
                    open={saveClientModal}
                    onClose={hasChanges => {
                        setSaveClientModal(false);
                        setClientToEdit(undefined);
                        if (hasChanges) {
                            KPIsQuery.refetch();
                            clientsQuery.refetch();
                        }
                    }}
                    clientToEdit={clientToEdit}
                    onImportCSVClick={() => setShowImportCSVModal(true)}
                />
            )}
            {!!inviteClientModal && (
                <InviteClientsModal
                    open={!!inviteClientModal}
                    handleClose={() => setInviteClientModal(undefined)}
                    friendlyUrl={activeCompany?.friendly_url ?? ""}
                    client={inviteClientModal}
                />
            )}
            {showImportCSVModal && (
                <InviteClientsFromCSVDialog
                    friendlyURL={activeCompany?.friendly_url ?? ""}
                    open={showImportCSVModal}
                    onClose={() => setShowImportCSVModal(false)}
                    onVendorsImported={() => {
                        KPIsQuery.refetch();
                        clientsQuery.refetch();
                        setSaveClientModal(false);
                    }}
                />
            )}
            {!!contractDialogConfig?.contract_id && !!contractDialogConfig?.companyId && (
                <ViewEditContractDrawer
                    open
                    onClose={handleCloseCustomerContract}
                    isClientMsp={true}
                    {...contractDialogConfig}
                />
            )}
        </>
    );
}
