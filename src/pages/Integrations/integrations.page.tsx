import {memo, useEffect, useState} from "react";
import {<PERSON>, useNavigate} from "react-router-dom";
import {CP_HELP_URL, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useActiveCompany from "../../hooks/useActiveCompany";
import {useFeatureFlags} from "../../hooks/useFeatureFlags";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {cyclrService} from "../../services/cyclr.service";
import {colorPalette} from "../../themes/themes.constant";
import BuilderIntegrationsAlertBanner from "../../uicomponents/Molecules/AlertBanner/integrationsAlertBanner.builder";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import Loader from "../../utils/loader";
import IntegrationsDrawer from "../../uicomponents/Molecules/Integrations/IntegrationsDrawer/IntegrationsDrawer.component";
import {DIRECT_PLAN_TYPES} from "../../constants/profiles.constant";

const _INFO_MESSAGE = `<p>Do you use tools for managing customers, contracts, or other resources? Integrating these tools with the Channel Program can effectively eliminate duplicate data entry. Channel Program utilizes Cyclr for seamless integration, ensuring that data syncs one-way from your tools into the Channel Program. </p>  &nbsp; <p>For more information, refer to <a href='${CP_HELP_URL}/about-integrating-with-channel-program' target='_blank' rel='nofollow'>About integrating with Channel Program.</a></p>`;

const Integrations = memo(() => {
    const {isFlagActivated, isLoadingFeatureFlags} = useFeatureFlags();
    const {activeCompany, isLoading: isLoadingActiveCompany, isVendor} = useActiveCompany();
    const navigate = useNavigate();
    const isCylrIntegrationEnabled = isFlagActivated("CYCLR_INTEGRATION");
    const {hasPermissions} = usePermissions();
    const hasIntegrationsUpdate = hasPermissions([PERMISSION_GROUPS.INTEGRATIONS_UPDATE]);
    const hasIntegrationsRead =
        hasPermissions([PERMISSION_GROUPS.INTEGRATIONS_READ]) &&
        (isVendor ||
            activeCompany?.manage_clients ||
            activeCompany?.manage_affiliates ||
            activeCompany?.company_profile_type.value == DIRECT_PLAN_TYPES.DIRECT_PREMIUM);

    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [iframeUrl, setIframeUrl] = useState<string>("");
    const integrationsRead = [PERMISSION_GROUPS.INTEGRATIONS_READ];
    const [drawer, setDrawer] = useState<boolean>(false);

    const handleOpenCloseDrawer = () => {
        setDrawer(!drawer);
    };

    useEffect(() => {
        if (!isLoadingFeatureFlags && (!isCylrIntegrationEnabled || !hasIntegrationsUpdate)) {
            navigate(routeConfig.Home.path);
            return;
        }
    }, [isLoadingActiveCompany, isLoadingFeatureFlags]);

    /*
     ** We use useEffect to fetch the API
     ** because we need a new URL every time the page is loaded
     ** therefore we can't use react-query here
     */
    useEffect(() => {
        if (!activeCompany || !hasIntegrationsRead || iframeUrl) {
            return;
        }

        cyclrService
            .getEmbedUrl(activeCompany.id)
            .then(r => {
                setIframeUrl(r.data.embed_url);
                setIsLoading(false);
            })
            .catch(e => {
                console.error("Error getting iframe", e);
            });
    }, [isLoadingActiveCompany, iframeUrl]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <PageInnerHeader
                    id="integrations"
                    title={{
                        text: "Integrations",
                    }}
                    actions={
                        hasIntegrationsUpdate &&
                        (activeCompany?.company_profile_type.value != DIRECT_PLAN_TYPES.DIRECT_PREMIUM
                            ? !activeCompany?.type_is_of_vendor &&
                              activeCompany?.company_profile_type.value != DIRECT_PLAN_TYPES.DIRECT_BASIC
                            : true)
                            ? {
                                  primary: {
                                      id: "map-integrations",
                                      children: <>Map Integration Fields</>,
                                      onClick: handleOpenCloseDrawer,
                                      permissions: integrationsRead,
                                  },
                              }
                            : undefined
                    }
                />
                <BuilderIntegrationsAlertBanner />
                {hasIntegrationsRead && (
                    <>
                        <Loader big inline loading={isLoading && !iframeUrl} />
                        <iframe style={{height: "100vw"}} src={iframeUrl} />
                    </>
                )}
                {!hasIntegrationsRead && !isLoadingActiveCompany && (
                    <Link to={routeConfig.Contact.path}>
                        <div style={{position: "relative"}}>
                            <img
                                src={"/Media/integrations/placeholder2.png"}
                                alt={"integrations placeholder"}
                                style={{width: "100%"}}
                            />
                        </div>
                    </Link>
                )}
                <IntegrationsDrawer open={drawer} onClose={handleOpenCloseDrawer} />
            </div>
        </div>
    );
});

export default Integrations;
