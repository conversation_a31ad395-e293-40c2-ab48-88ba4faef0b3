import {CancelOutlined, Check, Close, DoneAllOutlined} from "@mui/icons-material";
import {useTheme} from "@mui/material";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {ReactNode, useCallback, useState} from "react";
import type {Column} from "react-table";
import {TableV2Component} from "../../components/Table/TableV2.component";
import {DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import CompanyType from "../../constants/companyType.constant";
import useActiveCompany from "../../hooks/useActiveCompany";
import {useDebounce} from "../../hooks/useDebounce";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {INewFilters} from "../../Interfaces/filters.interface";
import type {IPaginationData} from "../../Interfaces/pagination.interface";
import type {
    IPartnerInvite,
    TConfirmationWithData,
    TVendorConfirmationModalTypes,
} from "../../Interfaces/partnerPage.interface";
import {PartnerPageService} from "../../services/partnerpage.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import Checkbox from "../../uicomponents/Atoms/Checkbox/Checkbox.component";
import IconButton from "../../uicomponents/Atoms/IconButton/IconButton.Component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../uicomponents/Molecules/Avatar/avatarV2.component";
import CustomPagination from "../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import VendorConfirmationModal from "../../uicomponents/Organism/VendorConfirmationModal";
import {getErrorFromArray} from "../../utils/error.util";
import {clearFilters} from "../../utils/filters.util";
import {formatDate, SHORT_MONTH_DATE_AT_TIME} from "../../utils/formatDate";
import {pluralizeString} from "../../utils/formatString.util";
import Loader from "../../utils/loader";

export default function PendingRequestsTab() {
    const [search, setSearch] = useState<string>("");
    const [filters, setFilters] = useState<INewFilters>({sort: "sorting_name__ASC"});
    const [items_per_page, setItemsPerPage] = useState<number>(40);
    const [page, setPage] = useState<number>(1);
    const [selectedRequests, setSelectedRequests] = useState<IPartnerInvite[]>([]);
    const [acceptingRequest, setAcceptingRequest] = useState<Record<string, boolean>>({});
    const [decliningRequest, setDecliningRequest] = useState<Record<string, boolean>>({});
    const [showConfirmationModal, setShowConfirmationModal] = useState<TConfirmationWithData<IPartnerInvite[]>>();
    const debouncedSearch = useDebounce(search, 500);
    const {activeCompany} = useActiveCompany();
    const notify = useNotification();
    const theme = useTheme();
    const queryClient = useQueryClient();

    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_CONNECTIONS_UPDATE]);
    const renderButton = (invites: IPartnerInvite[], accepting?: boolean): ReactNode => {
        const word = accepting ? "Accept" : "Decline";
        return (
            <Typography
                component={"div"}
                fontInter
                weight={400}
                size={14}
                lineHeight={1}
                sx={{
                    textTransform: "uppercase",
                    display: "flex",
                    alignItems: "center",
                }}>
                {accepting ? (
                    <DoneAllOutlined htmlColor="white" sx={{marginRight: 1, width: "18px", height: "18px"}} />
                ) : (
                    <CancelOutlined htmlColor="white" sx={{marginRight: 1, width: "18px", height: "18px"}} />
                )}
                {word}
                {invites.length > 1 ? " All" : ""}
            </Typography>
        );
    };
    const okButtonText =
        showConfirmationModal?.type === "Accept"
            ? renderButton(selectedRequests, true)
            : renderButton(selectedRequests);

    const requestsQuery = useQuery<IPaginationData<IPartnerInvite[]>>(
        ["partnerRequests", activeCompany?.id, debouncedSearch, JSON.stringify(clearFilters(filters))],
        () => {
            const promiseToReturn: Promise<IPaginationData<IPartnerInvite[]>> = new Promise((resolve, reject) =>
                PartnerPageService.loadPartnerInvites(activeCompany?.id || "", {
                    page,
                    items_per_page,
                    dynamic: {status: ["requested"], ...clearFilters(filters)},
                    search_word: debouncedSearch,
                })
                    .then((r: AxiosResponse<IPaginationData<IPartnerInvite[]>>) => {
                        setSelectedRequests([]);
                        resolve(r.data);
                    })
                    .catch((e: AxiosError) => {
                        notify(getErrorFromArray(e), "Error");
                        reject(e);
                    }),
            );
            return promiseToReturn;
        },
        {
            enabled: !!activeCompany?.id,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const allRequests = requestsQuery.data?.data || [];
    const allRequestsIds = allRequests.map(inv => inv.id);
    const metadata = requestsQuery.data?.meta || {total: 0, last_page: 0};

    const handleSuccess = (
        data: {reason: string; other: string; value: IPartnerInvite[]},
        type: TVendorConfirmationModalTypes,
    ) => {
        switch (type) {
            case "Accept":
                acceptRequest(data.value, data);
                break;
            default:
                declineRequest(data.value, data);
                break;
        }
    };

    const acceptRequest = (requests: IPartnerInvite[], data: {reason: string; other: string}) => {
        setShowConfirmationModal(undefined);
        setAcceptingRequest(requests.reduce((acc, inv) => ({...acc, [inv.id]: true}), {} as Record<string, boolean>));
        PartnerPageService.acceptMultiplePartnerRequests(
            activeCompany?.id || "",
            requests.map(req => req.id),
            data?.reason,
            data?.other,
        )
            .then(() => {
                notify(
                    `Successfully accepted ${
                        requests.length > 1
                            ? `${requests.length} requests`
                            : `request for ${requests[0].invited_by.name}`
                    }`,
                    "Success",
                );
                queryClient.invalidateQueries(["partnerRequests"]);
                queryClient.refetchQueries(["accessManagementCounts"]);
            })
            .finally(() => {
                setAcceptingRequest({});
            });
    };

    const declineRequest = (requests: IPartnerInvite[], data: {reason: string; other: string}) => {
        setShowConfirmationModal(undefined);
        setDecliningRequest(requests.reduce((acc, inv) => ({...acc, [inv.id]: true}), {} as Record<string, boolean>));
        PartnerPageService.declinePartnerInvite(
            activeCompany?.id || "",
            false,
            requests.map(req => req.id),
            data?.reason,
            data?.other,
        )
            .then(() => {
                notify(
                    `Successfully declined ${
                        requests.length > 1 ? `${requests.length} invites` : `invite for ${requests[0].invited_by.name}`
                    }`,
                    "Success",
                );
                queryClient.invalidateQueries(["partnerRequests"]);
                queryClient.refetchQueries(["accessManagementCounts"]);
            })
            .catch((errors: AxiosError[]) => {
                errors.forEach(e => notify(getErrorFromArray(e), "Error"));
            })
            .finally(() => {
                setDecliningRequest({});
            });
    };

    const ActionsCell = useCallback(
        (tableData, action: "accept" | "decline") => {
            return tableData?.row?.original ? (
                <Box sx={{display: "flex", justifyContent: "center", width: "100%"}}>
                    {!!(action === "accept" ? acceptingRequest : decliningRequest)[tableData.row.original.id] ? (
                        <Loader inline loading version="iconBlue" />
                    ) : action === "accept" ? (
                        <IconButton
                            id={"accept-request-" + tableData.row.original?.id}
                            color="success"
                            onClick={() =>
                                setShowConfirmationModal({
                                    show: true,
                                    type: "Accept",
                                    data: [tableData.row.original],
                                })
                            }
                            tooltipTitle="Accept Request"
                            disabled={!hasEditAccess || acceptingRequest[tableData.row.original.id]}
                            sx={{
                                "&:hover": {
                                    backgroundColor: `${theme.palette.success[100]}!important`,
                                    borderColor: theme.palette.success[600],
                                },
                            }}>
                            <Check color="success" sx={{width: "24px!important", height: "24px!important"}} />
                        </IconButton>
                    ) : (
                        <IconButton
                            id={"reject-request-" + tableData.row.original?.id}
                            color="error"
                            onClick={() =>
                                setShowConfirmationModal({
                                    show: true,
                                    type: "Reject",
                                    data: [tableData.row.original],
                                })
                            }
                            tooltipTitle="Decline Request"
                            disabled={!hasEditAccess || decliningRequest[tableData.row.original.id]}
                            sx={{
                                "&:hover": {
                                    backgroundColor: `${theme.palette.error[100]}!important`,
                                    borderColor: theme.palette.error[600],
                                },
                            }}>
                            <Close color="error" sx={{width: "24px!important", height: "24px!important"}} />
                        </IconButton>
                    )}
                </Box>
            ) : null;
        },
        [acceptingRequest, decliningRequest, allRequests],
    );

    const columns: Column<IPartnerInvite>[] = [
        ...(hasEditAccess
            ? [
                  {
                      id: "select-invite",
                      Header: () => (
                          <Checkbox
                              checked={!!selectedRequests.length && selectedRequests.length === allRequestsIds.length}
                              onChange={(_, checked) =>
                                  checked ? setSelectedRequests(allRequests) : setSelectedRequests([])
                              }
                          />
                      ),
                      Cell: tableData => (
                          <Checkbox
                              checked={!!selectedRequests.find(req => req.id === tableData.row.original.id)}
                              onChange={(_, checked) =>
                                  setSelectedRequests(
                                      checked
                                          ? [...selectedRequests, tableData.row.original]
                                          : selectedRequests.filter(req => req.id !== tableData.row.original.id),
                                  )
                              }
                          />
                      ),
                  },
              ]
            : []),
        {
            id: "name",
            Header: "Name",
            accessor: "invited_by.name" as any,
            defaultCanSort: true,
        },
        {
            id: "company_name",
            Header: "Company",
            accessor: "follower_partner.name" as any,
            defaultCanSort: true,
            Cell: tableData => {
                const company = tableData.row.original.follower_partner;
                if (!company) return null;
                return (
                    <Box sx={{display: "flex", alignItems: "center", gap: 1.25}}>
                        <AvatarComponentV2
                            isCompany
                            user={{
                                avatar: company.avatar ?? "",
                                type: company.type.value ?? "",
                                profile_type: company.type_is_of_vendor ? "vendor" : "msp",
                                friendly_url: company.friendly_url ?? "",
                                name: company.name ?? "",
                                is_distributor: company.is_distributor ?? false,
                            }}
                            size={40}
                            showProfileType={company.company_type !== CompanyType.MSP_CLIENT}
                            profileTypeMinimized={company.company_type !== CompanyType.MSP_CLIENT}
                            disableLink
                        />
                        <Typography fontSize={14} fontWeight={400} color={theme.palette.neutral[800]}>
                            {company.name}
                        </Typography>
                    </Box>
                );
            },
        },
        {
            id: "invited_at",
            Header: "Submitted at",
            accessor: "invited_at",
            defaultCanSort: true,
            Cell: tableData => formatDate(tableData.row.original.invited_at, SHORT_MONTH_DATE_AT_TIME, true),
        },
        {
            id: "accept",
            Header: <span style={{margin: "0 auto"}}>Accept</span>,
            Cell: tableData => ActionsCell(tableData, "accept"),
        },
        {
            id: "decline",
            Header: <span style={{margin: "0 auto"}}>Decline</span>,
            Cell: tableData => ActionsCell(tableData, "decline"),
        },
    ];

    const handleSortChange = (sortBy: string, order_by: string) => {
        setFilters({...filters, sort: `sorting_${order_by}__${sortBy}`});
    };

    return (
        <>
            <Box sx={{display: "flex", flexDirection: "column", gap: 2}}>
                <PageInnerHeader id="requests-search" searchState={[search, setSearch]} growSearch />
                {!!selectedRequests.length && (
                    <Box sx={{display: "flex", gap: 1, width: "100%"}}>
                        <Box
                            sx={{
                                bgcolor: "neutral.200",
                                paddingX: 1,
                                borderRadius: 2,
                                flexGrow: 1,
                                display: "flex",
                                alignItems: "center",
                            }}>
                            <Typography fontWeight={600} fontSize={16} sx={{color: "neutral.700"}}>
                                {selectedRequests.length} {pluralizeString("request", selectedRequests.length)} selected
                            </Typography>
                        </Box>
                        <Button
                            id="accept-many"
                            type="button"
                            variant="outlined"
                            color="secondary"
                            onClick={() =>
                                setShowConfirmationModal({
                                    show: true,
                                    type: "Accept",
                                    data: selectedRequests,
                                })
                            }>
                            <DoneAllOutlined color="secondary" sx={{marginRight: 1, width: "16px", height: "16px"}} />
                            Accept All
                        </Button>
                        <Button
                            id="delete-many"
                            type="button"
                            variant="outlined"
                            color="error"
                            onClick={() =>
                                setShowConfirmationModal({
                                    show: true,
                                    type: "Reject",
                                    data: selectedRequests,
                                })
                            }>
                            <CancelOutlined color="error" sx={{marginRight: 1, width: "16px", height: "16px"}} />
                            Decline All
                        </Button>
                    </Box>
                )}
                <TableV2Component
                    id="receivedRequests"
                    columns={columns}
                    customData={allRequests}
                    isLoading={requestsQuery.isFetching}
                    headerBackground={theme.palette.blue[100]}
                    noResultsMessage={
                        search
                            ? "No requests found based on your search or filter criteria. Please update your search or filter and try again."
                            : "No requests found."
                    }
                    onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                    orderBy={"name" as any}
                    sortType="ASC"
                />
                <CustomPagination
                    itemsPerPage={items_per_page}
                    metadata={metadata}
                    page={page}
                    setItemsPerPage={setItemsPerPage}
                    setPage={setPage}
                />
            </Box>
            {showConfirmationModal?.show && (
                <VendorConfirmationModal
                    showConfirmationModal={showConfirmationModal}
                    setShowConfirmationModal={setShowConfirmationModal}
                    handleSuccess={handleSuccess}
                    okButtonText={okButtonText}
                    title={showConfirmationModal?.type === "Accept" ? "Accept Request?" : "Decline Request?"}
                />
            )}
        </>
    );
}
