import builder from "@builder.io/react";
import {UseQueryResult, useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import Helmet from "react-helmet";
import {useNavigate, useParams, useSearchParams} from "react-router-dom";
import {IProducts} from "../../Interfaces/products.interface";
import ProductCard from "../../components/VendorProfile/productCard.component";
import {ROUTE_CLASS, STALE_TIME} from "../../constants/commonStrings.constant";
import {ERR_CANCELED} from "../../constants/errorMessages.constant";
import routeConfig from "../../constants/routeConfig";
import useNotification from "../../hooks/useNotification";
import {productService} from "../../services/products.service";
import {getErrorFromArray} from "../../utils/error.util";
import {stripHtml} from "../../utils/formatString.util";
import Loader from "../../utils/loader";
import styles from "./productDetails.module.sass";

export default function ProductDetails() {
    const [builderSEO, setBuilderSEO] = useState<any>();
    const {friendly_url} = useParams();
    const [searchParams] = useSearchParams();
    const review_id = searchParams.get("review_id");
    const reload = searchParams.get("r");
    const notify = useNotification();
    const navigate = useNavigate();
    const product: UseQueryResult<IProducts, any> = useQuery(
        ["products", friendly_url],
        () =>
            new Promise(resolve => {
                productService
                    .getByFriendlyUrl(friendly_url ?? "")
                    .then((r: AxiosResponse<any>) => resolve(r.data))
                    .catch((e: AxiosError<any>) => {
                        if (e.response?.status === 404) {
                            notify("Product not found.", "Error");
                            navigate(routeConfig.Home.path);
                            return;
                        }
                        if (e?.code === ERR_CANCELED) return;
                        notify(getErrorFromArray(e), "Error");
                    });
            }),
        {staleTime: STALE_TIME},
    );

    useEffect(() => {
        builder
            .get("main-pages", {url: "/product/*"})
            .promise()
            .then(res => {
                setBuilderSEO(res.data.seo);
            });
    }, []);

    useEffect(() => {
        if (reload === "1") {
            product.refetch();
        }
    }, [reload]);

    return (
        <div className={ROUTE_CLASS}>
            <Helmet>
                <title>{product.data?.name}</title>
                <meta name="description" content={stripHtml(product.data?.description)} />
                <meta property="og:title" content={product.data?.name} />
                <meta property="og:description" content={stripHtml(product.data?.description)} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="profile" />
                {product.data?.images?.[0]?.src && !review_id && (
                    <meta property="og:image" content={product.data?.images?.[0].src} />
                )}
                {review_id && (
                    <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                )}
                <meta property="og:image:alt" content={product.data?.name} />
                <meta name="twitter:title" content={product.data?.name} />
                <meta name="twitter:description" content={stripHtml(product.data?.description)} />
                {product.data?.images?.[0]?.src && (
                    <meta name="twitter:image" content={product.data?.images?.[0].src} />
                )}
            </Helmet>
            <div className={styles.container}>
                {product.data && product.data?.company?.friendly_url ? (
                    <ProductCard
                        key={product.data?.company?.id}
                        product={product.data}
                        company_friendly_url={product.data?.company?.friendly_url}
                        isEditable={false}
                        categoriesOptions={[]}
                    />
                ) : (
                    <div className="d-flex justify-content-center">
                        <Loader inline loading big />
                    </div>
                )}
            </div>
        </div>
    );
}
