import DownloadOutlined from "@mui/icons-material/DownloadOutlined";
import OpenInBrowserOutlined from "@mui/icons-material/OpenInBrowserOutlined";
import Box from "@mui/material/Box";
import {QueryFunction, QueryKey, useInfiniteQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {Dispatch, SetStateAction, useEffect, useRef, useState} from "react";
import {useNavigate} from "react-router-dom";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import type {TFolderContentTypes} from "../../Interfaces/folders.interface";
import type {IMeta} from "../../Interfaces/pagination.interface";
import {
    IPaginatedPartnerPageDataResponse,
    IPartnerPageDataResponse,
    IPartnerSection,
    TPartnerPageLayouts,
    VENDOR_LAYOUTS,
} from "../../Interfaces/partnerPage.interface";
import {buildPRMActions} from "../../components/VendorPortal/buildPRMActions.util";
import {CHANNEL_PROGRAM_COMPANY_ID, DEFAULT_QUERY_CONFIGS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useAuthState from "../../hooks/useAuthState";
import useCompanyProfile from "../../hooks/useCompanyProfile";
import {useDebounce} from "../../hooks/useDebounce";
import {useInfiniteQueryObserver} from "../../hooks/useInfinite";
import useNotification from "../../hooks/useNotification";
import {adminService} from "../../services/admin.service";
import {PartnerPageService} from "../../services/partnerpage.service";
import {vendorProfileService} from "../../services/vendorProfile.service";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ContentCard from "../../uicomponents/Molecules/ContentCard/contentCard.component";
import ContentPreview from "../../uicomponents/Molecules/ContentPreview/contentPreview.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import ToggleList from "../../uicomponents/Molecules/ToggleList/toggleList.component";
import {downloadImageFromBase64, openAsset} from "../../utils/downloadAssets.util";
import {downloadDoc} from "../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";
import useQuery from "../../utils/query.util";
import {removeQueryParam} from "../../utils/url.util";
import type {THandleContentFunction} from "./vendorPortal.page";
import {PORTAL_VIBILITY_GROUP_STRINGS} from "../../constants/visibilityLevels.constant";

interface TFilters {
    [key: string]: string | string[];
}

type IProps<T extends TPartnerPageLayouts> = {
    layout_type: T;
    sectionId: string;
    partnerPageId: string;
    filterState: [TFilters | undefined, (filters: TFilters | undefined) => void];
    searchState: [string, Dispatch<SetStateAction<string>>];
    sectionData?: IPartnerSection<T>[T];
    handleContent: THandleContentFunction;
    hasFilters?: boolean;
    onAddToFolderClick: (item: {id: string; name: string; type: TFolderContentTypes}) => void;
    visibilityFilter?: PORTAL_VIBILITY_GROUP_STRINGS;
};

type TItem<T extends TPartnerPageLayouts> = IPartnerSection<T>[T]["contents"][number];

const templateTypes = [
    {
        label: "Vendor Provided",
        value: "VENDOR",
        title: "Vendor Provided Templates",
    },
    {
        label: "My Saved Content",
        value: "SAVED",
        title: "Saved Templates",
    },
];

export default function VendorContentPage<T extends TPartnerPageLayouts>({
    layout_type,
    partnerPageId,
    sectionId,
    searchState,
    filterState,
    sectionData,
    handleContent,
    hasFilters,
    onAddToFolderClick,
    visibilityFilter,
}: IProps<T>) {
    const layoutTypes = VENDOR_LAYOUTS;
    const {canEditContent, mspId, companyId, asCompanyId, isMSP, subdomain, friendlyUrl, isChannelProgram} =
        useCompanyProfile({
            groupsToCheck: {
                edit: [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE],
                read: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ],
            },
        });
    const [playingVideo, setPlayingVideo] = useState<(TItem<"videos_layout"> & {index: number}) | null>(null);
    const [selectedAsset, setSelectedAsset] = useState<TItem<"assets_layout">>();
    const [selectedFilter, setSelectedFilter] = useState(templateTypes?.[0] ?? {});
    const [savedTemplates, setSavedTemplates] = useState([] as any);
    const [search] = searchState;
    const [filters] = filterState;
    const debouncedFilters = useDebounce(filters, 500);
    const pagination = useRef<IMeta | null>(null);
    const query = useQuery();
    const cid = query.get("cid");

    const notify = useNotification();
    const navigate = useNavigate();
    const {authState} = useAuthState();
    const urlWithoutCid = removeQueryParam(window.location.href, "cid");
    const contentIdToFocus = playingVideo?.id;
    const defaultImages = {
        articles_layout: "./Media/backgrounds/no_blog.svg",
        templates_layout: "./Media/backgrounds/no_template.svg",
        documents_layout: "./Media/backgrounds/no_document.svg",
        videos_layout: "./Media/backgrounds/no_video.svg",
        assets_layout: "",
    };

    const responseType: keyof IPartnerPageDataResponse =
        layout_type === layoutTypes.Article
            ? "articles"
            : layout_type === layoutTypes.Video
            ? "videos"
            : layout_type === layoutTypes.Document
            ? "documents"
            : layout_type === layoutTypes.Template
            ? "templates"
            : layout_type === layoutTypes.Assets
            ? "assets"
            : "articles";
    const requestType =
        layout_type === layoutTypes.Article
            ? "blog"
            : layout_type === layoutTypes.Video
            ? "video"
            : layout_type === layoutTypes.Document
            ? "document"
            : layout_type === layoutTypes.Template
            ? "template"
            : layout_type === layoutTypes.Assets
            ? "assets"
            : "";

    const getContents: QueryFunction<IPaginatedPartnerPageDataResponse, QueryKey> = ({pageParam = 1}) =>
        new Promise((resolve, reject) => {
            return PartnerPageService.getPartnerDataWithFilters(
                {
                    pageId: partnerPageId,
                    sectionId,
                    sectionType: layout_type,
                    search_word: search ?? undefined,
                    dynamic: debouncedFilters,
                    media_visibility: visibilityFilter,
                    page: pageParam,
                },
                requestType as "blog" | "template" | "document" | "video" | "assets",
            )
                .then((res: AxiosResponse<IPaginatedPartnerPageDataResponse>) => {
                    pagination.current = res.data?.[responseType].meta;
                    resolve(res.data);
                })
                .catch((e: AxiosError<any>) => {
                    reject(e);
                });
        });

    const queryKey = [
        "partnerPageDataPaginated",
        friendlyUrl,
        search,
        layout_type,
        JSON.stringify(debouncedFilters) || "",
        visibilityFilter,
    ];
    const {
        data: partnerPageContents,
        fetchNextPage,
        hasNextPage,
        isLoading,
        isFetchingNextPage,
        refetch,
    } = useInfiniteQuery<IPaginatedPartnerPageDataResponse>(queryKey, getContents, {
        getNextPageParam: res => {
            return res[responseType].meta?.last_page > res[responseType].meta?.current_page
                ? res[responseType].meta?.current_page + 1
                : null;
        },
        enabled: !!friendlyUrl && !!layout_type,
        ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
    });
    const {lastElementRef} = useInfiniteQueryObserver(isFetchingNextPage, fetchNextPage, hasNextPage);

    const getSavedTemplates = async () => {
        let response;
        if (asCompanyId) {
            response = await PartnerPageService.loadVendorSavedTempaltes(CHANNEL_PROGRAM_COMPANY_ID, asCompanyId);
        } else {
            response = mspId ? await PartnerPageService.loadSavedTempaltes(companyId, mspId) : undefined;
        }
        const sortedData = response?.data?.sort((a, b) =>
            DateTime.fromISO(b.updated_at).diff(DateTime.fromISO(a.updated_at)).as("minutes"),
        );
        setSavedTemplates(sortedData ?? []);
    };

    useEffect(() => {
        if ((companyId && isMSP) || asCompanyId) {
            getSavedTemplates();
        }
    }, [companyId]);
    const showSavedTemplates = selectedFilter.value === "SAVED" && layout_type === layoutTypes.Template;
    const contents = showSavedTemplates
        ? savedTemplates
        : partnerPageContents?.pages.map(p => p[responseType].data).flat(2) ?? [];

    const getName = (item: TItem<T>): string =>
        "title" in item && item.title
            ? item.title
            : "name" in item
            ? (item.name as string)
            : "custom_properties" in item
            ? item.custom_properties.title
            : "";
    const getDescription = (item: TItem<T>) =>
        "description" in item
            ? item.description
            : "custom_properties" in item
            ? item.custom_properties.description
            : "";
    const getImage = (item: TItem<T>) =>
        "thumbnail_url" in item
            ? item.thumbnail_url
            : "thumbnail" in item
            ? item.thumbnail
            : "media" in item
            ? item.media?.[0]?.url
            : "images" in item
            ? item.images?.[0]?.src
            : defaultImages[layout_type] ?? "";
    const getItemUrl = (item: TItem<T>) => {
        const url = urlWithoutCid + `${mspId ? "&" : "?"}cid=${item?.id}`;
        return url;
    };

    const handleItemClick = (item: TItem<T>) => {
        const activityLogBody: any = {
            action: "linkClick",
            author_type: "userType",
            author_id: authState?.id,
            additional_data: {
                from: window.location.origin + location.pathname + location.search,
                to: "",
            },
        };
        if (layout_type === layoutTypes.Video) {
            setPlayingVideo({...(item as TItem<"videos_layout">), index: contents.findIndex(i => i.id === item.id)});
            history.replaceState({}, "", getItemUrl(item));
        } else if (layout_type === layoutTypes.Article && "friendly_url" in item) {
            const url = routeConfig.PublicBlog.path.replace(":friendly_url", item.friendly_url);
            adminService.storeActivityLog({
                ...activityLogBody,
                additional_data: {...activityLogBody.additional_data, to: location.origin + url},
            });
            navigate(url);
        } else if (layout_type === layoutTypes.Template && (isMSP || asCompanyId) && !canEditContent) {
            const url =
                routeConfig.DocumentRebrand.path +
                `?t=${item?.id || ""}&ppid=${partnerPageId}&msp=${mspId || asCompanyId}&v=${companyId}&subdomain=${
                    subdomain ? subdomain : friendlyUrl
                }&asCompanyId=${asCompanyId}`;
            adminService.storeActivityLog({
                ...activityLogBody,
                additional_data: {...activityLogBody.additional_data, to: location.origin + url},
            });
            navigate(url, {state: {isFromViewAll: true}});
        } else if (layout_type === layoutTypes.Assets) {
            setSelectedAsset(item as TItem<"assets_layout">);
        }
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    useEffect(() => {
        return () => {
            setPlayingVideo(null);
            setSelectedAsset(undefined);
        };
    }, []);

    const handleSuccessOpenAsset = (item, response: AxiosResponse, image?: any) => {
        openAsset(response.data, image?.url ? image.name : item?.medias?.[0]?.name);
    };

    const handleSuccessDownload = (item, response: AxiosResponse, image?: any) => {
        if (layout_type === layoutTypes.Assets) {
            downloadImageFromBase64(response.data, image?.url ? image.name : item?.medias?.[0]?.name);
        } else {
            downloadDoc(response.data, item?.file_name);
        }
    };

    const onDownloadAsset = (gallery: any, image?: any) => {
        notify("Image will be downloaded shortly.", "Success");
        vendorProfileService.downloadMediaGallery(
            gallery?.id,
            image?.url ? image?.id : gallery?.medias?.[0]?.id,
            (res: AxiosResponse) => handleSuccessDownload(gallery, res, image),
            handleError,
        );
    };

    const onOpenAsset = (gallery: any, image?: any) => {
        notify("Image will open in a new browser window shortly.", "Success");
        vendorProfileService.downloadMediaGallery(
            gallery?.id,
            image?.url ? image?.id : gallery?.medias?.[0]?.id,
            (res: AxiosResponse) => handleSuccessOpenAsset(gallery, res, image),
            handleError,
        );
    };

    const handleContentMiddleware: THandleContentFunction = (
        id,
        type,
        contentType,
        sectionId,
        isHidden,
        title,
        visibilityType,
        friendly_url,
    ) =>
        handleContent(id, type, contentType, sectionId, isHidden, title, visibilityType, friendly_url, () => {
            refetch();
        });

    const getCardPosition = (index: number, columns: number) => {
        const row = Math.floor(index / columns) + 1;
        const column = (index % columns) + 1;
        return {row, column};
    };

    const shouldGoDown = (index: number, columns: number) => {
        if (!playingVideo) return false;
        if (index >= playingVideo.index) return true;
        // now we need to take in consideration the number of columns, so we can calculate the correct row
        // for that we need to check if the video is in the same row as the current item
        const currentRow = Math.floor(index / columns) + 1;
        const videoRow = Math.floor(playingVideo.index / columns) + 1;
        return videoRow === currentRow;
    };

    useEffect(() => {
        if (cid) {
            const item = contents.find(i => i.id === cid);
            if (item) {
                handleItemClick(item);
            }
        }
    }, [cid]);

    const handleSetFilter = (filter: string | null) => {
        const selectedFilter = templateTypes.find(item => item.value === filter);
        selectedFilter && setSelectedFilter(selectedFilter);
    };

    return (
        <Box
            component="div"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: "24px",
            }}>
            {(isMSP || asCompanyId) && !canEditContent && layout_type === layoutTypes.Template && (
                <div style={{display: "flex", justifyContent: "flex-end"}}>
                    <ToggleList
                        defaultValue={selectedFilter.value}
                        items={templateTypes}
                        onChange={handleSetFilter}
                        showCheck
                    />
                </div>
            )}
            <div>
                {contents?.length > 0 ? (
                    <Box
                        sx={{
                            display: "grid",
                            gridTemplateColumns: "repeat(1, minmax(250px, 1fr))",
                            gap: "24px",
                            "@media screen and (min-width: 600px)": {
                                gridTemplateColumns: "repeat(2, minmax(250px, 1fr))",
                            },
                            "@media screen and (min-width: 1000px)": {
                                gridTemplateColumns: "repeat(3, minmax(250px, 1fr))",
                            },
                            "@media screen and (min-width: 2000px)": {
                                gridTemplateColumns: "repeat(4, minmax(250px, 1fr))",
                            },
                        }}>
                        {playingVideo && (
                            <Box
                                sx={{
                                    gridColumn: "1 / -1",
                                    width: "100%",
                                    display: !!playingVideo ? undefined : "none",
                                    gridRow: getCardPosition(playingVideo.index, 1).row,
                                    "@media screen and (min-width: 600px)": {
                                        gridRow: getCardPosition(playingVideo.index, 2).row,
                                    },
                                    "@media screen and (min-width: 1000px)": {
                                        gridRow: getCardPosition(playingVideo.index, 3).row,
                                    },
                                    "@media screen and (min-width: 2000px)": {
                                        gridRow: getCardPosition(playingVideo.index, 4).row,
                                    },
                                }}>
                                <ContentPreview
                                    onClose={() => {
                                        setPlayingVideo(null);
                                        history.replaceState({}, "", urlWithoutCid);
                                    }}
                                    previewType="video"
                                    content={{
                                        id: playingVideo.id,
                                        name: playingVideo.custom_properties?.title,
                                        description: playingVideo.custom_properties?.description,
                                        tags: playingVideo.tags,
                                        visiblity:
                                            sectionData?.main_content_id === playingVideo.id
                                                ? "pinned"
                                                : playingVideo.section_content_id
                                                ? "visible"
                                                : "hidden",
                                        created_at: playingVideo.created_at,
                                        image: playingVideo.thumbnail,
                                        type: "video",
                                        url: playingVideo?.url,
                                    }}
                                />
                            </Box>
                        )}
                        {contents.map((item: TItem<T>, idx) => {
                            if (!item) return null;
                            const name =
                                "name" in item
                                    ? item.name
                                    : "custom_properties" in item
                                    ? item.custom_properties.title
                                    : "";

                            const type =
                                layout_type === layoutTypes.Template
                                    ? "template"
                                    : layout_type === layoutTypes.Assets
                                    ? "mediaGallery"
                                    : layout_type === layoutTypes.Article
                                    ? "blog"
                                    : layout_type === layoutTypes.Video
                                    ? "video"
                                    : "document";

                            return (
                                <>
                                    <ContentCard
                                        key={item?.id}
                                        focused={contentIdToFocus === item?.id}
                                        cardAction={
                                            layout_type === layoutTypes.Document
                                                ? undefined
                                                : {
                                                      onClick: () => handleItemClick(item),
                                                  }
                                        }
                                        canDownload={
                                            (!isMSP && canEditContent) ||
                                            (layout_type === layoutTypes.Template &&
                                                selectedFilter.value === "SAVED") ||
                                            layout_type === layoutTypes.Document
                                        }
                                        canOpen={layout_type === layoutTypes.Document}
                                        headerActions={buildPRMActions({
                                            handleContent: handleContentMiddleware,
                                            canEditContent,
                                            item: {
                                                id: item.id,
                                                name,
                                                type,
                                            },
                                            sectionContentId: item.section_content_id,
                                            sectionId,
                                            isHidden: item.is_hidden,
                                            mainContentId: item?.main_content_id || null,
                                            sectionType: layout_type,
                                            onAddToFolderClick,
                                            friendlyUrl: (item as any)?.friendly_url || "",
                                        })}
                                        content={{
                                            id: item?.id,
                                            name: getName(item),
                                            description: getDescription(item),
                                            tags: item?.tags,
                                            visiblity: !canEditContent
                                                ? undefined
                                                : item?.main_content_id === item?.section_content_id
                                                ? "pinned"
                                                : item.is_hidden
                                                ? "hidden"
                                                : "visible",
                                            created_at: item.created_at,
                                            image: getImage(item),
                                            mediaVisibility:
                                                canEditContent && isChannelProgram
                                                    ? "custom_properties" in item
                                                        ? item.custom_properties.media_visibility
                                                        : item?.media_visibility
                                                    : undefined,
                                            is_hidden: item.is_hidden,
                                            mime_type: "mime_type" in item ? item?.mime_type : undefined,
                                            file_name: "file_name" in item ? item?.file_name : undefined,
                                            type,
                                        }}
                                        companyId={companyId}
                                        analyticsData={{
                                            itemID: item.section_content_id,
                                            mspID: mspId,
                                            partnerPageId: partnerPageId,
                                        }}
                                        badge={
                                            layout_type === layoutTypes.Assets
                                                ? `${"media" in item ? item.media.length : 1} Vendor Asset${
                                                      "media" in item ? (item.media.length > 1 ? "s" : "") : ""
                                                  }`
                                                : undefined
                                        }
                                        shareUrl={isChannelProgram ? undefined : getItemUrl(item)}
                                        sx={{
                                            gridRow:
                                                playingVideo && shouldGoDown(idx, 1)
                                                    ? getCardPosition(idx, 1).row + 1
                                                    : getCardPosition(idx, 1).row,
                                            gridColumn: getCardPosition(idx, 1).column,
                                            "@media screen and (min-width: 600px)": {
                                                gridRow:
                                                    playingVideo && shouldGoDown(idx, 2)
                                                        ? getCardPosition(idx, 2).row + 1
                                                        : getCardPosition(idx, 2).row,
                                                gridColumn: getCardPosition(idx, 2).column,
                                            },
                                            "@media screen and (min-width: 1000px)": {
                                                gridRow:
                                                    playingVideo && shouldGoDown(idx, 3)
                                                        ? getCardPosition(idx, 3).row + 1
                                                        : getCardPosition(idx, 3).row,
                                                gridColumn: getCardPosition(idx, 3).column,
                                            },
                                            "@media screen and (min-width: 2000px)": {
                                                gridRow:
                                                    playingVideo && shouldGoDown(idx, 4)
                                                        ? getCardPosition(idx, 4).row + 1
                                                        : getCardPosition(idx, 4).row,
                                                gridColumn: getCardPosition(idx, 4).column,
                                            },
                                        }}
                                    />
                                </>
                            );
                        })}
                        <div ref={lastElementRef} />
                    </Box>
                ) : isLoading || isFetchingNextPage ? (
                    <div className="d-flex justify-content-center mt-5">
                        <Loader inline loading big />
                    </div>
                ) : (
                    <div className="d-flex justify-content-center mt-5">
                        {hasFilters
                            ? "No content found based on your search or filter criteria. Please update your search or filter and try again."
                            : `No content available.
                            ${canEditContent ? " To add, please click Add New." : ""}`}
                    </div>
                )}
            </div>
            <ModalComponent
                open={!!selectedAsset}
                fullWidth={true}
                maxWidth="lg"
                onClose={() => setSelectedAsset(undefined)}
                title={`Image Gallery - ${selectedAsset?.custom_properties?.title}`}
                showCancelButton={false}
                showOkButton={false}
                content={
                    <>
                        <Typography variant="body3" fontInter>
                            {selectedAsset?.custom_properties?.description}
                        </Typography>
                        <div
                            style={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: "16px",
                                marginTop: "24px",
                            }}>
                            {selectedAsset?.media.map((image, idx) => (
                                <div className="col-md-4 g-2" key={idx}>
                                    <ContentCard
                                        key={image?.id}
                                        focused={contentIdToFocus === image?.id}
                                        headerActions={[
                                            {
                                                id: "open-file" + image?.id,
                                                children: (
                                                    <span>
                                                        <OpenInBrowserOutlined color="neutral" /> View image
                                                    </span>
                                                ),
                                                onClick: () => onOpenAsset(selectedAsset, image),
                                            },
                                            {
                                                id: "download" + image?.id,
                                                children: (
                                                    <span>
                                                        <DownloadOutlined color="neutral" /> Download
                                                    </span>
                                                ),
                                                onClick: () => onDownloadAsset(selectedAsset, image),
                                            },
                                        ]}
                                        content={{
                                            id: image?.id,
                                            name: image.custom_properties?.title || image.name || image.file_name,
                                            created_at: image.created_at,
                                            type: "asset",
                                            image: image.url,
                                        }}
                                    />
                                </div>
                            ))}
                        </div>
                    </>
                }
            />
        </Box>
    );
}
