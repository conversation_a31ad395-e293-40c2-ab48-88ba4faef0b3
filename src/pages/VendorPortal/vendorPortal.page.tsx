import ContentCopyOutlinedIcon from "@mui/icons-material/ContentCopyOutlined";
import DoneOutlinedIcon from "@mui/icons-material/DoneOutlined";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import {AxiosResponse, CanceledError} from "axios";
import {useEffect, useMemo, useRef, useState} from "react";
import Helmet from "react-helmet";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {useLocation, useNavigate, useParams} from "react-router-dom";
import VendorContentModal from "../../components/Modal/vendorContentModal.component";
import PRMContentWrapper from "../../components/VendorPortal/PRMContentWrapper.component";
import {CHANNEL_PROGRAM_COMPANY_ID, PORTAL_VISIBILITY_LABEL} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {
    PORTAL_VIBILITY_GROUP_STRINGS,
    PORTAL_VISIBILITY_GROUP_CP_LABELS,
    PORTAL_VISIBILITY_GROUP_VENDOR_LABELS,
    PORTAL_VISIBILITY_GROUPS,
    VISIBILITY_OPTIONS,
} from "../../constants/visibilityLevels.constant";
import {WorkerMessageTypes} from "../../enums/fileUpload.enum";
import {usePartnerPage} from "../../hooks/fetches/usePartnerPage";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import useCompanyProfile from "../../hooks/useCompanyProfile";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import useServiceWorker, {TServiceWorkerMessage} from "../../hooks/useServiceWorker";
import useSettings from "../../hooks/useSettings";
import {useSubdomain} from "../../hooks/useSubdomain";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {TSelectedFile} from "../../Interfaces/fileUpload.interface";
import {IFolder, IFolderContentSubject, TFolderContentTypes} from "../../Interfaces/folders.interface";
import {
    IPartnerSection,
    IVendorLayouts,
    TPartnerPageLayouts,
    VENDOR_LAYOUTS,
} from "../../Interfaces/partnerPage.interface";
import {MutateTypes} from "../../Interfaces/queries.interface";
import {ITag} from "../../Interfaces/tags.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {adminService} from "../../services/admin.service";
import {analyticService} from "../../services/analytic.service";
import {enumService} from "../../services/enum.service";
import {PartnerPageService} from "../../services/partnerpage.service";
import {vendorProfileService} from "../../services/vendorProfile.service";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import CheckboxWithLabel from "../../uicomponents/Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import RadioWithLabel from "../../uicomponents/Molecules/RadioWithLabel/RadioWithLabel.component";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import AddContentToFolder from "../../uicomponents/Organism/AddContentToFolder/addContentToFolder.component";
import AddDocsToFolders, {
    IDocsToFoldersContent,
} from "../../uicomponents/Organism/AddDocsToFolders/addDocsToFolders.component";
import AddFolderDialog from "../../uicomponents/Organism/AddFolderDialog/addFolderDialog.component";
import AddToFolderDialog from "../../uicomponents/Organism/AddToFoldersDialog/addToFolderDialog.component";
import CodeCopyTextBox from "../../uicomponents/Organism/CodeCopyTextField/codeCopy";
import FileUploadModal, {
    IFileUploadModalProps,
    TFileUploadMode,
} from "../../uicomponents/Organism/FileUploadModal/fileUploadModal.component";
import NewDealRequestWidget from "../../uicomponents/Organism/NewDealRequestWidget/NewDealRequestWidget.component";
import OrganizeFoldersDailog from "../../uicomponents/Organism/OrganizeFolders/organizeFoldersDialog.component";
import ProfileHeaderV2 from "../../uicomponents/Organism/ProfileHeader/profileHeaderV2.components";
import UpcomingEventsWidget from "../../uicomponents/Organism/UpcomingEventsWidget/UpcomingEventsWidget";
import LabelComponent from "../../uicomponents/Typography/Label.component";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {openDoc} from "../../utils/downloadDoc.util";
import useQuery from "../../utils/query.util";
import {scrollToTop} from "../../utils/scrollToTop.util";
import {getSubdomain} from "../../utils/url.util";

export type THandleContentFunction = (
    id: string,
    type: "delete" | "toggleHide" | "add" | "pin" | "edit",
    contentType: TPartnerPageLayouts,
    sectionId: string,
    isHidden: boolean,
    title: string,
    visibilityType?: string,
    friendly_url?: string,
    onSettled?: () => void,
) => Promise<void>;

export default function VendorPortalPage() {
    const location = useLocation();
    const subdomainObj = useSubdomain();
    const subdomain = subdomainObj.company;
    const navigate = useNavigate();
    const {folderId} = useParams();
    const serviceWorker = useServiceWorker();

    const methods = useForm({shouldUnregister: true, defaultValues: {cp_partner_logo: false, my_logo_image: true}});
    const [copySuccess, setCopySuccess] = useState(false);
    const [showUploadModalType, setShowUploadModalType] = useState<TPartnerPageLayouts>();
    const [layoutTypes, setLayoutTypes] = useState<IVendorLayouts>();
    const [blogToEdit, setBlogToEdit] = useState<string>("");
    const [assetToEdit, setAssetToEdit] = useState<string>("");
    const [itemToEdit, setItemToEdit] = useState<string>("");
    const [searchKey, setSearchKey] = useState<string>("");
    const [isAddedNewData, setIsAddedNewData] = useState<boolean>(false);
    const [itemToFocus, setItemToFocus] = useState<any>();
    const [sectionId, setSectionId] = useState<string>("");
    const [openEmbeddedModel, setOpenEmbeddedModel] = useState<boolean>(false);
    const [_showBulletin, setShowBulletin] = useState<boolean>(true);
    const [isEventsEmpty, setIsEventsEmpty] = useState<boolean>(true);
    const {authState} = useAuthState();
    const {
        mspId,
        isChannelProgram,
        asCompanyId,
        companyId,
        companyInfo,
        avatar: companyAvatar,
    } = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE],
            read: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ],
        },
    });
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const {settings, updateSettings} = useSettings();
    const {prm_layouts} = settings;
    const {hasPermissions, isCompanySuperAdmin} = usePermissions({overrideCompanyId: companyId});
    const prmPermissions = [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE];
    const prmReadPermissions = [PERMISSION_GROUPS.CHANNEL_COMMAND_READ];
    const {activeCompany, isLoading: isLoadingActiveCompany, userCompanies} = useActiveCompany();
    const _canReadPRM = hasPermissions(prmReadPermissions);
    const canManagePRM = hasPermissions(prmPermissions);
    const notify = useNotification();
    const query = useQuery();
    const contentIdToFocus = query.get("cid");
    const {
        partnerPage,
        partnerPageBlog,
        partnerPageAssets,
        partnerPageDocs,
        partnerPageTemplates,
        partnerPageVideos,
        filterState,
        selectedLayoutState,
        searchState,
        isUpdating: isUpdatingPartnerData,
        updatePartnerPage,
        foldersData,
        allPartnerPageData,
        allFoldersData,
    } = usePartnerPage(subdomain, {
        isCompany: true,
        refreshAfterMutate: true,
        calls: {
            all: true,
        },
        as_company_id: asCompanyId,
        isChannelProgram: isChannelProgram,
        companyId: companyInfo?.id,
        requireAsCompanyId: !!companyInfo && !isCompanySuperAdmin && activeCompany?.id !== companyInfo?.id,
    });
    const {invalidateMatchedQueries} = useQueryHelper();
    const [selectedLayout, setSelectedLayout] = selectedLayoutState;
    const hasFocusedItem = useRef(false);

    const isUpdatingData =
        isUpdatingPartnerData?.[MutateTypes.AddSectionData] ||
        isUpdatingPartnerData?.[MutateTypes.RemoveSectionData] ||
        isUpdatingPartnerData?.[MutateTypes.PinContent];

    const loadingInfo = partnerPage.isLoading;
    const selectedSectionQuery = (layout: TPartnerPageLayouts | undefined) =>
        layout === "articles_layout"
            ? partnerPageBlog
            : layout === "assets_layout"
            ? partnerPageAssets
            : layout === "documents_layout"
            ? partnerPageDocs
            : layout === "videos_layout"
            ? partnerPageVideos
            : partnerPageTemplates;
    const sectionData = selectedSectionQuery(selectedLayout)?.data;
    const [redirectingForPermissionCheck, setRedirectingForPermissionCheck] = useState(false);
    const isChannelProgramPRM = companyInfo?.id === CHANNEL_PROGRAM_COMPANY_ID;
    const originalActiveCompanyFriendlyUrl = useRef<string>("");
    const partnerPageData = {
        articles_layout: partnerPageBlog.data,
        assets_layout: partnerPageAssets.data,
        documents_layout: partnerPageDocs.data,
        videos_layout: partnerPageVideos.data,
        templates_layout: partnerPageTemplates.data,
    };
    const tabs = canManagePRM
        ? isChannelProgramPRM
            ? [PORTAL_VISIBILITY_GROUPS.MSP, PORTAL_VISIBILITY_GROUPS.VENDOR]
            : [PORTAL_VISIBILITY_GROUPS.MSP]
        : [];
    const currentTab = useRef<PORTAL_VIBILITY_GROUP_STRINGS | undefined>(tabs[0] || undefined);

    const partnerLogoCheckboxWatcher = methods.watch("cp_partner_logo");
    const imgSrc = partnerLogoCheckboxWatcher
        ? getSubdomain({
              url: window.location.origin,
              defaultValues: {
                  pathname: "/Media/ChannelCommandPartnerLogo.png",
              },
          }).toString()
        : `"ENTER IMAGE LINK HERE"`;

    const linkToBeCopied: string = `<a href="${window.location.href}" target="_blank"><img src=${imgSrc} alt="Embedded link"/></a>`;
    const showWidgetSection =
        !isLoadingActiveCompany && !activeCompany?.type_is_of_vendor && (isChannelProgramPRM ? !isEventsEmpty : true);

    useEffect(() => {
        scrollToTop();
    }, [selectedLayout]);

    const loadLayoutTypes = () => {
        setIsLoading(true);
        enumService.getLayoutTypes(onSuccessLoadQuestionTypes, () => {
            setIsLoading(false);
        });
    };

    const onSuccessLoadQuestionTypes = (response: AxiosResponse<IVendorLayouts>) => {
        if (response && response.data) {
            setIsLoading(false);
            updateSettings(UPDATE_SETTINGS, {prm_layouts: response.data});
            setLayoutTypes(response.data);
        }
    };

    useEffect(() => {
        if (showUploadModalType && layoutTypes && Object.keys(layoutTypes).length > 0) {
            const key = Object.keys(layoutTypes)?.filter(a => layoutTypes[a] === showUploadModalType)?.[0];
            if (key) {
                setSearchKey(key);
            }
        }
    }, [showUploadModalType]);

    const onViewAllClick = (partner_page_section_id: string, type: TPartnerPageLayouts) => {
        setSelectedLayout(type);
        setSectionId(partner_page_section_id);
    };

    const handleContent: THandleContentFunction = async (
        id,
        type,
        contentType,
        sectionId,
        isHidden,
        title,
        visibilityType,
        friendly_url,
        onSettled,
    ) => {
        let actionValue: string = "true";
        let answer: {value: string} | null = null;
        let mutateType: MutateTypes | null = null;
        let mutateAppend = "";
        const toggleHideLabel = isHidden ? "show" : "hide";
        switch (type) {
            case "delete":
                answer = await getUserConfirmation(
                    "Are you sure you want to delete this? This operation can not be undone.",
                    {
                        form: (
                            <div style={{textAlign: "center", lineHeight: "1.7"}}>
                                {" "}
                                <div>
                                    {" "}
                                    Are you sure you want to delete the
                                    {contentType === layoutTypes?.Document ||
                                    contentType === layoutTypes?.Template ||
                                    contentType === layoutTypes?.Assets
                                        ? " file"
                                        : contentType === layoutTypes?.Video
                                        ? " video"
                                        : " blog"}
                                    ,{" "}
                                </div>
                                <div>
                                    <strong> {title} </strong>{" "}
                                </div>
                                <div> This process cannot be undone. </div>
                            </div>
                        ),
                        okButtonText: "DELETE",
                        cancelButtonText: "CANCEL",
                        title: `Delete ${
                            contentType === layoutTypes?.Document ||
                            contentType === layoutTypes?.Template ||
                            contentType === layoutTypes?.Assets
                                ? "File"
                                : contentType === layoutTypes?.Video
                                ? "Video"
                                : "Blog"
                        }?`,
                        mui: true,
                        modalTheme: "error",
                    },
                );
                actionValue = answer.value;
                mutateType = MutateTypes.RemoveSectionData;
                mutateAppend = id;
                break;
            case "toggleHide":
                answer = await getUserConfirmation(
                    "Are you sure you want to " +
                        toggleHideLabel +
                        " this? Don’t worry, you can always change it back later.",
                    {
                        form: (
                            <div style={{lineHeight: "1.7"}}>
                                <div style={{textAlign: "center"}}>
                                    <div> Are you sure you want to {toggleHideLabel} the content, </div>
                                    <div>
                                        <div style={{fontWeight: "600"}}> {title} </div>
                                    </div>
                                    <hr />
                                </div>
                                <div>
                                    <div>
                                        <div style={{fontWeight: "500"}}>
                                            By {isHidden ? "showing" : "hiding"} this content:{" "}
                                        </div>
                                        <div style={{fontSize: "12px"}}>
                                            &#9679; It would be {isHidden ? "shown" : "hidden"} from access in your
                                            portal
                                            <br /> &#9679; Your partners {isHidden ? "will" : "won't"} have access
                                        </div>
                                    </div>
                                    <hr />
                                </div>
                            </div>
                        ),
                        okButtonText: isHidden ? "SHOW" : "HIDE",
                        cancelButtonText: "CANCEL",
                        title: isHidden ? "Show content?" : "Hide content?",
                        mui: true,
                        modalTheme: "error",
                    },
                );
                actionValue = answer.value;
                mutateType = MutateTypes.ToggleHideSectionData;
                mutateAppend = id;
                break;
            case "add":
                mutateType = MutateTypes.AddSectionData;
                actionValue = "true";
                break;
            case "pin":
                updatePartnerPage.mutate({
                    id: partnerPage?.data?.id,
                    section_id: sectionId,
                    subject_id: id,
                    mutate_type: MutateTypes.PinContent,
                    mutate_is_updating_append: id,
                    contentType,
                });
                actionValue = "";
                onSettled?.();
                break;
            case "edit":
                if (contentType === layoutTypes?.Article && friendly_url) {
                    navigate(routeConfig.UpsertPRMBlog.path + `?blogToEdit=${friendly_url}`);
                } else if (contentType === layoutTypes?.Assets && id) {
                    setAssetToEdit(id);
                } else {
                    setItemToEdit(id);
                }
                setShowUploadModalType(contentType);
                break;
            default:
                notify(`ERRVP001:The type logic for ${type} has not been implemented`, "Error");
        }
        if (actionValue && mutateType) {
            updatePartnerPage.mutate({
                id: partnerPage?.data?.id,
                section_id: sectionId,
                subject_id: id,
                mutate_type: mutateType,
                subject_type:
                    contentType === layoutTypes?.Article
                        ? "blog"
                        : contentType === layoutTypes?.Assets
                        ? "mediaGallery"
                        : "media",
                media_visibility: visibilityType,
                contentType,
                ...(mutateAppend ? {mutate_is_updating_append: mutateAppend} : {}),
            });
            onEditContentSuccess();
            onSettled?.();
        }
    };

    const handleResetValues = () => {
        setShowUploadModalType(undefined);
        setBlogToEdit("");
        setAssetToEdit("");
        setItemToEdit("");
    };

    const onEditContentSuccess = () => {
        const getQueryNameBySearchType = () => {
            switch (showUploadModalType) {
                case "articles_layout":
                    return "partnerPageBlogData";
                case "assets_layout":
                    return "partnerPageAssetsData";
                case "videos_layout":
                    return "partnerPageVideosData";
                case "documents_layout":
                    return "partnerPageDocsData";
                case "templates_layout":
                    return "partnerPageTemplatesData";
                default:
                    return "";
            }
        };
        invalidateMatchedQueries([getQueryNameBySearchType(), companyInfo?.friendly_url]);
        invalidateMatchedQueries(["partnerPageDataPaginated", companyInfo?.friendly_url]);
        selectedSectionQuery(showUploadModalType).refetch();
        handleResetValues();
    };

    const storeActivityLogRedirect = (redirectUrl: string) => {
        adminService
            .storeActivityLog({
                action: "prmLogin",
                author_id: authState?.id,
                author_type: "userType",
                additional_data: {
                    status: "loggedIn",
                    user_email: authState?.email,
                    from: window.location.href,
                    to: redirectUrl,
                    subdomain,
                },
            })
            .finally(() => {
                window.location.href = redirectUrl;
            });
    };

    useEffect(() => {
        if (partnerPage.data?.id) {
            analyticService.storePartnerAnalytic("partnerPortalView", partnerPage.data?.id);
        }
    }, [partnerPage.data?.id]);

    useEffect(() => {
        if (prm_layouts) {
            setLayoutTypes(prm_layouts);
        } else {
            loadLayoutTypes();
        }
        const shouldShowBulleting = sessionStorage.getItem("showBulletin");
        if (shouldShowBulleting === "false") {
            setShowBulletin(false);
        }
    }, []);

    const hasQueryError =
        (partnerPage.error && !(partnerPage.error instanceof CanceledError)) ||
        (partnerPage.failureReason && !(partnerPage.failureReason instanceof CanceledError)) ||
        (partnerPageBlog.error && !(partnerPageBlog.error instanceof CanceledError)) ||
        (partnerPageBlog.failureReason && !(partnerPageBlog.failureReason instanceof CanceledError)) ||
        (partnerPageAssets.error && !(partnerPageAssets.error instanceof CanceledError)) ||
        (partnerPageAssets.failureReason && !(partnerPageAssets.failureReason instanceof CanceledError)) ||
        (partnerPageDocs.error && !(partnerPageDocs.error instanceof CanceledError)) ||
        (partnerPageDocs.failureReason && !(partnerPageDocs.failureReason instanceof CanceledError)) ||
        (partnerPageTemplates.error && !(partnerPageTemplates.error instanceof CanceledError)) ||
        (partnerPageTemplates.failureReason && !(partnerPageTemplates.failureReason instanceof CanceledError)) ||
        (partnerPageVideos.error && !(partnerPageVideos.error instanceof CanceledError)) ||
        (partnerPageVideos.failureReason && !(partnerPageVideos.failureReason instanceof CanceledError));

    useEffect(() => {
        if (hasQueryError) {
            if (!activeCompany?.type_is_of_vendor) {
                if (mspId === authState.company_id) {
                    //? Primary MSP account redirect to my-stack
                    storeActivityLogRedirect(
                        getSubdomain({
                            url: window.location.origin,
                            defaultValues: {
                                pathname: routeConfig.MyStack.path.substring(1, routeConfig.MyStack.path.length),
                                search: "?m=not_authorized",
                            },
                        })
                            .stripSubdomain()
                            .toString(),
                    );
                } else {
                    // ? Claimed company account redirect to company portal
                    const foundClaimedCompany = userCompanies?.find(c => c.id === mspId);

                    if (!foundClaimedCompany) {
                        storeActivityLogRedirect(
                            getSubdomain({
                                url: window.location.origin,
                                defaultValues: {
                                    pathname: routeConfig.MyStack.path,
                                    search: "?m=unauthorized",
                                },
                            })
                                .stripSubdomain()
                                .toString(),
                        );
                        return;
                    }
                    storeActivityLogRedirect(
                        getSubdomain({
                            url: window.location.origin,
                            defaultValues: {
                                pathname: routeConfig.CompanyPortal.path.replace(":tab", ""),
                                search: "?m=not_authorized",
                            },
                        })
                            .stripSubdomain()
                            .toString(),
                    );
                }
            } else {
                window.location.href = getSubdomain({
                    url: window.location.origin,
                    defaultValues: {
                        search: "?m=unauthorized",
                    },
                })
                    .stripSubdomain()
                    .toString();
            }
        }
    }, [hasQueryError]);

    useEffect(() => {
        if (!showUploadModalType) {
            setIsAddedNewData(true);
        }
    }, [showUploadModalType]);

    useEffect(() => {
        if (contentIdToFocus && partnerPage?.data && !hasFocusedItem.current) {
            let foundContent: any = null;
            Object.values(partnerPageData || {})?.forEach(section => {
                if ((section?.contents?.length || 0) > 0) {
                    section?.contents?.forEach(content => {
                        if (content?.id === contentIdToFocus) {
                            foundContent = {...content, layout: section.layout};
                        }
                    });
                }
            });
            if (foundContent) {
                if (foundContent.layout === "templates_layout") {
                    const subjectId = foundContent?.subject_id;
                    if (subjectId) {
                        PartnerPageService.getTemplatePDF(companyInfo?.id || "", mspId || "", subjectId, (res: any) => {
                            const split = res.data.split("base64,");
                            const b64 = split[1];
                            const byteCharacters = atob(b64);
                            const byteNumbers = new Array(byteCharacters.length);
                            for (let i = 0; i < byteCharacters.length; i++) {
                                byteNumbers[i] = byteCharacters.charCodeAt(i);
                            }
                            const byteArray = new Uint8Array(byteNumbers);
                            openDoc(byteArray, foundContent?.subject?.file_name);
                        });
                    }
                }
                //? If type is of document open the document in a new tab
                if (foundContent.layout === "documents_layout") {
                    const handleSuccessOpen = (response: AxiosResponse) => {
                        openDoc(response.data, foundContent?.subject?.file_name);
                    };

                    vendorProfileService.downloadDoc(
                        foundContent?.subject?.id,
                        foundContent?.subject?.model_id,
                        foundContent?.subject?.mime_type,
                        handleSuccessOpen,
                        () => null,
                    );
                }
                setItemToFocus(foundContent);
                hasFocusedItem.current = true;
            }
        }
    }, [contentIdToFocus, partnerPage?.data]);

    useEffect(() => {
        if (itemToFocus?.id && !(loadingInfo || allPartnerPageData.isLoading || isLoading)) {
            setTimeout(() => {
                const element = document.getElementById(itemToFocus?.id);
                if (element) {
                    element.scrollIntoView({behavior: "smooth"});
                }
            }, 1000);
        }
    }, [itemToFocus, loadingInfo, allPartnerPageData.isLoading, isLoading]);

    const onClickAddNewContent = (type: VENDOR_LAYOUTS) => {
        if (type === VENDOR_LAYOUTS.Article) {
            navigate(routeConfig.UpsertPRMBlog.path);
            return;
        }
        setShowUploadModalType(type);
    };

    const copyToClipBoard = async () => {
        notify("Copied Successfully", "Success");
        try {
            await navigator.clipboard.writeText(linkToBeCopied);
            setCopySuccess(true);
        } catch (err) {
            setCopySuccess(false);
        }
    };

    useEffect(() => {
        let timeOut;
        if (copySuccess) {
            timeOut = setTimeout(() => {
                setCopySuccess(false);
            }, 1000);
        }

        return () => clearTimeout(timeOut);
    }, [copySuccess]);

    const handleChecked = e => {
        if (e.target.id === "cp_partner_logo") {
            methods.setValue("cp_partner_logo", true);
            methods.setValue("my_logo_image", false);
        } else {
            methods.setValue("cp_partner_logo", false);
            methods.setValue("my_logo_image", true);
        }
    };

    const folders = useMemo(
        () => (foldersData.data?.pages?.flatMap(p => p?.data) || []) as IFolder[],
        [foldersData.data],
    );
    const [addFolderModal, setAddFolderModal] = useState<IFolder>();
    const [addContentToFolderModal, setAddContentToFolderModal] = useState<IFolder>();
    const [folderDetails, setFolderDetails] = useState<IFolder>();
    const [addToFolderModal, setAddToFolderModal] = useState<{id: string; name: string}>();
    const [showAddDocumentsModal, setShowAddDocumentModal] = useState(false);
    const [showOrganizeFoldersModal, setShowOrganizeFoldersModal] = useState<boolean>(false);
    const [allTags, setAllTags] = useState<ITag[]>([]);

    // Aditional params options for bulk upload
    const fileUploadAditionalParams = () => {
        if (!partnerPageData) return {};
        const documentsDetails =
            partnerPageData?.documents_layout as IPartnerSection<TPartnerPageLayouts>[TPartnerPageLayouts];
        const videosDetails =
            partnerPageData?.videos_layout as IPartnerSection<TPartnerPageLayouts>[TPartnerPageLayouts];
        const assetsDetails =
            partnerPageData?.assets_layout as IPartnerSection<TPartnerPageLayouts>[TPartnerPageLayouts];
        return {
            document: {
                is_partner_content: 1,
                partner_page_section_id: documentsDetails?.id,
                partner_page_id: documentsDetails?.partner_page_id,
            },
            video: {
                is_partner_content: 1,
                partner_page_section_id: videosDetails?.id,
                partner_page_id: videosDetails?.partner_page_id,
            },
            assets: {
                is_partner_content: 1,
                partner_page_section_id: assetsDetails?.id,
                partner_page_id: assetsDetails?.partner_page_id,
            },
        };
    };

    const bulkUploadMethods = useForm();
    const bulkUploadWatcher = useWatch({control: bulkUploadMethods.control});

    const [fileUploadModalConf, setFileUploadModalConf] = useState<IFileUploadModalProps>({
        open: false,
        companyFriendlyUrl: "",
        uploadMode: "files",
        aditionalParamsConfig: {
            fileUpload: {
                owner_type: "companyProfile",
                owner_id: companyInfo?.id,
                ...fileUploadAditionalParams(),
            },
            uploadSummary: {
                owner_type: "companyProfile",
                owner_id: companyInfo?.id,
                ...fileUploadAditionalParams(),
            },
        },
        context: "companyPortal",
        onClose: () => {},
    });

    useEffect(() => {
        if (!folderId && !!folderDetails) {
            setFolderDetails(undefined);
            return;
        }
        const selectedFolder = folders.find(folder => folder.id === folderId);
        if (!!selectedFolder) {
            setFolderDetails(selectedFolder);
        }
    }, [folders, folderId]);

    // Filtering aditional params for bulkUpload files
    const filterAditionalParams = (file: TSelectedFile, params: {[key: string]: any}) => {
        let response = {
            owner_id: params.owner_id ?? null,
            owner_type: params.owner_type ?? null,
            ...(!!params.media_visibility ? {media_visibility: params.media_visibility} : {}),
        };
        switch (file.fileType) {
            case "document":
                response = {...response, ...(params.document ?? {})};
                break;
            case "video":
                response = {...response, ...(params.video ?? {})};
                break;
            default:
                response = {...response, ...(params.assets ?? {})};
                break;
        }
        return response;
    };

    // Update aditional params for bulk upload based on custom controls values
    useEffect(() => {
        const values = bulkUploadMethods.getValues();
        setFileUploadModalConf(prev => ({
            ...prev,
            aditionalParamsConfig: {
                ...prev.aditionalParamsConfig,
                fileUpload: {
                    ...prev.aditionalParamsConfig?.fileUpload,
                    ...values,
                },
            },
        }));
    }, [bulkUploadWatcher]);

    // Open modal for bulk upload files
    const openBulkUploadModal = (uploadMode: TFileUploadMode, customProps?: {[key: string]: any}) => {
        if (!companyInfo?.friendly_url) return;
        bulkUploadMethods.setValue("media_visibility", currentTab.current || "msp");
        setFileUploadModalConf(prev => ({
            ...prev,
            open: true,
            companyFriendlyUrl: companyInfo?.friendly_url,
            uploadMode: uploadMode,
            aditionalParamsConfig: {
                fileUpload: {
                    owner_type: "companyProfile",
                    owner_id: companyInfo?.id,
                    media_visibility: "all",
                    ...fileUploadAditionalParams(),
                },
                uploadSummary: {
                    owner_type: "companyProfile",
                    owner_id: companyInfo?.id,
                    ...fileUploadAditionalParams(),
                },
            },
            onClose: closeFileUploadModal,
            customProps,
        }));
        setTimeout(() => {
            bulkUploadMethods.setValue("visibilityType", currentTab.current === "msp");
        }, 500);
    };
    // Open bulk upload modal in filesAndFolders upload mode
    const openFileUploadModal = (customProps?: {[key: string]: any}) => {
        openBulkUploadModal("filesAndFolders", customProps);
    };
    // Open bulk upload modal in folders upload mode
    const openFolderUploadModal = (customProps?: {[key: string]: any}) => {
        openBulkUploadModal("folders", customProps);
    };
    // Close bulk upload modal
    const closeFileUploadModal = () => {
        setFileUploadModalConf(prev => ({
            ...prev,
            open: false,
        }));
    };
    // Receive and handle messages sent by File Upload Service Worker
    const handleServiceWorkerMessages = (e: MessageEvent<TServiceWorkerMessage>) => {
        switch (e.data.type) {
            case WorkerMessageTypes.NEW_FILE_UPLOADED:
                switch (e.data.fileType) {
                    case "document":
                        partnerPageDocs.refetch();
                        break;
                    case "video":
                        partnerPageVideos.refetch();
                        break;
                    default:
                        partnerPageAssets.refetch();
                        break;
                }
                if (!!e.data?.relativePath) {
                    foldersData.refetch();
                }
                break;
            case WorkerMessageTypes.NEW_FOLDER_CREATED:
                foldersData.refetch();
                break;
            case WorkerMessageTypes.UPLOAD_FINISHED:
                partnerPageDocs.refetch();
                partnerPageVideos.refetch();
                partnerPageAssets.refetch();
                foldersData.refetch();
                break;
            default:
                break;
        }
    };
    // Register the listener function for FUSW messages
    useEffect(() => {
        serviceWorker.registerCallback(handleServiceWorkerMessages);
        return () => {
            serviceWorker.unregisterCallback(handleServiceWorkerMessages);
        };
    }, []);

    const handleFolderEditClick = (folderItem: IFolder) => setAddFolderModal(folderItem);

    const handleAddToFolderClick = (item: {id: string; name: string; type: TFolderContentTypes}) =>
        setAddToFolderModal(item);

    const getContentTags = () => {
        const tagIds = new Set<string>();
        const uniqueTags: ITag[] = [];
        Object.values(partnerPageData || {})?.forEach(section => {
            if ((section?.contents?.length || 0) > 0) {
                section?.contents?.forEach(content => {
                    content?.tags?.forEach((tag: ITag) => {
                        const isDuplicate = tagIds.has(tag.id);
                        if (!isDuplicate) {
                            tagIds.add(tag.id);
                            uniqueTags.push(tag);
                        }
                    });
                });
            }
        });
        if (uniqueTags.length > allTags.length) setAllTags(uniqueTags);
        return;
    };

    useEffect(() => {
        if (!partnerPageData) return;
        getContentTags();
    }, [partnerPageData]);

    const allContentsData = allPartnerPageData;
    const allContentsLoading = allPartnerPageData.isLoading;
    const allContents = useMemo(() => {
        const getName = (item): string =>
            "title" in item && item.title
                ? item.title
                : "name" in item
                ? (item.name as string)
                : "custom_properties" in item
                ? item.custom_properties.title
                : "";
        const contents = Object.entries(allContentsData || {})?.flatMap(([type, section]) => {
            if (!["isLoading", "isFetching"].includes(type) && (section?.contents?.length || 0) > 0) {
                return section?.contents?.map(content => ({
                    id: `${content.id}`,
                    label: getName(content),
                    owner: {
                        avatar: content?.author?.avatar || companyAvatar || "",
                        profileType: companyInfo?.company_type || "vendor",
                        friendly_url: companyInfo?.friendly_url || "",
                        name: companyInfo?.name || "",
                        type: "company",
                    },
                    tags: content.tags,
                    type,
                }));
            }
            return [];
        });
        return contents;
    }, [allContentsData, companyInfo]);

    const resetFilters = () => {
        filterState[1](undefined);
        searchState[1]("");
    };

    const handleEditContent = (item: IFolderContentSubject) => {
        switch (item.type) {
            case "blog":
                navigate(
                    routeConfig.UpsertPRMBlog.path +
                        `?blogToEdit=${item.custom_properties?.friendly_url || ""}&is_company=true`,
                );
                break;
            case "mediaGallery":
                setAssetToEdit(item.id);
                setShowUploadModalType("assets_layout");
                break;
            case "document":
                setItemToEdit(item.id);
                setShowUploadModalType("documents_layout");
                break;
            case "video":
                setItemToEdit(item.id);
                setShowUploadModalType("videos_layout");
                break;
            default:
                setItemToEdit(item.id);
                setShowUploadModalType("templates_layout");
        }
    };

    useEffect(() => {
        if (folderDetails) {
            const newFolderDetail = folders.find(f => f.id === folderDetails.id);
            newFolderDetail && setFolderDetails(newFolderDetail);
        }
    }, [folders]);

    useEffect(() => {
        if (redirectingForPermissionCheck) {
            const redirectUrl = getSubdomain({
                url: window.location.origin,
            })
                .stripSubdomain()
                .toString();
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);
        }
    }, [redirectingForPermissionCheck]);

    useEffect(() => {
        if (!companyInfo?.id || !activeCompany?.friendly_url) return;
        if (!originalActiveCompanyFriendlyUrl.current)
            originalActiveCompanyFriendlyUrl.current = activeCompany.friendly_url;
        if (activeCompany?.friendly_url !== originalActiveCompanyFriendlyUrl.current && !isChannelProgramPRM) {
            setRedirectingForPermissionCheck(true);
        } else {
            setRedirectingForPermissionCheck(false);
        }

        return () => setRedirectingForPermissionCheck(false);
    }, [activeCompany?.friendly_url, companyInfo]);

    return (
        <>
            <Helmet>
                <title>{`${companyInfo?.name ?? "Partner"} - Company Portal` || "This Company Profile"}</title>
                <meta
                    name="description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    property="og:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:url" content={window.location.origin + location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="profile" />
                <meta property="og:profile:username" content={companyInfo?.handle || ""} />
                <meta property="og:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
                <meta
                    property="og:image:alt"
                    content={companyInfo?.name || "This company profile at Channel Program"}
                />
                <meta name="twitter:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    name="twitter:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta name="twitter:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
            </Helmet>

            <Box
                className="newRoute"
                sx={
                    showWidgetSection
                        ? {display: "grid", gap: 3, gridTemplateColumns: {xs: "1fr", lg: "1fr 300px"}}
                        : {}
                }>
                <Box
                    className="grow newContainer"
                    sx={theme => ({
                        maxWidth: "100%",
                        padding: "0px !important",
                        background: theme.palette.neutral[100],
                    })}>
                    <ProfileHeaderV2
                        profile_type="company"
                        isVendorPortalPage={true}
                        partnerPageId={partnerPage?.data?.id}
                        partnerPageData={partnerPage?.data}
                        isLoadingPartnerPage={loadingInfo}
                        as_company_id={asCompanyId}
                        setOpenEmbeddedModel={setOpenEmbeddedModel}
                    />
                    <div style={{padding: "24px", display: "flex", flexDirection: "column", gap: "24px"}}>
                        {canManagePRM && isChannelProgramPRM ? (
                            <BasicTabs
                                tabsContentSx={{marginTop: 3}}
                                shouldTrackRoute
                                tabs={tabs?.map(visibilityValue => {
                                    return {
                                        id: visibilityValue,
                                        label: isChannelProgramPRM
                                            ? PORTAL_VISIBILITY_GROUP_CP_LABELS[visibilityValue]
                                            : PORTAL_VISIBILITY_GROUP_VENDOR_LABELS[visibilityValue],
                                        Component: (
                                            <PRMContentWrapper
                                                visibilityFilter={visibilityValue}
                                                setShowAddDocumentModal={setShowAddDocumentModal}
                                                openFolderUploadModal={openFolderUploadModal}
                                                openFileUploadModal={openFileUploadModal}
                                                onClickAddNewContent={onClickAddNewContent}
                                                setAddContentToFolderModal={setAddContentToFolderModal}
                                                handleFolderEditClick={handleFolderEditClick}
                                                handleEditContent={handleEditContent}
                                                sectionId={sectionId}
                                                sectionData={sectionData}
                                                handleContent={handleContent}
                                                handleAddToFolderClick={handleAddToFolderClick}
                                                setSectionId={setSectionId}
                                                onViewAllClick={onViewAllClick}
                                                allContents={allContents}
                                                setAddFolderModal={setAddFolderModal}
                                                isUpdating={isUpdatingPartnerData}
                                            />
                                        ),
                                    };
                                })}
                                onBeforeChange={t => {
                                    currentTab.current = t.id;
                                    return true;
                                }}
                            />
                        ) : (
                            <PRMContentWrapper
                                setShowAddDocumentModal={setShowAddDocumentModal}
                                openFolderUploadModal={openFolderUploadModal}
                                openFileUploadModal={openFileUploadModal}
                                onClickAddNewContent={onClickAddNewContent}
                                setAddContentToFolderModal={setAddContentToFolderModal}
                                handleFolderEditClick={handleFolderEditClick}
                                handleEditContent={handleEditContent}
                                sectionId={sectionId}
                                sectionData={sectionData}
                                handleContent={handleContent}
                                handleAddToFolderClick={handleAddToFolderClick}
                                setSectionId={setSectionId}
                                onViewAllClick={onViewAllClick}
                                allContents={allContents}
                                setAddFolderModal={setAddFolderModal}
                                isUpdating={isUpdatingPartnerData}
                            />
                        )}
                    </div>
                    <ModalComponent
                        open={openEmbeddedModel}
                        onClose={() => setOpenEmbeddedModel(false)}
                        okButtonText={copySuccess ? "COPIED" : "COPY"}
                        showOkButton={true}
                        onSuccess={() => copyToClipBoard()}
                        reverseButtonPosition={true}
                        cancelButtonText="CLOSE"
                        modalTheme="blue"
                        title="Configure an Embed Button"
                        okButtonSx={{backgroundColor: "#0C2556", "&:hover": {backgroundColor: "#0C2556"}}}
                        cancelButtonSx={{
                            backgroundColor: "#A9B5C6",
                            color: "#F6F6F6",
                            "&:hover": {backgroundColor: "#A9B5C6"},
                        }}
                        okButtonStartIcon={
                            copySuccess ? (
                                <DoneOutlinedIcon sx={{color: "white"}} />
                            ) : (
                                <ContentCopyOutlinedIcon sx={{color: "white"}} />
                            )
                        }
                        content={
                            <>
                                <Typography variant="body2" className="mt-2">
                                    <strong>Step 1.</strong> Select the image you’d like to use - your own or the
                                    Channel Program Partner logo.
                                </Typography>
                                <Typography variant="body2" className="mt-2">
                                    <strong>Step 2.</strong> Copy the code to your website. Refer to{" "}
                                    <u
                                        onClick={() => {
                                            window.open(
                                                "https://help.channelprogram.com/how-do-i-embed-a-button-to-my-channel-command-portal",
                                                "_blank",
                                            );
                                        }}
                                        style={{cursor: "pointer"}}>
                                        this article
                                    </u>{" "}
                                    for more information.
                                </Typography>
                                <FormProvider {...methods}>
                                    <LabelComponent
                                        text="Configuration Options"
                                        size="sm"
                                        position="start"
                                        color="#808080"
                                        sx={{
                                            fontWeight: "400 !important",
                                            fontSize: "14px !important",
                                        }}
                                    />
                                    <Grid container className="ms-0">
                                        <Grid item xs={6}>
                                            <CheckboxWithLabel
                                                className={`mt-0 h-auto`}
                                                id={"my_logo_image"}
                                                label={"Use my own image"}
                                                onChange={e => handleChecked(e)}
                                            />
                                        </Grid>
                                        <Grid item xs={6}>
                                            <Box sx={{display: "flex", flexDirection: "column", alignItems: "center"}}>
                                                <CheckboxWithLabel
                                                    className={`mt-0 h-auto`}
                                                    id={"cp_partner_logo"}
                                                    label={"Channel Command Partner Logo"}
                                                    onChange={e => handleChecked(e)}
                                                />
                                                <Box>
                                                    <img
                                                        src="Media/ChannelCommandPartnerLogo.png"
                                                        alt="channelCommandPartnerLogo"
                                                    />
                                                </Box>
                                            </Box>
                                        </Grid>
                                    </Grid>
                                    <CodeCopyTextBox
                                        valueToBeCopied={linkToBeCopied}
                                        label="EMBED CODE"
                                        showCopyButton={false}
                                        sxBeforeSuccess={{
                                            backgroundColor: "#0C2556",
                                            color: "#F6F6F6",
                                            "&:hover": {backgroundColor: "#0C2556"},
                                        }}
                                        sxAfterSuccess={{
                                            backgroundColor: "#0C2556",
                                            color: "#F6F6F6",
                                            "&:hover": {backgroundColor: "#0C2556"},
                                        }}
                                    />
                                </FormProvider>
                            </>
                        }
                    />
                    {!!showUploadModalType && (
                        <VendorContentModal
                            modalType={showUploadModalType}
                            sectionDetails={
                                partnerPageData?.[
                                    showUploadModalType
                                ] as IPartnerSection<TPartnerPageLayouts>[TPartnerPageLayouts]
                            }
                            isUpdating={isUpdatingData}
                            handleContent={handleContent}
                            companyId={companyId}
                            companyData={companyInfo}
                            onEditContentSuccess={onEditContentSuccess}
                            companySubdomain={companyInfo?.subdomain || ""}
                            showUploadModal={!!showUploadModalType}
                            setShowUploadModal={handleResetValues}
                            partnerPageData={partnerPageData}
                            layoutTypes={layoutTypes!}
                            searchKey={searchKey}
                            shouldRefech={isAddedNewData}
                            isChannelProgram={isChannelProgram}
                            editStates={{
                                blogToEditState: [blogToEdit, setBlogToEdit],
                                assetToEditState: [assetToEdit, setAssetToEdit],
                                itemToEditState: [itemToEdit, setItemToEdit],
                            }}
                            defaultPortalVisibility={currentTab.current}
                        />
                    )}
                </Box>
                <Box display={showWidgetSection ? "flex" : "none"} flexDirection="column" gap={2}>
                    {!isChannelProgramPRM && <NewDealRequestWidget />}
                    {partnerPage.data?.company_id && (
                        <UpcomingEventsWidget
                            company_id={activeCompany?.id}
                            isMSP={!activeCompany?.type_is_of_vendor}
                            filterById={partnerPage.data?.company_id}
                            isVisibleCallback={isVisible => {
                                setIsEventsEmpty(!isVisible);
                            }}
                        />
                    )}
                </Box>
            </Box>

            <FileUploadModal
                {...fileUploadModalConf}
                filterAditionalParams={filterAditionalParams}
                customControls={
                    isChannelProgramPRM ? (
                        <FormProvider {...bulkUploadMethods}>
                            <form onSubmit={e => e.preventDefault()}>
                                <Box sx={{display: "flex", flexDirection: "column", width: "100%", gap: 1}}>
                                    <Typography variant="body3" fontInter sx={{lineHeight: "19.36px"}}>
                                        {PORTAL_VISIBILITY_LABEL} {" *"}
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            gap: 2,
                                            "& .MuiTypography-root": {
                                                fontSize: "14px !important",
                                            },
                                            "& .MuiFormControlLabel-asterisk": {
                                                display: "none",
                                            },
                                        }}>
                                        {!!isChannelProgramPRM && (
                                            <RadioWithLabel
                                                label={VISIBILITY_OPTIONS.VENDORS_ONLY}
                                                id="media_visibility"
                                                value="vendor"
                                                validateOnTheFly
                                                required
                                            />
                                        )}
                                        <RadioWithLabel
                                            label={VISIBILITY_OPTIONS.MSP_ONLY}
                                            id="media_visibility"
                                            value="msp"
                                            validateOnTheFly
                                            required
                                        />
                                        {/* <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.INTERNAL_IT_ONLY}
                                        id="media_visibility"
                                        value="internal_it"
                                        validateOnTheFly
                                        required
                                    /> */}
                                        <RadioWithLabel
                                            label={VISIBILITY_OPTIONS.ALL_USERS}
                                            id="media_visibility"
                                            value="all"
                                            validateOnTheFly
                                            required
                                        />
                                    </Box>
                                </Box>
                            </form>
                        </FormProvider>
                    ) : undefined
                }
            />
            {!!addFolderModal && (
                <AddFolderDialog
                    onClose={() => setAddFolderModal(undefined)}
                    open={!!addFolderModal}
                    companyId={companyId}
                    defaultValues={addFolderModal}
                    onSave={foldersData.refetch}
                />
            )}
            {!!addContentToFolderModal && (
                <AddContentToFolder
                    onClose={() => setAddContentToFolderModal(undefined)}
                    open={!!addContentToFolderModal}
                    folderData={addContentToFolderModal}
                    companyId={companyId}
                    onSave={foldersData.refetch}
                    contents={allContents}
                    isLoading={allContentsLoading}
                />
            )}
            {!!addToFolderModal && (
                <AddToFolderDialog
                    onClose={() => setAddToFolderModal(undefined)}
                    open={!!addToFolderModal}
                    companyId={companyId}
                    selectedFile={addToFolderModal}
                    onSave={foldersData.refetch}
                />
            )}
            {showOrganizeFoldersModal && (
                <OrganizeFoldersDailog
                    onClose={() => setShowOrganizeFoldersModal(false)}
                    open={showOrganizeFoldersModal}
                    companyId={companyId}
                    onSave={foldersData.refetch}
                    allFoldersData={allFoldersData}
                />
            )}
            {!!showAddDocumentsModal && (
                <AddDocsToFolders
                    onClose={() => {
                        resetFilters();
                        setShowAddDocumentModal(false);
                    }}
                    open={showAddDocumentsModal}
                    companyId={companyId}
                    onSave={foldersData.refetch}
                    contents={allContents as IDocsToFoldersContent[]}
                    isLoadingContent={allContentsLoading}
                    allFoldersData={allFoldersData}
                    filters={[
                        {
                            id: "type",
                            label: "Type (Optional)",
                            multiSelect: true,
                            placeholder: "Select",
                            values: [
                                {id: "blog", name: "Blog Post"},
                                {id: "template", name: "Brandable Content"},
                                {id: "document", name: "Vendor Document"},
                                {id: "mediaGallery", name: "Vendor Asset"},
                                {id: "video", name: "Video"},
                            ],
                        },
                        {
                            id: "tags",
                            label: "Tags (Optional)",
                            multiSelect: true,
                            placeholder: "Select",
                            values: allTags || [],
                        },
                    ]}
                />
            )}
        </>
    );
}
