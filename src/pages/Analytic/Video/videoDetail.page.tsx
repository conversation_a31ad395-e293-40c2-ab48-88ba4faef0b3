import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useLocation, useParams} from "react-router";
import {Link} from "react-router-dom";
import {ICategory} from "../../../Interfaces/categories.interface";
import ButtonComponent from "../../../components/FormControls/button.component";
import TextBoxComponent from "../../../components/FormControls/textBox.component";
import {ArrowBackIcon, CloudDownloadIcon} from "../../../components/Icons";
import SubtitleContainer from "../../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import {MIME_TYPE} from "../../../constants/mimeTypes.constant";
import routeConfig from "../../../constants/routeConfig";
import useNotification from "../../../hooks/useNotification";
import useSettings from "../../../hooks/useSettings";
import {analyticService} from "../../../services/analytic.service";
import {categoriesService} from "../../../services/category.service";
import styles from "../../../styles/pages/analyticVideoDetail.module.sass";
import {downloadDoc} from "../../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {DATE_FORMAT, convertSecondsToMinutes, formatDate} from "../../../utils/formatDate";
import {abbreviateNumber} from "../../../utils/formatNumber.util";
import Loader from "../../../utils/loader";

interface ILocState {
    videoId: string;
    videoName: string;
}
interface IVideoDetailResponse {
    average_watch_time: number;
    total_view_count: number;
    total_viewers: number;
    authenticated_views: number;
    anonymous_views: number;
    categories: Array<string>;
    uploaded_date: string;
    uploader_name?: string;
    uploader_company?: string;
    uploader_company_handle?: string;
    company_friendly_url?: string;
    user_friendly_url?: string;
    duration?: number;
}
export default function VideoDetailPage() {
    const [loading, setLoading] = useState<{detail: boolean; downloading: boolean; viewers: boolean}>({
        detail: false,
        downloading: false,
        viewers: false,
    });
    const [videoDetail, setVideoDetail] = useState<IVideoDetailResponse>();
    const [categories, setCategories] = useState<Array<ICategory>>([]);

    const methods = useForm();
    const startDateWatcher = methods.watch("startDate");
    const endDateWatcher = methods.watch("endDate");
    const {settings, updateSettings} = useSettings();
    const location = useLocation();
    const locationState = location.state as ILocState;
    const {id} = useParams<{id: string}>();
    const videoId = id || locationState.videoId;
    const notify = useNotification();

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setLoading({viewers: false, downloading: false, detail: false});
    };

    const handleSuccess = (response: AxiosResponse<IVideoDetailResponse>) => {
        setVideoDetail(response.data);
        setLoading({...loading, detail: false});
    };

    const onSuccessCategories = (response: AxiosResponse<Array<ICategory>>) => {
        if (!response.data || response.data.length === 0) return;
        let newCategories: Array<[string, string]> = [];
        response.data.forEach(item => {
            newCategories = [...newCategories, [item.id, item.name]];
        });
        setCategories(response.data);
        updateSettings({cp_categories: newCategories});
        setLoading({...loading, detail: false});
    };

    const getVideoCategoriesNames = () => {
        const categoriesNames: Array<string> = [];
        if (!videoDetail || !videoDetail.categories || categories.length === 0) return "--";

        categories.forEach(item => {
            if (videoDetail?.categories.includes(item.id)) {
                categoriesNames.push(item.name);
            }
        });
        return categoriesNames.join();
    };

    const handleSuccessFilterByDate = (response: AxiosResponse<IVideoDetailResponse>) => {
        setVideoDetail(response.data);
        setLoading({...loading, viewers: false});
    };

    const buildDate = (date: string, isEndOfDay: boolean) => {
        if (date.includes("-")) {
            const dateArray = date.split("-");
            const stringDate = `${dateArray[2]}-${dateArray[1]}-${dateArray[0]} ${
                isEndOfDay ? "23:59:59" : "00:00:00"
            }`;
            return stringDate;
        } else {
            const dateArray = date.split("/");
            const stringDate = `${("0" + dateArray[1]).slice(-2)}-${("0" + dateArray[0]).slice(-2)}-${dateArray[2]} ${
                isEndOfDay ? "23:59:59" : "00:00:00"
            }`;
            return stringDate;
        }
    };

    const getViewsByDate = () => {
        setLoading({...loading, viewers: true});
        const startDate = startDateWatcher ? buildDate(startDateWatcher, false) : null;
        const endDate = endDateWatcher ? buildDate(endDateWatcher, true) : null;
        analyticService.getVideoViewsDetailByDate(videoId, startDate, endDate, handleSuccessFilterByDate, handleError);
    };

    const handleSuccessDownload = (response: AxiosResponse) => {
        downloadDoc(response.data, `video_${locationState?.videoName}_analytics.csv`);
        setLoading({...loading, downloading: false});
    };

    const downloadCsvFile = () => {
        let startDate: string | null = null;
        let endDate: string | null = null;
        if (startDateWatcher) {
            startDate = buildDate(startDateWatcher, false);
        }
        if (endDateWatcher) {
            endDate = buildDate(endDateWatcher, true);
        }
        setLoading({...loading, downloading: true});
        analyticService.downloadCsvFileVideoDetail(
            videoId,
            startDate,
            endDate,
            MIME_TYPE.CSV,
            handleSuccessDownload,
            handleError,
        );
    };

    const handleClear = () => {
        methods.reset({startDate: "", endDate: ""});
    };

    useEffect(() => {
        if (settings.cp_categories) {
            setCategories(settings.cp_categories.map(item => ({id: item[0], name: item[1]})));
        } else {
            setLoading({...loading, detail: true});
            categoriesService.getAllCategories(onSuccessCategories, handleError);
        }

        if (!videoId) return;
        setLoading({...loading, detail: true});
        analyticService.getVideoViewsDetail(videoId, handleSuccess, handleError);
        // eslint-disable-next-line
    }, [locationState?.videoId, id]);

    const renderDetails = (label: string, value: string) => {
        return (
            <div>
                <div className="card-body">
                    <TitleContainer as="h1" noConnector text={value} />
                    <SubtitleContainer as="p" text={label} />
                </div>
            </div>
        );
    };

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <div className="row p-2">
                    <div className="d-flex align-items-center">
                        <Link to={routeConfig.VideoAnalytics.path}>
                            <ArrowBackIcon size={60} />
                        </Link>
                        <TitleContainer
                            as="h1"
                            position="start"
                            text={`Name of the video: ${locationState?.videoName}`}
                        />
                    </div>
                </div>
                <div className="row p-2">
                    <div className="col-12">
                        <div className={`${styles.cardContainer}`}>
                            <div className={`card-body ${styles.cardContainer}`}>
                                <legend className="card-title">Video Detail</legend>
                                {loading.detail && <Loader loading version="iconOrange" inline />}
                                {!loading.detail && (
                                    <div className="d-flex flex-column">
                                        {videoDetail?.user_friendly_url && (
                                            <span className="mb-2">
                                                Uploader name:{" "}
                                                <Link
                                                    to={`${routeConfig.UserProfile.path.replaceAll(
                                                        ":id",
                                                        videoDetail.user_friendly_url,
                                                    )}`}>{`${
                                                    videoDetail?.uploader_name ? "@" + videoDetail.uploader_name : "--"
                                                }`}</Link>
                                            </span>
                                        )}
                                        {videoDetail?.company_friendly_url && (
                                            <span className="mb-2">
                                                Uploader company:{" "}
                                                <Link
                                                    to={routeConfig.VendorProfile.path.replaceAll(
                                                        ":id",
                                                        videoDetail.company_friendly_url,
                                                    )}>
                                                    {`${
                                                        videoDetail?.uploader_company
                                                            ? "@" + videoDetail.uploader_company
                                                            : "--"
                                                    }`}
                                                </Link>
                                            </span>
                                        )}
                                        <span className="mb-2">{`Date uploaded: ${formatDate(
                                            videoDetail?.uploaded_date!,
                                            DATE_FORMAT,
                                        )}`}</span>
                                        <span className="mb-2">{`Video duration: ${
                                            videoDetail?.duration && videoDetail?.duration <= 60
                                                ? Math.floor(videoDetail?.duration!) + " sec."
                                                : convertSecondsToMinutes(videoDetail?.duration!) + "min."
                                        }`}</span>
                                        <span className="mb-2">{`Video categories: ${getVideoCategoriesNames()}`}</span>
                                        <span className="mb-2">{`Average watch time: ${
                                            videoDetail?.average_watch_time ? videoDetail.average_watch_time : 0
                                        }`}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                <div className="row p-2">
                    <div className="col-12">
                        <div className={`${styles.cardContainer}`}>
                            <div className={`card-body ${styles.cardContainer}`}>
                                <legend className="card-title mb-4">Viewers Detail</legend>
                                <FormProvider {...methods}>
                                    <form
                                        className="d-flex gap-4 align-items-center w-100 mb-5"
                                        onSubmit={methods.handleSubmit(getViewsByDate)}>
                                        <TextBoxComponent label="Start Date" type="date" id="startDate" />
                                        <TextBoxComponent label="End Date" type="date" id="endDate" />
                                        <ButtonComponent
                                            type="submit"
                                            id="videoDetailSearch"
                                            className="cpBlueBtn"
                                            disabled={loading.detail || loading.viewers}
                                            buttonText="Search"
                                        />
                                        <ButtonComponent
                                            type="button"
                                            onClick={() => handleClear()}
                                            id="clearFilter"
                                            className="cpBlueAltBtn"
                                            buttonText="Clear"
                                            disabled={loading.detail || loading.viewers}
                                        />
                                        {loading.viewers && (
                                            <Loader loading={loading.viewers} inline version="iconOrange" />
                                        )}
                                    </form>
                                </FormProvider>
                                <div className="d-flex justify-content-between">
                                    {renderDetails(
                                        "Total View Count",
                                        videoDetail?.total_view_count
                                            ? abbreviateNumber(videoDetail?.total_view_count)
                                            : "0",
                                    )}
                                    {renderDetails(
                                        "Total Viewers",
                                        videoDetail?.total_viewers ? abbreviateNumber(videoDetail?.total_viewers) : "0",
                                    )}
                                    {renderDetails(
                                        "Authenticated",
                                        videoDetail?.authenticated_views
                                            ? abbreviateNumber(videoDetail?.authenticated_views)
                                            : "0",
                                    )}
                                    {renderDetails(
                                        "Anonymous",
                                        videoDetail?.anonymous_views
                                            ? abbreviateNumber(videoDetail?.anonymous_views)
                                            : "0",
                                    )}
                                </div>
                                <div className="d-flex flex-column juntify-content-center">
                                    <div className={styles.dropBox}>
                                        <div className={styles.cloudIcon}>
                                            <span onClick={downloadCsvFile}>
                                                <CloudDownloadIcon size={50} />
                                            </span>
                                        </div>
                                        <TitleContainer
                                            as="h2"
                                            noConnector
                                            text={`${loading.downloading ? "Downloading..." : "Download CSV File"}`}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
