import {
    DownloadOutlined,
    FormatListBulleted,
    GridOnOutlined,
    PictureAsPdfOutlined,
    ViewListOutlined,
} from "@mui/icons-material";
import type {AxiosResponse} from "axios";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import {useNaviStack} from "../../hooks/fetches/useNaviStack";
import {useQueryHelper} from "../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../hooks/useActiveCompany";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {IStackCategorization} from "../../Interfaces/myStack.interface";
import {stackService} from "../../services/stack.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ManageStack from "../../uicomponents/Organism/ManageStack/ManageStack.component";
import MSPProductClassification from "../../uicomponents/Organism/MSPProductClassification/MSPProductClassification.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {generateActivityLog} from "../../utils/customTracking.util";
import {downloadDoc} from "../../utils/downloadDoc.util";
import fuzzySearch from "../../utils/fuzzySearch.util";
import Loader from "../../utils/loader";
import {useTheme} from "@mui/material";

import AddCustomerProductModal from "../../uicomponents/Organism/AddCustomerProductModal/AddCustomerProductModal.component";
import CustomerViewStackModal from "../../uicomponents/Organism/CustomerViewStackModal/CustomerViewStackModal.component";

type ViewMode = "category-view" | "list-view";

const INITIAL_STACK_MODAL_PROPS = {
    open: false,
    showError: false,
    errorMessage: "",
    errorDescription: "",
    defaultCategory: undefined,
    defaultProduct: undefined,
    stack: undefined,
};
const INITIAL_VIEW_STACK_MODAL_PROPS = {
    open: false,
    stack: undefined,
};

interface IAddToStackForm {
    product: {id: string; label: string; isCreatableOption?: boolean} | null;
    vendor: {id: string; label: string; isCreatableOption?: boolean} | null;
    vendor_id: string;
    product_id: string;
    vendor_name: string;
    product_name: string;
    category: string;
    subcategory: string;
    description: string;
    create: boolean;
}

export default function CustomerNavistack() {
    const searchState = useState<string>("");
    const [removing, setRemoving] = useState<boolean>(false);
    const [saving, setSaving] = useState<boolean>(false);
    const [viewMode, setViewMode] = useState<ViewMode>("category-view");
    const [addToStackModalProps, setAddToStackModalProps] = useState<{
        open: boolean;
        showError: boolean;
        errorMessage?: string;
        errorDescription?: string;
        defaultCategory?: string;
        defaultProduct?: any;
        stack?: IStackCategorization;
    }>(INITIAL_STACK_MODAL_PROPS);
    const [viewStackModalProps, setViewStackModalProps] = useState<{
        open: boolean;
        stack?: IStackCategorization;
    }>(INITIAL_VIEW_STACK_MODAL_PROPS);

    const notify = useNotification();
    const {activeCompany} = useActiveCompany();
    const {invalidateMatchedQueries} = useQueryHelper();
    const methods = useForm<IAddToStackForm>();
    const viewMethods = useForm<IStackCategorization>();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MSP_CLIENT_PRODUCTS_UPDATE]);

    const handleError = (err: any) => {
        const message = err.response.data.message.split("|");
        setAddToStackModalProps(prev => ({...prev, errorMessage: message[0], errorDescription: message[1] ?? ""}));
    };

    const {naviStack, naviStackCategories, numOfCategoriesFilled} = useNaviStack(activeCompany?.id || "", {
        isCompany: true,
        customerStack: true,
        companyType: activeCompany?.company_type_id,
        calls: {
            all: false,
            naviStack: true,
            naviStackCategories: true,
        },
    });
    const search = searchState[0];
    const filteredNaviStack =
        search && naviStack.data
            ? {
                  ...naviStack,
                  data: fuzzySearch(search, naviStack.data, [
                      "category.name",
                      "product.name",
                      "stack_company.name",
                  ] as any).results,
              }
            : naviStack;

    const uniqueVendors = new Set<string>();
    filteredNaviStack.data?.forEach(stack => {
        uniqueVendors.add(stack.stack_company_id);
    });

    const onClickExportCSV = () => {
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        stackService.generateCustomerCSV(
            activeCompany?.id || "",
            (res: AxiosResponse) => {
                downloadDoc(res.data, "YourStack" + ".csv");
            },
            handleError,
        );
    };

    const onClickDownloadPDF = () => {
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        stackService.generateCustomerPDF(
            activeCompany?.id || "",
            (res: AxiosResponse) => {
                downloadDoc(res.data, "YourStack" + ".pdf");
            },
            handleError,
        );
    };

    const removeStackProduct = async (stack: IStackCategorization) => {
        const answer = await getUserConfirmation("Remove Product?", {
            mui: true,
            modalTheme: "error",
            title: "Remove Product?",
            form: (
                <Typography variant="body4" textAlign="center">
                    Are you sure you want to remove {stack.stack_company?.name} {stack.product?.name + " "}
                    from your technology stack?
                </Typography>
            ),
            okButtonText: "Remove",
            customModalBtnTracking: generateActivityLog(stack, "stack", activeCompany?.id),
        });
        if (!answer.value) return;
        setRemoving(true);
        stackService
            .deleteCustomerStack(activeCompany?.id || "", {id: stack.id})
            .then(() => {
                notify("Product removed successfully", "Success");
                setViewStackModalProps(INITIAL_VIEW_STACK_MODAL_PROPS);
                invalidateMatchedQueries(["naviStack", activeCompany?.id]);
            })
            .catch(handleError)
            .finally(() => setRemoving(false));
    };

    const handleEditStack = (stack: IStackCategorization) => {
        methods.reset({
            category: stack.parent_category_id,
            subcategory: stack.category_id,
            product: {
                id: stack.product_id,
                label: stack.product.name,
            },
            description: stack.product.description || "",
            vendor: {
                id: stack.stack_company_id,
                label: stack.stack_company.name,
            },
            vendor_id: stack.stack_company.name,
            product_id: stack.product.name,
            vendor_name: stack.stack_company.name,
            product_name: stack.product.name,
            create: false,
        });
        setSaving(false);
        setRemoving(false);
        setViewStackModalProps(INITIAL_VIEW_STACK_MODAL_PROPS);
        setAddToStackModalProps({
            open: true,
            showError: false,
            defaultCategory: stack.category_id,
            defaultProduct: stack.product.name,
            stack,
        });
    };

    if (!activeCompany) {
        return (
            <Box className={ROUTE_CLASS}>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    className={ROUTE_CONTAINER_CLASS}>
                    <Loader inline loading big />
                </Box>
            </Box>
        );
    }

    return (
        <Box className={ROUTE_CLASS}>
            <Box className={ROUTE_CONTAINER_CLASS}>
                <PageInnerHeader
                    id="customer-navistack"
                    title={{
                        text: "ProductTracker",
                        sx: {
                            fontSize: "26px!important",
                        },
                    }}
                    subtitle={{
                        text: "Start now by adding products and categories your company uses.",
                        sx: {
                            marginTop: "24px!important",
                            fontSize: "16px!important",
                            fontWeight: "400",
                            color: "neutral.800",
                        },
                    }}
                    searchState={searchState}
                    growSearch
                    actions={{
                        primary: {
                            children: <>+ Add Product</>,
                            id: "add-product",
                            permissions: [PERMISSION_GROUPS.MSP_CLIENT_PRODUCTS_UPDATE],
                            onClick: () => {
                                methods.reset({
                                    category: "",
                                    subcategory: "",
                                    product: null,
                                    description: "",
                                    vendor: null,
                                    vendor_id: "",
                                    product_id: "",
                                    create: true,
                                });
                                setAddToStackModalProps({
                                    open: true,
                                    showError: false,
                                    errorMessage: "",
                                    errorDescription: "",
                                });
                            },
                        },
                        secondary: {
                            id: "export",
                            children: (
                                <>
                                    <DownloadOutlined color="secondary" sx={{width: "18px", height: "18px"}} />
                                    Export
                                </>
                            ),
                            permissions: [PERMISSION_GROUPS.DATA_EXPORT],
                            items: [
                                {
                                    id: "export-client-csv",
                                    children: (
                                        <Box display="flex" alignItems="center" gap="16px">
                                            <ViewListOutlined color="neutral" />
                                            <Typography
                                                variant="subtitle4"
                                                fontInter
                                                fontWeight={400}
                                                sx={theme => ({
                                                    color: theme.palette.secondary.main,
                                                })}>
                                                Download .CSV
                                            </Typography>
                                        </Box>
                                    ),
                                    onClick: onClickExportCSV,
                                },
                                {
                                    id: "export-client-pdf",
                                    children: (
                                        <Box display="flex" alignItems="center" gap="16px">
                                            <PictureAsPdfOutlined color="neutral" />
                                            <Typography
                                                variant="subtitle4"
                                                fontInter
                                                fontWeight={400}
                                                sx={theme => ({
                                                    color: theme.palette.secondary.main,
                                                })}>
                                                Export PDF
                                            </Typography>
                                        </Box>
                                    ),
                                    onClick: onClickDownloadPDF,
                                },
                            ],
                        },
                        tabs: {
                            id: "view-modes",
                            props: {
                                tabs: [
                                    {
                                        id: "category-view",
                                        label: (
                                            <Box display="flex" alignItems="center">
                                                <GridOnOutlined sx={{marginRight: 1}} />
                                                Category View
                                            </Box>
                                        ),
                                    },
                                    {
                                        id: "list-view",
                                        label: (
                                            <Box display="flex" alignItems="center">
                                                <FormatListBulleted sx={{marginRight: 1}} /> List View
                                            </Box>
                                        ),
                                    },
                                ] as any,
                                defaultTab: "category-view",
                                handleChange: tab => {
                                    setViewMode(tab.id as ViewMode);
                                },
                                hideIndicator: true,
                            },
                        },
                    }}
                    actionAsFilters={["tabs"]}
                />
                {viewMode === "list-view" ? (
                    <ManageStack
                        companyId={activeCompany?.id}
                        openAddToStack={(parent_category_id?: string) => {
                            if (parent_category_id) {
                                methods.reset({category: parent_category_id, create: true});
                            }
                            setAddToStackModalProps({
                                open: true,
                                showError: false,
                                errorMessage: "",
                                errorDescription: "",
                                defaultCategory: parent_category_id,
                            });
                        }}
                        searchState={searchState}
                        customerStack
                        showCount
                        companyType={activeCompany?.company_type_id}
                        naviStack={filteredNaviStack}
                        uniqueVendors={Array.from(uniqueVendors)}
                        onViewStackDetails={(stack: IStackCategorization) => {
                            viewMethods.reset(stack);
                            setViewStackModalProps({
                                open: true,
                                stack,
                            });
                        }}
                        onEditStackDetails={(stack: IStackCategorization) => {
                            handleEditStack(stack);
                        }}
                    />
                ) : (
                    <MSPProductClassification
                        className="my-4"
                        companyId={activeCompany?.id}
                        companyFriendlyUrl={activeCompany?.friendly_url}
                        onClickAddToStack={(category_id?: string, product?: any) => {
                            if (category_id) {
                                const stackEntry = naviStack.data?.find(stack => stack.category_id === category_id);
                                const categoriesEntry = naviStackCategories.data?.find(c =>
                                    c["sub-categories"].some(s => s.id === category_id),
                                );
                                methods.reset({
                                    category: stackEntry?.parent_category_id || categoriesEntry?.id || "",
                                    subcategory: category_id,
                                    create: true,
                                });
                            }
                            setAddToStackModalProps({
                                open: true,
                                showError: false,
                                defaultCategory: category_id,
                                defaultProduct: product,
                            });
                        }}
                        searchState={searchState}
                        customerStack
                        companyType={activeCompany?.company_type_id}
                        naviStack={filteredNaviStack}
                        naviStackCategories={naviStackCategories}
                        numOfCategoriesFilled={numOfCategoriesFilled}
                        onClickProductDetails={(stack: IStackCategorization) => {
                            viewMethods.reset(stack);
                            setViewStackModalProps({
                                open: true,
                                stack,
                            });
                        }}
                    />
                )}
            </Box>
            <AddCustomerProductModal
                open={addToStackModalProps.open}
                onClose={() => {
                    methods.reset({
                        category: "",
                        subcategory: "",
                        product: null,
                        description: "",
                        vendor: null,
                        vendor_id: "",
                        product_id: "",
                        create: false,
                    });
                    setAddToStackModalProps(INITIAL_STACK_MODAL_PROPS);
                }}
                saving={saving}
                setSaving={setSaving}
                companyId={activeCompany?.id || ""}
                defaultCategory={addToStackModalProps.defaultCategory}
                defaultProduct={addToStackModalProps.defaultProduct}
                stack={addToStackModalProps.stack}
                errorMessage={addToStackModalProps.errorMessage}
                errorDescription={addToStackModalProps.errorDescription}
                onError={handleError}
            />
            <CustomerViewStackModal
                open={viewStackModalProps.open}
                stack={viewStackModalProps.stack}
                removing={removing}
                hasEditAccess={hasEditAccess}
                companyType={activeCompany?.company_type_id}
                onClose={() => setViewStackModalProps(INITIAL_VIEW_STACK_MODAL_PROPS)}
                onRemove={removeStackProduct}
                onEdit={handleEditStack}
            />
        </Box>
    );
}
