import useTheme from "@mui/material/styles/useTheme";
import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useEffect, useRef, useState} from "react";
import Helmet from "react-helmet";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation, useNavigate, useParams} from "react-router-dom";
import {CSSTransition, SwitchTransition} from "react-transition-group";
import {IProducts} from "../../Interfaces/products.interface";
import {IFormAnswer, IReviewForUserType, ReviewQuestion} from "../../Interfaces/reviews.interface";
import {AnalyticAction} from "../../constants/analitycs.constant";
import {AnalyticSubjectTypes} from "../../constants/analyticSubjectTypes.constant";
import {
    getReferralId,
    REVIEW_TITLE_MIN_CHARACTERS,
    REVIEW_TITLE_REQUIRED,
    ROUTE_CLASS,
    ROUTE_CONTAINER_CLASS,
} from "../../constants/commonStrings.constant";
import {QUESTION_KEYS, QUESTION_TYPES, REVIEW_STATUS} from "../../constants/reviews.constant";
import routeConfig from "../../constants/routeConfig";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAppConfig from "../../hooks/useAppConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import {useRestrictCopyPaste} from "../../hooks/useRestrictCopyPaste";
import {analyticService} from "../../services/analytic.service";
import {productService} from "../../services/products.service";
import {reviewService} from "../../services/review.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import CheckboxWithLabel from "../../uicomponents/Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import ProgressBar from "../../uicomponents/Molecules/ProgressBar/progressBar.component";
import ReviewQuestionBuilder from "../../uicomponents/Organism/ReviewQuestionBuilder/reviewQuestionBuilder.component";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../utils/error.util";
import ErrorBoundary from "../../utils/errorBoundary.util";
import Loader from "../../utils/loader";
import useQuery from "../../utils/query.util";

const CreateReview = () => {
    useRestrictCopyPaste({window, actions: import.meta.env.MODE === "development" ? [] : ["paste"]});
    const [step, setStep] = useState<number>(1);
    const [productInfo, setProductInfo] = useState<IProducts>();
    const [questions, setQuestions] = useState<ReviewQuestion[]>([]);
    const [isFetchingComplete, setIsFetchingComplete] = useState<boolean>(false);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [previous, setPrevious] = useState<any>();
    // const [isSendingStepOne, setIsSendingStepOne] = useState<boolean>(false);
    const [isSyncingAnswers, setIsSyncingAnswers] = useState<boolean>(false);
    const [percentage, setPercentage] = useState(0);
    const {authState} = useAuthState();
    const defaultJobTitle: any = authState.user?.job_title;

    const {friendly_url} = useParams();
    const {search} = useLocation();
    const query = useQuery();
    const reviewForm: any = useRef<any>({});
    const stepOneId = useRef("");
    const methods = useForm();
    const navigate = useNavigate();
    const notify = useNotification();
    const {userCompanies} = useActiveCompany();
    const {configs, isLoadingConfig} = useAppConfig({
        config_key: ["REVIEW_QUESTIONS_PER_PAGE", "PREVENT_EDIT_REVIEWS_BEFORE_DATE"],
    });
    const theme = useTheme();
    const configQuestionsPerPage: string = configs.find(c => c.key === "REVIEW_QUESTIONS_PER_PAGE")?.value || "5";
    const preventEditBeforeDate: string = configs.find(c => c.key === "PREVENT_EDIT_REVIEWS_BEFORE_DATE")?.value || "";
    const questions_per_page = Number(configQuestionsPerPage) || 5;
    const nonAboutYouQuestions = questions.filter(q => !q.is_about_you_question);
    const aboutYouQuestions = questions.filter(q => q.is_about_you_question);
    const last_review_page = Math.ceil(nonAboutYouQuestions?.length / questions_per_page) + 1;
    const recommended_id = "recommended_by_reviewer_value";
    const job_title_id = "job_title_id";
    const terms_of_use_id = "termsOfUse";
    const additional_text_append = "_additional_text";
    const validationSteps = new Array(last_review_page)
        ?.fill(null)
        ?.reduce((prev: any, cur: ReviewQuestion, i: number) => {
            const stepToValidate = i + 1;
            let questionIds: string[] = [];
            if (stepToValidate === 1) {
                const qIds: string[] = [];
                [...nonAboutYouQuestions].slice(0, questions_per_page)?.map(q => {
                    qIds.push(q.question_key === QUESTION_KEYS.RECOMMENDATION_RATING ? recommended_id : q.id);
                    if (q.options.find(o => o.show_answer_option)) {
                        qIds.push(q.id + additional_text_append);
                    }
                });
                questionIds = ["title", ...qIds];
            } else if (stepToValidate === last_review_page) {
                questionIds = [...aboutYouQuestions?.map(q => q.id), terms_of_use_id];
            } else {
                const qIds: string[] = [];
                [...nonAboutYouQuestions]
                    .slice((stepToValidate - 1) * questions_per_page, stepToValidate * questions_per_page)
                    ?.map(q => {
                        qIds.push(q.question_key === QUESTION_KEYS.RECOMMENDATION_RATING ? recommended_id : q.id);
                        if (q.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                            q.options?.forEach(o => {
                                qIds.push(o.id + additional_text_append);
                            });
                        } else if (q.options.find(o => o.show_answer_option)) {
                            qIds.push(q.id + additional_text_append);
                        }
                    });
                questionIds = qIds;
            }
            return {...prev, [stepToValidate]: questionIds};
        }, {});
    const isEditingReview = previous?.id && previous.status !== "abandoned";

    const generatePayloadAnswers = () => {
        const answers: IFormAnswer[] = [];
        questions.forEach((q: ReviewQuestion) => {
            const review_question_id = q.id;
            if (
                q.question_type === QUESTION_TYPES.SINGLE_ANSWER ||
                q.question_type === QUESTION_TYPES.STAR_RATING ||
                q.question_type === QUESTION_TYPES.RADIO_BUTTONS
            ) {
                if (q.question_key !== QUESTION_KEYS.JOB_TITLE) {
                    const isRecommended = q.question_key === QUESTION_KEYS.RECOMMENDATION_RATING;
                    const recommended_id_value = isRecommended
                        ? q.options.find(o => o.order === reviewForm.current[recommended_id])?.id
                        : undefined;
                    answers.push({
                        review_question_id,
                        review_question_option_ids: [
                            isRecommended ? recommended_id_value : reviewForm.current[review_question_id],
                        ],
                        answer: reviewForm.current[`${review_question_id}${additional_text_append}`] || undefined,
                    });
                }
            } else if (q.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                const foundAnswers: any[] = [];
                const idsToSend = Object.keys(reviewForm.current[review_question_id] || {})?.filter(key => {
                    const valueAtKey = reviewForm.current[review_question_id][key];
                    const answerAtKey = reviewForm.current[`${key}${additional_text_append}`];
                    if (valueAtKey) {
                        if (answerAtKey) {
                            foundAnswers.push(answerAtKey);
                        } else {
                            foundAnswers.push(null);
                        }
                    }
                    return valueAtKey;
                });
                answers.push({
                    review_question_id,
                    review_question_option_ids: idsToSend,
                    answers: foundAnswers || undefined,
                });
            } else if (q.question_type === QUESTION_TYPES.SCALE) {
                answers.push({
                    review_question_id,
                    review_question_option_ids: q.options?.filter(o => reviewForm.current[o.id])?.map(o => o.id) || [],
                });
            } else if (
                q.question_type === QUESTION_TYPES.OPEN_TEXT ||
                q.question_type === QUESTION_TYPES.SINGLE_LINE_TEXT
            ) {
                answers.push({
                    review_question_id,
                    answer: reviewForm.current[review_question_id],
                });
            }
        });
        const errors = methods.formState.errors;
        return answers.filter(a => {
            return (
                (!errors[a.review_question_id] &&
                    (a.review_question_option_ids?.filter(v => Boolean(v))?.length || 0) > 0) ||
                a.answer
            );
        });
    };

    const setDefaultAnswerValues = (
        questionsToCheck: any,
        answersToCheck: any,
        updateReviewFormRef: boolean = false,
    ) => {
        methods.setValue(
            recommended_id,
            reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
        );
        questionsToCheck.forEach((question: ReviewQuestion) => {
            if (
                question.question_type === QUESTION_TYPES.STAR_RATING ||
                question.question_type === QUESTION_TYPES.SINGLE_ANSWER ||
                question.question_type === QUESTION_TYPES.RADIO_BUTTONS
            ) {
                const foundAnswer = answersToCheck?.find(a => a.review_question_id === question.id);
                const value = reviewForm.current[question.id] || foundAnswer?.review_question_option_id;
                const additionalTextBoxValue =
                    reviewForm.current[question.id + additional_text_append] || foundAnswer?.answer;
                if (additionalTextBoxValue) {
                    methods.setValue(question.id + additional_text_append, additionalTextBoxValue);
                }
                methods.setValue(question.id, value);
                if (updateReviewFormRef) {
                    reviewForm.current[question.id] = value;
                    if (additionalTextBoxValue) {
                        reviewForm.current[question.id + additional_text_append] = additionalTextBoxValue;
                    }
                }
            } else if (question.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                const foundAnswers = answersToCheck?.filter(a => a.review_question_id === question.id) || [];
                const newValue = {};
                const newAdditionalTextValues = {};
                question.options.forEach(o => {
                    const foundInPrevious = foundAnswers.find(a => a.review_question_option_id === o.id);
                    const value =
                        reviewForm.current[question.id]?.[o.id] !== undefined
                            ? reviewForm.current[question.id][o.id]
                            : foundInPrevious
                            ? true
                            : false;
                    newValue[o.id] = value;
                    const additionalTextId = o.id + additional_text_append;
                    if (foundInPrevious?.answer || reviewForm.current[additionalTextId]) {
                        newAdditionalTextValues[additionalTextId] =
                            reviewForm.current[additionalTextId] || foundInPrevious.answer;
                        methods.setValue(
                            additionalTextId,
                            reviewForm.current[additionalTextId] || foundInPrevious.answer,
                        );
                    }
                });
                methods.setValue(question.id, newValue);
                if (updateReviewFormRef) {
                    reviewForm.current[question.id] = newValue;
                    if (newAdditionalTextValues) {
                        reviewForm.current = {...reviewForm.current, ...newAdditionalTextValues};
                    }
                }
            } else {
                const foundAnswer = answersToCheck?.find(a => a.review_question_id === question.id);
                const value =
                    reviewForm.current[question.id] || foundAnswer?.answer || foundAnswer?.review_question_option_id;
                methods.setValue(question.id, value);
                if (updateReviewFormRef) {
                    reviewForm.current[question.id] = value;
                }
            }
        });
        calculatePercentage();
    };

    const calculatePercentage = () => {
        const values = methods.getValues();
        const nonRequiredQuestions = questions.filter(q => q.is_required);
        let numOfValidFields = nonRequiredQuestions.filter(q => {
            const question_id =
                q.question_key === QUESTION_KEYS.RECOMMENDATION_RATING
                    ? recommended_id
                    : q.id === QUESTION_KEYS.JOB_TITLE
                    ? job_title_id
                    : q.id;
            if (q.question_type === QUESTION_TYPES.MULTIPLE_ANSWER) {
                return (
                    Object.values(values[question_id] || {}).filter(v => v)?.length > 0 ||
                    Object.values(reviewForm.current[question_id] || {}).filter(v => v)?.length > 0
                );
            }
            return values[question_id] || reviewForm.current[question_id];
        })?.length;
        if (values["title"] || reviewForm.current["title"]) {
            numOfValidFields++;
        }
        if (values[terms_of_use_id] || reviewForm.current[terms_of_use_id]) {
            numOfValidFields++;
        }
        if (values[job_title_id] || reviewForm.current[job_title_id]) {
            numOfValidFields++;
        }
        if (values[recommended_id] || reviewForm.current[recommended_id]) {
            numOfValidFields++;
        }
        setPercentage((numOfValidFields / (nonRequiredQuestions.length + 3)) * 100);
    };

    const goToNextStep = async () => {
        const formValues = methods.getValues();
        const valid = await methods.trigger(validationSteps[step]);
        if (!valid) return;
        reviewForm.current = {...reviewForm.current, ...formValues};
        //? Checking for a previously reviewed product that is the same product that is currently being reviewed
        if (step === 1 && !isEditingReview) {
            const {title, model_id} = reviewForm.current;
            setIsSyncingAnswers(true);
            await reviewService.storeStepOne(
                {
                    title,
                    model_id: productInfo?.id || model_id,
                    model_type: "product",
                    incentivize: false,
                    hide_reviewer_name: false,
                },
                (res: AxiosResponse) => {
                    if (res.data.id) {
                        stepOneId.current = res.data.id;
                        reviewService
                            .syncAnswers(
                                res.data.id,
                                reviewForm.current[job_title_id] || defaultJobTitle?.id,
                                generatePayloadAnswers(),
                                reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
                                false,
                                () => null,
                                handleError,
                            )
                            .finally(() => {
                                setIsSyncingAnswers(false);
                            });
                    } else {
                        setIsSyncingAnswers(false);
                    }
                },
                () => {
                    setIsSyncingAnswers(false);
                },
            );
        } else if (step !== last_review_page && !isEditingReview) {
            setIsSyncingAnswers(true);
            reviewService
                .syncAnswers(
                    stepOneId.current,
                    reviewForm.current[job_title_id] || defaultJobTitle?.id,
                    generatePayloadAnswers(),
                    reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
                    false,
                    () => null,
                    handleError,
                )
                .finally(() => {
                    setIsSyncingAnswers(false);
                });
        }
        if (step === last_review_page) {
            if (isEditingReview) {
                setIsSubmitting(true);
                const newAnswers = generatePayloadAnswers();
                if (
                    reviewForm.current.title !== previous.title ||
                    reviewForm.current.recommended_by_reviewer_value !== previous[recommended_id]
                ) {
                    Promise.all([
                        reviewService.updateReview({
                            id: previous.id,
                            title: reviewForm.current.title,
                            recommended_by_reviewer_value: Number(reviewForm.current[recommended_id]),
                        }),
                        reviewService.syncAnswers(
                            previous?.id,
                            reviewForm.current[job_title_id] || defaultJobTitle?.id,
                            newAnswers,
                            reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
                            true,
                        ),
                    ])
                        .then(() => {
                            handleSuccessEditReview();
                        })
                        .catch(errs => {
                            handleError(errs[0]);
                            handleError(errs[1]);
                        });
                } else {
                    reviewService.syncAnswers(
                        previous?.id,
                        reviewForm.current[job_title_id] || defaultJobTitle?.id,
                        newAnswers,
                        reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
                        true,
                        handleSuccessEditReview,
                        handleError,
                    );
                }
            } else {
                setIsSubmitting(true);
                return reviewService.storeStepTwo(
                    {
                        //? Other Form fields
                        answers: generatePayloadAnswers(),
                        id: stepOneId.current,
                        recommended_by_reviewer_value: Number(reviewForm.current[recommended_id]),
                        job_title_id: reviewForm.current?.["job_title_id"],
                    },
                    handleSuccessCreateReview,
                    handleError,
                );
            }
        }
        setStep(step + 1);
        window.scrollTo(0, 0);
    };

    const handleSecondaryAction = async () => {
        if (!isEditingReview) {
            if (step === 1) {
                const answer = await getUserConfirmation(
                    "Are you sure you want to abandon this review? If yes, click Abandon to save your progress and you can always finish it at a later date.",
                    {
                        cancelButtonText: "CANCEL",
                        okButtonText: "ABANDON",
                        title: "Abandon Review",
                        mui: true,
                        modalTheme: "blue",
                    },
                );
                if (answer.value) {
                    productInfo
                        ? navigate(routeConfig.ProductDetails.path.replace(":friendly_url", productInfo.friendly_url))
                        : navigate(routeConfig.ProductReviewSearch.path);
                }
                return;
            }
            const formValues = methods.getValues();
            reviewForm.current = {...reviewForm.current, ...formValues};
            setIsSyncingAnswers(true);
            reviewService
                .syncAnswers(
                    stepOneId.current,
                    reviewForm.current[job_title_id] || defaultJobTitle?.id,
                    generatePayloadAnswers(),
                    reviewForm.current?.[recommended_id] || Number(previous?.recommended_by_reviewer_value),
                    false,
                    () => null,
                    handleError,
                )
                .finally(() => {
                    setIsSyncingAnswers(false);
                });
            setStep(step - 1);
            return;
        } else {
            if (step === 1) {
                const answer = await getUserConfirmation("", {
                    cancelButtonText: "CANCEL",
                    okButtonText: "LEAVE",
                    title: "Cancel Confirmation",
                    mui: true,
                    modalTheme: "blue",
                    form: (
                        <Box className="d-flex flex-column" gap={2}>
                            <Typography
                                fontInter
                                variant="body3"
                                sx={{color: theme.palette.neutral[800], textAlign: "center"}}>
                                Are you sure you want to leave this review?.
                                <br />
                                Any changes made may be lost
                            </Typography>
                        </Box>
                    ),
                });
                if (answer.value) {
                    return navigate({pathname: routeConfig.Account.path, search: "?tab=myReviews"});
                }
            } else {
                setStep(step - 1);
            }
        }
    };

    const handleSuccessGetProduct = (res: AxiosResponse) => {
        setProductInfo(res.data);
        reviewForm.current = {...reviewForm.current, model_id: res.data.id};
    };

    const handleSuccessGetQuestions = (res: AxiosResponse) => {
        const ordered = res.data.sort((a: any, b: any) => a.order - b.order);
        setQuestions(ordered);
    };

    const handleSuccessGetOldReview = useCallback(
        (res: AxiosResponse) => {
            if (!res.data.answers) return;
            if (new Date(res.data?.created_at) < new Date(preventEditBeforeDate)) {
                handleAlreadyCreatedReview();
                return;
            }
            setPrevious(res.data);
        },
        [preventEditBeforeDate],
    );

    const handleSuccessGetLastReviewed = (r: IReviewForUserType, q: any) => {
        if (!r.answers) return;
        const previousAboutYouAnswers = r.answers?.filter(a => a.review_question.is_about_you_question);
        if (previousAboutYouAnswers.length > 0) {
            setDefaultAnswerValues(
                q.filter(q => q.is_about_you_question),
                previousAboutYouAnswers,
                true,
            );
        }
    };

    const handleSuccessCreateReview = () => {
        const referral_id = getReferralId();
        if (referral_id && productInfo?.id) {
            analyticService.storeBasicReferral(
                AnalyticAction.REFERRED_REVIEW,
                productInfo.id,
                AnalyticSubjectTypes.PRODUCT,
                navigator.userAgent,
                {
                    referral_id,
                },
            );
        }
        setIsSubmitting(false);
        notify("Review created successfully", "Success");
        navigate(routeConfig.CompletedReview.path, {
            state: {
                friendly_url,
                id: productInfo?.id,
            },
        });
    };

    const handleSuccessEditReview = () => {
        setIsSubmitting(false);
        notify("Review updated successfully", "Success");
        navigate(
            routeConfig.ProductDetails.path.replace(":friendly_url", previous.model.friendly_url) +
                `?review_id=${previous?.id || ""}&r=1`,
        );
    };

    const handleAlreadyCreatedReview = async () => {
        const answer = await getUserConfirmation(
            "You have reviewed this product before and can only submit one review per product. Visit “My Product Reviews” on My Account to see all your product reviews.",
            {
                cancelButtonText: "Not now",
                okButtonText: "Go to My Product Reviews",
                title: "Review already created",
                mui: true,
                modalTheme: "blue",
            },
        );
        if (answer.value) {
            return navigate({pathname: routeConfig.Account.path, search: "?tab=myReviews"});
        }
        return navigate(-1);
    };

    const handleError = (error: AxiosError) => {
        notify(getErrorFromArray(error), "Error");
        setIsSubmitting(false);
    };

    const handleErrorGetProduct = () => {
        notify("That product could not be found", "Error");
        navigate(routeConfig.ProductReviewSearch.path);
    };

    useEffect(() => {
        if (questions) {
            calculatePercentage();
        }
    }, [questions]);

    useEffect(() => {
        if (productInfo?.id && !search.includes("mode=edit")) {
            reviewService
                .preCheckReview(productInfo?.id)
                .then(r => {
                    if (r.data.can_create_review === false) {
                        handleAlreadyCreatedReview();
                    } else {
                        setIsFetchingComplete(true);
                    }
                })
                .catch(handleError);
        } else if (productInfo?.id) {
            setIsFetchingComplete(true);
        }
    }, [productInfo]);

    useEffect(() => {
        methods.setValue("title", reviewForm.current?.title ? reviewForm.current.title : previous?.title);
        methods.setValue(
            "recommended_by_reviewer_value",
            reviewForm.current?.recommended_by_reviewer_value
                ? String(reviewForm.current.recommended_by_reviewer_value)
                : String(previous?.recommended_by_reviewer_value),
        );
        methods.setValue("job_title_id", defaultJobTitle?.id);
        setDefaultAnswerValues(questions, previous?.answers || [], false);
    }, [previous, step]);

    useEffect(() => {
        if (!friendly_url || !preventEditBeforeDate) return;
        const company_id = query.get("company_id");
        const getProduct = () => {
            return company_id
                ? productService.getProductById(company_id, friendly_url)
                : productService.getByFriendlyUrl(friendly_url);
        };
        Promise.all([
            getProduct(),
            reviewService.getReviewQuestions("product"),
            reviewService.getReviewsByUser(authState.id || ""),
        ])
            .then(([p, q, ur]) => {
                if (!!userCompanies?.find(c => c.friendly_url === p?.data?.company.friendly_url)) {
                    notify("You cannot write a review for this product.", "Error");
                    navigate(routeConfig.ProductDetails.path.replace(":friendly_url", p?.data?.friendly_url));
                    return;
                }
                handleSuccessGetProduct(p);
                handleSuccessGetQuestions(q);
                const found = ur.data.find(r => r.model?.friendly_url === friendly_url);
                const sorted = ur.data.reverse();
                const last_reviewed = sorted.find(
                    r =>
                        r.status === REVIEW_STATUS.APPROVED ||
                        r.status === REVIEW_STATUS.SUBMITTED ||
                        r.status === REVIEW_STATUS.UNDER_REVIEW,
                );
                if (found?.id) {
                    handleSuccessGetOldReview({data: found} as any);
                    if (found?.status === REVIEW_STATUS.ABANDONED) {
                        handleSuccessGetLastReviewed(last_reviewed, q.data);
                    }
                } else {
                    handleSuccessGetLastReviewed(last_reviewed, q.data);
                }
            })
            .catch(([p, r, q]) => {
                if (p) handleErrorGetProduct();
                if (r) handleError(r);
                if (q) handleError(q);
                setIsFetchingComplete(true);
            });
    }, [friendly_url, preventEditBeforeDate]);

    return (
        <ErrorBoundary>
            <div className={ROUTE_CLASS}>
                <div className={ROUTE_CONTAINER_CLASS}>
                    <Helmet>
                        <title>{`Channel Program - Reviewing ${productInfo?.name || "Product"}`}</title>
                        <meta
                            name="description"
                            content={
                                productInfo
                                    ? `Write a new review for ${productInfo.name}.`
                                    : "Find and review the latest trending products on Channel Program"
                            }
                        />
                        <meta
                            property="og:description"
                            content={
                                productInfo
                                    ? `Write a new review for ${productInfo.name}.`
                                    : "Review products with Channel Program."
                            }
                        />
                        <meta property="og:url" content={window.location.origin + window.location.pathname} />
                        <meta property="og:site_name" content="Channel Program" />
                        <meta
                            property="og:title"
                            content={`Channel Program - Review ${productInfo?.name || "Product"}`}
                        />
                        <meta property="og:locale" content="en_US" />
                        <meta property="og:type" content="website" />
                        <meta
                            property="website:tag"
                            content={"channel profile, it channel vendors, technology vendors, review, create review"}
                        />
                        <meta
                            property="keywords"
                            content={"channel profiles, it channel vendors, technology vendors, review, create review"}
                        />
                        <meta property="robots" content="index,follow" />
                        <meta property="og:image" content={productInfo?.images?.[0]?.src || "/Media/seo/og-home.jpg"} />
                        <meta property="og:image:type" content="image/jpeg" />
                        <meta property="og:image:width" content="800" />
                        <meta property="og:image:height" content="600" />
                        <meta
                            name="twitter:title"
                            content={`Channel Program - Review ${productInfo?.name || "Product"}`}
                        />
                        <meta
                            name="twitter:description"
                            content={
                                productInfo
                                    ? `Write a new review for ${productInfo.name}.`
                                    : "Review products with Channel Program."
                            }
                        />
                        <meta
                            name="twitter:image"
                            content={productInfo?.images?.[0]?.src || "/Media/seo/twitter-home.jpg"}
                        />
                    </Helmet>
                    {!isFetchingComplete || isLoadingConfig ? (
                        <div className="d-flex align-items-center justify-content-center my-5 py-5">
                            <Loader loading big inline />
                        </div>
                    ) : (
                        <>
                            <div
                                className="d-flex flex-row align-items-center justify-content-between p-3"
                                style={{backgroundColor: theme.palette.blue["main"], minHeight: 140}}>
                                <div className="d-flex flex-row flex-wrap justify-content-between w-100">
                                    <div className="d-flex flex-column col-12 col-md-9">
                                        <Typography variant="body1" color="#FFF">
                                            <strong>Submit a review for {productInfo?.name}</strong>
                                        </Typography>
                                        {step === last_review_page && (
                                            <Typography variant="body2" color="#FFF">
                                                Before you submit the request, tell us a bit about yourself and your
                                                organization.
                                            </Typography>
                                        )}
                                    </div>
                                    {(!previous?.id || previous.status === REVIEW_STATUS.ABANDONED) && (
                                        <div className="d-flex flex-column justify-content-start align-items-start col-12 col-md-3">
                                            <Typography variant="body4" color="#fff">
                                                Your Progress
                                            </Typography>
                                            <ProgressBar
                                                colors={{
                                                    25: "error",
                                                    50: "warning",
                                                    75: "success",
                                                    100: "success",
                                                }}
                                                value={isSubmitting ? 100 : percentage}
                                                className="pe-5 mt-1 w-100"
                                                progressBarTitle={(progress: string) => `${progress}% completed`}
                                                style={{height: 10}}
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <hr className="fadedBorder" />
                            {isSubmitting ? (
                                <div className="d-flex align-items-center justify-content-center my-5 py-5">
                                    <Loader loading big inline />
                                </div>
                            ) : (
                                <FormProvider {...methods}>
                                    <SwitchTransition mode="out-in">
                                        <CSSTransition key={step} className="fadeLeftTransition" timeout={200}>
                                            <form
                                                onChange={calculatePercentage}
                                                noValidate
                                                style={{
                                                    display: "flex",
                                                    flexDirection: "row",
                                                    flexWrap: "wrap",
                                                    justifyContent: "space-between",
                                                    padding: "0px 1rem",
                                                }}>
                                                {step === 1 ? (
                                                    <>
                                                        <div className="w-100">
                                                            <TextBoxComponent
                                                                id="title"
                                                                placeholder="Review Title"
                                                                helperText={REVIEW_TITLE_MIN_CHARACTERS}
                                                                label="Give a title for your review"
                                                                minLength={5}
                                                                maxLength={150}
                                                                minLengthErrorMessage={REVIEW_TITLE_MIN_CHARACTERS}
                                                                isRequired={REVIEW_TITLE_REQUIRED}
                                                                validateOnTheFly
                                                                containerClassName="w-100 mb-2"
                                                                onKeyUp={() => {
                                                                    methods.trigger("title");
                                                                }}
                                                            />
                                                        </div>
                                                        <ReviewQuestionBuilder
                                                            questions={nonAboutYouQuestions.slice(
                                                                0,
                                                                questions_per_page,
                                                            )}
                                                            isAboutYouPage={false}
                                                            productInfo={productInfo}
                                                        />
                                                    </>
                                                ) : (
                                                    <>
                                                        <ReviewQuestionBuilder
                                                            questions={
                                                                step === last_review_page
                                                                    ? aboutYouQuestions
                                                                    : nonAboutYouQuestions.slice(
                                                                          (step - 1) * questions_per_page,
                                                                          step * questions_per_page,
                                                                      )
                                                            }
                                                            isAboutYouPage={step === last_review_page}
                                                            productInfo={productInfo}
                                                        />
                                                        {step === last_review_page && (
                                                            <div className="w-a mx-2 my-4">
                                                                <CheckboxWithLabel
                                                                    id={terms_of_use_id}
                                                                    isRequired
                                                                    validateOnTheFly
                                                                    label={
                                                                        <Typography variant="body3">
                                                                            I understand and agree that my review is
                                                                            subject to Channel Program's{" "}
                                                                            <Link
                                                                                to="/legal/privacy-policy"
                                                                                target="_blank">
                                                                                Privacy Policy{" "}
                                                                            </Link>
                                                                            {` and `}
                                                                            <Link
                                                                                to="/legal/website-terms-of-use"
                                                                                target="_blank">
                                                                                Website Terms of Use
                                                                            </Link>
                                                                        </Typography>
                                                                    }
                                                                />
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                                {step <= last_review_page && (
                                                    <div className="d-flex flex-row align-items-center justify-content-center my-4 w-100">
                                                        <MuiButtonComponent
                                                            id="cancelBtn"
                                                            onClick={handleSecondaryAction}
                                                            disableTrack
                                                            disabled={isSyncingAnswers && step !== 1}
                                                            className="me-3"
                                                            color="blue">
                                                            {step === 1 ? "CANCEL" : "BACK"}
                                                        </MuiButtonComponent>
                                                        <MuiButtonComponent
                                                            // type="submit"
                                                            id="nextBtn"
                                                            disabled={isSyncingAnswers}
                                                            disableTrack
                                                            color="blue"
                                                            onClick={e => {
                                                                e.preventDefault();
                                                                e.stopPropagation();
                                                                goToNextStep();
                                                            }}>
                                                            {isSyncingAnswers && step === 1 && !stepOneId.current ? (
                                                                <Loader inline loading version="iconOrange" />
                                                            ) : step === last_review_page ? (
                                                                "SUBMIT"
                                                            ) : (
                                                                "NEXT"
                                                            )}
                                                        </MuiButtonComponent>
                                                    </div>
                                                )}
                                            </form>
                                        </CSSTransition>
                                    </SwitchTransition>
                                </FormProvider>
                            )}
                        </>
                    )}
                </div>
            </div>
        </ErrorBoundary>
    );
};

export default CreateReview;
