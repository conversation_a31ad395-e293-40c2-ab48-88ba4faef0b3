import Divider from "@mui/material/Divider";
import BorderedBox from "../../uicomponents/Atoms/Box/BorderedBox.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {FormProvider, useForm} from "react-hook-form";
import TextBoxComponent from "../../uicomponents/FormControls/TextBox/textBox.component";
import PasswordInput from "../../uicomponents/Molecules/PasswordInput/passwordInput.component";
import {Link, useNavigate} from "react-router-dom";
import Button from "../../uicomponents/Atoms/Button/Button.Component";
import ArrowForwardOutlined from "@mui/icons-material/ArrowForwardOutlined";
import routeConfig from "../../constants/routeConfig";
import useTheme from "@mui/material/styles/useTheme";
import {useLogin} from "../../hooks/useLogin";
import ModalComponent from "../../uicomponents/Molecules/Modal/Modal.component";
import ForgotPassword from "../../components/Auth/forgotPassword.component";
import {useEffect, useState} from "react";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {useAtom} from "jotai";
import {twofaAtom} from "../../builder_cms/registration/client/clientLogin.atoms";
import TwoFactorChallenge from "../../components/Auth/twoFactorChallenge.component";
import useAuthState from "../../hooks/useAuthState";
import Loader from "../../utils/loader";

const BetterTrackerLoginPage = () => {
    const [forgotPassword, setForgotPassword] = useState<boolean>(false);
    const methods = useForm();
    const theme = useTheme();
    const {triggerLogin} = useLogin();
    const {updateSettings, settings} = useSettings();
    const {authState} = useAuthState();
    const navigate = useNavigate();
    const [twoFAModalConfig] = useAtom(twofaAtom);
    const codeSent = twoFAModalConfig.codeSent;

    const sendLoginInfo = async (data: any) => {
        updateSettings(UPDATE_SETTINGS, {loading: true});
        triggerLogin(data.email, data.password, data.signature);
    };

    useEffect(() => {
        if (authState.authenticated) {
            navigate(routeConfig.Home.path);
        }
    }, [authState.authenticated]);

    return (
        <>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    minHeight: "calc(100vh - 60px - 80px)",
                    flexGrow: 1,
                    padding: 2,
                }}>
                <BorderedBox
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        maxWidth: 500,
                        minWidth: 500,
                        padding: 3,
                        justifyContent: "center",
                        alignItems: "center",
                        gap: 3,
                        height: "fit-content",
                    }}>
                    {twoFAModalConfig.show && !!twoFAModalConfig.config ? (
                        <TwoFactorChallenge
                            {...twoFAModalConfig.config}
                            codeSent={codeSent}
                            onSuccess={twoFAModalConfig.twoFASuccess}
                            onCancel={twoFAModalConfig.twoFACancel}
                            hideTwoAuthPage={twoFAModalConfig.twoFACancel}
                        />
                    ) : (
                        <>
                            <Typography variant="30" fontWeight={600} color="blue.800">
                                Login to your account
                            </Typography>
                            <FormProvider {...methods}>
                                <form
                                    onSubmit={methods.handleSubmit(sendLoginInfo)}
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        gap: 24,
                                        width: "100%",
                                    }}>
                                    <TextBoxComponent
                                        id="email"
                                        placeholder="Enter your registered email"
                                        tooltipHelperText="Email is required"
                                        label="Email"
                                        isRequired
                                        validateOnTheFly
                                        containerSx={{width: "100%"}}
                                    />
                                    <Box width="100%">
                                        <PasswordInput
                                            id="password"
                                            placeholder="Enter your password"
                                            tooltipHelperText="Password is required"
                                            label="Password"
                                            isRequired
                                            validateOnTheFly
                                            criteriaHelper={false}
                                        />
                                        <Typography variant="body4">
                                            <Link
                                                to="#"
                                                style={{fontWeight: 700}}
                                                onClick={() => setForgotPassword(!forgotPassword)}>
                                                Forgot your password?
                                            </Link>
                                        </Typography>
                                    </Box>
                                    <Box
                                        sx={{
                                            height: "1px",
                                            width: "100%",
                                            backgroundColor: theme.palette.neutral[500],
                                            opacity: 0.7,
                                        }}
                                    />
                                    <Button
                                        id="submit-login"
                                        type="submit"
                                        variant="tonal"
                                        sx={{width: "100%", maxWidth: 400, height: 54}}>
                                        {settings.loading || !!authState.id ? (
                                            <Box display="flex" alignItems="center" gap={2}>
                                                <Loader inline loading />
                                                <Typography variant="18" fontWeight={600}>
                                                    Logging in...
                                                </Typography>
                                            </Box>
                                        ) : (
                                            <>
                                                <ArrowForwardOutlined sx={{width: 18, height: 18, marginRight: 1}} />
                                                <Typography variant="18" fontWeight={600}>
                                                    Login
                                                </Typography>
                                            </>
                                        )}
                                    </Button>
                                </form>
                            </FormProvider>
                            <Typography variant="18" color="blue.800">
                                Don't have an account?{" "}
                                <Link style={{fontWeight: 700}} to={routeConfig.Register.path}>
                                    Click here to Register
                                </Link>
                            </Typography>
                        </>
                    )}
                </BorderedBox>
            </Box>
            {forgotPassword && (
                <ModalComponent
                    onClose={() => {
                        setForgotPassword(false);
                    }}
                    showBottom={false}
                    content={
                        <ForgotPassword
                            showRegisterLink={false}
                            forgotPassword={forgotPassword}
                            setForgotPassword={setForgotPassword}
                        />
                    }
                    showCancelButton={false}
                    showOkButton={false}
                    open={forgotPassword}
                />
            )}
        </>
    );
};

export default BetterTrackerLoginPage;
