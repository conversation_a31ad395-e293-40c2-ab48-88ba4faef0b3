import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useMemo, useState} from "react";
import {useNavigate, useSearchParams} from "react-router-dom";
import {VendorCompanyTypes} from "../../constants/companyType.constant";
import routeConfig from "../../constants/routeConfig";
import {useDealsRegistrations} from "../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../hooks/useActiveCompany";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import CreateEditDealDrawer from "../../uicomponents/Organism/DealRegistrations/CreateEditDealDrawer/CreateEditDealDrawer.component";
import DealRegistrationsStatusChartWidget from "../../uicomponents/Organism/DealRegistrations/DealRegistrationsStatusChartWidget/DealRegistrationsStatusChartWidget.component";
import SentDealRegistrationsCountWidget from "../../uicomponents/Organism/DealRegistrations/SentDealRegistrationsCountWidget/SentDealRegistrationsCountWidget.component";
import TotalDealSizesWidget from "../../uicomponents/Organism/DealRegistrations/TotalDealSizesWidget/TotalDealSizesWidget.component";
import VendorDealRegistrationSettings from "../../uicomponents/Organism/DealRegistrations/VendorDealRegistrationSettings/VendorDealRegistrationSettings.component";
import VendorDealsTable from "../../uicomponents/Organism/DealRegistrations/VendorDealsTable/VendorDealsTable.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import ErrorBoundary from "../../utils/errorBoundary.util";

const DealRegistrationsPage = () => {
    const [createDealOpen, setCreateDealOpen] = useState(false);
    const {activeCompany, setActiveCompany, userCompanies} = useActiveCompany();
    const {
        dealRegistrations,
        dealRegistrationsFilters,
        filterState,
        searchState,
        setDealRegistrationsItemsPerPage,
        setDealRegistrationsPage,
        dealRegistrationsItemsPerPage,
        dealRegistrationsPage,
    } = useDealsRegistrations(activeCompany?.id, {
        calls: {
            all: false,
            dealRegistrations: true,
            dealRegistrationsFilters: true,
        },
        defaultItemsPerPage: 40,
        defaultSort: "status__DESC",
        isVendor: true,
    });
    const [dealRegistrationsFilterState, setDealRegistrationsFilterState] = filterState;
    const totalDeals = useMemo(() => dealRegistrations?.data?.meta?.total || 0, [dealRegistrations.data]);
    const theme = useTheme();
    const [params] = useSearchParams();
    const as_company = params.get("as_company");
    const navigate = useNavigate();

    useEffect(() => {
        if (!!as_company && as_company !== activeCompany?.id) {
            const found = userCompanies?.find(c => c.id === as_company);
            const foundIsVendor = found?.company_type.value && VendorCompanyTypes.includes(found?.company_type.value);
            !!found && !foundIsVendor && setActiveCompany(found.friendly_url, found.id);
            !!found && foundIsVendor && navigate(routeConfig.Home.path);
        }
    }, [as_company]);

    return (
        <ErrorBoundary>
            <Box
                gap={3}
                padding={3}
                display="flex"
                flexDirection="column"
                width="100%"
                bgcolor={theme.palette.neutral[200]}>
                <Box
                    padding={2}
                    bgcolor={theme.palette.neutral[100]}
                    gap={3}
                    border={`1px solid ${theme.palette.neutral[300]}`}
                    borderRadius={1}>
                    <PageInnerHeader
                        id="deal-registrations-header"
                        title={{
                            text: "Deal Registrations",
                        }}
                        actions={
                            {
                                //? Save for V2 - Drew 10-01-2024
                                // secondary: {
                                //     id: "export",
                                //     children: (
                                //         <>
                                //             <DownloadOutlinedIcon sx={{marginRight: 1}} />
                                //             Export CSV
                                //         </>
                                //     ),
                                //     onClick: () => null,
                                //     disabled: false,
                                // },
                            }
                        }
                    />
                    <Box
                        width="100%"
                        display="grid"
                        marginTop={2}
                        gridTemplateColumns={{xs: "1fr", md: "repeat(3, 1fr)"}}
                        gap={2}
                        paddingX={{xs: 1, md: 3}}>
                        <SentDealRegistrationsCountWidget />
                        <TotalDealSizesWidget />
                        <DealRegistrationsStatusChartWidget />
                    </Box>
                </Box>
                {/* Deal Registration Settings */}
                <VendorDealRegistrationSettings />
                {/* Received Deal Registrations table */}
                <Box
                    padding={2}
                    bgcolor={theme.palette.neutral[100]}
                    gap={3}
                    border={`1px solid ${theme.palette.neutral[300]}`}
                    borderRadius={1}>
                    <PageInnerHeader
                        id="recieved-requests-header"
                        title={{
                            text: "Received Deal Registrations",
                        }}
                        indicator={totalDeals}
                        searchState={searchState}
                        growSearch
                        fullWidthSearch
                        filters={dealRegistrationsFilters.data}
                        filterState={filterState}
                    />
                    <Box paddingTop={2}>
                        <VendorDealsTable
                            dealRegistrations={dealRegistrations}
                            dealRegistrationsFilterState={dealRegistrationsFilterState}
                            setDealRegistrationsFilterState={setDealRegistrationsFilterState}
                            setDealRegistrationsItemsPerPage={setDealRegistrationsItemsPerPage}
                            setDealRegistrationsPage={setDealRegistrationsPage}
                            dealRegistrationsItemsPerPage={dealRegistrationsItemsPerPage}
                            dealRegistrationsPage={dealRegistrationsPage}
                        />
                    </Box>
                </Box>
            </Box>
            {createDealOpen && (
                <CreateEditDealDrawer open={createDealOpen} onClose={() => setCreateDealOpen(false)} mode="CREATE" />
            )}
        </ErrorBoundary>
    );
};

export default DealRegistrationsPage;
