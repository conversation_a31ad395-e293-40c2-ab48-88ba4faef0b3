import builder from "@builder.io/react";
import {useEffect, useState} from "react";
import {STRICT_ROUTE_CLASS} from "../../constants/commonStrings.constant";
import Helmet from "react-helmet";
import {Breadcrumbs} from "../../components/Breadcrumbs/breadcrumbs.component";
import styles from "../../styles/pages/Categories.module.sass";
import {categoriesService} from "../../services/category.service";
import {AxiosError, AxiosResponse} from "axios";
import {getErrorFromArray} from "../../utils/error.util";
import useNotification from "../../hooks/useNotification";
import Loader from "../../utils/loader";
import useSettings from "../../hooks/useSettings";
import {ICategory} from "../../Interfaces/categories.interface";
import {Col, Row, Image} from "react-bootstrap";
import {Link} from "react-router-dom";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {defaultCustomBreadcrumb} from "./VendorsProductsDirectory.page";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import ReactIcon from "../../components/Icons/reactIcon.component";
import routeConfig from "../../constants/routeConfig";
import {getContrastColor} from "../../utils/color.util";

interface ICategorySorted {
    parent: ICategory;
    children?: ICategory[];
    childrenHaveProducts: boolean;
}

export default function Categories() {
    const [featuredCategories, setFeaturedCategories] = useState<Array<ICategory>>([]);
    const [topCategories, setTopCategories] = useState<Array<ICategory>>([]);
    const [categories, setCategories] = useState<Array<ICategorySorted>>([]);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);

    const notify = useNotification();
    const {settings, updateSettings} = useSettings();

    useEffect(() => {
        builder
            .get("main-pages", {url: "/categories"})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderSEO(res.data.seo);
                }
            });
        // eslint-disable-next-line
    }, []);

    const categoryAvatar = (category: ICategory, size: string, useContrastColor?: boolean) => {
        const bgColor = getContrastColor(!category.color || category.color === "#000000" ? "#ffffff" : category.color);
        if (category.icon_name) {
            return (
                <ReactIcon
                    iconName={category.icon_name}
                    size={size}
                    style={{fill: useContrastColor ? bgColor : category.color}}
                />
            );
        } else {
            return (
                <Image
                    src={
                        createImageFromInitials(
                            parseInt(size),
                            category?.name[0] || "C",
                            useContrastColor ? getContrastColor(bgColor) : category?.color || "#000000",
                        )!
                    }
                    roundedCircle
                    width={size}
                />
            );
        }
    };

    const populateCategories = (categories: ICategory[]) => {
        const featCategories: ICategory[] = [];
        const topCategories: ICategory[] = [];
        const parents: ICategory[] = [];
        const children: ICategory[] = [];
        categories.forEach((category: ICategory) => {
            if (category.parent_id === null) {
                parents.push(category);
            } else {
                children.push(category);
            }

            if (category.featured) {
                featCategories.push(category);
            }

            if (category.is_top_category) {
                topCategories.push(category);
            }
        });
        const sortedCategories: ICategorySorted[] = parents.map(parent => {
            return {
                parent,
                children: children.filter(child => child.parent_id === parent.id),
                childrenHaveProducts:
                    children
                        .filter(child => child.parent_id === parent.id)
                        .filter(child => child.number_of_products !== undefined && child.number_of_products > 0)
                        .length > 0,
            };
        });
        setCategories(sortedCategories);
        setFeaturedCategories(featCategories);
        setTopCategories(topCategories);
    };

    const handleSuccess = (response: AxiosResponse) => {
        populateCategories(response.data);
        updateSettings(UPDATE_SETTINGS, {all_categories: response.data});
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    useEffect(() => {
        if (settings.all_categories) {
            return populateCategories(settings.all_categories);
        }
        categoriesService.getAllCategories(handleSuccess, handleError);
        // eslint-disable-next-line
    }, []);

    return (
        <div className={STRICT_ROUTE_CLASS}>
            <Helmet>
                <title>{builderSEO?.title || "Channel Program - Explorer"}</title>
                <meta
                    name="description"
                    content={builderSEO?.description || "Explore, find and compare industry solutions"}
                />
                <meta
                    property="og:description"
                    content={builderSEO?.description || "Explore, find and compare industry solutions"}
                />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:title" content={builderSEO?.openGraphTitle || "Channel Program - Explorer"} />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords || "channel explorer, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords || "channel explorer, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta property="og:image" content={builderSEO?.openGraphImage || "/Media/seo/og-home.jpg"} />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta name="twitter:title" content={builderSEO?.twitterTitle || "Channel Program - Explorer"} />
                <meta
                    name="twitter:description"
                    content={builderSEO?.twitterDescription || "Explore, find and compare industry solutions"}
                />
                <meta name="twitter:image" content={builderSEO?.twitterImage || "/Media/seo/twitter-home.jpg"} />
            </Helmet>
            <div className={styles.categoriesContainer}>
                <Breadcrumbs customBreadcrumb={defaultCustomBreadcrumb} />
                {categories?.length !== 0 ? (
                    <>
                        {!!topCategories.length && (
                            <>
                                <div className="d-flex gap-4 mb-2 mt-3 align-items-center flex-wrap">
                                    <h2>Top of the Stack</h2>
                                    <div className="d-flex gap-2 align-items-center">
                                        <ReactIcon
                                            iconName="AiOutlineAreaChart"
                                            style={{width: "28px", height: "28px", fill: "var(--AccentColor)"}}
                                        />
                                        <h4 className="accentColor fs-20">
                                            Click on a Top of the Stack Category to view the StackCharts!
                                        </h4>
                                    </div>
                                </div>
                                <div className="d-flex flex-wrap gap-2">
                                    {topCategories.map(category => (
                                        <Link
                                            to={`${routeConfig.Categories.path}/${category.friendly_url}`}
                                            key={category.id}>
                                            <div
                                                className={styles.categoryItem}
                                                style={{
                                                    backgroundColor:
                                                        !category.color || category.color === "#000000"
                                                            ? "#fff"
                                                            : category.color,
                                                    borderColor:
                                                        !category.color || category.color === "#000000"
                                                            ? "#000"
                                                            : category.color,
                                                }}>
                                                {categoryAvatar(category, "22px", true)}
                                                <span
                                                    style={{
                                                        color: getContrastColor(
                                                            !category.color || category.color === "#000000"
                                                                ? "#ffffff"
                                                                : category.color,
                                                        ),
                                                    }}>
                                                    {category.name}
                                                </span>
                                            </div>
                                        </Link>
                                    ))}
                                </div>
                            </>
                        )}
                        <hr className="fadedBorder" />
                        {featuredCategories.length !== 0 && (
                            <>
                                <h2 className="mx-2">Featured Categories</h2>
                                <Row xs={1} sm={2} md={3} lg={4} className={styles.gridRowContainer}>
                                    {featuredCategories.map(category => (
                                        <Col key={`category_${category.id}`}>
                                            <Link
                                                id={`categoryLink_${category.id}`}
                                                to={`/categories/${category.friendly_url}`}>
                                                <div className={styles.gridColContainer}>
                                                    <div className={styles.imageContainer}>
                                                        {categoryAvatar(category, "60px")}
                                                    </div>
                                                    <div className={styles.infoContainer}>
                                                        <h3 style={{color: `${category.color}`}}>{category.name}</h3>
                                                        <p>{category?.description}</p>
                                                    </div>
                                                </div>
                                            </Link>
                                        </Col>
                                    ))}
                                </Row>
                            </>
                        )}
                        <hr className="fadedBorder" />
                        <h2 className="mx-2">All Categories</h2>
                        <Row xs={1} sm={2} lg={3} xl={4}>
                            {categories.map(category => (
                                <>
                                    {category.childrenHaveProducts && (
                                        <Col key={`category_${category.parent.id}`}>
                                            <Link
                                                id={`categoryLink_${category.parent.id}`}
                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                    ":friendly_url",
                                                    category.parent?.friendly_url || "",
                                                )}>
                                                <div className={styles.listParentTitle}>
                                                    <div className={styles.imageContainer}>
                                                        {categoryAvatar(category.parent, "36px")}
                                                    </div>
                                                    <div className={styles.infoContainer}>
                                                        <h4 style={{color: `${category.parent.color}`}}>
                                                            {category.parent.name}
                                                        </h4>
                                                    </div>
                                                </div>
                                            </Link>
                                            {category.children && (
                                                <ul className={styles.childrenListContainer}>
                                                    {category.children.map(child => (
                                                        <>
                                                            {child.number_of_products !== undefined &&
                                                                child.number_of_products > 0 && (
                                                                    <li key={`subcategory_${child.id}`}>
                                                                        <Link
                                                                            id={`subcategoryLink_${child.id}`}
                                                                            to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                                                ":friendly_url",
                                                                                child.friendly_url || "",
                                                                            )}>
                                                                            {child.name}
                                                                        </Link>
                                                                    </li>
                                                                )}
                                                        </>
                                                    ))}
                                                </ul>
                                            )}
                                        </Col>
                                    )}
                                </>
                            ))}
                        </Row>
                    </>
                ) : (
                    <Loader inline loading />
                )}
            </div>
        </div>
    );
}
