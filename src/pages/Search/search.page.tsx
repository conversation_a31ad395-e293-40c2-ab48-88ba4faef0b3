import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useEffect, useState} from "react";
import {ButtonGroup, ToggleButton} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation, useNavigate, useSearchParams} from "react-router-dom";
import {IBlog} from "../../Interfaces/blog.interface";
import {IMediaGallery} from "../../Interfaces/mediaGallery.interface";
import {IProfile} from "../../Interfaces/people.interface";
import {ISearchPagination, ISearchResults} from "../../Interfaces/search.interface";
import BlogCard from "../../components/Cards/blogCard.component";
import GalleryCard from "../../components/Cards/galleryCard.component";
import ExplorerCard from "../../components/Explorer/explorerCard.component";
import ButtonComponent from "../../components/FormControls/button.component";
import TextBoxComponent from "../../components/FormControls/textBox.component";
import {DocumentsIcon} from "../../components/Icons/documents.icon";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import useNotification from "../../hooks/useNotification";
import {searchService} from "../../services/search.service";
import styles from "../../styles/pages/search.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import {getFriendlyDate} from "../../utils/formatDate";
import Loader from "../../utils/loader";

interface ILocation {
    query: string;
}

interface ILimit {
    content: number;
    vendors: number;
    people: number;
    products: number;
}

interface IForm {
    searchQuery: string;
}

interface IFetching {
    content: boolean;
    vendors: boolean;
    people: boolean;
    products: boolean;
}

/**
 * Search results page divided by content, vendors, people, and products
 * @returns {JSX.Element} Search page
 */
export default function SearchResults() {
    const location = useLocation();
    const locationState = location.state as ILocation;
    const [query, setQuery] = useState<string>(locationState?.query || location.search.split("q=")[1] || "");
    const [params] = useSearchParams();
    const [lastQuery, setLastQuery] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(false);
    const [results, setResults] = useState<ISearchResults>({} as ISearchResults);
    const [selectedFilter, setSelectedFilter] = useState<Array<string>>([]);
    const [noResults, setNoResults] = useState(false);
    const [fetching, setFetching] = useState<IFetching>({
        content: false,
        vendors: false,
        people: false,
        products: false,
    });

    const [limit, setLimit] = useState<ILimit>({
        content: 8,
        vendors: 4,
        people: 4,
        products: 4,
    });

    const notify = useNotification();
    const navigate = useNavigate();
    const searchMethods = useForm<IForm>({
        defaultValues: {
            searchQuery: locationState?.query || params.get("q") || "",
        },
    });
    const searchWatcher = searchMethods.watch("searchQuery");
    const filters: ["Content", "Vendors", "People", "Products"] = ["Content", "Vendors", "People", "Products"];
    const contents: ["videos", "images", "blogs", "documents"] = ["videos", "images", "blogs", "documents"];

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setLoading(false);
    };

    const handleSuccessSearch = (response: AxiosResponse<ISearchResults>) => {
        const data = response.data;
        setLastQuery(query);
        const hasData = Object.keys(data)
            .map(res => {
                if (res === "content") {
                    const hasContent = Object.keys(data[res]).reduce((acc, cur) => {
                        return data[res][cur].data?.length > 0 ? true : acc;
                    }, false);
                    return hasContent;
                }
                return !!data[res]?.data?.length;
            })
            .some(res => res);
        if (hasData) {
            setNoResults(false);
        } else {
            setNoResults(true);
        }

        setResults(extractAndSortContent(data));
        setLoading(false);
    };

    const extractAndSortContent = (data: ISearchResults, pagination?: ISearchPagination): ISearchResults => {
        let linksController: {page: number; key: string} = {page: 0, key: ""};
        let metaController: {page: number; key: string} = {page: 0, key: ""};
        contents.forEach(c => {
            if (
                (pagination || data.content)?.[c]?.links.last &&
                parseInt((pagination || data.content)?.[c]?.links.last?.split("page=")[1] || "0") > linksController.page
            ) {
                linksController = {
                    page: parseInt((pagination || data.content)?.[c]?.links.last?.split("page=")[1] || "0"),
                    key: c,
                };
            }
            if (
                (pagination || data.content)?.[c]?.meta?.last_page &&
                ((pagination || data.content)?.[c]?.meta?.last_page || 0) > metaController.page
            ) {
                metaController = {
                    page: parseInt((pagination || data.content)?.[c]?.links.last?.split("page=")[1] || "0"),
                    key: c,
                };
            }
        });
        let contentMixed = {
            data: [
                ...(pagination && data?.content?.data ? data.content.data : []),
                ...contents
                    .map(c => (pagination || data.content)?.[c]?.data.map(d => ({...d, type: c})).flat())
                    .flat()
                    .sort((a, b) =>
                        a && b ? DateTime.fromISO(b.created_at).diff(DateTime.fromISO(a.created_at)).as("minutes") : 0,
                    ),
            ],
            links: (pagination || data.content)?.[linksController.key]?.links,
            meta: (pagination || data.content)?.[metaController.key]?.meta,
        };

        data.content = {...data.content, ...contentMixed};
        contentMixed = {} as any;
        return data;
    };

    useEffect(() => {
        if (query && query !== lastQuery) {
            setLoading(true);
            searchService.full(query, handleSuccessSearch, handleError);
        }
        // eslint-disable-next-line
    }, [query]);

    useEffect(() => {
        if (selectedFilter.length === 0) {
            setLimit({
                content: 8,
                vendors: 4,
                people: 4,
                products: 4,
            });
        } else if (selectedFilter.length === 1) {
            filters?.map(target => {
                if (selectedFilter?.[0] === target) {
                    return setLimit(state => ({
                        content: target === "Content" && state.content < 18 ? state.content + 8 : state.content,
                        vendors: target === "Vendors" && state.vendors < 16 ? state.vendors + 8 : state.vendors,
                        people: target === "People" && state.people < 16 ? state.people + 8 : state.people,
                        products: target === "Products" && state.products < 16 ? state.products + 8 : state.products,
                    }));
                }
                return null;
            });
        }
        // eslint-disable-next-line
    }, [selectedFilter]);

    const handleNewSearch = (value: string) => {
        setQuery(value);
        navigate(
            {
                pathname: routeConfig.SearchResults.path,
                search: `?q=${value}`,
            },
            {state: {query: value}},
        );
    };

    const handleSuccessSearchPage = (response: AxiosResponse, target: string) => {
        if (target === "content") {
            setFetching(state => ({...state, content: false}));
            setResults(extractAndSortContent(results, response.data));
            return;
        }
        setResults(state => {
            return {
                ...state,
                [target]: {
                    data: [...state[target].data, ...response.data.data],
                    links: response.data?.links,
                    meta: response.data?.meta,
                },
            };
        });
        setFetching(state => {
            return {
                ...state,
                [target]: false,
            };
        });
    };

    const handlePaginate = (target: string) => {
        setLimit(state => {
            return {
                ...state,
                [target]: limit[target] + (target === "content" ? 8 : 4),
            };
        });
        if (results?.[target]?.meta?.last_page > results?.[target]?.meta.current_page && !fetching?.[target]) {
            setFetching(state => {
                return {
                    ...state,
                    [target]: true,
                };
            });
            searchService.full(
                query,
                r => handleSuccessSearchPage(r, target),
                handleError,
                results[target]?.meta.current_page + 1,
                target,
            );
        }
    };

    useEffect(() => {
        if (!searchWatcher && query !== lastQuery) return;
        const searchTimeout = setTimeout(() => {
            if (searchWatcher.trim().length < 2) {
                notify("Please enter a search query with more than 2 letters", "Error");
            } else {
                handleNewSearch(searchWatcher);
            }
        }, 500);
        return () => clearTimeout(searchTimeout);
        // eslint-disable-next-line
    }, [searchWatcher]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <FormProvider {...searchMethods}>
                    <div className="d-flex justify-content-center align-items-center mb-4 flex-wrap">
                        <TextBoxComponent
                            id="searchQuery"
                            placeholder="Search in Channel Program"
                            className="rounded-pill"
                            containerClassName="mb-0 w-100 w-md-75 w-lg-50"
                        />
                    </div>
                </FormProvider>
                <div className="mb-5 mt-3 mt-md-0">
                    <ButtonGroup className="d-flex justify-content-center gap-3 flex-wrap">
                        {filters.map(filter => (
                            <ToggleButton
                                key={filter}
                                id={`filter-${filter.toLowerCase()}`}
                                type="checkbox"
                                className={
                                    selectedFilter.includes(filter) ? styles.filterPillFull : styles.filterPillEmpty
                                }
                                name={`filter-${filter.toLowerCase()}`}
                                value={filter}
                                checked={selectedFilter.includes(filter)}
                                onChange={() => setSelectedFilter([filter])}>
                                {filter}
                            </ToggleButton>
                        ))}
                    </ButtonGroup>
                    {!!selectedFilter.length && (
                        <ButtonComponent
                            id="clearFilters"
                            className="textBtn d-block mx-auto mt-2"
                            onClick={() => setSelectedFilter([])}>
                            <span>Clear filter</span>
                        </ButtonComponent>
                    )}
                </div>
                {loading ? (
                    <div className="d-flex w-100 justify-content-center">
                        <Loader inline loading big />
                    </div>
                ) : noResults ? (
                    <div className={styles.resultsContainer}>
                        <SubtitleContainer
                            as="span"
                            text={`Nothing to display at the moment. Please do another search.`}
                            className="d-block my-auto"
                        />
                    </div>
                ) : (
                    <>
                        {filters.map(target => {
                            return (
                                <div key={target}>
                                    {(selectedFilter.includes(target) ||
                                        (!!!selectedFilter.length &&
                                            !!results?.[target.toLowerCase()]?.data?.length)) && (
                                        <div className={styles.resultsContainer}>
                                            <>
                                                {!!!results?.[target.toLowerCase()]?.data?.length &&
                                                selectedFilter.includes(target) ? (
                                                    <SubtitleContainer
                                                        as="span"
                                                        text={`No ${target.toLowerCase()} found`}
                                                        className="d-block my-auto"
                                                    />
                                                ) : (
                                                    <TitleContainer
                                                        as="h3"
                                                        text={target}
                                                        noConnector
                                                        position="start"
                                                        className="mb-2"
                                                    />
                                                )}
                                                <div className={styles.dataContainer}>
                                                    {!!results?.[target.toLowerCase()]?.data?.length &&
                                                        results?.[target.toLowerCase()]?.data
                                                            ?.slice(0, limit[target.toLowerCase()])
                                                            .map(t =>
                                                                target === "Content" ? (
                                                                    t.type === "videos" ? (
                                                                        <ExplorerCard
                                                                            video={{
                                                                                ...t,
                                                                                company_handle: t.parent?.handle,
                                                                                company_name: t.parent?.name,
                                                                                parent_avatar: t.parent?.avatar,
                                                                                parent_model_type: "company",
                                                                            }}
                                                                            className={styles.contentCard}
                                                                            key={t.id}
                                                                        />
                                                                    ) : t.type === "images" ? (
                                                                        <GalleryCard
                                                                            gallery={t as IMediaGallery}
                                                                            key={t.id}
                                                                            className={styles.galleryCard}
                                                                        />
                                                                    ) : t.type === "blogs" ? (
                                                                        <BlogCard
                                                                            info={t as IBlog}
                                                                            key={t.id}
                                                                            className={styles.contentCard}
                                                                            maxDescription={100}
                                                                            entityId={
                                                                                (t as IBlog).subject?.id ||
                                                                                (t as IBlog)?.author?.id
                                                                            }
                                                                        />
                                                                    ) : (
                                                                        <Link
                                                                            to={routeConfig.VendorProfile.path
                                                                                .replace(":id", t.parent?.friendly_url)
                                                                                .concat("?tab=content&subTab=docs")}
                                                                            key={t.id}
                                                                            className={`${styles.contentCard} ${styles.docContainer}`}>
                                                                            <div className={styles.docThumbnail}>
                                                                                <DocumentsIcon
                                                                                    mime={t.mime_type}
                                                                                    size="75"
                                                                                />
                                                                            </div>
                                                                            <div className={styles.docInfo}>
                                                                                <TitleContainer
                                                                                    as="h4"
                                                                                    text={t.title}
                                                                                    noConnector
                                                                                />
                                                                                <span>
                                                                                    Posted{" "}
                                                                                    {getFriendlyDate(t.created_at)}
                                                                                </span>
                                                                            </div>
                                                                        </Link>
                                                                    )
                                                                ) : (
                                                                    <ExplorerCard
                                                                        key={t.id}
                                                                        entity={
                                                                            {
                                                                                type:
                                                                                    target === "People"
                                                                                        ? "USER"
                                                                                        : "VENDOR_ALL",
                                                                                id: t.id,
                                                                                name:
                                                                                    target === "Products"
                                                                                        ? t.name
                                                                                        : t.parent?.name || t.name,
                                                                                handle: t.handle || "",
                                                                                friendly_url:
                                                                                    t.parent?.friendly_url ||
                                                                                    t.friendly_url,
                                                                                type_is_of_vendor: target !== "People",
                                                                                is_company: target !== "People",
                                                                                avatar:
                                                                                    target === "Products"
                                                                                        ? t.parent?.avatar
                                                                                        : t.image || t.avatar || "",
                                                                                is_distributor:
                                                                                    target === "Products"
                                                                                        ? t.parent?.is_distributor
                                                                                        : t.is_distributor,
                                                                            } as IProfile
                                                                        }
                                                                        product_name={
                                                                            t.parent?.name ? t.name : undefined
                                                                        }
                                                                        className={styles.profileCard}
                                                                    />
                                                                ),
                                                            )}
                                                </div>
                                                {results?.[target.toLowerCase()]?.data?.length >
                                                    limit[target.toLowerCase()] && (
                                                    <ButtonComponent
                                                        id={`paginate${target}`}
                                                        type="button"
                                                        className="textBtn d-block mx-auto mt-4"
                                                        onClick={() => handlePaginate(target.toLowerCase())}>
                                                        <span>See More</span>
                                                    </ButtonComponent>
                                                )}
                                            </>
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </>
                )}
            </div>
        </div>
    );
}
