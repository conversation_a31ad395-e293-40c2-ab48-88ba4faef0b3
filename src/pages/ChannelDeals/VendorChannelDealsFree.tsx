import React from "react";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import {Link, useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import {colorPalette} from "../../themes/themes.constant";
import {useTheme} from "@mui/material";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import ErrorBoundary from "../../utils/errorBoundary.util";

const VendorChannelDealsFree = () => {
    const theme = useTheme();
    const navigate = useNavigate();

    return (
        <ErrorBoundary>
            <Box
                sx={{
                    flex: "1 0 auto",
                    "@media (min-width: 992px)": {
                        width: "calc(75% - 12px)",
                        maxWidth: "calc(75% - 12px)",
                    },
                }}>
                <Box
                    display="flex"
                    flexDirection="column"
                    width="100%"
                    padding={3}
                    gap={2}
                    bgcolor={theme.palette.neutral[200]}>
                    <Box
                        padding={2}
                        display="flex"
                        flexDirection="column"
                        gap={3}
                        bgcolor={theme.palette.neutral[100]}
                        border={`1px solid ${theme.palette.neutral[300]}`}
                        borderRadius={2}>
                        <PageInnerHeader
                            id="channel-deals-header"
                            title={{
                                text: "Channel Deals",
                                sx: {
                                    fontSize: "26px!important",
                                    fontWeight: "400!important",
                                    color: "neutral.700",
                                },
                            }}
                            growSearch
                            subtitle={{
                                text: (
                                    <>
                                        <Typography
                                            size={16}
                                            variant={"body2"}
                                            color={"neutral.800"}
                                            fontWeight={500}
                                            style={{
                                                fontSize: "16px !important",
                                                paddingTop: 0,
                                                paddingBottom: "16px !important",
                                            }}>
                                            Upgrade Now to Unlock Channel Deals
                                        </Typography>

                                        <Typography color={"neutral.800"} fontWeight={400} style={{marginTop: "15px"}}>
                                            Channel Deals is an exclusive feature for subscribed partners.
                                            <Link
                                                to={routeConfig.Contact.path}
                                                style={{fontWeight: "bold", color: colorPalette.blue[600]}}>
                                                {" "}
                                                Contact Sales{" "}
                                            </Link>
                                            to upgrade and gain access to this and other powerful tools designed to
                                            enhance your experience and deepen your engagement with MSPs!
                                        </Typography>
                                    </>
                                ),
                                sx: {
                                    paddingTop: "15px",
                                    fontSize: "14px !important",
                                    fontWeight: "inherit",
                                    lineHeight: "22px",
                                },
                            }}
                            actions={{
                                gradient: {
                                    id: "upgrade-now-button",
                                    children: "Upgrade Now",
                                    onClick: () => {
                                        navigate(routeConfig.Contact.path);
                                    },
                                },
                            }}
                        />
                        <Box position={"relative"} minHeight={"150px"}>
                            <img
                                src={"/Media/placeholder/channel-deals.jpg"}
                                alt={"channel-deals-placeholder"}
                                width={"100%"}
                            />
                            <Typography
                                fontWeight={"bold"}
                                textAlign={"center"}
                                fontInter
                                style={{
                                    textAlign: "center",
                                    top: "50%",
                                    transform: "translateY(-50%)",
                                    position: "absolute",
                                    margin: "0 auto",
                                    left: "0",
                                    right: "0",
                                }}>
                                <img
                                    style={{marginBottom: "5px"}}
                                    src={"/Media/icons/plusAdd.svg"}
                                    alt={"integrations plus"}
                                />
                                <p style={{fontSize: "16px"}}>
                                    <Link
                                        to={routeConfig.Contact.path}
                                        style={{fontWeight: "bold", color: colorPalette.primary["600"]}}>
                                        Upgrade now &nbsp;
                                    </Link>
                                    to unlock
                                </p>
                                <p style={{fontSize: "16px"}}> Channel Deals</p>
                            </Typography>
                        </Box>
                    </Box>
                </Box>
            </Box>
        </ErrorBoundary>
    );
};

export default VendorChannelDealsFree;
