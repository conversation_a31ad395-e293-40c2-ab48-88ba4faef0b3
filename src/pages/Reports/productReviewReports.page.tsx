import {AxiosError, AxiosResponse} from "axios";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {BarChartComponent} from "../../components/Charts/barChart.component";
import PieChartComponent from "../../components/Charts/pieChart.component";
import ButtonComponent from "../../components/FormControls/button.component";
import TextBoxComponent from "../../components/FormControls/textBox.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import useNotification from "../../hooks/useNotification";
import {reportService} from "../../services/report.service";
import {downloadDoc} from "../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../utils/error.util";
import {DATE_FORMAT, formatDate} from "../../utils/formatDate";
import Loader from "../../utils/loader";

interface IReviewByVendor {
    number_of_reviews: number;
    subscription_plan: string;
    vendor_friendly_url: string;
    vendor_name: string;
}

interface IReviewPerProduct {
    number_of_reviews: number;
    product_friendly_url: string;
    product_name: string;
    vendor_friendly_url: string;
    vendor_name: string;
}

const today: Date = new Date();
const startOfMonth: Date = new Date();
startOfMonth.setDate(1);

const ProductReviewReports = () => {
    const [loading, setLoading] = useState(false);
    const [exportingVendor, setExportingVendor] = useState(false);
    const [exportingProducts, setExportingProducts] = useState(false);
    const [chartData, setChartData] = useState({
        reviews_by_vendor: [],
        reviews_per_product: [],
    });
    const notify = useNotification();
    const methods = useForm({
        defaultValues: {
            from_date: startOfMonth.toISOString().slice(0, 10),
            to_date: today.toISOString().slice(0, 10),
        },
    });

    const exportProductReviews = () => {
        setExportingProducts(true);
        const fileName: string = `ReviewsProduct${formatDate(
            methods.watch("from_date"),
            DATE_FORMAT.replaceAll("/", "-"),
        ).replaceAll("/", "-")}-${formatDate(methods.watch("to_date"), DATE_FORMAT).replaceAll("/", "-")}`;
        reportService.getProductReviewCountsByProductCSV(
            methods.getValues(),
            (res: AxiosResponse) => {
                downloadDoc(res.data, fileName + ".csv");
                setExportingProducts(false);
            },
            handleError,
        );
    };

    const exportVendorReviews = () => {
        setExportingVendor(true);
        const fileName: string = `VendorReviews${formatDate(methods.watch("from_date"), DATE_FORMAT).replaceAll(
            "/",
            "-",
        )}-${formatDate(methods.watch("to_date"), DATE_FORMAT).replaceAll("/", "-")}`;
        reportService.getProductReviewCountByVendorCSV(
            methods.getValues(),
            (res: AxiosResponse) => {
                downloadDoc(res.data, fileName + ".csv");
                setExportingVendor(false);
            },
            handleError,
        );
    };

    const runReport = () => {
        const values = methods.getValues();
        setLoading(true);
        reportService.getProductReviewReports(values, onSuccessRunReport, handleError);
    };

    const onSuccessRunReport = (res: AxiosResponse) => {
        const newData: any = {
            reviews_by_vendor: res.data.reviews_by_vendor,
            reviews_per_product: res.data.reviews_per_product,
        };
        setChartData(newData);
        setLoading(false);
    };

    const handleError = (err: AxiosError) => {
        setLoading(false);
        setExportingProducts(false);
        setExportingVendor(false);
        notify(getErrorFromArray(err), "Error");
    };

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <FormProvider {...methods}>
                    <form
                        className="d-flex flex-row flex-wrap align-items-center w-auto"
                        onSubmit={methods.handleSubmit(runReport)}>
                        <TextBoxComponent
                            isRequired
                            label="From Date"
                            type="date"
                            id="from_date"
                            containerStyles={{minWidth: 230}}
                            containerClassName="me-2"
                        />
                        <TextBoxComponent
                            isRequired
                            label="To Date"
                            type="date"
                            id="to_date"
                            containerStyles={{minWidth: 230}}
                            containerClassName="me-2"
                        />
                        <ButtonComponent
                            id="submitBtn"
                            type="submit"
                            variant="cpMainBtnThin"
                            className="m-0"
                            disabled={loading}>
                            {loading ? <Loader inline loading version="iconWhite" /> : "Run Report"}
                        </ButtonComponent>
                    </form>
                </FormProvider>

                <hr className="fadedBorder" />
                <div className="d-flex flex-row flex-wrap">
                    <div className="col-12 col-md-6 d-flex flex-column align-items-center">
                        <TitleContainer as="h2" text="Vendor Number of Reviews" position="center" className="m-1" />
                        <SubtitleContainer as="span" text="Total Number of Reviews per Vendor" position="center" />
                        <PieChartComponent
                            width="container"
                            height={440}
                            loading={loading}
                            data={chartData.reviews_by_vendor}
                            dataKey="number_of_reviews"
                            noDataMessage="No Data. Run a report to see data."
                            customHoverContentHeight={50}
                            customHoverContentWidth={300}
                            CustomHoverContent={(contentProps: {
                                payload: IReviewByVendor;
                                percentage: number;
                                value: any;
                            }) => {
                                return (
                                    <div className="d-flex flex-column p-1 shadow bg-light" style={{maxWidth: 300}}>
                                        <h6>{contentProps.payload.vendor_name}</h6>
                                        <div>
                                            <span>
                                                {contentProps.payload.number_of_reviews} Review
                                                {contentProps.payload.number_of_reviews > 1 ? "s" : ""}
                                            </span>
                                            <span className="ms-1">
                                                ({(contentProps.percentage * 100).toFixed(1)}%)
                                            </span>
                                        </div>
                                    </div>
                                );
                            }}
                        />
                        <ButtonComponent
                            disabled={
                                exportingVendor ||
                                loading ||
                                (!chartData.reviews_by_vendor.length && !chartData.reviews_per_product.length)
                            }
                            id="exportVendorReviews"
                            onClick={exportVendorReviews}
                            variant="cpBlueBtnThin">
                            {exportingVendor ? <Loader inline loading version="iconWhite" /> : "Export as CSV"}
                        </ButtonComponent>
                    </div>
                    <div className="col-12 col-md-6 d-flex flex-column align-items-center">
                        <TitleContainer as="h2" text="Top Products" position="center" className="m-1" />
                        <SubtitleContainer as="span" text="Total Number of Reviews per Product" position="center" />
                        <BarChartComponent
                            xAxisLabel=""
                            yAxisLabel="# of Reviews"
                            xAxisLabelColor="cpNavy"
                            showLegend={false}
                            showTooltip
                            showGridLines={false}
                            width={"100%"}
                            height={440}
                            loading={loading}
                            noDataMessage="No Data. Run a report to see data."
                            rotateXAxisTick={-30}
                            customInterval={chartData.reviews_per_product?.length > 20 ? 1 : 0}
                            customChartMargin={{
                                top: 15,
                                right: 5,
                                left: 0,
                                bottom: 120,
                            }}
                            // tickOffset={(i: number) => (i % 2 === 0 ? 0 : 20)}
                            //@ts-ignore
                            data={chartData.reviews_per_product.map((item: IReviewPerProduct) => {
                                return {
                                    xAxisName: item.product_name,
                                    yAxisName: item.number_of_reviews,
                                    barName: item.product_name,
                                    event_id: "",
                                };
                            })}
                            dataKeys={[{keyName: "yAxisName", label: "Number of Reviews", fillColor: "var(--cpNavy)"}]}
                        />
                        <ButtonComponent
                            disabled={
                                exportingProducts ||
                                loading ||
                                (!chartData.reviews_by_vendor.length && !chartData.reviews_per_product.length)
                            }
                            id="exportProductReviews"
                            onClick={exportProductReviews}
                            variant="cpBlueBtnThin">
                            {exportingProducts ? <Loader inline loading version="iconWhite" /> : "Export as CSV"}
                        </ButtonComponent>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProductReviewReports;
