import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useCallback, useEffect, useRef, useState} from "react";
import {Nav, Tab} from "react-bootstrap";
import {Helmet} from "react-helmet";
import PollsBroadcastTabs from "../../components/Admin/PollsBroadcast.component";
import {AdminChat} from "../../components/Chat";
import {PublicChatBox} from "../../components/Chat/publicChatBox.component";
import {TimeCounter} from "../../components/Counter/timeCounter.component";
import {ErrorIcon} from "../../components/Icons";
import {ChatBubblesIcon} from "../../components/Icons/chatBubbles.icon";
import {NotificationDot} from "../../components/Indicators/notification.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {VideoPlayer} from "../../components/VideoPlayer";
import {PusherEventsEnum, PusherEvtTypesEnum} from "../../enums/pusherEvents.enum";
import useAppConfig from "../../hooks/useAppConfig";
import useAuthState from "../../hooks/useAuthState";
import useNotification from "../../hooks/useNotification";
import usePitchesState from "../../hooks/usePitchesState";
import usePusher, {PRIVATE_CHANNEL} from "../../hooks/usePusher";
import usePusherState from "../../hooks/usePusherState";
import {IBroadcastMsg, IChat, IPublicChat, IPusherData} from "../../models/Pusher.interface";
import {GET_NOT_STARTED} from "../../reducers/pitches.reducer";
import {UPDATE_PUSHER} from "../../reducers/pusher.reducer";
import {eventService} from "../../services/event.service";
import styles from "../../styles/pages/pitchDay.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {getErrorFromArray} from "../../utils/error.util";
import ErrorBoundary from "../../utils/errorBoundary.util";
import gZipInflate from "../../utils/gZipInflate.util";
import useQuery from "../../utils/query.util";

export interface IChatMessagesList {
    id: string;
    max_date: string;
    last_message: string;
    first_name: string;
    last_name: string;
    company_name: string;
    company_type: string;
    user_avatar?: string;
}

export interface IVendors {
    company_id: string;
    company_name: string;
    break_out_link: string;
}

interface IModInfo {
    pusherChannel: string;
    attendantsUsersCount: number;
    enteredUsersCount: number;
    notEnteredUsersCount: number;
    usersHistory: IChatMessagesList[];
    broadcastHistory: IBroadcastMsg[];
    vendors: IVendors[];
    publicHistory: IPublicChat[];
}

const sortPublicChat = (a: IBroadcastMsg | IPublicChat, b: IBroadcastMsg | IPublicChat) => {
    const aDate = DateTime.fromISO(a.date);
    const bDate = DateTime.fromISO(b.date);
    return aDate === bDate ? 0 : aDate < bDate ? -1 : 1;
};

export default function PitchDay() {
    const [newBMsg, setNewBMsg] = useState<any>();
    const [newMsg, setNewMsg] = useState<IChat>();
    const [newPublicMsg, setNewPublicMsg] = useState<IPublicChat>();
    const [pusherChannel, setPusherChannel] = useState<string>("");
    const [vendorsArray, setVendorsArray] = useState<IVendors[]>([]);
    const [eventEnded, setEventEnded] = useState(false);
    const [editedBMsg, setEditedBMsg] = useState<IBroadcastMsg>();
    const [newMsgController, setNewMsgController] = useState<boolean>(false);
    const [eventKey, setEventKey] = useState<"moderator" | "public">("moderator");

    const query = useQuery();
    const notify = useNotification();
    const {pitchesState, updatePitchesState} = usePitchesState();
    const {pusherState, updatePusherState} = usePusherState();
    const {authState} = useAuthState();
    const {BindEvents} = usePusher();
    const publicMessagesRef = useRef<any>();
    const eventId = query.get("pitch_event_id") || pitchesState.pitches?.[0]?.id;
    const {config, isLoadingConfig} = useAppConfig({config_key: "PUBLIC_PITCH_CHAT"});

    if (eventId && pusherState.publicChatHistory) {
        publicMessagesRef.current = pusherState.publicChatHistory || {};
    }
    const eventInfo = pitchesState.pitches && pitchesState.pitches.find(pitch => pitch.id === eventId);
    const counterStart = pitchesState.pitches && pitchesState.pitches.find(pitch => pitch.id === eventId)?.start_date;
    const pusherEventToSend = "MsgListener";
    const eventsToListen = [
        PusherEventsEnum.AdmMsgListener,
        PusherEventsEnum.Broadcast,
        PusherEventsEnum.PollTimer,
        PusherEventsEnum.PublicMsgListener,
        PusherEventsEnum.DeletePublicMsgListener,
        PusherEventsEnum.BanPublicUserListener,
        PusherEventsEnum.EditBroadcast,
    ];

    const handleSuccessModInfo = (response: AxiosResponse<IModInfo>) => {
        const pusherChannel = response.data.pusherChannel;
        const usersHistory: IChatMessagesList[] =
            response.data.usersHistory.sort((a, b) => (a.max_date > b.max_date ? -1 : 1)) || [];
        const broadcastHistory: IBroadcastMsg[] = response.data.broadcastHistory || [];
        const vendors: IVendors[] = response.data.vendors || [];
        const publicChatHistory = response.data.publicHistory || [];
        const sortedPublicChat = [...publicChatHistory, ...broadcastHistory].sort(sortPublicChat);
        setPusherChannel(pusherChannel);
        setVendorsArray(vendors);
        updatePusherState(UPDATE_PUSHER, {
            broadcastMsgs: broadcastHistory?.sort((a, b) => (a.date === b.date ? 0 : a.date > b.date ? -1 : 1)),
            chatList: usersHistory,
            publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: sortedPublicChat},
        });
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const handleEditBroadcast = useCallback((b: IBroadcastMsg) => {
        setEditedBMsg(b);
    }, []);

    const deletePublicMessage = useCallback(
        (msg: any) => {
            const msgId = typeof msg === "string" ? msg : msg.message_id;
            const found = publicMessagesRef.current[eventId!].find(chat => chat.id === msgId);
            if (!found) return;
            const newPublicChat = publicMessagesRef.current[eventId!].filter(chat => chat.id !== msgId);
            updatePusherState(UPDATE_PUSHER, {
                publicChatHistory: {...publicMessagesRef.current, [eventId!]: newPublicChat},
            });
        },
        //eslint-disable-next-line
        [eventId, pusherState.publicChatHistory],
    );

    const handleNewMsg = useCallback((msgToParse: string) => {
        const newChatMessage: IChat = JSON.parse(msgToParse);
        setNewMsg(newChatMessage);
    }, []);

    const handleNewBroadcastMsg = useCallback((msgToParse: string) => {
        const newBroadcastMessage: IBroadcastMsg = JSON.parse(msgToParse);
        setNewBMsg(newBroadcastMessage);
    }, []);

    const privateCallback = (data: IPusherData) => {
        const event = data.type || data.event;
        switch (event) {
            case PusherEvtTypesEnum.MsgListener:
                handleNewMsg(data.message);
                break;
            case PusherEvtTypesEnum.PollTimer:
                updatePusherState(UPDATE_PUSHER, {startPollTimer: true, pollEnds: data.pollEnds});
                break;
            case PusherEventsEnum.Broadcast:
                handleNewBroadcastMsg(data.message);
                break;
            case PusherEventsEnum.EditBroadcast:
                handleEditBroadcast(JSON.parse(data.message));
                break;
            case PusherEvtTypesEnum.PublicMsgListener:
                const parsedMsg = gZipInflate(data.message, "parsed");
                const newMessage: IPublicChat = {
                    id: parsedMsg.message_id,
                    message: parsedMsg.message,
                    message_type: "publicChat",
                    creator: parsedMsg.creator_user_id,
                    date: parsedMsg.created_at,
                    pusher_channel: PRIVATE_CHANNEL,
                    receiver: eventId || "",
                    can_ban: parsedMsg.can_ban,
                };
                setNewPublicMsg(newMessage);
                break;
            case PusherEventsEnum.DeletePublicMsgListener:
                deletePublicMessage(gZipInflate(data.message, "parsed"));
                break;
            case PusherEventsEnum.BanPublicUserListener:
                deletePublicMessage((data as any).message_id);
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        if (pusherState.privateChannel && authState.authenticated && eventId) {
            BindEvents(eventsToListen, privateCallback);
        }
        // eslint-disable-next-line
    }, [pusherState.privateChannel, eventId]);

    useEffect(() => {
        if (pusherState.privateChannel && newBMsg) {
            const now = DateTime.now().toISO();
            const newMessage: IBroadcastMsg = {...newBMsg, date: now, message_type: "broadcastChat"};
            const updatedBroadcastMsgs = [newBMsg, ...pusherState.broadcastMsgs];
            const sortedPublicChat = [...pusherState.publicChatHistory[eventId!], newMessage].sort(sortPublicChat);
            updatePusherState(UPDATE_PUSHER, {
                broadcastMsgs: updatedBroadcastMsgs,
                publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: sortedPublicChat},
            });
        }
        // eslint-disable-next-line
    }, [newBMsg]);

    useEffect(() => {
        if (editedBMsg) {
            const copy = [...pusherState.broadcastMsgs];
            const foundOldIndex = copy.findIndex(bMsg => bMsg.id === editedBMsg.edited_message_id);
            if (foundOldIndex > -1) {
                copy.splice(foundOldIndex, 1, editedBMsg);
                const newPublicChat = [...pusherState.publicChatHistory[eventId!]].map(chat => {
                    if (chat.id === editedBMsg.edited_message_id) {
                        return {...editedBMsg};
                    }
                    return chat;
                });
                updatePusherState(UPDATE_PUSHER, {
                    broadcastMsgs: copy,
                    publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: newPublicChat},
                });
            }
            setEditedBMsg(undefined);
        }
    }, [editedBMsg]);

    useEffect(() => {
        if (newPublicMsg) {
            const newChatHistory: IPublicChat[] = pusherState.publicChatHistory[eventId!]
                ? [...pusherState.publicChatHistory[eventId!]]
                : [];
            newChatHistory.push(newPublicMsg);
            updatePusherState(UPDATE_PUSHER, {
                publicChatHistory: {...pusherState.publicChatHistory, [eventId!]: newChatHistory},
            });
            setNewPublicMsg(undefined);
        }
    }, [newPublicMsg]);

    useEffect(() => {
        if (!eventId || !eventInfo) {
            updatePitchesState(GET_NOT_STARTED);
            return;
        }
        if (!pusherChannel && eventId) {
            eventService.loadModInfo(eventId, handleSuccessModInfo, handleError);
        }
        let tempEventEnded = false;
        if (eventInfo && eventInfo?.end_date) {
            tempEventEnded = DateTime.fromISO(eventInfo.end_date).diff(DateTime.now()).as("seconds") < 0;
        }
        setEventEnded(tempEventEnded);
        // eslint-disable-next-line
    }, [eventInfo]);

    useEffect(() => {
        if (newMsg && eventKey !== "moderator") {
            setNewMsgController(true);
        }
        if (eventKey === "moderator") {
            setNewMsgController(false);
        }
    }, [eventKey, newMsg]);

    return (
        <ErrorBoundary>
            <Helmet>
                <title>{eventInfo?.name || "Channel Engage"}</title>
                <meta name="description" content={eventInfo?.description} />
            </Helmet>
            <div className="newRoute">
                <Box className="newContainer">
                    <TitleContainer as="h2" position="start" text={eventInfo?.name || "Channel Engage"} />
                    <Typography variant="h4" className="mb-3">
                        {eventInfo?.description || "Channel Engage"}
                    </Typography>
                    <Box className={styles.topContainer}>
                        <div className={styles.videoContainer}>
                            <div className={styles.timeBanner}>
                                <p>
                                    {counterStart ? (
                                        <TimeCounter
                                            counterStart={counterStart}
                                            eventEnded={eventEnded}
                                            beforeStartText="Event starting in:"
                                            afterStartText="Stream duration:"
                                        />
                                    ) : (
                                        "Event not started"
                                    )}
                                </p>
                            </div>
                            {!eventEnded && pitchesState.pitches?.length && <VideoPlayer />}
                            {eventEnded && (
                                <div className="errorColor h-100 w-100 d-flex flex-column align-items-center justify-content-center">
                                    <ErrorIcon version="triangle" size="40" />
                                    <SubtitleContainer
                                        as="p"
                                        text="The Channel Engage event has ended!"
                                        className="mt-3"
                                    />
                                </div>
                            )}
                        </div>
                        <div className={styles.pollingContainer}>
                            <PollsBroadcastTabs
                                vendorArray={vendorsArray}
                                pusherChannel={pusherChannel}
                                setEventEnded={setEventEnded}
                                eventId={eventId || ""}
                            />
                        </div>
                    </Box>
                    <Box className={styles.bottomContainer}>
                        <Tab.Container defaultActiveKey="moderator">
                            <Nav className="d-flex flex-row align-items-center">
                                <Nav.Item>
                                    <Nav.Link
                                        eventKey="moderator"
                                        className={styles.chatListHeaderTitle}
                                        onClick={() => setEventKey("moderator")}>
                                        <ChatBubblesIcon size={18} />
                                        <TitleContainer
                                            as="h4"
                                            text="MODERATOR CHAT"
                                            position="start"
                                            noConnector
                                            className="mb-0"
                                        />
                                        {newMsgController && <NotificationDot />}
                                    </Nav.Link>
                                </Nav.Item>

                                {!isLoadingConfig && config && config?.value.toUpperCase() === "TRUE" && (
                                    <Nav.Item>
                                        <Nav.Link
                                            eventKey="public"
                                            className={styles.chatListHeaderTitle}
                                            onClick={() => setEventKey("public")}>
                                            <ChatBubblesIcon size={18} />
                                            <TitleContainer
                                                as="h4"
                                                text="PUBLIC CHAT"
                                                position="start"
                                                noConnector
                                                className="mb-0"
                                            />
                                        </Nav.Link>
                                    </Nav.Item>
                                )}
                            </Nav>
                            <Tab.Content className={styles.actionsContainer}>
                                <Tab.Pane eventKey="moderator" className="w-100">
                                    {eventId && (
                                        <AdminChat
                                            pusherChannel={pusherChannel}
                                            eventId={eventId}
                                            pusherEventToSend={pusherEventToSend}
                                            newMsg={newMsg}
                                            setNewMsg={setNewMsg}
                                        />
                                    )}
                                </Tab.Pane>
                                <Tab.Pane eventKey="public" className="w-100">
                                    <PublicChatBox
                                        pusherChannel={pusherChannel}
                                        eventId={eventId!}
                                        allowEmojis
                                        tabActive={"test"}
                                    />
                                </Tab.Pane>
                            </Tab.Content>
                        </Tab.Container>
                    </Box>
                </Box>
            </div>
        </ErrorBoundary>
    );
}
