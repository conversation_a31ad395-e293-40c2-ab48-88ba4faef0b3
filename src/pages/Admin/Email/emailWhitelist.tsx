import {AxiosError} from "axios";
import {use<PERSON><PERSON>back, useEffect, useState} from "react";
import {Form<PERSON>rovider, useForm, useWatch} from "react-hook-form";
import {<PERSON>} from "react-router-dom";
import {IEmailWhitelist} from "../../../Interfaces/emailWhiteList.interface";
import InfoBanner from "../../../components/DescriptionBanner/infoBanner.component";
import ButtonComponent from "../../../components/FormControls/button.component";
import TextBoxComponent from "../../../components/FormControls/textBox.component";
import {InfoIcon} from "../../../components/Icons";
import {ModalComponent} from "../../../components/Modal";
import {TableComponent} from "../../../components/Table/table.component";
import TitleContainer from "../../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import useAuthState from "../../../hooks/useAuthState";
import {useDebounce} from "../../../hooks/useDebounce";
import useNotification from "../../../hooks/useNotification";
import {emailWhitelistService} from "../../../services/emailwhitelist.service";
import styles from "../../../styles/components/registerForm.module.sass";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";

interface IProps {
    onSuccess: Function;
}

export default function AdminEmailWhitelist() {
    const methods = useForm();
    const searchWatcher = methods.watch("search");
    const searchWord = useDebounce(searchWatcher, 500);
    const [isDeleting, setIsDeleting] = useState<boolean>(false);
    const [newEmailModal, setNewEmailModal] = useState(false);
    const [dataRefetchKey, setDataRefetchKey] = useState(0);
    const notify = useNotification();

    useEffect(() => {
        if (searchWord && searchWord.length < 3) {
            notify("The search word must be at least 3 characters.", "Error");
        }
    }, [searchWord]);

    const ButtonCell = useCallback(tableData => {
        return (
            <ButtonComponent
                id="removePitchBtn"
                type="button"
                className={styles.removeBtn}
                onClick={() => handleDelete(tableData?.row?.original?.id)}>
                {isDeleting ? <Loader inline loading /> : "Remove"}
            </ButtonComponent>
        );
    }, []);

    const NameCell = useCallback(tableData => {
        return (
            <Link to={tableData?.row?.original?.user?.profile_link} target="_self">
                {tableData?.row?.original?.user?.name}
            </Link>
        );
    }, []);

    const handleDelete = async (id: string) => {
        const answer = await getUserConfirmation("Are you sure you want to delete this domain?");
        if (answer.value) {
            if (id !== "") {
                setIsDeleting(true);
                emailWhitelistService.delete(id, handleError, () => handleSuccess(`Domain deleted!`, "Delete"));
            }
        }
    };

    const handleError = (error: AxiosError<any>) => {
        setIsDeleting(false);
        notify(getErrorFromArray(error), "Error");
    };

    const handleSuccess = (msg: string, type: string) => {
        if (type === "Add") setNewEmailModal(false);
        else setIsDeleting(false);
        setDataRefetchKey(dataRefetchKey => dataRefetchKey + 1);
        notify(msg, "Success");
    };

    const columns: any = [
        {Header: "Id", accessor: "id"},
        {Header: "Domain", accessor: "domain", sortable: true},
        {Header: "Reason", accessor: "reason"},
        {Header: "User Responsible", accessor: "", Cell: NameCell},
        {Header: "Action", accessor: "", Cell: ButtonCell},
    ];

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                {/* <div className={`d-flex ${styles.headerLink}`} onClick={() => navigate(-1)}>
                <ArrowBackIcon size={20} /> <span>Back</span>
            </div> */}
                <FormProvider {...methods}>
                    <form className="d-flex justify-content-between align-items-center">
                        <TitleContainer
                            as="h1"
                            position="start"
                            text={routeConfig.AdminEmailWhitelist.name}
                            className="mxx-2"
                        />
                        <TextBoxComponent id="search" placeholder="Search for Domain" className={styles.searchInput} />
                        <ButtonComponent
                            id="addCompany"
                            type="button"
                            className={styles.addButton}
                            onClick={() => setNewEmailModal(true)}>
                            Add
                        </ButtonComponent>
                    </form>
                </FormProvider>
                <InfoBanner type="info">
                    <div className="d-flex py-3 px-2 align-items-center">
                        <InfoIcon size={24} />
                        <p className="m-0 ms-2">
                            Domains added here will be allowed to Create an Account with Channel Program regardless of
                            the domain risk. Make sure to add the ".com, .edu,".
                        </p>
                    </div>
                </InfoBanner>
                <TableComponent
                    id="emailWhitelistManagement"
                    columns={columns}
                    hiddenColumns={["id"]}
                    paginated
                    searchWord={searchWord && searchWord.length > 2 ? searchWord : ""}
                    serviceHandler={emailWhitelistService.getEmailWhiteListed}
                    refetchKey={dataRefetchKey}
                />

                {newEmailModal && (
                    <ModalComponent
                        isStatic
                        showHeader
                        headerTitle="Add Domain"
                        onCancel={() => setNewEmailModal(false)}
                        modalSwitcher={newEmailModal}
                        modalSwitcherCallback={setNewEmailModal}
                        showCancelButton={false}
                        showOkButton={false}
                        form={<AddDomainForm onSuccess={handleSuccess} />}
                    />
                )}
            </div>
        </div>
    );
}

const AddDomainForm = (props: IProps) => {
    const addMethods = useForm();
    const watcher = useWatch({control: addMethods.control});
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const {authState} = useAuthState();
    const notify = useNotification();
    // const domainRegex = /([A-Za-z0-9_-]+)/;
    const domainRegex = /(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]/;
    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setIsSaving(false);
    };

    const handleSuccess = (msg: string, type: string) => {
        setIsSaving(false);
        addMethods.reset();
        props.onSuccess(msg, type);
    };

    const handleDomainAdd = () => {
        const domain: string = watcher.addDomain;
        const reason: string = watcher.reason;

        if (domain !== "" && !domainRegex.test(domain)) {
            addMethods.setError("addDomain", {type: "manual"});
            notify("The domain may only contain letters, numbers, dashes and underscores.", "Error");
            return;
        }

        if (domain !== "" && reason !== "") {
            setIsSaving(true);

            const data: IEmailWhitelist = {
                domain: domain,
                reason: reason,
                user_id_responsible: authState.id!.toString(),
            };
            emailWhitelistService.create(data, handleError, () => handleSuccess(`Domain ${domain} saved!`, "Add"));
        }
    };
    return (
        <div className={styles.formContainer}>
            <FormProvider {...addMethods}>
                <form onSubmit={addMethods.handleSubmit(handleDomainAdd)}>
                    <TextBoxComponent
                        className="mb-1 col-12"
                        id="addDomain"
                        label="Domain"
                        containerClassName={styles.contactInputLeft}
                        maxLength={100}
                        isRequired={true}
                    />
                    <TextBoxComponent
                        className="mb-1 col-12"
                        id="reason"
                        label="Reason"
                        containerClassName={styles.contactInputLeft}
                        maxLength={1000}
                        isRequired={true}
                    />
                    <div className="d-flex justify-content-center mt-4 flex-wrap">
                        <ButtonComponent id="addDomainBtn" className="cpBlueBtn order-1 order-md-2">
                            {isSaving ? <Loader loading inline version="iconWhite" /> : "Add"}
                        </ButtonComponent>
                    </div>
                </form>
            </FormProvider>
        </div>
    );
};
