import {useTheme} from "@mui/material";
import {useCallback, useState} from "react";
import {DEFAULT_QUERY_CONFIGS, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import Badge from "../../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Tab from "../../../uicomponents/Atoms/Tabs/Tab.component";
import Tabs from "../../../uicomponents/Atoms/Tabs/Tabs.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import DealsTable, {IChannelDealPaginationData} from "./Deals/DealsTable";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import {Add} from "@mui/icons-material";
import useFilters, {CHANNEL_DEAL_REQUESTS_FILTERS, CHANNEL_DEALS_FILTERS} from "../../../hooks/fetches/useFilters";
import {useDebounce} from "../../../hooks/useDebounce";
import {useQuery, UseQueryResult} from "@tanstack/react-query";
import {IChannelDealData} from "../../../Interfaces/channelDeal.interface";
import ChannelDealRequestsTable, {IChannelDealRequestPaginationData} from "./DealRequests/ChannelDealRequestsTable";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import CrudChannelDealDrawer, {
    ICrudChanelDealDrawerProps,
} from "../../../uicomponents/Molecules/ChannelDeals/CrudChannelDealDrawer/CrudChannelDealDrawer.component";
import ChannelDealRequestDrawer, {
    ICrudChanelDealRequestDrawerProps,
} from "../../../uicomponents/Molecules/ChannelDeals/ChannelDealRequestDrawer/ChannelDealRequestDrawer.component";
import {IChannelDealRequestData} from "../../../Interfaces/adminChannelDealRequest.interface";
import {channelDealService} from "../../../services/channelDealService";
import {AxiosError, AxiosResponse} from "axios";
import {
    QUERY_ADMIN_CHANNEL_DEALS_PENDING,
    QUERY_ADMIN_CHANNEL_DEALS_REQUESTS_PENDING,
} from "../../../constants/queryKeys.constant";
import useChannelDealsStatuses from "../../../hooks/fetches/useChannelDealsStatuses";
import {CHANNEL_DEALS_CLAIMED_PENDING, CHANNEL_DEALS_STATUSES_PENDING} from "../../../constants/channeldeals.constant";
import useChannelDealRequestStatuses from "../../../hooks/fetches/useChannelDealRequestStatuses";

type TTabs = "channelDeals" | "channelDealRequests";

const CHANNEL_DEALS_TAB = "channelDeals";
const CHANNEL_DEALS_REQUESTS_TAB = "channelDealRequests";
export default function ChannelDealsManagementPage() {
    const [crudDealDrawerProps, setCrudDealDrawerProps] = useState<ICrudChanelDealDrawerProps>({
        open: false,
    });

    const [channelDealRequestDrawerProps, setChannelDealRequestDrawerProps] =
        useState<ICrudChanelDealRequestDrawerProps>({
            open: false,
        });

    const [selectedTab, setSelectedTab] = useState<TTabs>(CHANNEL_DEALS_TAB);
    const theme = useTheme();
    const [searchContents, setSearchContents] = useState<string>("");
    const [dealsTableQuery, setDealsTableQuery] = useState<UseQueryResult<IChannelDealPaginationData> | null>(null);
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const {getStatus} = useChannelDealsStatuses();
    const {getStatus: getStatusRequest} = useChannelDealRequestStatuses();

    const [dealsRequestTableQuery, setDealsRequestTableQuery] =
        useState<UseQueryResult<IChannelDealRequestPaginationData> | null>(null);

    const {filtersQuery: channelDealsFilters, filterState: channelDealsFilterState} = useFilters({
        filterKey: CHANNEL_DEALS_FILTERS,
    });

    const {filtersQuery: channelDealRequestFilters, filterState: channelDealsRequestFilterState} = useFilters({
        filterKey: CHANNEL_DEAL_REQUESTS_FILTERS,
    });

    const [channelDealsFilterContents, setChannelDealsFilterContents] = channelDealsFilterState;
    const [channelDealRequestFilterContents, setChannelDealRequestFilterContents] = channelDealsRequestFilterState;
    const debouncedSearch = useDebounce(searchContents, 500);

    const adminChannelDealsDebouncedFilter = useDebounce(channelDealsFilterContents, 500);
    const adminChannelDealsRequestDebouncedFilter = useDebounce(channelDealRequestFilterContents, 500);

    const hasEditAccess =
        !isLoadingPermissions && hasPermissions([PERMISSION_GROUPS.ADMIN_MANAGE_CHANNEL_DEALS_UPDATE]);

    const channelDealsPendingQuery = useQuery<IChannelDealData[]>(
        [QUERY_ADMIN_CHANNEL_DEALS_PENDING, getStatus(CHANNEL_DEALS_STATUSES_PENDING)?.id],
        () => {
            return new Promise((resolve, reject) => {
                return channelDealService
                    .getChannelDeals({
                        dynamic: {
                            status: [getStatus(CHANNEL_DEALS_STATUSES_PENDING)?.id || ""],
                        },
                    })
                    .then((r: AxiosResponse<any>) => {
                        resolve(r.data);
                    })
                    .catch((e: AxiosError<any>) => reject(e));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const channelDealsRequestsPendingQuery = useQuery<IChannelDealData[]>(
        [QUERY_ADMIN_CHANNEL_DEALS_REQUESTS_PENDING, getStatusRequest(CHANNEL_DEALS_CLAIMED_PENDING)?.id],
        () => {
            return new Promise((resolve, reject) => {
                return channelDealService
                    .getChannelDealRequests({
                        dynamic: {
                            status: [getStatusRequest(CHANNEL_DEALS_CLAIMED_PENDING)?.id || ""],
                        },
                    })
                    .then((r: AxiosResponse<any>) => {
                        resolve(r.data);
                    })
                    .catch((e: AxiosError<any>) => reject(e));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const handleChannelDealsSortChange = (sortBy: string, order_by: string) => {
        if (setChannelDealsFilterContents) {
            setChannelDealsFilterContents({
                ...channelDealsFilterContents,
                sort: `sorting_${order_by}__${sortBy}`,
            });
        }
    };

    const handleChannelDealRequestSortChange = (sortBy: string, order_by: string) => {
        if (setChannelDealRequestFilterContents) {
            setChannelDealRequestFilterContents({
                ...channelDealRequestFilterContents,
                sort: `sorting_${order_by}__${sortBy}`,
            });
        }
    };

    const setTab = (e: React.SyntheticEvent<Element, Event>, value: TTabs) => {
        setSelectedTab(value);
    };

    const handleAddNewDeal = () => {
        setCrudDealDrawerProps({...crudDealDrawerProps, open: true});
    };

    const addNewAction = {
        primary: {
            id: "add-new-channel-deal",
            children: (
                <>
                    <Add htmlColor="white" sx={{fontSize: "18px"}} /> Add New
                </>
            ),
            onClick: handleAddNewDeal,
        },
    };

    const handleDealsTableLoaded = (dealsQuery: UseQueryResult<IChannelDealPaginationData>) => {
        if (dealsQuery?.data) {
            setDealsTableQuery(dealsQuery);
        }
    };

    const handleDealRequestTableLoaded = (dealRequestQuery: UseQueryResult<IChannelDealRequestPaginationData>) => {
        if (dealRequestQuery?.data) {
            setDealsRequestTableQuery(dealRequestQuery);
        }
    };

    const renderTab = (tab: TTabs, text: string, badgeValue: number) => (
        <Tab
            value={tab}
            label={
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                    }}>
                    <Typography
                        fontInter
                        variant="body3"
                        sx={{
                            color: theme.palette.blue[800],
                            fontWeight: selectedTab === tab ? "600!important" : "400!important",
                        }}>
                        {text}
                    </Typography>
                    <Badge
                        color={"gradient"}
                        padding="2px 6px"
                        style={{
                            background: theme.palette.gradient.primary,
                            border: "none",
                            minWidth: "initial",
                        }}>
                        <Typography color={"white"}>{badgeValue}</Typography>
                    </Badge>
                </Box>
            }
        />
    );
    const renderTabs = () => (
        <Box sx={{display: "flex", gap: ``, flexDirection: "column"}}>
            <Box
                sx={{
                    display: "flex",
                    gap: 2,
                    alignItems: "center",
                    justifyContent: "space-between",
                }}>
                <Tabs onChange={setTab} value={selectedTab}>
                    {renderTab(CHANNEL_DEALS_TAB, "Submitted Deals", channelDealsPendingQuery.data?.length || 0)}
                    {renderTab(
                        CHANNEL_DEALS_REQUESTS_TAB,
                        "Deals Requested",
                        channelDealsRequestsPendingQuery.data?.length || 0,
                    )}
                </Tabs>
            </Box>
        </Box>
    );

    const refreshDealsTableQuery = useCallback(() => {
        channelDealsFilters.refetch();
        if (dealsTableQuery) {
            dealsTableQuery.refetch();
        }
    }, [dealsTableQuery]);

    const handleCloseDrawer = useCallback(() => {
        setCrudDealDrawerProps({...crudDealDrawerProps, open: false, channelDeal: undefined});
    }, []);

    const handleCloseRequestDrawer = useCallback(() => {
        setChannelDealRequestDrawerProps({
            ...channelDealRequestDrawerProps,
            open: false,
        });
    }, []);

    const closeAndRefreshTable = () => {
        refreshDealsTableQuery();
        setCrudDealDrawerProps({...crudDealDrawerProps, open: false});
    };

    const handleOnUpdateDeal = useCallback(() => {
        dealsTableQuery?.refetch();
    }, [dealsTableQuery]);

    const handleOnUpdateRequestDeal = useCallback(() => {
        dealsRequestTableQuery?.refetch();
        setChannelDealRequestDrawerProps({
            ...channelDealRequestDrawerProps,
            open: false,
        });
    }, [dealsRequestTableQuery]);

    const handleViewDeal = (channelDeal: IChannelDealData) => {
        setCrudDealDrawerProps({...crudDealDrawerProps, open: true, channelDeal: channelDeal, readMode: true});
    };

    const handleViewDealRequest = (channelDealRequest: IChannelDealRequestData) => {
        setChannelDealRequestDrawerProps({...channelDealRequest, open: true, channelDealRequest: channelDealRequest});
    };

    const handleEditDeal = (channelDeal: IChannelDealData) => {
        setCrudDealDrawerProps({...crudDealDrawerProps, open: true, channelDeal: channelDeal, readMode: false});
    };

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <PageInnerHeader
                    id="channel-deals-management"
                    title={{
                        text: "Channel Deal Management",
                        sx: {
                            fontSize: "26px!important",
                            fontWeight: "400 !important",
                            color: "neutral.700",
                        },
                    }}
                    searchBoxProps={{
                        placeholder: "Start typing to search....",
                    }}
                    searchState={[
                        searchContents,
                        search => {
                            setSearchContents(search);
                        },
                    ]}
                    growSearch
                    searchWrapperSx={{
                        marginTop: "16px",
                        flexDirection: "column",
                        alignItems: "initial",
                        gap: "8px !important",
                    }}
                    filtersWrapperSx={{
                        justifyContent: "start !important",
                    }}
                    filterKeysOrder={[
                        "featured",
                        "category",
                        "sub_category",
                        "vendor",
                        "status",
                        "date",
                        "expiry_date",
                    ]}
                    hideFilterKeys={["sort"]}
                    filterState={
                        selectedTab === CHANNEL_DEALS_TAB
                            ? [channelDealsFilterContents, setChannelDealsFilterContents]
                            : [channelDealRequestFilterContents, setChannelDealRequestFilterContents]
                    }
                    filters={
                        selectedTab === CHANNEL_DEALS_TAB ? channelDealsFilters.data : channelDealRequestFilters.data
                    }
                    actions={hasEditAccess ? addNewAction : undefined}
                />
                {renderTabs()}

                <DealsTable
                    showTable={selectedTab === CHANNEL_DEALS_TAB}
                    onQueryResultFetched={handleDealsTableLoaded}
                    debouncedSearch={debouncedSearch}
                    debouncedFilter={adminChannelDealsDebouncedFilter}
                    channelDealsFilterContent={channelDealsFilterContents}
                    onDealViewClicked={handleViewDeal}
                    onEditClicked={handleEditDeal}
                    onHandleSortChange={handleChannelDealsSortChange}
                    onClickAddNewDeal={handleAddNewDeal}
                />

                <ChannelDealRequestsTable
                    showTable={selectedTab === CHANNEL_DEALS_REQUESTS_TAB}
                    onQueryResultFetched={handleDealRequestTableLoaded}
                    debouncedSearch={debouncedSearch}
                    debouncedFilter={adminChannelDealsRequestDebouncedFilter}
                    channelDealRequestsFilterContent={channelDealRequestFilterContents}
                    onHandleSortChange={handleChannelDealRequestSortChange}
                    onDealRequestViewClicked={handleViewDealRequest}
                />

                {crudDealDrawerProps.open && (
                    <CrudChannelDealDrawer
                        {...crudDealDrawerProps}
                        isAdminPanel
                        onCreate={closeAndRefreshTable}
                        onUpdate={handleOnUpdateDeal}
                        onClose={handleCloseDrawer}
                        onDeletedDeal={() => closeAndRefreshTable()}
                    />
                )}

                <ChannelDealRequestDrawer
                    {...channelDealRequestDrawerProps}
                    onUpdate={handleOnUpdateRequestDeal}
                    onClose={handleCloseRequestDrawer}
                />
            </div>
        </div>
    );
}
