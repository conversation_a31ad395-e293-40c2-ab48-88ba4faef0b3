import {VisibilityOutlined} from "@mui/icons-material";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DeclineIcon from "@mui/icons-material/Close";
import ApproveIcon from "@mui/icons-material/Done";
import {useTheme} from "@mui/material";
import {SvgIconOwnProps} from "@mui/material/SvgIcon/SvgIcon";
import {useQuery, useQueryClient, UseQueryResult} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {memo, useCallback, useEffect, useMemo, useState} from "react";
import {Column} from "react-table";
import {
    IChannelDealRequestData,
    IUpdateChannelDealRequest,
} from "../../../../Interfaces/adminChannelDealRequest.interface";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {INewFilters} from "../../../../Interfaces/filters.interface";
import {IMeta, IPaginationData} from "../../../../Interfaces/pagination.interface";
import {TableV2Component} from "../../../../components/Table/TableV2.component";
import {
    CHANNEL_DEALS_CLAIMED_APPROVED,
    CHANNEL_DEALS_CLAIMED_DECLINED,
} from "../../../../constants/channeldeals.constant";
import {DEFAULT_QUERY_CONFIGS} from "../../../../constants/commonStrings.constant";
import useChannelDealRequestStatuses from "../../../../hooks/fetches/useChannelDealRequestStatuses";
import useNotification from "../../../../hooks/useNotification";
import usePermissions from "../../../../hooks/usePermissions";
import {NestedObject} from "../../../../models";
import {channelDealService} from "../../../../services/channelDealService";
import Box from "../../../../uicomponents/Atoms/Box/Box.component";
import CellActionsMenu from "../../../../uicomponents/Atoms/Table/CellActionsMenu.component";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import ChannelDealRequestStatusBadge from "../../../../uicomponents/Badges/channelDealRequestStatusBadge.component";
import AvatarComponentV2 from "../../../../uicomponents/Molecules/Avatar/avatarV2.component";
import CustomPagination from "../../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import {IMenuButtonItem} from "../../../../uicomponents/Molecules/MenuButton/menuButton.component";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../../utils/error.util";
import {clearFilters} from "../../../../utils/filters.util";
import {DATE_FORMAT, formatDate} from "../../../../utils/formatDate";
import {reduceString} from "../../../../utils/reduceString.util";
import DeclineConfirmation from "../Deals/DeclineConfirmation";
import {getApproveRequestDialogConfigs} from "../Deals/getDialogConfigs.util";
import Loader from "../../../../utils/loader";
import {QUERY_ADMIN_CHANNEL_DEALS_REQUESTS_PENDING} from "../../../../constants/queryKeys.constant";

type TNestedChannelDealData = NestedObject<IChannelDealRequestData & {status_name: string}>;

export type IChannelDealRequestPaginationData = IPaginationData<IChannelDealRequestData[]>;

interface IProps {
    showTable?: boolean;
    debouncedSearch: string;
    debouncedFilter: INewFilters | undefined;
    onHandleSortChange?: (sortBy: string, order_by: string) => void;
    onDealRequestViewClicked?: (data: IChannelDealRequestData) => void;
    channelDealRequestsFilterContent: INewFilters | undefined;
    onQueryResultFetched?: (data: UseQueryResult<IChannelDealRequestPaginationData>) => void;
}

const ChannelDealRequestsTable = memo(
    ({
        showTable = true,
        debouncedSearch,
        debouncedFilter,
        onHandleSortChange,
        channelDealRequestsFilterContent,
        onQueryResultFetched,
        onDealRequestViewClicked,
    }: IProps) => {
        const [page, setPage] = useState(1);
        const [itemsPerPage, setItemsPerPage] = useState<number>(40);
        const [declining, setDeclining] = useState<{[key: string]: boolean}>({});
        const [approving, setApproving] = useState<{[key: string]: boolean}>({});

        const [declineDealRequest, setDeclineDealRequest] = useState<IChannelDealRequestData | undefined>(undefined);

        const notify = useNotification();
        const queryClient = useQueryClient();

        const theme = useTheme();
        const {getStatus, isLoading: isChannelDealRequestStatusLoading} = useChannelDealRequestStatuses();
        const {hasPermissions, isLoadingPermissions} = usePermissions();

        const hasEditAccess =
            !isLoadingPermissions && hasPermissions([PERMISSION_GROUPS.ADMIN_MANAGE_CHANNEL_DEALS_UPDATE]);

        const checkLoading = (id: string) => approving[id] || declining[id];

        useEffect(() => {
            setPage(1);
        }, [channelDealRequestsFilterContent]);

        const channelDealRequestsQuery = useQuery<IChannelDealRequestPaginationData>(
            [
                "admin-channel-deal-requests",
                debouncedSearch,
                JSON.stringify(clearFilters(debouncedFilter)),
                itemsPerPage,
                page,
            ],
            async () => {
                return new Promise<IChannelDealRequestPaginationData>((resolve, reject) => {
                    channelDealService.getChannelDealRequests(
                        {
                            page,
                            items_per_page: itemsPerPage,
                            search_word: debouncedSearch,
                            dynamic: clearFilters(channelDealRequestsFilterContent),
                        },
                        (res: AxiosResponse<IChannelDealRequestPaginationData>) => {
                            resolve(res.data);
                        },
                        err => {
                            reject(err);
                        },
                    );
                });
            },
            {
                ...DEFAULT_QUERY_CONFIGS(),
            },
        );

        const handleSortChange = (sortBy: string, order_by: string) => {
            if (onHandleSortChange) {
                onHandleSortChange(sortBy, order_by);
            }
        };

        const updateDealRequest = (
            dealId: string,
            data: IUpdateChannelDealRequest,
            message: string,
            onSuccess: Function | null = null,
        ) => {
            channelDealService
                .updateChannelDealRequest(dealId, {...data})
                .then(() => {
                    queryClient.refetchQueries([QUERY_ADMIN_CHANNEL_DEALS_REQUESTS_PENDING]);
                    notify(message);
                    channelDealRequestsQuery.refetch();
                    onSuccess && onSuccess();
                    setApproving({...approving, [dealId]: false});
                    setDeclining({...declining, [dealId]: false});
                })
                .catch(err => notify(getErrorFromArray(err), "Error"));
        };

        const handleApproveClick = async (channelDealRequest: IChannelDealRequestData) => {
            const answer = await getUserConfirmation("", getApproveRequestDialogConfigs(channelDealRequest));

            if (answer.value) {
                setApproving({...approving, [channelDealRequest.id]: true});
                const approvedStatus = getStatus(CHANNEL_DEALS_CLAIMED_APPROVED);
                updateDealRequest(
                    channelDealRequest.id,
                    {status_id: approvedStatus?.id || ""},
                    "Deal Request Approved",
                );
            }
        };

        const renderApproveIcon = (channelDealRequest: IChannelDealRequestData) => {
            if (approving[channelDealRequest.id]) {
                return <Loader inline loading version="iconBlue" />;
            }

            if ([CHANNEL_DEALS_CLAIMED_APPROVED].includes(channelDealRequest.status?.key || "")) {
                return <CheckCircleIcon color={"disabled"} />;
            }
            const hasNotUserInfo = !channelDealRequest.user?.email;
            if (channelDealRequest.status?.key === CHANNEL_DEALS_CLAIMED_DECLINED || hasNotUserInfo) {
                return <ApproveIcon titleAccess={hasNotUserInfo ? "User Invitation Pending" : ""} color={"disabled"} />;
            }

            return (
                <ApproveIcon
                    style={{cursor: checkLoading(channelDealRequest.id) ? "not-allowed" : "pointer"}}
                    color={checkLoading(channelDealRequest.id) ? "disabled" : "success"}
                    onClick={() => {
                        if (checkLoading(channelDealRequest.id)) return;
                        handleApproveClick(channelDealRequest);
                    }}
                />
            );
        };

        const renderDeclineIcon = (channelDealRequest: IChannelDealRequestData) => {
            if (declining[channelDealRequest.id]) {
                return <Loader inline loading version="iconBlue" />;
            }

            const hasNotUserInfo = !channelDealRequest.user?.email;
            if ([CHANNEL_DEALS_CLAIMED_APPROVED].includes(channelDealRequest.status?.key || "") || hasNotUserInfo) {
                return <DeclineIcon color={"disabled"} titleAccess={hasNotUserInfo ? "User Invitation Pending" : ""} />;
            }

            if (channelDealRequest.status?.key === CHANNEL_DEALS_CLAIMED_DECLINED) {
                return <CancelIcon color={"disabled"} />;
            }

            return (
                <DeclineIcon
                    titleAccess={"Decline Deal"}
                    style={{cursor: checkLoading(channelDealRequest.id) ? "not-allowed" : "pointer"}}
                    color={checkLoading(channelDealRequest.id) ? "disabled" : "error"}
                    onClick={() => {
                        setDeclineDealRequest(channelDealRequest);
                    }}
                />
            );
        };

        useEffect(() => {
            if (onQueryResultFetched && channelDealRequestsQuery.data) {
                onQueryResultFetched(channelDealRequestsQuery);
            }
        }, [
            channelDealRequestsQuery.isFetched,
            channelDealRequestsQuery.isLoading,
            channelDealRequestsQuery.isFetchedAfterMount,
            channelDealRequestsQuery.isRefetching,
        ]);

        const metadata: IMeta = channelDealRequestsQuery.data?.meta ?? ({} as IMeta);
        const tableData = channelDealRequestsQuery.data?.data || [];

        const renderChannelDealsMenuItems: (rowData: IChannelDealRequestData) => IMenuButtonItem[] = (
            rowData: IChannelDealRequestData,
        ) => {
            const defaultIconProps: SvgIconOwnProps = {
                htmlColor: theme.palette.neutral[500],
                sx: {fontSize: "20px"},
            };

            const defaultActions: IMenuButtonItem[] = [];

            const viewMenu = {
                id: "view",
                children: (
                    <>
                        <VisibilityOutlined {...defaultIconProps} />
                        <Typography color={"blue.800"}>View</Typography>
                    </>
                ),
                onClick: () => {
                    onDealRequestViewClicked && onDealRequestViewClicked(rowData);
                },
            };

            if (!hasEditAccess) {
                return [viewMenu];
            }

            defaultActions.push(viewMenu);

            if (
                ![CHANNEL_DEALS_CLAIMED_APPROVED, CHANNEL_DEALS_CLAIMED_DECLINED].includes(rowData.status?.key || "") &&
                rowData.user?.email
            ) {
                defaultActions.push({
                    id: "approve",
                    children: (
                        <>
                            <ApproveIcon {...defaultIconProps} />
                            <Typography color={"blue.800"}>Approve</Typography>
                        </>
                    ),
                    onClick: () => {
                        handleApproveClick(rowData);
                    },
                });

                defaultActions.push({
                    id: "decline",
                    children: (
                        <>
                            <DeclineIcon {...defaultIconProps} />
                            <Typography color={"blue.800"}>Decline</Typography>
                        </>
                    ),
                    onClick: () => {
                        setDeclineDealRequest(rowData);
                    },
                });
            }

            return defaultActions;
        };

        const cellActions = useCallback(
            tableData => {
                return tableData?.row?.original ? (
                    <CellActionsMenu
                        id={`more-actions-${tableData.row.original.id}`}
                        items={renderChannelDealsMenuItems(tableData.row.original)}
                    />
                ) : null;
            },
            [isChannelDealRequestStatusLoading, approving, declining],
        );

        const renderDealName = (channelDealRequest: IChannelDealRequestData) => {
            return (
                <Box
                    tooltip={{
                        title: channelDealRequest.deal?.deal_name || "",
                    }}>
                    <Typography
                        style={{cursor: "pointer"}}
                        color={theme.palette.blue[600]}
                        fontWeight={600}
                        onClick={() => onDealRequestViewClicked && onDealRequestViewClicked(channelDealRequest)}>
                        {reduceString(channelDealRequest.deal?.deal_name || "", 25)}
                    </Typography>
                </Box>
            );
        };

        const renderCompanyAvatar = (
            avatar: string | undefined,
            companyName: string | undefined,
            type: "vendor" | "msp",
        ) => {
            return useMemo(() => {
                return (
                    <Box display={"flex"} gap={"10px"} alignItems={"center"}>
                        <AvatarComponentV2
                            isCompany
                            user={{
                                avatar: avatar || "",
                                type,
                                profile_type: type,
                                name: companyName || "",
                                friendly_url: "",
                            }}
                            size={28}
                            showProfileType
                            profileTypeMinimized
                            disableLink
                        />
                        {companyName}
                    </Box>
                );
            }, [avatar, companyName]);
        };

        const columns: Column<TNestedChannelDealData>[] = [
            {Header: "Id", accessor: "id"},
            {
                id: "deal.deal_name",
                Header: "Deal Name",
                Cell: ({row}) => renderDealName(row?.original),
                defaultCanSort: true,
            },
            {
                id: "deal.company",
                Header: "Vendor",
                Cell: ({row}) =>
                    renderCompanyAvatar(
                        row?.original?.deal?.company_avatar,
                        row?.original?.deal?.company_name,
                        "vendor",
                    ),
                defaultCanSort: true,
            },
            {
                Header: "Submitted on",
                accessor: "created_at",
                Cell: ({row}) => formatDate(row?.original?.created_at.slice(0, 10), DATE_FORMAT),
                defaultCanSort: true,
            },
            {
                id: "deal.category_name",
                Header: "Category",
                Cell: ({row}) => (
                    <Box
                        tooltip={{
                            title: row?.original?.deal?.category_name || "",
                        }}>
                        {reduceString(row?.original?.deal?.category_name || "", 15)}
                    </Box>
                ),
                defaultCanSort: true,
            },
            {
                id: "deal.sub_category_name",
                Header: "Sub Category",
                Cell: ({row}) => (
                    <Box
                        tooltip={{
                            title: row?.original?.deal?.sub_category_name || "",
                        }}>
                        {reduceString(row?.original?.deal?.sub_category_name || "", 15)}{" "}
                    </Box>
                ),
                defaultCanSort: true,
            },
            {
                Header: "Requested By",
                accessor: "company_id",
                Cell: ({row}) =>
                    renderCompanyAvatar(row?.original?.company?.avatar || "", row?.original?.company?.name, "msp"),
                defaultCanSort: true,
            },
            {
                id: "status_name",
                Header: "Status",
                Cell: ({row}) => <ChannelDealRequestStatusBadge statusKey={row?.original.status?.key || ""} />,
                defaultCanSort: true,
            },
        ];

        if (hasEditAccess) {
            columns.push({
                Header: "",
                id: "approve",
                Cell: ({row}) => renderApproveIcon(row?.original),
            });
            columns.push({
                Header: "",
                id: "decline",
                Cell: ({row}) => renderDeclineIcon(row?.original),
            });
            columns.push({Header: "Actions", id: "menu", Cell: cellActions});
        }

        const handleDeclineDeal = useCallback(
            reason => {
                if (declineDealRequest) {
                    const declineStatus = getStatus(CHANNEL_DEALS_CLAIMED_DECLINED);
                    setDeclining({...declining, [declineDealRequest.id]: true});
                    updateDealRequest(
                        declineDealRequest.id,
                        {status_id: declineStatus?.id || "", reject_reason: reason},
                        "Deal Request Declined",
                        () => {
                            setDeclineDealRequest(undefined);
                        },
                    );
                }
            },
            [declineDealRequest],
        );

        const handleOnCloseDeclineDeal = useCallback(() => {
            setDeclineDealRequest(undefined);
        }, [declineDealRequest]);

        return (
            <>
                {showTable && (
                    <>
                        {hasEditAccess && (
                            <Typography color={"neutral.800"}>
                                If MSP or Internal IT partners have requested access to vendor or distributor deals,
                                please review these requests and take the appropriate action. Approving a partner
                                request will trigger an email notification to the vendor.
                            </Typography>
                        )}
                        <TableV2Component
                            id="channelDealsRequest"
                            columns={columns}
                            hiddenColumns={["id"]}
                            customData={tableData as TNestedChannelDealData[]}
                            isLoading={channelDealRequestsQuery.isLoading || channelDealRequestsQuery.isFetching}
                            headerBackground={theme.palette.blue[100]}
                            noResultsMessage="No results found based on your search or filter criteria. Please update your search or filter and try again."
                            onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                            orderBy="status_name"
                            sortType="DESC"
                        />
                        <CustomPagination
                            metadata={metadata}
                            page={page}
                            itemsPerPage={itemsPerPage}
                            setPage={setPage}
                            setItemsPerPage={setItemsPerPage}
                        />
                        {declineDealRequest && (
                            <DeclineConfirmation
                                open={true}
                                dealName={declineDealRequest.deal?.deal_name || ""}
                                companyName={declineDealRequest.deal?.company_name || ""}
                                title={"Decline Deal Request?"}
                                onConfirm={handleDeclineDeal}
                                onClose={handleOnCloseDeclineDeal}
                            />
                        )}
                    </>
                )}
            </>
        );
    },
);
export default ChannelDealRequestsTable;
