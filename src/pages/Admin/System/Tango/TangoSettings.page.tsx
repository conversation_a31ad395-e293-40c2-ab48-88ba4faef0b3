import AddIcon from "@mui/icons-material/Add";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import {Grid, useTheme} from "@mui/material";
import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {TableComponent} from "../../../../components/Table/table.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../../constants/commonStrings.constant";
import routeConfig from "../../../../constants/routeConfig";
import useNotification from "../../../../hooks/useNotification";
import useSettings from "../../../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../../../reducers/settings.reducer";
import {adminService} from "../../../../services/admin.service";
import {tangoService} from "../../../../services/tango.service";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../../utils/error.util";
import Loader from "../../../../utils/loader";

export default function TangoSettings() {
    const [isDeleting, setIsDeleting] = useState<boolean>(false);
    const [dataRefetchKey, setDataRefetchKey] = useState(0);
    const {settings, updateSettings} = useSettings();
    const theme = useTheme();
    const notify = useNotification();
    const navigate = useNavigate();

    const handleDelete = async (id: string) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Catalog?",
            okButtonText: "DELETE",
            cancelButtonText: "CANCEL",
            reverseBtnPosition: true,
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                        Are you sure you want to delete this catalog?
                    </Typography>
                    <Typography
                        variant="body2"
                        style={{textAlign: "center", fontSize: "20px !important"}}
                        className="mt-2">
                        This cannot be undone.
                    </Typography>
                </Grid>
            ),
        });
        if (answer.value) {
            if (id !== "") {
                setIsDeleting(true);
                tangoService.deleteCatalog(id, handleSuccess, handleError);
            }
        }
    };

    const handleError = (error: AxiosError<any>) => {
        const errorCode = getErrorFromArray(error);
        setIsDeleting(false);
        notify(errorCode, "Error");
    };

    const handleSuccess = () => {
        setIsDeleting(false);
        setDataRefetchKey(dataRefetchKey => dataRefetchKey + 1);
        notify("Catalog Deleted Successfully!", "Success");
    };

    const ButtonCell = useCallback(tableData => {
        return (
            <div className="d-flex justify-content-center">
                <MuiButtonComponent
                    title="Edit Catalog"
                    style={{border: "none"}}
                    color="primary"
                    id="editcatalog"
                    variant="contained"
                    size={"sm"}
                    type="button"
                    onClick={() => {
                        navigate(routeConfig.AdminAddTangoCatalog.path, {state: {...tableData?.row?.original}});
                    }}>
                    Edit
                </MuiButtonComponent>
                <MuiButtonComponent
                    title="Delete Catalog"
                    style={{border: "none", marginLeft: 10}}
                    color="error"
                    id="deleteCatalog"
                    variant="contained"
                    size={"sm"}
                    type="button"
                    onClick={() => handleDelete(tableData?.row?.original?.id)}>
                    {isDeleting ? <Loader inline loading version="iconOrange" /> : "Delete"}
                </MuiButtonComponent>
            </div>
        );
    }, []);

    const columns: any = [
        {Header: "id", accessor: "id"},
        {
            Header: "utid",
            accessor: "utid",
            Cell: (data: any) => <div className="d-flex justify-content-center">{data.value}</div>,
        },
        {
            Header: "Catalog Name",
            accessor: "catalog_name",
            Cell: (data: any) => <div className="d-flex justify-content-center">{data.value}</div>,
        },
        {
            Header: "Region",
            accessor: "region_option.display_value",
            Cell: (data: any) => <div className="d-flex justify-content-center">{data.value}</div>,
        },
        {Header: "Action", accessor: "", Cell: ButtonCell},
    ];

    const onSuccessGetFeatureFlag = (res: AxiosResponse) => {
        if (res?.data) {
            updateSettings(UPDATE_SETTINGS, {tangoFeatureFlagActive: res?.data?.activated});
            if (!res?.data?.activated) {
                notify("Feature flag is off", "Error");
            }
        }
    };

    useEffect(() => {
        adminService.checkFeatureFlagByName("TANGO_INTEGRATION", onSuccessGetFeatureFlag, handleError);
    }, []);
    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <div className="d-flex flex-column justify-content-start align-items-start">
                    <MuiButtonComponent
                        style={{color: "black", border: "none"}}
                        color="primary"
                        id="back"
                        variant="text"
                        size={"xl"}
                        type="button"
                        onClick={() => {
                            navigate(-1);
                        }}>
                        <ArrowBackIosIcon className="me-4" fontSize="small" /> Back
                    </MuiButtonComponent>
                </div>
                <div className="d-flex align-items-center w-100 justify-content-between mb-2">
                    <h1>Tango Settings Management</h1>
                    <MuiButtonComponent
                        disabled={!settings?.tangoFeatureFlagActive}
                        id="addBtn"
                        color="primary"
                        onClick={() => navigate(routeConfig.AdminAddTangoCatalog.path)}
                        style={{
                            backgroundColor: theme.palette.neutral[200],
                            border: `1px solid ${theme.palette.neutral[500]}`,
                        }}>
                        <AddIcon />
                        Add New
                    </MuiButtonComponent>
                </div>
                <TableComponent
                    id="tangoSettings"
                    columns={columns}
                    hiddenColumns={["id"]}
                    paginated
                    serviceHandler={tangoService.getStoredCatalogs}
                    refetchKey={dataRefetchKey}
                />
            </div>
        </div>
    );
}
