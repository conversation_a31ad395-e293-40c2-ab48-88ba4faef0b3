import {AxiosError, AxiosResponse} from "axios";
import * as EmailValidator from "email-validator";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation, useNavigate} from "react-router-dom";
import {ITangoCatalogBody} from "../../../../Interfaces/tango.interface";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../../constants/commonStrings.constant";
import routeConfig from "../../../../constants/routeConfig";
import useAppConfig from "../../../../hooks/useAppConfig";
import useNotification from "../../../../hooks/useNotification";
import useTangoMeta from "../../../../hooks/useTangoMeta";
import {enumService} from "../../../../services/enum.service";
import {tangoService} from "../../../../services/tango.service";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../../../uicomponents/FormControls/TextBox/textBox.component";
import AutoCompleteComponent from "../../../../uicomponents/Molecules/Autocomplete/Autocomplete.component";
import {getErrorFromArray} from "../../../../utils/error.util";
import formatString, {CAPITALIZE_ALL} from "../../../../utils/formatString.util";
import Loader from "../../../../utils/loader";

const AddTangoCatalogPage = () => {
    const location = useLocation();
    const isEditing = !!location.state?.id;
    const methods = useForm(
        location.state
            ? {
                  defaultValues: {
                      utid: location.state.utid,
                      region_option_id: location.state.region_option_id,
                      customer_identifier: location.state.customer_identifier,
                      account_identifier: location.state.account_identifier,
                      catalog_type: location.state.catalog_type,
                      catalog_name: location.state.catalog_name,
                      catalog_email: location.state.catalog_email,
                      email_subject: location.state.email_subject,
                      email_message: location.state.email_message,
                  } as any,
              }
            : undefined,
    );
    const meta = useTangoMeta();
    const notify = useNotification();
    const navigate = useNavigate();
    const {config} = useAppConfig({config_key: "MARKETING_EMAIL"});
    const utidWatcher = methods.watch("utid");
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [catalogTypes, setCatalogTypes] = useState<any[]>([]);
    const [dropdownOptions, setDropdownOptions] = useState<{
        tangoRegionOptions: any[];
        tangoCatalogs: any[];
        tangoCustomers: any[];
        tangoCustomerAccounts: any[];
    }>({
        tangoRegionOptions: [],
        tangoCatalogs: [],
        tangoCustomers: [],
        tangoCustomerAccounts: [],
    });
    const handleError = (error: AxiosError<any>) => {
        setIsSubmitting(false);
        const errorCode = getErrorFromArray(error);
        notify(errorCode, "Error");
    };

    const onSuccessGetCatalogTypes = (response: AxiosResponse) => {
        if (response?.data) {
            setCatalogTypes(
                Object.values(response.data).map(item => ({
                    id: item,
                    label: formatString(item as string, CAPITALIZE_ALL),
                })),
            );
        }
    };

    const onSubmitSuccess = () => {
        setIsSubmitting(false);
        navigate(routeConfig.AdminTangoSettings.path);
    };

    const onSubmit = (data: any) => {
        const requestBody: ITangoCatalogBody = {
            utid: data.utid,
            region_option_id: data.region_option_id,
            customer_identifier: data.customer_identifier,
            account_identifier: data.account_identifier,
            catalog_name: data.catalog_name,
            catalog_type: data.catalog_type,
            catalog_email: data.catalog_email,
            email_subject: data.email_subject,
            email_message: data.email_message,
        };
        setIsSubmitting(true);
        if (isEditing) {
            requestBody.id = location.state.id;
            tangoService.updateCatalog(requestBody, onSubmitSuccess, handleError);
        } else {
            tangoService.storeCatalog(requestBody, onSubmitSuccess, handleError);
        }
    };
    useEffect(() => {
        if (meta.tangoRegionOptions && meta.tangoCatalogs && meta.tangoCustomers) {
            const newTangoRegionOptions = meta.tangoRegionOptions.map(region => ({
                id: region.id,
                label: region.display_value,
            }));

            const newTangoCatalogs = meta.tangoCatalogs.brands?.flatMap(
                catalog =>
                    catalog.items?.map(item => ({
                        id: item.utid,
                        label: item.rewardName + ` (${item?.utid})`,
                    })) || [],
            );

            const newTangoCustomers = meta.tangoCustomers.map(customer => ({
                id: customer?.customerIdentifier,
                label: customer?.displayName + ` (${customer?.customerIdentifier})`,
            }));

            const newTangoCustomerAccounts = meta.tangoCustomers.flatMap(
                customer =>
                    customer.accounts?.map(item => ({
                        id: item.accountIdentifier,
                        label: item.displayName,
                        customerid: customer?.customerIdentifier,
                    })) || [],
            );
            setDropdownOptions({
                tangoRegionOptions: newTangoRegionOptions,
                tangoCatalogs: newTangoCatalogs,
                tangoCustomers: newTangoCustomers,
                tangoCustomerAccounts: newTangoCustomerAccounts,
            });
        }
    }, [meta.tangoRegionOptions, meta.tangoCatalogs, meta.tangoCustomers]);

    useEffect(() => {
        if (utidWatcher) {
            const filteredCatalog = meta.tangoCatalogs?.brands?.flatMap(catalog =>
                catalog?.items?.filter(item => item?.utid === utidWatcher),
            );
            if (filteredCatalog?.length) methods.setValue("catalog_name", filteredCatalog[0]?.rewardName);
        }
    }, [utidWatcher]);

    useEffect(() => {
        if (!isEditing && config?.value) {
            methods.setValue("catalog_email", config.value);
        }
        // eslint-disable-next-line
    }, [config?.value]);

    useEffect(() => {
        async function getTangoCatalogTypes() {
            enumService.getTangoCatalogType(onSuccessGetCatalogTypes, handleError);
        }
        getTangoCatalogTypes();
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <h1 className="ps-3">{isEditing ? "Update" : "Add"} Catalog</h1>
                <FormProvider {...methods}>
                    <form onSubmit={methods.handleSubmit(onSubmit)} className="d-flex flex-column p-3">
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                UTID * (Unique TangoCard Identification)
                            </Typography>
                            <AutoCompleteComponent
                                id="utid"
                                label="UtId"
                                options={dropdownOptions.tangoCatalogs}
                                placeholder="Select UTID"
                                className="w-50"
                                isRequired
                                disabled={meta.isLoadingTangoCatalogs}
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Region *
                            </Typography>
                            <AutoCompleteComponent
                                id="region_option_id"
                                label="Region"
                                options={dropdownOptions.tangoRegionOptions}
                                placeholder="Select Region"
                                className="w-50"
                                isRequired
                                disabled={meta.isLoadingRegionOption}
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Customer Identifier *
                            </Typography>
                            <AutoCompleteComponent
                                id="customer_identifier"
                                label="Customer"
                                options={dropdownOptions.tangoCustomers}
                                placeholder="Select Customer"
                                className="w-50"
                                isRequired
                                disabled={meta.isLoadingTangoCustomers}
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Account Identifier *
                            </Typography>
                            <AutoCompleteComponent
                                id="account_identifier"
                                label="Account"
                                options={dropdownOptions.tangoCustomerAccounts?.filter(
                                    a => a.customerid === methods.watch("customer_identifier"),
                                )}
                                placeholder="Select Account Type"
                                className="w-50"
                                isRequired
                                disabled={meta.isLoadingTangoCustomers}
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Catalog Name *
                            </Typography>
                            <TextBoxComponent
                                id="catalog_name"
                                type="text"
                                placeholder="Catalog Name"
                                className="w-50"
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Catalog Type *
                            </Typography>
                            <AutoCompleteComponent
                                id="catalog_type"
                                label="Catalog Type"
                                options={catalogTypes}
                                placeholder="Catalog Type *"
                                className="w-50"
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Catalog Email * (The email/name the emails will be sent From)
                            </Typography>
                            <TextBoxComponent
                                id="catalog_email"
                                type="email"
                                placeholder="Catalog Email"
                                className="w-50"
                                validationSchema={v => EmailValidator.validate(v)}
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Email Subject * (The subject for the email)
                            </Typography>
                            <TextBoxComponent
                                isRequired
                                id="email_subject"
                                type="text"
                                placeholder="Email Subject"
                                label="Email Subject"
                                className="w-50"
                            />
                        </div>
                        <div className="d-flex flex-column mb-2">
                            <Typography variant="subtitle2" className="mt-2">
                                Email Message * (The message body to explain why the reward is being received)
                            </Typography>
                            <TextBoxComponent
                                isRequired
                                id="email_message"
                                type="text"
                                placeholder="Email Message"
                                label="Email Message"
                                className="w-50"
                            />
                        </div>
                        <div className="d-flex flex-row justify-content-start align-items-center mt-4">
                            <Link to={routeConfig.AdminTangoSettings.path}>
                                <MuiButtonComponent
                                    id="cancel"
                                    color="neutral"
                                    style={{
                                        color: "#FFF",
                                        border: "none",
                                    }}>
                                    CANCEL
                                </MuiButtonComponent>
                            </Link>
                            <MuiButtonComponent
                                id="submit"
                                type="submit"
                                variant="contained"
                                className="ms-3"
                                color="secondary">
                                {isSubmitting ? <Loader inline loading version="iconWhite" /> : "SUBMIT"}
                            </MuiButtonComponent>
                        </div>
                    </form>
                </FormProvider>
            </div>
        </div>
    );
};

export default AddTangoCatalogPage;
