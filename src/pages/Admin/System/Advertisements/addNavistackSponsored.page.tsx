import {AxiosError} from "axios";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Link, useLocation, useNavigate} from "react-router-dom";
import {AD_LOCATION_KEYS, AD_STATUS, CARD_TYPE_KEYS} from "../../../../constants/advertisements.constant";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../../constants/commonStrings.constant";
import routeConfig from "../../../../constants/routeConfig";
import useAdvertisementMeta from "../../../../hooks/useAdvertisementMeta";
import useNotification from "../../../../hooks/useNotification";
import adminAdvertisementService from "../../../../services/adminAdvertisement.service";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../../../uicomponents/FormControls/TextBox/textBox.component";
import CategoryAutocomplete from "../../../../uicomponents/Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import VendorSearch from "../../../../uicomponents/Molecules/VendorSearch/vendorSearch.component";
import {getErrorFromArray} from "../../../../utils/error.util";
import {
    DATE_FORMAT_Year_Month_Day,
    INPUT_TIME,
    dateFromDateAndTimeStrings,
    formatDate,
} from "../../../../utils/formatDate";
import Loader from "../../../../utils/loader";

const AddNaviStackSponsoredPage = () => {
    const [saving, setSaving] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();
    const adToEdit = location.state;
    const isEditing = !!adToEdit?.id;
    const methods = useForm(
        isEditing
            ? {
                  defaultValues: {
                      name: adToEdit?.name,
                      description: adToEdit?.description,
                      vendor: adToEdit?.subject?.id,
                      startDate: formatDate(adToEdit?.start_date, DATE_FORMAT_Year_Month_Day),
                      startTime: formatDate(adToEdit?.start_date, INPUT_TIME),
                      endDate: formatDate(adToEdit?.end_date, DATE_FORMAT_Year_Month_Day),
                      endTime: formatDate(adToEdit?.end_date, INPUT_TIME),
                      parentCategory: adToEdit?.category?.parent_id || adToEdit?.category?.id,
                      subCategory: adToEdit?.category?.parent_id ? adToEdit?.category?.id : undefined,
                  },
              }
            : {
                  defaultValues: {
                      startTime: "00:00",
                      endTime: "23:59",
                  },
              },
    );
    const {locations, card_types} = useAdvertisementMeta();
    const notify = useNotification();
    const naviStackCardTypeId = card_types?.find(ct => ct.key === CARD_TYPE_KEYS.NAVISTACK_SPONSORED)?.id;
    const naviStackLocationId = locations?.find(l => l.key === AD_LOCATION_KEYS.NAVISTACK_SPONSORED)?.id;
    const today = new Date().toISOString();
    const startDateWatcher = methods.watch("startDate");

    const onSubmit = (formValues: any) => {
        setSaving(true);
        const startDateISO = dateFromDateAndTimeStrings(formValues.startDate, formValues.startTime)?.toISOString();
        const endDateISO = dateFromDateAndTimeStrings(formValues.endDate, formValues.endTime)?.toISOString();
        const requestBody = {
            name: formValues.name,
            description: formValues.description,
            subject_id: formValues.vendor,
            subject_type: "companyProfile",
            advertisement_card_type_id: naviStackCardTypeId ?? "",
            additional_data: "{}",
            start_date: startDateISO,
            end_date: endDateISO,
            status: AD_STATUS.ACTIVE,
            locations: [naviStackLocationId],
            category_id: formValues?.subCategory ? formValues.subCategory : formValues.parentCategory,
        };

        if (isEditing) {
            adminAdvertisementService
                .updateAdvertisement(
                    {
                        ...requestBody,
                        id: adToEdit.id,
                    },
                    () => {
                        notify("Successfully updated NaviStack Ad.", "Success");
                        navigate(routeConfig.AdminAdvertisementManagement.path);
                    },
                    handleError,
                )
                ?.finally(() => {
                    setSaving(false);
                });
            return;
        }
        adminAdvertisementService
            .storeAdvertisement(
                requestBody,
                () => {
                    notify("Successfully saved NaviStack Ad.", "Success");
                    navigate(routeConfig.AdminAdvertisementManagement.path);
                },
                handleError,
            )
            .finally(() => {
                setSaving(false);
            });
    };

    const handleError = (err: AxiosError) => {
        notify(getErrorFromArray(err), "Error");
    };

    const minStartDate = formatDate(isEditing ? adToEdit?.start_date : today, DATE_FORMAT_Year_Month_Day);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <Typography variant="h6">NaviStack Sponsored</Typography>
                <FormProvider {...methods}>
                    <form
                        className="d-flex flex-column col-12 col-md-8 gap-2"
                        onSubmit={methods.handleSubmit(onSubmit)}>
                        <Typography variant="subtitle2">NAME *</Typography>
                        <TextBoxComponent id="name" label="Name" isRequired showCharacterCount maxLength={50} />
                        <Typography variant="subtitle2">DESCRIPTION</Typography>
                        <TextBoxComponent
                            id="description"
                            label="Description"
                            rows={10}
                            showCharacterCount
                            maxLength={1000}
                            multiline
                        />
                        <Typography variant="subtitle2" className="mt-2">
                            VENDOR*
                        </Typography>
                        <VendorSearch
                            id="vendor"
                            label="Vendor"
                            clearOnSelect={false}
                            clearOnBlur={false}
                            isRequired
                            defaultVendors={adToEdit?.subject ? [adToEdit.subject] : []}
                            allVendorTypes={true}
                        />
                        <div className="w-100">
                            <Typography variant="subtitle2" className="mt-2">
                                CATEGORY * / SUBCATEGORY
                            </Typography>
                            <CategoryAutocomplete
                                wrapperClassName="d-flex flex-row gap-2"
                                parentCategoryRequired
                                subCategoryRequired={false}
                                categoryInputId="parentCategory"
                                subCategoryInputId="subCategory"
                                parentCategoryLabel="Category"
                                subCategoryLabel="Subcategory"
                            />
                        </div>
                        <div className="d-flex flex-column mt-4">
                            <Typography variant="subtitle2" className="mt-2">
                                SCHEDULE *
                            </Typography>
                            <div className="w-100 d-flex flex-row justify-content-start mt-2">
                                <TextBoxComponent
                                    id="startDate"
                                    type="date"
                                    containerClassName="me-3"
                                    label="Start Date"
                                    isRequired
                                    minDate={minStartDate}
                                />
                                <TextBoxComponent id="startTime" type="time" label="Start Time" isRequired />
                            </div>
                            <div className="w-100 d-flex flex-row justify-content-start mt-2">
                                <TextBoxComponent
                                    id="endDate"
                                    type="date"
                                    containerClassName="me-3"
                                    label="End Date"
                                    isRequired
                                    minDate={startDateWatcher}
                                />
                                <TextBoxComponent
                                    id="endTime"
                                    type="time"
                                    label="End Time"
                                    isRequired
                                    // isDate
                                    // minDate={startDateWatcher === endDateWatcher ? methods.watch("startTime") : undefined}
                                />
                            </div>
                        </div>
                        <div className="d-flex flex-row mt-4">
                            <Link to={routeConfig.AdminAdvertisementManagement.path}>
                                <MuiButtonComponent id="cancel" color="blue">
                                    CANCEL
                                </MuiButtonComponent>
                            </Link>
                            <MuiButtonComponent
                                id="submit"
                                type="submit"
                                color="blue"
                                className="ms-3"
                                disabled={saving}>
                                {saving ? <Loader inline loading /> : "SAVE"}
                            </MuiButtonComponent>
                        </div>
                    </form>
                </FormProvider>
            </div>
        </div>
    );
};

export default AddNaviStackSponsoredPage;
