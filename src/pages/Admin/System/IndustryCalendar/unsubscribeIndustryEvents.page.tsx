import {useCallback, useEffect, useState} from "react";
import {NavigateOptions, useNavigate, useParams} from "react-router-dom";
import TitleContainer from "../../../../components/Titles/titleContainer.component";
import {
    ROUTE_CLASS,
    UNSUBSCRIBE_CANCEL_BTN_TEXT,
    UNSUBSCRIBE_OK_BTN_TEXT,
    UNSUBSCRIBE_TITLE_TEXT,
} from "../../../../constants/commonStrings.constant";
import routeConfig from "../../../../constants/routeConfig";
import {eventService} from "../../../../services/event.service";
import Loader from "../../../../utils/loader";
import useNotification from "../../../../hooks/useNotification";
import {INVALID_UNSUBSCRIBE_TOKEN} from "../../../../constants/errorMessages.constant";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import UnsubscribeOutlinedIcon from "@mui/icons-material/UnsubscribeOutlined";

const UnsubscribeIndustryEventsPage = () => {
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const {unsubscription_token} = useParams();
    const notify = useNotification();

    const unsubscribe = useCallback(async () => {
        const resp = await getUserConfirmation("", {
            title: UNSUBSCRIBE_TITLE_TEXT,
            form: (
                <div style={{display: "flex", flexDirection: "column", gap: 15, lineHeight: "1.7"}}>
                    <p>
                        We're sorry to see you go! Before you confirm, are you sure you want to unsubscribe from our
                        events newsletter?{" "}
                    </p>

                    <p>
                        If you'd like to continue receiving the latest updates and news, click{" "}
                        <b>No, keep me subscribed</b>. If you're sure about leaving, click <b>Yes, unsubscribe</b> to
                        confirm.
                    </p>

                    <p>Thank you for being a valued member of Channel Program!</p>
                </div>
            ),
            okButtonText: UNSUBSCRIBE_OK_BTN_TEXT,
            okButtonIcon: <UnsubscribeOutlinedIcon htmlColor="error" fontSize="small" />,
            cancelButtonText: UNSUBSCRIBE_CANCEL_BTN_TEXT,
            mui: true,
        });

        if (resp.value) {
            setLoading(true);

            eventService
                .unsubscribeEmailWithToken(String(unsubscription_token))
                .then(() => {
                    setLoading(false);
                    notify("Your unsubscription has been completed.", "Success");
                    navigate(routeConfig.IndustryCalendar.path);
                })
                .catch(({response: {status}}) => {
                    let options: NavigateOptions | undefined = undefined;
                    if (status === 422) {
                        options = {
                            state: {
                                bannerVariant: "error",
                                bannerTitle: "Invalid token",
                                bannerMessage: INVALID_UNSUBSCRIBE_TOKEN,
                            },
                        };
                    } else {
                        notify("Error Unsubscribing Email!", "Error");
                    }
                    navigate(routeConfig.IndustryCalendar.path, options);
                });
        } else {
            navigate(routeConfig.IndustryCalendar.path);
        }
    }, []);

    useEffect(() => {
        if (!unsubscription_token) {
            notify("Invalid token!", "Error");
            navigate(routeConfig.IndustryCalendar.path);
            return;
        }

        unsubscribe();
    }, [unsubscription_token]);

    return (
        <div className={ROUTE_CLASS + " h-75 d-flex align-items-center justify-content-center fullSection flex-column"}>
            <TitleContainer as="h1" text="Unsubscribing..." />
            <Loader inline loading={loading} big />
        </div>
    );
};

export default UnsubscribeIndustryEventsPage;
