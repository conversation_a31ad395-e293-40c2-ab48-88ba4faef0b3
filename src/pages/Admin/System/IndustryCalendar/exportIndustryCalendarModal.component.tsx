import Divider from "@mui/material/Divider";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../../hooks/useNotification";
import {eventService} from "../../../../services/event.service";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../../uicomponents/FormControls/TextBox/textBox.component";
import ModalComponent from "../../../../uicomponents/Molecules/Modal/Modal.component";
import RadioWithLabel from "../../../../uicomponents/Molecules/RadioWithLabel/RadioWithLabel.component";
import {downloadDoc} from "../../../../utils/downloadDoc.util";
import {formatDate, INPUT_DATE_TIME} from "../../../../utils/formatDate";

interface IProps {
    open: boolean;
    onClose?: any;
    search?: string;
}

const exportTypeOptions = {
    all: "export-all",
    selected: "export-selected",
};

const ExportIndustryCalendarModal = (props: IProps) => {
    const [loading, setLoading] = useState(false);
    const notify = useNotification();
    const methods = useForm({
        defaultValues: {
            exportType: "export-all",
            startDate: null,
            endDate: null,
        },
    });
    const exportType = methods.watch("exportType");
    const startDateValue: any = methods.watch("startDate");

    const onSubmit = (formValues: any) => {
        setLoading(true);
        const startDate = formValues.startDate?.toISOString();
        const endDate = formValues.endDate?.toISOString();
        eventService.exportIndustryEvents(
            {
                search: props.search,
                sort: "",
                order_by: "",
                start_date: startDate,
                end_date: endDate ? endDate.replace("T00:00:00.000Z", "T23:59:59.999Z") : endDate,
            },
            onSuccess,
            onError,
        );
    };

    const onSuccess = (response: any) => {
        downloadDoc(response.data, "allIndustryEvents.csv");
        notify("Industry events exported successfully", "Success");
        setLoading(false);
        props.onClose && props.onClose();
    };

    const onError = () => {
        notify("There was an error while exporting industry events", "Error");
        setLoading(false);
    };

    return (
        <ModalComponent
            open={props.open}
            onClose={props.onClose}
            onSuccess={methods.handleSubmit(onSubmit)}
            okButtonText="Export"
            loading={loading}
            title="Export Events Calendar"
            content={
                <FormProvider {...methods}>
                    <form onSubmit={methods.handleSubmit(onSubmit)}>
                        <Typography variant="body2" uppercase>
                            select export type
                        </Typography>
                        <div className="d-flex flex-column flex-md-row align-items-start justify-content-start mt-1">
                            <RadioWithLabel
                                id="exportType"
                                value={exportTypeOptions.all}
                                label="Export All"
                                wrapperClassName="me-4"
                            />
                            <RadioWithLabel
                                id="exportType"
                                value={exportTypeOptions.selected}
                                label="Specific Date Range"
                            />
                        </div>
                        <Divider />
                        {exportType === exportTypeOptions.selected && (
                            <div className="d-flex flex-column justify-content-start mt-4">
                                <Typography variant="body2" uppercase>
                                    select date range
                                </Typography>
                                <Divider className="my-2" />
                                <div className="d-flex align-items-center justify-content-start">
                                    <TextBoxComponent
                                        id="startDate"
                                        type="date"
                                        className="me-4"
                                        isRequired
                                        isDate
                                        label="Start Date"
                                    />
                                    <TextBoxComponent
                                        id="endDate"
                                        type="date"
                                        isRequired
                                        isDate
                                        label="End Date"
                                        minDate={
                                            startDateValue
                                                ? undefined
                                                : formatDate(startDateValue?.toISOString(), INPUT_DATE_TIME)
                                        }
                                    />
                                </div>
                            </div>
                        )}
                    </form>
                </FormProvider>
            }
        />
    );
};

export default ExportIndustryCalendarModal;
