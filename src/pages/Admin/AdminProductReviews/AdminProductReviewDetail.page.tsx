import {Grid} from "@mui/material";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {<PERSON>, useNavigate, useParams} from "react-router-dom";
import {IAdminProductReview} from "../../../Interfaces/adminReview.interface";
import AdminReviewForm from "../../../components/AdminProductReviews/adminReview.form";
import {ArrowBackIcon} from "../../../components/Icons";
import TitleContainer from "../../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import useAppConfig from "../../../hooks/useAppConfig";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import useSettings from "../../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {adminService} from "../../../services/admin.service";
import {adminReviewService} from "../../../services/adminReview.service";
import {reviewService} from "../../../services/review.service";
import styles from "../../../styles/pages/adminProductReviewDetail.module.sass";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import CheckboxWithLabel from "../../../uicomponents/Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import MoreButtonTooltip from "../../../uicomponents/Molecules/MoreIconButton/moreButtonTooltip.component";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {formatDate} from "../../../utils/formatDate";
import formatString, {CAPITALIZE_ALL} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";

const AdminProductReviewDetail = () => {
    const [loadingStatus, setLoadingStatus] = useState({
        approving: false,
        flagging: false,
        update: false,
        payOut: false,
    });
    const [review, setReview] = useState<IAdminProductReview | undefined>();
    const [totalPaid, setTotalPaid] = useState<number | null>(null);
    const [isValid, setIsValid] = useState(true);
    const {settings, updateSettings} = useSettings();
    const {id} = useParams();
    const notify = useNotification();
    const navigate = useNavigate();
    const flagMethods = useForm();
    const {authState} = useAuthState();
    const {configs} = useAppConfig({
        config_key: ["TANGO_MAX_SINGLE_PAYOUT", "TANGO_DEFAULT_PAYOUT_AMOUNT", "TANGO_TOTAL_MAX_PAYOUT	"],
    });
    const tangoMaxSinglePayout: string = configs.find(c => c.key === "TANGO_MAX_SINGLE_PAYOUT")?.value || "0";
    const tangoDefaultAmount: string = configs.find(c => c.key === "TANGO_DEFAULT_PAYOUT_AMOUNT")?.value || "0";
    const tangoTotalMaxPayout: string = configs.find(c => c.key === "TANGO_TOTAL_MAX_PAYOUT")?.value || "0";
    const [showPayoutModal, setShowPayoutModal] = useState({show: false, type: "payout"});
    const incentivizeMethods = useForm({
        defaultValues: {
            incentivize: review?.incentivize ? "Yes" : "No",
            incentive_amount: review?.incentive?.amount,
            reason: "",
            overRide: false,
        },
    });
    const incentive_amount_watcher = incentivizeMethods.watch("incentive_amount");
    const overRideCheckbox_watcher = incentivizeMethods.watch("overRide");
    const incentivize = incentivizeMethods.watch("incentivize");
    const seenIDs = new Set();

    const getReview = () => {
        if (!id) return;
        updateSettings(UPDATE_SETTINGS, {loading: true});
        adminReviewService.getReviewById(id, handleSuccessGetReview, handleError);
    };

    const handleSuccessGetReview = (res: AxiosResponse<IAdminProductReview>) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setReview(res.data);
        incentivizeMethods.setValue("incentivize", res.data.incentivize ? "Yes" : "No");
        incentivizeMethods.setValue("incentive_amount", res.data.incentive?.amount ?? tangoDefaultAmount);
    };

    const handleError = (err: AxiosError) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setLoadingStatus({approving: false, flagging: false, update: false, payOut: false});
        if (err.response?.status === 404) {
            navigate(routeConfig.AdminProductReviews.path);
            notify("Review not found", "Error");
            return;
        }
        notify(getErrorFromArray(err), "Error");
    };

    const onSuccessApprove = (res: AxiosResponse) => {
        notify("Review approved successfully", "Success");
        setLoadingStatus({...loadingStatus, approving: false});
        const newReview = {
            ...review,
            ...res.data,
            model: review?.model,
            approver: {
                id: authState.id,
                first_name: authState.firstName,
                last_name: authState.lastName,
            },
        };
        setReview(newReview);
    };

    const onSuccessFlag = (res: AxiosResponse) => {
        notify("Review flagged successfully", "Success");
        setLoadingStatus({...loadingStatus, flagging: false});
        const newReview = {
            ...review,
            ...res.data,
            model: review?.model,
            flagger: {
                first_name: authState.firstName,
                last_name: authState.lastName,
                id: authState.id,
            },
        };
        setReview(newReview);
    };

    const handleApprove = async () => {
        if (!review) return;
        if (!isValid) {
            notify("Please examine the form, as there are some fields that are either missing or incorrect.", "Error");
            return;
        }
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "success",
            title: "Approve Review?",
            okButtonText: "APPROVE",
            cancelButtonText: "CANCEL",
            reverseBtnPosition: true,
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                        Are you sure you want to approve this review?
                    </Typography>
                </Grid>
            ),
        });
        if (!answer.value) return;
        if (answer.value) {
            setLoadingStatus({...loadingStatus, approving: true});
            adminReviewService.approveReview(review.id, onSuccessApprove, handleError);
        }
    };

    const handleFlag = async () => {
        if (!review) return;
        const answer = await getUserConfirmation("", {
            title: "Flag Review?",
            okButtonText: "FLAG",
            cancelButtonText: "CANCEL",
            form: (
                <FormProvider {...flagMethods}>
                    <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                        Are you sure you want to flag this review? <br /> When a review is flagged, it means that the
                        won't be published. <br /> Please enter a reason for flagging this review.
                    </Typography>
                    <form className="d-flex justify-content-center align-items-center w-75 mx-auto mt-2">
                        <TextBoxComponent
                            id="reason"
                            isRequired
                            placeholder="Reason"
                            label="Reason"
                            containerClassName="w-100"
                        />
                    </form>
                </FormProvider>
            ),
            mui: true,
            modalTheme: "error" as any,
            reverseBtnPosition: true,
        });
        if (answer.value && flagMethods.getValues()?.reason) {
            setLoadingStatus({...loadingStatus, flagging: true});
            adminReviewService.flagReview(review.id, flagMethods.getValues().reason, onSuccessFlag, handleError);
        }
    };

    const onSuccessPayout = (res: AxiosResponse, msg: string) => {
        setLoadingStatus({...loadingStatus, payOut: false});
        notify(msg, "Success");
        setShowPayoutModal({show: false, type: ""});
        setReview({...review, ...res.data, model: review?.model});
    };

    const handlePayOut = async () => {
        if (!incentive_amount_watcher) {
            return notify("Incentive Amount is required", "Error");
        }
        if (incentivizeMethods.getValues()?.incentive_amount && review) {
            setLoadingStatus({...loadingStatus, payOut: true});
            const payload: any = {
                id: review.id,
                status: "paid",
                amount: Number(incentivizeMethods.getValues().incentive_amount),
            };
            if (overRideCheckbox_watcher) {
                payload["override_limit"] = true;
            }
            adminReviewService.reviewIncentiveUpdate(
                payload,
                res => onSuccessPayout(res, "Pay Out submitted to Tango Card"),
                handleError,
            );
        }
    };

    const handleNotPaid = async () => {
        if (!incentivizeMethods.getValues()?.reason) {
            return notify("Rejecting reason is required", "Error");
        }
        if (incentivizeMethods.getValues()?.reason && review) {
            setLoadingStatus({...loadingStatus, payOut: true});
            const payload: any = {
                id: review.id,
                status: "unpaid",
                rejection_reason: incentivizeMethods.getValues()?.reason,
            };
            adminReviewService.reviewIncentiveUpdate(
                payload,
                res => onSuccessPayout(res, "Pay Out status updated"),
                handleError,
            );
        }
    };

    const handleInReview = () => {
        const payload: any = {
            id: review?.id,
            status: "review",
        };
        adminReviewService.reviewIncentiveUpdate(
            payload,
            res => onSuccessPayout(res, "Pay Out status updated"),
            handleError,
        );
    };

    const handleUnderReview = () => {
        const payload: any = {
            id: review?.id,
        };
        adminReviewService.underReviewUpdate(
            payload,
            res => onSuccessPayout(res, "Review updated successfully"),
            handleError,
        );
    };

    const onSuccessUpdate = (res: AxiosResponse) => {
        notify("Review updated successfully", "Success");
        updateSettings(UPDATE_SETTINGS, {loading: false});
        setLoadingStatus({...loadingStatus, update: false});
        setReview({...review, ...res.data, model: review?.model});
    };

    const onSuccessGetFeatureFlag = (res: AxiosResponse) => {
        if (res?.data) {
            updateSettings(UPDATE_SETTINGS, {tangoFeatureFlagActive: res?.data?.activated});
            if (!res?.data?.activated) {
                notify("Feature flag is off", "Error");
            }
        }
    };

    const getTotalIncentivePaid = async (id: string) => {
        const response: AxiosResponse = await adminReviewService.getTotalIncentivePaid(id);
        if (response?.data) {
            setTotalPaid(response?.data?.total_amount);
        }
    };

    useEffect(() => {
        if (!id) return;
        getReview();
    }, [id]);

    useEffect(() => {
        if (review?.reviewer_user_id) {
            getTotalIncentivePaid(review?.reviewer_user_id);
        }
    }, [review]);

    useEffect(() => {
        if (review && incentivize !== (review.incentivize ? "Yes" : "No")) {
            updateSettings(UPDATE_SETTINGS, {loading: true});
            reviewService.updateReview(
                {id: review?.id, incentivize: incentivize === "Yes" ? true : false},
                onSuccessUpdate,
                handleError,
            );
        }
        // eslint-disable-next-line
    }, [incentivize]);

    useEffect(() => {
        if (showPayoutModal?.show) {
            incentivizeMethods.setValue("incentive_amount", review?.incentive?.amount ?? tangoDefaultAmount);
        }
    }, [showPayoutModal?.show]);

    useEffect(() => {
        adminService.checkFeatureFlagByName("TANGO_INTEGRATION", onSuccessGetFeatureFlag, handleError);
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                {showPayoutModal?.show && (
                    <ModalComponent
                        open={showPayoutModal.show}
                        title={showPayoutModal?.type === "payout" ? "Approve Pay Out" : "Not Paid"}
                        onSuccess={showPayoutModal?.type === "payout" ? handlePayOut : handleNotPaid}
                        okButtonText={showPayoutModal?.type === "payout" ? "APPROVE" : "REJECT"}
                        cancelButtonText="CANCEL"
                        onClose={() => setShowPayoutModal({show: false, type: ""})}
                        loading={loadingStatus?.payOut}
                        content={
                            <Grid container display={"flex"} flexDirection={"column"}>
                                {showPayoutModal?.type === "payout" ? (
                                    <Typography
                                        variant="body2"
                                        style={{textAlign: "center", fontSize: "20px !important"}}>
                                        {review?.reviewer?.name} has already received <br />
                                        {`$${totalPaid}`} in incentive awards.
                                        <br />
                                        <br />
                                        The following amount will be awarded. <br /> Update value to award more or less.
                                    </Typography>
                                ) : (
                                    <Typography
                                        variant="body2"
                                        style={{textAlign: "center", fontSize: "20px !important"}}>
                                        {review?.reviewer?.name} has already received <br /> {`$${totalPaid}`} in
                                        incentive awards. <br />
                                        <br />
                                        Provide a reason for rejecting this pay out.
                                    </Typography>
                                )}
                                <FormProvider {...incentivizeMethods}>
                                    <form className="d-flex justify-content-center align-items-center w-75 mx-auto mt-2">
                                        {showPayoutModal?.type === "payout" ? (
                                            <div className="d-flex flex-column">
                                                <TextBoxComponent
                                                    startAdornmentIcon={"FaDollarSign"}
                                                    id="incentive_amount"
                                                    isRequired
                                                    placeholder="Incentive Amount"
                                                    label="Incentive Amount"
                                                    containerClassName="w-100"
                                                />
                                                {((Number(tangoMaxSinglePayout) > 0 &&
                                                    incentive_amount_watcher &&
                                                    Number(incentive_amount_watcher) > Number(tangoMaxSinglePayout)) ||
                                                    Number(incentive_amount_watcher) + Number(totalPaid) >
                                                        Number(tangoTotalMaxPayout)) && (
                                                    <CheckboxWithLabel
                                                        color={"error"}
                                                        key={review?.id}
                                                        id={"overRide"}
                                                        label={"Override Limit"}
                                                    />
                                                )}
                                            </div>
                                        ) : (
                                            <TextBoxComponent
                                                id="reason"
                                                isRequired
                                                placeholder="Reason"
                                                label="Reason"
                                                containerClassName="w-100"
                                                validateOnTheFly
                                            />
                                        )}
                                    </form>
                                </FormProvider>
                            </Grid>
                        }
                        modalTheme={showPayoutModal?.type === "payout" ? "success" : "error"}
                        justifyButtons={"center"}
                        reverseBtnPosition
                        customModalBtnTracking={{
                            review_id: review?.id,
                        }}
                        okButtonDisabled={
                            !overRideCheckbox_watcher &&
                            ((Number(tangoMaxSinglePayout) > 0 &&
                                incentive_amount_watcher &&
                                Number(incentive_amount_watcher) > Number(tangoMaxSinglePayout)) ||
                                Number(incentive_amount_watcher) + Number(totalPaid) > Number(tangoTotalMaxPayout))
                        }
                    />
                )}
                <div className={styles.adminProductReviewDetail}>
                    <div className="d-flex flex-row justify-content-between align-items-center px-5 py-3 w-100">
                        <div className={styles.headerLink} onClick={() => navigate(-1)}>
                            <ArrowBackIcon size={20} />{" "}
                            <span style={{fontSize: 24, fontWeight: 700}} className="mt-1 ms-2">
                                Product Review
                            </span>
                        </div>
                    </div>
                    <hr className="fadedBorder" />
                    <div
                        className={`${styles.leftContainer} col-12 col-sm-6 d-flex flex-column justify-content-start px-5 mx-5`}
                        style={{backgroundColor: "#F5F6F9"}}>
                        <div className="mt-2">
                            <div className={styles.headerLabel}>
                                <h4>Reviewer Details: </h4>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Reviewer Name: </h5>
                                <Link
                                    target="_blank"
                                    to={routeConfig.UserProfile.path.replace(
                                        ":id",
                                        review?.reviewer?.friendly_url || "",
                                    )}>
                                    {review?.reviewer?.name}
                                </Link>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Reviewer Email: </h5>
                                <a href={`mailto:${review?.reviewer?.email}`}>{review?.reviewer?.email}</a>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Approved reviews?</h5>
                                <span>
                                    {review?.reviewer?.approved_reviews_count &&
                                    review?.reviewer?.approved_reviews_count > 0
                                        ? "Yes (" + review?.reviewer?.approved_reviews_count + ")"
                                        : "No"}
                                </span>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Flagged Reviews?</h5>
                                <span>
                                    {review?.flagged_reviews?.count && review?.flagged_reviews?.count > 0 ? (
                                        <MoreButtonTooltip
                                            title={
                                                <>
                                                    {review?.flagged_reviews?.details?.map(value => {
                                                        return (
                                                            <>
                                                                <div
                                                                    style={{
                                                                        fontSize: "16px",
                                                                        fontWeight: 500,
                                                                        color: "#292E36",
                                                                    }}>
                                                                    Review Name: {value.title}
                                                                </div>
                                                                <div style={{fontSize: "16px", color: "#292E36"}}>
                                                                    Flagged Reason: {value.red_flag_reason}
                                                                </div>
                                                            </>
                                                        );
                                                    })}
                                                </>
                                            }>
                                            <div className="d-flex justify-content-center">
                                                {"Yes (" + review?.flagged_reviews?.count + ")"}
                                            </div>
                                        </MoreButtonTooltip>
                                    ) : (
                                        "No"
                                    )}
                                </span>
                            </div>
                        </div>
                        <div className="mt-2">
                            <div className={styles.headerLabel}>
                                <h4>Review Details: </h4>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Date of Review: </h5>
                                <span>{formatDate(review?.created_at || "", "DDD")}</span>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Product Name: </h5>
                                <Link
                                    target="_blank"
                                    to={routeConfig.ProductDetails.path.replace(
                                        ":friendly_url",
                                        review?.model?.friendly_url || "",
                                    )}>
                                    {review?.model?.name}
                                </Link>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Vendor Name: </h5>
                                <Link
                                    target="_blank"
                                    to={routeConfig.VendorProfile.path.replace(
                                        ":id",
                                        review?.model?.company?.friendly_url || "",
                                    )}>
                                    {review?.model?.company?.name}
                                </Link>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Product Category: </h5>
                                {review?.model?.categories?.map((category: any, index: number) => {
                                    return (
                                        <Link
                                            target="_blank"
                                            key={index}
                                            to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                ":friendly_url",
                                                category.friendly_url || "",
                                            )}>
                                            {category.name}
                                        </Link>
                                    );
                                })}
                            </div>
                        </div>
                        {/* <div className={styles.headerLabel}>
                        <h6>Incentivize: </h6>
                        <FormProvider {...incentivizeMethods}>
                            <DropdownComponent
                                id="incentivize"
                                placeholder="Please select"
                                containerClassName="m-0"
                                options={[
                                    ["Yes", "Yes"],
                                    ["No", "No"],
                                ]}
                            />
                        </FormProvider>
                    </div> */}
                    </div>
                    <div className={`${styles.statusText} px-5`}>
                        <div className="mt-2">
                            <div className={styles.headerLabel}>
                                <h4>Review Admin</h4>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Status:</h5>
                                <h5>
                                    {loadingStatus.flagging || loadingStatus.approving ? (
                                        <Loader inline loading />
                                    ) : review?.status === "flagged" ? (
                                        "Flagged"
                                    ) : review?.status === "approved" ? (
                                        "Approved"
                                    ) : review?.status === "abandoned" ? (
                                        "Abandoned"
                                    ) : review?.status === "under_review" ? (
                                        "Under Review"
                                    ) : review?.status === "archived" ? (
                                        "Archived"
                                    ) : (
                                        "Submitted for Approval"
                                    )}
                                </h5>
                            </div>
                            {review?.status === "approved" && !loadingStatus.flagging && (
                                <div className={styles.headerLabel}>
                                    <h5>Approver:</h5>
                                    <h5>
                                        {review.approver?.first_name} {review.approver?.last_name}
                                    </h5>
                                </div>
                            )}
                            {(review?.status === "flagged" || review?.status === "archived") &&
                                !loadingStatus.approving && (
                                    <>
                                        <div className={styles.headerLabel}>
                                            <h5 className="mb-1">Reason:</h5>
                                            <h5>{review.red_flag_reason}</h5>
                                        </div>
                                        <div className={styles.headerLabel}>
                                            <h5 className="mb-1">Flagged By:</h5>
                                            <h5>{`${review.flagger?.first_name} ${review.flagger?.last_name}`}</h5>
                                        </div>
                                    </>
                                )}
                            <div className="d-flex flex-row align-items-center gap-3 mt-2 mb-2 justify-content-between">
                                <MuiButtonComponent
                                    size={"sm"}
                                    id="under_review_btn"
                                    disabled={
                                        review?.status === "under_review" ||
                                        loadingStatus["under_review"] ||
                                        review?.status === "abandoned" ||
                                        loadingStatus["flagging"] ||
                                        loadingStatus["approved"]
                                    }
                                    data_custom_properties={JSON.stringify({
                                        reviewId: review?.id,
                                        reviewer_id: review?.reviewer_user_id,
                                        action: "under_review",
                                    })}
                                    color="blue"
                                    type="button"
                                    onClick={handleUnderReview}
                                    variant="outlined">
                                    UNDER REVIEW
                                </MuiButtonComponent>
                                <MuiButtonComponent
                                    size={"sm"}
                                    disabled={
                                        review?.status === "approved" ||
                                        review?.status === "archived" ||
                                        review?.status === "abandoned" ||
                                        loadingStatus["approved"] ||
                                        loadingStatus["under_review"] ||
                                        loadingStatus["flagging"]
                                    }
                                    id="approveBtn"
                                    color="success"
                                    type="button"
                                    onClick={handleApprove}
                                    variant="outlined">
                                    {review?.status === "approved" ? "APPROVED" : "APPROVE"}
                                </MuiButtonComponent>
                                <MuiButtonComponent
                                    size={"sm"}
                                    disabled={
                                        review?.status === "flagged" ||
                                        review?.status === "archived" ||
                                        review?.status === "abandoned" ||
                                        loadingStatus["approved"] ||
                                        loadingStatus["under_review"] ||
                                        loadingStatus["flagging"]
                                    }
                                    id="flagBtn"
                                    color="error"
                                    type="button"
                                    onClick={handleFlag}
                                    variant="outlined">
                                    {review?.status === "flagged" ? "FLAGGED" : "FLAG"}
                                </MuiButtonComponent>
                            </div>
                            <div className={styles.headerLabel}>
                                <h4>Pay Out Details</h4>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Status:</h5>
                                <h5>{formatString(review?.incentive?.status ?? "", CAPITALIZE_ALL)}</h5>
                            </div>
                            {review?.incentive?.status === "unpaid" && review?.incentive?.rejection_reason && (
                                <div className={styles.headerLabel}>
                                    <h5>Reason:</h5>
                                    <h5>{review.incentive.rejection_reason}</h5>
                                </div>
                            )}
                            <div className={styles.headerLabel}>
                                <h5>User:</h5>
                                <Link
                                    target="_blank"
                                    to={routeConfig.UserProfile.path.replace(
                                        ":id",
                                        review?.incentive?.reviewed?.friendly_url || "",
                                    )}>
                                    {review?.incentive?.reviewed?.name}
                                </Link>
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>Past Payouts:</h5>
                                {Number(totalPaid) >= 0 && <h5>{`$${totalPaid}`}</h5>}
                            </div>
                            <div className={styles.headerLabel}>
                                <h5>TangoCard Response:</h5>
                                <h5>{review?.incentive?.tango_reference_order_id}</h5>
                            </div>
                            <div className="d-flex flex-row align-items-center gap-3 mt-2 mb-2 justify-content-between">
                                <MuiButtonComponent
                                    disabled={
                                        review?.status !== "approved" ||
                                        review?.incentive?.status === "paid" ||
                                        review?.incentive?.status === "review" ||
                                        !settings?.tangoFeatureFlagActive
                                    }
                                    size={"sm"}
                                    id="in_review_btn"
                                    data_custom_properties={JSON.stringify({
                                        reviewId: review?.id,
                                        reviewer_id: review?.reviewer_user_id,
                                        action: "payout_in_review",
                                    })}
                                    color="blue"
                                    type="button"
                                    onClick={handleInReview}
                                    variant="outlined">
                                    IN REVIEW
                                </MuiButtonComponent>
                                <MuiButtonComponent
                                    disabled={
                                        review?.status !== "approved" ||
                                        review?.incentive?.status === "paid" ||
                                        !settings?.tangoFeatureFlagActive
                                    }
                                    data_custom_properties={JSON.stringify({
                                        reviewId: review?.id,
                                        reviewer_id: review?.reviewer_user_id,
                                        action: "payout_modal_open",
                                    })}
                                    color="success"
                                    size={"sm"}
                                    id="pay_out_btn"
                                    type="button"
                                    onClick={() => setShowPayoutModal({show: true, type: "payout"})}
                                    variant="outlined">
                                    PAY OUT
                                </MuiButtonComponent>
                                <MuiButtonComponent
                                    disabled={
                                        review?.status !== "approved" ||
                                        review?.incentive?.status === "paid" ||
                                        review?.incentive?.status === "unpaid" ||
                                        review?.incentive?.status === "under_review" ||
                                        !settings?.tangoFeatureFlagActive
                                    }
                                    data_custom_properties={JSON.stringify({
                                        reviewId: review?.id,
                                        reviewer_id: review?.reviewer_user_id,
                                        action: "payout_notpaid_modal_open",
                                    })}
                                    color="error"
                                    size={"sm"}
                                    id="not_paid_btn"
                                    type="button"
                                    onClick={() => setShowPayoutModal({show: true, type: "reject"})}
                                    variant="outlined">
                                    NOT PAID
                                </MuiButtonComponent>
                            </div>
                        </div>
                    </div>
                </div>
                <hr className="fadedBorder" />
                <div className="row d-flex flex-column px-5">
                    <TitleContainer as="h1" text="Review Details" position="start" />
                    {review ? (
                        <AdminReviewForm
                            answers={review?.answers}
                            review={review}
                            friendly_url={review?.model?.friendly_url || ""}
                            setIsValidState={setIsValid}
                            questions={review.answers
                                ?.sort((a, b) => (a.review_question?.order || 0) - (b.review_question?.order || 0))
                                ?.filter(
                                    answer =>
                                        !seenIDs.has(answer.review_question?.id) &&
                                        seenIDs.add(answer.review_question?.id),
                                )
                                ?.map(a => a.review_question)}
                        />
                    ) : null}
                </div>
            </div>
        </div>
    );
};

export default AdminProductReviewDetail;
