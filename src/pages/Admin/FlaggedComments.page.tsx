import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {OverlayTrigger, Tooltip} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {Link} from "react-router-dom";
import {RejectFlagCommentModal} from "../../components/Comment/rejectCommentModal.component";
import ButtonComponent from "../../components/FormControls/button.component";
import DropdownComponent from "../../components/FormControls/dropdown.component";
import TextBoxComponent from "../../components/FormControls/textBox.component";
import {TableComponent} from "../../components/Table/table.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {useDebounce} from "../../hooks/useDebounce";
import useNotification from "../../hooks/useNotification";
import useSettings from "../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {adminFlaggedCommentsService} from "../../services/adminFlaggedComment.service";
import getUserConfirmation from "../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../utils/error.util";
import {formatDate} from "../../utils/formatDate";

const MAX_TEXT_LENGTH = 25;

const FlaggedComments = () => {
    const [status, setStatus] = useState({status: "flagged"});
    const [rejectCommentToggle, setRejectCommentToggle] = useState<boolean>(false);
    const methods = useForm();
    const searchTerm = methods.watch("flaggedCommentSearch");
    const statusValue = methods.watch("statusDropdown");
    const debouncedSearch = useDebounce(searchTerm, 500, {
        minLength: 3,
        minLengthNotify: "Please enter at least 3 characters",
    });
    const notify = useNotification();
    const {updateSettings} = useSettings();
    const [refetchKey, setRefetchKey] = useState(0);

    const [selectedItem, setSelectedItem] = useState<any>(undefined);

    const handleSelect = selectedItem => {
        setSelectedItem(selectedItem);
        setRejectCommentToggle(true);
    };
    const handleOnCloseModal = () => {
        setRejectCommentToggle(false);
        setSelectedItem(undefined);
    };

    const handleApprove = async data => {
        if (!data) return;
        const answer = await getUserConfirmation("Are you sure you want to approve this comment?");
        if (answer.value) {
            adminFlaggedCommentsService.approveComment(data.id, onSuccessApprove, handleError);
        }
    };

    const renderCommentItem = (row: any) => {
        const {subject} = row;
        if (subject?.type === "shoutOut") {
            return (
                <>
                    {subject?.shout_out?.length < MAX_TEXT_LENGTH ? (
                        <>{subject?.shout_out}</>
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{subject?.shout_out}</Tooltip>}>
                            <>{subject?.shout_out?.substring(0, MAX_TEXT_LENGTH)}...</>
                        </OverlayTrigger>
                    )}
                </>
            );
        }
        if (subject?.type === "comment") {
            return (
                <>
                    {subject?.comment?.length < MAX_TEXT_LENGTH ? (
                        subject.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{subject.comment}</Tooltip>}>
                            <p className="text-dark">{subject.comment.substring(0, MAX_TEXT_LENGTH)}...</p>
                        </OverlayTrigger>
                    )}
                </>
            );
        }
        if (subject?.type === "media" && subject?.mime_type === "video/mp4") {
            const videoUrl = routeConfig.WatchVideo.path.replace(":id", subject?.id);
            return (
                <Link to={videoUrl}>
                    {subject?.custom_properties?.title?.length < MAX_TEXT_LENGTH ? (
                        subject.custom_properties?.title
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{subject.custom_properties?.title}</Tooltip>}>
                            <p style={{color: "var(--bs-link-color)"}}>
                                {subject.custom_properties?.title?.substring(0, MAX_TEXT_LENGTH)}...
                            </p>
                        </OverlayTrigger>
                    )}
                </Link>
            );
        }
        if (subject?.type === "blog") {
            const blogUrl = routeConfig.PublicBlog.path.replace(":friendly_url", subject.friendly_url!);
            return (
                <Link to={blogUrl}>
                    {subject?.title?.length < MAX_TEXT_LENGTH ? (
                        subject.title
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{subject?.title}</Tooltip>}>
                            <p style={{color: "var(--bs-link-color)"}}>
                                {subject.title.substring(0, MAX_TEXT_LENGTH)}...
                            </p>
                        </OverlayTrigger>
                    )}
                </Link>
            );
        }
        if (subject?.type === "mediaGallery") {
            return (
                <>
                    {subject?.custom_properties?.title?.length < MAX_TEXT_LENGTH ? (
                        subject?.custom_properties?.title
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{subject?.custom_properties?.title}</Tooltip>}>
                            <p>{subject?.custom_properties?.title?.substring(0, MAX_TEXT_LENGTH)}...</p>
                        </OverlayTrigger>
                    )}
                </>
            );
        }

        return <> </>;
    };

    const renderCommentCell = (row: any) => {
        const {subject} = row;
        if (subject?.type === "shoutOut") {
            return (
                <>
                    {row.comment.length < MAX_TEXT_LENGTH ? (
                        row.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{row?.comment}</Tooltip>}>
                            <p className="text-dark">{row.comment.substring(0, MAX_TEXT_LENGTH)}...</p>
                        </OverlayTrigger>
                    )}
                </>
            );
        }
        if (subject?.type === "comment") {
            return (
                <>
                    {row?.comment?.length < MAX_TEXT_LENGTH ? (
                        row.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{row?.comment}</Tooltip>}>
                            <p className="text-dark">{row.comment.substring(0, MAX_TEXT_LENGTH)}...</p>
                        </OverlayTrigger>
                    )}
                </>
            );
        }
        if (subject?.type === "media" && subject?.mime_type === "video/mp4") {
            const videoUrl = routeConfig.WatchVideo.path.replace(":id", subject?.id);
            return (
                <Link to={videoUrl}>
                    {row?.comment?.length < MAX_TEXT_LENGTH ? (
                        row.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{row?.comment}</Tooltip>}>
                            <p style={{color: "var(--bs-link-color)"}}>
                                {row.comment.substring(0, MAX_TEXT_LENGTH)}...
                            </p>
                        </OverlayTrigger>
                    )}
                </Link>
            );
        }
        if (subject?.type === "blog") {
            const blogUrl = routeConfig.PublicBlog.path.replace(":friendly_url", subject.friendly_url!);
            return (
                <Link to={blogUrl}>
                    {row?.comment?.length < MAX_TEXT_LENGTH ? (
                        row.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{row?.comment}</Tooltip>}>
                            <p style={{color: "var(--bs-link-color)"}}>
                                {row.comment.substring(0, MAX_TEXT_LENGTH)}...
                            </p>
                        </OverlayTrigger>
                    )}
                </Link>
            );
        }
        if (subject?.type === "mediaGallery") {
            return (
                <>
                    {row?.comment?.length < MAX_TEXT_LENGTH ? (
                        row.comment
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={<Tooltip id="tooltip-discord">{row?.comment}</Tooltip>}>
                            <p>{row.comment.substring(0, MAX_TEXT_LENGTH)}...</p>
                        </OverlayTrigger>
                    )}
                </>
            );
        }

        return <> </>;
    };

    const onSuccessApprove = () => {
        notify("Review approved successfully", "Success");
        setRefetchKey(refetchKey + 1);
    };

    const handleError = (err: AxiosError) => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        notify(getErrorFromArray(err), "Error");
    };

    const columns: any = [
        {
            Header: "Action",
            accessor: "",
            Cell: (data: any) => (
                <>
                    <ButtonComponent
                        id="flaggedCommentApproveBtn"
                        className="cpBlueAltBtnThin"
                        onClick={() => handleApprove(data.row.original)}>
                        Approve
                    </ButtonComponent>
                    <ButtonComponent
                        id="flaggedCommentRejectBtn"
                        className="cpBlueAltBtnThin ms-3"
                        onClick={() => handleSelect(data.row.original)}
                        // onClick={() => setRejectCommentToggle(true)
                    >
                        Reject
                    </ButtonComponent>
                </>
            ),
        },
        {
            Header: "Comment",
            accessor: "comment",
            Cell: (data: any) => <>{renderCommentCell(data.row.original)}</>,
        },
        {
            Header: "Content",
            accessor: "content",
            Cell: (data: any) => <>{renderCommentItem(data.row.original)}</>,
        },
        {
            Header: "Date of Review",
            accessor: "created_at",
            Cell: (data: any) => formatDate(data.value, "MM/dd/yyyy"),
        },
        {
            Header: "Flagged By",
            accessor: "flagged_by.name",
            Cell: (data: any) =>
                data.row.original.flagged_by && (
                    <Link to={routeConfig.UserProfile.path.replace(":id", data.row.original.flagged_by.friendly_url)}>
                        {data.value}
                    </Link>
                ),
        },
        {
            Header: "Status",
            accessor: "status",
            Cell: (data: any) => data.value.charAt(0).toUpperCase() + data.value.slice(1),
        },
    ];

    useEffect(() => {
        if (!statusValue) return;
        setStatus({status: statusValue});
    }, [statusValue]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <FormProvider {...methods}>
                    <div className="d-flex flex-row justify-content-between align-items-end">
                        <div className="d-flex flex-column">
                            <TitleContainer as="h2" text="Flagged Comments" position="start" className="m-0" />
                            <SubtitleContainer
                                as="span"
                                text="Showing all flagged comments for approval. To see other status change view."
                                elClassName="mb-3"
                            />
                        </div>
                        <div className="d-flex flex-column justify-content-end">
                            <SubtitleContainer as="span" text="View all reviews based on status" elClassName="m-0" />
                            <DropdownComponent
                                id="statusDropdown"
                                options={[
                                    ["flagged", "Flagged"],
                                    ["approved", "Approved"],
                                    ["rejected", "Rejected"],
                                ]}
                                containerClassName="mb-0"
                            />
                        </div>
                    </div>
                    <div className="d-flex flex-column">
                        <TextBoxComponent id="flaggedCommentSearch" placeholder="Search Comments by Comment" />
                        <TableComponent
                            id="adminFlaggedComments"
                            columns={columns}
                            serviceHandler={adminFlaggedCommentsService.getFlaggedComments}
                            paginated
                            searchWord={debouncedSearch}
                            noResultsMessage="No Comments Found"
                            customParameters={status}
                            refetchKey={refetchKey}
                        />
                        {selectedItem && (
                            <RejectFlagCommentModal
                                subject_id={selectedItem?.id}
                                setToggle={setRejectCommentToggle}
                                toggle={rejectCommentToggle}
                                handleOnCloseModal={handleOnCloseModal}
                                handleRejectCommentSuccess={() => setRefetchKey(refetchKey + 1)}
                            />
                        )}
                    </div>
                </FormProvider>
            </div>
        </div>
    );
};

export default FlaggedComments;
