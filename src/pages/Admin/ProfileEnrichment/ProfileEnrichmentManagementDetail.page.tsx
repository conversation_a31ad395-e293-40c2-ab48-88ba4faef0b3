import {AxiosError, AxiosResponse} from "axios";
import React, {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useNavigate, useParams} from "react-router-dom";
import {IProfileEnrichmentGetAllQuestionsType} from "../../../Interfaces/profileEnrichment.interface";
import QuestionOptions from "../../../components/Admin/ProfileEnrichmentManagement/QuestionOptions.component";
import ButtonComponent from "../../../components/FormControls/button.component";
import DropdownComponent from "../../../components/FormControls/dropdown.component";
import TextBoxComponent from "../../../components/FormControls/textBox.component";
import TitleContainer from "../../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import useNotification from "../../../hooks/useNotification";
import useSettings from "../../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {profileEnrichmentService} from "../../../services/profileEnrichment.service";
import styles from "../../../styles/pages/profileEnrichmentManagment.module.sass";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";

interface IProps {}

const createNewRoute = "create-new";
const newQuestion: IProfileEnrichmentGetAllQuestionsType = {
    question: "",
    type: "OpenText",
    is_archived: false,
    type_is_of_vendor: true,
};

const ProfileEnrichmentManagementDetail: React.FC<IProps> = () => {
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [question, setQuestion] = useState<any>(newQuestion);
    const {id} = useParams<any>();
    const navigate = useNavigate();
    const methods = useForm({
        defaultValues: {
            question: "",
            type: "OpenText",
            is_archived: false,
            company_type: "Vendor",
            ...question,
        },
    });
    const questionType = methods.watch("type");
    const notify = useNotification();
    const {updateSettings} = useSettings();

    const handleSuccessSaveQuestion = (res: AxiosResponse) => {
        setIsSaving(false);
        if (id === createNewRoute) {
            navigate(routeConfig.ProfileEnrichmentManagementDetail.path.replace(":id", res.data.id));
            setQuestion(res.data);
        }
        notify("Sucessfully saved question", "Success");
    };

    const handleSave = () => {
        const requestBody = {
            question: methods.watch("question"),
            type: methods.watch("type"),
            is_archived: false,
            type_is_of_vendor: methods.watch("company_type") === "Vendor",
            id: question.id || null,
        };
        if (requestBody.question.length === 0) {
            notify("Please enter question", "Error");
            return;
        }
        setIsSaving(true);
        if (id === createNewRoute) {
            profileEnrichmentService.createQuestion(requestBody, handleSuccessSaveQuestion, handleError);
        } else {
            profileEnrichmentService.updateQuestion(requestBody, handleSuccessSaveQuestion, handleError);
        }
    };

    const handleError = (err: AxiosError<any>) => {
        notify(getErrorFromArray(err), "Error");
    };

    const handleCancel = () => {
        updateSettings(UPDATE_SETTINGS, {loading: false});
        navigate(routeConfig.ProfileEnrichmentManagement.path);
    };

    useEffect(() => {
        methods.setValue("question", question.question);
        methods.setValue("type", question.type);
        methods.setValue("company_type", question.type_is_of_vendor ? "Vendor" : "MSP");
        methods.setValue("is_archived", question.is_archived);
        methods.setValue("id", question.id);
        //eslint-disable-next-line
    }, [question]);

    useEffect(() => {
        if (id !== createNewRoute) {
            updateSettings(UPDATE_SETTINGS, {loading: true});
            profileEnrichmentService.getAllQuestions(
                null,
                (res: AxiosResponse) => {
                    const found = res.data.find(q => q.id === id);
                    if (found) {
                        setQuestion(found);
                    } else {
                        setQuestion(newQuestion);
                    }
                    updateSettings(UPDATE_SETTINGS, {loading: false});
                },
                handleError,
            );
        }
        setQuestion(newQuestion);
        // eslint-disable-next-line
    }, [id]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <div className="col-12">
                    <TitleContainer
                        as="h1"
                        text={id === "create-new" ? "Create Question" : "Edit Question"}
                        position="start"
                    />
                </div>
                <div className={styles.cardContainer}>
                    <FormProvider {...methods}>
                        <form onSubmit={methods.handleSubmit(handleSave)}>
                            <DropdownComponent
                                id="company_type"
                                options={[
                                    ["Vendor", "Vendor"],
                                    ["MSP", "MSP"],
                                ]}
                                label="Company Type: *"
                                selectedOption={methods.watch("company_type")}
                                disabled={question === null}
                            />
                            <TextBoxComponent
                                id="question"
                                label="Question: *"
                                placeholder="Enter question"
                                disabled={question === null}
                                value={methods.watch("question")}
                            />
                            <DropdownComponent
                                id="type"
                                options={[
                                    ["OpenText", "Open Text"],
                                    ["Date", "Date"],
                                    ["MultipleAnswer", "Multiple Answer"],
                                    ["SingleAnswer", "Single Answer"],
                                ]}
                                containerClassName="m-0"
                                label="Question Type: *"
                                disabled={question === null}
                                selectedOption={methods.watch("type")}
                            />
                        </form>
                        {(questionType === "SingleAnswer" || questionType === "MultipleAnswer") &&
                            (id ? <QuestionOptions questionId={question.id} /> : <h4>Save to add options</h4>)}
                    </FormProvider>
                    <div className="col-5 d-flex justify-content-end align-items-center">
                        <ButtonComponent
                            disabled={isSaving}
                            id="save-btn"
                            className={styles.editBtn}
                            onClick={handleSave}>
                            {isSaving ? <Loader loading inline /> : "Save"}
                        </ButtonComponent>
                        <ButtonComponent id="cancel-btn" className={styles.cancelBtn} onClick={handleCancel}>
                            Cancel
                        </ButtonComponent>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProfileEnrichmentManagementDetail;
