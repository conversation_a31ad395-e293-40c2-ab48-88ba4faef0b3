import {
    AddOutlined,
    ArrowBack,
    Check,
    EditOutlined,
    KeyboardCommandKeyOutlined,
    LayersOutlined,
    VisibilityOutlined,
} from "@mui/icons-material";
import {Divider, useTheme} from "@mui/material";
import {useQuery, useQueryClient, UseQueryResult} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useParams} from "react-router";
import {useNavigate} from "react-router-dom";
import CustomLink from "../../../components/Links/CustomLink.component";
import {DEFAULT_QUERY_CONFIGS, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import CompanyType, {MSPCompanyTypes, TCompanyType, VendorCompanyTypes} from "../../../constants/companyType.constant";
import routeConfig from "../../../constants/routeConfig";
import {VENDOR_TYPES} from "../../../constants/vendorProfiles.constants";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import {
    IDirectBasic,
    IDirectPremium,
    IMSPBasic,
    IMSPPremium,
    IVendorBasic,
    IVendorFree,
    IVendorPlus,
} from "../../../Interfaces/businessRules.interface";
import {ICompanyProfileType} from "../../../Interfaces/company.interface";
import {
    COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS,
    PERMISSION_GROUPS,
} from "../../../Interfaces/features/features.interface";
import {IProductFeature} from "../../../Interfaces/products.interface";
import {IPlanTypes} from "../../../Interfaces/profiles.interface";
import {companyService} from "../../../services/company.service";
import Badge from "../../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Button from "../../../uicomponents/Atoms/Button/Button.Component";
import Tab from "../../../uicomponents/Atoms/Tabs/Tab.component";
import Tabs from "../../../uicomponents/Atoms/Tabs/Tabs.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../../uicomponents/Molecules/Avatar/avatarV2.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import RoleListInput from "../../../uicomponents/Molecules/RoleListInput/RoleListInput.component";
import UserSearch from "../../../uicomponents/Molecules/UserSearch/userSearch.component";
import AddLocationDialog from "../../../uicomponents/Organism/AdminCompany/AddLocationDialog.component";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import {getErrorFromArray} from "../../../utils/error.util";
import {DATE_FORMAT, formatDate, MONTH_YEAR} from "../../../utils/formatDate";
import Loader from "../../../utils/loader";
import {TCompanyAdminCompanyType} from "../../../utils/permissions.util";
import CompanyFeaturesTab from "./companyFeaturesTab.component";
import LocationsTab from "./locationsTab.component";
import LogsTab from "./logsTab.component";
import SummaryTab from "./summaryTab.component";
import UsersTab, {TUserQueryPagination} from "./usersTab.component";

interface IClaimer {
    id: string;
    first_name: string;
    last_name: string;
    friendly_url: string;
    is_distributor?: boolean;
}
interface ICompany {
    id: string;
    name: string;
    company_profile_types_id: string;
    claimers: Array<IClaimer>;
    subscription_started_at: string;
    subscription_ends_at: string;
    subscription_hubspot_deal_type: string | null;
    hubspot_id: string;
    friendly_url: string;
    type: string;
    type_label: string;
    type_value: TCompanyType;
    is_distributor: boolean;
    is_affiliate_brand_main_company: boolean;
    manage_clients: boolean;
    manage_affiliates: boolean;
    avatar: string;
    profile_type: ICompanyProfileType["value"];
    description: string;
    partner_flag: boolean;
    features: IProductFeature | null;
    company_type: string | null;
    profile_company_friendly_name: string | null;
    profile_company_website_url: string | null;
    profile_vendor_handle: string | null;
    revenue: string | null;
    phone: string | null;
    industry: string | null;
    founded: string | null;
    brand: {
        id: string;
        name: string;
    };
    affiliate_id: string | null;
    affiliates_count: number | null;
    location: string | null;
    type_is_of_vendor: boolean;
    manage_expenses?: boolean;
}

interface ICompanyDetailResponse {
    company: ICompany;
    profile_types: Array<IPlanTypes>;
    business_rules: {
        VENDOR_BASIC: IVendorBasic;
        VENDOR_PLUS: IVendorPlus;
        VENDOR_PREMIUM: IVendorPlus;
        MSP_BUSINESS_BASIC: IMSPBasic;
        MSP_BUSINESS_PREMIUM: IMSPPremium;
        VENDOR_FREE: IVendorFree;
        DIRECT_BASIC: IDirectBasic;
        DIRECT_PREMIUM: IDirectPremium;
    };
}
interface ILoading {
    deletingClaimer: boolean;
    storingUser: boolean;
    search: boolean;
    storingCompany: boolean;
}
export interface ICompanyDetailObject extends ICompany {
    plan: string;
    created_date: string;
    end_date: string;
    avatar: string;
    profileTypes?: IPlanTypes[];
    parent_id?: string | null;
    parent?: {id: string; name: string};
}

interface ICompanyForm {
    name: string;
    profile_vendor_handle: string;
    description: string;
    status: string;
    company_type: string;
    company_profile_type_id: string;
    brand: string;
    location: string;
    profile_company_website_url: string;
    hubspot_id: string;
    affiliate_id: string;
    tier?: string;
    company_profile_types_id?: number;
    /* tier_status: string; */
    effective_date: string;
    expiry_date: string;
    address: string;
    address2: string;
    city: string;
    state: string;
    country: string;
    zip: string;
}

type TTabs = "users" | "affiliates" | "logs" | "features";

export default function CompanyManagementDetail() {
    const {company_id, tab: routeTab} = useParams<{company_id: string; tab: TTabs}>();
    const companyId = company_id?.split("?")[0] || "";
    const [userQuery, setUserQuery] = useState<UseQueryResult<TUserQueryPagination> | undefined>(undefined);
    const [allUsersCount, setAllUsersCount] = useState<number>(0);

    const companyDetailQuery = useQuery<ICompanyDetailObject>(
        ["companyDetail", companyId],
        () => {
            return new Promise((resolve, reject) => {
                companyService.getCompanyDetail(
                    companyId,
                    (response: AxiosResponse<ICompanyDetailResponse>) => {
                        const info = handleLoadDetail(response);
                        resolve(info);
                    },
                    reject,
                );
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!companyId,
            cacheTime: 0,
            staleTime: 0,
        },
    );
    const [openModal, setOpenModal] = useState<{
        user: boolean;
        delete: boolean;
        affiliate: boolean;
    }>({
        user: false,
        delete: false,
        affiliate: false,
    });
    const companyInfo = companyDetailQuery.data;
    const [loading, setLoading] = useState<ILoading>({
        deletingClaimer: false,
        storingUser: false,
        search: false,
        storingCompany: false,
    });
    const [selectedTab, setSelectedTab] = useState<TTabs>(routeTab || "features");
    const [isEditing, setIsEditing] = useState(false);
    const [finishedLoading, setFinishedLoading] = useState(false);
    const companyType = companyDetailQuery?.data?.type_value;

    const notify = useNotification();
    const theme = useTheme();
    const userMethods = useForm();
    const methods = useForm<ICompanyForm>();
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const storedCompanyId = useRef<string>(company_id ?? "");
    const isMSPClient = companyInfo?.type_value === CompanyType.MSP_CLIENT;

    const checkVendorType = (type: TCompanyType) => VendorCompanyTypes.includes(type);
    const checkMSPType = (type: TCompanyType) => MSPCompanyTypes.includes(type);

    const isVendor = companyInfo?.type_value ? checkVendorType(companyInfo?.type_value) : undefined;
    const isMSP = companyInfo?.type_value ? checkMSPType(companyInfo?.type_value) : undefined;

    const {hasPermissions, isSuperAdmin, getAdminCompanyPermission} = usePermissions();
    const pageAccessRules = {
        MANAGE_COMPANIES: !!(isSuperAdmin || hasPermissions([PERMISSION_GROUPS.ADMIN_COMPANY_MNGMT_UPDATE])),
        MANAGE_MSP_AFFILIATES: !!(isSuperAdmin || hasPermissions([PERMISSION_GROUPS.ADMIN_MSP_AFFILIATES_UPDATE])),
    };
    const profilePermissions = [
        getAdminCompanyPermission(
            COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS.COMPANY_PROFILE_READ,
            (companyType as TCompanyAdminCompanyType) || undefined,
        ),
    ];
    const naviStackPermissions = [
        getAdminCompanyPermission(
            COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS.MANAGE_STACK_READ,
            (companyType as TCompanyAdminCompanyType) || undefined,
        ),
    ];
    const channelCommandPermissions = [
        getAdminCompanyPermission(
            COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS.CHANNEL_COMMAND_READ,
            (companyType as TCompanyAdminCompanyType) || undefined,
        ),
    ];

    const handleLoadDetail = (response: AxiosResponse<ICompanyDetailResponse>) => {
        const plan = response.data.profile_types.find(
            r => `${r.id}` === `${response.data.company.company_profile_types_id}`,
        );
        const info: ICompanyDetailObject = {
            ...response.data.company,
            avatar: response.data.company.avatar || "",
            created_date: response.data.company.subscription_started_at
                ? formatDate(response.data.company.subscription_started_at, DATE_FORMAT)
                : "--",
            end_date: response.data.company.subscription_ends_at
                ? formatDate(response.data.company.subscription_ends_at, DATE_FORMAT)
                : "--",
            plan: plan?.label ?? "--",
            profile_type: plan?.value ?? "--",
            hubspot_id: response.data.company.hubspot_id || "--",
        };
        const tempDetail = JSON.parse(JSON.stringify(response.data));
        if (isVendor) {
            const tempFreePlan = tempDetail.profile_types.find(r => r.value === VENDOR_TYPES.VENDOR_FREE);
            tempDetail.profile_types = tempDetail.profile_types.filter(r => r.id !== tempFreePlan.id);
            tempDetail.profile_types = [tempFreePlan, ...tempDetail.profile_types];
        } else {
            tempDetail.profile_types = [...tempDetail.profile_types];
        }
        return {...info, profileTypes: tempDetail.profile_types};
    };

    const setFormDefaults = (info: ICompanyDetailObject) => {
        methods.setValue("name", info.name);
        methods.setValue("profile_vendor_handle", info.profile_vendor_handle || "--");
        methods.setValue("company_type", info.type_value);
        methods.setValue("brand", info.brand?.name || "");
        methods.setValue("location", info.location || "");
        methods.setValue("profile_company_website_url", info.profile_company_website_url || "");
        methods.setValue("hubspot_id", info.hubspot_id || "");
        methods.setValue("affiliate_id", info.affiliate_id || "");
        methods.setValue("tier", `${info.company_profile_types_id}`);
        methods.setValue(
            "effective_date",
            info.subscription_started_at ? formatDate(info.subscription_started_at, MONTH_YEAR) : "",
        );
        methods.setValue(
            "expiry_date",
            info.subscription_ends_at ? formatDate(info.subscription_ends_at, MONTH_YEAR) : "",
        );
        setFinishedLoading(true);
    };

    const companyProfileRoute = isVendor
        ? routeConfig.VendorProfile.path
        : companyInfo?.type_label === CompanyType.MSP_CLIENT
          ? routeConfig.MspClientProfile.path
          : routeConfig.MspProfile.path.replace(":tab", "features");

    const {activeCompany, setActiveCompany} = useActiveCompany();
    const mockedCompany = useRef(false);
    const mockActive = (friendlyUrl: string, id: string) => {
        if (activeCompany?.friendly_url === friendlyUrl) return openMockedTab();
        mockedCompany.current = true;
        setActiveCompany(friendlyUrl, id);
        setTimeout(() => {
            openMockedTab();
        }, 300);
    };

    const openMockedTab = () => {
        if (isVendor) {
            mockedCompany.current = false;
            window.open(
                routeConfig.CompanyPortal.path.replaceAll(
                    ":tab",
                    activeCompany?.partner_flag ? "my-channel" : "claimers",
                ),
                "_blank",
            );
        } else {
            mockedCompany.current = false;
            window.open(routeConfig.CompanyNaviStack.path.replaceAll(":tab", "navistack"), "_blank");
        }
    };

    const profileLink = isVendor
        ? routeConfig.VendorProfile.path.replace(":id", companyInfo?.friendly_url ?? "")
        : companyInfo?.type_value === CompanyType.MSP_CLIENT
          ? routeConfig.MspClientProfile.path.replace(":friendly_url", companyInfo?.friendly_url || "")
          : routeConfig.MspProfile.path.replace(":tab", "features").replace(":id", companyInfo?.friendly_url ?? "");

    const setTab = (e: React.SyntheticEvent<Element, Event>, value: TTabs) => {
        setSelectedTab(value);
        navigate(routeConfig.CompanyManagementDetail.path.replace(":company_id", companyId).replace(":tab", value), {
            preventScrollReset: true,
            replace: true,
        });
    };

    const handleSubmit = () => {
        const values = methods.getValues();
        if (!values.tier) return;
        const clearedValues = Object.keys(values).reduce((acc, key) => {
            if (["--", null, undefined].includes(values[key]) || values[key] === companyInfo?.[key]) {
                return acc;
            }
            return {...acc, [key]: values[key]};
        }, {} as ICompanyForm);
        if (`${clearedValues.tier}` === `${companyInfo?.company_profile_types_id}`) {
            delete clearedValues.tier;
            delete clearedValues.company_profile_types_id;
        }
        if (clearedValues.location) {
            const [, city, stateZip, , ..._] = clearedValues.location.split(", ");
            const [, zip] = stateZip.split(" ");
            clearedValues.address = clearedValues.location;
            clearedValues.city = clearedValues["location_city"] || city;
            clearedValues.zip = clearedValues["location_zip"] || zip;
            clearedValues.state = clearedValues["location_state"];
            clearedValues.country = clearedValues["location_country"];
            delete clearedValues["location_city"];
            delete clearedValues["location_state"];
            delete clearedValues["location_country"];
            delete clearedValues["location_zip"];
        }

        setLoading({...loading, storingCompany: true});
        companyService
            .adminUpdateCompany(companyId, clearedValues)
            .then(() => {
                notify("Company updated successfully!", "Success");
                setIsEditing(false);
                companyDetailQuery.refetch();
                queryClient.refetchQueries(["admin-companies"]);
                setLoading({...loading, storingCompany: false});
            })
            .catch((error: AxiosError) => {
                notify(getErrorFromArray(error), "Error");
                setLoading({...loading, storingCompany: false});
            });
    };

    useEffect(() => {
        if (companyInfo) {
            setFormDefaults(companyInfo);
            setIsEditing(false);
            if (storedCompanyId.current !== companyId) setSelectedTab("features");
            storedCompanyId.current = companyId;
        }
    }, [companyInfo]);

    useEffect(() => {
        if (userQuery && userQuery.data) {
            setAllUsersCount(userQuery.data?.meta.total);
        }
    }, [userQuery]);

    const handleAddUserToCompany = () => {
        const {role_id, user_id} = userMethods.getValues();
        if (!role_id || !user_id) {
            notify("Please, include the user and the role", "Error");
            return;
        }

        setLoading({...loading, storingUser: true});

        companyService
            .addUserRole(companyId, {role_id, user_id})
            .then(() => {
                notify("User Added to Company", "Success");
                userQuery?.refetch();
                setOpenModal({...openModal, user: false});
            })
            .catch(error => notify(getErrorFromArray(error), "Error"))
            .finally(() => {
                setLoading({...loading, storingUser: false});
            });
    };

    return (
        <FormProvider {...methods}>
            <div className={ROUTE_CLASS}>
                <div className={ROUTE_CONTAINER_CLASS}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 3,
                        }}>
                        <PageInnerHeader
                            id="company-management-detail"
                            title={{
                                text: "",
                            }}
                            hideDivider
                            actions={{
                                primary: pageAccessRules.MANAGE_COMPANIES
                                    ? {
                                          id: "edit-company",
                                          children: loading.storingCompany ? (
                                              <>
                                                  <Loader inline loading version="iconWhite" />
                                              </>
                                          ) : isEditing ? (
                                              <>
                                                  <Check width={12} htmlColor="white" /> Save Changes
                                              </>
                                          ) : (
                                              <>
                                                  <EditOutlined width={12} htmlColor="white" /> Edit Company
                                              </>
                                          ),
                                          onClick: () => (isEditing ? handleSubmit() : setIsEditing(true)),
                                          disabled: loading.storingCompany,
                                      }
                                    : undefined,
                                secondary:
                                    isEditing || (!isVendor && !isMSP) || isMSPClient
                                        ? undefined
                                        : {
                                              id: "company-specific-action",
                                              children: isVendor ? (
                                                  <>
                                                      <KeyboardCommandKeyOutlined width={12} color="secondary" /> Go to
                                                      Channel Command
                                                  </>
                                              ) : (
                                                  <>
                                                      <LayersOutlined width={12} color="secondary" /> Go to NaviStack
                                                  </>
                                              ),
                                              onClick: () => mockActive(companyInfo?.friendly_url || "", companyId),
                                              disabled: !!isVendor && !companyInfo?.partner_flag,
                                              permissions: isVendor ? channelCommandPermissions : naviStackPermissions,
                                          },
                                tertiary:
                                    !companyInfo?.type_value && !isEditing
                                        ? undefined
                                        : {
                                              id: "view-company-profile",
                                              children: isEditing ? (
                                                  <>Cancel</>
                                              ) : (
                                                  <>
                                                      <VisibilityOutlined width={12} color="secondary" />
                                                      View Profile
                                                  </>
                                              ),
                                              to: isEditing ? undefined : profileLink,
                                              onClick: isEditing
                                                  ? () => {
                                                        companyInfo && setFormDefaults(companyInfo);
                                                        setIsEditing(false);
                                                    }
                                                  : () => {
                                                        mockedCompany.current = true;
                                                        setActiveCompany(companyInfo?.friendly_url || "", companyId);
                                                    },
                                              permissions: isEditing ? undefined : profilePermissions,
                                          },
                                back: {
                                    id: "back-to-companies",
                                    children: (
                                        <Button
                                            id="back"
                                            variant="text"
                                            color="secondary"
                                            onClick={() => {
                                                navigate(-1);
                                            }}
                                            sx={{
                                                fontSize: "14px",
                                                fontWeight: "600",
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "8px",
                                                color: theme.palette.secondary[600],
                                            }}>
                                            <ArrowBack sx={{width: "18px", height: "18px"}} /> Back
                                        </Button>
                                    ),
                                },
                            }}
                        />
                        <Box
                            sx={{
                                display: "flex",
                                gap: 2,
                                alignItems: "center",
                            }}>
                            <AvatarComponentV2
                                size={80}
                                user={{
                                    avatar: companyInfo?.avatar || "",
                                    is_distributor: companyInfo?.is_distributor,
                                    profile_type: companyInfo?.profile_type || "",
                                    friendly_url: companyInfo?.friendly_url || "",
                                    name: companyInfo?.name || "",
                                    type: companyInfo?.type_value || "",
                                }}
                                isCompany
                                showProfileType
                                isRedirectEnabled={false}
                            />
                            <Box>
                                <Typography
                                    variant="subtitle3"
                                    fontInter
                                    sx={{
                                        color: "neutral.700",
                                        fontSize: "18px!important",
                                        fontWeight: "500!important",
                                        lineHeight: "27px",
                                    }}>
                                    {companyInfo?.name || "Loading..."}
                                </Typography>
                                <CustomLink
                                    to={companyProfileRoute
                                        .replace(":id", companyInfo?.friendly_url ?? "")
                                        .replace(":friendly_url", companyInfo?.friendly_url ?? "")}>
                                    <Typography
                                        variant="h4"
                                        fontInter
                                        sx={{
                                            color: "blue.600",
                                            fontSize: "15px!important",
                                            fontWeight: "600!important",
                                        }}>
                                        @{companyInfo?.profile_vendor_handle ?? companyInfo?.friendly_url}
                                    </Typography>
                                </CustomLink>
                            </Box>
                        </Box>
                        <Divider />
                        <SummaryTab
                            company={companyInfo}
                            isEditing={isEditing}
                            plans={companyInfo?.profileTypes ?? []}
                            isLoadingCompanyInfo={!finishedLoading}
                            refetchCompanyDetails={() => companyDetailQuery.refetch()}
                        />
                        {isEditing && (
                            <Box
                                sx={{
                                    display: "flex",
                                    gap: 3,
                                    justifyContent: "flex-start",
                                }}>
                                <Button
                                    id={"cancel-edit-company"}
                                    variant="text"
                                    color="secondary"
                                    onClick={() => {
                                        companyInfo && setFormDefaults(companyInfo);
                                        setIsEditing(false);
                                    }}
                                    disabled={loading.storingCompany}>
                                    Cancel
                                </Button>
                                <Button
                                    id={"save-company"}
                                    variant="contained"
                                    color="secondary"
                                    onClick={handleSubmit}
                                    disabled={loading.storingCompany}>
                                    {loading.storingCompany ? (
                                        <>
                                            <Loader inline loading version="iconWhite" />
                                        </>
                                    ) : (
                                        <>
                                            <Check width={12} htmlColor="white" /> Save Changes
                                        </>
                                    )}
                                </Button>
                            </Box>
                        )}
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                gap: 3,
                            }}>
                            <Tabs onChange={setTab} value={selectedTab}>
                                <Tab
                                    value="features"
                                    label={
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "10px",
                                            }}>
                                            <Typography
                                                fontInter
                                                variant="body3"
                                                sx={{
                                                    color: theme.palette.secondary.main,
                                                    fontWeight:
                                                        selectedTab === "features" ? "700!important" : "400!important",
                                                }}>
                                                Features
                                            </Typography>
                                        </Box>
                                    }
                                />
                                <Tab
                                    value="users"
                                    label={
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "10px",
                                            }}>
                                            <Typography
                                                fontInter
                                                variant="body3"
                                                sx={{
                                                    color: theme.palette.secondary.main,
                                                    fontWeight:
                                                        selectedTab === "users" ? "700!important" : "400!important",
                                                }}>
                                                Users
                                            </Typography>
                                            <Badge color="secondary" padding="2px 6px">
                                                {allUsersCount}
                                            </Badge>
                                        </Box>
                                    }
                                />
                                {companyInfo?.manage_affiliates &&
                                    companyInfo?.type_value !== CompanyType.MSP_LOCATION && (
                                        <Tab
                                            value="affiliates"
                                            label={
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        gap: "10px",
                                                    }}>
                                                    <Typography
                                                        fontInter
                                                        variant="body3"
                                                        sx={{
                                                            color: theme.palette.secondary.main,
                                                            fontWeight:
                                                                selectedTab === "affiliates"
                                                                    ? "700!important"
                                                                    : "400!important",
                                                        }}>
                                                        Locations
                                                    </Typography>
                                                    <Badge color="secondary" padding="2px 6px">
                                                        {companyInfo?.affiliates_count || 0}
                                                    </Badge>
                                                </Box>
                                            }
                                        />
                                    )}
                                <Tab
                                    value="logs"
                                    label={
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "10px",
                                            }}>
                                            <Typography
                                                fontInter
                                                variant="body3"
                                                sx={{
                                                    color: theme.palette.secondary.main,
                                                    fontWeight:
                                                        selectedTab === "logs" ? "700!important" : "400!important",
                                                }}>
                                                Logs
                                            </Typography>
                                        </Box>
                                    }
                                />
                            </Tabs>
                            <Box display="flex" alignItems="center" gap={2}>
                                {selectedTab === "affiliates" && pageAccessRules.MANAGE_MSP_AFFILIATES && (
                                    <Button
                                        id="add-affiliate-btn"
                                        variant="outlined"
                                        color="secondary"
                                        sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            gap: 1,
                                        }}
                                        disabled={!companyInfo?.manage_affiliates}
                                        tooltip={
                                            !companyInfo?.brand?.id &&
                                            companyInfo?.type_value === CompanyType.FRANCHISE_CORPORATE_MSP
                                                ? {
                                                      title: "First assign a brand to this company",
                                                  }
                                                : undefined
                                        }
                                        onClick={() => setOpenModal({...openModal, affiliate: true})}>
                                        <AddOutlined
                                            color="secondary"
                                            sx={{
                                                width: "18px",
                                                height: "18px",
                                            }}
                                        />
                                        Add Location
                                    </Button>
                                )}
                                {selectedTab === "users" && pageAccessRules.MANAGE_COMPANIES && (
                                    <Box>
                                        <Button
                                            id="add-user-btn"
                                            variant="outlined"
                                            color="secondary"
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: 1,
                                            }}
                                            onClick={() => setOpenModal({...openModal, user: true})}
                                            /* disabled={addClaimerBtnState.disabled} */
                                        >
                                            <AddOutlined
                                                color="secondary"
                                                sx={{
                                                    width: "18px",
                                                    height: "18px",
                                                }}
                                            />
                                            Add User
                                        </Button>
                                    </Box>
                                )}
                            </Box>
                        </Box>
                    </Box>
                    <UsersTab
                        show={selectedTab === "users"}
                        onUserQueryLoaded={userQuery => setUserQuery(userQuery)}
                        refetchCompanyDetails={() => companyDetailQuery.refetch()}
                        readOnly={!pageAccessRules.MANAGE_COMPANIES}
                    />
                    {selectedTab === "affiliates" && <LocationsTab company_id={companyInfo?.id} />}
                    {selectedTab === "logs" && <LogsTab />}
                    {selectedTab === "features" && (
                        <CompanyFeaturesTab
                            company={companyInfo}
                            readOnly={!pageAccessRules.MANAGE_COMPANIES}
                            isLoadingCompanyInfo={!finishedLoading}
                            refetchCompanyDetails={() => companyDetailQuery.refetch()}
                            isUpdatingCompany={loading?.storingCompany}
                        />
                    )}
                    <ModalComponent
                        open={openModal.user}
                        showCancelButton
                        showOkButton
                        okButtonDisabled={loading.storingUser}
                        onClose={() => {
                            userMethods.reset();
                            setOpenModal({...openModal, user: false});
                        }}
                        onSuccess={handleAddUserToCompany}
                        title="Add User"
                        cancelButtonText="Cancel"
                        loading={loading.storingUser}
                        okButtonText={loading.storingUser ? "Working..." : "Accept"}
                        content={
                            <FormProvider {...userMethods}>
                                <UserSearch
                                    isRequired
                                    id="user_id"
                                    allUsers
                                    sx={{
                                        width: "100%",
                                    }}
                                    placeholder="Type to search users"
                                />
                                <RoleListInput
                                    isRequired
                                    id="role_id"
                                    label="Role"
                                    companyId={company_id}
                                    sx={{
                                        width: "100%",
                                    }}
                                    placeholder="Select Role"
                                />
                            </FormProvider>
                        }
                    />
                    {openModal.affiliate && (
                        // <AddCompanyDialog
                        //     open={openModal.affiliate}
                        //     onClose={() => {
                        //         setOpenModal({...openModal, affiliate: false});
                        //         companyDetailQuery.refetch();
                        //         queryClient.refetchQueries(["admin-company-affiliates", companyInfo?.friendly_url]);
                        //         queryClient.refetchQueries(["companyAffilaites-filters"]);
                        //         queryClient.resetQueries(["companyDetail", companyInfo?.id]);
                        //         queryClient.resetQueries(["admin-franchises"]);
                        //         queryClient.resetQueries(["admin-franchises-filters"]);
                        //     }}
                        //     defaultValues={{
                        //         company_type: allCompanyTypes.find(c => c.value === CompanyType.FRANCHISE_MSP)?.value,
                        //         affiliate_brand_id: companyInfo?.brand?.id,
                        //         brand_name: companyInfo?.brand?.name,
                        //         parent_id: companyInfo?.id,
                        //     }}
                        //     disableFields={["company_type", "affiliate_brand_id", "company_profile_types_id"]}
                        // />
                        <AddLocationDialog
                            open={openModal.affiliate}
                            handleClose={() => {
                                setOpenModal({...openModal, affiliate: false});
                            }}
                            companyId={companyInfo?.id}
                        />
                    )}
                </div>
            </div>
        </FormProvider>
    );
}
