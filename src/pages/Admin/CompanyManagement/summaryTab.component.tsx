import {AlternateEmailOutlined} from "@mui/icons-material";
import {AxiosResponse} from "axios";
import DOMPurify from "dompurify";
import {useEffect, useState} from "react";
import {useFormContext} from "react-hook-form";
import {IPlanTypes} from "../../../Interfaces/profiles.interface";
import {COMPANY_TYPES_FILTER} from "../../../constants/commonStrings.constant";
import CompanyType, {MSPCompanyTypes, VendorCompanyTypes} from "../../../constants/companyType.constant";
import useCompanyTypes from "../../../hooks/fetches/useCompanyTypes";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Button from "../../../uicomponents/Atoms/Button/Button.Component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import AddressAutoComplete from "../../../uicomponents/Molecules/AddressAutocomplete/addressAutoComplete.component";
import MuiSelect from "../../../uicomponents/Molecules/MuiSelect/MuiSelect.component";
import {getErrorFromArray} from "../../../utils/error.util";
import {ICompanyDetailObject} from "./companyManagementDetail.page";
import {COMPANY_TYPE} from "../../../enums/companyTypes.enum";
import Loader from "../../../utils/loader";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import CachedOutlinedIcon from "@mui/icons-material/CachedOutlined";
import useTheme from "@mui/material/styles/useTheme";

interface IProps {
    company?: ICompanyDetailObject;
    isEditing?: boolean;
    plans: IPlanTypes[];
    isLoadingCompanyInfo?: boolean;
    refetchCompanyDetails: () => void;
}

export default function SummaryTab({company, isEditing, plans, isLoadingCompanyInfo, refetchCompanyDetails}: IProps) {
    const [canManageAffiliates, setCanManageAffiliates] = useState(company?.manage_affiliates ?? false);
    const [isCorporation, setIsCorporation] = useState(
        company ? company.type_value === CompanyType.FRANCHISE_CORPORATE_MSP : false,
    );
    const [hubspotSyncing, setHubspotSyncing] = useState(false);
    const {allCompanyTypes, typeIsManageClientsCandidate} = useCompanyTypes(
        company?.type_value !== CompanyType.MSP_CLIENT,
    );
    const {setValue, watch} = useFormContext();
    const [isVendor, setIsVendor] = useState<boolean>(
        allCompanyTypes.find(t => t.id === company?.type)?.type_is_of_vendor ?? false,
    );
    const [isAffiliate, setIsAffiliate] = useState<boolean>(company?.type_value === CompanyType.FRANCHISE_MSP);
    const [isCorporationValue, setIsCorporationValue] = useState<boolean>(false);
    const [locationInput, setLocationInput] = useState<string>("");
    const [readMoreDescription, setReadMoreDescription] = useState(false);
    const typeValue = watch("company_type");
    const planValue = watch("tier");
    const notify = useNotification();
    const theme = useTheme();
    const {hasPermissions, isSuperAdmin} = usePermissions();
    const pageAccessRules = {
        MANAGE_COMPANIES: !!(isSuperAdmin || hasPermissions([PERMISSION_GROUPS.ADMIN_COMPANY_MNGMT_UPDATE])),
        MANAGE_MSP_AFFILIATES: !!(isSuperAdmin || hasPermissions([PERMISSION_GROUPS.ADMIN_MSP_AFFILIATES_UPDATE])),
    };

    const [availablePlans, setAvailablePlans] = useState<{id: string; name: string; is_default: boolean}[]>([]);
    const filterPlansByCompanyType = (companyTypeValue: string) => {
        const typeIsOfVendor = VendorCompanyTypes.includes(companyTypeValue);
        const typeIsInternalIT = companyTypeValue === COMPANY_TYPE.DIRECT;
        const profileTypePrefix = typeIsOfVendor ? "VENDOR_" : typeIsInternalIT ? COMPANY_TYPE.DIRECT + "_" : "MSP_";
        const filteredPlans = plans
            .filter(plan => plan.value.startsWith(profileTypePrefix))
            .map(plan => ({id: `${plan.id}`, name: plan.label, is_default: plan.is_default}));
        setAvailablePlans(filteredPlans);
    };

    const onChangeCompanyType = (v: {id: string; name: string}[]) => {
        const companyTypeValue = v[0].id;
        setValue("company_type", companyTypeValue);

        const manageClientsCandidate = typeIsManageClientsCandidate(companyTypeValue);
        if (!manageClientsCandidate) {
            setValue("manage_clients", false);
        }

        const typeIsOfVendor = VendorCompanyTypes.includes(companyTypeValue);
        setIsVendor(typeIsOfVendor);
        if (!typeIsOfVendor) {
            setValue("is_distributor", false);
        }

        const typeIsOfCorporation = companyTypeValue === CompanyType.FRANCHISE_CORPORATE_MSP;
        const typeIsOfAffiliate = companyTypeValue === CompanyType.FRANCHISE_MSP;
        setIsAffiliate(typeIsOfAffiliate);
        setIsCorporationValue(typeIsOfCorporation);
        if (!typeIsOfCorporation) {
            setValue("manage_affiliates", !canManageAffiliates);
            setCanManageAffiliates(false);
        }
        const selectedPlanId = typeIsOfVendor ? 6 : 4;
        const shouldSetPlan =
            isVendor !== typeIsOfVendor &&
            (typeIsOfVendor ? !VendorCompanyTypes.includes(typeValue) : !MSPCompanyTypes.includes(typeValue));
        if (shouldSetPlan) {
            filterPlansByCompanyType(companyTypeValue);
            setValue("tier", selectedPlanId);
            setValue("company_profile_types_id", selectedPlanId);
        }
    };

    const handleSuccessSyncHubspot = (response: AxiosResponse) => {
        if (response.data?.hubspot_id) {
            refetchCompanyDetails?.();
            notify("Company Hubspot Id was successfully synced", "Success");
        } else {
            notify("Hubspot sync failed!", "Error");
        }
        setHubspotSyncing(false);
    };

    const handleErrorSyncHubspot = error => {
        setHubspotSyncing(false);
        notify(getErrorFromArray(error), "Error");
    };

    const handleSyncHubspot = () => {
        setHubspotSyncing(true);
        companyService.syncHubSpotId(company?.id || "", handleSuccessSyncHubspot, handleErrorSyncHubspot);
    };

    useEffect(() => {
        if (company) {
            setCanManageAffiliates(company.manage_affiliates);
            setIsCorporation(company.type_value === CompanyType.FRANCHISE_CORPORATE_MSP);
            setIsCorporationValue(
                isEditing ? isCorporation : company?.type_value === CompanyType.FRANCHISE_CORPORATE_MSP,
            );
            setIsVendor(allCompanyTypes.find(t => t.id === `${company.type}`)?.type_is_of_vendor ?? false);
            setLocationInput(company.location ?? "");
            filterPlansByCompanyType(company.type_value);
            setIsAffiliate(company.type_value === CompanyType.FRANCHISE_MSP);
        }
    }, [company, plans]);

    return (
        <Box sx={{display: "flex", flexDirection: "column", gap: 3}}>
            <Typography
                fontInter
                variant="h5"
                sx={{
                    color: "neutral.700",
                    fontWeight: "500!important",
                    fontSize: "18px!important",
                }}>
                Company Information
            </Typography>
            <Box sx={{display: "flex", flexDirection: "column", gap: 1}}>
                {isEditing && (
                    <>
                        <TextBoxComponent
                            id="name"
                            label="Company Name"
                            keepRegistered
                            prependInputText={
                                isAffiliate && company?.brand?.name ? `${company?.brand?.name} -` : undefined
                            }
                        />
                        {isVendor && (
                            <TextBoxComponent
                                id="profile_vendor_handle"
                                label="Company Handle"
                                keepRegistered
                                startAdornment={
                                    <AlternateEmailOutlined
                                        sx={{color: "neutral.500", width: "20px", height: "20px"}}
                                    />
                                }
                            />
                        )}
                    </>
                )}
                {isEditing ? null : (
                    <>
                        <Typography
                            fontInter
                            variant="subtitle3"
                            sx={{
                                color: "neutral.700",
                                fontWeight: "400!important",
                                fontSize: "14px!important",
                                lineHeight: "21px",
                            }}
                            dangerouslySetInnerHTML={{
                                __html: DOMPurify.sanitize(
                                    (company?.description?.length || 0) > 400 && !readMoreDescription
                                        ? company?.description?.substring(0, 400) + "..."
                                        : (company?.description ?? ""),
                                ),
                            }}
                        />
                        {(company?.description?.length || 0) > 400 && (
                            <Box>
                                <Button
                                    id="read_more_description"
                                    variant="text"
                                    color="secondary"
                                    sx={{color: "blue.600", fontWeight: "600"}}
                                    onClick={() => setReadMoreDescription(prev => !prev)}>
                                    {readMoreDescription ? "Read Less" : "Read More"}
                                </Button>
                            </Box>
                        )}
                    </>
                )}
            </Box>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    width: "100%",
                    "&>div>div": {
                        flexGrow: 1,
                        width: "auto",
                        maxHeight: "51px",
                        "& .MuiInputBase-input.MuiInput-input": {
                            height: "inital!important",
                        },
                    },
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 3,
                        width: "100%",
                        alignItems: "flex-end",
                        "&>div": {
                            maxWidth: isCorporationValue || isAffiliate ? "calc(33.33% - 12px)" : "calc(50% - 12px)",
                        },
                    }}>
                    <MuiSelect
                        id="company_type"
                        label="Type"
                        options={allCompanyTypes
                            .map(item => ({id: item.value, name: item.label}))
                            .filter(ct => !COMPANY_TYPES_FILTER.includes(ct.id))}
                        value={
                            typeValue
                                ? [
                                      {
                                          id: typeValue,
                                          name: allCompanyTypes.find(t => t.value === typeValue)?.label ?? "",
                                      },
                                  ]
                                : []
                        }
                        onChange={onChangeCompanyType}
                        readOnly={!isEditing}
                        showDropdownIcon
                        disableClearAll
                    />
                    {(isCorporationValue || isAffiliate) && (
                        <TextBoxComponent keepRegistered id="brand" label="Brand" readOnly />
                    )}
                    {!!company && (
                        <AddressAutoComplete
                            id="location"
                            label="Location"
                            readOnly={!isEditing}
                            sx={{"&>div>div": {height: "51px"}}}
                            inputValue={locationInput}
                            defaultValue={company.location}
                            onInputChange={(_, v) => setLocationInput(v)}
                            registerDetails
                        />
                    )}
                </Box>
                <Box sx={{display: "flex", gap: 3, width: "100%", alignItems: "flex-end"}}>
                    {isAffiliate ? (
                        <TextBoxComponent keepRegistered id="affiliate_id" label="Location ID" readOnly={!isEditing} />
                    ) : (
                        <TextBoxComponent
                            keepRegistered
                            id="profile_company_website_url"
                            label="Company Domain"
                            readOnly={!isEditing}
                        />
                    )}
                    <TextBoxComponent keepRegistered id="hubspot_id" label="HubSpot ID" readOnly />
                    {pageAccessRules.MANAGE_COMPANIES && (
                        <Button
                            id="add-user-btn"
                            variant="text"
                            color="secondary"
                            sx={{color: theme.palette.blue[600]}}
                            onChange={handleSyncHubspot}
                            disabled={hubspotSyncing}>
                            {hubspotSyncing ? (
                                <Loader inline version="iconBlue" loading />
                            ) : (
                                <>
                                    <CachedOutlinedIcon htmlColor={theme.palette.blue[600]} /> Sync to Hubspot
                                </>
                            )}
                        </Button>
                    )}
                </Box>
            </Box>
            <Typography
                fontInter
                variant="h5"
                sx={{
                    color: "neutral.700",
                    fontWeight: "500!important",
                    fontSize: "18px!important",
                }}>
                Subscription Information
            </Typography>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    width: "100%",
                    "&>div>div": {
                        flexGrow: 1,
                        width: "auto",
                    },
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 3,
                        width: "100%",
                        alignItems: "flex-end",
                    }}>
                    <MuiSelect
                        id="tier"
                        label="Tier"
                        options={availablePlans.map(item => ({id: item.id, name: item.name}))} /**/
                        value={
                            planValue
                                ? [
                                      {
                                          id: String(planValue),
                                          name: plans.find(t => String(t.id) === String(planValue))?.label ?? "",
                                      },
                                  ]
                                : []
                        }
                        onChange={v => {
                            setValue("tier", parseInt(v[0].id));
                            setValue("company_profile_types_id", parseInt(v[0].id));
                        }}
                        readOnly={!isEditing}
                        error={!planValue && !isLoadingCompanyInfo}
                        helperText="Please select a tier."
                        required
                        showDropdownIcon
                        disableClearAll
                    />
                    {/* <TextBoxComponent
                        keepRegistered
                        id="tier_status"
                        label="Status"
                        readOnly={!isEditing}
                        containerSx={{
                            "& input.MuiInputBase-input.MuiInput-input": {
                                height: "51px!important",
                                maxHeight: "51px!important",
                                boxSizing: "border-box",
                            },
                        }}
                    /> */}
                </Box>
                <Box sx={{display: "flex", gap: 3, width: "100%"}}>
                    <TextBoxComponent keepRegistered id="effective_date" label="Effective Date" readOnly />
                    <TextBoxComponent keepRegistered id="expiry_date" label="Expiration Date" readOnly />
                </Box>
            </Box>
        </Box>
    );
}
