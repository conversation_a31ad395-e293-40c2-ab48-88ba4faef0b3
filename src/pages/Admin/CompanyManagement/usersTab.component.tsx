import DeleteOutlined, {default as DeleteOutlinedIcon} from "@mui/icons-material/DeleteOutlined";
import EditIcon from "@mui/icons-material/Edit";
import MoreVert from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {Box, SvgIconProps, useTheme} from "@mui/material";
import {useQuery, UseQueryResult} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {useCallback, useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useNavigate, useParams} from "react-router-dom";
import {Column} from "react-table";
import {ICompanyUser} from "../../../Interfaces/company.interface";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import CustomLink from "../../../components/Links/CustomLink.component";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import useFilters from "../../../hooks/fetches/useFilters";
import {useDebounce} from "../../../hooks/useDebounce";
import useNotification from "../../../hooks/useNotification";
import {NestedObject} from "../../../models";
import {companyService} from "../../../services/company.service";
import Badge from "../../../uicomponents/Atoms/Badge/badge.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import CustomPagination from "../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import MenuButton, {IMenuButtonItem} from "../../../uicomponents/Molecules/MenuButton/menuButton.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import RoleListInput from "../../../uicomponents/Molecules/RoleListInput/RoleListInput.component";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {clearFilters} from "../../../utils/filters.util";
import {DATE_FORMAT, formatDate} from "../../../utils/formatDate";
import formatString, {CAPITALIZE_ALL} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";

type ICompanyUserRow = NestedObject<ICompanyUser & {name: string}>;

export type TUserQueryPagination = IPaginationData<ICompanyUser[]>;

interface IProps {
    show: boolean;
    refetchCompanyDetails: () => void;
    readOnly?: boolean;
    onUserQueryLoaded?: (userQuery: UseQueryResult<TUserQueryPagination>) => void;
}

export default function UsersTab({refetchCompanyDetails, readOnly = false, onUserQueryLoaded, show = false}: IProps) {
    const [itemsPerPage, setItemsPerPage] = useState<number>(40);
    const [page, setPage] = useState(1);
    const [search, setSearch] = useState<string>("");
    const [editUser, setEditUser] = useState<ICompanyUser | null>(null);
    const [deletingUser, setDeletingUser] = useState<{[id: string]: boolean}>({});
    const [isEditingUser, setIsEditingUser] = useState<boolean>(false);

    const editUserMethods = useForm();
    const {company_id} = useParams<{company_id: string}>();

    const {filterState, filtersQuery} = useFilters({
        filterKey: "companyUsers",
        enabled: !!company_id,
        queryParams: {company_id},
        defaultFilters: {
            sort: "sorting_first_name__ASC",
        },
    });
    const [filterContents, setFilterContents] = filterState;
    const debouncedSearch = useDebounce(search, 500);
    const debouncedFilter = useDebounce(filterContents, 500);
    const theme = useTheme();
    const navigate = useNavigate();
    const notify = useNotification();

    const usersQuery = useQuery<IPaginationData<ICompanyUser[]>>(
        ["admin-company-users", debouncedSearch, company_id, page, itemsPerPage, JSON.stringify(debouncedFilter)],
        () => {
            return new Promise((resolve, reject) =>
                companyService
                    .getUsersPaginated(company_id!, {
                        page,
                        items_per_page: itemsPerPage,
                        search_word: debouncedSearch,
                        dynamic: clearFilters(filterContents),
                    })
                    .then((res: AxiosResponse<IPaginationData<ICompanyUser[]>>) => resolve(res.data))
                    .catch(reject),
            );
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!company_id,
        },
    );

    useEffect(() => {
        if (usersQuery) {
            onUserQueryLoaded && onUserQueryLoaded(usersQuery);
        }
    }, [usersQuery.isFetchedAfterMount, usersQuery.isLoading]);

    const handleDeleteUser = async (user: ICompanyUser) => {
        const res = await getUserConfirmation("Are you sure you want to delete this user?", {
            mui: true,
            title: "Remove User?",
            form: (
                <>
                    <Typography variant="body2">
                        Are you sure you want to remove{" "}
                        <strong>
                            {user.first_name} {user.last_name} {user.email}
                        </strong>
                        ?
                    </Typography>

                    <Typography variant="body2">This action can not be undone.</Typography>
                </>
            ),
            okButtonText: "Remove",
            modalTheme: "error",
            okButtonIcon: <DeleteOutlinedIcon />,
        });
        if (res.value) {
            setDeletingUser(prev => ({
                ...prev,
                [user.id]: true,
            }));

            companyService
                .removeUserRole(company_id ?? "", {user_id: user.id, role_id: user.company_role.id})
                .then(() => {
                    usersQuery.remove();
                    refetchCompanyDetails();
                    filtersQuery.refetch();
                    notify("User deleted successfully", "Success");
                    setDeletingUser(prev => ({
                        ...prev,
                        [user.id]: false,
                    }));
                });
        }
    };

    const renderMenuItems: (rowData: ICompanyUser) => IMenuButtonItem[] = (rowData: ICompanyUser) => {
        const defaultIconProps: SvgIconProps = {color: "disabled", sx: {width: "16px", height: "16px"}};
        return [
            {
                id: "view",
                children: (
                    <>
                        <VisibilityIcon {...defaultIconProps} />
                        View
                    </>
                ),
                onClick: () => {
                    navigate(`${routeConfig.UserManagementDetail.path.replace(":id", rowData.id)}`);
                },
            },
            {
                id: "edit",
                children: (
                    <>
                        {" "}
                        <EditIcon {...defaultIconProps} /> Edit
                    </>
                ),
                onClick: () => {
                    editUserMethods.setValue("first_name", rowData.first_name);
                    editUserMethods.setValue("last_name", rowData.last_name);
                    editUserMethods.setValue("status", rowData.status);
                    editUserMethods.setValue("email", rowData.email);
                    setEditUser(rowData);
                },
            },

            {
                id: " ",
                children: (
                    <Typography
                        variant="subtitle3"
                        fontInter
                        sx={{
                            color: "error.600",
                            fontSize: "14px!important",
                            fontWeight: "400!important",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            gap: 1,
                        }}>
                        <DeleteOutlined {...defaultIconProps} color={"error"} />{" "}
                        <span style={{color: theme.palette.error["main"]}}>Remove</span>
                    </Typography>
                ),
                onClick: () => handleDeleteUser(rowData),
            },
        ];
    };

    const ViewCell = useCallback(
        tableData => {
            const user = tableData?.row?.original;
            return user && !readOnly ? (
                !!deletingUser?.[user.id] ? (
                    <Loader inline loading version="iconOrange" />
                ) : (
                    <MenuButton
                        id={`more-actions-${user.id}`}
                        buttonVariant="iconButton"
                        buttonProps={{
                            sx: {
                                width: "24px",
                                minWidth: "24px!important",
                                height: "24px",
                                boxShadow: "none",
                                flexShrink: 0,
                                position: "relative",
                                zIndex: 10,
                            },
                        }}
                        items={renderMenuItems(user)}
                        aria-label="settings">
                        <MoreVert color="neutral" />
                    </MenuButton>
                )
            ) : null;
        },
        [deletingUser],
    );

    const renderStatusBadge = (status: "Active" | "Inactive") => {
        const colors = {
            active: "success",
            inactive: "error",
        };
        return <Badge color={colors[status.toLowerCase()]}>{formatString(status, CAPITALIZE_ALL)}</Badge>;
    };

    const columns: Column<ICompanyUserRow>[] = [
        {
            Header: "Name",
            id: "first_name",
            Cell: ({row}) => (
                <CustomLink
                    to={routeConfig.UserManagementDetail.path
                        .replace(":id", row.original.id)
                        .replace(":tab", "user-companies")}
                    style={{color: theme.palette.blue[600]}}>
                    {row.original.first_name} {row.original.last_name}
                </CustomLink>
            ),
            defaultCanSort: true,
        },
        {Header: "Email", accessor: "email", defaultCanSort: true},
        {
            id: "role",
            Header: "Role",
            accessor: "roles",
            Cell: ({row}) => row.original.company_role.display_name,
            defaultCanSort: true,
        },
        {
            id: "status",
            Header: "Status",
            accessor: "status",
            Cell: ({value}) => value && renderStatusBadge(value),
            defaultCanSort: true,
        },
        {
            id: "email_verified_at",
            Header: "Verified At",
            accessor: "email_verified_at",
            Cell: ({value}) => (value ? formatDate(value, DATE_FORMAT) : ""),
            defaultCanSort: true,
        },
        {
            id: "created_at",
            Header: "Register Date",
            accessor: "created_at",
            Cell: ({value}) => (value ? formatDate(value, DATE_FORMAT) : ""),
            defaultCanSort: true,
        },
        {
            id: "last_logged_in_at",
            Header: "Last Login",
            accessor: "last_logged_in_at",
            Cell: ({value}) => (value ? formatDate(value, DATE_FORMAT) : ""),
            defaultCanSort: true,
        },
        {
            Header: "Actions",
            id: "actions",
            Cell: ViewCell,
        },
    ];

    const handleSortChange = (sortType: string, order_by: string) => {
        setFilterContents(prev => ({...prev, sort: `sorting_${order_by}__${sortType}`}));
    };

    const handleEditUser = useCallback(() => {
        setIsEditingUser(true);
        let roleId = editUserMethods.getValues().role_id;

        if (!roleId && editUser) {
            roleId = editUser.company_role.id || "";
        }
        companyService
            .updateUserRole(company_id || "", {
                role_id: roleId,
                user_id: editUser?.id || "",
                prev_role_id: editUser?.company_role.id || "",
            })
            .then(() => {
                notify("User Role Updated", "Success");
                usersQuery?.refetch();
                setEditUser(null);
                editUserMethods.reset();
            })
            .catch(error => notify(getErrorFromArray(error), "Error"))
            .finally(() => {
                setIsEditingUser(false);
            });
    }, [editUser]);

    if (!show) {
        return;
    }

    return (
        <>
            <PageInnerHeader
                id="users-header"
                filterState={filterState}
                filters={filtersQuery.data}
                filterKeysOrder={["status", "roles", "last_login", "register_date"]}
                searchState={[search, setSearch]}
            />
            <TableV2Component
                id="company-users-table"
                columns={columns}
                customData={(usersQuery.data?.data || []) as ICompanyUserRow[]}
                isLoading={usersQuery.isLoading}
                headerBackground={theme.palette.blue[100]}
                noResultsMessage="No users found."
                onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                orderBy="first_name"
                sortType="ASC"
            />
            <ModalComponent
                open={!!editUser}
                showCancelButton
                showOkButton
                title="Edit User"
                cancelButtonText="Cancel"
                okButtonText={"SAVE"}
                loading={isEditingUser}
                onClose={() => {
                    setEditUser(null);
                    editUserMethods.reset();
                }}
                onSuccess={handleEditUser}
                content={
                    <FormProvider {...editUserMethods}>
                        {editUser && (
                            <>
                                <Box display="flex" gap={2}>
                                    <TextBoxComponent
                                        containerStyles={{width: "30%"}}
                                        id="first_name"
                                        label="First Name"
                                        readOnly
                                        isRequired
                                    />
                                    <TextBoxComponent
                                        containerStyles={{width: "30%"}}
                                        id="last_name"
                                        label="Last Name"
                                        readOnly
                                        isRequired
                                    />
                                    <TextBoxComponent id="email" label="Email" readOnly isRequired />
                                </Box>

                                <Box display="flex" gap={2}>
                                    <RoleListInput
                                        id="role_id"
                                        label="Role"
                                        companyId={company_id}
                                        defaultValue={editUser.company_role.display_name || ""}
                                        placeholder="Select Role"
                                        style={{width: "50%"}}
                                        key={editUser.company_role.display_name}
                                    />
                                    <TextBoxComponent id="status" label="Status" readOnly isRequired />
                                </Box>
                            </>
                        )}
                    </FormProvider>
                }
            />
            <CustomPagination
                metadata={usersQuery.data?.meta || {total: 0, last_page: 1}}
                page={page}
                itemsPerPage={itemsPerPage}
                setPage={setPage}
                setItemsPerPage={setItemsPerPage}
            />
        </>
    );
}
