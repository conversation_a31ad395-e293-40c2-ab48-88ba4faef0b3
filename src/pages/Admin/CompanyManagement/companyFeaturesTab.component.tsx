import {useQueryClient} from "@tanstack/react-query";
import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import CompanyType from "../../../constants/companyType.constant";
import {featureFlagValues} from "../../../enums/featureFlagValues.enum";
import useCompanyTypes from "../../../hooks/fetches/useCompanyTypes";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Switch from "../../../uicomponents/Atoms/Switch/Switch.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {ICompanyDetailObject} from "./companyManagementDetail.page";
import {FormProvider, useForm} from "react-hook-form";
import BrandAutocomplete from "../../../uicomponents/Molecules/BrandAutocomplete/BrandAutocomplete.component";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";
import Textbox from "../../../uicomponents/Atoms/Textbox/Textbox.component";
import MSPParentAutoComplete from "../../../uicomponents/Molecules/MSPParentAutocomplete/MSPParentAutocomplete.component";
import AutoCompleteComponent from "../../../uicomponents/Molecules/Autocomplete/Autocomplete.component";
import {Skeleton} from "@mui/material";

interface IProps {
    company?: ICompanyDetailObject;
    isLoadingCompanyInfo?: boolean;
    refetchCompanyDetails: () => void;
    readOnly?: boolean;
    cancelEdit?: () => void;
    isUpdatingCompany?: boolean;
}

export default function CompanyFeaturesTab({
    company,
    refetchCompanyDetails,
    readOnly = false,
    cancelEdit,
    isUpdatingCompany,
}: IProps) {
    const [canManageClients, setCanManageClients] = useState(company?.manage_clients ?? false);
    const [canManageAffiliates, setCanManageAffiliates] = useState(company?.manage_affiliates ?? false);
    const [canManageExpenses, setCanManageExpenses] = useState(company?.manage_expenses ?? false);
    const [isDistributor, setIsDistributor] = useState(company?.is_distributor ?? false);
    const {allCompanyTypes, typeIsManageClientsCandidate} = useCompanyTypes(
        company?.type_value !== CompanyType.MSP_CLIENT,
    );
    const [isVendor, setIsVendor] = useState<boolean>(
        allCompanyTypes.find(t => t.id === company?.type)?.type_is_of_vendor ?? false,
    );
    const [isManageClientsCandidate, setIsManageClientsCandidate] = useState<boolean>(false);
    const [isPRMEnabled, setIsPRMEnabled] = useState(company?.partner_flag ?? false);
    const [isSavingFeatureFlag, setIsSavingFeatureFlag] = useState({
        manage_clients: false,
        partner_flag: false,
        is_distributor: false,
        manage_affiliates: false,
        manage_expenses: false,
    });
    const methods = useForm();
    const {invalidateMatchedQueries} = useQueryHelper();
    const {updateCompany, isUpdating} = useCompany(company?.friendly_url || "", {
        isCompany: true,
    });
    const isDistributorValue = isDistributor;

    const isAffiliateCompany =
        company?.type_value === CompanyType.FRANCHISE_MSP || company?.type_value === CompanyType.MSP_LOCATION;
    const isFranchiseAffiliate = company?.type_value === CompanyType.FRANCHISE_MSP;
    const isMSPLocation = company?.type_value === CompanyType.MSP_LOCATION;
    const isMSPAffiliateCorporation = company?.type_value === CompanyType.FRANCHISE_CORPORATE_MSP;
    const isClientMsp = company?.type_value === CompanyType.MSP_CLIENT;
    const isUpdatingBrand = isUpdating[MutateTypes.Update + "updateBrand"];
    const isUpdatingMSPParent = isUpdating[MutateTypes.Update + "updateParent"];
    const multiStackTypeWatcher = methods.watch("multistack-type");
    const mspParentWatcher = methods.watch("mspParent");
    const brandWatcher = methods.watch("brand");

    const notify = useNotification();
    const queryClient = useQueryClient();
    const saveFeatureFlag = (featureFlag: keyof typeof isSavingFeatureFlag, value: boolean) => {
        if (!company) return;
        let serviceName: string = "";
        switch (featureFlag) {
            case featureFlagValues.IS_DISTRIBUTOR:
                serviceName = "isDistributorUpdate";
                break;
            case featureFlagValues.MANAGE_AFFILIATES:
                serviceName = "manageAffiliatesUpdate";
                break;
            case featureFlagValues.MANAGE_CLIENTS:
                serviceName = "manageClientsUpdate";
                break;
            case featureFlagValues.PARTNER_FLAG:
                serviceName = "prmStatusUpdate";
                break;
            case featureFlagValues.MANAGE_EXPENSES:
                serviceName = "manageExpensesUpdate";
                break;
            default:
                return new Error("No logic implemented for " + featureFlag);
        }
        setIsSavingFeatureFlag(prev => ({...prev, [featureFlag]: true}));
        companyService[serviceName](company.id, value)
            .then(() => {
                notify("Flag updated successfully!", "Success");
                refetchCompanyDetails();
                setIsSavingFeatureFlag(prev => ({...prev, [featureFlag]: false}));
                queryClient.resetQueries(["admin-companies"]);
                cancelEdit?.();
            })
            .catch((error: AxiosError) => {
                notify(getErrorFromArray(error), "Error");
                setIsSavingFeatureFlag(prev => ({...prev, [featureFlag]: false}));
            });
    };

    const onSuccessToggleFlag = () => {
        invalidateMatchedQueries(["companyDetail", company?.id]);
        cancelEdit?.();
    };

    const handleBrandChange = (main_company_id: string, affiliate_brand_id: string, isParent: boolean = false) => {
        updateCompany.mutate({
            mutate_type: MutateTypes.Update,
            mutate_is_updating_append: "updateBrand",
            id: company?.id,
            ...(!isParent && {parent_id: main_company_id}),
            affiliate_brand_id,
            onSuccess: onSuccessToggleFlag,
        });
    };

    const handleParentChange = (parent_id: string) => {
        const company_type =
            multiStackTypeWatcher === "child" && company?.type_value !== CompanyType.MSP_LOCATION
                ? CompanyType.MSP_LOCATION
                : company?.type_value;
        updateCompany.mutate({
            mutate_type: MutateTypes.Update,
            mutate_is_updating_append: "updateParent",
            id: company?.id,
            parent_id,
            company_type,
            onSuccess: onSuccessToggleFlag,
        });
    };

    useEffect(() => {
        if (multiStackTypeWatcher === "parent" && company?.parent_id) {
            methods.setValue("mspParent", "");
            updateCompany.mutate({
                mutate_type: MutateTypes.Update,
                mutate_is_updating_append: "updateParent",
                id: company?.id,
                parent_id: null,
                company_type: CompanyType.ISP_ALL,
                onSuccess: onSuccessToggleFlag,
            });
        }
    }, [multiStackTypeWatcher]);

    useEffect(() => {
        if (company) {
            setCanManageClients(company.manage_clients);
            setCanManageAffiliates(company.manage_affiliates);
            setCanManageExpenses(company.manage_expenses ?? false);
            setIsDistributor(company.is_distributor);
            setIsManageClientsCandidate(typeIsManageClientsCandidate(company.type_value));
            setIsVendor(allCompanyTypes.find(t => t.id === `${company.type}`)?.type_is_of_vendor ?? false);
            setIsPRMEnabled(company.partner_flag);
            if (!company.type_is_of_vendor) {
                methods.reset({
                    ...(company?.brand?.id && {brand: company.brand.id}),
                    ...(company?.parent_id && {mspParent: company.parent_id}),
                    ...(company?.type_value !== CompanyType.FRANCHISE_CORPORATE_MSP && {
                        "multistack-type": !!company.parent_id ? "child" : "parent",
                    }),
                });
            }
        }
    }, [company]);

    return (
        <FormProvider {...methods}>
            <Box sx={{display: "flex", flexDirection: "column", gap: 3}}>
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                        width: "100%",
                        "&>div>div": {
                            flexGrow: 1,
                        },
                    }}>
                    {!company &&
                        Array(2)
                            .fill(null)
                            .map((_, index) => <Skeleton key={index} height={60} width="100%" />)}
                    {!isVendor && !isClientMsp && !!company && (
                        <Box sx={{display: "flex", alignItems: "center", gap: 2, justifyContent: "flex-start"}}>
                            <Switch
                                checked={canManageExpenses}
                                className="align-middle"
                                disabled={!!readOnly || isSavingFeatureFlag.manage_expenses || isUpdatingCompany}
                                onChange={() => {
                                    setCanManageExpenses(!canManageExpenses);
                                    saveFeatureFlag(featureFlagValues.MANAGE_EXPENSES, !canManageExpenses);
                                }}
                            />
                            <Box display="flex" flexDirection="column" gap={1}>
                                <Typography
                                    variant="body3"
                                    fontInter
                                    sx={{color: "neutral.800", fontWeight: "400"}}
                                    title={canManageClients ? "Enabled" : "Disabled"}>
                                    Manage Expenses
                                </Typography>
                                <Typography variant="body4" fontInter sx={{color: "neutral.800", fontWeight: "400"}}>
                                    Enable plaid integration functionalities
                                </Typography>
                            </Box>
                            {isSavingFeatureFlag.manage_expenses && <Loader inline loading version="iconOrange" />}
                        </Box>
                    )}
                    {!isVendor && company?.type_value !== CompanyType.MSP_CLIENT && !!company && (
                        <Box sx={{display: "flex", alignItems: "center", gap: 2, justifyContent: "flex-start"}}>
                            <Switch
                                checked={canManageAffiliates}
                                className="align-middle"
                                disabled={
                                    !!readOnly ||
                                    isSavingFeatureFlag.manage_affiliates ||
                                    isAffiliateCompany ||
                                    (!!company?.affiliates_count && company?.manage_affiliates) ||
                                    isUpdatingCompany
                                }
                                onChange={() => {
                                    setCanManageAffiliates(!canManageAffiliates);
                                    saveFeatureFlag(featureFlagValues.MANAGE_AFFILIATES, !canManageAffiliates);
                                }}
                            />
                            <Box display="flex" flexDirection="column" gap={1} flexGrow={"0 !important"}>
                                <Typography
                                    variant="body3"
                                    fontInter
                                    sx={{color: "neutral.800", fontWeight: "400"}}
                                    title={canManageClients ? "Enabled" : "Disabled"}>
                                    MultiStack
                                </Typography>
                                <Typography variant="body4" fontInter sx={{color: "neutral.800", fontWeight: "400"}}>
                                    Enable features for Corporation/Affiliate or Parent/Child management
                                </Typography>
                            </Box>
                            <Box display="flex" flexDirection="row" gap={1}>
                                {isFranchiseAffiliate &&
                                    (readOnly ? (
                                        <Textbox value={company?.brand?.name} new label="Brand" disabled />
                                    ) : (
                                        <BrandAutocomplete
                                            id="brand"
                                            label="Brand"
                                            defaultOptions={company?.brand ? [company.brand] : undefined}
                                            hasMainCompany
                                            readOnly={!!readOnly}
                                            onValueChange={(_, __, newValue) => {
                                                if (newValue?.main_company_id) {
                                                    handleBrandChange(newValue?.main_company_id, newValue?.id);
                                                }
                                            }}
                                            disabled={isUpdatingBrand || isUpdatingCompany}
                                            sx={{width: "60%"}}
                                            key={brandWatcher + company?.brand?.id}
                                        />
                                    ))}
                                {!isFranchiseAffiliate &&
                                    isMSPLocation &&
                                    (readOnly ? (
                                        <>
                                            <Textbox
                                                value={"Child"}
                                                new
                                                label="MultiStack Type"
                                                readOnly
                                                sx={{width: 210}}
                                            />
                                            <Textbox
                                                value={company?.parent?.name}
                                                new
                                                label="Parent"
                                                readOnly
                                                sx={{width: 210}}
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <AutoCompleteComponent
                                                id="multistack-type"
                                                label="MultiStack Type"
                                                options={[
                                                    {id: "parent", label: "Parent"},
                                                    {id: "child", label: "Child"},
                                                ]}
                                                sx={{width: 210}}
                                                disabled={isUpdatingMSPParent || isUpdatingCompany}
                                                key={multiStackTypeWatcher}
                                            />
                                            <MSPParentAutoComplete
                                                id="mspParent"
                                                readOnly={!!readOnly}
                                                onValueChange={(_, __, newValue) => {
                                                    if (newValue?.id) {
                                                        handleParentChange(newValue?.id);
                                                    }
                                                }}
                                                disabled={isUpdatingMSPParent || isUpdatingCompany}
                                                sx={{width: 210}}
                                                defaultOptions={
                                                    !!company?.parent?.id
                                                        ? [{id: company.parent_id, name: company.parent.name}]
                                                        : []
                                                }
                                                key={mspParentWatcher + company?.parent?.id}
                                                filterOptions={options => {
                                                    return options.filter(o => o.id !== company?.id);
                                                }}
                                            />
                                        </>
                                    ))}
                                {!isVendor &&
                                    !isMSPLocation &&
                                    !isFranchiseAffiliate &&
                                    canManageAffiliates &&
                                    (company?.affiliates_count ? (
                                        <>
                                            {!isMSPAffiliateCorporation && (
                                                <Textbox
                                                    value="Parent"
                                                    new
                                                    label="MultiStack Type"
                                                    readOnly
                                                    sx={{width: 210}}
                                                />
                                            )}
                                            {isMSPAffiliateCorporation && (
                                                <BrandAutocomplete
                                                    id="brand"
                                                    label="Brand"
                                                    defaultOptions={company?.brand ? [company.brand] : undefined}
                                                    readOnly={!!readOnly}
                                                    hasMainCompany={false}
                                                    onValueChange={(_, __, newValue) => {
                                                        if (newValue?.id) {
                                                            handleBrandChange("", newValue?.id, true);
                                                        }
                                                    }}
                                                    disabled={isUpdatingBrand || isUpdatingCompany}
                                                    sx={{width: "60%"}}
                                                    key={brandWatcher + company?.brand?.id}
                                                />
                                            )}
                                        </>
                                    ) : readOnly ? (
                                        <>
                                            <Textbox
                                                value={company?.parent_id ? "Child" : "Parent"}
                                                new
                                                label="MultiStack Type"
                                                readOnly
                                                sx={{width: 210}}
                                            />
                                            {!!company?.parent_id && (
                                                <Textbox
                                                    value={company?.parent?.name}
                                                    new
                                                    label="Parent"
                                                    readOnly
                                                    sx={{width: 210}}
                                                />
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            <AutoCompleteComponent
                                                id="multistack-type"
                                                label="MultiStack Type"
                                                options={[
                                                    {id: "parent", label: "Parent"},
                                                    {id: "child", label: "Child"},
                                                ]}
                                                sx={{width: 210}}
                                                disabled={isUpdatingMSPParent || isUpdatingCompany}
                                            />
                                            {methods.watch("multistack-type") === "child" && (
                                                <MSPParentAutoComplete
                                                    id="mspParent"
                                                    readOnly={!!readOnly}
                                                    onValueChange={(_, __, newValue) => {
                                                        if (newValue?.id) {
                                                            handleParentChange(newValue?.id);
                                                        }
                                                    }}
                                                    disabled={isUpdatingMSPParent || isUpdatingCompany}
                                                    sx={{width: 210}}
                                                    defaultOptions={
                                                        !!company?.parent?.id
                                                            ? [{id: company.parent_id, name: company.parent?.name}]
                                                            : []
                                                    }
                                                    key={mspParentWatcher + company?.parent?.id}
                                                    filterOptions={options => {
                                                        return options.filter(o => o.id !== company?.id);
                                                    }}
                                                />
                                            )}
                                        </>
                                    ))}
                            </Box>
                            {isSavingFeatureFlag.manage_affiliates && <Loader inline loading version="iconOrange" />}
                        </Box>
                    )}
                    {isManageClientsCandidate && (
                        <Box sx={{display: "flex", alignItems: "center", gap: 2, justifyContent: "flex-start"}}>
                            <Switch
                                checked={canManageClients}
                                className="align-middle"
                                disabled={!!readOnly || isSavingFeatureFlag.manage_clients || isUpdatingCompany}
                                onChange={() => {
                                    setCanManageClients(!canManageClients);
                                    saveFeatureFlag(featureFlagValues.MANAGE_CLIENTS, !canManageClients);
                                }}
                            />
                            <Box display="flex" flexDirection="column" gap={1}>
                                <Typography
                                    variant="body3"
                                    fontInter
                                    sx={{color: "neutral.800", fontWeight: "400"}}
                                    title={canManageClients ? "Enabled" : "Disabled"}>
                                    BetterTracker
                                </Typography>
                                <Typography variant="body4" fontInter sx={{color: "neutral.800", fontWeight: "400"}}>
                                    Enable customer management capabilities
                                </Typography>
                            </Box>
                            {isSavingFeatureFlag.manage_clients && <Loader inline loading version="iconOrange" />}
                        </Box>
                    )}
                    {isVendor && (
                        <>
                            <Box sx={{display: "flex", alignItems: "center", gap: 2, justifyContent: "flex-start"}}>
                                <Switch
                                    checked={isPRMEnabled}
                                    className="align-middle"
                                    disabled={!!readOnly || isSavingFeatureFlag.partner_flag || isUpdatingCompany}
                                    onChange={() => {
                                        setIsPRMEnabled(prev => !prev);
                                        saveFeatureFlag(featureFlagValues.PARTNER_FLAG, !isPRMEnabled);
                                    }}
                                />
                                <Typography
                                    variant="body4"
                                    sx={{fontSize: "16px!important", color: "neutral.800", fontWeight: "400"}}>
                                    Channel Command is{" "}
                                    <b style={{fontWeight: "700"}}>{isPRMEnabled ? "Enabled" : "Disabled"}</b>
                                </Typography>
                                {isSavingFeatureFlag.partner_flag && <Loader inline loading version="iconOrange" />}
                            </Box>
                            <Box sx={{display: "flex", alignItems: "center", gap: 2, justifyContent: "flex-start"}}>
                                <Switch
                                    checked={isDistributorValue}
                                    className="align-middle"
                                    disabled={!!readOnly || isSavingFeatureFlag.is_distributor || isUpdatingCompany}
                                    onChange={() => {
                                        setIsDistributor(!isDistributor);
                                        saveFeatureFlag(featureFlagValues.IS_DISTRIBUTOR, !isDistributor);
                                    }}
                                />
                                <Typography
                                    variant="body4"
                                    sx={{fontSize: "16px!important", color: "neutral.800", fontWeight: "400"}}>
                                    Distributor Profile is{" "}
                                    <b style={{fontWeight: "700"}}>{isDistributorValue ? "Enabled" : "Disabled"}</b>
                                </Typography>
                                {isSavingFeatureFlag.is_distributor && <Loader inline loading version="iconOrange" />}
                            </Box>
                        </>
                    )}
                </Box>
            </Box>
        </FormProvider>
    );
}
