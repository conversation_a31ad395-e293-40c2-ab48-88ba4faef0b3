import EditOutlined from "@mui/icons-material/EditOutlined";
import {useTheme} from "@mui/material";
import {UseQueryResult} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm, useWatch} from "react-hook-form";
import {useNavigate} from "react-router-dom";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {UserStatus} from "../../../Interfaces/people.interface";
import {IUpdatingQuery, MutateTypes} from "../../../Interfaces/queries.interface";
import LoginHistoryTable from "../../../components/Admin/UsersManagment/loginHistoryTable.component";
import ButtonComponent from "../../../components/FormControls/button.component";
import DropdownComponent from "../../../components/FormControls/dropdown.component";
import ToggleInput from "../../../components/FormControls/toggle.component";
import SubtitleContainer from "../../../components/Titles/subtitleContainer.component";
import routeConfig from "../../../constants/routeConfig";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import {enumService} from "../../../services/enum.service";
import {profileService} from "../../../services/profile.service";
import {userService} from "../../../services/user.service";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../uicomponents/FormControls/TextBox/textBox.component";
import ModalComponent from "../../../uicomponents/Molecules/Modal/Modal.component";
import AccessControl from "../../../utils/accessControl.util";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {DATE_FORMAT, formatDate} from "../../../utils/formatDate";
import Loader from "../../../utils/loader";
import {validateEmail} from "../../../utils/validation.util";
import {IAdminUserDetail} from "./userManagementDetailTabs.page";

interface IProps {
    userInfo: IAdminUserDetail;
    updatePeople: any;
    isUpdatingPeople: IUpdatingQuery;
    people: UseQueryResult<IAdminUserDetail | undefined>;
}

const UserManagementDetail = (props: IProps) => {
    const userId = props.userInfo.id;
    const {userInfo} = props;
    const [editingProfileType, setEditingProfileType] = useState<boolean>(false);
    const [editingProfileStatus, setEditingProfileStatus] = useState<boolean>(false);
    const [firstLoad, setFirstLoad] = useState<boolean>(true);
    const [profileTypes, setProfileTypes] = useState<Array<[string, string]>>([]); // so that is shows the loading symbol
    const [loadingTFA, setLoadingTFA] = useState<boolean>(false);
    const [loadingDeleteUser, setLoadingDeleteUser] = useState<boolean>(false);
    const [loadingUpdateEmail, setLoadingUpdateEmail] = useState<boolean>(false);
    const [loadingUpdateEmailSaveClicked, setLoadingUpdateEmailSaveClicked] = useState<boolean>(false);
    const [currentEmail, setCurrentEmail] = useState<string>(userInfo.email);

    const theme = useTheme();
    const notify = useNotification();
    const navigate = useNavigate();
    const {authState} = useAuthState();
    const methods = useForm({
        defaultValues: {
            userProfileTypeChoice: 1,
            userProfileStatusChoice: userInfo?.status,
            userTFAChoice: userInfo?.has_tfa,
        },
    });
    const watcher = useWatch({control: methods.control});
    const addEmailMethods = useForm({
        defaultValues: {
            updatedEmail: currentEmail,
        },
    });

    useEffect(() => {
        enumService.getUserProfileTypes(handleSuccessProfileTypes, handleError);
        setFirstLoad(false);
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (!firstLoad && userInfo) {
            setEditingProfileType(false);

            // save new profile type to user
            props.updatePeople.mutate({
                id: userInfo.id,
                user_profile_type_value: watcher.userProfileTypeChoice,
                profile_type: watcher.userProfileTypeChoice,
                mutate_type: MutateTypes.Update,
                onSuccess: handleMutationSuccess,
            });
        }
        // eslint-disable-next-line
    }, [watcher.userProfileTypeChoice]);

    const handleSuccessProfileTypes = (response: AxiosResponse<{[id: string]: string}>) => {
        // fill the dropdown with the profile types
        const types: [string, string][] = Object.values(response.data).map((type: string) => [
            type.toUpperCase(),
            type,
        ]);
        setProfileTypes(types);
    };

    const editProfileType = () => {
        setEditingProfileType(true);
    };

    const editProfileStatus = () => {
        setEditingProfileStatus(true);
    };

    const handleError = (error: AxiosError<any>) => {
        setLoadingTFA(false);
        setLoadingUpdateEmail(false);
        setLoadingUpdateEmailSaveClicked(false);
        notify(getErrorFromArray(error), "Error");
    };

    const cancelProfileType = () => {
        setEditingProfileType(false);
    };

    const cancelProfileStatus = () => {
        setEditingProfileStatus(false);
    };

    const handleProfileStatusUpdate = (value: boolean) => {
        const newStatus = value ? "Active" : "Inactive";
        props.updatePeople.mutate({
            subject_id: userInfo?.id,
            profile_status: newStatus,
            mutate_type: MutateTypes.UpdateStatus,
        });
        setEditingProfileStatus(false);
    };

    const removeTfa = () => {
        setLoadingTFA(true);
        userService.deleteTFA(userId, handleSuccessDeleteTFA, handleError);
    };

    const handleSuccessDeleteTFA = () => {
        // save new 2fa status to user
        props.updatePeople.mutate({
            id: userId,
            hasTFA: false,
            mutate_type: MutateTypes.Update,
            onSuccess: handleMutationSuccess,
        });
    };

    const handleMutationSuccess = () => {
        setLoadingTFA(false);
        props.people.refetch();
    };

    const deleteUser = async () => {
        setLoadingDeleteUser(true);

        const confirmation = await getUserConfirmation(
            "Are you sure you want to delete this user? " +
                "Deleting will delete all history pertaining to the user. This action cannot be undone. " +
                "Recommend disabling the user rather than deleting.",
            {okButtonText: "DELETE", cancelButtonText: "CANCEL"},
        );

        if (confirmation.value) {
            userService.deleteUser(userId).then(() => {
                notify("User Deleted Successfully", "Success");

                // redirect to user grid
                navigate(routeConfig.UsersManagement.path);
            });
        } else {
            setLoadingDeleteUser(false);
        }
    };

    const updateEmail = async () => {
        setLoadingUpdateEmail(true);
    };

    const handleSaveEmail = () => {
        const email = addEmailMethods.getValues("updatedEmail");

        if (validateEmail(email)) {
            setLoadingUpdateEmailSaveClicked(true);
            profileService.updateEmail(userId, email, handleSuccessSaveEmail, handleError);
        }
    };

    const handleSuccessSaveEmail = (res: AxiosResponse) => {
        const email = res.data.email;

        setCurrentEmail(email);

        props.updatePeople.mutate({
            id: userId,
            email: email,
            mutate_type: MutateTypes.Update,
            onSuccess: handleMutationSuccess,
        });

        setLoadingUpdateEmailSaveClicked(false);
        setLoadingUpdateEmail(false);
        notify("User Email Updated Successfully", "Success");
    };

    return (
        <div>
            <FormProvider {...methods}>
                <div className="row">
                    <div className="col-12">
                        {userInfo && (
                            <>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Profile Type:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    {props.isUpdatingPeople?.[MutateTypes.Update] && (
                                        <>
                                            <Loader inline loading />
                                        </>
                                    )}
                                    {!editingProfileType ? (
                                        <>
                                            <SubtitleContainer
                                                elClassName="m-0 fw-bold"
                                                as="span"
                                                text={(userInfo?.user_profile_type_value as string) || ""}
                                                position="start"
                                            />

                                            <ButtonComponent
                                                onClick={editProfileType}
                                                id="editProfileType"
                                                disabled={props.isUpdatingPeople?.[MutateTypes.Update]}
                                                className="cpBlueAltBtn ms-3"
                                                buttonText="Edit Profile Type"
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <DropdownComponent
                                                id="userProfileTypeChoice"
                                                placeholder="Select a Type"
                                                label="User Profile Type *"
                                                isRequired
                                                options={profileTypes}
                                                selectedOption={`${watcher.userProfileTypeChoice ?? ""}`}
                                                validationSchema={v => v !== "0"}
                                            />

                                            <ButtonComponent
                                                onClick={cancelProfileType}
                                                id="cancelProfileType"
                                                className="cpBlueAltBtn ms-3"
                                                buttonText="Cancel"
                                            />
                                        </>
                                    )}
                                </div>
                                <div className="d-flex align-items-center gap-2 my-3">
                                    <SubtitleContainer
                                        as="span"
                                        text="Profile Status:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    {(props.isUpdatingPeople?.[MutateTypes.UpdateStatus] || props.people.isLoading) && (
                                        <>
                                            <Loader inline loading />
                                        </>
                                    )}
                                    {!editingProfileStatus ? (
                                        <>
                                            <SubtitleContainer
                                                elClassName="m-0 fw-bold"
                                                as="span"
                                                text={
                                                    props.people.isLoading ||
                                                    props.isUpdatingPeople?.[MutateTypes.UpdateStatus]
                                                        ? "Loading..."
                                                        : userInfo?.status === UserStatus.Active
                                                        ? "Active"
                                                        : "Inactive"
                                                }
                                                position="start"
                                            />
                                            {authState?.id === userId ? (
                                                ""
                                            ) : (
                                                <ButtonComponent
                                                    onClick={editProfileStatus}
                                                    id="editProfileStatus"
                                                    disabled={
                                                        props.isUpdatingPeople?.[MutateTypes.UpdateStatus] ||
                                                        !userInfo?.status
                                                    }
                                                    className="cpBlueAltBtn ms-3"
                                                    buttonText="Edit Profile Status"
                                                />
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            <ToggleInput
                                                checked={userInfo?.status === UserStatus.Active}
                                                onToggle={handleProfileStatusUpdate}
                                                beforeText="Inactive"
                                                afterText="Active"
                                                id="profileStatus"
                                                disabled={props.isUpdatingPeople?.[MutateTypes.UpdateStatus]}
                                            />

                                            <ButtonComponent
                                                onClick={cancelProfileStatus}
                                                id="cancelProfileStatus"
                                                className="cpBlueAltBtn ms-3"
                                                buttonText="Cancel"
                                            />
                                        </>
                                    )}
                                </div>

                                <div className="d-flex align-items-center gap-2 my-3">
                                    <SubtitleContainer
                                        as="span"
                                        text="2fa Status:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={userInfo.has_tfa ? "Yes" : "No"}
                                        position="start"
                                    />
                                    {userInfo.has_tfa && (
                                        <>
                                            <ButtonComponent
                                                onClick={removeTfa}
                                                id="removeTFAButton"
                                                className="cpBlueAltBtn ms-3"
                                                buttonText="Remove 2fa"
                                                disabled={loadingTFA}>
                                                {loadingTFA && (
                                                    <>
                                                        <Loader inline loading />
                                                    </>
                                                )}
                                            </ButtonComponent>
                                        </>
                                    )}
                                </div>

                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer as="span" text="Email:" position="start" elClassName="m-0" />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={currentEmail}
                                        position="start"
                                    />
                                    <AccessControl permissions={[PERMISSION_GROUPS.CP_SUPER_ADMIN]}>
                                        <ButtonComponent
                                            onClick={updateEmail}
                                            id="updateUserButton"
                                            className="cpBlueAltBtn ms-3"
                                            buttonText="Update Email"
                                            disabled={loadingUpdateEmail}>
                                            {loadingUpdateEmail && (
                                                <>
                                                    <Loader inline loading />
                                                </>
                                            )}
                                        </ButtonComponent>
                                    </AccessControl>
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Email Verified At:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={
                                            /* userInfo?.email_verified_at
                                                ? formatDate(userInfo?.email_verified_at, DATE_FORMAT)
                                                :  */
                                            "--"
                                        }
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Last Login:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={
                                            userInfo?.last_logged_in_at
                                                ? formatDate(userInfo?.last_logged_in_at, DATE_FORMAT)
                                                : "--"
                                        }
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Register Date:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={
                                            userInfo?.created_at ? formatDate(userInfo?.created_at, DATE_FORMAT) : "--"
                                        }
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Hubspot Contact Id:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        /* text={userInfo?.hubspot_contact_id} */
                                        text="--"
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer as="span" text="Handle:" position="start" elClassName="m-0" />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={userInfo?.handle}
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Company Name:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={userInfo?.company_name}
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Is Profile Private:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={userInfo?.is_private ? "Yes" : "No"}
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Is Profile Complete:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        /* text={userInfo?.is_profile_complete ? "Yes" : "No"} */
                                        text="--"
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <SubtitleContainer
                                        as="span"
                                        text="Is Registration Complete:"
                                        position="start"
                                        elClassName="m-0"
                                    />
                                    <SubtitleContainer
                                        elClassName="m-0 fw-bold"
                                        as="span"
                                        text={userInfo?.registration_step >= 5 ? "Yes" : "No"}
                                        position="start"
                                    />
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <AccessControl permissions={[PERMISSION_GROUPS.CP_SUPER_ADMIN]}>
                                        <ButtonComponent
                                            onClick={deleteUser}
                                            id="deleteUserButton"
                                            className="cpBlueAltBtn ms-3"
                                            buttonText="Delete User"
                                            disabled={loadingDeleteUser}>
                                            {loadingDeleteUser && (
                                                <>
                                                    <Loader inline loading />
                                                </>
                                            )}
                                        </ButtonComponent>
                                    </AccessControl>
                                </div>
                            </>
                        )}
                    </div>
                </div>
                <div className="row mt-3">
                    <SubtitleContainer
                        elClassName="m-0 fw-bold mb-2 text-dark"
                        as="span"
                        text="Login Details"
                        position={"start" || ""}
                    />
                    <LoginHistoryTable userId={props.userInfo?.id || ""} />
                </div>
            </FormProvider>

            <ModalComponent
                open={loadingUpdateEmail === true}
                maxWidth="lg"
                onClose={() => setLoadingUpdateEmail(false)}
                title={"Update User Email"}
                okButtonText="Save"
                icon={<EditOutlined htmlColor={theme.palette.neutral[500]} />}
                onSuccess={handleSaveEmail}
                justifyButtons="flex-start"
                loading={loadingUpdateEmailSaveClicked}
                okButtonDisabled={
                    !addEmailMethods.watch("updatedEmail") || !validateEmail(addEmailMethods.watch("updatedEmail"))
                }
                content={
                    <>
                        <FormProvider {...addEmailMethods}>
                            <Typography
                                variant="body1"
                                sx={{
                                    color: theme.palette.blue[800],
                                }}
                                fontInter>
                                PLEASE READ: You are changing the users email and login. Make sure this user email is
                                valid by emailing them to it and having them email you back!
                            </Typography>
                            <TextBoxComponent
                                id="updatedEmail"
                                label="Email"
                                placeholder="Input the new Email"
                                isRequired
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                isInvalid={!validateEmail(addEmailMethods.watch("updatedEmail"))}
                            />
                        </FormProvider>
                    </>
                }
            />
        </div>
    );
};

export default UserManagementDetail;
