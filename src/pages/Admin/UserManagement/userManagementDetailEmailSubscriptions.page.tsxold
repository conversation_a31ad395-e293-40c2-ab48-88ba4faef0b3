import {UseQueryResult} from "@tanstack/react-query";
import {AxiosError} from "axios";
import {useState} from "react";
import {IUpdatingQuery, MutateTypes} from "../../../Interfaces/queries.interface";
import ButtonComponent from "../../../components/FormControls/button.component";
import useNotification from "../../../hooks/useNotification";
import {userService} from "../../../services/user.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {IAdminUserDetail} from "./userManagementDetailTabs.page";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import UnsubscribeOutlinedIcon from "@mui/icons-material/UnsubscribeOutlined";
import {
    UNSUBSCRIBE_CANCEL_BTN_TEXT,
    UNSUBSCRIBE_OK_BTN_TEXT,
    UNSUBSCRIBE_TITLE_TEXT,
} from "../../../constants/commonStrings.constant";

interface IProps {
    userInfo: IAdminUserDetail;
    updatePeople: any;
    isUpdatingPeople: IUpdatingQuery;
    people: UseQueryResult<IAdminUserDetail | undefined>;
}

const UserManagementDetailEmailSubscriptions = (props: IProps) => {
    const notify = useNotification();
    const userId = props.userInfo.id;
    const [loadingEventSubscription, setLoadingEventSubscription] = useState<boolean>(false);
    const [isUserSubscribed, setIsUserSubscribed] = useState<boolean>(
        props.userInfo.email_subscriptions?.event_email_subscribed,
    );
    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const toggleEventSubscription = async () => {
        let executeEvent = true;

        if (isUserSubscribed) {
            const answer = await getUserConfirmation("", {
                title: UNSUBSCRIBE_TITLE_TEXT,
                form: (
                    <div style={{display: "flex", flexDirection: "column", gap: 15, lineHeight: "1.7"}}>
                        <p>
                            We're sorry to see you go! Before you confirm, are you sure you want to unsubscribe from our
                            events newsletter?{" "}
                        </p>

                        <p>
                            If you'd like to continue receiving the latest updates and news, click{" "}
                            <b>No, keep me subscribed</b>. If you're sure about leaving, click <b>Yes, unsubscribe</b>{" "}
                            to confirm.
                        </p>

                        <p>Thank you for being a valued member of Channel Program!</p>
                    </div>
                ),
                okButtonText: UNSUBSCRIBE_OK_BTN_TEXT,
                okButtonIcon: <UnsubscribeOutlinedIcon htmlColor="error" fontSize="small" />,
                cancelButtonText: UNSUBSCRIBE_CANCEL_BTN_TEXT,
                mui: true,
            });
            executeEvent = !!answer.value;
        }

        if (executeEvent) {
            setIsUserSubscribed(!isUserSubscribed);
            setLoadingEventSubscription(true);
            const json = {
                id: userId,
                event_email_subscribed: !isUserSubscribed,
            };

            userService.updateUser(json, () => handleSuccessToggleEventSubscription(isUserSubscribed), handleError);
        }
    };

    const handleSuccessToggleEventSubscription = (oldValue: boolean) => {
        // save new event subscription status to user
        props.updatePeople.mutate({
            id: userId,
            email_subscriptions: {event_email_subscribed: oldValue},
            mutate_type: MutateTypes.Update,
            onSuccess: handleMutationSuccess,
        });
    };

    const handleMutationSuccess = () => {
        setLoadingEventSubscription(false);
    };

    return (
        <>
            <div>
                Industry Event Email: {isUserSubscribed ? <span>Subscribed</span> : <span>Not Subscribed</span>}
                <ButtonComponent
                    onClick={toggleEventSubscription}
                    id="toggleEventSubscriptionButton"
                    className="cpBlueAltBtn ms-3"
                    buttonText={isUserSubscribed ? "Unsubscribe" : "Subscribe"}
                    disabled={loadingEventSubscription}>
                    {loadingEventSubscription && (
                        <>
                            <Loader inline loading />
                        </>
                    )}
                </ButtonComponent>
            </div>
        </>
    );
};

export default UserManagementDetailEmailSubscriptions;
