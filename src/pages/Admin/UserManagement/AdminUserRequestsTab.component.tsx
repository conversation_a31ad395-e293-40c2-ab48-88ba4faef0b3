import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import FlagIcon from "@mui/icons-material/Flag";
import FlagOutlinedIcon from "@mui/icons-material/FlagOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import useTheme from "@mui/material/styles/useTheme";
import {Dispatch, SetStateAction, useCallback, useMemo, useState} from "react";
import {Column} from "react-table";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";
import usePermissions from "../../../hooks/usePermissions";
import {IUserRequest} from "../../../Interfaces/companyUserManagement.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {IMeta} from "../../../Interfaces/pagination.interface";
import {companyUserManagementService} from "../../../services/companyUserManagement.service";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";
import CustomPagination from "../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import MenuButton from "../../../uicomponents/Molecules/MenuButton/menuButton.component";
import FlagRequestDialog from "../../../uicomponents/Organism/CompanyUserManagement/FlagRequestDialog.component";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {formatDate, FULL_DATETIME_SHORT_MONTH} from "../../../utils/formatDate";
import formatString, {CAPITALIZE_FIRST} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";

interface IProps {
    data?: IUserRequest[];
    page: number;
    setPage: Dispatch<SetStateAction<number>>;
    itemsPerPage: number;
    setItemsPerPage: Dispatch<SetStateAction<number>>;
    metadata?: IMeta;
    isLoading?: boolean;
    filters?: INewFilters;
    setFilters?: React.Dispatch<React.SetStateAction<INewFilters | undefined>>;
}

const AdminUserRequestsTab = ({
    data,
    page,
    setPage,
    itemsPerPage,
    setItemsPerPage,
    metadata,
    isLoading,
    filters,
    setFilters,
}: IProps) => {
    const theme = useTheme();
    const [requestToFlag, setRequestToFlag] = useState<IUserRequest>();
    const [unflagging, setUnflagging] = useState<Record<string, boolean>>({});
    const {invalidateMatchedQueries} = useQueryHelper();
    const {hasPermissions} = usePermissions();
    const hasRequestEditAccess = hasPermissions([PERMISSION_GROUPS.ADMIN_USER_COMPANY_REQUESTS_UPDATE]);
    const emptyValue = "--";

    const handleFlagUser = async (request: IUserRequest) => {
        setRequestToFlag(request);
    };

    const handleUnflagUser = useCallback(
        async (request: IUserRequest) => {
            const name = request.user.first_name + " " + request.user.last_name;
            const answer = await getUserConfirmation("Remove Flag from User?", {
                mui: true,
                modalTheme: "error",
                okButtonText: "YES",
                cancelButtonText: "CANCEL",
                title: "Remove Flag from User?",
                form: (
                    <Box display="flex" flexDirection="column" gap={2}>
                        <Typography fontInter variant="body3">
                            Are you sure you want to remove the flag{" "}
                            <strong>
                                {name} {request.user.email}
                            </strong>
                            ?
                        </Typography>
                    </Box>
                ),
            });
            if (!answer.value) return;
            setUnflagging({...unflagging, [request.id]: true});
            companyUserManagementService.updateFlagStatus(
                {
                    request_id: request.id || "",
                    red_flagged: false,
                },
                () => {
                    invalidateMatchedQueries(["user-company-requests"]);
                    setUnflagging({...unflagging, [request.id]: false});
                },
                () => {
                    setUnflagging({...unflagging, [request.id]: false});
                },
            );
        },
        [unflagging],
    );

    const handleSortChange = useCallback((sort, orderBy) => {
        setFilters?.({
            ...(filters || {}),
            sort: `sorting_${orderBy}__${sort}`,
        });
    }, []);

    const ActionsCell = useCallback(tableData => {
        const original = tableData.row.original;
        return original ? (
            <MenuButton
                id={`more-actions-${tableData.row.original.id}`}
                buttonVariant="iconButton"
                buttonProps={{
                    sx: {
                        width: "24px",
                        minWidth: "24px!important",
                        height: "24px",
                        boxShadow: "none",
                        flexShrink: 0,
                        position: "relative",
                        zIndex: 10,
                    },
                }}
                items={[
                    {
                        id: "edit-flag-message",
                        children: (
                            <>
                                <EditOutlinedIcon color="secondary" sx={{width: "16px", height: "16px"}} /> Edit Flag
                                Message
                            </>
                        ),
                        onClick: () => handleFlagUser(original),
                        disabled: !original.user.red_flagged,
                    },
                    // {
                    //     id: "reject",
                    //     children: (
                    //         <>
                    //             <BlockOutlinedIcon color="secondary" sx={{width: "16px", height: "16px"}} />
                    //             Block Email
                    //         </>
                    //     ),
                    //     onClick: () => {
                    //         handleDeclineUser(original);
                    //     },
                    // },
                ]}
                aria-label="settings">
                <MoreVert color="neutral" />
            </MenuButton>
        ) : (
            <></>
        );
    }, []);

    const columns = useMemo(
        () => [
            {
                Header: "Requested Date",
                accessor: "created_at",
                Cell: ({row}) => (
                    <Typography variant="body4">
                        {row.original.created_at
                            ? formatDate(row.original.created_at, FULL_DATETIME_SHORT_MONTH)
                            : emptyValue}
                    </Typography>
                ),
                sortable: true,
            },
            {
                Header: "Name",
                accessor: "name",
                Cell: ({row}) => (
                    <Typography variant="body4">
                        {`${row.original.user?.first_name} ${row.original.user?.last_name}` || emptyValue}
                    </Typography>
                ),
                sortable: true,
            },
            {
                Header: "Company",
                accessor: "company",
                Cell: ({row}) => <Typography variant="body4">{row.original.company?.name || emptyValue}</Typography>,
                sortable: true,
            },
            {
                Header: "Email",
                accessor: "email",
                Cell: ({row}) => <Typography variant="body4">{row.original.user?.email || emptyValue}</Typography>,
                sortable: true,
            },
            {
                Header: "Flag Notes",
                accessor: "flag_notes",
                Cell: ({row}) => (
                    <Box width={200} maxWidth={200} overflow="hidden" textOverflow="ellipsis">
                        <Typography
                            title={row.original.red_flag_reason}
                            variant="body4"
                            width="200px"
                            textOverflow="ellipsis">
                            {row.original.user?.red_flag_reason || emptyValue}
                        </Typography>
                    </Box>
                ),
                sortable: true,
            },
            {
                Header: "Status",
                accessor: "status",
                sortable: true,
                Cell: ({row}) => (
                    <Typography variant="body4">
                        {row.original.status ? formatString(row.original.status, CAPITALIZE_FIRST) : emptyValue}
                    </Typography>
                ),
            },
            {
                Header: "Flagged",
                accessor: "flag-user",
                Cell: ({row}) => {
                    const red_flagged = !!row.original.user?.red_flagged;
                    return (
                        <Box
                            title={red_flagged ? "Unflag User" : "Flag User"}
                            sx={{cursor: "pointer"}}
                            onClick={
                                unflagging[row.original.id] || !hasRequestEditAccess
                                    ? undefined
                                    : red_flagged
                                    ? () => handleUnflagUser(row.original)
                                    : () => handleFlagUser(row.original)
                            }>
                            {unflagging[row.original.id] ? (
                                <Loader inline loading />
                            ) : red_flagged ? (
                                <FlagIcon htmlColor={theme.palette.blue[600]} />
                            ) : (
                                <FlagOutlinedIcon htmlColor={theme.palette.blue[600]} />
                            )}
                        </Box>
                    );
                },
            },
            ...(hasRequestEditAccess
                ? [
                      {
                          Header: "Actions",
                          accessor: "_actions",
                          Cell: ActionsCell,
                      },
                  ]
                : []),
        ],
        [unflagging, hasRequestEditAccess],
    );

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" paddingTop={3}>
                <TableV2Component
                    id="users-requests-table"
                    columns={columns as Column<IUserRequest>[]}
                    customData={data || []}
                    headerBackground={theme.palette.blue[100]}
                    noResultsMessage="No user requests found."
                    isLoading={isLoading}
                    onSortChange={handleSortChange}
                    sortType="ASC"
                    orderBy="created_at"
                />
                {!!metadata && (
                    <CustomPagination
                        metadata={metadata}
                        page={page}
                        itemsPerPage={itemsPerPage}
                        setPage={setPage}
                        setItemsPerPage={setItemsPerPage}
                    />
                )}
            </Box>
            {!!requestToFlag && (
                <FlagRequestDialog request={requestToFlag} open handleClose={() => setRequestToFlag(undefined)} />
            )}
        </ErrorBoundary>
    );
};

export default AdminUserRequestsTab;
