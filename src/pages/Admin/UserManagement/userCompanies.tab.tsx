import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {useState} from "react";
import {Link, useParams} from "react-router-dom";
import {Column} from "react-table";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {userService} from "../../../services/user.service";
import CustomPagination from "../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import {clearFilters} from "../../../utils/filters.util";
import {DATE_FORMAT, formatDate} from "../../../utils/formatDate";

interface IUserCompany {
    id: string;
    name: string;
    friendly_url: string;
    company_type: string;
    title: string;
    display_name: string | null;
    created_at: string;
}
type TUserCompaniesAPIResponse = IPaginationData<IUserCompany[]>;

interface IProps {
    onCompaniesFetch: (companies: TUserCompaniesAPIResponse) => void;
}

export default function UserCompaniesTab({onCompaniesFetch}: IProps) {
    const theme = useTheme();
    const {id} = useParams();
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(40);
    const [filterContents, setFilterContents] = useState<INewFilters>({sort: "sorting_companies.name__ASC"});

    const userCompaniesQuery = useQuery<TUserCompaniesAPIResponse>(
        ["userCompanies", id, page, itemsPerPage, JSON.stringify(filterContents)],
        async () => {
            return new Promise<TUserCompaniesAPIResponse>(resolve => {
                userService
                    .getUserCompanies({
                        page,
                        items_per_page: itemsPerPage,
                        dynamic: clearFilters({
                            ...filterContents,
                            id: id || "",
                        }),
                    })
                    .then(res => {
                        const userCompanies = res.data.data;
                        onCompaniesFetch({...res.data, data: userCompanies});
                        resolve({...res.data, data: userCompanies});
                    });
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS({
                refetch: {
                    onMount: true,
                },
            }),
        },
    );
    const companiesData = userCompaniesQuery.data?.data || [];
    const metadata = userCompaniesQuery.data?.meta || {total: 0, last_page: 0};

    const columns: Column<IUserCompany>[] = [
        {
            Header: "Company Name",
            id: "companies.name",
            accessor: "name",
            //:company_id/:tab
            Cell: ({row, value}) => (
                <Link
                    to={routeConfig.CompanyManagementDetail.path
                        .replace(":company_id", row.original.id)
                        .replace(":tab", "summary")}
                    style={{color: theme.palette.blue[600], fontWeight: 600, display: "flex", gap: 2}}>
                    {value}
                </Link>
            ),
            defaultCanSort: true,
        },
        {
            Header: "Company Type",
            id: "company_types.label",
            accessor: "company_type",
            defaultCanSort: true,
        },
        {
            Header: "Role",
            accessor: row => row.display_name || row.title,
            id: "roles.display_name",
            defaultCanSort: true,
        },
        {
            Header: "Authorized Date",
            accessor: "created_at",
            id: "roles_users.created_at",
            Cell: ({value}) => (value ? formatDate(value, DATE_FORMAT) : ""),
            defaultCanSort: true,
        },
    ];

    const handleSortChange = (sortBy: string, order_by: string) => {
        setFilterContents({...filterContents, sort: `sorting_${order_by}__${sortBy}`});
    };

    return (
        <>
            <TableV2Component
                id="user-companies-table"
                columns={columns}
                customData={companiesData}
                isLoading={userCompaniesQuery.isLoading}
                headerBackground={theme.palette.blue[100]}
                onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                orderBy={"companies.name" as any}
                sortType="ASC"
            />
            <CustomPagination
                page={page}
                setPage={setPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                metadata={metadata}
            />
        </>
    );
}
