import {useTheme} from "@mui/material";
import {Column} from "react-table";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import NotificationToggle from "./notificationToggle.component";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";

interface IProps {
    tableId: string;
    userId: string;
    customData: INotificationTableData[];
    toggleHandler: (id: string) => Promise<any>;
}

export interface INotificationTableData {
    email: boolean | "disabled";
    inApp: boolean | "disabled";
    name: string;
}

export default function NotificationTable({tableId, userId, customData, toggleHandler}: IProps) {
    const theme = useTheme();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.ADMIN_USER_MNGMT_UPDATE]);
    const ToggleCell = ({row}, {key}) => {
        const value = row.original[key];
        const id = row.original.name.replaceAll(" ", "_");
        const checked = typeof value === "boolean" ? value : false;
        const disabled = value === "disabled";
        return (
            <NotificationToggle
                id={id}
                defaultChecked={checked}
                promiseHandler={toggleHandler}
                userId={userId}
                disabled={disabled || !hasEditAccess}
            />
        );
    };
    const columns: Column<INotificationTableData>[] = [
        {
            id: "email",
            Header: "Email",
            accessor: "email",
            Cell: data => ToggleCell(data, {key: "email"}),
        },
        {
            id: "inApp",
            Header: "In App",
            accessor: "inApp",
            Cell: data => ToggleCell(data, {key: "inApp"}),
        },
        {
            id: "description",
            Header: "Description",
            accessor: "name",
        },
    ];
    return (
        <TableV2Component
            id={tableId}
            columns={columns}
            customData={customData}
            headerBackground={theme.palette.secondary[100]}
        />
    );
}
