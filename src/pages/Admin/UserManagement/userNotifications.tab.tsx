import {useTheme} from "@mui/material";
import {AxiosError} from "axios";
import {useState} from "react";
import {useParams} from "react-router-dom";
import useNotification from "../../../hooks/useNotification";
import {userService} from "../../../services/user.service";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import MenuCollapsible from "../../../uicomponents/Atoms/Collapse/MenuCollapsible.component";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import NotificationTable, {INotificationTableData} from "./notificationTable.component";
import {eventSubscriptionConfirmationConfig} from "./toggleHandlers.util";
import {IAdminUserDetail} from "./userManagementDetail.page";

interface IProps {
    userDetails?: IAdminUserDetail;
}

export default function UserNotificationsTab({userDetails}: IProps) {
    const [isUserSubscribed, setIsUserSubscribed] = useState<boolean>(
        !!userDetails?.email_subscriptions?.event_email_subscribed,
    );
    const theme = useTheme();
    const {id} = useParams();
    const notify = useNotification();

    const standardPromise = new Promise(resolve => resolve(isUserSubscribed));

    //! In the future we need to have a better logic to handle multiple notifications
    const yourContentData: INotificationTableData[] = [
        {
            email: isUserSubscribed,
            inApp: "disabled",
            name: "Upcoming event notification",
        },
    ];

    const toggleHandler = async (key: string) => {
        switch (key.toLowerCase()) {
            case "upcoming_event_notification":
                if (isUserSubscribed && userDetails) {
                    const answer = await getUserConfirmation("", eventSubscriptionConfirmationConfig(userDetails));
                    if (!answer.value) return standardPromise;
                }
                return userService
                    .updateUser({id, event_email_subscribed: !isUserSubscribed})
                    ?.then(() => {
                        setIsUserSubscribed(!isUserSubscribed);
                        return !isUserSubscribed;
                    })
                    .catch(handleError);
            default:
                return new Promise(resolve => {
                    resolve(isUserSubscribed);
                });
        }
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    return (
        <Box
            sx={{
                display: "grid",
                gap: 3,
                gridTemplateColumns: "1fr 1fr",
            }}>
            <MenuCollapsible
                title="Your Content"
                defaultOpen
                titleColor={theme.palette.secondary.main}
                wrapperSx={{
                    h6: {
                        fontWeight: "500",
                        fontSize: "18px",
                        color: theme.palette.neutral[700],
                    },
                    svg: {
                        color: theme.palette.secondary.main,
                    },
                    borderBottom: "1px solid",
                    borderColor: theme.palette.neutral[300],
                    marginBottom: 3,
                }}
                sx={{
                    padding: "0 0 10px",
                    background: "none",
                }}>
                <NotificationTable
                    customData={yourContentData}
                    tableId="your-content"
                    userId={id || ""}
                    toggleHandler={toggleHandler}
                />
            </MenuCollapsible>
        </Box>
    );
}
