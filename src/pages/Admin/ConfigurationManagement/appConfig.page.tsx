import {useQueryClient} from "@tanstack/react-query";
import {AxiosError} from "axios";
import {useCallback, useState} from "react";
import {IAppConfigData} from "../../../Interfaces/appConfig.interface";
import ButtonComponent from "../../../components/FormControls/button.component";
import {EditIcon, XIcon} from "../../../components/Icons";
import {ModalComponent} from "../../../components/Modal";
import {TableComponent} from "../../../components/Table/table.component";
import TitleContainer from "../../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import {ModifyFormsType} from "../../../enums/modifyFormsType.enum";
import useNotification from "../../../hooks/useNotification";
import {appConfigService} from "../../../services/appConfig.service";
import styles from "../../../styles/pages/companyManagement.module.sass";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {AppConfigForm} from "./appConfig.modal";

export default function AppConfigPage() {
    const [newAppConfigModal, setNewAppConfigModal] = useState(false);
    const [currentRecord, setCurrentRecord] = useState<IAppConfigData>();
    const [modifyType, setModifyType] = useState<ModifyFormsType>(ModifyFormsType.Add);
    const [isSending, setIsSending] = useState(false);

    const queryClient = useQueryClient();
    const notify = useNotification();

    const handleEdit = (data: IAppConfigData) => {
        setCurrentRecord(data);

        setModifyType(ModifyFormsType.Edit);
        setNewAppConfigModal(true);
    };

    const handleDelete = async (key: string) => {
        const answer = await getUserConfirmation("Are you sure you want to delete this config?");
        if (answer.value) {
            setModifyType(ModifyFormsType.Delete);
            setIsSending(true);
            appConfigService.delete(key, () => handleDeleteConfigSaved(), handleError);
        }
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setIsSending(false);
    };

    const handleAddAppConfig = () => {
        // bring up modal
        setModifyType(ModifyFormsType.Add);
        setNewAppConfigModal(true);
    };

    const handleCancelAppConfigModal = () => {
        setNewAppConfigModal(false);
    };

    const handleAppConfigSaved = () => {
        queryClient.removeQueries(["table-appConfigTable"]);
        setNewAppConfigModal(false);
        setModifyType(ModifyFormsType.Add);
    };

    const handleDeleteConfigSaved = () => {
        queryClient.removeQueries(["table-appConfigTable"]);
        setModifyType(ModifyFormsType.Add);
    };

    const ViewCell = useCallback(
        tableData => {
            const optionId: string = tableData.row.values.key;
            return (
                <>
                    <div>
                        <ButtonComponent
                            type="button"
                            tooltip="Edit"
                            id={optionId}
                            disabled={isSending}
                            className="cpBlueBtnThin white"
                            onClick={() => handleEdit(tableData.row.values)}>
                            <EditIcon size={12} />
                        </ButtonComponent>
                        &nbsp;
                        <ButtonComponent
                            type="button"
                            tooltip="Delete"
                            id={optionId}
                            disabled={isSending}
                            className="cpBlueBtnThin white"
                            onClick={() => handleDelete(optionId)}>
                            <div className="text-center">
                                <XIcon size={12} className="ms-2" />
                            </div>
                        </ButtonComponent>
                    </div>
                </>
            );
        },
        //eslint-disable-next-line
        [],
    );

    const columns: any[] = [
        {Header: "Manage", accessor: "", Cell: ViewCell},
        {Header: "Key", accessor: "key"},
        {Header: "Value", accessor: "value"},
    ];

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <TitleContainer as="h1" position="start" text={routeConfig.AppConfiguration.name} className="mxx-2" />
                <div className="mb-4">
                    <ButtonComponent
                        id="addAppConfig"
                        type="button"
                        className={styles.addButton}
                        onClick={handleAddAppConfig}>
                        Add App Config
                    </ButtonComponent>
                </div>

                <TableComponent
                    id="appConfigTable"
                    columns={columns}
                    hiddenColumns={["id"]}
                    serviceHandler={appConfigService.getAllAppConfigs}
                />

                {newAppConfigModal && (
                    <ModalComponent
                        isStatic
                        showHeader
                        headerTitle={(modifyType === ModifyFormsType.Add ? "Add" : "Edit") + " Application Job"}
                        onCancel={handleCancelAppConfigModal}
                        modalSwitcher={newAppConfigModal}
                        modalSwitcherCallback={setNewAppConfigModal}
                        showCancelButton={false}
                        showOkButton={false}
                        form={
                            <AppConfigForm
                                onSuccess={handleAppConfigSaved}
                                type={modifyType}
                                defaultValues={currentRecord}
                            />
                        }
                    />
                )}
            </div>
        </div>
    );
}
