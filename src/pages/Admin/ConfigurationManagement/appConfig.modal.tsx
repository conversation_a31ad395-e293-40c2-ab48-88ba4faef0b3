import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IAppConfigData} from "../../../Interfaces/appConfig.interface";
import ButtonComponent from "../../../components/FormControls/button.component";
import TextBoxComponent from "../../../components/FormControls/textBox.component";
import {ModifyFormsType} from "../../../enums/modifyFormsType.enum";
import useNotification from "../../../hooks/useNotification";
import {appConfigService} from "../../../services/appConfig.service";
import styles from "../../../styles/components/registerForm.module.sass";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";

interface IProps {
    onSuccess: Function;
    type: ModifyFormsType;
    defaultValues?: IAppConfigData;
}

export const AppConfigForm = (props: IProps) => {
    const [loading, setLoading] = useState<boolean>(false);
    const methodsAddAppConfig = useForm();
    const notify = useNotification();

    useEffect(() => {
        if (props.defaultValues && props.type === ModifyFormsType.Edit) {
            methodsAddAppConfig.setValue("key", props.defaultValues.key);
            methodsAddAppConfig.setValue("value", props.defaultValues.value);
        }
        // eslint-disable-next-line
    }, []);

    const handleError = (error: AxiosError<any>) => {
        const errMsg: string = getErrorFromArray(error);
        notify(errMsg, "Error");

        setLoading(false);
    };

    const handleSaveAppConfig = () => {
        setLoading(true);

        const keyChosen: string = methodsAddAppConfig.getValues("key");
        const valueChosen: string = methodsAddAppConfig.getValues("value");

        if (props.type === ModifyFormsType.Add) {
            appConfigService.create(keyChosen, valueChosen, handleSuccessAppConfig, handleError);
        } else {
            appConfigService.update(keyChosen, valueChosen, handleSuccessAppConfig, handleError);
        }
    };

    const handleSuccessAppConfig = (response: any) => {
        setLoading(false);

        props.onSuccess(response.data);
    };

    return (
        <div className={styles.formContainer}>
            <FormProvider {...methodsAddAppConfig}>
                <form onSubmit={methodsAddAppConfig.handleSubmit(() => handleSaveAppConfig())}>
                    <TextBoxComponent
                        className="mb-1 col-12"
                        containerClassName={styles.contactInputLeft}
                        id="key"
                        label="Key"
                        isRequired
                        validationSchema={v => v.trim() !== ""}
                        autoFocus={props.type === ModifyFormsType.Add}
                        disabled={props.type === ModifyFormsType.Edit}
                    />
                    <TextBoxComponent
                        className="mb-1 col-12"
                        containerClassName={styles.contactInputLeft}
                        id="value"
                        label="Value"
                        autoFocus={props.type === ModifyFormsType.Edit}
                    />

                    <div className="d-flex justify-content-center mt-4 flex-wrap">
                        <ButtonComponent
                            type="submit"
                            id="saveNewAppConfig"
                            className="cpBlueBtn order-1 order-md-2"
                            buttonText="Save">
                            {loading && <Loader inline version="iconWhite" loading />}
                        </ButtonComponent>
                    </div>
                </form>
            </FormProvider>
        </div>
    );
};
