import {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IFeatureFlagData} from "../../../Interfaces/featureFlag.interface";
import ButtonComponent from "../../../components/FormControls/button.component";
import CheckBoxComponent from "../../../components/FormControls/checkBox.component";
import TextBoxComponent from "../../../components/FormControls/textBox.component";
import {ModifyFormsType} from "../../../enums/modifyFormsType.enum";
import useNotification from "../../../hooks/useNotification";
import {featureFlagService} from "../../../services/featureFlag.service";
import styles from "../../../styles/components/registerForm.module.sass";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";

interface IProps {
    onSuccess: Function;
    type: ModifyFormsType;
    defaultValues?: IFeatureFlagData;
}

export const FeatureFlagForm = (props: IProps) => {
    const [loading, setLoading] = useState<boolean>(false);
    const methodsAddFeatureFlag = useForm();
    const notify = useNotification();

    useEffect(() => {
        if (props.defaultValues && props.type === ModifyFormsType.Edit) {
            methodsAddFeatureFlag.setValue("key", props.defaultValues.name);
            methodsAddFeatureFlag.setValue("activated", props.defaultValues.activated);
        }
        // eslint-disable-next-line
    }, []);

    const handleError = (error: AxiosError<any>) => {
        const errMsg: string = getErrorFromArray(error);
        notify(errMsg, "Error");

        setLoading(false);
    };

    const handleSaveFeatureFlag = () => {
        setLoading(true);

        const keyChosen: string = methodsAddFeatureFlag.getValues("name");
        const valueChosen: boolean = methodsAddFeatureFlag.getValues("activated");

        if (props.type === ModifyFormsType.Add) {
            featureFlagService.create(keyChosen, valueChosen, handleSuccessFeatureFlag, handleError);
        }
    };

    const handleSuccessFeatureFlag = (response: any) => {
        setLoading(false);

        props.onSuccess(response.data);
    };

    return (
        <div className={styles.formContainer}>
            <FormProvider {...methodsAddFeatureFlag}>
                <form onSubmit={methodsAddFeatureFlag.handleSubmit(() => handleSaveFeatureFlag())}>
                    <TextBoxComponent
                        className="mb-1 col-12"
                        containerClassName={styles.contactInputLeft}
                        id="name"
                        label="Name"
                        isRequired
                        validationSchema={v => v.trim() !== ""}
                        autoFocus={props.type === ModifyFormsType.Add}
                        disabled={props.type === ModifyFormsType.Edit}
                    />

                    <CheckBoxComponent name="activated" id="activated">
                        Activate?
                    </CheckBoxComponent>

                    <div className="d-flex justify-content-center mt-4 flex-wrap">
                        <ButtonComponent
                            type="submit"
                            id="saveNewFeatureFlag"
                            className="cpBlueBtn order-1 order-md-2"
                            buttonText="Save">
                            {loading && <Loader inline version="iconWhite" loading />}
                        </ButtonComponent>
                    </div>
                </form>
            </FormProvider>
        </div>
    );
};
