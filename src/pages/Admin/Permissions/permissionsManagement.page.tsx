import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ErrorIcon from "@mui/icons-material/Error";
import WarningIcon from "@mui/icons-material/Warning";
import {useNavigate} from "react-router-dom";
import {DEFAULT_QUERY_CONFIGS, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import MuiButtonComponent from "../../../uicomponents/Buttons/MuiButtonComponent/MuiButton.component";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {Column} from "react-table";
import {IPermission} from "../../../Interfaces/permissions.interface";
import {NestedObject} from "../../../models";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {useState} from "react";
import {rolesService} from "../../../services/roles.service";
import {AxiosResponse} from "axios";
import CustomPagination from "../../../uicomponents/Molecules/CustomPagination/CustomPagination.component";
import {useQuery} from "@tanstack/react-query";
import {useDebounce} from "../../../hooks/useDebounce";
import useFilters from "../../../hooks/fetches/useFilters";
import {clearFilters} from "../../../utils/filters.util";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../../uicomponents/Atoms/Typography/Typography.component";

type IPermissionRow = NestedObject<IPermission & {name: string}>;

export default function PermissionsManagementPage() {
    const navigate = useNavigate();
    const [itemsPerPage, setItemsPerPage] = useState<number>(40);
    const [search, setSearch] = useState<string>("");
    const {filterState, filtersQuery} = useFilters({
        filterKey: "permissionFilters",
    });
    const [filterContents, setFilterContents] = filterState;
    const debouncedSearch = useDebounce(search, 500);
    const debouncedFilter = useDebounce(filterContents, 500);
    const [page, setPage] = useState(1);
    const columns: Column<IPermissionRow>[] = [
        {
            Header: "Status",
            accessor: "is_active",
            Cell: ({row}) => (
                <>
                    {(row.original.is_active as boolean) ? (
                        "Assigned"
                    ) : (
                        <Box
                            tooltip={{
                                title: "The private API route has not been assigned to any permission group.",
                            }}>
                            Unassigned
                            {row.original.is_private && (
                                <WarningIcon sx={{color: "orange", marginLeft: "5px", width: "20px"}} />
                            )}
                        </Box>
                    )}
                </>
            ),
            defaultCanSort: true,
        },
        {
            Header: "Visibility",
            accessor: "is_private",
            Cell: ({value}) => <>{(value as boolean) ? "Private" : "Public"}</>,
            defaultCanSort: true,
        },
        {
            Header: "Title (API route)",
            accessor: "title",
            Cell: ({row}) => (
                <>
                    {row.original.title}
                    {!(row.original.dont_fix as boolean) && (
                        <Box
                            tooltip={{
                                title: "The API route is missing the method type, please fix in the permissions table.",
                            }}>
                            <ErrorIcon sx={{color: "red", marginLeft: "5px", width: "20px"}} />
                        </Box>
                    )}
                </>
            ),
            defaultCanSort: true,
        },
        {Header: "Description", accessor: "description", defaultCanSort: true},
        {
            Header: "Created at",
            accessor: "created_at",
            Cell: ({value}) => <>{value !== null ? value : "--"}</>,
            defaultCanSort: true,
        },
    ];

    const handleSortChange = (sortType: string, order_by: string) => {
        setFilterContents(prev => ({...prev, sort: `sorting_${order_by}__${sortType}`}));
    };

    const permissionsQuery = useQuery<IPaginationData<IPermission[]>>(
        ["admin-permissions", page, itemsPerPage, debouncedSearch, JSON.stringify(debouncedFilter)],
        () => {
            return new Promise((resolve, reject) =>
                rolesService
                    .getPermissions({
                        page,
                        items_per_page: itemsPerPage,
                        search_word: debouncedSearch,
                        dynamic: clearFilters(filterContents),
                    })
                    .then((res: AxiosResponse<IPaginationData<IPermission[]>>) => resolve(res.data))
                    .catch(reject),
            );
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const legend = (
        <Box className={`d-flex flex-row align-items-center flex-wrap`}>
            <Box
                className="d-flex flex-row align-items-center"
                tooltip={{
                    title: "The API route is missing the method type, please fix in the permissions table.",
                }}>
                <ErrorIcon sx={{color: "red", marginLeft: "5px", width: "20px"}} />
                <Typography variant="body4" letterSpacing={0} color="#000">
                    Needs immediate attention
                </Typography>
            </Box>
            <Box
                className="d-flex flex-row align-items-center mx-3"
                tooltip={{
                    title: "The private API route has not been assigned to any permission group.",
                }}>
                <WarningIcon sx={{color: "orange", marginLeft: "5px", width: "20px"}} />
                <Typography variant="body4" letterSpacing={0} color="#000">
                    Validate if route needs to be assigned to a permission group
                </Typography>
            </Box>
        </Box>
    );

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <div className="d-flex flex-column justify-content-start align-items-start">
                    <MuiButtonComponent
                        style={{color: "black", border: "none"}}
                        color="primary"
                        id="back"
                        variant="text"
                        size={"xl"}
                        type="button"
                        onClick={() => {
                            navigate(-1);
                        }}>
                        <ArrowBackIosIcon className="me-4" fontSize="small" /> Back
                    </MuiButtonComponent>
                </div>
                <PageInnerHeader
                    id="permissions-management-header"
                    searchState={[search, setSearch]}
                    filterState={filterState}
                    filters={filtersQuery.data}
                    filterKeysOrder={["created_at", "is_active", "is_private", "title", "description"]}
                    title={{
                        text: "API Permissions",
                        sx: {
                            fontSize: "24px!important",
                            fontWeight: "500!important",
                        },
                    }}
                />
                {legend}
                <TableV2Component
                    id="permissions-management-table"
                    columns={columns}
                    customData={(permissionsQuery.data?.data || []) as IPermissionRow[]}
                    isLoading={permissionsQuery.isLoading}
                    noResultsMessage="No API permissions found."
                    onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                />
                <CustomPagination
                    metadata={permissionsQuery.data?.meta || {total: 0, last_page: 1}}
                    page={page}
                    itemsPerPage={itemsPerPage}
                    setPage={setPage}
                    setItemsPerPage={setItemsPerPage}
                />
            </div>
        </div>
    );
}
