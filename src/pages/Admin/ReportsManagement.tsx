import builder, {BuilderComponent} from "@builder.io/react";
import {useEffect, useState} from "react";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";

export default function ReportsManagement() {
    const [builderContent<PERSON><PERSON>, setBuilderContentJson] = useState<any | null>(null);

    useEffect(() => {
        builder
            .get("main-pages", {url: "/admin/reports"})
            .promise()
            .then(res => {
                setBuilderContentJson(res);
            });
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <BuilderComponent model="main-pages" content={builderContentJson} />
            </div>
        </div>
    );
}
