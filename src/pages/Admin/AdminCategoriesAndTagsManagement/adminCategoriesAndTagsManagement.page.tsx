import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import {useQuery} from "@tanstack/react-query";
import {useRef, useState} from "react";
import {DEFAULT_QUERY_CONFIGS, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import {ICategory} from "../../../Interfaces/categories.interface";
import {ITag} from "../../../Interfaces/tags.interface";
import {categoriesService} from "../../../services/category.service";
import {tagsService} from "../../../services/tags.service";
import Badge from "../../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../../uicomponents/Atoms/Box/Box.component";
import BasicTabs from "../../../uicomponents/Molecules/Tabs/tabs.component";
import AdminCategoriesManagementTab from "../../../uicomponents/Organism/AdminCategoriesAndTagsManagement/AdminCategoriesManagmentTab.component";
import AdminTagsManagementTab from "../../../uicomponents/Organism/AdminCategoriesAndTagsManagement/AdminTagsManagmentTab.component";
import UpsertCategoryDialog from "../../../uicomponents/Organism/AdminCategoriesAndTagsManagement/UpsertCategoryDrawer.component";
import UpsertTagDrawer from "../../../uicomponents/Organism/AdminCategoriesAndTagsManagement/UpsertTagDrawer.component";
import PageInnerHeader from "../../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import ErrorBoundary from "../../../utils/errorBoundary.util";

const AdminCategoriesAndTagsManagement = () => {
    const [showCategoryDrawer, setShowCategoryDrawer] = useState<boolean>(false);
    const [showTagDrawer, setShowTagDrawer] = useState<boolean>(false);
    const currentTab = useRef<"categories" | "tags">("categories");

    const all_categories = useQuery<ICategory[], Error>(
        ["admin-all-categories"],
        () => categoriesService.getAllAdmin().then(res => res.data),
        DEFAULT_QUERY_CONFIGS(),
    );
    const parent_categories_count = all_categories?.data?.filter(cat => !cat.parent_id)?.length || 0;

    const all_tags = useQuery<ITag[]>(["all-admin-tags"], () => tagsService.search({}).then(res => res.data));
    const tags_count = all_tags?.data?.length || 0;

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={3} className={ROUTE_CLASS}>
                <Box className={ROUTE_CONTAINER_CLASS} width="100%">
                    <PageInnerHeader
                        id="admin-manage-tags-categories-header"
                        title={{text: "Categories Management"}}
                        actions={{
                            primary: {
                                id: "add-new-category-tag",
                                children: (
                                    <>
                                        <AddOutlinedIcon />
                                        Add New
                                    </>
                                ),
                                onClick: () => {
                                    currentTab.current === "categories" && setShowCategoryDrawer(true);
                                    currentTab.current === "tags" && setShowTagDrawer(true);
                                },
                            },
                        }}
                    />
                    <BasicTabs
                        tabs={[
                            {
                                id: "categories",
                                label: (
                                    <Box display="flex" flexDirection="row" gap={1} alignItems="center">
                                        Categories <Badge color="blue">{parent_categories_count}</Badge>
                                    </Box>
                                ),
                                Component: <AdminCategoriesManagementTab />,
                            },
                            {
                                id: "tags",
                                label: (
                                    <Box display="flex" flexDirection="row" gap={1} alignItems="center">
                                        Tags <Badge color="blue">{tags_count}</Badge>
                                    </Box>
                                ),
                                Component: <AdminTagsManagementTab />,
                            },
                        ]}
                        onBeforeChange={newTab => {
                            currentTab.current = newTab?.id;
                            return true;
                        }}
                    />
                </Box>
            </Box>
            {showCategoryDrawer && (
                <UpsertCategoryDialog open={showCategoryDrawer} onClose={() => setShowCategoryDrawer(false)} />
            )}
            {showTagDrawer && <UpsertTagDrawer open={showTagDrawer} onClose={() => setShowTagDrawer(false)} />}
        </ErrorBoundary>
    );
};

export default AdminCategoriesAndTagsManagement;
