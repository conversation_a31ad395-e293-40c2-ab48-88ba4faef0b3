import Box from "../../../../uicomponents/Atoms/Box/Box.component";
import ModalComponent from "../../../../uicomponents/Molecules/Modal/Modal.component";
import {useTheme} from "@mui/material";
import AlertBanner from "../../../../uicomponents/Molecules/AlertBanner/alertBanner.component";
import routeConfig from "../../../../constants/routeConfig";
import {IProducts} from "../../../../Interfaces/products.interface";
import CustomLink from "../../../../components/Links/CustomLink.component";

export interface IUserContentProductApprovedModalProps {
    product?: IProducts;
    open?: boolean;
    onClose?: () => void;
}

const UserContentProductApprovedModal = ({product, open = false, onClose}: IUserContentProductApprovedModalProps) => {
    const theme = useTheme();

    return (
        <ModalComponent
            id="user-content-product-approved"
            justifyButtons="flex-start"
            open={open}
            onClose={onClose}
            title={"View New Product"}
            contentClassName="mt-0"
            modalTheme="success"
            maxWidth="md"
            dialogSx={{
                ".MuiDialog-paper": {
                    overflow: "auto",
                    maxWidth: "800px !important",
                    maxHeight: "100%",
                    width: "100%",
                },
                ".MuiDialogContent-root": {
                    overflow: "auto",
                    gap: 2,
                },
            }}
            showOkButton={false}
            showCancelButton={true}
            cancelButtonText="CLOSE"
            content={
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                        flexWrap: "wrap",
                    }}>
                    <AlertBanner
                        id={`alert-banner-company-pending`}
                        variant={"success"}
                        title={{text: "Product approved", color: theme.palette.neutral[800]}}
                        iconSx={{
                            "& svg": {
                                color: theme.palette.success[500],
                            },
                        }}
                        onClose={onClose}
                        subTitle={{
                            text: (
                                <Box sx={{}}>
                                    It is now added to {product?.company?.name} company profile.
                                    <CustomLink
                                        id="review-company-btn"
                                        target="_blank"
                                        to={routeConfig.ProductDetails.path.replace(
                                            ":friendly_url",
                                            product?.friendly_url ?? "",
                                        )}
                                        style={{
                                            color: theme.palette.blue[600],
                                            fontWeight: 700,
                                            fontSize: "16px",
                                            lineHeight: "130%",
                                            cursor: "pointer",
                                            padding: "0 6px",
                                        }}>
                                        Open product page
                                    </CustomLink>
                                </Box>
                            ),
                        }}
                    />
                </Box>
            }
        />
    );
};

export default UserContentProductApprovedModal;
