import {Form<PERSON>rovider, useForm} from "react-hook-form";
import {use<PERSON><PERSON>back, useEffect, useMemo, useState} from "react";
import DeclineIcon from "@mui/icons-material/Close";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import {
    IRejectUserContentForm,
    IRejectUserContentFormResponse,
    StatusScopeDuplicateOptionKey,
    StatusScopeOtherOptionKey,
    TStatusScopeEntityTypes,
    TStatusScopeRejectOption,
} from "../../../../Interfaces/statusScope.interface";
import {AxiosError, AxiosResponse} from "axios";
import {Box} from "@mui/material";
import {genericErrorString} from "../../../../utils/error.util";
import useNotification from "../../../../hooks/useNotification";
import ModalComponent from "../../../../uicomponents/Molecules/Modal/Modal.component";
import MuiSelect from "../../../../uicomponents/Molecules/MuiSelect/MuiSelect.component";
import {userContentService} from "../../../../services/userContent.service";
import {useQuery} from "@tanstack/react-query";
import {DEFAULT_QUERY_CONFIGS} from "../../../../constants/commonStrings.constant";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../../uicomponents/FormControls/TextBox/textBox.component";
import DuplicateProductSearch from "./DuplicateProductSearch.component";
import DuplicateCompanySearch from "./DuplicateCompanySearch.component";
import Loader from "../../../../utils/loader";

export interface IRejectUserContentEntity {
    id: string;
    name: string;
}
interface IStatusReasonOption {
    id: string;
    name: string;
}
export interface IDeclineUserContentConfirmationProps {
    entityType: TStatusScopeEntityTypes;
    entities: IRejectUserContentEntity[];
    defaultStatusReasonKey?: string;
    onConfirm: (entityIDs: string[]) => void;
    onClose?: () => void;
    title?: string;
    open?: boolean;
}

const DeclineUserContentConfirmation = ({
    entityType,
    entities,
    defaultStatusReasonKey,
    onConfirm,
    onClose,
    title = "Decline Entity?",
    open = false,
}: IDeclineUserContentConfirmationProps) => {
    const notify = useNotification();
    const methods = useForm<IRejectUserContentForm>({
        defaultValues: {
            entity: entityType,
            entity_ids: entities.map(entity => entity.id),
            status_reason_key: defaultStatusReasonKey ?? "",
        },
    });
    const {
        watch,
        setValue,
        unregister,
        formState: {errors},
    } = methods;
    const selectedStatusReasonOption = watch("status_reason_key");
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [showOtherDescription, setShowOtherDescription] = useState<boolean>(false);
    const markAsDuplicate = defaultStatusReasonKey === StatusScopeDuplicateOptionKey[entityType];

    /**
     * Returns a default search string based on the most common entity names.
     * - If there's only one entity, its name is returned.
     * - Otherwise, it collects unique, lowercase names and counts their occurrences.
     * - Returns the most frequent name(s) joined by a space.
     */
    const getDefaultSearch = useCallback(() => {
        if (entities.length === 1) {
            return entities[0].name;
        }
        const names: string[] = [];
        entities
            .filter(entity => !!entity.name)
            .forEach(entity => {
                const name = entity.name.toLowerCase();
                if (!names.includes(name)) {
                    names.push(name);
                }
            });
        const terms: {value: string; count: number}[] = [];
        let maxCount = 0;
        names.forEach(name => {
            let termIndex = terms.findIndex(item => item.value === name);
            if (termIndex < 0) {
                termIndex = terms.length;
                terms[termIndex] = {value: name, count: 0};
            }
            terms[termIndex].count++;
            maxCount = Math.max(maxCount, terms[termIndex].count);
        });
        const response: string[] = [];
        terms
            .filter(term => term.count === maxCount)
            .map(item => item.value)
            .join(" ")
            .split(" ")
            .filter(word => word.length >= 2)
            .forEach(word => {
                if (!response.includes(word)) {
                    response.push(word);
                }
            });

        return response.slice(0, 5).join(" ");
    }, [entities]);
    const defaultSearch = getDefaultSearch();

    const userContentRejectOptionsQuery = useQuery<TStatusScopeRejectOption[]>(
        [`admin-user-content-reject-options-${entityType}`],
        async () => {
            return new Promise<TStatusScopeRejectOption[]>((resolve, reject) => {
                userContentService.getAdminUserContentRejectOptions(
                    entityType,
                    (res: AxiosResponse<TStatusScopeRejectOption[]>) => {
                        resolve(res.data);
                    },
                    err => {
                        reject(err);
                    },
                );
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!entityType,
        },
    );
    const {data} = userContentRejectOptionsQuery;

    const selectedReasonOptionObj = useMemo<IStatusReasonOption[]>(() => {
        const selectedItem = (data ?? []).find(item => item.value_key === selectedStatusReasonOption);
        return !!selectedItem ? [{id: selectedItem.value_key, name: selectedItem.name}] : [];
    }, [data, selectedStatusReasonOption]);

    const handleStatusReasonOptionChange = (selected: IStatusReasonOption[]) => {
        setValue("status_reason_key", selected[0]?.id ?? "");
        const isOther = selected[0]?.id === StatusScopeOtherOptionKey[entityType];
        const isDuplicate = selected[0]?.id === StatusScopeDuplicateOptionKey[entityType];
        setShowOtherDescription(isOther);
        if (!isOther) {
            unregister("reason_description");
        }
        if (!isDuplicate) {
            unregister("duplicate_id", undefined);
        }
    };

    const handleConfirm = methods.handleSubmit((values: IRejectUserContentForm) => {
        setIsSaving(true);
        userContentService
            .adminRejectUserContent(values)
            .then((data: AxiosResponse<IRejectUserContentFormResponse>) => {
                const entityName = entityType === "company" ? "Company" : "Product";
                const message = !!markAsDuplicate ? "marked as dupicate" : "declined";
                notify(`${entityName} ${message} successfully`, "Success");
                onConfirm?.(data.data.entity_ids);
                onClose?.();
            })
            .catch((error: AxiosError<any>) => {
                notify(genericErrorString(error), "Error");
            })
            .finally(() => setIsSaving(false));
    });

    useEffect(() => {
        return () => {
            methods.reset();
        };
    }, [open]);

    const renderDuplicateEntitySearch = () => {
        if (entityType === "product") {
            return <DuplicateProductSearch defaultSearch={defaultSearch} productsIds={entities.map(e => e.id)} />;
        }
        if (entityType === "company") {
            return <DuplicateCompanySearch defaultSearch={defaultSearch} companiesIds={entities.map(e => e.id)} />;
        }
    };

    return (
        <ModalComponent
            title={title}
            open={open}
            onClose={() => onClose && onClose()}
            modalTheme={!!markAsDuplicate ? "blue" : "error"}
            onSuccess={!!isSaving ? undefined : handleConfirm}
            justifyButtons="flex-start"
            showCancelButton={!isSaving}
            okButtonText={!!markAsDuplicate ? "Save" : "Decline"}
            dialogSx={{
                ".MuiDialog-paper": {
                    overflow: "auto",
                    maxWidth: "800px !important",
                    maxHeight: "100%",
                    width: "100%",
                },
                ".MuiDialogContent-root": {
                    overflow: "auto",
                },
                ".css-1sylg6-MuiDialogActions-root": {
                    // display: "none",
                },
            }}
            okButtonSx={{
                "& .inlineLoader": {
                    fontSize: "16px",
                    margin: 0,
                    "& div": {
                        display: "flex",
                    },
                },
            }}
            okButtonDisabled={!!isSaving}
            okButtonStartIcon={
                !!isSaving ? (
                    <Loader inline loading version="iconWhite" />
                ) : !!markAsDuplicate ? (
                    <CheckOutlinedIcon />
                ) : (
                    <DeclineIcon />
                )
            }
            content={
                <FormProvider {...methods}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "24px",
                        }}>
                        <Typography fontInter color={theme => theme.palette.neutral[700]}>
                            {!!markAsDuplicate ? (
                                <>
                                    Are you sure you want to mark <b>{entities.map(item => item.name).join(", ")}</b> as
                                    duplicates?
                                </>
                            ) : (
                                <>
                                    Are you sure you want to decline <b>{entities.map(item => item.name).join(", ")}</b>
                                    ? Please provide a reason for declining to proceed.
                                </>
                            )}
                        </Typography>
                        {!markAsDuplicate && (
                            <MuiSelect
                                id="status_reason_key"
                                showDropdownIcon={!markAsDuplicate}
                                readOnly={!!markAsDuplicate}
                                label={"Reason*"}
                                disableClearAll
                                onChange={handleStatusReasonOptionChange}
                                placeholder={"Select a Reason"}
                                accessKey="id"
                                value={selectedReasonOptionObj}
                                options={(data ?? []).map(item => ({id: item.value_key, name: item.name}))}
                                required
                                validateOnTheFly
                                helperText={"Choose a reason"}
                                disabled={!!userContentRejectOptionsQuery.isLoading || isSaving}
                                sx={{
                                    "& div:has(svg)": {
                                        padding: "16px 0 16px 8px !important",
                                    },
                                }}
                            />
                        )}
                        {selectedStatusReasonOption === StatusScopeDuplicateOptionKey[entityType] &&
                            renderDuplicateEntitySearch()}
                        {!!showOtherDescription && (
                            <TextBoxComponent
                                id={"reason_description"}
                                isRequired
                                maxLength={50}
                                disabled={isSaving}
                                validateOnTheFly
                                containerStyles={{width: "100%"}}
                                placeholder="Tell us about your decision"
                                label="Description"
                            />
                        )}
                    </Box>
                </FormProvider>
            }
        />
    );
};

export default DeclineUserContentConfirmation;
