import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useEffect, useReducer, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import ButtonComponent from "../../components/FormControls/button.component";
import DropdownComponent from "../../components/FormControls/dropdown.component";
import TextBoxComponent from "../../components/FormControls/textBox.component";
import ToggleInput from "../../components/FormControls/toggle.component";
import {TableComponent} from "../../components/Table/table.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import {useDebounce} from "../../hooks/useDebounce";
import useNotification from "../../hooks/useNotification";
import {chartService} from "../../services/chart.service";
import {eventService} from "../../services/event.service";
import {profileService} from "../../services/profile.service";
import styles from "../../styles/pages/adminReportCleanup.module.sass";
import {getErrorFromArray} from "../../utils/error.util";
import Loader from "../../utils/loader";

interface IUserListItem {
    email: string;
    first_name: string;
    id: string;
    is_spam: boolean;
    last_name: string;
    status: "inactive" | "active";
}

const SET_SPAM = "SET_SPAM";
const SET_STATUS = "SET_STATUS";
const SET_TABLE_DATA = "SET_TABLE_DATA";

const cleanupReducer = (state: any, action: any) => {
    switch (action.type) {
        case SET_SPAM:
            return {...state, spam: {...state.spam, [action.payload.id]: action.payload.value}};
        case SET_STATUS:
            return {...state, status: {...state.status, [action.payload.id]: action.payload.value}};
        case SET_TABLE_DATA:
            return {...state, tableData: action.payload};
        default:
            return state;
    }
};

const AdminReportCleanupPage = () => {
    const [dropdownOptions, setDropdownOptions] = useState({
        pitch_events: [],
        poll_questions: [],
    });
    const [isLoading, setIsLoading] = useState({
        table: false,
        pitch_events: false,
        poll_questions: false,
        recalculate: false,
    });
    const [meta, setMeta] = useState<any>(null);
    const [cleanupState, cleanupDispatch] = useReducer(cleanupReducer, {
        spam: {},
        status: {},
        tableData: [],
    });
    const notify = useNotification();
    const methods = useForm();
    const searchMethods = useForm();
    const selected_pitch_id = methods.watch("pitchEventSelect");
    const selected_poll_question = methods.watch("pollQuestionSelect");
    const search_value = searchMethods.watch("cleanupTableSearch");
    const debouncedSearch = useDebounce(search_value);

    const handleToggleSpam = (values: IUserListItem) => {
        cleanupDispatch({type: SET_SPAM, payload: {id: values.id, value: true}});
        const requestBody = {
            pitch_event_id: selected_pitch_id,
            poll_question_id: selected_poll_question,
            user_id: values.id,
            is_spam: !values.is_spam,
        };
        eventService.updateMSPAttendants(
            requestBody,
            (res: AxiosResponse) => {
                const copy = [...cleanupState.tableData];
                const foundIndex = copy.findIndex(user => user.id === values.id);
                if (foundIndex > -1) {
                    copy.splice(foundIndex, 1, {...values, ...res.data});
                    cleanupDispatch({type: SET_TABLE_DATA, payload: copy});
                    notify("Successfully Updated Spam", "Success");
                    cleanupDispatch({type: SET_SPAM, payload: {id: values.id, value: false}});
                }
            },
            (err: AxiosError) => {
                notify(getErrorFromArray(err), "Error");
                cleanupDispatch({type: SET_SPAM, payload: {id: values.id, value: false}});
            },
        );
    };

    const handleToggleStatus = (values: IUserListItem) => {
        cleanupDispatch({type: SET_STATUS, payload: {id: values.id, value: true}});
        const newStatus: any = values.status === "active" ? "Inactive" : "Active";
        profileService.updateStatus(
            values.id,
            newStatus,
            () => {
                const copy = [...cleanupState.tableData];
                const foundIndex = copy.findIndex(user => user.id === values.id);
                if (foundIndex > -1) {
                    copy.splice(foundIndex, 1, {...values, status: newStatus.toLowerCase()});
                    cleanupDispatch({type: SET_TABLE_DATA, payload: copy});
                    notify("Successfully Updated Status", "Success");
                    cleanupDispatch({type: SET_STATUS, payload: {id: values.id, value: false}});
                }
            },
            (err: AxiosError) => {
                notify(getErrorFromArray(err), "Error");
                cleanupDispatch({type: SET_STATUS, payload: {id: values.id, value: false}});
            },
        );
    };

    const SpamCell = useCallback(
        tableData => {
            const values = tableData.row.original;
            return (
                <div className="d-flex flex-row align-items-center">
                    {cleanupState.spam[values.id] ? (
                        <>
                            <Loader inline loading />
                            <span className="fw-bold">Updating</span>
                        </>
                    ) : (
                        <>
                            <span style={{width: 60}}>{values.is_spam ? "Yes" : "No"}</span>
                            <ToggleInput
                                id={`toggleSpam-${values.email}`}
                                checked={values.is_spam}
                                onToggle={() => handleToggleSpam(values)}
                                beforeText=""
                                afterText=""
                                big={false}
                            />
                        </>
                    )}
                </div>
            );
        },
        //eslint-disable-next-line react-hooks/exhaustive-deps
        [cleanupState.tableData, cleanupState.spam, handleToggleSpam],
    );

    const StatusCell = useCallback(
        tableData => {
            const values = tableData.row.original;
            return (
                <div className="d-flex flex-row align-items-center">
                    {cleanupState.status[values.id] ? (
                        <>
                            <Loader inline loading />
                            <span className="fw-bold">Updating</span>
                        </>
                    ) : (
                        <>
                            <span style={{width: 60}}>{values.status === "active" ? "Active" : "Inactive"}</span>
                            <ToggleInput
                                id={`toggleStatus-${values.email}`}
                                checked={values.status === "active"}
                                onToggle={() => handleToggleStatus(values)}
                                beforeText=""
                                afterText=""
                                big={false}
                            />
                        </>
                    )}
                </div>
            );
        },
        //eslint-disable-next-line react-hooks/exhaustive-deps
        [cleanupState.tableData, cleanupState.status, handleToggleStatus],
    );

    const columns: any = [
        {
            accessor: "first_name",
            Header: "First Name",
            sortable: true,
        },
        {
            accessor: "last_name",
            Header: "Last Name",
            sortable: true,
        },
        {
            accessor: "email",
            Header: "Email",
            sortable: true,
        },
        {
            accessor: "is_spam",
            Header: "Spam",
            Cell: SpamCell,
        },
        {
            accessor: "status",
            Header: "User Profile Status",
            Cell: StatusCell,
        },
    ];

    const getPitchEvents = () => {
        eventService.getNames(
            (res: AxiosResponse) => {
                const pitchOptions: any = [...res.data];
                setDropdownOptions({...dropdownOptions, pitch_events: pitchOptions.reverse()});
                setIsLoading({...isLoading, pitch_events: false});
            },
            () => {
                setIsLoading({...isLoading, pitch_events: false});
            },
        );
    };

    const getPollQuestions = pitch_event_id => {
        if (pitch_event_id === "0" || !pitch_event_id) return;
        eventService.getPollQuestionByEventId(
            pitch_event_id,
            (res: AxiosResponse) => {
                setDropdownOptions({...dropdownOptions, poll_questions: res.data});
                if (res.data.length === 0) {
                    notify("No questions were answered during this pitch.", "Error");
                }
            },
            (err: any) => {
                notify(getErrorFromArray(err), "Error");
            },
        );
    };

    const runUserList = (filters?: {page?: number; sortType?: "ASC" | "DESC"; orderBy?: string}) => {
        if (
            selected_pitch_id !== "0" &&
            selected_poll_question !== "0" &&
            selected_poll_question &&
            selected_pitch_id
        ) {
            setIsLoading({...isLoading, table: true});
            eventService.getMSPAttendants(
                {
                    ...filters,
                    pitch_id: selected_pitch_id,
                    question_id: selected_poll_question,
                    search_word: debouncedSearch,
                },
                onSuccessRunUserList,
            );
        }
    };

    const handleRecalculate = () => {
        setIsLoading({...isLoading, recalculate: true});
        chartService.recalculateQuadrantChartJobData(
            (res: AxiosResponse) => {
                setIsLoading({...isLoading, recalculate: false});
                notify(res.data, "Success");
            },
            (err: AxiosError) => {
                notify(err.message || "There was an error recalculating.", "Error");
                setIsLoading({...isLoading, recalculate: false});
            },
        );
    };

    const onSuccessRunUserList = (res: AxiosResponse) => {
        setMeta(res.data.meta);
        cleanupDispatch({type: SET_TABLE_DATA, payload: res.data.data});
        setIsLoading({...isLoading, table: false});
    };

    const handleParameterChange = (newParams: any) => {
        runUserList(newParams);
    };

    useEffect(() => {
        runUserList();
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [debouncedSearch]);

    useEffect(() => {
        if (selected_pitch_id) {
            getPollQuestions(selected_pitch_id);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selected_pitch_id]);

    useEffect(() => {
        if (selected_poll_question && selected_pitch_id) {
            runUserList();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selected_poll_question]);

    useEffect(() => {
        setIsLoading({...isLoading, pitch_events: true});
        getPitchEvents();
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                <TitleContainer as="h1" text="Remove User from Reports" position="start" className="mb-1" />
                <SubtitleContainer
                    as="p"
                    position="start"
                    text="Remove users that are fraudulent or spam on reports and charts. Select the Pitch and the poll question to mark the user as a spam by switching the spam toggle to “yes”. This will auto-save the changes. Click the Recalculate button to update the chart results. "
                />
                <SubtitleContainer
                    as="span"
                    position="start"
                    className={styles.secondarySubtitle}
                    text="To inactivate the user, toggle the switch on User Profile Status. This will prevent the user from logging in to the platform. User is not deleted when it is inactive."
                />
                <hr className="fadedBorder" />
                <FormProvider {...methods}>
                    <form onSubmit={methods.handleSubmit(runUserList)}>
                        <DropdownComponent
                            placeholder="Event"
                            label="Channel Engage event"
                            options={dropdownOptions.pitch_events?.map((event: any) => [event.id, event.name])}
                            id="pitchEventSelect"
                        />
                        <DropdownComponent
                            placeholder="Poll Question"
                            label="Poll Question"
                            options={
                                selected_pitch_id === "0"
                                    ? (["0", "Select an option"] as any)
                                    : dropdownOptions.poll_questions?.map((question: any) => [
                                          question.id,
                                          question.question,
                                      ])
                            }
                            disabled={selected_pitch_id === "0" || !selected_pitch_id}
                            id="pollQuestionSelect"
                        />
                    </form>
                </FormProvider>
                <hr className="fadedBorder" />
                <FormProvider {...searchMethods}>
                    <form className="d-flex flex-row align-items-center justify-content-end">
                        <TextBoxComponent
                            placeholder="Search using name or email address"
                            id="cleanupTableSearch"
                            className={styles.searchBox}
                        />
                    </form>
                </FormProvider>
                <TableComponent
                    id="cleanupTable"
                    columns={columns}
                    searchWord={searchMethods.watch("cleanupTableSearch")}
                    customData={cleanupState.tableData}
                    isLoading={isLoading.table}
                    noResultsMessage={
                        selected_pitch_id !== "0" || selected_poll_question !== "0"
                            ? "Select the Channel Engage event and Poll Question to view data."
                            : "No Results Found..."
                    }
                    paginated
                    meta={meta}
                    handleParameterChange={handleParameterChange}
                />
                <div className="d-flex flex-row align-items-center justify-content-end">
                    <ButtonComponent
                        id="recalculateChartBtn"
                        className="cpMainBtnThin"
                        type="button"
                        onClick={handleRecalculate}
                        disabled={isLoading.recalculate}>
                        {isLoading.recalculate ? <Loader version="iconWhite" inline loading /> : "Recalculate Charts"}
                    </ButtonComponent>
                </div>
            </div>
        </div>
    );
};

export default AdminReportCleanupPage;
