import {useEffect, useState} from "react";
import QuadrantChart from "../../components/Charts/quadrantChart.component";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import styles from "../../styles/pages/channelChartPage.module.sass";
import "../../styles/pages/channelChartsPrint.sass";
import routeConfig from "../../constants/routeConfig";
import {useNavigate, useParams} from "react-router-dom";
import builder, {BuilderComponent} from "@builder.io/react";
import Helmet from "react-helmet";
import ChartPagesFilters from "../../components/Charts/chartPagesFilters.component";
import useAuthState from "../../hooks/useAuthState";
import VendorQuadrantPoint from "../../components/ChannelChartsPages/vendorQuadrantPoint.component";
import {ShareIcon} from "../../components/Icons";
import ShareModal from "../../components/SocialShare/shareModal.component";
import ButtonComponent from "../../components/FormControls/button.component";
import {UPDATE_AUTH} from "../../reducers/auth.reducer";

const chartLinks = [
    {
        name: "Market Performance",
        path: routeConfig.ChannelCharts.path.replace(":chart_name", "market-performance"),
        key: "market-performance",
    },
    {
        name: "Product Relevance",
        path: routeConfig.ChannelCharts.path.replace(":chart_name", "product-relevance"),
        key: "product-relevance",
    },
    {
        name: "Service Impact",
        path: routeConfig.ChannelCharts.path.replace(":chart_name", "service-impact"),
        key: "service-impact",
    },
    {
        name: "Financial Impact",
        path: routeConfig.ChannelCharts.path.replace(":chart_name", "financial-impact"),
        key: "financial-impact",
    },
    {
        name: "MSP Interest",
        path: routeConfig.ChannelCharts.path.replace(":chart_name", "msp-interest"),
        key: "msp-interest",
    },
];

interface IBuilderChartModel {
    name: string;
    description: string;
    chartKey: string;
    xDataKey: string;
    yDataKey: string;
}

const ChannelChartsPage = () => {
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [builderSEO, setBuilderSEO] = useState<any | null>(null);
    const [builderChartDescriptions, setBuilderChartDescriptions] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<any>([]);
    const [categories, setCategories] = useState<Array<{id: string; name: string; key: string}>>([]);
    const [errorMessage, setErrorMessage] = useState("");
    const [shareOpen, setShareOpen] = useState(false);
    const {authState, updateAuthState} = useAuthState();
    const {chart_name} = useParams();
    const navigate = useNavigate();
    const builderChart: {name: string; data: IBuilderChartModel} | undefined = builderChartDescriptions?.find(
        d => d.data.chartKey === chart_name,
    );
    const friendlyChartName = builderChart?.name;
    const chartConfig = builderChart?.data;
    const x = categories.find(c => c.key === chartConfig?.xDataKey) || null;
    const y = categories.find(c => c.key === chartConfig?.yDataKey) || null;
    let maxRatingsXY = data.find(item => item.ratings[x?.id || ""] && item.ratings[y?.id || ""]);
    maxRatingsXY = maxRatingsXY
        ? {
              x: {
                  min: maxRatingsXY.ratings[x?.id || ""].ceiling_min,
                  max: maxRatingsXY.ratings[x?.id || ""].ceiling_max,
              },
              y: {
                  min: maxRatingsXY.ratings[y?.id || ""].ceiling_min,
                  max: maxRatingsXY.ratings[y?.id || ""].ceiling_max,
              },
          }
        : {x: {min: 0, max: 100}, y: {min: 0, max: 100}};
    const chartData = data.map(item => {
        const copy = {...item};
        Object.keys(item.ratings).forEach(key => {
            const ratingObj = item.ratings[key];
            if (ratingObj.percentage > ratingObj.ceiling_max) {
                copy.ratings[key].percentage = ratingObj.ceiling_max;
            }
            if (ratingObj.percentage < ratingObj.ceiling_min) {
                copy.ratings[key].percentage = ratingObj.ceiling_min;
            }
        });
        return copy;
    });
    const currentDescription =
        builderChartDescriptions.find(desc => desc.data.url === `/${chart_name}`)?.data?.description || "";

    const handleMetricChange = (newMetrics: any) => {
        setCategories(newMetrics);
    };

    const handleDataChange = (data: any) => {
        setData(data);
    };

    const getDescription = async () => {
        const descriptions: any = await builder.getAll("channel-chart-descriptions");
        setBuilderChartDescriptions(descriptions);
    };

    const handleErrorMessage = (message: string) => {
        setErrorMessage(message);
    };

    useEffect(() => {
        const found = chartLinks.find(chart => chart.key === chart_name);
        if (!found) {
            navigate(chartLinks[0].path);
        }
        // eslint-disable-next-line
    }, [chart_name]);

    useEffect(() => {
        getDescription();
        builder
            .get("main-pages", {url: "/channelcharts/*"})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderContentJson(res);
                    setBuilderSEO(res.data.seo);
                }
            });
    }, []);

    useEffect(() => {
        //? Shows login modal if user is not logged in
        if (!authState.authenticated && builderContentJson !== null && !authState.showLoginModal) {
            navigate({
                search: "?redirect_url=" + routeConfig.ChannelCharts.path.replace(":chart_name", chart_name || ""),
            });
            updateAuthState(UPDATE_AUTH, {showLoginModal: true});
        }
        // eslint-disable-next-line
    }, [authState.authenticated, authState.showLoginModal, builderContentJson]);

    return (
        <div className={ROUTE_CLASS + " p-0"}>
            <Helmet>
                <title>{builderSEO?.title || `${friendlyChartName} - Channel Charts`}</title>
                <meta name="description" content={builderSEO?.description || currentDescription} />
                <meta property="og:description" content={builderSEO?.description || currentDescription} />
                <meta property="og:url" content={window.location.origin + window.location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta
                    property="og:title"
                    content={builderSEO?.openGraphTitle || `${friendlyChartName} - Channel Charts`}
                />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="website" />
                <meta
                    property="website:tag"
                    content={builderSEO?.keywords || "channel charts, it channel vendors, technology vendors"}
                />
                <meta
                    property="keywords"
                    content={builderSEO?.keywords || "channel charts, it channel vendors, technology vendors"}
                />
                <meta property="robots" content="index,follow" />
                <meta
                    property="og:image"
                    content={builderSEO?.openGraphImage || "/Media/stackCharts/stack_chart_bg.png"}
                />
                <meta property="og:image:type" content="image/jpeg" />
                <meta property="og:image:width" content="800" />
                <meta property="og:image:height" content="600" />
                <meta
                    name="twitter:title"
                    content={builderSEO?.twitterTitle || `${friendlyChartName} - Channel Charts`}
                />
                <meta name="twitter:description" content={builderSEO?.twitterDescription || currentDescription} />
                <meta
                    name="twitter:image"
                    content={builderSEO?.twitterImage || "/Media/stackCharts/stack_chart_bg.png"}
                />
            </Helmet>
            <BuilderComponent model="main-pages" content={builderContentJson} />
            <div className="d-flex flex-column-reverse flex-lg-row align-items-start justify-content-center overflow-none">
                <ChartPagesFilters
                    setLoading={setLoading}
                    handleMetricChange={handleMetricChange}
                    handleDataChange={handleDataChange}
                    handleErrorMessage={handleErrorMessage}
                />
                <div
                    className={`d-flex flex-column col-12 col-lg-8 align-items-start shadow rounded py-3 ${styles.sectionToPrint} ${styles.chartWrapper}`}>
                    <div className="d-flex flex-row justify-content-center align-items-center mx-auto">
                        <div style={{color: "var(--cpOrange)", fontWeight: "bold", fontSize: "22px"}}>
                            {friendlyChartName}
                        </div>
                        <ShareIcon size={20} onClick={() => setShareOpen(true)} className={styles.shareBtn} />
                    </div>
                    <QuadrantChart
                        key={chart_name}
                        data={chartData}
                        isLoading={loading}
                        errorText={errorMessage}
                        xDataKey={`ratings.${x?.id}.percentage`}
                        yDataKey={`ratings.${y?.id}.percentage`}
                        uniqueIdentifier="company.id"
                        height={800}
                        width="container"
                        quadrantLabels={{
                            topLeft: "High Potential",
                            topRight: "Chart Toppers",
                            bottomLeft: friendlyChartName === "Market Performance" ? "Niche" : "Limited",
                            bottomRight: "Challengers",
                        }}
                        xAxisLabel={x?.name}
                        yAxisLabel={y?.name}
                        maxX={maxRatingsXY.x.max}
                        maxY={maxRatingsXY.y.max}
                        minX={maxRatingsXY.x.min}
                        minY={maxRatingsXY.y.min}
                        CustomMultiPoint={({matches, className}) => (
                            <div
                                className={`d-flex flex-column justify-content-center align-items-center multiPoint ${className}`}>
                                <h3 className={styles.numberText}>2+</h3>
                                <div className={styles.previewSection}>
                                    {matches.map(match => {
                                        return (
                                            <img
                                                key={match.company.name}
                                                alt="avatar"
                                                src={
                                                    match.company?.avatar
                                                        ? match.company.avatar
                                                        : createImageFromInitials(25, match.company.name, "", "company")
                                                }
                                                style={{height: 20, width: 20}}
                                                className={styles.customPoint}
                                            />
                                        );
                                    })}
                                </div>
                            </div>
                        )}
                        CustomPoint={VendorQuadrantPoint}
                        CustomTooltip={false}
                        pointHeight={50}
                        chartWrapperId="channelChartsPageChartWrapper"
                        mobileBreakpoint={768}
                        mobilePointHeight={30}
                    />
                    {!loading && (
                        <div className={`w-100 px-5 ${styles.chartDescription} ${styles.sectionToHide}`}>
                            <p>{currentDescription}</p>
                            <ButtonComponent
                                id="shareBtnBottom"
                                className={`cpBlueBtn mt-3 ${styles.sectionToHide}`}
                                buttonText="Share"
                                onClick={() => setShareOpen(true)}
                            />
                        </div>
                    )}
                </div>
            </div>
            {shareOpen && <ShareModal isOpen={shareOpen} onClose={() => setShareOpen(false)} createUrl />}
        </div>
    );
};

export default ChannelChartsPage;
