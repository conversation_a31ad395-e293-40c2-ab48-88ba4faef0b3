import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IBarChartData} from "../../Interfaces/charts/barChart.interface";
import {IEventAttendeeCount} from "../../Interfaces/event.interface";
import {LineChartComponent} from "../../components/Charts/lineChart.component";
import ButtonComponent from "../../components/FormControls/button.component";
import CheckBoxComponent from "../../components/FormControls/checkBox.component";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS, STALE_TIME} from "../../constants/commonStrings.constant";
import useNotification from "../../hooks/useNotification";
import {IPitchNames} from "../../models/PitchObject.interface";
import {eventService} from "../../services/event.service";
import {getErrorFromArray} from "../../utils/error.util";
import {eventNameForDisplay} from "../../utils/events.util";
import Loader from "../../utils/loader";

export default function EventCharts() {
    const notify = useNotification();
    const [attendeeCount, setAttendeeCount] = useState<IBarChartData[]>([]);
    const methods = useForm();
    const someSelected = Object.values(methods.getValues()).some(value => value === true);

    const getNames = useCallback(() => {
        const promiseToReturn: Promise<IPitchNames[] | any> = new Promise(resolve => {
            eventService
                .getNames()
                .then(r => resolve(r.data))
                .catch(() => {});
        });
        return promiseToReturn;
    }, []);

    const pitches = useQuery<IPitchNames[] | undefined>(["pitches"], getNames, {
        staleTime: STALE_TIME,
    });

    useEffect(() => {
        eventService.getAttendeeCount(onSuccessAttendeeCount, onError);
        // eslint-disable-next-line
    }, []);

    const onSuccessAttendeeCount = (response: AxiosResponse) => {
        const results: IEventAttendeeCount[] = response.data;

        // create IBarChartData from results
        const temp: IBarChartData[] = [];

        results.map(item => {
            const row: IBarChartData = {} as IBarChartData;
            row.xAxisName = item.event_name;
            row.yAxisName = item.attendee_count.toString();
            row.barName = item.attendee_count.toString();
            row.event_id = item.event_id;

            return temp.push(row);
        });
        setAttendeeCount(temp);
    };

    const onError = (error: AxiosError<any>) => {
        const errorMsg = getErrorFromArray(error);
        notify(errorMsg, "Error");
    };

    const handleToggleAll = () => {
        if (!pitches.data) return;
        const formReset = {};
        if (someSelected) {
            pitches.data.forEach(p => (formReset[p.id] = false));
            methods.reset(formReset);
        } else {
            pitches.data.forEach(p => (formReset[p.id] = true));
            methods.reset(formReset);
        }
    };

    useEffect(() => {
        if (pitches.data) {
            const formReset = {};
            pitches.data.forEach((item: IPitchNames) => {
                formReset[item.id] = true;
            });
            methods.reset(formReset);
        }
        // eslint-disable-next-line
    }, [pitches.data]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                {pitches.data && attendeeCount ? (
                    <>
                        <div className="col-3">
                            <FormProvider {...methods}>
                                <ButtonComponent
                                    id="toggleFilters"
                                    onClick={handleToggleAll}
                                    className="cpBlueAltBtnThin">
                                    {someSelected ? "Unselect All" : "Select All"}
                                </ButtonComponent>
                                <form>
                                    {pitches.data
                                        ?.filter(pe => attendeeCount.find(x => x.event_id === pe.id))
                                        .map((p: IPitchNames) => (
                                            <CheckBoxComponent
                                                id={p.id}
                                                name={p.id}
                                                key={p.id}
                                                testid={`event-${eventNameForDisplay(p)}`}>
                                                {eventNameForDisplay(p)}
                                            </CheckBoxComponent>
                                        ))}
                                </form>
                            </FormProvider>
                        </div>
                        <div className="flex-1-1-auto">
                            <LineChartComponent
                                chartTitle="Count of User Attendees by Event"
                                data={attendeeCount.filter((item: IBarChartData) =>
                                    methods.watch(item.event_id as any),
                                )}
                                xAxisLabel="Event Name"
                                yAxisLabel="# of Attendees Entered"
                                showLegend={false}
                                showTooltip={true}
                                dataKeys={[{keyName: "barName", label: "# of Attendees Entered", fillColor: "#FF6120"}]}
                                width="75%"
                                height={800}
                                showGridLines={false}
                                xAxisLabelColor="black"
                                yAxisLabelColor="black"
                                rotateXAxisTick
                                customEmptyMessage="No Data to Display"
                            />
                        </div>
                    </>
                ) : (
                    <div className="d-flex justify-content-center mt-5 col-12">
                        <Loader inline loading big />
                    </div>
                )}
            </div>
        </div>
    );
}
