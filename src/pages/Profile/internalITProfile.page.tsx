import {AxiosError, AxiosResponse} from "axios";
import {useEffect} from "react";
import Helmet from "react-helmet";
import {useLocation, useNavigate, useParams} from "react-router-dom";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {USERS_TYPE} from "../../enums/usersTypes.enum";
import {useCompany} from "../../hooks/fetches/useCompany";
import {useMspAffiliates} from "../../hooks/fetches/useMspAffiliates";
// import {useMspClients} from "../../hooks/fetches/useMspClients";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import useSettings from "../../hooks/useSettings";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import {enumService} from "../../services/enum.service";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import AboutTab from "../../uicomponents/Organism/CompanyProfile/AboutTab.component";
import AffiliatesTab from "../../uicomponents/Organism/CompanyProfile/AffiliatesTab/AffiliatesTab.component";
import ProfileHeaderV2 from "../../uicomponents/Organism/ProfileHeader/profileHeaderV2.components";
import {getErrorFromArray} from "../../utils/error.util";
import {pluralizeString} from "../../utils/formatString.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {useGetFriendlyUrl} from "../../utils/profile.util";

export default function InternalITProfilePage() {
    const location = useLocation();
    const friendlyUrl = useGetFriendlyUrl(location.pathname);
    const notify = useNotification();
    const {company} = useCompany(friendlyUrl, {
        isCompany: true,
        isMsp: true,
        calls: {
            all: false,
            company: true,
        },
        companyParams: ["profile_images", "is_affiliate_brand_main_company"],
    });
    const {updateSettings} = useSettings();
    const navigate = useNavigate();
    const {tab} = useParams();
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const hasAdminAccess = hasPermissions([PERMISSION_GROUPS.ADMIN_INTIT_COMPANY_PROFILE_UPDATE]);
    const hasEditAccess =
        hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE], {id: company.data?.id}) || hasAdminAccess;
    const hasReadAccess = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_READ], {id: company.data?.id});
    const companyInfo = company.data;
    const companyAvatar =
        companyInfo?.profile_images?.CompanyAvatar?.[0]?.src ||
        createImageFromInitials(150, companyInfo?.name!, "", "company");
    const canManageClients = company?.data?.manage_clients;
    // const {mspClients} = useMspClients(canManageClients ? friendlyUrl : undefined);
    const {mspAffiliates} = useMspAffiliates(company?.data?.is_affiliate_brand_main_company ? friendlyUrl : undefined, {
        calls: {all: false, mspAffiliates: true},
        returnSortOnAffiliateFilters: true,
    });
    // const total_clients = mspClients?.data?.pages?.[0]?.meta?.total || 0;
    const total_affiliates = mspAffiliates?.data?.pages?.[0]?.meta?.total || 0;
    const isAffiliateBrandMainCompany =
        companyInfo?.is_affiliate_brand_main_company &&
        companyInfo?.company_type === USERS_TYPE.FRANCHISE_CORPORATE_MSP;

    const onSuccessGetFocusTypes = (response: AxiosResponse) => {
        const focusTypes = response.data;
        updateSettings(UPDATE_SETTINGS, {focusTypes: focusTypes});
    };

    const handleError = (error: AxiosError<any>) => {
        const errorCode = getErrorFromArray(error);
        notify(errorCode, "Error");
    };

    const tabs = [
        {label: "Summary", Component: <AboutTab profile_type="internal_it" />, id: "summary"},
        {
            label: (
                <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                    Locations
                    <Badge
                        color="secondary"
                        title={`${total_affiliates || 0} ${pluralizeString("affiliate", total_affiliates || 0)}`}>
                        {total_affiliates || 0}
                    </Badge>
                </Box>
            ),
            Component: <AffiliatesTab readOnly={!hasEditAccess} />,
            id: "affiliates",
        },
    ].filter(item => {
        if (item.id === "affiliates") return isAffiliateBrandMainCompany && (hasEditAccess || hasReadAccess);
        return true;
    });

    useEffect(() => {
        if (tab === ":tab") {
            navigate(routeConfig.MspProfile.path.replace(":id", friendlyUrl).replace(":tab", "summary"));
        }
    }, []);

    useEffect(() => {
        if (!hasEditAccess && !hasReadAccess && !isLoadingPermissions) {
            return navigate(routeConfig.Home.path);
        }
        // eslint-disable-next-line
    }, [friendlyUrl]);

    useEffect(() => {
        async function getFocusTypes() {
            enumService.getFocusTypes(onSuccessGetFocusTypes, handleError);
        }
        getFocusTypes();
        // eslint-disable-next-line
    }, []);

    return (
        <>
            <Helmet>
                <title>{companyInfo?.name || "This Company Profile"}</title>
                <meta
                    name="description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    property="og:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:url" content={window.location.origin + location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="profile" />
                <meta property="og:profile:username" content={companyInfo?.handle || ""} />
                <meta property="og:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
                <meta
                    property="og:image:alt"
                    content={companyInfo?.name || "This company profile at Channel Program"}
                />
                {/* <meta property="website:tag" content="channel explorer, channel technology, channel industry" />
                <meta property="keywords" content="channel explorer, channel technology, channel industry" /> */}
                <meta name="twitter:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    name="twitter:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta name="twitter:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
            </Helmet>
            <div className={ROUTE_CLASS}>
                <Box
                    className={ROUTE_CONTAINER_CLASS}
                    sx={{
                        padding: "0!important",
                    }}>
                    <ProfileHeaderV2 profile_type="msp" isVendorPortalPage={false} />
                    <BasicTabs
                        tabsSx={{display: tabs.length > 1 ? "flex" : "none"}}
                        tabsContentSx={{padding: 2}}
                        tabs={tabs}
                        hideBorder
                        shouldTrackRoute
                        variant="scrollable"
                        useParams
                    />
                </Box>
            </div>
        </>
    );
}
