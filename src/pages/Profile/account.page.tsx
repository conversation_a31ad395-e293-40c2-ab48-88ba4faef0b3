import {builder} from "@builder.io/react";
import {useEffect, useState} from "react";
import {Nav, Tab} from "react-bootstrap/";
import {Helmet} from "react-helmet";
import {useSearchParams} from "react-router-dom";
import ReactIcon from "../../components/Icons/reactIcon.component";
import {ChangePassword} from "../../components/Profile/changePassword.component";
import {ChangePersonalInformation} from "../../components/Profile/changePersonalInformation.component";
import ContactDetails from "../../components/Profile/contactDetails.component";
import {MyProductReviews} from "../../components/Profile/myProductReviews.component";
import {TwoFactorAuth} from "../../components/Profile/twoFactorAuth.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import TitleContainer from "../../components/Titles/titleContainer.component";
import {TogglePrivacyComponent} from "../../components/TogglePrivacy/togglePrivacy.component";
import {ROUTE_CLASS} from "../../constants/commonStrings.constant";
import CompanyType from "../../constants/companyType.constant";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import styles from "../../styles/pages/account.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";

export default function AccountPage() {
    const [params, setParams] = useSearchParams();
    const tabParams = params.get("tab");
    const [tab, setTab] = useState(tabParams || "profile");
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const {authState} = useAuthState();
    const {activeCompany} = useActiveCompany();
    const isClientMSP = activeCompany?.company_type === CompanyType.MSP_CLIENT;

    useEffect(() => {
        builder
            .get("my-account-page", {url: "/account"})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderContentJson(res.data);
                }
            });
    }, []);

    return (
        <div className={ROUTE_CLASS}>
            <Helmet>
                <title> My Account </title>
            </Helmet>
            <Box
                className="newAltContainer"
                style={{
                    overflow: "visible",
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 3,
                        width: "100%",
                        flexWrap: "wrap",
                        "@media (min-width: 992px)": {
                            flexWrap: "nowrap",
                        },
                    }}>
                    <Tab.Container
                        id="left-tabs-example"
                        activeKey={tab}
                        onSelect={t => {
                            setTab(t || "profile");
                            setParams({tab: t || "profile"});
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                flex: "0 0 100%",
                                flexWrap: "wrap",
                                "@media (min-width: 992px)": {
                                    flexDirection: "column",
                                    flex: `0 0 calc((100% / 12) * 2)`,
                                },
                            }}>
                            <TitleContainer
                                as="h3"
                                text="My Info"
                                className="mb-2 col-12 col-lg-auto"
                                noConnector
                                position="start"
                            />
                            <Nav className="flex-column w-100 w-lg-auto text-center text-lg-start gap-3 gap-lg-0">
                                <Nav.Item className={styles.navItem}>
                                    <Nav.Link eventKey="profile">Profile Details</Nav.Link>
                                </Nav.Item>
                                <Nav.Item className={styles.navItem}>
                                    <Nav.Link eventKey="contact">Contact Details</Nav.Link>
                                </Nav.Item>
                                {!isClientMSP && (
                                    <Nav.Item className={styles.navItem}>
                                        <Nav.Link eventKey="myReviews">My Product Reviews</Nav.Link>
                                    </Nav.Item>
                                )}
                            </Nav>
                            <TitleContainer
                                as="h3"
                                text="Security and privacy"
                                className="mb-2 mt-5"
                                noConnector
                                position="start"
                            />
                            <Nav className="flex-column w-100 w-lg-auto text-center text-lg-start gap-3 gap-lg-0">
                                <Nav.Item className={styles.navItem}>
                                    <Nav.Link eventKey="password">Password</Nav.Link>
                                </Nav.Item>
                                <Nav.Item className={styles.navItem}>
                                    <Nav.Link eventKey="2fa">2FA</Nav.Link>
                                </Nav.Item>
                                {!isClientMSP && (
                                    <Nav.Item className={styles.navItem}>
                                        <Nav.Link eventKey="visibility">Profile Visibility</Nav.Link>
                                    </Nav.Item>
                                )}
                            </Nav>
                        </Box>
                        <Box
                            sx={{
                                flex: "1 1 100%",
                                gap: 3,
                                overflowY: "visible",
                                "@media (min-width: 992px)": {
                                    flex: "1 1 auto",
                                },
                            }}>
                            <Box
                                className={styles.borderedContent}
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    padding: 4,
                                }}>
                                <Tab.Content>
                                    <Tab.Pane eventKey="profile">
                                        <div className={styles.personalInfo}>
                                            <ChangePersonalInformation
                                                builder={builderContentJson?.personalInformation}
                                            />
                                        </div>
                                    </Tab.Pane>
                                    <Tab.Pane eventKey="contact" mountOnEnter>
                                        <div className="d-flex align-items-center justify-content-center w-100 mt-4">
                                            <ContactDetails builder={builderContentJson?.contactDetails} />
                                        </div>
                                    </Tab.Pane>
                                    {!isClientMSP && (
                                        <Tab.Pane eventKey="myReviews" mountOnEnter>
                                            <div className="d-flex align-items-center justify-content-center w-100 mt-4">
                                                <MyProductReviews builder={builderContentJson?.productReviews} />
                                            </div>
                                        </Tab.Pane>
                                    )}
                                    {/* Security panes */}
                                    <Tab.Pane eventKey="password" mountOnEnter>
                                        <div className="d-flex justify-content-center mt-4">
                                            <ChangePassword builder={builderContentJson?.password} />
                                        </div>
                                    </Tab.Pane>
                                    <Tab.Pane eventKey="2fa" mountOnEnter>
                                        <Box
                                            sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                marginTop: 4,
                                                maxWidth: "100%",
                                            }}>
                                            <TwoFactorAuth builder={builderContentJson?.twoFactor} />
                                        </Box>
                                    </Tab.Pane>
                                    {!isClientMSP && (
                                        <Tab.Pane eventKey="visibility" mountOnEnter>
                                            <div className="mt-4">
                                                <TitleContainer
                                                    noConnector
                                                    as="h2"
                                                    text={`Your profile visibility is set to ${
                                                        authState.is_private ? "Private" : "Public"
                                                    }`}
                                                    className="mb-2"
                                                />
                                                <div className="d-flex gap-2 align-items-center justify-content-center">
                                                    <ReactIcon
                                                        iconName="BsFillInfoCircleFill"
                                                        color="#004DCE"
                                                        className={styles.infoIcon}
                                                    />
                                                    <SubtitleContainer
                                                        as="p"
                                                        text="Setting your profile to Public allows other users to find you and your content."
                                                        elClassName="mb-0"
                                                    />
                                                </div>
                                                <div className="p-3 m-3 d-flex justify-content-center">
                                                    <TogglePrivacyComponent
                                                        big
                                                        key={authState?.is_private ? "private" : "public"}
                                                    />
                                                </div>
                                            </div>
                                        </Tab.Pane>
                                    )}
                                </Tab.Content>
                            </Box>
                        </Box>
                    </Tab.Container>
                </Box>
            </Box>
        </div>
    );
}
