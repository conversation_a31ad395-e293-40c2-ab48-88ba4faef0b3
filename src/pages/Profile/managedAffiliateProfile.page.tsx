import Helmet from "react-helmet";
import {Navigate, useLocation} from "react-router-dom";
import {getStackTypeLabel, ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import routeConfig from "../../constants/routeConfig";
import {VENDOR_TYPES} from "../../constants/vendorProfiles.constants";
import {useCompany} from "../../hooks/fetches/useCompany";
import useActiveCompany from "../../hooks/useActiveCompany";
import useIsViewingAsAffiliateParent from "../../hooks/useIsViewingAsAffiliateParent";
import usePermissions from "../../hooks/usePermissions";
import {
    COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS,
    PERMISSION_GROUPS,
} from "../../Interfaces/features/features.interface";
import Badge from "../../uicomponents/Atoms/Badge/badge.component";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import AboutTab from "../../uicomponents/Organism/CompanyProfile/AboutTab.component";
import NavistackTab from "../../uicomponents/Organism/CompanyProfile/NavistackTab.component";
import MSPTrackedSubscriptionsTab from "../../uicomponents/Organism/MSPTrackedSubscriptionsTab/MSPTrackedSubscriptionsTab.component";
import ProfileHeaderV2 from "../../uicomponents/Organism/ProfileHeader/profileHeaderV2.components";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {useGetFriendlyUrl} from "../../utils/profile.util";

export default function ManagedAffiliateProfile() {
    const location = useLocation();
    const friendlyUrl = useGetFriendlyUrl(location.pathname);
    const {activeCompany, isAffiliateCorporation} = useActiveCompany();
    const {company} = useCompany(friendlyUrl, {isCompany: true, calls: {all: false, company: true}});
    const companyInfo = company.data;
    const {hasPermissions, getAdminCompanyPermission} = usePermissions();
    const isViewingAsMSPBrand = useIsViewingAsAffiliateParent();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE], {id: companyInfo?.id});
    const hasReadAccess =
        isViewingAsMSPBrand || hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_READ], {id: companyInfo?.id});
    const companyAvatar = companyInfo?.avatar || createImageFromInitials(150, companyInfo?.name!, "", "company");
    const isPremiumMsp = companyInfo?.company_profile_type?.value === VENDOR_TYPES.MSP_BUSINESS_PREMIUM;
    const hasContractReadAccess = hasPermissions([
        PERMISSION_GROUPS.MANAGE_CONTRACTS_READ,
        getAdminCompanyPermission(
            COMPANY_ADMIN_BASE_PERMISSION_GROUP_KEYS.MANAGE_CONTRACTS_READ,
            activeCompany?.company_type,
        ),
    ]);
    const stackTypeLabel = getStackTypeLabel(!isAffiliateCorporation);

    const tabs = [
        {
            label: "Summary",
            Component: <AboutTab profile_type={"msp"} />,
            id: "summary",
        },
        {
            label: `${stackTypeLabel} Stack`,
            Component: (
                <Box
                    id="navistack-tab-wrapper"
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 3,
                        width: "100%",
                    }}>
                    <NavistackTab company_id={companyInfo?.id ?? ""} isViewingAsAffiliateParent={isViewingAsMSPBrand} />
                </Box>
            ),
            id: "brandstack",
        },
        ...(hasContractReadAccess
            ? [
                  {
                      label: (
                          <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                              Contract Management
                              {!isPremiumMsp && (
                                  <Badge color="error" title="Not Enabled">
                                      Not Enabled
                                  </Badge>
                              )}
                          </Box>
                      ),
                      Component: (
                          <MSPTrackedSubscriptionsTab
                              companyId={companyInfo?.id}
                              company_name={companyInfo?.name}
                              company_profile_type={companyInfo?.company_profile_type}
                              readOnly={!hasEditAccess}
                              isViewingAsAffiliateParent={isViewingAsMSPBrand}
                              // isClientMsp
                          />
                      ),
                      id: "contract",
                      disabled: !isPremiumMsp,
                  },
              ]
            : []),
    ];

    if (companyInfo && !hasEditAccess && !hasReadAccess) return <Navigate to={routeConfig.CompanyContracts.path} />;
    return (
        <>
            <Helmet>
                <title>{companyInfo?.name || "Location Company Profile"}</title>
                <meta
                    name="description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:title" content={companyInfo?.name || "Location Company Profile"} />
                <meta
                    property="og:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:url" content={window.location.origin + location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="profile" />
                <meta property="og:profile:username" content={companyInfo?.handle || ""} />
                <meta property="og:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
                <meta
                    property="og:image:alt"
                    content={companyInfo?.name || "This company profile at Channel Program"}
                />
                <meta name="twitter:title" content={companyInfo?.name || "Affiliate Company Profile"} />
                <meta
                    name="twitter:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta name="twitter:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
            </Helmet>
            <div className={ROUTE_CLASS}>
                <Box
                    className={ROUTE_CONTAINER_CLASS}
                    sx={{
                        padding: "0!important",
                    }}>
                    <ProfileHeaderV2 profile_type="msp" isVendorPortalPage={false} />
                    <BasicTabs
                        tabsSx={{display: tabs.length > 1 ? "flex" : "none"}}
                        tabsContentSx={{padding: 3}}
                        tabs={tabs}
                        styleBox={{
                            padding: "0 24px",
                        }}
                        hideBorder
                        variant="scrollable"
                    />
                </Box>
            </div>
        </>
    );
}
