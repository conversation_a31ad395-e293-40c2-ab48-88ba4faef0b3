import {DownloadOutlined, PictureAsPdfOutlined, ViewListOutlined} from "@mui/icons-material";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect} from "react";
import Helmet from "react-helmet";
import {Navigate, useLocation, useNavigate, useSearchParams} from "react-router-dom";
import {
    DEFAULT_QUERY_CONFIGS,
    PAGE_INSUFFICENT_PERMISSIONS,
    ROUTE_CLASS,
    ROUTE_CONTAINER_CLASS,
} from "../../constants/commonStrings.constant";
import CompanyType from "../../constants/companyType.constant";
import routeConfig from "../../constants/routeConfig";
import useActiveCompany from "../../hooks/useActiveCompany";
import useNotification from "../../hooks/useNotification";
import usePermissions from "../../hooks/usePermissions";
import {ICompany, ICompanyAccessible} from "../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {mspService} from "../../services/msp.service";
import {stackService} from "../../services/stack.service";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import AlertBanner from "../../uicomponents/Molecules/AlertBanner/alertBanner.component";
import MenuButton from "../../uicomponents/Molecules/MenuButton/menuButton.component";
import BasicTabs from "../../uicomponents/Molecules/Tabs/tabs.component";
import AllExpensesBreakdownCard from "../../uicomponents/Organism/CompanyExpenses/AllExpensesBreakdownCard.component";
import AboutTab from "../../uicomponents/Organism/CompanyProfile/AboutTab.component";
import NavistackTab from "../../uicomponents/Organism/CompanyProfile/NavistackTab.component";
import MSPTrackedSubscriptionsTab from "../../uicomponents/Organism/MSPTrackedSubscriptionsTab/MSPTrackedSubscriptionsTab.component";
import ProfileHeaderV2 from "../../uicomponents/Organism/ProfileHeader/profileHeaderV2.components";
import {downloadDoc} from "../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../utils/error.util";
import {createImageFromInitials} from "../../utils/imageFromText.util";
import {useGetFriendlyUrl} from "../../utils/profile.util";
import {COMPANY_EXPENSES_TEXT} from "../../constants/siteFlags";

export default function MspClientProfilePage() {
    const location = useLocation();
    const friendlyUrl = useGetFriendlyUrl(location.pathname);

    const notify = useNotification();
    const clientQuery = useQuery<ICompany>(["client-company", friendlyUrl], () => {
        return new Promise<ICompany>((resolve, reject) =>
            mspService
                .getClientInfo(friendlyUrl)
                .then((res: AxiosResponse<ICompany>) => resolve(res.data))
                .catch(err => {
                    handleError(err);
                    reject(err);
                }),
        );
    });
    const {activeCompany} = useActiveCompany();
    const companyInfo = clientQuery.data;
    const hideExpensesTabForParent = !!companyInfo?.hide_expenses;
    const navigate = useNavigate();
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const {hasPermissions: hasPermissionsInCompany, isLoadingPermissions: isLoadingInCompanyPermissions} =
        usePermissions({overrideCompanyId: companyInfo?.id});
    const loadingPermissions = isLoadingPermissions || isLoadingInCompanyPermissions;
    const isViewingAsMSPClient = activeCompany?.company_type === CompanyType.MSP_CLIENT;
    const hasReadAccessInCompany = hasPermissionsInCompany([PERMISSION_GROUPS.COMPANY_PROFILE_READ]);
    const hasEditAccessInCompany = hasPermissionsInCompany([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE]);
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE]) || hasEditAccessInCompany;
    const hasReadAccess = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_READ]) || hasReadAccessInCompany;
    const isViewingAsClientParent = !isViewingAsMSPClient && !!hasEditAccessInCompany;

    const companyAvatar = companyInfo?.avatar || createImageFromInitials(150, companyInfo?.name!, "", "company");
    const runCustomerIsAccessibleQuery =
        !!companyInfo?.id && !!activeCompany?.id && companyInfo?.id !== activeCompany?.id;
    const customerIsAccessibleQuery = useQuery<ICompanyAccessible>(
        ["customer-is-accessible", friendlyUrl],
        () => {
            return new Promise<ICompanyAccessible>((resolve, reject) =>
                mspService
                    .getCustomerIsAccessible(friendlyUrl)
                    .then((res: AxiosResponse<ICompanyAccessible>) => resolve(res.data))
                    .catch(err => {
                        handleError(err);
                        reject(err);
                    }),
            );
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: runCustomerIsAccessibleQuery},
    );
    const customerIsAccessible = customerIsAccessibleQuery.data;
    const customerIsAccessibleLoading = customerIsAccessibleQuery.isLoading;
    const [params] = useSearchParams();
    const tab = params.get("tab") || "summary";
    const hasPlaidReadAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_READ]);
    const showExpensesTab = !isViewingAsMSPClient && !!hasPlaidReadAccess;

    const handleError = (error: AxiosError<any>) => {
        if (!hasEditAccessInCompany && !hasReadAccess && !hasEditAccess) {
            notify(PAGE_INSUFFICENT_PERMISSIONS, "Error");
            return;
        }
        const errorCode = getErrorFromArray(error);
        notify(errorCode, "Error");
    };

    const tabs = [
        {label: "Summary", Component: <AboutTab profile_type="client" canEditData={hasEditAccess} />, id: "summary"},
        {
            label: "NaviStack",
            Component: (
                <NavistackTab
                    company_id={companyInfo?.id ?? ""}
                    parent_id={companyInfo?.client_parent?.id}
                    isClientMsp
                    isViewingAsClientParent={!!isViewingAsClientParent}
                    company={companyInfo}
                />
            ),
            id: "navistack",
        },
        {
            label: "Contract Management",
            Component: (
                <MSPTrackedSubscriptionsTab
                    companyId={companyInfo?.id}
                    company_name={companyInfo?.name}
                    company_profile_type={companyInfo?.company_profile_type}
                    company_friendly_url={friendlyUrl}
                    readOnly={!hasEditAccess}
                    isClientMsp
                    isParentClaimer={!!hasEditAccess}
                    canEditData={hasEditAccess}
                />
            ),
            id: "contract",
        },
        ...(showExpensesTab
            ? [
                  {
                      label: COMPANY_EXPENSES_TEXT,
                      Component: hideExpensesTabForParent ? (
                          <AlertBanner
                              id="parent-no-access-banner"
                              subTitle={{text: "Customer expense data is not visible at this time."}}
                              titleContainerSx={{display: "none"}}
                              variant="warning"
                          />
                      ) : (
                          <Box display="flex" flexDirection="column">
                              <AllExpensesBreakdownCard isParentViewingChild={true} company_id={companyInfo?.id} />
                          </Box>
                      ),
                      id: "expenses",
                  },
              ]
            : []),
    ].filter(tab => (clientQuery.isLoading || isViewingAsMSPClient ? tab.id === "summary" : true));

    useEffect(() => {
        if (companyInfo && !hasEditAccess && !hasReadAccess && !loadingPermissions) {
            navigate(routeConfig.Home.path);
        }
    }, [companyInfo]);

    if (
        (companyInfo &&
            !hasEditAccessInCompany &&
            !hasEditAccess &&
            !hasReadAccess &&
            !loadingPermissions &&
            hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_READ]) &&
            !customerIsAccessibleLoading &&
            !customerIsAccessible?.accessible) ||
        (!customerIsAccessibleLoading && !customerIsAccessible?.accessible)
    ) {
        return <Navigate to={routeConfig.CompanyContracts.path} />;
    }

    const onClickExportCSV = () => {
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        stackService.generateCustomerCSV(
            companyInfo?.id || "",
            (res: AxiosResponse) => {
                downloadDoc(res.data, "YourStack" + ".csv");
            },
            handleError,
        );
    };

    const onClickDownloadPDF = () => {
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        stackService.generateCustomerPDF(
            companyInfo?.id || "",
            (res: AxiosResponse) => {
                downloadDoc(res.data, "YourStack" + ".pdf");
            },
            handleError,
        );
    };
    return (
        <>
            <Helmet>
                <title>{companyInfo?.name || "This Company Profile"}</title>
                <meta
                    name="description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    property="og:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta property="og:url" content={window.location.origin + location.pathname} />
                <meta property="og:site_name" content="Channel Program" />
                <meta property="og:locale" content="en_US" />
                <meta property="og:type" content="profile" />
                <meta property="og:profile:username" content={companyInfo?.handle || ""} />
                <meta property="og:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
                <meta
                    property="og:image:alt"
                    content={companyInfo?.name || "This company profile at Channel Program"}
                />
                <meta name="twitter:title" content={companyInfo?.name || "This Company Profile"} />
                <meta
                    name="twitter:description"
                    content={companyInfo?.description || "Check out this company profile at Channel Program"}
                />
                <meta name="twitter:image" content={companyAvatar || "/Media/seo/og-home.jpg"} />
            </Helmet>
            <div className={ROUTE_CLASS}>
                <Box
                    className={ROUTE_CONTAINER_CLASS}
                    sx={{
                        padding: "0!important",
                    }}>
                    <ProfileHeaderV2 profile_type="client" isVendorPortalPage={false} />
                    <BasicTabs
                        tabsSx={{display: tabs.length > 1 ? "flex" : "none"}}
                        tabsContentSx={{padding: 3}}
                        tabs={tabs}
                        styleBox={{
                            padding: "0 24px",
                        }}
                        defaultTab="summary"
                        hideBorder
                        variant="scrollable"
                        shouldTrackRoute>
                        {!isViewingAsMSPClient && tab === "navistack" && (
                            <MenuButton
                                id="export"
                                buttonProps={{
                                    variant: "outlined",
                                    color: "secondary",
                                    sx: {
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "8px",
                                        fontWeight: "600 !important",
                                        fontSize: "14px",
                                        padding: "8px 16px",
                                        lineHeight: "18px",
                                    },
                                }}
                                menuProps={{
                                    sx: {
                                        marginTop: "8px",
                                    },
                                }}
                                showDropdownIcon
                                items={[
                                    {
                                        id: "export-client-csv",
                                        children: (
                                            <Box display="flex" alignItems="center" gap="16px">
                                                <ViewListOutlined color="neutral" />
                                                <Typography
                                                    variant="subtitle4"
                                                    fontInter
                                                    fontWeight={400}
                                                    sx={theme => ({
                                                        color: theme.palette.secondary.main,
                                                    })}>
                                                    Download .CSV
                                                </Typography>
                                            </Box>
                                        ),
                                        onClick: onClickExportCSV,
                                    },
                                    {
                                        id: "export-client-pdf",
                                        children: (
                                            <Box display="flex" alignItems="center" gap="16px">
                                                <PictureAsPdfOutlined color="neutral" />
                                                <Typography
                                                    variant="subtitle4"
                                                    fontInter
                                                    fontWeight={400}
                                                    sx={theme => ({
                                                        color: theme.palette.secondary.main,
                                                    })}>
                                                    Export PDF
                                                </Typography>
                                            </Box>
                                        ),
                                        onClick: onClickDownloadPDF,
                                    },
                                ]}>
                                <DownloadOutlined color="secondary" sx={{fontSize: "18px"}} />
                                Export
                            </MenuButton>
                        )}
                    </BasicTabs>
                </Box>
            </div>
        </>
    );
}
