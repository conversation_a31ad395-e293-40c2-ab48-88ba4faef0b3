import {useEffect} from "react";
import {
    AFFILIATE_WHITELABEL_HOSTS,
    DEFAULT_QUERY_CONFIGS,
    DEFAULT_SUBDOMAINS,
    ROUTE_CLASS,
    ROUTE_CONTAINER_CLASS,
} from "../../constants/commonStrings.constant";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import BasicTabs, {ITabItem} from "../../uicomponents/Molecules/Tabs/tabs.component";
import PageInnerHeader from "../../uicomponents/Organism/PageInnerHeader/pageInnerHeader";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import usePermissions from "../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../constants/routeConfig";
import Loader from "../../utils/loader";
import {useQuery} from "@tanstack/react-query";
import {IWhitelabeling} from "../../Interfaces/whitelabeling.interface";
import useActiveCompany from "../../hooks/useActiveCompany";
import {whitelabelingService} from "../../services/whitelabeling.service";
import {AxiosError, AxiosResponse} from "axios";
import {VisibilityOutlined} from "@mui/icons-material";
import PortalBrandingTab from "../../components/Whitelabeling/PortalBranding/portalBrandingTab.component";
import PlatformEmailsTab from "../../components/Whitelabeling/PlatformEmails/platformEmailsTab.component";
import {WhitelabelingTabIds} from "../../constants/tabs.constant";

export default function ManageWhitelabeling() {
    const navigate = useNavigate();
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const canReadWhitelabeling = hasPermissions([PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_READ]);
    const {activeCompany} = useActiveCompany();
    const companyWhitelabelingQuery = useQuery<IWhitelabeling>(
        ["whitelabeling-query-mngmnt", activeCompany?.id],
        () => {
            const promiseToReturn: Promise<IWhitelabeling> = new Promise((resolve, reject) =>
                whitelabelingService
                    .loadByCompanyId(activeCompany?.id || "")
                    .then((response: AxiosResponse<IWhitelabeling>) => resolve(response.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.id,
        },
    );

    const tabs: ITabItem[] = [
        {
            id: WhitelabelingTabIds.PORTAL_BRANDING,
            label: (
                <Box display="flex" alignItems="center" gap={1}>
                    Portal Branding
                </Box>
            ),
            Component: <PortalBrandingTab data={companyWhitelabelingQuery?.data} />,
        },
        {
            id: WhitelabelingTabIds.PLATFORM_EMAILS,
            label: (
                <Box display="flex" alignItems="center" gap={1}>
                    Platform Emails
                </Box>
            ),
            Component: <PlatformEmailsTab email_header={companyWhitelabelingQuery?.data?.email_header || undefined} />,
        },
    ];

    const openCustomerLoginPage = () => {
        const subdomain = activeCompany?.subdomain ?? activeCompany?.friendly_url;
        const route = DEFAULT_SUBDOMAINS[0]
            ? `${subdomain}.${DEFAULT_SUBDOMAINS[0]}.${AFFILIATE_WHITELABEL_HOSTS[0]}`
            : `${subdomain}.${AFFILIATE_WHITELABEL_HOSTS[0]}`;
        window.open(`${window.location.protocol}//${route}/msp-client-login`, "_blank");
    };

    useEffect(() => {
        if (!isLoadingPermissions && !canReadWhitelabeling) {
            navigate(routeConfig.Home.path);
        }
    }, [canReadWhitelabeling, isLoadingPermissions]);

    return (
        <div className={ROUTE_CLASS}>
            <div className={ROUTE_CONTAINER_CLASS}>
                {!activeCompany || isLoadingPermissions ? (
                    <Loader inline loading version="iconBlue" />
                ) : (
                    <>
                        <PageInnerHeader
                            id="manage-whitelabeling-header"
                            title={{
                                text: "Manage Whitelabeling",
                            }}
                            actions={{
                                primary: {
                                    id: "back",
                                    onClick: () => openCustomerLoginPage(),
                                    children: (
                                        <>
                                            <VisibilityOutlined /> Preview Portal
                                        </>
                                    ),
                                },
                            }}
                        />
                        <Typography variant="body3">
                            The settings you choose here will be applied to your customers login, register area, sent
                            emails and BetterTracker. You can customize our platform to fit your brand and visuals, and
                            preview how that will show to your customers.
                        </Typography>
                        <BasicTabs tabs={tabs} variant="scrollable" shouldTrackRoute />
                    </>
                )}
            </div>
        </div>
    );
}
