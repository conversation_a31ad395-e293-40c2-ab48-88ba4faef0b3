import builder, {<PERSON>uild<PERSON><PERSON>omponent} from "@builder.io/react";
import {useEffect, useState} from "react";
import {Nav, Tab} from "react-bootstrap";
import {Facebook} from "react-content-loader";
import {FormProvider, useForm} from "react-hook-form";
import {Navigate} from "react-router-dom";
import ButtonComponent from "../../components/FormControls/button.component";
import ControlledCheckBox from "../../components/FormControls/controlledCheckBox.component";
import {ModalComponent} from "../../components/Modal";
import AddContentModal from "../../components/Modal/addContentModal.component";
import AllContentNewsFeed from "../../components/NewsFeed/AllContentNewsFeed.component";
import NewsFeed from "../../components/NewsFeed/NewsFeed.component";
import SubtitleContainer from "../../components/Titles/subtitleContainer.component";
import {CHANNEL_PROGRAM_COMPANY_ID, CHANNEL_PROGRAM_SUBDOMAIN} from "../../constants/commonStrings.constant";
import CompanyType, {VendorCompanyTypes} from "../../constants/companyType.constant";
import routeConfig from "../../constants/routeConfig";
import {usePeople} from "../../hooks/fetches/usePeople";
import useActiveCompany from "../../hooks/useActiveCompany";
import useAuthState from "../../hooks/useAuthState";
import usePermissions from "../../hooks/usePermissions";
import useRandomAdvertisement from "../../hooks/useRandomAdvertisement";
import useSettings from "../../hooks/useSettings";
import {CompanyProfileTypes} from "../../Interfaces/businessRules.interface";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";
import {ITypesProfile} from "../../Interfaces/people.interface";
import {UPDATE_SETTINGS} from "../../reducers/settings.reducer";
import styles from "../../styles/pages/homeNewsFeed.module.sass";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Advertisement from "../../uicomponents/Organism/Advertisement/advertisement.component";
import MarketingBanners from "../../uicomponents/Organism/MarketingBanners/MarketingBanners.component";
import ErrorBoundary from "../../utils/errorBoundary.util";

interface IOption {
    value: string;
    label: string;
}

const HomeNewsFeed = () => {
    const [showProfileSwitchModal, setShowProfileSwitchModal] = useState<boolean>(false);
    const {settings, updateSettings} = useSettings();
    const [selectedOption, setSelectedOption] = useState<IOption>(
        settings.post_profile
            ? {value: settings.post_profile.id, label: settings.post_profile.name}
            : {value: "", label: ""},
    );
    const [builderContentJson, setBuilderContentJson] = useState<any | null>(null);
    const [activeKey, setActiveKey] = useState("allContent");
    const isCompanyProfile = (settings.post_profile?.profile_type as ITypesProfile)?.value in CompanyProfileTypes;
    const {authState} = useAuthState();
    const {people} = usePeople(authState.friendly_url, {
        isCompany: false,
        calls: {
            people: true,
            all: false,
        },
    });
    const {ad, ad_location} = useRandomAdvertisement("Home Page");
    const {activeCompany, userCompanies} = useActiveCompany();
    const {hasPermissions} = usePermissions();
    const activeProfile = settings.post_profile;

    const profileMethods = useForm();
    const companySelectWatcher = profileMethods.watch("asCompany");

    const userProfiles = userCompanies
        ? [
              people.data,
              ...userCompanies.filter(
                  company => company.company_type && VendorCompanyTypes.includes(company.company_type.value),
              ),
          ]
        : [];
    const profileOptions: IOption[] = userProfiles.map(profile => ({
        value: profile?.id || "",
        label: profile?.name || "",
    }));
    const onSwitchProfile = () => {
        setShowProfileSwitchModal(true);
    };
    const handleRadioToggle = e => {
        const foundOption = profileOptions.find(option => option.value === e.target.name);
        setSelectedOption(foundOption || {value: "", label: ""});
    };
    const handleSwitchProfile = () => {
        const foundProfile = userProfiles.find(profile => profile?.id === selectedOption.value);
        if (selectedOption) {
            updateSettings(UPDATE_SETTINGS, {post_profile: foundProfile});
        }
        setShowProfileSwitchModal(false);
    };

    useEffect(() => {
        builder
            .get("home-widgets", {url: "/"})
            .promise()
            .then(res => {
                if (res) {
                    setBuilderContentJson(res);
                }
            });

        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (!authState.friendly_url) return;
        if (!settings.post_profile && people.data) {
            if (!settings.post_profile) updateSettings(UPDATE_SETTINGS, {post_profile: people.data});
            if (!selectedOption.value) setSelectedOption({value: people.data.id, label: people.data.name});
        }
        // eslint-disable-next-line
    }, [settings.post_profile, people.data]);

    const redirectToChannelProgramPRM = (as_company_id?: string) => {
        let url = `${window.location.protocol}//${CHANNEL_PROGRAM_SUBDOMAIN}.${window.location.host}`;
        if (as_company_id) {
            url = url + `?as_company_id=${as_company_id}`;
        }
        window.location.href = url;
    };

    useEffect(() => {
        if (companySelectWatcher) {
            redirectToChannelProgramPRM(
                companySelectWatcher === CHANNEL_PROGRAM_COMPANY_ID ? "" : companySelectWatcher,
            );
        }
    }, [companySelectWatcher]);

    if (activeCompany?.company_type === CompanyType.MSP_CLIENT) {
        if (hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_READ])) {
            return <Navigate to={routeConfig.CompanyContracts.path} replace />;
        }
        return (
            <Navigate
                to={routeConfig.MspClientProfile.path.replace(":friendly_url", activeCompany?.friendly_url || "")}
                replace
            />
        );
    }
    return (
        <ErrorBoundary>
            <Box
                sx={{
                    flex: "1 0 auto",
                    "@media (min-width: 992px)": {
                        width: "calc(75% - 12px)",
                        maxWidth: "calc(75% - 12px)",
                    },
                }}>
                <MarketingBanners />
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginBottom: ad ? 2 : 0,
                    }}>
                    <Advertisement ad={ad} ad_location={ad_location} allowedCardType="horizontal-lg" />
                    <Advertisement ad={ad} ad_location={ad_location} allowedCardType="horizontal-md" />
                </Box>
                <div className={styles.newsFeedContainer + " " + styles.cardContainer}>
                    {activeProfile ? (
                        <AddContentModal
                            entity={activeProfile}
                            isCompany={isCompanyProfile}
                            onSwitchProfile={onSwitchProfile}
                            canSwitchProfile={profileOptions.length > 1}
                            show
                        />
                    ) : (
                        <div className="d-flex justify-content-center">
                            <Facebook />
                        </div>
                    )}
                </div>
                <div>
                    <hr className="fadedBorder" />
                    <Tab.Container
                        defaultActiveKey="allContent"
                        activeKey={activeKey}
                        onSelect={newActiveKey => setActiveKey(newActiveKey || "allContent")}>
                        {people.data && (
                            <>
                                <div className="w-100 d-flex flex-row align-items-center justify-content-center position-relative">
                                    <Nav.Link
                                        id="allContentLink"
                                        eventKey="allContent"
                                        className={`${styles.switchTabBtn} ${
                                            activeKey === "allContent" && styles.activeTabBtn
                                        }`}>
                                        All Content
                                    </Nav.Link>
                                    <Nav.Link
                                        id="yourFollowingLink"
                                        eventKey="yourFollowing"
                                        className={`${styles.switchTabBtn} ${
                                            activeKey === "yourFollowing" && styles.activeTabBtn
                                        }`}>
                                        My Feed
                                    </Nav.Link>
                                </div>
                                <Tab.Content>
                                    <Tab.Pane eventKey="allContent" mountOnEnter unmountOnExit>
                                        <AllContentNewsFeed />
                                    </Tab.Pane>
                                    <Tab.Pane eventKey="yourFollowing">
                                        <NewsFeed />
                                    </Tab.Pane>
                                </Tab.Content>
                            </>
                        )}
                    </Tab.Container>
                </div>
            </Box>
            <Box
                sx={{
                    flex: "1 0 auto",
                    width: "100%",
                    "@media (min-width: 992px)": {
                        minWidth: "calc(25% - 12px)",
                        width: "calc(25% - 12px)",
                    },
                    "&>*": {
                        maxWidth: "100%",
                    },
                }}>
                <BuilderComponent model="home-widgets" content={builderContentJson} />
                <div className="d-flex align-items-center justify-content-center mt-2">
                    <Advertisement ad={ad} ad_location={ad_location} allowedCardType="stacked" />
                </div>
            </Box>
            {showProfileSwitchModal && profileOptions.length > 1 && (
                <ModalComponent
                    modalSwitcher={showProfileSwitchModal}
                    modalSwitcherCallback={setShowProfileSwitchModal}
                    form={
                        <div className="d-flex flex-column align-items-center">
                            <SubtitleContainer as="p" className="w-75 my-3" text={"You are interacting as:"} />
                            <FormProvider {...profileMethods}>
                                <form onSubmit={profileMethods.handleSubmit(handleSwitchProfile)}>
                                    <div className={styles.formContainer}>
                                        {profileOptions.map((option: IOption) => (
                                            <ControlledCheckBox
                                                type="radio"
                                                key={"reponse_" + option.value}
                                                value={selectedOption?.value === option.value}
                                                testid={`option-${option.label}`}
                                                id={option.value}
                                                onChange={handleRadioToggle}>
                                                <>
                                                    <p className="my-3">{option.label}</p>
                                                </>
                                            </ControlledCheckBox>
                                        ))}
                                    </div>
                                    <div className="d-flex justify-content-center mt-0 mt-md-4 flex-wrap">
                                        <ButtonComponent
                                            id="saveProfile"
                                            className="cpMainBtn order-2 m-2"
                                            type="submit">
                                            Save
                                        </ButtonComponent>
                                    </div>
                                </form>
                            </FormProvider>
                        </div>
                    }
                />
            )}
        </ErrorBoundary>
    );
};

export default HomeNewsFeed;
