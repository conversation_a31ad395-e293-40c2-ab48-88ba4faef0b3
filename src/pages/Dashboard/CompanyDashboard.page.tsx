import ErrorBoundary from "../../utils/errorBoundary.util";
import Box from "../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../uicomponents/Atoms/Typography/Typography.component";
import {ROUTE_CONTAINER_CLASS} from "../../constants/commonStrings.constant";
import useTheme from "@mui/system/useTheme";
import CompanyFeatureCards from "../../uicomponents/Organism/CompanyDashboard/CompanyFeatureCards.component";
import QuickOverview from "../../uicomponents/Organism/CompanyDashboard/QuickOverview.component";
import AlertAndInsights from "../../uicomponents/Organism/CompanyDashboard/AlertAndInsightsCard.component";
import QuickActions from "../../uicomponents/Organism/CompanyDashboard/QuickActions.component";
import StackBreakDown from "../../uicomponents/Organism/CompanyDashboard/StackBreakdown.component";
import VendorPortals from "../../uicomponents/Organism/CompanyDashboard/VendorPortals.component";
import OnboardingProgress, {
    shouldHideOnboardingProgress,
} from "../../uicomponents/Molecules/OnboardingProgress/OnboardingProgress.component";
import Advertisement from "../../uicomponents/Organism/Advertisement/advertisement.component";
import useRandomAdvertisement from "../../hooks/useRandomAdvertisement";
import CalendarEvents from "../../uicomponents/Organism/CompanyDashboard/CalendarEvents.component";
import AccessControl from "../../utils/accessControl.util";
import {MSPCompanyTypes, TCompanyType} from "../../constants/companyType.constant";
import {IS_BETTERTRACKER_SITE, IS_DIRECT_IT_SITE} from "../../constants/siteFlags";
import useActiveCompany from "../../hooks/useActiveCompany";
import {PERMISSION_GROUPS} from "../../Interfaces/features/features.interface";

export default function CompanyDashboard() {
    const theme = useTheme();
    const {isClientMsp, isDirect, activeCompany} = useActiveCompany();
    const hideAdvertisment = IS_BETTERTRACKER_SITE || isClientMsp || isDirect;
    const {ad, ad_location} = useRandomAdvertisement(hideAdvertisment ? undefined : "Home Page");
    const shouldHideOnboarding = shouldHideOnboardingProgress(activeCompany?.id || "");
    return (
        <ErrorBoundary>
            <Box
                sx={{
                    overflow: "hidden",
                    position: "relative",
                    display: "flex",
                    flexDirection: {xl: "row", xs: "column"},
                }}>
                <Box
                    className={ROUTE_CONTAINER_CLASS}
                    display={"flex"}
                    gap={"24px"}
                    overflow="hidden"
                    padding={6}
                    bgcolor={IS_DIRECT_IT_SITE ? theme.palette.neutral[100] : theme.palette.neutral[200]}
                    sx={{
                        padding: {xs: "24px", lg: "48px"},
                        paddingRight: {lg: "16px"},
                    }}>
                    <Box display={"flex"} flexDirection={"column"} overflow={"hidden"} gap={"24px"}>
                        <Typography size={24} fontWeight={"bold"} color={"blue.800"}>
                            Company Dashboard
                        </Typography>
                        <CompanyFeatureCards />
                        <QuickOverview />
                        <AlertAndInsights />
                        <QuickActions />
                        <StackBreakDown />
                        <AccessControl
                            permissions={[
                                PERMISSION_GROUPS.MANAGE_VENDORS_READ,
                                PERMISSION_GROUPS.MANAGE_VENDORS_UPDATE,
                            ]}
                            companyTypes={MSPCompanyTypes as TCompanyType[]}>
                            <VendorPortals />
                        </AccessControl>
                    </Box>
                </Box>
                <Box
                    bgcolor={"white"}
                    padding={2}
                    display={"flex"}
                    gap={"14px"}
                    flexDirection={"column"}
                    borderLeft={IS_BETTERTRACKER_SITE ? `1px solid ${theme.palette.neutral[300]}` : "none"}
                    sx={{
                        maxWidth: {xl: "310px", xs: "initial"},
                    }}>
                    {!shouldHideOnboarding && <OnboardingProgress />}
                    {ad?.name && !hideAdvertisment && (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                            }}>
                            <Advertisement ad={ad} ad_location={ad_location} allowedCardType="stacked" />
                        </Box>
                    )}
                    <CalendarEvents />
                </Box>
            </Box>
        </ErrorBoundary>
    );
}
