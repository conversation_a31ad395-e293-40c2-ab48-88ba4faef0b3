import {ChevronLeftOutlined, ChevronRightOutlined} from "@mui/icons-material";
import CircleIcon from "@mui/icons-material/Circle";
import {SxProps, Theme, useTheme} from "@mui/material";
import {Children, forwardRef, type ReactNode, useEffect, useImperativeHandle, useRef} from "react";
import Slider, {type Settings} from "react-slick";
import {JsxElement} from "typescript";
import {generateSxFromProps} from "../../../utils/sx.util";
import Box from "../../Atoms/Box/Box.component";
import { IS_DIRECT_IT_SITE } from "../../../constants/siteFlags";

export interface ICPSliderProps extends Settings {
    children: ReactNode;
    wrapperSx?: SxProps<Theme>;
    centeredDots?: boolean;
    showDotsOnTopRight?: boolean;
    spaceBetweenItems?: string;
    gap?: number | string;
    justifyBetweenDots?: boolean;
}

const CPSlider = forwardRef<Slider | null, ICPSliderProps>(
    (
        {
            children,
            centeredDots,
            wrapperSx,
            showDotsOnTopRight,
            spaceBetweenItems,
            justifyBetweenDots,
            ...props
        }: ICPSliderProps,
        ref,
    ) => {
        const theme = useTheme();
        const sliderRef = useRef<Slider | null>(null);
        const currentSlide = useRef<number>(props.initialSlide || 0);
        const childrenCount = Children.count(children);
        useImperativeHandle(ref, () => sliderRef.current!, [sliderRef]);

        const settings: Settings = {
            dots: true,
            infinite: false,
            autoplay: props.autoplay,
            autoplaySpeed: props.autoplay ? props.autoplaySpeed || 3000 : undefined,
            speed: 500,
            slidesToShow: props.slidesToShow || 4,
            slidesToScroll: props.slidesToShow || 4,
            arrows: false,
            lazyLoad: "ondemand",
            appendDots: centeredDots
                ? dots => {
                      return (
                          <Box
                              sx={{
                                  display: "flex !important",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  gap: "8px",
                                  width: "100%",
                                  maxWidth: "100%",
                              }}>
                              {(dots as Array<JsxElement>).length > 1 && (
                                  <ChevronLeftOutlined
                                      onClick={() => sliderRef.current?.slickPrev()}
                                      sx={theme => ({
                                          fill: theme.palette.neutral[600],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "32px !important",
                                      })}
                                  />
                              )}
                              <Box
                                  sx={{
                                      cursor: "pointer",
                                      "&:hover": {
                                          fill: theme.palette.primary.main,
                                      },
                                  }}
                                  onClick={() => sliderRef.current?.slickGoTo(currentSlide.current - 2)}>
                                  <CircleIcon
                                      sx={theme => ({
                                          display: "flex",
                                          fill: theme.palette.neutral[500],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "8px",
                                      })}
                                  />
                              </Box>
                              <Box
                                  sx={{
                                      cursor: "pointer",
                                      "&:hover": {
                                          fill: theme.palette.primary.main,
                                      },
                                  }}
                                  onClick={() => sliderRef.current?.slickPrev()}>
                                  <CircleIcon
                                      sx={theme => ({
                                          display: "flex",
                                          fill: theme.palette.neutral[500],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "10px",
                                      })}
                                  />
                              </Box>
                              {dots}
                              <Box
                                  sx={{
                                      cursor: "pointer",
                                      "&:hover": {
                                          fill: theme.palette.primary.main,
                                      },
                                  }}
                                  onClick={() => sliderRef.current?.slickNext()}>
                                  <CircleIcon
                                      sx={theme => ({
                                          display: "flex",
                                          fill: theme.palette.neutral[500],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "10px",
                                      })}
                                  />
                              </Box>
                              <Box
                                  sx={{
                                      cursor: "pointer",
                                      "&:hover": {
                                          fill: theme.palette.primary.main,
                                      },
                                  }}
                                  onClick={() => sliderRef.current?.slickGoTo(currentSlide.current + 2)}>
                                  <CircleIcon
                                      sx={theme => ({
                                          display: "flex",
                                          fill: theme.palette.neutral[500],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "8px",
                                      })}
                                  />
                              </Box>
                              {(dots as Array<JsxElement>).length > 1 && (
                                  <ChevronRightOutlined
                                      onClick={() => sliderRef.current?.slickNext()}
                                      sx={theme => ({
                                          fill: theme.palette.neutral[600],
                                          cursor: "pointer",
                                          "&:hover": {
                                              fill: theme.palette.primary.main,
                                          },
                                          fontSize: "32px !important",
                                      })}
                                  />
                              )}
                          </Box>
                      );
                  }
                : justifyBetweenDots
                  ? dots => (
                        <Box
                            sx={{
                                display: "flex !important",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "8px",
                                width: "100%",
                            }}>
                            {(dots as Array<JsxElement>).length > 1 && (
                                <ChevronLeftOutlined
                                    onClick={() => sliderRef.current?.slickPrev()}
                                    sx={theme => ({
                                        fill: theme.palette.neutral[600],
                                        cursor: "pointer",
                                        position: "absolute",
                                        left: "-6%",
                                        "&:hover": {
                                            fill: theme.palette.blue[600],
                                        },
                                        fontSize: "50px !important",
                                    })}
                                />
                            )}
                            {(dots as Array<JsxElement>).length > 1 && (
                                <ChevronRightOutlined
                                    onClick={() => sliderRef.current?.slickNext()}
                                    sx={theme => ({
                                        fill: theme.palette.neutral[600],
                                        cursor: "pointer",
                                        position: "absolute",
                                        right: "-6%",
                                        fontSize: "50px !important",
                                        "&:hover": {
                                            fill: theme.palette.blue[600],
                                        },
                                    })}
                                />
                            )}
                        </Box>
                    )
                  : dots => (
                        <Box
                            sx={{
                                display: "flex !important",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "8px",
                                width: "100%",
                            }}>
                            {(dots as Array<JsxElement>).length > 1 && (
                                <ChevronLeftOutlined
                                    onClick={() => sliderRef.current?.slickPrev()}
                                    sx={theme => ({
                                        fill: theme.palette.neutral[600],
                                        cursor: "pointer",
                                        "&:hover": {
                                            fill: theme.palette.primary.main,
                                        },
                                        fontSize: "24px !important",
                                    })}
                                />
                            )}
                            {dots}
                            {(dots as Array<JsxElement>).length > 1 && (
                                <ChevronRightOutlined
                                    onClick={() => sliderRef.current?.slickNext()}
                                    sx={theme => ({
                                        fill: theme.palette.neutral[600],
                                        cursor: "pointer",
                                        "&:hover": {
                                            fill: theme.palette.primary.main,
                                        },
                                        fontSize: "24px !important",
                                    })}
                                />
                            )}
                        </Box>
                    ),
            customPaging: () => (
                <div
                    data-id="slick-custom-navigation"
                    style={{
                        width: "8px",
                        height: "8px",
                        backgroundColor: theme.palette.neutral[500],
                        transition: "width 350ms ease-in-out, background-color 350ms ease-in-out 100ms",
                        borderRadius: "60px",
                    }}
                />
            ),
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 3,
                        infinite: false,
                        dots: true,
                    },
                },
                {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        initialSlide: 2,
                    },
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                    },
                },
            ],
            ...props,
        };

        useEffect(() => {
            if (!!children && !props.initialSlide) {
                setTimeout(() => {
                    sliderRef.current?.slickGoTo(0);
                }, 1000);
            }
        }, [children]);

        return (
            <Box
                component="div"
                sx={{
                    display: childrenCount > 0 ? undefined : "none!important",
                    "& .slick-track": {
                        display: "flex !important",
                        gap: spaceBetweenItems || "8px",
                    },
                    "& .slick-dots": {
                        bottom: "-15px",
                    },
                    "& .slick-dots li": {
                        display: "flex !important",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: "8px",
                        margin: "0px",
                        maxWidth: "fit-content",
                    },
                    "& .slick-slide": {
                        margin: "0px",
                        height: "auto",
                        padding: "0!important",
                    },
                    "& .slick-dots li.slick-active>div": {
                        width: "32px !important",
                        backgroundColor: IS_DIRECT_IT_SITE ? `${theme.palette.btGreen.main} !important` : `${theme.palette.neutral[500]} !important`,
                        transition: "width 150ms ease-in-out, background-color 150ms ease-in-out 100ms",
                    },
                    "& div.slick-slide>div, & div.slick-slide>div>div": {
                        height: "100% !important",
                    },
                    "& div.slick-track": {
                        margin: "0px !important",
                        gap: props.gap || "8px",
                    },
                    ...(centeredDots
                        ? {
                              "& .slick-dots li": {
                                  display: "none !important",
                                  width: "32px",
                              },
                              "& .slick-dots li.slick-active": {
                                  display: "flex !important",
                                  cursor: "default",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  gap: "8px",
                                  margin: "0px",
                                  maxWidth: "fit-content",
                              },
                          }
                        : {}),
                    ...(showDotsOnTopRight
                        ? {
                              "& .slick-dots": {
                                  bottom: "-15px",
                                  top: "-30px",
                                  right: 0,
                                  position: "absolute",
                                  alignItems: "self-start",
                                  justifyContent: "flex-end",
                                  maxHeight: "30px",
                              },
                          }
                        : {}),
                    ...(justifyBetweenDots
                        ? {
                              maxWidth: "90%",
                              margin: "0 auto",
                              "& .slick-dots": {
                                  position: "absolute",
                                  top: "50%",
                                  transform: "translateY(-50%)",
                              },
                              "& .slick-dots li.slick-active": {
                                  display: "flex !important",
                                  cursor: "default",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  gap: "8px",
                                  margin: "0px",
                                  maxWidth: "fit-content",
                              },
                          }
                        : {}),
                    ...generateSxFromProps(wrapperSx, theme),
                }}>
                <Slider
                    ref={sliderRef}
                    afterChange={newIndex => {
                        currentSlide.current = newIndex;
                    }}
                    {...settings}>
                    {children}
                </Slider>
            </Box>
        );
    },
);

export default CPSlider;
