import ProgressBar from "../ProgressBar/progressBar.component";
import Typography from "../../Atoms/Typography/Typography.component";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import useTheme from "@mui/system/useTheme";
import useAppConfig from "../../../hooks/useAppConfig";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {useNaviStack} from "../../../hooks/fetches/useNaviStack";

interface INaviStackProgress {
    title?: string;
}

const NaviStackProgress = ({title = "Progress on NaviStack"}: INaviStackProgress) => {
    const theme = useTheme();

    const {config} = useAppConfig({config_key: "MAX_MY_STACK_CATEGORY_COUNT"});
    const maxCategories = config?.value ? Number(config?.value) : 30;
    const {activeCompany} = useActiveCompany();

    const {numOfCategoriesFilled} = useNaviStack(activeCompany?.id || "", {
        isCompany: true,
        isMSP: true,
        calls: {
            all: false,
            naviStack: true,
        },
    });
    const percentage = numOfCategoriesFilled ? (numOfCategoriesFilled / maxCategories) * 100 : 0;

    return (
        <BorderedBox sx={{padding: "16px 24px", width: "100%"}}>
            <Typography sx={{color: theme.palette.neutral[800]}} size={14} fontInter fontWeight="500">
                {title}
            </Typography>
            <ProgressBar
                value={percentage > 100 ? 100 : percentage}
                labels={{
                    0: "0%",
                    25: "25%",
                    50: "50%",
                    75: "75%",
                    100: "100%",
                }}
                progressBarTitle={(progress: string) => `NaviStack ${progress}% completed`}
                colors={{
                    25: "error",
                    50: "warning",
                    75: "blue",
                    100: "success",
                }}
                style={{
                    height: "10px",
                    marginTop: "5px",
                    borderRadius: "24px",
                    minWidth: "195px",
                }}
            />
        </BorderedBox>
    );
};

export default NaviStackProgress;
