import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import ArrowDropDown from "@mui/icons-material/ArrowDropDown";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import InfoOutlined from "@mui/icons-material/InfoOutlined";
import {Divider, useTheme} from "@mui/material";
import {useRef, useState} from "react";
import {IContactList} from "../../../Interfaces/massMessaging.interface";
import {ContactListTypes} from "../../../enums/contactListTypes.enum";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Menu from "../../Atoms/Menu/menu.component";
import Typography from "../../Atoms/Typography/Typography.component";
import ContactListDropdownItem from "./contactListDropdownItem.component";

interface IProps {
    items: IContactList[];
    selectedId?: string | undefined;
    hasError?: boolean;
    disabled?: boolean;
    onChangeList?: (item: IContactList) => void;
    onViewList?: (item: IContactList) => void;
    onEditList?: (item: IContactList) => void;
    onDeleteList?: (item: IContactList) => void;
    onSaveList?: (item: IContactList) => void;
    onCreateNewList?: () => void;
    hideActionMenu?: boolean;
    testId?: string;
    readOnly?: boolean;
}
/**
 * ContactListDropdown Component.
 *
 * This component renders a dropdown menu for selecting contact lists.
 * It displays a selected contact list and allows interactions like changing, editing, and deleting lists.
 *
 * @param items An array of contact list items to display in the dropdown menu.
 * @param selectedId The ID of the currently selected contact list.
 * @param hasError Indicates whether the component should display with error styling.
 * @param onChangeList Callback function triggered when a contact list is selected.
 * @param onViewList Callback function triggered when the view option is selected for a contact list.
 * @param onEditList Callback function triggered when the edit option is selected for a contact list.
 * @param onDeleteList Callback function triggered when the delete option is selected for a contact list.
 * @param onDeleteList Callback function triggered when the save option is selected for a contact list.
 * @param onCreateNewList Callback function triggered when the "Create a List" button is clicked.
 * @param hideActionMenu - Indicates whether to hide the action menu for all lists.
 *
 * @returns JSX.Element representing the ContactListDropdown component.
 *
 * @example
 * ```tsx
 * <ContactListDropdown
 *     items={[
 *          { id: '1', name: 'List A', type: 'CUSTOM', companies_count: 10, contacts_count: 15 }
 *      ]}
 *     selectedId="1"
 *     onChangeList={(item) => void 0}
 *     onViewList={(item) => void 0}
 *     onEditList={(item) => void 0}
 *     onDeleteList={(item) => void 0}
 *     onSaveList={(item) => void 0}
 *     onCreateNewList={() => void 0}
 * />
 * ```
 */
export default function ContactListDropdown({
    items = [],
    selectedId = undefined,
    hasError = false,
    onChangeList,
    onViewList,
    onEditList,
    onDeleteList,
    onSaveList,
    onCreateNewList,
    hideActionMenu = false,
    testId = "contact-list-dropdown",
    readOnly,
}: IProps) {
    const [open, setOpen] = useState(false);
    const anchorEl = useRef<HTMLDivElement>(null);
    const theme = useTheme();
    const selectedIndex = items.findIndex(item => item.id === selectedId);
    const selected = selectedIndex >= 0 ? items[selectedIndex] : null;
    const handleItemChange = (list: IContactList) => {
        setOpen(false);
        if (onChangeList) onChangeList(list);
    };
    const handleViewList = (list: IContactList) => {
        setOpen(false);
        if (onViewList) onViewList(list);
    };
    const handleEditList = (list: IContactList) => {
        setOpen(false);
        if (onEditList) onEditList(list);
    };
    const handleDeleteList = async (list: IContactList) => {
        setOpen(false);
        if (onDeleteList) onDeleteList(list);
    };
    const handleSaveList = async (list: IContactList) => {
        setOpen(false);
        if (onSaveList) onSaveList(list);
    };
    const handleCreateNewList = () => {
        if (onCreateNewList) onCreateNewList();
        setOpen(false);
    };
    return (
        <>
            <Box
                ref={anchorEl}
                data-custom-properties={JSON.stringify({name: "menuToggler-avatar"})}
                data-testid={testId}
                onClick={() => setOpen(prev => !prev)}
                tabIndex={0}
                role="button"
                onKeyDown={e => {
                    if (e.key === "Enter" || e.key === " ") {
                        setOpen(prev => !prev);
                    }
                }}
                data-hasError={hasError}
                sx={{
                    backgroundColor: theme.palette.neutral[200],
                    display: "flex",
                    padding: "4px 0 4px 16px",
                    borderRadius: "4px 4px 0 0",
                    gap: "4px",
                    alignItems: "center",
                    cursor: readOnly ? "default" : "pointer",
                    borderBottom: `solid 1px ${hasError ? theme.palette.error[500] : theme.palette.neutral[500]}`,
                    pointerEvents: readOnly ? "none" : "all",
                    "&:hover": readOnly
                        ? {}
                        : {
                              bgcolor: theme.palette.secondary[100],
                          },
                }}>
                <Box
                    sx={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                    }}>
                    <Typography
                        fontInter
                        variant="body4"
                        sx={{
                            fontSize: "12px !important",
                            fontWeight: 400,
                            lineHeight: "16px",
                            color: theme.palette.neutral[700],
                        }}>
                        Contact List
                    </Typography>
                    <Typography
                        fontInter
                        variant="body3"
                        sx={{
                            fontSize: "14px !important",
                            fontWeight: 400,
                            lineHeight: "24px",
                            letterSpacing: "0.5px",
                            color: readOnly
                                ? theme.palette.neutral[500]
                                : !!selected
                                ? theme.palette.neutral[800]
                                : theme.palette.neutral[600],
                        }}>
                        {selected?.name ?? "Select the list of who should get this message"}
                    </Typography>
                </Box>
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "48px",
                        height: "48px",
                        "& svg": {
                            width: "24px",
                            height: "24px",
                        },
                    }}>
                    {!!selected && !hideActionMenu && selected.type === ContactListTypes.TEMPORARY ? (
                        <IconButton
                            sx={{
                                color: theme.palette.neutral[500],
                                "& svg": {
                                    width: "24px",
                                    height: "24px",
                                },
                            }}
                            onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleEditList(selected);
                            }}>
                            <EditOutlinedIcon />
                        </IconButton>
                    ) : (
                        <ArrowDropDown htmlColor={readOnly ? theme.palette.neutral[500] : ""} />
                    )}
                </Box>
            </Box>
            <Menu
                open={open}
                onClose={() => setOpen(false)}
                anchorEl={anchorEl.current}
                PaperProps={{
                    style: {
                        minWidth: anchorEl ? anchorEl.current?.clientWidth : undefined,
                    },
                }}>
                <Box
                    sx={{
                        padding: 1,
                        display: "flex",
                        flexDirection: "column",
                        gap: "4px",
                    }}>
                    {items.length === 0 ? (
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                padding: "24px 4px",
                                borderRadius: "8px",
                                gap: 1,
                                backgroundColor: theme.palette.blue[100],
                                "& svg": {
                                    width: "18px",
                                    height: "18px",
                                },
                            }}>
                            <InfoOutlined htmlColor={theme.palette.neutral[500]} />
                            <Typography
                                fontInter
                                variant={"body3"}
                                sx={{
                                    color: theme.palette.neutral[800],
                                    fontSize: "14px",
                                    fontWeight: 600,
                                    lineHeight: "120%",
                                }}>
                                No lists found
                            </Typography>
                        </Box>
                    ) : (
                        <>
                            <Box
                                sx={{
                                    maxHeight: "297px",
                                    overflowY: "auto",
                                }}>
                                {items.map((item, index) => (
                                    <ContactListDropdownItem
                                        key={index}
                                        item={item}
                                        isSelected={selectedId === item.id}
                                        onViewList={handleViewList}
                                        onSelectList={handleItemChange}
                                        onEditList={handleEditList}
                                        onDeleteList={handleDeleteList}
                                        onSaveList={handleSaveList}
                                        hideActionMenu={hideActionMenu}
                                    />
                                ))}
                            </Box>
                            <Divider />
                        </>
                    )}
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            flex: 1,
                        }}>
                        <Button
                            id="create-new-contact-list"
                            color="secondary"
                            sx={{
                                color: theme.palette.blue[600],
                                fontSize: "12px",
                                padding: "4px 8px",
                                whiteSpace: "nowrap",
                                display: "flex",
                                alignItems: "center",
                                gap: "4px",
                                fontWeight: "600 !important",
                                "@media (min-width: 768px)": {
                                    fontSize: "14px",
                                    padding: "6px 20px",
                                },
                            }}
                            disabled={false}
                            onClick={handleCreateNewList}>
                            <AddOutlinedIcon />
                            Create a List
                        </Button>
                    </Box>
                </Box>
            </Menu>
        </>
    );
}
