import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";
import {pluralizeString} from "../../../utils/formatString.util";
import {useMspClients} from "../../../hooks/fetches/useMspClients";
import {IMspClient} from "../../../Interfaces/msp.interface";

interface IProps extends IAutoCompleteProps<any, any, any, any> {
    msp_friendly_url?: string;
}

const CompanyClientsAutocomplete = (props: IProps) => {
    const {msp_friendly_url} = props;
    const {mspClients, clientsQuerySearchState} = useMspClients(msp_friendly_url);
    const [searchState, setSearchState] = clientsQuerySearchState;
    const clients: IMspClient[] =
        mspClients.data?.pages.reduce((cur: any[], page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || [];
    const options = clients.map(c => ({id: c.id, label: c.name, checkboxes: {show: true}}));

    return (
        <AutoCompleteComponent
            {...props}
            options={options}
            loading={mspClients.isLoading}
            noOptionsText="No results"
            placeholder={
                props.placeholder ||
                `Select the ${pluralizeString("customer", props.multiple ? 2 : 1)} that have it installed`
            }
            // inputValue={searchState}
            onInputChange={(_, value) => {
                setSearchState(value);
            }}
            clearOnBlur={false}
        />
    );
};

CompanyClientsAutocomplete.defaultProps = {
    renderInput: undefined,
    options: undefined,
};

export default CompanyClientsAutocomplete;
