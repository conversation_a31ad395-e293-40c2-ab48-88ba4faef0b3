import React, {useEffect, useRef, useState} from "react";
import {Overlay, Popover} from "react-bootstrap";
import {useFormContext} from "react-hook-form";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent, {ITextBoxProps} from "../../FormControls/TextBox/textBox.component";
import {VisibilityOffOutlined, VisibilityOutlined} from "@mui/icons-material";
interface IProps extends ITextBoxProps {
    wrapperClassName?: string;
    criteriaHelper?: boolean;
}

const PasswordInput = (props: IProps) => {
    const [showPassword, setShowPassword] = useState(false);
    const [showTooltip, setShowTooltip] = useState<boolean>(false);
    const [lowercaseCriteria, setLowercaseCriteria] = useState<boolean>(false);
    const [uppercaseCriteria, setUppercaseCriteria] = useState<boolean>(false);
    const [symbolCriteria, setSymbolCriteria] = useState<boolean>(false);
    const [numberCriteria, setNumberCriteria] = useState<boolean>(false);
    const [lengthCriteria, setLengthCriteria] = useState<boolean>(false);
    const inputRef = useRef(null);
    const {watch} = useFormContext();
    const value = watch(props.id);

    useEffect(() => {
        if (props.id !== "password" || !value) {
            setSymbolCriteria(false);
            setLowercaseCriteria(false);
            setUppercaseCriteria(false);
            setNumberCriteria(false);
            return;
        }
        if (/(?=.*[\W_])/.test(value)) {
            setSymbolCriteria(true);
        } else {
            setSymbolCriteria(false);
        }
        if (/(?=.*[a-z])/.test(value)) {
            setLowercaseCriteria(true);
        } else {
            setLowercaseCriteria(false);
        }
        if (/(?=.*[A-Z])/.test(value)) {
            setUppercaseCriteria(true);
        } else {
            setUppercaseCriteria(false);
        }
        if (/(?=.*[0-9])/.test(value)) {
            setNumberCriteria(true);
        } else {
            setNumberCriteria(false);
        }
        if (value.length >= 8) {
            setLengthCriteria(true);
        } else {
            setLengthCriteria(false);
        }
        // eslint-disable-next-line
    }, [value]);

    return (
        <>
            <div ref={inputRef} className={props.wrapperClassName}>
                <TextBoxComponent
                    {...props}
                    containerSx={{
                        width: "100%",
                    }}
                    type={showPassword ? "text" : "password"}
                    onFocus={() => {
                        setShowTooltip(true);
                    }}
                    onBlur={() => {
                        setShowTooltip(false);
                    }}
                    endAdornment={
                        <div
                            className="d-flex align-items-center justify-content-center"
                            style={{cursor: "pointer"}}
                            onClick={() => {
                                setShowPassword(!showPassword);
                            }}>
                            {showPassword ? (
                                <VisibilityOffOutlined sx={{color: "blue.600"}} />
                            ) : (
                                <VisibilityOutlined sx={{color: "blue.600"}} />
                            )}
                        </div>
                    }
                />
            </div>
            {props.criteriaHelper ? (
                <Overlay show={showTooltip} target={inputRef.current} placement={"bottom-end"} containerPadding={20}>
                    {(p: any) => (
                        <Popover id="popover-contained" className="passwordPopover" {...p}>
                            <Popover.Body>
                                <div className="mt-1">
                                    <Typography variant="body1" color="#000" sx={{fontSize: "18px !important"}}>
                                        Password requirements:
                                    </Typography>
                                    <ul style={{listStyle: "none", paddingLeft: "0"}}>
                                        <li className={lowercaseCriteria ? "criteriaCheck passwordLi" : "passwordLi"}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24">
                                                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"></path>
                                            </svg>
                                            <Typography variant="body2">At least 1 lowercase character</Typography>
                                        </li>
                                        <li className={uppercaseCriteria ? "criteriaCheck passwordLi" : "passwordLi"}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24">
                                                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"></path>
                                            </svg>
                                            <Typography variant="body2">At least 1 UPPERCASE character</Typography>
                                        </li>
                                        <li className={symbolCriteria ? "criteriaCheck passwordLi" : "passwordLi"}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24">
                                                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"></path>
                                            </svg>
                                            <Typography variant="body2">At least 1 symbol character</Typography>
                                        </li>
                                        <li className={numberCriteria ? "criteriaCheck passwordLi" : "passwordLi"}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24">
                                                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"></path>
                                            </svg>
                                            <Typography variant="body2">At least 1 numeric character</Typography>
                                        </li>
                                        <li className={lengthCriteria ? "criteriaCheck passwordLi" : "passwordLi"}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24">
                                                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"></path>
                                            </svg>
                                            <Typography variant="body2">8 characters minimum</Typography>
                                        </li>
                                    </ul>
                                </div>
                            </Popover.Body>
                        </Popover>
                    )}
                </Overlay>
            ) : null}
        </>
    );
};

PasswordInput.defaultProps = {
    wrapperClassName: "w-100",
};

export default PasswordInput;
