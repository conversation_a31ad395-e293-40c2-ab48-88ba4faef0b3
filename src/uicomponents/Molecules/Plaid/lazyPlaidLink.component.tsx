import {useEffect} from "react";
import {usePlaidLink} from "react-plaid-link";

interface LazyPlaidLinkProps {
    config: Parameters<typeof usePlaidLink>[0];
    onClose: () => void;
}

const LazyPlaidLink = ({config, onClose}: LazyPlaidLinkProps) => {
    const {open, ready} = usePlaidLink(config);

    useEffect(() => {
        if (ready) {
            open();
            onClose?.();
        }
    }, [ready, open]);

    return null;
};

export default LazyPlaidLink;
