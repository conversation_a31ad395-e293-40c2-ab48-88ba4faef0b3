import {ChatOutlined} from "@mui/icons-material";
import ForumOutlinedIcon from "@mui/icons-material/ForumOutlined";
import {ButtonProps} from "@mui/material/Button";
import {NotificationDot} from "../../../components/Indicators/notification.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps extends ButtonProps {
    hasUnread?: boolean;
    partner_name?: string;
    partner_id?: string;
    partner_friendly_url?: string;
    partner_handle?: string;
    hideChatText?: boolean;
    iconSize?: string;
    chatBtnText?: string;
    tooltipText?: string;
    newButton?: boolean;
}

const ChatButton = (props: IProps) => {
    const {
        hideChatText,
        iconSize,
        hasUnread,
        partner_name,
        partner_friendly_url,
        partner_handle,
        partner_id,
        chatBtnText,
        tooltipText,
        newButton,
        ...buttonProps
    } = props;
    const custom_properties = JSON.stringify({
        partner_name,
        partner_friendly_url,
        partner_handle,
        partner_id,
    });
    return newButton ? (
        <Button
            id={`chat_${partner_id}`}
            data-custom-properties={custom_properties}
            title={tooltipText}
            color="secondary"
            variant="outlined"
            sx={{
                fontSize: "14px !important",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "8px",
                position: "relative",
            }}
            {...buttonProps}>
            <ChatOutlined color="secondary" sx={{fontSize: iconSize || "18px"}} />
            {!hideChatText && (chatBtnText ?? "Chat")}
            {hasUnread ? <NotificationDot style={{right: 5, top: 5}} /> : null}
        </Button>
    ) : (
        <Button
            id={`chat_${partner_id}`}
            data-custom-properties={custom_properties}
            title={tooltipText}
            sx={{
                margin: "0",
                color: "#7ABC79",
                fontSize: "14px !important",
                position: "relative",
                border: "1px solid #A4D0A4",
                borderRadius: "99px",
                padding: "5px 16px !important",
                background: "#E8F4E8",
                minWidth: "20px !important",
                cursor: (buttonProps as ButtonProps).disabled ? "not-allowed" : "pointer",
                pointerEvents: "all !important",
            }}
            {...buttonProps}>
            <ForumOutlinedIcon sx={{fontSize: iconSize || "18px"}} className="me-1"></ForumOutlinedIcon>
            {hideChatText ? null : (
                <Typography variant="body5" color="#7ABC79">
                    {chatBtnText ?? "Chat"}
                </Typography>
            )}
            {hasUnread ? <NotificationDot style={{left: 15, top: 15}} /> : null}
        </Button>
    );
};

export default ChatButton;
