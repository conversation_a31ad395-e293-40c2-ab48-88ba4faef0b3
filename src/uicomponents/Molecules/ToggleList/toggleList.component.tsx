import Check from "@mui/icons-material/Check";
import ToggleButton, {type ToggleButtonProps} from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import {MouseEvent, ReactNode, useState} from "react";
import Typography from "../../Atoms/Typography/Typography.component";

declare module "@mui/material/ToggleButton" {
    interface ToggleButtonPropsColorOverrides {
        primaryOrange: true;
        neutral: true;
        sky: true;
        purple: true;
        blue: true;
    }
}

interface IProps<T extends string> {
    items: {label: ReactNode; value: T; title?: string}[];
    onChange: (newValue: T | null) => void;
    defaultValue?: T;
    allowUnselect?: boolean;
    color?: ToggleButtonProps["color"];
    showCheck?: boolean;
}

export default function ToggleList<T extends string>({
    items,
    onChange,
    defaultValue,
    allowUnselect,
    showCheck,
    ...otherToggleButtonProps
}: IProps<T>) {
    const [value, setValue] = useState<string | null>(defaultValue || "");

    const handleValue = (event: MouseEvent<HTMLElement>, newValue: T | null) => {
        if (!newValue && !defaultValue && !allowUnselect) return;
        setValue(newValue);
        onChange(newValue ?? defaultValue ?? null);
    };

    return (
        <ToggleButtonGroup
            value={value}
            exclusive
            onChange={handleValue}
            sx={{
                gap: "1px",
            }}>
            {items.map((item, idx) => (
                <ToggleButton
                    value={item.value}
                    key={item.value}
                    aria-label={item.title}
                    title={item.title}
                    autoCapitalize="none"
                    selected={value === item.value}
                    sx={theme => ({
                        borderRadius:
                            idx === 0
                                ? "100px 0 0 100px !important"
                                : idx === items.length - 1
                                ? "0 100px 100px 0 !important"
                                : undefined,
                        textTransform: "none",
                        background:
                            (value === item.value ? theme.palette.secondary[100] : theme.palette.neutral[100]) +
                            " !important",
                        border:
                            value === item.value
                                ? `1px solid ${theme.palette.blue[400]}`
                                : `1px solid ${theme.palette.neutral[500]}`,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: "8px",
                    })}
                    {...otherToggleButtonProps}>
                    {value === item.value && showCheck && <Check sx={{fontSize: "18px"}} color="secondary" />}
                    {typeof item.label === "string" ? (
                        <Typography
                            variant="body4"
                            fontInter
                            sx={theme => ({
                                color: theme.palette.secondary.main,
                                fontWeight: "500",
                            })}>
                            {item.label}
                        </Typography>
                    ) : (
                        item.label
                    )}
                </ToggleButton>
            ))}
        </ToggleButtonGroup>
    );
}
