import useTheme from "@mui/system/useTheme";
import Box from "../../Atoms/Box/Box.component";
import ArrowRightAltOutlinedIcon from "@mui/icons-material/ArrowRightAltOutlined";
import ArrowDropUpOutlinedIcon from "@mui/icons-material/ArrowDropUpOutlined";
import ArrowDropDownOutlinedIcon from "@mui/icons-material/ArrowDropDownOutlined";
import Typography from "../../Atoms/Typography/Typography.component";
import React from "react";

interface IPercentageDiffProps {
    percentage?: number | string;
    increased?: boolean;
}

const PercentageDiff = ({percentage = 0, increased = false}: IPercentageDiffProps) => {
    const theme = useTheme();
    const greenColor = theme.palette.success[600];
    const redColor = theme.palette.error[500];

    return (
        <Box display={"flex"} flexDirection="row" alignItems="center">
            {Number(percentage) === 0 ? (
                <ArrowRightAltOutlinedIcon
                    htmlColor={theme.palette.blue[600]}
                    sx={{height: 24, width: 24, marginLeft: 0.5}}
                />
            ) : increased ? (
                <ArrowDropUpOutlinedIcon htmlColor={redColor} />
            ) : (
                <ArrowDropDownOutlinedIcon htmlColor={greenColor} />
            )}
            <Typography
                variant="14"
                fontWeight={700}
                fontInter
                color={Number(percentage) === 0 ? theme.palette.blue[600] : increased ? redColor : greenColor}>
                {percentage}%
            </Typography>
        </Box>
    );
};

export default PercentageDiff;
