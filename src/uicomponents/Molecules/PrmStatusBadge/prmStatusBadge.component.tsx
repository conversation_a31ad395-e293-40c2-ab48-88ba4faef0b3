import useTheme from "@mui/material/styles/useTheme";
import {PARTNER_INVITE_STATUS} from "../../../constants/partnerInviteStatus.constant";
import {portalAccess, portalNoAccess, portalRequestPending} from "../../../constants/commonStrings.constant";
import Typography from "../../Atoms/Typography/Typography.component";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

interface IProps {
    partnerStatus?: string;
    partnerFlag?: boolean;
    inline?: boolean;
    styles?: any;
    className?: string;
}

const PRMStatusBadge = (props: IProps) => {
    const {partnerFlag, partnerStatus} = props;
    const theme = useTheme();

    const styles: any = {
        backgroundColor:
            partnerStatus === PARTNER_INVITE_STATUS.ACCEPTED
                ? theme.palette.success[200]
                : partnerStatus === PARTNER_INVITE_STATUS.REQUESTED
                ? "#FCF0EC"
                : theme.palette.error[200],
        color:
            partnerStatus === PARTNER_INVITE_STATUS.ACCEPTED
                ? theme.palette.success[600]
                : partnerStatus === PARTNER_INVITE_STATUS.REQUESTED
                ? theme.palette.primaryOrange["main"]
                : theme.palette.error["main"],
    };

    const positionStyles: any = props.inline ? {} : {position: "absolute", top: 0, left: 0};

    const title =
        partnerStatus === PARTNER_INVITE_STATUS.ACCEPTED
            ? portalAccess
            : partnerStatus === PARTNER_INVITE_STATUS.REQUESTED
            ? portalRequestPending
            : portalNoAccess;

    if (!partnerFlag) return null;
    return (
        <div
            style={{
                ...styles,
                ...positionStyles,
                borderRadius: "10px",
                padding: "5px 10px",
                height: 24,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                ...(props.styles || {}),
            }}
            className={props.className}
            title={title || ""}>
            <Typography variant="body4" fontWeight={500} color={styles.color || "#000"}>
                Portal <InfoOutlinedIcon sx={{height: 10, width: 10, verticalAlign: "top"}} htmlColor={styles.color} />
            </Typography>
        </div>
    );
};

export default PRMStatusBadge;
