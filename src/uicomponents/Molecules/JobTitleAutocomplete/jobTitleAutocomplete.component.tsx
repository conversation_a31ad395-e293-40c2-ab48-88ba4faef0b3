import {useEffect, useState} from "react";
import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";
import {AxiosError, AxiosResponse} from "axios";
import {userService} from "../../../services/user.service";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";

interface IProps extends IAutoCompleteProps<any, any, any, any> {}

const JobTitleAutoComplete = (props: IProps) => {
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<any[]>([]);
    const notify = useNotification();

    const handleError = (error: AxiosError) => {
        notify(getErrorFromArray(error), "Error");
        setLoading(false);
    };

    const handleSuccess = (res: AxiosResponse) => {
        // if (defaultJobTitle) setSelectedJobTitle(r.data?.filter(j => j.id === defaultJobTitle.id) || []);
        setOptions(res.data.map(j => ({id: j.id, label: j.name})));
    };

    useEffect(() => {
        setLoading(true);
        userService.getJobTitles(handleSuccess, handleError);
    }, []);

    return (
        <AutoCompleteComponent
            {...props}
            options={options}
            loading={loading || !options?.length}
            noOptionsText="No results"
            placeholder={props.placeholder || "Select a Job Title"}
        />
    );
};

JobTitleAutoComplete.defaultProps = {
    renderInput: undefined,
    options: undefined,
};

export default JobTitleAutoComplete;
