import {useTheme} from "@mui/material";
import {Palette} from "@mui/material/styles/createPalette";
import {DateTime} from "luxon";
import {useCallback, useEffect, useState} from "react";
import ReactCalendar, {type CalendarProps} from "react-calendar";
import "react-calendar/dist/Calendar.css";
import type {View} from "react-calendar/dist/cjs/shared/types";
import {DATE_MONTH_NAME_FULL, DATE_YEAR_ONLY, formatDate} from "../../../utils/formatDate";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../Avatar/avatarV2.component";
import ButtonList from "../ButtonList/buttonList.component";
import CustomPopover from "../CustomPopover/CustomPopover";
import "./Calendar.sass";
import MoreEventsPopover from "./MoreEventsPopover.component";

export interface ICalendarEvent {
    id: string;
    date: string;
    name: string;
    color?: keyof Palette;
    company?: {
        avatar: string;
        type: string;
        profile_type?: string | {value: string; label: string};
        friendly_url: string;
        name: string;
    };
    product?: {
        name: string;
    };
    //? Multi day events
    start_date?: string;
    end_date?: string;
    //? Render extra borders for multi events at the same day
    extraEvents?: Array<keyof Palette>;
    onClick?: (id: string) => void;
    //? Overwrites the id passed in the click handler if needed
    clickId?: string;
    hasAddons?: boolean;
    type?: string;
    parent_id?: string;
    hideAvatar?: boolean;
}

interface IColorLegend {
    color: keyof Palette;
    label: string;
}

interface IProps extends CalendarProps {
    events?: ICalendarEvent[];
    colorLegend?: IColorLegend[];
    onRangeChange?: (newDate: Date) => void;
}

function _isDateInRange(date: Date, event: any): boolean {
    return date >= event.startDate && date <= event.endDate;
}

const Calendar = (props: IProps) => {
    const [date, setDate] = useState<Date>(new Date());
    const [view] = useState<View>("month");
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [eventsToRender, setEventsToRender] = useState<ICalendarEvent[] | null>(null);
    const theme = useTheme();
    const label =
        formatDate(date.toISOString(), DATE_MONTH_NAME_FULL) + " " + formatDate(date.toISOString(), DATE_YEAR_ONLY);

    const handleNextPrevClick = (next: boolean = true) => {
        const newDate = new Date(date.setMonth(date.getMonth() + (next ? 1 : -1)));
        setDate(newDate);
    };

    const goToToday = () => {
        setDate(new Date());
    };

    // const changeView = viewType => {
    //     setView(viewType);
    // };

    const renderTileContent = useCallback(
        ({date}: {date: Date}) => {
            const dayEvents = props.events?.filter(event => {
                const eventDate = DateTime.fromISO(event.date);
                const calendarDate = DateTime.fromJSDate(date);
                return eventDate.hasSame(calendarDate, "day");
            });
            const uniqueEventTypes: Array<keyof Palette> = dayEvents
                ? [
                      ...new Set(dayEvents.map(event => event.color ?? "secondary")),
                      ...new Set(...dayEvents.map(event => event.extraEvents ?? [])),
                  ]
                : [];
            /* const dateEvents = props.events?.filter(event => isDateInRange(date, event)); */
            return dayEvents?.length ? (
                <>
                    <Box
                        sx={{
                            display: "none",
                            "@media (min-width: 600px)": {display: "block"},
                        }}>
                        {dayEvents.slice(0, 2).map((event, index) => (
                            <Box
                                key={event.id + index}
                                onClick={() => event.onClick?.(event.clickId ?? event.id)}
                                sx={{
                                    display: event.onClick ? "flex" : "default",
                                    cursor: "pointer",
                                    height: "38px",
                                }}>
                                {event.extraEvents?.map((extraEvent, idx) => (
                                    <Box
                                        key={`${extraEvent}-${event.id}-${idx}`}
                                        sx={{
                                            display: "block",
                                            backgroundColor: theme.palette[extraEvent][300],
                                            width: "3px",
                                            height: "100%",
                                            flex: "0 0 3px",
                                        }}
                                    />
                                ))}
                                <Box
                                    sx={{
                                        backgroundColor: event.color
                                            ? theme.palette[event.color][100]
                                            : theme.palette.secondary[100],
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                        padding: "4px",
                                        "& a": {
                                            flexShrink: 0,
                                        },
                                    }}>
                                    {event.company && !event.hideAvatar && (
                                        <AvatarComponentV2
                                            isCompany
                                            showProfileType
                                            profileTypeMinimized
                                            user={event.company}
                                            size={30}
                                            disableLink
                                        />
                                    )}
                                    <Typography
                                        fontInter
                                        variant="subtitle3"
                                        title={event.name}
                                        sx={{
                                            color: event.color
                                                ? theme.palette[event.color][800]
                                                : theme.palette.secondary[800],
                                            whiteSpace: "nowrap",
                                            overflow: "hidden",
                                            fontWeight: "600 !important",
                                            marginLeft: event.company ? undefined : "8px",
                                        }}>
                                        {event.name}
                                    </Typography>
                                </Box>
                            </Box>
                        ))}
                        {dayEvents.length > 2 && (
                            <Box
                                sx={{
                                    display: "none",
                                    "@media (min-width: 600px)": {display: "block"},
                                }}>
                                <Box
                                    id={date.toISOString() + "-more-events-btn"}
                                    onClick={e => {
                                        setAnchorEl(e.currentTarget);
                                        setEventsToRender(dayEvents);
                                    }}
                                    sx={{
                                        display: "flex",
                                        justifyContent: "flex-start",
                                        padding: "4px 8px",
                                        border: "none!important",
                                        cursor: "pointer",
                                        "&:hover": {
                                            backgroundColor: theme.palette.secondary[100],
                                        },
                                    }}>
                                    <Typography
                                        fontInter
                                        variant="subtitle3"
                                        sx={{
                                            color: theme.palette.secondary[600],
                                            fontWeight: "700 !important",
                                        }}>
                                        +{dayEvents.length - 2} more
                                    </Typography>
                                </Box>
                            </Box>
                        )}
                    </Box>
                    {!!dayEvents.length && (
                        <Box
                            sx={{
                                position: "relative",
                                maxWidth: "100%",
                                "@media (min-width: 600px)": {display: "none"},
                            }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    position: "absolute",
                                    top: "0",
                                    left: "0",
                                    width: "100%",
                                    height: "100%",
                                }}>
                                {uniqueEventTypes.map((eventType, i) => (
                                    <Box
                                        key={i}
                                        sx={{
                                            backgroundColor: theme.palette[eventType][300],
                                            height: "100%",
                                            flexGrow: 1,
                                        }}
                                    />
                                ))}
                            </Box>
                            <Box
                                id={date.toISOString() + "-more-events-btn"}
                                onClick={e => {
                                    setAnchorEl(e.currentTarget);
                                    setEventsToRender(dayEvents);
                                }}
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    padding: "4px 8px",
                                    border: "none!important",
                                    width: "100%",
                                    height: "100%",
                                    cursor: "pointer",
                                    position: "relative",
                                    zIndex: 2,
                                }}>
                                <Typography
                                    fontInter
                                    variant="subtitle3"
                                    sx={{
                                        color: theme.palette.neutral[700],
                                        fontWeight: "700 !important",
                                        fontSize: "12px !important",
                                        maxWidth: "100%",
                                    }}>
                                    {dayEvents.length}
                                </Typography>
                            </Box>
                        </Box>
                    )}
                </>
            ) : null;
        },
        [props.events],
    );

    useEffect(() => {
        if (date) {
            props.onRangeChange?.(date);
        }
    }, [date]);

    return (
        <Box sx={{width: "100%"}}>
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    flexDirection: "column",
                    gap: 2,
                    marginBottom: 2,
                    "@media (min-width: 600px)": {
                        flexDirection: "row",
                    },
                }}>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "flex-start",
                        alignItems: "center",
                    }}>
                    <Typography
                        fontInter
                        variant="h4"
                        sx={{
                            fontSize: "20px !important",
                            fontWeight: "600 !important",
                            color: theme.palette.secondary.main,
                        }}>
                        {label}
                    </Typography>
                </Box>
                <Box display="flex" justifyContent="flex-end">
                    <ButtonList
                        id="calendar-actions-btn"
                        color="secondary"
                        items={[
                            {
                                label: "Previous",
                                onClick: () => handleNextPrevClick(false),
                                title: "Go back",
                            },
                            {
                                label: "Current",
                                onClick: goToToday,
                                title: "Go to today",
                            },
                            {
                                label: "Next",
                                onClick: handleNextPrevClick,
                                title: "Go to next",
                            },
                        ]}
                    />
                </Box>
                {/* <Grid item display="flex" flexDirection="row">
                    <Button id="month" onClick={() => setView("month")}>
                        Month
                    </Button>
                    <Button id="week" onClick={() => setView("month")}>
                        Week
                    </Button>
                    <Button id="work_week" onClick={() => setView("month")}>
                        Work Week
                    </Button>
                </Grid> */}
            </Box>
            <ReactCalendar
                className="cp_calendar"
                activeStartDate={date}
                onActiveStartDateChange={({activeStartDate}) => {
                    setDate(activeStartDate as Date);
                }}
                view={view}
                tileContent={props.tileContent || renderTileContent}
                tileClassName="calendar-tile not-interactive"
                calendarType="gregory"
                {...props}
            />
            {props.colorLegend && (
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2,
                        padding: "12px 16px 24px 16px",
                        flexWrap: "wrap",
                    }}>
                    {props.colorLegend.map(legend => (
                        <Box
                            key={legend.color}
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                            }}>
                            <Box
                                sx={{
                                    width: "16px",
                                    height: "16px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}>
                                <Box
                                    sx={{
                                        backgroundColor: theme.palette[legend.color][100],
                                        border: `1px solid ${theme.palette[legend.color][400]}`,
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                    }}
                                />
                            </Box>
                            <Typography
                                fontInter
                                variant="subtitle3"
                                sx={{
                                    color: theme.palette.neutral[700],
                                    fontWeight: "500 !important",
                                }}>
                                {legend.label}
                            </Typography>
                        </Box>
                    ))}
                </Box>
            )}
            {!!anchorEl && !!eventsToRender && (
                <CustomPopover
                    id={"more-events-popover"}
                    open={!!anchorEl}
                    onClose={() => {
                        setAnchorEl(null);
                        setEventsToRender(null);
                    }}
                    anchorEl={anchorEl}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                    }}
                    sx={{
                        "& .MuiPopover-paper": {
                            borderRadius: 1,
                            padding: "16px 8px",
                            boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.15)",
                        },
                    }}
                    content={<MoreEventsPopover events={eventsToRender} />}
                />
            )}
        </Box>
    );
};

export default Calendar;
