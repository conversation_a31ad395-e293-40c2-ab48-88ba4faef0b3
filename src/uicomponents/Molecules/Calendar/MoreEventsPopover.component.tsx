import AddToPhotosOutlined from "@mui/icons-material/AddToPhotosOutlined";
import {useTheme} from "@mui/material";
import {Palette} from "@mui/material/styles/createPalette";
import {DATE_FULL_SHORT_MONTH, formatDate} from "../../../utils/formatDate";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../Avatar/avatarV2.component";
import type {ICalendarEvent} from "./Calendar.component";

export default function MoreEventsPopover({events}: {events: ICalendarEvent[]}) {
    const theme = useTheme();

    const assertBadgeByColor = (color: keyof Palette) => {
        switch (color) {
            case "secondary":
                return "Next Payment";
            case "success":
                return "Renewal";
            case "error":
                return "Expiration Date";
            case "warning":
                return "Notice Date";
            default:
                return "Add-on";
        }
    };

    const getBadges = (event: ICalendarEvent) => {
        const badges: JSX.Element[] = [];
        if (!!event.hasAddons) {
            badges.push(
                <Badge
                    color="purple"
                    size={14}
                    sx={{
                        span: {
                            color: `${theme.palette.purple[500]}!important`,
                        },
                    }}>
                    <AddToPhotosOutlined
                        color="purple"
                        sx={{
                            width: "13px",
                            height: "13px",
                        }}
                    />
                </Badge>,
            );
        }
        if (event.type === "addon") {
            badges.push(
                <Badge color="purple" size={14}>
                    Add-on
                </Badge>,
            );
        }
        if (!!event.extraEvents?.length) {
            event.extraEvents.forEach(extraEvent => {
                badges.push(
                    <Badge color={extraEvent} size={14}>
                        {assertBadgeByColor(extraEvent)}
                    </Badge>,
                );
            });
        }
        if (!!event.color) {
            badges.push(
                <Badge color={event.color} size={14}>
                    {assertBadgeByColor(event.color)}
                </Badge>,
            );
        }
        return badges;
    };

    return (
        <Box>
            <Typography
                fontInter
                variant="subtitle2"
                sx={{
                    color: theme.palette.secondary.main,
                    fontWeight: "500 !important",
                }}>
                Important Events: {formatDate(events[0].date, DATE_FULL_SHORT_MONTH)}
            </Typography>
            {events.map(event => (
                <Box
                    key={event.id + "-expanded-view"}
                    onClick={() => {
                        if (event.onClick) {
                            const modalBackdrop = document.getElementsByClassName(
                                "MuiModal-backdrop",
                            )[0] as HTMLElement;
                            modalBackdrop.click();
                            event.onClick(event.id);
                        }
                    }}
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2,
                        padding: 1,
                        cursor: event.onClick ? "pointer" : "default",
                        "&:hover": {
                            background: theme.palette.secondary[100],
                        },
                    }}>
                    <AvatarComponentV2
                        isCompany
                        showProfileType
                        profileTypeMinimized
                        user={event.company}
                        size={32}
                        disableLink
                    />
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 1,
                        }}>
                        <Typography
                            fontInter
                            variant="subtitle3"
                            sx={{
                                color: theme.palette.secondary.main,
                                fontWeight: "700 !important",
                            }}>
                            {event.product?.name ?? event.name}
                        </Typography>
                        <Typography
                            fontInter
                            variant="subtitle3"
                            sx={{
                                color: theme.palette.secondary.main,
                                fontWeight: "400 !important",
                            }}>
                            {event.company?.name}
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: "4px",
                            flexWrap: "wrap",
                        }}>
                        {getBadges(event)}
                    </Box>
                </Box>
            ))}
        </Box>
    );
}
