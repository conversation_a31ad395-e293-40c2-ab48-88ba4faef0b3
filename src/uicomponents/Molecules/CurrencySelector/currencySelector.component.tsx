import {Attach<PERSON><PERSON>} from "@mui/icons-material";
import {SxProps, Theme, useTheme} from "@mui/material";
import {useEffect, useState} from "react";
import {useFormContext} from "react-hook-form";
import useCurrency from "../../../hooks/useCurrency";
import {generateSxFromProps} from "../../../utils/sx.util";
import Typography from "../../Atoms/Typography/Typography.component";
import AutoCompleteComponent, {IAutocompleteOption} from "../Autocomplete/Autocomplete.component";
import MenuButton, {IMenuButtonItem} from "../MenuButton/menuButton.component";

interface Props {
    id?: string;
    asInput?: boolean;
    sx?: SxProps<Theme>;
    readOnly?: boolean;
    helperText?: string;
    onChange?: (currency: string) => void;
    companyId?: string;
}

export default function CurrencySelector({asInput, id, sx, readOnly, helperText, onChange, companyId}: Props) {
    const {allCurrencies, companyCurrency} = useCurrency({companyId});
    const companyCurrencyAsItem = companyCurrency
        ? {
              id: companyCurrency.id,
              name: companyCurrency.name,
              children: companyCurrency.name,
          }
        : null;
    const [selectedCurrency, setSelectedCurrency] = useState<IMenuButtonItem | null>(companyCurrencyAsItem);
    const finalId = id || "currency-selector";
    const formMethods = useFormContext();
    const theme = useTheme();
    const inputValue = formMethods?.watch(finalId);

    const currencyMenuItems: IMenuButtonItem[] = allCurrencies.map(c => ({
        id: c.id,
        name: c.name,
        children: c.name,
    }));
    const currencyAutocompleteOptions: IAutocompleteOption[] = allCurrencies.map(c => ({
        id: c.id,
        label: `${c.key} ${c.symbol}`,
    }));

    useEffect(() => {
        if (selectedCurrency?.id && formMethods?.setValue && !asInput) {
            formMethods.setValue(finalId, selectedCurrency.id);
            onChange?.(selectedCurrency.id);
        }
    }, [selectedCurrency]);

    return (
        <>
            {asInput ? (
                <AutoCompleteComponent
                    id={finalId}
                    key={inputValue}
                    options={currencyAutocompleteOptions}
                    label="Currency"
                    validateOnTheFly
                    isOptionEqualToValue={(option, value) => {
                        const valueAsString = String(value);
                        return option.id === valueAsString || option.label === valueAsString;
                    }}
                    onValueChange={(_, value: string) => {
                        const currency = allCurrencies.find(c => c.id === value);
                        setSelectedCurrency({
                            id: currency?.id || "",
                            name: currency?.name || "",
                            children: currency?.name || "",
                        });
                        onChange?.(currency?.id ?? "");
                    }}
                    readOnly={readOnly}
                    sx={sx}
                    helperText={helperText}
                    disableClearable
                />
            ) : (
                <MenuButton
                    id={finalId}
                    showDropdownIcon
                    buttonVariant="text"
                    items={currencyMenuItems}
                    selectedItem={selectedCurrency}
                    onChange={item => setSelectedCurrency(item as IMenuButtonItem)}
                    buttonProps={{
                        color: "neutral",
                        disabled: readOnly,
                        sx: {
                            padding: "4px 8px!important",
                            fontSize: "11px",
                            color: "neutral.600",
                            fontWeight: "600",
                            display: "flex",
                            alignItems: "center",
                            lineHeight: "11px",
                            ...generateSxFromProps(sx, theme),
                        },
                    }}>
                    <AttachMoney
                        sx={{
                            width: "16px",
                            height: "16px",
                            color: "neutral.500",
                        }}
                    />
                    Currency:{" "}
                    <Typography fontSize={11} weight={700} color="neutral.700" lineHeight="11px" marginLeft="4px">
                        {selectedCurrency?.name}
                    </Typography>
                </MenuButton>
            )}
        </>
    );
}
