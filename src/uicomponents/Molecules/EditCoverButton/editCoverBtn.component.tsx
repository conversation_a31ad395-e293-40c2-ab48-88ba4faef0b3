import InsertPhotoOutlinedIcon from "@mui/icons-material/InsertPhotoOutlined";
import {Tooltip, useTheme} from "@mui/material";
import {IIcons} from "../../../models/Icons.interface";
import IconButton from "../../Atoms/IconButton/IconButton.Component";

interface IProps extends IIcons {
    hasShadow?: boolean;
}
export function EditCoverButton({hasShadow, ...props}: IProps) {
    const {showTooltip, tooltipTitle, tooltipPlacement = "right"} = props;
    const theme = useTheme();
    return (
        <>
            {showTooltip ? (
                <Tooltip title={tooltipTitle} placement={tooltipPlacement}>
                    <div>
                        <IconButton
                            color="blue"
                            id="back"
                            type="button"
                            sx={{
                                backgroundColor: "white",
                                "&:hover": {
                                    backgroundColor: "white",
                                },
                                padding: "8px !important",
                                width: "40px",
                                height: "40px",
                            }}
                            onClick={props.onClick}
                            hasShadow={hasShadow}
                            className={props.className}>
                            <InsertPhotoOutlinedIcon
                                sx={{
                                    width: `${props.size - 16}px !important`,
                                    height: `${props.size - 16}px !important`,
                                }}
                                color={theme.palette.blue[800]}
                            />
                        </IconButton>
                    </div>
                </Tooltip>
            ) : (
                <IconButton
                    color="blue"
                    id="back"
                    type="button"
                    sx={{
                        backgroundColor: "white",
                        "&:hover": {
                            backgroundColor: "white",
                        },
                        padding: "8px !important",
                        width: "40px",
                        height: "40px",
                    }}
                    onClick={props.onClick}
                    hasShadow={hasShadow}
                    className={props.className}>
                    <InsertPhotoOutlinedIcon width={props.size - 16} color={theme.palette.blue[800]} />
                </IconButton>
            )}
        </>
    );
}
