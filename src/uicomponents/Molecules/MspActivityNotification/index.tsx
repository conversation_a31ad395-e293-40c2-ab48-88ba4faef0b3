import Typography from "../../Atoms/Typography/Typography.component";
import styles from "./MspActivityNotification.module.sass";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import PostAddIcon from "@mui/icons-material/PostAdd";

interface IProps {
    text: string;
    customIcon?: any;
    bgColor?: string;
    textColor?: string;
}
export default function MspActivityNotification(props: IProps) {
    return (
        <div
            className={styles.container}
            style={{
                backgroundColor: props.bgColor || "#FCF0EC",
            }}
            title="View Portal">
            <Typography
                sx={{
                    color: props.textColor || "#E4514A",
                }}
                className={styles.notificationText}
                variant="body2"
                fontSize="14px !important"
                fontWeight="500 !important">
                {props.customIcon ? props.customIcon : <PostAddIcon className={`${styles.notificationText} me-1`} />}
                {props.text}
            </Typography>
            <KeyboardArrowRightIcon
                className={styles.notificationText}
                style={{
                    color: props.textColor || "#E4514A",
                }}
            />
        </div>
    );
}
