/**
 * Component used to display information about a company in a tag-like format.
 *
 * @param {object} company - Object containing information about the company.
 * @param {function} onDelete - Callback function triggered when the delete icon is clicked, which receives the company ID and the company object as arguments.
 * @param {object} sx - Custom style properties to style the component.
 * @returns {JSX.Element} Company tag component.
 * @note The delete button is only displayed when the onDelete method is provided.
 */
import {Box, IconButton, SxProps, Theme, useTheme} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import AvatarComponentV2 from "../Avatar/avatarV2.component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps {
    product: any;
    onDelete?: (company_id: string, company: any) => void;
    sx?: SxProps<Theme>;
    hideAvatar?: boolean;
    avatarSize?: number;
    tagLabelSx?: SxProps<Theme>;
}

const ProductInputTag = ({product, onDelete, sx = {}, hideAvatar = false, avatarSize = 32, tagLabelSx}: IProps) => {
    const theme = useTheme();
    return (
        <>
            <Box
                title={product?.name || product.product_name}
                sx={{
                    backgroundColor: theme.palette.neutral[100],
                    borderColor: theme.palette.blue[300],
                    borderStyle: "solid",
                    borderWidth: "1px",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    padding: "4px",
                    gap: 2,
                    overflow: "hidden",
                    ...(sx || {}),
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 1,
                        alignItems: "center",
                        "& span": {
                            fontSize: "14px!important",
                            color: theme.palette.blue[800],
                            fontWeight: 700,
                        },
                    }}>
                    {!hideAvatar && (
                        <AvatarComponentV2
                            isCompany
                            size={avatarSize}
                            showProfileType
                            profileTypeMinimized
                            disableLink
                            isRedirectEnabled={false}
                            user={product.parent}
                        />
                    )}
                    <Typography fontInter variant="body4" sx={tagLabelSx}>
                        {product?.name || product.product_name}
                    </Typography>
                    {!!onDelete && (
                        <Box sx={{display: "flex", alignItems: "center"}}>
                            <IconButton
                                sx={{
                                    display: "flex",
                                    backgroundColor: "#fff !important",
                                    fontSize: "20px",
                                    "& svg": {
                                        width: "18px !important",
                                        height: "18px !important",
                                        fill: theme.palette.blue[800],
                                    },
                                }}
                                title="Remove vendor from list"
                                onClick={() => onDelete(product?.id, product)}
                                color="blue">
                                <ClearIcon />
                            </IconButton>
                        </Box>
                    )}
                </Box>
            </Box>
        </>
    );
};

export default ProductInputTag;
