import EditOutlined from "@mui/icons-material/EditOutlined";
import {useTheme} from "@mui/material/styles";
import {useEffect, useRef, useState} from "react";
import {useFormContext} from "react-hook-form";
import formatString, {CAPITALIZE_FIRST} from "../../../utils/formatString.util";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography, {ITypographyProps} from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";

interface IProps {
    id: string;
    text?: string;
    typographyProps?: ITypographyProps;
    label?: string;
    prepend?: string;
    onSave?: (newValue: string) => void;
    editable?: boolean;
    loading?: boolean;
    isRequired?: boolean;
    prependInputText?: string;
}

const EditableTypography = ({editable = false, ...props}: IProps) => {
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const theme = useTheme();
    const {setValue, watch, trigger} = useFormContext();
    const previousLoadingState = useRef<boolean>(false);

    useEffect(() => {
        if (props.loading !== previousLoadingState.current && isEditing && !props.loading) {
            setIsEditing(false);
        }
        previousLoadingState.current = Boolean(props.loading);
    }, [props.loading]);

    useEffect(() => {
        if (isEditing) {
            setValue(props.id, props.text);
        }
    }, [isEditing]);

    if (isEditing)
        return (
            <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                <TextBoxComponent
                    id={props.id}
                    value={props.text}
                    label={props.label}
                    isRequired={props.isRequired}
                    validateOnTheFly
                    tooltipHelperText={
                        props.isRequired
                            ? `${
                                  props.label
                                      ? formatString(props.label?.replaceAll("*", ""), CAPITALIZE_FIRST)
                                      : "This field"
                              } is required`
                            : ""
                    }
                    prependInputText={props.prependInputText}
                />
                <Button
                    loading={props.loading}
                    variant="contained"
                    color="secondary"
                    id={`save_edit_${props.id}`}
                    onClick={async () => {
                        const passedValidation = await trigger(props.id);
                        if (!passedValidation) return;
                        props.onSave?.(watch(props.id));
                    }}>
                    Save
                </Button>
                <Button
                    disabled={props.loading}
                    variant="text"
                    color="secondary"
                    id={`cancel_edit_${props.id}`}
                    onClick={() => {
                        setIsEditing(false);
                    }}>
                    Cancel
                </Button>
            </Box>
        );
    return (
        <Box display="flex" flexDirection="row" alignItems="center">
            <Typography {...(props.typographyProps || {})}>
                {props.prepend}
                {props.text}
            </Typography>
            {editable && !props.loading && (
                <Box sx={{cursor: "pointer"}} onClick={() => setIsEditing(true)}>
                    <EditOutlined sx={{marginX: 1}} htmlColor={theme.palette.neutral[500]} />
                </Box>
            )}
        </Box>
    );
};

export default EditableTypography;
