import {useFormContext} from "react-hook-form";
import Switch from "../../Atoms/Switch/Switch.component";
import {SwitchProps} from "@mui/material/Switch";

interface IProps extends SwitchProps {
    testid?: string;
    validateOnTheFly?: boolean;
    isRequired?: boolean;
    id: string;
    keepRegistered?: boolean;
}

const RegisteredSwitch = (props: IProps) => {
    const {isRequired, validateOnTheFly, ...switchProps} = props;
    const testid = props.testid ? props.testid.replace(/\s/g, "") : `${props.id}-${props.value}`;
    const {register, formState, setValue, watch} = useFormContext();
    const checked = !!watch(props.id);
    const hasSubmitted = formState.isSubmitted;
    const isInvalid = validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : hasSubmitted && formState.errors[props.id]
        ? true
        : false;

    return (
        <Switch
            {...register(props.id, {
                required: isRequired,
                disabled: props.disabled,
                shouldUnregister: !props.keepRegistered,
            })}
            aria-invalid={isInvalid}
            data-test-id={testid}
            checked={checked}
            {...switchProps}
            onChange={(_, checked) => setValue(props.id, checked)}
        />
    );
};

export default RegisteredSwitch;
