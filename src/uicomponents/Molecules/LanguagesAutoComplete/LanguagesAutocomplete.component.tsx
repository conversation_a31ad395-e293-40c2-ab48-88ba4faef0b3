import ErrorOutlined from "@mui/icons-material/ErrorOutlined";
import {useEffect, useMemo, useRef, useState} from "react";
import {useFormContext} from "react-hook-form";
import {useDebounce} from "../../../hooks/useDebounce";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";
import useSettings from "../../../hooks/useSettings";

interface IProps extends IAutoCompleteProps<any, any, any, any> {
    exclude?: string[];
    noResultsCallback?: (isNoResults: boolean, id: string, searchValue: string) => void;
    clearOnSelect?: boolean;
    onValueChange?: (id: string, oldValue: ILanguageOption, newValue: ILanguageOption) => void;
    defaultSearch?: string;
    defaultOptions?: any[];
    preventCloseOnSelect?: boolean;
    showCheckboxes?: boolean;
    noOptionsContent?: string | React.ReactNode | undefined;
}

export interface ILanguageOption {
    id: string;
    label: string;
    checkbox?: {show?: boolean; checked?: boolean};
}

/**
 * LanguageAutocomplete Component
 *
 * This component provides an autocomplete search functionality for languages. It fetches all languages and allows the user to search
 *
 * Props:
 *  - noResultsCallback?: (isNoResults: boolean, id: string, searchValue: string) => any - Callback function triggered when no search results are found.
 *  - clearOnSelect?: boolean - Determines whether the input field should be cleared on selecting an option.
 *  - onValueChange?: any - Callback function triggered when the selected value changes.
 *  - defaultSearch?: string - Default search query.
 *  - defaultOptions?: any[] - Default options to be displayed.
 *  - preventCloseOnSelect?: boolean - Prevents the autocomplete dropdown from closing when an option is selected.
 *  - showCheckboxes?: boolean - Determines whether checkboxes should be displayed for single select.
 *
 * @component
 * @param {IProps} props - Component props
 * @returns {JSX.Element} - Rendered LanguagesAutocomplete component
 */
const LanguagesAutocomplete = ({validateOnTheFly, clearOnSelect, onValueChange, ...props}: IProps) => {
    const [search, setSearch] = useState<string | undefined>(props.defaultSearch || undefined);
    const [hideSelect, setHideSelect] = useState<boolean>(false);
    const [isOpen, setOpen] = useState<boolean>(false);
    const inputChanged = useRef(false);
    const {settings, getAllLanguages} = useSettings();
    const {watch, formState} = useFormContext();
    const value = watch(props.id);
    const debouncedSearch = useDebounce(search);
    const isInvalid = validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : formState.isSubmitted && formState.errors[props.id]
        ? true
        : false;
    const options: ILanguageOption[] = useMemo(
        () =>
            (settings.languages || [])
                .filter(l => (props.exclude ? !props.exclude.includes(l.id) : true))
                .map((l: any) => {
                    return {
                        label: l.language,
                        id: l.id,
                        checkbox: {
                            show: true,
                            checked: Array.isArray(value) ? value.find(i => i.id === l.id) : value === l.id,
                        },
                    };
                }),
        [settings.languages, props.exclude, props.showCheckboxes],
    );

    const handleInputChange = e => {
        if (!e?.target) return;
        setSearch(e?.target?.value || "");
    };

    useEffect(() => {
        if (debouncedSearch) {
            setOpen(true);
        } else {
            setHideSelect(false);
        }
    }, [debouncedSearch]);

    useEffect(() => {
        getAllLanguages();
    }, []);

    return (
        <AutoCompleteComponent
            {...props}
            options={options}
            placeholder={props.placeholder || "Select languages supported"}
            onInputChange={handleInputChange}
            loading={!settings.countries?.length}
            inputValue={search || props.inputValue || ""}
            onValueChange={(id, value1, value2) => {
                onValueChange?.(id, value1, value2);
                if (props.inputValue) inputChanged.current = true;
                if (search && clearOnSelect) {
                    setSearch("");
                }
            }}
            noOptionsText={debouncedSearch ? props?.noOptionsContent || "No Options" : "Start typing to select"}
            onClose={(_, reason) => {
                if (props.preventCloseOnSelect && reason === "selectOption") return;
                setOpen(false);
            }}
            onOpen={() => setOpen(true)}
            onBlur={() => setHideSelect(false)}
            open={hideSelect ? false : isOpen}
            componentsProps={{
                popper: {
                    open: hideSelect ? false : isOpen,
                    sx: {
                        "& .MuiAutocomplete-noOptions": {
                            padding: "8px !important",
                            borderRadius: "8px",
                        },
                    },
                },
            }}
            InputProps={{
                endAdornment: isInvalid ? (
                    <CPTooltip title="Please search for and select a language">
                        <ErrorOutlined color="error" />
                    </CPTooltip>
                ) : undefined,
                ...props?.InputProps,
            }}
        />
    );
};

LanguagesAutocomplete.defaultProps = {
    renderInput: undefined,
    options: [],
    variant: "default",
    clearOnSelect: true,
};

export default LanguagesAutocomplete;
