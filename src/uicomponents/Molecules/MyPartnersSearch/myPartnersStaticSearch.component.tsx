import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {useEffect, useMemo, useState} from "react";
import {uniqueId} from "react-bootstrap-typeahead/types/utils";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IPageInnerHeadFilter} from "../../../Interfaces/filters.interface";
import {IContactListPartnership} from "../../../Interfaces/massMessaging.interface";
import {noResultsFoundOnSearchOrFilter} from "../../../constants/commonStrings.constant";
import useFilters from "../../../hooks/fetches/useFilters";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useCompanyProfile from "../../../hooks/useCompanyProfile";
import {useDebounce} from "../../../hooks/useDebounce";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import PageInnerHeader from "../../Organism/PageInnerHeader/pageInnerHeader";

interface IMyPartnerSearchProps {
    isLoading?: boolean;
    partnerships: IContactListPartnership[];
    onRenderItem?: (partnership: IContactListPartnership) => React.ReactNode;
}

/**
 * Component that renders a search interface for managing partner contacts.
 *
 * This component allows users to search and filter through a STATIC list of partner contacts associated with a company.
 *
 * @param {boolean} [isLoading=false] - Flag indicating whether data is currently being loaded.
 * @param {IContactListPartnership[]} partnerships - An array of partner companies to display and filter.
 * @param {(partnership: IContactListPartnership) => React.ReactNode} [onRenderItem] - Optional custom renderer for each partner item.
 */
export default function MyPartnersStaticSearch({
    partnerships = [],
    isLoading = false,
    onRenderItem,
}: IMyPartnerSearchProps) {
    const [search, setSearch] = useState("");
    const {activeCompany} = useActiveCompany();
    const friendly_url = activeCompany?.friendly_url;
    const {companyInfo} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.CHANNEL_COMMAND_UPDATE],
            read: [PERMISSION_GROUPS.CHANNEL_COMMAND_READ],
        },
        companyFriendlyUrl: friendly_url || "",
    });
    const {filterState, filtersQuery} = useFilters({
        filterKey: "myContactListPartners",
        enabled: !!companyInfo?.id,
        queryParams: {
            company_id: companyInfo?.id ?? "",
            profile_types: "true",
        },
    });
    const [filteredPartnerships, setFilteredPartnerships] = useState<IContactListPartnership[]>(partnerships ?? []);
    const [filters, setFilters] = filterState;
    const debouncedSearch = useDebounce(search, 500);
    const debouncedFilters = useDebounce(filters, 500);
    const theme = useTheme();
    const userNotifications: any = useQuery(["user-notifications"])?.data || {};

    let allNotifications: any[] = [];
    if (userNotifications?.pages?.length) {
        userNotifications.pages.forEach((page: any) => {
            allNotifications = [...allNotifications, ...page.data];
        });
    }
    const transformFiltersQuery = (data: IPageInnerHeadFilter | undefined) => {
        const response: any = {...data};
        if (!!response?.accepted_reason) {
            response.accepted_reason.placeholder = "Partnership Status";
        }
        if (!!response?.company_type) {
            response.company_type.placeholder = "Type";
        }
        return response;
    };

    useEffect(() => {
        setFilteredPartnerships(
            partnerships
                .filter(partnership => {
                    let response = true;
                    if (
                        // Search by company.name
                        (!!debouncedSearch.length &&
                            partnership.company.name.toLowerCase().indexOf(debouncedSearch.toLowerCase()) < 0) ||
                        // Search by company_type (vendor | msp)
                        (debouncedFilters?.company_type === "vendor" && !partnership.company.type.type_is_of_vendor) ||
                        (debouncedFilters?.company_type === "msp" && !!partnership.company.type.type_is_of_vendor) ||
                        // Search by company.profile_type
                        (!!debouncedFilters?.profile_type &&
                            !!debouncedFilters?.profile_type.length &&
                            !debouncedFilters?.profile_type.includes(partnership.company.profile_type.value)) ||
                        // Search by company.partner.accepted_reason
                        (!!debouncedFilters?.accepted_reason &&
                            !!debouncedFilters?.accepted_reason.length &&
                            !!partnership.company?.partner?.accepted_reason &&
                            !debouncedFilters?.accepted_reason.includes(partnership.company.partner.accepted_reason))
                    ) {
                        response = false;
                    }

                    return response;
                })
                .sort((p1: IContactListPartnership, p2: IContactListPartnership) => {
                    if (!debouncedFilters?.sort) return 0;
                    if (debouncedFilters.sort === "sorting_name__ASC") {
                        return p1.company.name.localeCompare(p2.company.name);
                    } else {
                        return p2.company.name.localeCompare(p1.company.name);
                    }
                }),
        );
    }, [debouncedSearch, debouncedFilters, partnerships]);

    const renderItem = (partnership: IContactListPartnership) => {
        if (!!onRenderItem) return onRenderItem(partnership);
        return (
            <Box
                key={`${partnership.id}_${uniqueId()}`}
                sx={{
                    display: "flex",
                    alignItems: "center",
                    padding: "8px 24px 8px 16px",
                    gap: 2,
                    "&:hover": {
                        backgroundColor: theme.palette.blue[100],
                    },
                }}>
                <AvatarComponentV2
                    isCompany
                    user={{
                        avatar: partnership.company?.avatar ?? "",
                        type: "",
                        profile_type: partnership.company?.type?.type_is_of_vendor ? "vendor" : "msp",
                        friendly_url: partnership.company?.friendly_url,
                        name: partnership.company?.name || "",
                    }}
                    showProfileType
                    profileTypeMinimized
                    disableLink
                    size={32}
                />
                <Typography
                    fontInter
                    variant="body3"
                    sx={{
                        flex: 1,
                        alignItems: "center",
                        color: theme.palette.neutral[800],
                        fontWeight: 400,
                        lineHeight: "130%",
                    }}>
                    {partnership.company?.name || ""}
                </Typography>
            </Box>
        );
    };

    const partnerList = useMemo(() => {
        if (!filteredPartnerships.length) return <></>;
        return filteredPartnerships.map(partnership => renderItem(partnership));
    }, [filteredPartnerships]);

    return (
        <Box
            sx={{
                background: theme.palette.neutral[100],
                display: "flex",
                flexDirection: "column",
                gap: "16px",
            }}>
            <PageInnerHeader
                id="your-network"
                searchState={[search, setSearch]}
                filterState={[filters, setFilters]}
                filters={transformFiltersQuery(filtersQuery.data)}
                filterKeysOrder={["profile_type", "company_type"]}
                indicator={partnerships.length || undefined}
                searchWrapperSx={{
                    flexDirection: "column",
                    gap: "8px !important",
                    padding: "0",
                    "& > div": {
                        width: "100%",
                        justifyContent: "flex-start",
                    },
                }}
            />
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    maxHeight: "285px",
                    overflowY: "auto",
                    "&::-webkit-scrollbar": {
                        width: "8px",
                    },
                    "&::-webkit-scrollbar-track": {
                        backgroundColor: theme.palette.neutral[200],
                    },
                    "&::-webkit-scrollbar-thumb": {
                        backgroundColor: theme.palette.neutral[400],
                        borderRadius: "8px",
                    },
                }}>
                {!isLoading && !filteredPartnerships.length && (
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                        }}>
                        <Typography
                            variant="subtitle1"
                            fontInter
                            sx={{color: theme.palette.neutral[600], display: "flex", gap: "6px"}}>
                            {noResultsFoundOnSearchOrFilter}
                        </Typography>
                    </Box>
                )}
                {partnerList}
            </Box>
        </Box>
    );
}
