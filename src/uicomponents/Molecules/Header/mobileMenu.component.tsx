import ArrowRight from "@mui/icons-material/ArrowRight";
import EmailOutlined from "@mui/icons-material/EmailOutlined";
import HelpOutline from "@mui/icons-material/HelpOutline";
import MenuIcon from "@mui/icons-material/Menu";
import {Divider, useTheme} from "@mui/material";
import {ReactNode, useEffect, useRef, useState} from "react";
import {Link, useLocation} from "react-router-dom";
import CustomLink from "../../../components/Links/CustomLink.component";
import OutboundLink from "../../../components/Links/OutboundLink.component";
import CompanyType from "../../../constants/companyType.constant";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useAuthState from "../../../hooks/useAuthState";
import Badge, {IBadgeProps} from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import MenuCollapsible from "../../Atoms/Collapse/MenuCollapsible.component";
import DropdownItem from "../../Atoms/Dropdown/DropdownItem.component";
import Menu from "../../Atoms/Menu/menu.component";
import Typography from "../../Atoms/Typography/Typography.component";
import MassMessagingModal from "../../Organism/ChannelCommand/MyChannel/massMessaging.modal";
import ReferFriendDialog from "../../Organism/ReferFriendDialog/referFriendDialog.component";
import ActiveCompanyToggle from "../Sidebar/activeCompanyToggle.component";
import SidebarMenuLinks from "../Sidebar/sidebarMenuLinks.component";
import useWhitelabeling from "../../../hooks/fetches/useWhitelabeling";
import {IS_BETTERTRACKER_SITE, IS_DIRECT_IT_SITE} from "../../../constants/siteFlags";

interface IMenuLink {
    name: string;
    description: string;
    url: string;
    badge?: string;
    openInNewTab?: boolean;
    showLoggedIn: boolean;
    showForType: "msp" | "vendor" | "all" | "internal_it";
}

interface IDropdown {
    label: string;
    url?: string;
    items: IMenuLink[];
}

interface ISecondaryAction {
    text: string;
    link: string;
    badgeText?: string;
    badgeColor?: IBadgeProps["color"];
    hideBtn?: boolean;
    showLoggedIn: boolean;
}

interface ISecondaryActionRoles {
    all?: ISecondaryAction;
    vendors?: ISecondaryAction;
    msp?: ISecondaryAction;
}

interface IProps {
    dropdowns?: IDropdown[];
    secondaryAction?: ISecondaryActionRoles;
}

export default function MobileMenu(props: IProps) {
    const [openInvite, setOpenInvite] = useState(false);
    const [showMassMessageModal, setShowMassmessageModal] = useState(false);
    const [menuOpen, setMenuOpen] = useState(false);
    const theme = useTheme();
    const buttonRef = useRef<SVGSVGElement>(null);
    const remainingHeight = buttonRef.current?.getBoundingClientRect().bottom ?? 65 + 16;
    const location = useLocation();
    const handleClose = () => setMenuOpen(false);
    const {authState} = useAuthState();
    const {activeCompany, isClientMsp, userCompanies} = useActiveCompany();
    const activeCompanyType = activeCompany?.type_is_of_vendor
        ? "vendor"
        : activeCompany?.company_type === CompanyType.DIRECT
          ? "internal_it"
          : "msp";
    const isAuthenticated = !!authState.id;
    const secondaryAction = props.secondaryAction ?? {};
    const isMSP = !authState.type_is_of_vendor;
    const showSecondaryAction =
        (!authState.id && !!secondaryAction.all?.text) ||
        (authState.id && ((isMSP && !!secondaryAction.msp?.text) || !!secondaryAction.vendors?.text));
    const secondaryActionRole = !authState.id ? "all" : isMSP ? "msp" : "vendors";
    const secondaryActionItem: IMenuLink = {
        name: secondaryAction[secondaryActionRole]?.text ?? "",
        description: "",
        url: secondaryAction[secondaryActionRole]?.link ?? "",
        badge: secondaryAction[secondaryActionRole]?.badgeText ?? "",
        showLoggedIn: secondaryAction[secondaryActionRole]?.showLoggedIn ?? false,
        showForType: "all",
    };
    const {whitelabeling} = useWhitelabeling();

    const checkLinkVisibility = (link: IMenuLink) =>
        isAuthenticated
            ? !activeCompany?.id
                ? false
                : link.showLoggedIn && (link.showForType === "all" || link.showForType === activeCompanyType)
            : true;

    const dropdowns = props.dropdowns
        ?.map(dropdown => ({
            label: dropdown.label,
            url: dropdown.url,
            items: dropdown.items.filter(i => !!i.url && checkLinkVisibility(i)),
        }))
        .filter(dropdown => !!dropdown.items.length || !!dropdown.url);

    const handleClick = (action: string) => {
        switch (action) {
            case "invitePartners":
                setOpenInvite(true);
                break;
            case "massMessage":
                setShowMassmessageModal(true);
                break;
            case "publishImage":
                break;
            case "publishVideo":
                break;
            case "publishDoc":
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        setMenuOpen(false);
    }, [location]);

    return (
        <>
            <Box
                onClick={() => setMenuOpen(prev => !prev)}
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "32px",
                    maxWidth: "32px",
                    height: "32px",
                    "@media (min-width: 992px)": {
                        display: "none",
                    },
                    svg: {
                        width: "32px",
                        height: "32px",
                    },
                }}>
                <MenuIcon
                    htmlColor={IS_DIRECT_IT_SITE ? theme.palette.neutral[100] : theme.palette.secondary[800]}
                    ref={buttonRef}
                />
            </Box>
            <Menu
                open={menuOpen}
                onClose={() => setMenuOpen(false)}
                anchorEl={buttonRef.current}
                sx={{
                    ".MuiPaper-root": {
                        marginTop: 2,
                        width: "100vw",
                        maxWidth: "100vw",
                        top: `${remainingHeight}px !important`,
                        left: "0 !important",
                        height: `calc(100vh - ${remainingHeight}px)`,
                        background:
                            whitelabeling || IS_DIRECT_IT_SITE
                                ? theme.palette.neutral[100]
                                : theme.palette.secondary.main,
                        padding: "32px 24px",
                    },
                }}>
                {isAuthenticated && (
                    <Box
                        sx={{
                            marginBottom: 3,
                            "& svg": {
                                marginLeft: "auto",
                            },
                        }}>
                        <ActiveCompanyToggle collapsedState={[false, () => void 0]} />
                    </Box>
                )}
                {isAuthenticated && (
                    <SidebarMenuLinks
                        collapsed={false}
                        onClick={handleClick}
                        isClientMsp={isClientMsp}
                        isMSP={isMSP}
                        activeCompany={activeCompany}
                        userCompanies={userCompanies}
                    />
                )}
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 1,
                        marginBottom: 5,
                        "& div.MuiCollapse-wrapperInner": {
                            display: "flex",
                            flexDirection: "column",
                            gap: 1,
                        },
                    }}>
                    {activeCompanyType !== "internal_it" &&
                        showSecondaryAction &&
                        !secondaryAction[secondaryActionRole]?.hideBtn && (
                            <Box
                                sx={{
                                    display: "block",
                                    "@media (min-width: 600px)": {
                                        display: "none",
                                    },
                                }}>
                                <MobileMenuLink item={secondaryActionItem} />
                            </Box>
                        )}
                    {!isClientMsp &&
                        !IS_BETTERTRACKER_SITE &&
                        !!dropdowns?.length &&
                        dropdowns.map((dropdown, idx) =>
                            dropdown.url ? (
                                <DropdownItem
                                    key={idx}
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                        "&:hover": {
                                            backgroundColor: "transparent",
                                        },
                                    }}>
                                    <CustomLink to={dropdown.url} target="_blank">
                                        <Typography
                                            fontInter
                                            fontSize={16}
                                            color={theme.palette.neutral[100]}
                                            lineHeight={1}>
                                            {dropdown.label}
                                        </Typography>
                                    </CustomLink>
                                </DropdownItem>
                            ) : (
                                <MenuCollapsible
                                    title={dropdown.label}
                                    key={idx}
                                    wrapperOpenSx={{
                                        "&,&:hover": {
                                            backgroundColor: "blue.700",
                                        },
                                    }}>
                                    {dropdown.items.map((item, index) => (
                                        <MobileMenuLink item={item} key={index} />
                                    ))}
                                </MenuCollapsible>
                            ),
                        )}
                </Box>
                {!isClientMsp && !IS_BETTERTRACKER_SITE && (
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 1,
                        }}>
                        <Divider />
                        <MobileMenuLink
                            item={{
                                name: "Knowledge Base",
                                description: "",
                                url: "/knowledge-base",
                                badge: "",
                                showLoggedIn: true,
                                showForType: "all",
                            }}
                            onClick={handleClose}
                            customIcon={<HelpOutline htmlColor={theme.palette.neutral[500]} />}
                        />
                        <MobileMenuLink
                            item={{
                                name: "Contact Us",
                                description: "",
                                url: "/contact",
                                badge: "",
                                showLoggedIn: true,
                                showForType: "all",
                            }}
                            onClick={handleClose}
                            customIcon={<EmailOutlined htmlColor={theme.palette.neutral[500]} />}
                        />
                    </Box>
                )}
            </Menu>
            {openInvite && (
                <ReferFriendDialog
                    open={openInvite}
                    onClose={() => {
                        setOpenInvite(false);
                    }}
                    type="channelCommand"
                    company={activeCompany}
                    enableCSV
                    urlOnly
                    title="Invite to your portal"
                    usePortalOpenInvite
                />
            )}
            <MassMessagingModal modalState={[showMassMessageModal, setShowMassmessageModal]} />
        </>
    );
}

function MobileMenuLink({item, onClick, customIcon}: {item: IMenuLink; onClick?: () => void; customIcon?: ReactNode}) {
    const theme = useTheme();
    const isExternalLink = item.url.startsWith("http") || item.url.startsWith("www") || false;
    const {whitelabeling} = useWhitelabeling();

    const renderItem = () => {
        return (
            <Box>
                <Typography
                    fontInter
                    fontSize={16}
                    fontWeight={600}
                    sx={{
                        color: `${whitelabeling ? theme.palette.neutral[800] : theme.palette.neutral[100]}!important`,
                        display: "flex",
                        gap: 1,
                    }}>
                    {item.name}
                    {item.badge && (
                        <Badge
                            color="primary"
                            padding="2px 6px"
                            sx={{
                                "&, & *": {
                                    fontWeight: "700!important",
                                    color: `${theme.palette.primary[700]}!important`,
                                },
                            }}>
                            {item.badge}
                        </Badge>
                    )}
                </Typography>
                <Typography
                    fontInter
                    fontSize={14}
                    fontWeight={400}
                    maxLines={2}
                    sx={{
                        color: `${theme.palette.neutral[200]}!important`,
                    }}>
                    {item.description}
                </Typography>
            </Box>
        );
    };
    return (
        <DropdownItem
            sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                "&>*": {
                    display: "flex",
                    gap: 0.5,
                    flexDirection: "column",
                },
                "&:hover": {
                    backgroundColor: whitelabeling ? theme.palette.neutral[200] : theme.palette.blue[700],
                },
            }}>
            {location.pathname === item.url && <ArrowRight color="primary" />}
            {customIcon}
            {item.openInNewTab || isExternalLink ? (
                <OutboundLink href={item.url} target="_blank" onClick={onClick}>
                    {renderItem()}
                </OutboundLink>
            ) : (
                <Link to={item.url} onClick={onClick}>
                    {renderItem()}
                </Link>
            )}
        </DropdownItem>
    );
}
