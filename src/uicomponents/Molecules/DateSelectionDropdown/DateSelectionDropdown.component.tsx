import {useEffect, useMemo, useRef, useState} from "react";
import {DateTime} from "luxon";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import MenuButton from "../MenuButton/menuButton.component";
import useTheme from "@mui/material/styles/useTheme";
import Popover from "@mui/material/Popover";
import Box from "../../Atoms/Box/Box.component";
import DatePicker from "../../Atoms/DatePicker/DatePicker.component";

type DateOptionTypes =
    | "this-week"
    | "last-week"
    | "last-2-weeks"
    | "this-month"
    | "last-month"
    | "this-quarter"
    | "last-quarter"
    | "this-year"
    | "last-year"
    | "last-30-days"
    | "past-12-months"
    | "custom";

interface IProps {
    id: string;
    defaultValue?: DateOptionTypes;
    options: DateOptionTypes[];
    onChange?: (selectedOption: {id: string; children: string}, dateRange: {start: string; end: string}) => void;
}

const DATE_OPTION_LABELS: Record<DateOptionTypes, string> = {
    "this-week": "This Week",
    "last-week": "Last Week",
    "last-2-weeks": "Last 2 Weeks",
    "this-month": "This Month",
    "last-month": "Last Month",
    "this-quarter": "This Quarter",
    "last-quarter": "Last Quarter",
    "this-year": "This Year",
    "last-year": "Last Year",
    "last-30-days": "Last 30 Days",
    "past-12-months": "Past 12 Months",
    custom: "Custom",
};

const getDateRange = (option: DateOptionTypes) => {
    const now = DateTime.now();

    switch (option) {
        case "this-week":
            return {
                start: now.startOf("week").toUTC().toISO(),
                end: now.endOf("week").toUTC().toISO(),
            };
        case "last-week":
            return {
                start: now.minus({weeks: 1}).startOf("week").toUTC().toISO(),
                end: now.minus({weeks: 1}).endOf("week").toUTC().toISO(),
            };
        case "last-2-weeks":
            return {
                start: now.minus({weeks: 2}).startOf("week").toUTC().toISO(),
                end: now.minus({weeks: 1}).endOf("week").toUTC().toISO(),
            };
        case "this-month":
            return {
                start: now.startOf("month").toUTC().toISO(),
                end: now.endOf("month").toUTC().toISO(),
            };
        case "last-month":
            return {
                start: now.minus({months: 1}).startOf("month").toUTC().toISO(),
                end: now.minus({months: 1}).endOf("month").toUTC().toISO(),
            };
        case "this-quarter":
            return {
                start: now.startOf("quarter").toUTC().toISO(),
                end: now.endOf("quarter").toUTC().toISO(),
            };
        case "last-quarter":
            return {
                start: now.minus({quarters: 1}).startOf("quarter").toUTC().toISO(),
                end: now.minus({quarters: 1}).endOf("quarter").toUTC().toISO(),
            };
        case "this-year":
            return {
                start: now.startOf("year").toUTC().toISO(),
                end: now.endOf("year").toUTC().toISO(),
            };
        case "last-year":
            return {
                start: now.minus({years: 1}).startOf("year").toUTC().toISO(),
                end: now.minus({years: 1}).endOf("year").toUTC().toISO(),
            };
        case "last-30-days":
            return {
                start: now.minus({days: 30}).startOf("day").toUTC().toISO(),
                end: now.endOf("day").toUTC().toISO(),
            };
        case "past-12-months":
            return {
                start: now.minus({months: 12}).startOf("day").toUTC().toISO(),
                end: now.endOf("day").toUTC().toISO(),
            };
        case "custom":
        default:
            return {start: "", end: ""};
    }
};

export const getDateSelectionOptionFromRange = (start?: string, end?: string): DateOptionTypes => {
    if (!start || !end) return "custom";
    const startDate = DateTime.fromISO(start).startOf("day");
    const endDate = DateTime.fromISO(end).endOf("day");
    const now = DateTime.now().toUTC();

    const predefinedRanges: Record<DateOptionTypes, {start: DateTime; end: DateTime}> = {
        "this-week": {start: now.startOf("week"), end: now.endOf("week")},
        "last-week": {start: now.minus({weeks: 1}).startOf("week"), end: now.minus({weeks: 1}).endOf("week")},
        "last-2-weeks": {start: now.minus({weeks: 2}).startOf("week"), end: now.minus({weeks: 1}).endOf("week")},
        "this-month": {start: now.startOf("month"), end: now.endOf("month")},
        "last-month": {start: now.minus({months: 1}).startOf("month"), end: now.minus({months: 1}).endOf("month")},
        "this-quarter": {start: now.startOf("quarter"), end: now.endOf("quarter")},
        "last-quarter": {
            start: now.minus({quarters: 1}).startOf("quarter"),
            end: now.minus({quarters: 1}).endOf("quarter"),
        },
        "this-year": {start: now.startOf("year"), end: now.endOf("year")},
        "last-year": {start: now.minus({years: 1}).startOf("year"), end: now.minus({years: 1}).endOf("year")},
        "last-30-days": {start: now.minus({days: 30}), end: now},
        "past-12-months": {start: now.minus({months: 12}).startOf("day"), end: now},
        custom: {start: DateTime.fromISO(""), end: DateTime.fromISO("")},
    };

    for (const [key, range] of Object.entries(predefinedRanges)) {
        if (startDate.hasSame(range.start, "day") && endDate.hasSame(range.end, "day")) {
            return key as DateOptionTypes;
        }
    }

    return "custom";
};

const DateSelectionDropdown = ({defaultValue = "this-month", options, onChange, id}: IProps) => {
    const theme = useTheme();
    const [selectedOption, setSelectedOption] = useState<{id: DateOptionTypes; children: string}>({
        id: defaultValue,
        children: DATE_OPTION_LABELS[defaultValue],
    });
    const [selectedDateRange, setSelectedDateRange] = useState(getDateRange(defaultValue));
    const [showPopover, setShowPopover] = useState<boolean>(false);
    const menuBtnWrapperRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const newDateRange = selectedOption?.id === "custom" ? selectedDateRange : getDateRange(selectedOption?.id);
        setSelectedDateRange(newDateRange);
        onChange?.(selectedOption, newDateRange);
    }, [selectedOption]);

    const handleCustomChange = (type: "start" | "end", newDate: string) => {
        const newSelectedRange = {...selectedDateRange, [type]: newDate};
        setSelectedDateRange(newSelectedRange);
        if (newSelectedRange.start && newSelectedRange.end) {
            onChange?.(selectedOption, newSelectedRange);
        }
    };

    const dateOptions = useMemo(
        () => options.map(option => ({id: option, children: DATE_OPTION_LABELS[option]})),
        [options],
    );

    return (
        <ErrorBoundary>
            <Box ref={menuBtnWrapperRef}>
                <MenuButton
                    id={id}
                    items={dateOptions}
                    buttonVariant="text"
                    showDropdownIcon
                    onChange={(v: any) => {
                        if (!v?.id) return;
                        setSelectedOption(v);
                        if (v.id === "custom") {
                            setShowPopover(true);
                        }
                    }}
                    buttonProps={{
                        color: "blue",
                        sx: {color: theme.palette.blue[600], padding: 0.5},
                    }}>
                    {selectedOption?.children}
                </MenuButton>
            </Box>
            <Popover
                id={`date-range-popover-${id}`}
                anchorEl={menuBtnWrapperRef.current}
                anchorOrigin={{vertical: "bottom", horizontal: "left"}}
                open={showPopover}
                onClose={() => setShowPopover(false)}>
                <Box display="flex" flexDirection="row" gap={1} padding={2}>
                    <DatePicker
                        label="Start Date"
                        id={"start__" + id}
                        value={selectedDateRange.start}
                        onChange={newDate => {
                            handleCustomChange("start", newDate);
                        }}
                    />
                    <DatePicker
                        label="End Date"
                        id={"end__" + id}
                        value={selectedDateRange.end}
                        onChange={newDate => {
                            handleCustomChange("end", newDate);
                        }}
                    />
                </Box>
            </Popover>
        </ErrorBoundary>
    );
};

export default DateSelectionDropdown;
