import {SxProps, Theme, useTheme} from "@mui/material";
import Avatar from "@mui/material/Avatar";
import Badge from "@mui/material/Badge";
import {CSSProperties, ReactNode, useCallback, useState} from "react";
import {Link} from "react-router-dom";
import type {IProfile} from "../../../Interfaces/people.interface";
import {MSP_BRAND_TEXT} from "../../../constants/commonStrings.constant";
import CompanyType from "../../../constants/companyType.constant";
import routeConfig from "../../../constants/routeConfig";
import {VENDOR_TYPES} from "../../../constants/vendorProfiles.constants";
import {COMPANY_TYPE} from "../../../enums/companyTypes.enum";
import {USERS_TYPE} from "../../../enums/usersTypes.enum";
import {createImageFromInitials} from "../../../utils/imageFromText.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import styles from "./avatarV2.module.sass";

export interface IAvatarV2Props {
    isCompany: boolean;
    user?:
        | IProfile
        | {
              avatar: string;
              type: string;
              is_distributor?: boolean;
              profile_type?: string | {value: string; label: string};
              friendly_url: string;
              name: string;
              parent_id?: string;
              is_mdf?: boolean;
          };
    size: number;
    showProfileType?: boolean;
    profileTypeMinimized?: boolean;
    origin?: {vertical: "top" | "bottom"; horizontal: "left" | "right"};
    disableLink?: boolean;
    aligned?: "right" | "bottom" | "left";
    isRedirectEnabled?: boolean;
    isMSPClientPage?: boolean;
    border?: string;
    statusBadge?: string | ReactNode;
    target?: string;
    sx?: SxProps<Theme>;
    containerSx?: SxProps<Theme>;
    badgeSx?: SxProps<Theme>;
    title?: string;
    onClick?: () => void;
    style?: CSSProperties;
    badgeImageStyle?: CSSProperties;
}

const aligned = {
    right: {
        bottom: "50%",
        right: "5%",
    },
    left: {
        bottom: "50%",
        right: "95%",
    },
    bottom: {
        bottom: 5,
        right: "50%",
    },
};

const compensationAligned = (size: number) => ({
    right: `${size / 2}px`,
    left: `${size / 2}px`,
    bottom: "5.2px",
});

/**
 * Returns an object with the styles for each type of entity
 */
const getStylesByTypeObject = (theme: Theme, size: number) => {
    const avatarBorderWidth = size >= 80 ? "3px" : size >= 48 ? "2px" : "1px";
    return {
        MSP: {
            badgeContentStyle: {
                background: `linear-gradient(99deg, #F48D42 13.14%, #FF6120 52.3%)`,
                borderColor: theme.palette.primary[500],
                color: theme.palette.neutral[100],
            },
            avatarColor: theme.palette.primary[200],
            avatarTextColor: theme.palette.primary[700],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.primary[500]}`,
        },
        Customer: {
            badgeContentStyle: {
                backgroundColor: theme.palette.primary[100],
                borderColor: theme.palette.primary[200],
                color: theme.palette.primary[600],
            },
            avatarColor: theme.palette.primary[200],
            avatarTextColor: theme.palette.primary[700],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.primary[200]}`,
        },
        Location: {
            badgeContentStyle: {
                backgroundColor: theme.palette.neutral[200],
                borderColor: theme.palette.neutral[600],
                color: theme.palette.neutral[600],
            },
            avatarColor: theme.palette.primary[200],
            avatarTextColor: theme.palette.primary[700],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.neutral[500]}`,
        },
        Corporation: {
            badgeContentStyle: {
                backgroundColor: theme.palette.neutral[700],
                borderColor: theme.palette.neutral[700],
                color: theme.palette.neutral[100],
            },
            avatarColor: theme.palette.neutral[700],
            avatarTextColor: theme.palette.neutral[100],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.neutral[600]}`,
        },
        Marketplace: {
            badgeContentStyle: {
                background: `linear-gradient(100deg, ${theme.palette.blue[500]} 10.05%, ${theme.palette.blue[700]} 91.82%)`,
                borderColor: "transparent",
                color: theme.palette.neutral[100],
            },
            avatarColor: theme.palette.blue[100],
            avatarTextColor: theme.palette.blue[700],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.blue[500]}`,
        },
        Vendor: {
            badgeContentStyle: {
                background: theme.palette.secondary[100],
                borderColor: theme.palette.secondary[400],
                color: theme.palette.secondary[700],
            },
            avatarColor: theme.palette.blue[200],
            avatarTextColor: theme.palette.blue[800],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.blue[400]}`,
        },
        Influencer: {
            badgeContentStyle: {
                backgroundColor: theme.palette.purple[100],
                borderColor: theme.palette.purple[500],
                color: theme.palette.purple[700],
            },
            avatarColor: theme.palette.secondary[100],
            avatarTextColor: theme.palette.secondary[700],
            avatarBorder: `2px solid ${theme.palette.purple[400]}`,
        },
        "Direct IT": {
            badgeContentStyle: {
                background: theme.palette.blue[500],
                borderColor: theme.palette.blue[500],
                color: theme.palette.neutral[100],
            },
            avatarColor: theme.palette.secondary[100],
            avatarTextColor: theme.palette.secondary[700],
            avatarBorder: `${avatarBorderWidth} solid ${theme.palette.blue[500]}`,
        },
        default: {
            badgeContentStyle: {
                backgroundColor: theme.palette.secondary[100],
                borderColor: theme.palette.secondary[400],
                color: theme.palette.secondary[700],
            },
            avatarColor: theme.palette.secondary[100],
            avatarTextColor: theme.palette.secondary[700],
            avatarBorder: "none",
        },
    };
};

const AvatarComponentV2 = ({
    size = 36,
    showProfileType = false,
    origin = {
        vertical: "bottom",
        horizontal: "right",
    },
    aligned: alignment = "bottom",
    isRedirectEnabled = true,
    isMSPClientPage,
    ...props
}: IAvatarV2Props) => {
    const {isCompany} = props;
    const [errorImage, setErrorImage] = useState<string>("");
    const profileType =
        typeof props.user?.profile_type === "string" ? props.user?.profile_type : props.user?.profile_type?.value || "";

    const isInfluencer =
        props.user?.type === USERS_TYPE.INFLUENCER || [USERS_TYPE.INFLUENCER, "influencer"].includes(profileType);
    const isMSP =
        [VENDOR_TYPES.MSP_BUSINESS_BASIC, VENDOR_TYPES.MSP_BUSINESS_PREMIUM, "msp", "MSP"].includes(profileType) ||
        (
            [
                CompanyType.ISP_ALL,
                CompanyType.MSSP,
                CompanyType.VAR,
                CompanyType.FRANCHISE_MSP,
                CompanyType.FRANCHISE_CORPORATE_MSP,
                CompanyType.MSP_LOCATION,
            ] as string[]
        ).includes(props.user?.type || "");
    const isDistributor = !!props?.user?.is_distributor;
    const isAffiliateBrandMainCompany = props.user?.type === USERS_TYPE.FRANCHISE_CORPORATE_MSP;
    const isAffiliateBrandChildCompany =
        isMSP && (props.user?.type === USERS_TYPE.FRANCHISE_MSP || !!(props.user as any)?.parent_id);
    const isMSPLocation = props.user?.type === CompanyType.MSP_LOCATION;
    const isMSPClient = isMSP && props.user?.type === COMPANY_TYPE.MSP_CLIENT;
    const isInternalIT = props.user?.type === COMPANY_TYPE.DIRECT;
    const isMDF = props.user && "is_mdf" in props.user ? props.user?.is_mdf : false;
    const showProfileTypeAvatarBadge = showProfileType && !isInternalIT;

    const badgeText = isInternalIT
        ? "Direct IT"
        : isCompany
          ? isMSP
              ? isAffiliateBrandMainCompany
                  ? "Corporation"
                  : isAffiliateBrandChildCompany || isMSPLocation
                    ? "Location"
                    : isMSPClient
                      ? MSP_BRAND_TEXT.CUSTOMER
                      : "MSP"
              : isDistributor
                ? "Marketplace"
                : "Vendor"
          : isInfluencer
            ? "Influencer"
            : "";
    const theme = useTheme();
    const stylesByType = getStylesByTypeObject(theme, size);
    const {badgeContentStyle, avatarColor, avatarTextColor, avatarBorder} =
        stylesByType?.[badgeText] || stylesByType.default;
    const fallbackImage = createImageFromInitials(
        90, // size
        props.user?.name ?? "User",
        avatarColor,
        undefined,
        avatarTextColor,
    );
    const badgeImg = `/Media/icons/${
        isCompany
            ? isMSP
                ? isAffiliateBrandMainCompany
                    ? "newMsp"
                    : isAffiliateBrandChildCompany || isMSPLocation
                      ? "newAffiliate"
                      : "newMsp"
                : isDistributor
                  ? "newDistributor"
                  : "newVendor"
            : isInfluencer
              ? "newInfluencer"
              : ""
    }Icon.svg`;
    const fallbackAvatarIcon = isCompany
        ? isMSP
            ? "/Media/icons/newMspIconFallback.svg"
            : "/Media/icons/newVendorIcon.svg"
        : "";
    const avatarSrc = errorImage
        ? errorImage
        : props.user?.avatar || props.user?.name
          ? props.user.avatar
              ? props.user.avatar
              : fallbackImage
          : isMSPClientPage
            ? fallbackImage
            : !!badgeImg.replace("Icon.svg", "")
              ? fallbackAvatarIcon
              : fallbackImage;

    const renderAvatar = useCallback(
        () => (
            <Badge
                overlap="circular"
                sx={{
                    ...(showProfileTypeAvatarBadge
                        ? {
                              "& .MuiBadge-badge": {
                                  ...aligned[alignment],
                                  padding: "0!important",
                              },
                          }
                        : {}),
                    maxWidth: "100%",
                    width: "100%",
                    height: "100%",
                    marginBottom:
                        showProfileTypeAvatarBadge && badgeText ? compensationAligned(size)[alignment] : undefined,
                    verticalAlign: "unset",
                    position: "relative",
                }}
                anchorOrigin={origin}
                badgeContent={
                    showProfileTypeAvatarBadge && badgeText && !isMSPClient ? (
                        <Typography
                            variant="subtitle4"
                            fontInter
                            className={styles.typeBadge}
                            style={badgeContentStyle}
                            sx={{
                                padding: props.profileTypeMinimized ? "2px 4px !important" : "2px 6px !important",
                                borderWidth: "1px !important",
                                fontSize: "11px !important",
                                lineHeight: "120% !important",
                                fontWeight: "700 !important",
                                maxHeight: 18,
                            }}>
                            <Box
                                sx={{
                                    height: size < 40 ? "5.4px" : (size as number) / 4.5,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    gap: 0.5,
                                    textAlign: "center",
                                    verticalAlign: "center",
                                    whiteSpace: "nowrap",
                                    "&, & img": {
                                        padding: "0 !important",
                                        objectFit: "contain",
                                    },
                                    ...props?.badgeSx,
                                }}>
                                {!isMSPClient && (
                                    <img
                                        src={badgeImg}
                                        style={{
                                            maxHeight: 12,
                                            maxWidth: 12,
                                            height: 12,
                                            width: 12,
                                            objectFit: "contain",
                                            ...props?.badgeImageStyle,
                                        }}
                                        alt={badgeText}
                                    />
                                )}
                                {!props.profileTypeMinimized && badgeText}
                            </Box>
                        </Typography>
                    ) : null
                }>
                {isMDF && (
                    <Box
                        data-testid="mdf-badge"
                        sx={{
                            position: "absolute",
                            top: "-6px",
                            left: "-4px",
                            zIndex: 2,
                            width: 16,
                            height: 16,
                        }}>
                        <img src="/Media/icons/handcoin.png" alt="handcoin" style={{width: "100%", height: "100%"}} />
                    </Box>
                )}
                <Avatar
                    src={avatarSrc}
                    onError={(e: any) => {
                        if (!e.target?.src) return;
                        setErrorImage(fallbackImage);
                    }}
                    alt={props.user?.name || "user avatar"}
                    variant="circular"
                    imgProps={{
                        // @ts-expect-error - data-testid is not a valid prop for TS
                        "data-testid": "avatar",
                    }}
                    sx={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        aspectRatio: 1,
                        width: "100%",
                        height: "100%",
                        padding: isCompany || isInfluencer ? "2px" : "0",
                        border: props?.border || avatarBorder,
                        borderRadius: "50%",
                        flexShrink: 0,
                        position: "relative",
                        zIndex: 1,
                        backgroundColor: "#fff",
                        ...(avatarSrc === fallbackAvatarIcon && {
                            background: isMSP ? theme.palette.primary[100] : theme.palette.blue[100],
                        }),
                        "&, & img": {
                            borderRadius: "50%",
                            objectFit: "cover",
                            flexShrink: 0,
                            ...(avatarSrc === fallbackAvatarIcon && {objectFit: "scale-down"}),
                        },
                        ...(props?.sx || {}),
                    }}
                />
                <Box
                    sx={{
                        bgcolor: "neutral.200",
                        borderRadius: "50%",
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: "calc(100% - 8px)",
                        height: "calc(100% - 8px)",
                        zIndex: 0,
                    }}
                />
                {props?.statusBadge && <span className={styles.statusBadge}>{props.statusBadge}</span>}
            </Badge>
        ),
        [avatarSrc, avatarColor, avatarTextColor, stylesByType, fallbackImage],
    );

    return isRedirectEnabled && !props.disableLink ? (
        <Link
            style={{
                width: size,
                height: size,
                maxWidth: size,
                maxHeight: size,
                display: "inline-block",
                flex: "0 0 auto",
                ...(props.style || {}),
            }}
            target={props.target}
            title={props.title}
            onClick={props.onClick}
            to={
                isCompany
                    ? isMSP
                        ? routeConfig.MspProfile.static_path + (props.user?.friendly_url || "")
                        : isMSPClient
                          ? routeConfig.MspClientProfile.path.replace(":friendly_url", props.user?.friendly_url || "")
                          : isInternalIT
                            ? routeConfig.InternalITProfile.static_path + (props.user?.friendly_url || "")
                            : routeConfig.VendorProfile.path.replace(":id", props.user?.friendly_url || "")
                    : routeConfig.UserProfile.path.replace(":id", props.user?.friendly_url || "")
            }>
            {renderAvatar()}
        </Link>
    ) : (
        <div
            title={props.title}
            onClick={props.onClick}
            style={{
                width: size,
                height: size,
                maxWidth: size,
                maxHeight: size,
                flex: "0 0 auto",
                cursor: props.onClick ? "pointer" : "default",
                ...(props.style || {}),
            }}>
            {renderAvatar()}
        </div>
    );
};

export default AvatarComponentV2;
