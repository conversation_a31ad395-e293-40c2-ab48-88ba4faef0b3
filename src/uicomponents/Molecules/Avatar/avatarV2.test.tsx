import {render} from "@testing-library/react";
import {describe, expect, it} from "vitest";
import TestWrapper from "../../../../__test_utils__/TestWrapper";
import {USERS_TYPE} from "../../../enums/usersTypes.enum";
import AvatarComponentV2 from "./avatarV2.component";

type MockedUser = {
    avatar: string;
    type: string;
    is_distributor?: boolean;
    profile_type?: string | {value: string; label: string};
    friendly_url: string;
    name: string;
    parent_id?: string;
};
const DEFAULT_MOCKED_USER: MockedUser = {
    avatar: "",
    type: USERS_TYPE.ISP_ALL,
    friendly_url: "example",
    name: "Example MSP",
};

describe("AvatarComponentV2", () => {
    it("renders without crashing", () => {
        const {getByTestId} = render(
            <TestWrapper>
                <AvatarComponentV2
                    isCompany
                    user={{...DEFAULT_MOCKED_USER, avatar: "https://example.com/avatar.jpg"}}
                />
            </TestWrapper>,
        );
        expect(getByTestId("avatar")).toBeTruthy();
    });

    describe("Badge Text Logic", () => {
        it.each([
            [
                {user: {...DEFAULT_MOCKED_USER, is_distributor: true, type: USERS_TYPE.DISTRIBUTOR}, isCompany: true},
                "Distributor",
            ],
            [{user: {...DEFAULT_MOCKED_USER, type: USERS_TYPE.ISP_ALL}, isCompany: true}, "MSP"],
            [
                {user: {...DEFAULT_MOCKED_USER, type: USERS_TYPE.FRANCHISE_CORPORATE_MSP}, isCompany: true},
                "Corporation",
            ],
            [{user: {...DEFAULT_MOCKED_USER, type: USERS_TYPE.FRANCHISE_MSP}, isCompany: true}, "Affiliate"],
            [{user: {...DEFAULT_MOCKED_USER, type: USERS_TYPE.VENDOR}, isCompany: true}, "Vendor"],
            [{user: {...DEFAULT_MOCKED_USER, type: USERS_TYPE.INFLUENCER}, isCompany: false}, "Influencer"],
        ])("displays correct badge text for props %o", (props, expectedBadgeText) => {
            const {getByText} = render(
                <TestWrapper>
                    <AvatarComponentV2 {...props} showProfileType />
                </TestWrapper>,
            );
            expect(getByText(expectedBadgeText)).toBeTruthy();
        });
    });

    describe("Avatar Source Selection", () => {
        it("uses user avatar if available", () => {
            const userAvatar = "http://example.com/avatar.jpg";
            const component = render(
                <TestWrapper>
                    <AvatarComponentV2 user={{...DEFAULT_MOCKED_USER, avatar: userAvatar}} isCompany />
                </TestWrapper>,
            );

            expect((component.getByTestId("avatar") as HTMLImageElement).src).toBe(userAvatar);
        });

        it("falls back to generated image on error", () => {
            const userAvatar = "";
            const component = render(
                <TestWrapper>
                    <AvatarComponentV2 user={{...DEFAULT_MOCKED_USER, avatar: userAvatar}} isCompany />
                </TestWrapper>,
            );
            if (component.queryByTestId("avatar")) {
                expect((component.getByTestId("avatar") as HTMLImageElement).src).not.toBe(userAvatar);
            } else {
                expect(component.getByTestId("PersonIcon")).toBeTruthy();
            }
        });
    });

    describe("Redirect Behavior", () => {
        it("renders a Link when redirection is enabled", () => {
            const {container} = render(
                <TestWrapper>
                    <AvatarComponentV2 isRedirectEnabled={true} user={DEFAULT_MOCKED_USER} isCompany />
                </TestWrapper>,
            );
            expect(container.querySelector("a")).toBeTruthy();
        });

        it("renders a div when redirection is disabled", () => {
            const {container} = render(
                <TestWrapper>
                    <AvatarComponentV2 isRedirectEnabled={false} user={DEFAULT_MOCKED_USER} isCompany />
                </TestWrapper>,
            );
            expect(container.querySelector("div")).toBeTruthy();
        });
    });
});
