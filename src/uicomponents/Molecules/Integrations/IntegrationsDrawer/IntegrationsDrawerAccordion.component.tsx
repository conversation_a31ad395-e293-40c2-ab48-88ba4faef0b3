import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Checkbox,
    Divider,
    Grid,
} from "@mui/material";
import {ExpandMore} from "@mui/icons-material";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import React, {useEffect, useRef} from "react";
import Row from "../IntegrationsDrawer/IntegrationsDrawerAccordion/Row";
import {IIntegration, IMapping} from "../../../../Interfaces/integration.interface";
import {IContractFields} from "../../../../Interfaces/contractFields.interface";
import {IContractBillingTypes} from "../../../../Interfaces/contractBillingTypes.interface";
import {IContractRecurrenceIntervals} from "../../../../Interfaces/contractRecurrenceIntervals.interface";
import {IContractBillingContacts} from "../../../../Interfaces/contractBillingContacts.interface";
import useTheme from "@mui/material/styles/useTheme";
import {IContractInvoicingCycle} from "../../../../Interfaces/contractInvoicingCycle.interface";

interface IIntegrationAccordion extends IIntegration{
    index:number;
    contractFields: IContractFields[];
    contractBillingTypes: IContractBillingTypes[];
    contractRecurrenceIntervals:IContractRecurrenceIntervals[];
    contractBillingContacts:IContractBillingContacts[];
    contractInvoicingCycle:IContractInvoicingCycle[];
    fieldsValues:IMapping[];
    setFieldValues: (e: any) => void;
    filterUnmapped:boolean;
    filterUnmappedHandle: (e: any) => void;
}
const IntegrationsDrawerAccordion = ({
    key,
    header,
    left_column,
    index,
    contractFields,
    contractBillingTypes,
    contractRecurrenceIntervals,
    contractBillingContacts,
    contractInvoicingCycle,
    fieldsValues,
    setFieldValues,
    filterUnmapped,
    filterUnmappedHandle
}:IIntegrationAccordion) => {

    const scrollRef = useRef<HTMLDivElement|null>(null);
    const theme = useTheme();

    const boxOnScrollHandle = (e) => {
        localStorage.setItem(`IntegrationDrawerScrollTop${index}`, String(e.currentTarget.scrollTop));
    }

    useEffect(()=>{
        const element = scrollRef.current;
        if(element){
            const savedScroll = parseInt(localStorage.getItem(`IntegrationDrawerScrollTop${index}`) || "0");
            element.scrollTop = savedScroll;
        }
    }, [fieldsValues]);


    return (
        <Accordion
            defaultExpanded
            sx={{
                border:`1px solid ${theme.palette.neutral[300]}`,
                borderRadius: "8px",
                boxShadow:"inherit"
            }}
        >
            <AccordionSummary
                expandIcon={
                    <ExpandMore sx={{
                        color:theme.palette.blue[600],
                        fontSize: "2.1rem",
                    }}/>
                }
                aria-controls={key}
                id={key}
                sx={{
                    padding: "0 15px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                    }}
                >
                    <Typography
                        variant="h5"
                        fontSize="16px !important"
                        fontWeight="bold !important">
                        {header}
                    </Typography>
                </Box>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        alignItems: "center",
                        gap: "8px",
                        marginLeft: "auto"
                    }}
                >
                    <Checkbox size="small"
                              onSelect={(e)=>{
                                  filterUnmappedHandle(e);
                              }}
                              onClick={(e)=>{
                                  filterUnmappedHandle(e);
                              }}
                              checked={filterUnmapped}
                    />
                    <Typography
                        variant="h5"
                        fontSize="14px !important">
                        Show unmapped only
                    </Typography>
                    &nbsp;&nbsp;&nbsp;
                </Box>
            </AccordionSummary>
            <Divider
                sx={{
                    width: "calc(100% - 30px)",
                    borderColor: theme.palette.neutral[300],
                    borderBottomWidth: "1px",
                    marginLeft: "auto",
                    marginRight: "auto"
                }}
            />
            <AccordionDetails
                sx={{
                    padding: "16px",
                }}
            >
                <Typography
                    variant="h5"
                    fontSize="14px !important"
                    sx={{ marginBottom: "14px" }}
                >
                    All customer agreements will be imported under your
                    company's name as the <b>Vendor</b>. Once integrated, these
                    agreements will be visible to your customers, categorized
                    as <b>Technology &gt; Managed IT Services</b>.
                </Typography>
                <Typography
                    variant="h5"
                    fontWeight="bold !important"
                    fontSize="16px !important"
                    padding="10px 0"
                >
                    Choose the corresponding fields
                </Typography>
                <Grid container spacing={2}
                      sx={{
                          margin: "4px 0",
                      }}>
                    <Grid item xs={5} sx={{
                        paddingLeft: "0 !important",
                    }}>
                        <Typography
                            variant="body2"
                            fontSize="14px !important"
                            sx={{
                                color: theme.palette.neutral[600],
                                textAlign: "left",
                            }}
                        >
                            {left_column}
                        </Typography>
                    </Grid>
                    <Grid item xs={2}></Grid>
                    <Grid item xs={5} sx={{
                        paddingLeft: "0 !important",
                    }}>
                        <Typography
                            variant="body2"
                            fontSize="14px !important"
                            sx={{
                                color: theme.palette.neutral[600],
                                textAlign: "left"
                            }}
                        >
                            CHANNEL PROGRAM FIELDS
                        </Typography>
                    </Grid>
                </Grid>
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                        overflow:"auto",
                        height:"calc(100vh - 60vh)",
                    }}
                >
                    <Box
                        ref={scrollRef}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-start",
                            overflowY:"auto",
                            width:"-webkit-fill-available"
                        }}
                        onScroll={boxOnScrollHandle}
                    >
                        <Grid container spacing={1}>
                            {fieldsValues.map((props, index) => (
                                <Row
                                    {...props}
                                    index={index}
                                    key={`parent-${props.id}`}
                                    contractFields={contractFields}
                                    contractBillingTypes={contractBillingTypes}
                                    contractRecurrenceIntervals={contractRecurrenceIntervals}
                                    contractBillingContacts={contractBillingContacts}
                                    contractInvoicingCycle={contractInvoicingCycle}
                                    fieldsValues={fieldsValues}
                                    setFieldValues={setFieldValues}
                                    filterUnmapped={filterUnmapped}
                                />
                            ))}
                        </Grid>
                    </Box>
                </Box>
            </AccordionDetails>
        </Accordion>
    )
}

export default IntegrationsDrawerAccordion;