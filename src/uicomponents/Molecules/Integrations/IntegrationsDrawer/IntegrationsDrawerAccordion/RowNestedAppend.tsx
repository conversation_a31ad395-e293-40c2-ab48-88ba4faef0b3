import {Grid, useTheme, <PERSON><PERSON>, IconButton} from "@mui/material";
import Box from "../../../../Atoms/Box/Box.component";
import Typography from "../../../../Atoms/Typography/Typography.component";
import React, {useRef, useState} from "react";
import CustomLArrowIcon from "./icons/CustomLArrowIcon";
import CustomPlusIcon from "./icons/CustomPlusIcon";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import UserListInput from "../../../UserListInput/UserListInput.component";
import AutoCompleteComponent from "../../../Autocomplete/Autocomplete.component";
import {IMapping} from "../../../../../Interfaces/integration.interface";
import {sortByProperty} from "../../../../../utils/array.util";

interface IIntegrationRowNestedAppend {
    id:string;
    dropdownlist:string;
    options:any[];
    fieldsValues:IMapping[];
    setFieldValues: (e: any) => void;
    hasValue:boolean;
}

const RowNestedAppend = ({
     id,
     dropdownlist,
     options,
     fieldsValues,
     setFieldValues,
     hasValue
 }:IIntegrationRowNestedAppend) => {

    const theme = useTheme();
    const [addNewRow, setAddNewRow] = useState<boolean>(true);
    const [search, setSearch] = useState<string>(null);
    const [selected, setSelected] = useState<string>(null);
    const [focus, setFocus] = useState<boolean>(false);
    const fieldName = useRef<string|null>(null);
    const fieldValue = useRef<string|null>(null);

    const addNewRowHandle = () => {
        setAddNewRow(!addNewRow);
    }

    const createCustomKey = (key) => {
        let number = 0;
        let exist = false;
        fieldsValues.forEach(ele => {
            if(ele.id == key){
                do{
                    exist = false;
                    for (const index in ele.children){
                        if(ele.children[index].append_key == `${key}-${number}`){
                            exist = true;
                            number+=1;
                            break;
                        }
                    }
                }while(exist);
            }
        });
        return `${key}-${number}`
    }

    const addNewRowFieldHandle = (key, type, value) => {
        if(type == 'name'){
            fieldName.current = value;
        }
        if(type == 'value'){
            fieldValue.current = value;
        }
        if(hasValue){
            if(!fieldName.current || !fieldValue.current){
                return;
            }
        }else{
            if(!fieldName.current){
                return;
            }
        }
        const newFieldsValues = fieldsValues.map((props)=>{
            const {id, children} = props;
            if(id == key && children){
                const customKey = createCustomKey(key);
                const newChildren = [...children, {
                    append_key:customKey, name:fieldName.current,
                    is_custom:true, selection:selected,
                    dropdown_list:dropdownlist, value:fieldValue.current}]
                return {
                    ...props,
                    children: newChildren
                }
            }else{
                return props;
            }
        });
        setFieldValues(newFieldsValues);
        addNewRowHandle();
        setSelected(null);
    }


    const getDefaultValue = (selection) => {
        const value = selection ? options.filter((item)=>item.id == selection)[0] : null;
        return value != undefined ? value : null;
    }

    const getOptionsFilter = () => {
        const newOptions = options.filter((item)=>{
            const lowerItem = item.label.toLowerCase();
            const searchLower = search?.toLowerCase();
            if(searchLower){
                return lowerItem.startsWith(searchLower) || lowerItem.endsWith(searchLower) || lowerItem.includes(searchLower);
            }else{
                return true;
            }
        });
        return sortByProperty(newOptions, 'label');
    }

    return (
        <React.Fragment key={`append-${id}`}>
            { addNewRow ?
                <>
                    <Grid item xs={5.7}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding:"0 !important",
                                width:"100%"
                            }}
                        >
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between",
                                    fontWeight: 600,
                                    color: theme.palette.neutral[600],
                                    borderRadius:"4px",
                                    border:`solid 1px ${theme.palette.neutral[300]}`,
                                    padding:"10px 16px 10px 16px",
                                    gap: 1,
                                    width:"100%",
                                    marginLeft:"10px"
                                }}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "8px",
                                    }}
                                >
                                    <CustomLArrowIcon/>
                                    <Button
                                        startIcon={
                                            <CustomPlusIcon sx={{
                                                fontSize: "100%",
                                                color: `${theme.palette.blue[600]} !important`,
                                                width:"16px !important",
                                                height:"16px !important",
                                            }}/>
                                        }
                                        sx={{
                                            padding: "0 !important",
                                            margin:"0 !important",
                                            color: theme.palette.blue[600],
                                            textTransform: "none",
                                            fontWeight: "bold",
                                            fontSize: "14px !important",
                                            "&:hover": {
                                                backgroundColor: "inherit",
                                                boxShadow: "none",
                                            },
                                        }}
                                        onClick={addNewRowHandle}
                                    >
                                        Add new field
                                    </Button>
                                </Box>
                            </Box>
                        </Box>
                    </Grid>
                    <Grid
                        item
                        xs={0.6}
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <ArrowRightAltIcon sx={{
                            color:theme.palette.neutral[500]
                        }}/>
                    </Grid>
                    <Grid item xs={5.7}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding:"0px 5px"
                            }}
                        >
                            <AutoCompleteComponent
                                id={`new-child-autocomplete-${id}`}
                                placeholder="Please select"
                                sx={{
                                    width:"100% !important",
                                    ".MuiInput-root .MuiInput-input":{
                                        fontSize:"14px !important",
                                        paddingTop:"12px !important"
                                    },
                                    ".MuiInput-root .MuiInput-input.MuiInputBase-input::placeholder":{
                                        fontSize: "14px !important",
                                    },
                                    "& .MuiInputBase-input::placeholder": {
                                        fontSize: "14px !important",
                                    },
                                    "div.MuiAutocomplete-inputRoot.MuiInputBase-root": {
                                        "&::before, &::after": {
                                            display: "none !important",
                                        }
                                    },
                                    "& div.MuiInputBase-root": {
                                        borderRadius:"4px !important",
                                        "& div.MuiFormControl-root input::placeholder": {
                                            "&::before, &::after": {
                                                display: "none !important",
                                            }
                                        },
                                        "&:hover::before": {
                                            borderBottom: `1px solid ${theme.palette.neutral[200]}!important`,
                                        }
                                    }
                                }}
                                disabled
                            />
                        </Box>
                    </Grid>
                </>
                :
                <>
                    <Grid item xs={12}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding:"0 !important",
                                width:"100%"
                            }}
                        >
                            <Box
                                sx={{
                                    alignItems: "center",
                                    gap: "8px",
                                    width:"100%",
                                    margin:"5px 5px 5px 10px",
                                    border: `1px solid ${theme.palette.grey[300]}`,
                                    borderRadius: "8px",
                                    padding: "10px 16px 10px 16px",
                                }}
                            >
                                <Grid container justifyContent="space-between" alignItems="center" sx={{marginBottom: "10px"}}>
                                    <Typography
                                        fontWeight="bold"
                                        color={theme.palette.neutral[600]}
                                        fontSize="14px"
                                    >
                                        ADD NEW FIELD
                                    </Typography>
                                    <Typography
                                        fontWeight="bold"
                                        color={theme.palette.blue[600]}
                                        fontSize="14px"
                                    >
                                        CANCEL
                                    </Typography>
                                </Grid>
                                <Grid container alignItems="center">
                                    <Grid item xs={5.7} display="flex" alignItems="center">
                                        <CustomLArrowIcon />
                                            <Box
                                                sx={{
                                                    width:"100%",
                                                    padding: "0px 16px 0px 12px",
                                                }}
                                            >
                                                <UserListInput
                                                    isRequired
                                                    id={`custom-field-name-${id}`}
                                                    label="Field Name"
                                                    placeholder="Enter you custom field"
                                                    validateOnTheFly
                                                    sx={{
                                                        width:"100% !important"
                                                    }}
                                                    onBlur={(e)=>{
                                                        addNewRowFieldHandle(id, 'name', e.target.value);
                                                    }}
                                                />
                                                {hasValue && (
                                                    <Box pt={3}>
                                                        <UserListInput
                                                            isRequired
                                                            id={`custom-field-value-${id}`}
                                                            label="Value"
                                                            placeholder="Enter the value of your custom field"
                                                            validateOnTheFly
                                                            sx={{
                                                                width:"100% !important"
                                                            }}
                                                            onBlur={(e)=>{
                                                                addNewRowFieldHandle(id, 'value', e.target.value);
                                                            }}
                                                        />
                                                    </Box>
                                                )}
                                            </Box>
                                    </Grid>
                                    <Grid item xs={0.6} display="flex" justifyContent="center">
                                        <ArrowRightAltIcon sx={{
                                            color:theme.palette.neutral[500]
                                        }}/>
                                    </Grid>
                                    <Grid item xs={5.7} display="flex" alignItems="center">
                                        <AutoCompleteComponent
                                            id={`new-child-autocomplete-selection-${id}`}
                                            placeholder="Please select"
                                            validateOnTheFly
                                            options={getOptionsFilter()}
                                            sx={{
                                                width:"100% !important",
                                                ".MuiInput-root .MuiInput-input":{
                                                    fontSize:"14px !important",
                                                    paddingTop:"12px !important"
                                                },
                                                ".MuiInput-root .MuiInput-input.MuiInputBase-input::placeholder":{
                                                    fontSize: "14px !important",
                                                },
                                                "& .MuiInputBase-input::placeholder": {
                                                    fontSize: "14px !important",
                                                },
                                                "div.MuiAutocomplete-inputRoot.MuiInputBase-root": {
                                                    "&::before, &::after": {
                                                        display: "none !important",
                                                    }
                                                },
                                                "& div.MuiInputBase-root": {
                                                    borderRadius:"4px !important",
                                                    "& div.MuiFormControl-root input::placeholder": {
                                                        "&::before, &::after": {
                                                            display: "none !important",
                                                        }
                                                    },
                                                    "&:hover::before": {
                                                        borderBottom: `1px solid ${theme.palette.neutral[200]}!important`,
                                                    }
                                                }
                                            }}
                                            defaultValue={getDefaultValue(selected)}
                                            disableClearable={!focus as any}
                                            value={getDefaultValue(selected)}
                                            isOptionEqualToValue={(option, value) => {
                                                return option.label == (value?.label ? value.label : value);
                                            }}
                                            onInputChange={(e, value)=>{
                                                setSearch(value);
                                            }}
                                            onChange={(e, option)=>{
                                                e.stopPropagation();
                                                e.preventDefault();
                                                let result = option ? option.id : option;
                                                setSelected(result);
                                            }}
                                            onFocus={() =>{
                                                setSearch(null);
                                                setFocus(true);
                                            }}
                                            onBlur={() =>{
                                                setFocus(false);
                                            }}
                                            inputReadOnly
                                        />
                                        &nbsp;&nbsp;&nbsp;
                                        <IconButton
                                            color="primary"
                                            aria-label="add"
                                            onClick={addNewRowHandle}
                                        >
                                            <CustomPlusIcon
                                                sx={{
                                                    fontSize: "100%",
                                                    color:`${theme.palette.blue[600]} !important`,
                                                    width:"24px !important",
                                                    height:"24px !important",
                                                }}
                                            />
                                        </IconButton>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Box>
                    </Grid>
                </>
            }
        </React.Fragment>
    )
}

export default RowNestedAppend;