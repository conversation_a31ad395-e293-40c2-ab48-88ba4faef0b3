import {SvgIcon, SvgIconProps} from "@mui/material";
import React from "react";
import useTheme from "@mui/material/styles/useTheme";

const CustomLArrowIcon: React.FC<SvgIconProps> = (props) => {
    const theme = useTheme();
    return (
        <SvgIcon
            {...props}
            viewBox="0 0 24 24"
            sx={{fontSize: "16px", color: theme.palette.neutral[500]}}
        >
            <path
                d="M8 6v8h8"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M16 16l2-2-2-2"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </SvgIcon>
    );
};

export default CustomLArrowIcon;