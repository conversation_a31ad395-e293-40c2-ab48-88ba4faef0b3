import React, {useEffect, useState} from "react";
import {Grid, useTheme} from "@mui/material";
import Box from "../../../../Atoms/Box/Box.component";
import Typography from "../../../../Atoms/Typography/Typography.component";
import Badge from "../../../../Atoms/Badge/badge.component";
import RowNested from "./RowNested";
import RowNestedAppend from "./RowNestedAppend";
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import AutoCompleteComponent from "../../../Autocomplete/Autocomplete.component";
import {IMapping} from "../../../../../Interfaces/integration.interface";
import {IntegrationsEnum} from "../../../../../enums/integrations.enum";
import {sortByProperty} from "../../../../../utils/array.util";
interface IIntegrationRow extends IMapping{
    index:number;
    contractFields:any[];
    contractBillingTypes:any[];
    contractRecurrenceIntervals:any[];
    contractBillingContacts:any[];
    contractInvoicingCycle:any[];
    fieldsValues:IMapping[];
    setFieldValues: (e: any) => void;
    filterUnmapped:boolean;
}
const Row = ({
     id,
     index,
     name,
     children,
     selection,
     dropdown_selection,
     type,
     open,
     contractFields,
     contractBillingTypes,
     contractRecurrenceIntervals,
     contractBillingContacts,
     contractInvoicingCycle,
     fieldsValues,
     setFieldValues,
     filterUnmapped
 }:IIntegrationRow) => {

    const theme = useTheme();
    const [search, setSearch] = useState<string>(null);
    const [selected, setSelected] = useState<string>(selection);
    const [focus, setFocus] = useState<boolean>(false);
    const [showChildren, setShowChildren] = useState<boolean>(selection == dropdown_selection);

    const openSubmenuHandle = (key, open) => {
        const newFieldsValues = fieldsValues.map((props)=>{
            const {id} = props;
            if(id == key){
                return {
                    ...props,
                    open
                };
            }else{
                return props;
            }
        });
        setFieldValues(newFieldsValues);
    }

    const inputOnChangeHandle = (key, selection) => {
        const newFieldsValues = fieldsValues.map((props)=>{
            const {id} = props;
            if(id == key){
                return {
                    ...props,
                    selection
                };
            }else{
                return props;
            }
        });
        setSelected(selection);
        setFieldValues(newFieldsValues);
    }

    const getDefaultValue = (selection) => {
        const value =  selection ? contractFields.filter((item)=>item.id == selection)[0] : null;
        return value != undefined ? value : null;
    }

    const getOptions = () => {
        const options = contractFields.filter((item)=>{
            return type == item.type;
        });
        return sortByProperty(options, 'label');
    }

    const getOptionsFilter = () => {
        return getOptions().filter((item)=>{
            const lowerItem = item.label.toLowerCase();
            const searchLower = search?.toLowerCase();
            if(searchLower){
                return lowerItem.startsWith(searchLower) || lowerItem.endsWith(searchLower) || lowerItem.includes(searchLower);
            }else{
                return true;
            }
        })
    }

    const deleteChildrenValues = (key) => {
        const newFieldsValues = fieldsValues.map((props)=>{
            const {children} = props;
            if(props.id == key && children.length > 0){
                const newChildren = children.map((childrenProps)=>{
                    return {
                        ...childrenProps,
                        selection:null
                    }
                });
                return {...props, children:newChildren}
            } else {
                return props;
            }
        });
        setFieldValues(newFieldsValues);
    }

    useEffect(() => {
        if(selected != dropdown_selection){
            deleteChildrenValues(id);
        }
        setShowChildren(selected == dropdown_selection);
    }, [selected]);

    const AppendRow = ({id, list, fieldsValues, setFieldValues, dropdownlist, showChildren}) => {
        if(list[0]?.dropdown_list){
            let options;
            if(list[0].dropdown_list == IntegrationsEnum.ContractBillingType){
                options = contractBillingTypes;
            }
            if(list[0].dropdown_list == IntegrationsEnum.ContractRecurrenceIntervals){
                options = contractRecurrenceIntervals;
            }
            if(list[0].dropdown_list == IntegrationsEnum.ContractBillingContact){
                options = contractBillingContacts;
            }
            if(list[0].dropdown_list == IntegrationsEnum.ContractInvoiceCycle){
                options = contractInvoicingCycle;
            }
            if(!showChildren){
                options = [];
            }
            const hasValue = !!list[0].value;
            return (
                <RowNestedAppend
                    id={id}
                    dropdownlist={dropdownlist}
                    options={options}
                    fieldsValues={fieldsValues}
                    setFieldValues={setFieldValues}
                    hasValue={hasValue}
                />
            );
        }
    }

    return (
        <>
            {
                (
                    (filterUnmapped && !selection) ||
                    (filterUnmapped && !!children?.find((ele)=>!ele.selection)) ||
                    (!filterUnmapped)
                ) &&
                <>
                    <Grid item xs={5.7}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding:"0 !important",
                                width:"100%",
                            }}
                        >
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between",
                                    fontWeight: 600,
                                    color: theme.palette.neutral[600],
                                    borderRadius:"4px",
                                    border:`solid 1px ${theme.palette.neutral[300]}`,
                                    padding:"10px 16px 10px 16px",
                                    gap: 1,
                                    width:"100%",
                                    cursor:`${children.length > 0 && 'pointer'}`
                                }}
                                onClick={()=>{
                                    openSubmenuHandle(id, !open);
                                }}
                            >
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "8px",
                                    }}
                                >
                                    <Typography
                                        variant="h5"
                                        fontSize="14px !important"
                                        fontWeight="500 !important"
                                        color="black"
                                    >
                                        {name}
                                    </Typography>
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        alignItems: "center",
                                        gap: "8px",
                                        marginLeft: "auto",
                                    }}
                                >
                                    {children?.length > 0 &&
                                        <Badge
                                            size={10}
                                        >
                                            {
                                                !filterUnmapped ? children.length : children.filter((ele)=>{
                                                    return !ele.selection;
                                                }).length}
                                        </Badge>
                                    }
                                </Box>
                            </Box>
                        </Box>
                    </Grid>
                    <Grid
                        item
                        xs={0.6}
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <ArrowRightAltIcon sx={{
                            color:theme.palette.neutral[500]
                        }}/>
                    </Grid>
                    <Grid item xs={5.7}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding:"0px 5px",
                            }}
                        >
                            {getOptions().length == 1 ?
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "8px",
                                        padding:"0 !important",
                                        width:"100%",
                                    }}
                                >
                                    <Box
                                        sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "space-between",
                                            fontWeight: 600,
                                            color: theme.palette.neutral[600],
                                            borderRadius:"4px",
                                            border:`solid 1px ${theme.palette.neutral[300]}`,
                                            padding:"10px 16px 10px 16px",
                                            gap: 1,
                                            width:"100%",
                                            cursor:`${children.length > 0 && 'pointer'}`
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "8px",
                                            }}
                                        >
                                            <Typography
                                                variant="h5"
                                                fontSize="14px !important"
                                                fontWeight="500 !important"
                                                color="black"
                                            >
                                                {getDefaultValue(selected).label}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Box>
                            :
                                <AutoCompleteComponent
                                    id={`parent-autocomplete-${id}-${index}`}
                                    placeholder="Please select"
                                    validateOnTheFly
                                    options={getOptionsFilter()}
                                    sx={{
                                        width:"100% !important",
                                        ".MuiInput-root .MuiInput-input":{
                                            fontSize:"14px !important",
                                            paddingTop:"12px !important"
                                        },
                                        ".MuiInput-root .MuiInput-input.MuiInputBase-input::placeholder":{
                                            fontSize: "14px !important",
                                        },
                                        "& .MuiInputBase-input::placeholder": {
                                            fontSize: "14px !important",
                                        },
                                        "div.MuiAutocomplete-inputRoot.MuiInputBase-root": {
                                            "&::before, &::after": {
                                                display: "none !important",
                                            }
                                        },
                                        "& div.MuiInputBase-root": {
                                            borderRadius:"4px !important",
                                            "& div.MuiFormControl-root input::placeholder": {
                                                "&::before, &::after": {
                                                    display: "none !important",
                                                }
                                            },
                                            "&:hover::before": {
                                                borderBottom: `1px solid ${theme.palette.neutral[200]}!important`,
                                            }
                                        }
                                    }}
                                    defaultValue={null}
                                    disableClearable={!focus as any}
                                    value={getDefaultValue(selected)}
                                    isOptionEqualToValue={(option, value) => {
                                        return option.label == (value?.label ? value.label : value);
                                    }}
                                    onValueChange={(key, value, option) => {
                                        let result = option ? option.id : option;
                                        inputOnChangeHandle(id, result);
                                    }}
                                    onInputChange={(e, value, reason)=>{
                                        if (reason === "clear") {
                                            setSearch(null);
                                            inputOnChangeHandle(id, null);
                                        } else {
                                            setSearch(value);
                                        }
                                    }}
                                    onFocus={() =>{
                                        setSearch(null);
                                        setFocus(true);
                                    }}
                                    onBlur={() =>{
                                        setFocus(false);
                                    }}
                                    inputReadOnly
                                />
                            }
                        </Box>
                    </Grid>
                    {
                        (!!open && children.length > 0) &&
                        <>
                            {
                                children.map((props, index) => {
                                 let options;
                                 if(props.dropdown_list == IntegrationsEnum.ContractBillingType){
                                     options = contractBillingTypes;
                                 }
                                 if(props.dropdown_list == IntegrationsEnum.ContractRecurrenceIntervals){
                                     options = contractRecurrenceIntervals;
                                 }
                                if(props.dropdown_list == IntegrationsEnum.ContractBillingContact){
                                    options = contractBillingContacts;
                                }
                                if(props.dropdown_list == IntegrationsEnum.ContractInvoiceCycle){
                                    options = contractInvoicingCycle;
                                }
                                 if(!showChildren){
                                     options = [];
                                     props = {...props, selection:null};
                                 }
                                 return (
                                     <RowNested
                                         {...props}
                                         index={index}
                                         key={!props.is_custom ? `child-${props.id}-${index}` : `child-${props.append_key}-${index}`}
                                         options={options}
                                         fieldsValues={fieldsValues}
                                         setFieldValues={setFieldValues}
                                         filterUnmapped={filterUnmapped}
                                     />
                                 )
                                })
                            }
                            <AppendRow
                                id={id}
                                list={children}
                                fieldsValues={fieldsValues}
                                setFieldValues={setFieldValues}
                                dropdownlist={children[0].dropdown_list}
                                showChildren={showChildren}
                            />
                        </>
                    }
                </>
            }
        </>
    )
}

export default Row;
