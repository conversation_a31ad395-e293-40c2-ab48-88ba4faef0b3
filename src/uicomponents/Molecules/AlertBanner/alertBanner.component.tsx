import CheckCircleOutlined from "@mui/icons-material/CheckCircleOutlined";
import CloseOutlined from "@mui/icons-material/CloseOutlined";
import ErrorOutlineOutlined from "@mui/icons-material/ErrorOutlineOutlined";
import InfoOutlined from "@mui/icons-material/InfoOutlined";
import WarningAmberOutlined from "@mui/icons-material/WarningAmberOutlined";
import type {SxProps, Theme} from "@mui/material";
import DOMPurify from "dompurify";
import type {CSSProperties, ReactNode} from "react";
import {stripHtml} from "../../../utils/formatString.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";

interface IProps {
    id: string;
    variant?: "success" | "warning" | "error" | "info" | "default";
    title?: {
        text: ReactNode;
        color?: string | ((theme: Theme) => string);
        sx?: SxProps<Theme>;
    };
    subTitle?: {
        text?: ReactNode;
        color?: string | ((theme: Theme) => string);
        sx?: SxProps<Theme>;
        html?: string;
    };
    button?: {
        text: ReactNode;
        sx?: SxProps<Theme>;
        onClick?: () => void;
        type?: "submit" | "reset";
    };
    secondaryButton?: {
        text: ReactNode;
        sx?: SxProps<Theme>;
        onClick?: () => void;
        type?: "submit" | "reset";
    };
    styles?: CSSProperties;
    classNames?: string;
    iconPosition?: CSSProperties["alignItems"];
    onClose?: () => void;
    sx?: SxProps<Theme> | ((theme: Theme) => SxProps<Theme>);
    icon?: ReactNode;
    showBoxShadow?: boolean;
    titleContainerSx?: SxProps<Theme>;
    contentContainerSx?: SxProps<Theme>;
    containerSx?: SxProps<Theme>;
    hideIcon?: boolean;
    customActions?: ReactNode;
    iconSx?: SxProps<Theme>;
}

export default function AlertBanner({
    id,
    title,
    subTitle,
    styles,
    classNames,
    button,
    secondaryButton,
    variant,
    iconPosition,
    onClose,
    icon,
    showBoxShadow,
    titleContainerSx,
    contentContainerSx,
    containerSx,
    hideIcon,
    customActions,
    iconSx,
}: IProps) {
    const success = variant === "success";
    const info = variant === "info";
    const error = variant === "error";
    const warning = variant === "warning";
    const finalIcon =
        icon !== undefined ? (
            icon
        ) : success ? (
            <CheckCircleOutlined sx={theme => ({fill: theme.palette.success[500]})} />
        ) : error ? (
            <ErrorOutlineOutlined sx={theme => ({fill: theme.palette.error[500]})} />
        ) : info ? (
            <InfoOutlined sx={theme => ({fill: theme.palette.secondary[600]})} />
        ) : warning ? (
            <WarningAmberOutlined sx={theme => ({fill: theme.palette.warning[700]})} />
        ) : (
            <InfoOutlined sx={theme => ({fill: theme.palette.secondary[600]})} />
        );
    return (
        <Box
            id={id}
            className={classNames}
            style={styles}
            sx={theme => ({
                backgroundColor:
                    theme.palette[
                        success ? "green" : info ? "secondary" : error ? "error" : warning ? "warning" : "neutral"
                    ][!variant ? 200 : 100],
                display: "flex",
                alignItems: "flex-start",
                padding: "16px",
                flexWrap: button ? "wrap" : undefined,
                flexGrow: 1,
                border: `1px solid ${
                    theme.palette[
                        success ? "green" : info ? "secondary" : error ? "error" : warning ? "warning" : "neutral"
                    ][warning ? 400 : 300]
                }`,
                borderRadius: "8px",
                gap: "16px",
                boxShadow: showBoxShadow ? "0px 4px 4px 0px rgba(0, 0, 0, 0.10)" : "",
                justifyContent: "center",
                "@media (min-width: 992px)": {
                    justifyContent: "space-between",
                },
                ...((typeof containerSx === "function" ? containerSx(theme) : containerSx) as any),
            })}>
            <Box
                sx={{
                    display: "flex",
                    alignItems: iconPosition ?? "center",
                    gap: "16px",
                    flexGrow: "1",
                }}>
                <Box
                    sx={{
                        display: "none !important",
                        "@media (min-width: 992px)": {display: "block !important"},
                        ...iconSx,
                    }}>
                    {!hideIcon && finalIcon}
                </Box>
                <Box
                    sx={theme => ({
                        display: "flex",
                        flexDirection: "column",
                        gap: "8px",
                        flexGrow: "1",
                        ...((typeof contentContainerSx === "function"
                            ? contentContainerSx(theme)
                            : contentContainerSx) as any),
                    })}>
                    <Box
                        sx={{
                            display: "flex",
                            gap: "8px",
                            justifyContent: "space-between",
                            ...(titleContainerSx || {}),
                        }}>
                        {!!title && (
                            <Typography
                                weight={700}
                                sx={theme => ({
                                    "h1, h2, h3, h4, h5, h6": {
                                        color: title?.color
                                            ? typeof title?.color === "string"
                                                ? title.color
                                                : title.color(theme)
                                            : theme.palette.neutral[800],
                                    },
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "8px",
                                    justifyContent: "center",
                                    "@media (min-width: 992px)": {justifyContent: "initial"},
                                    ...((typeof title?.sx === "function" ? title?.sx(theme) : title?.sx) as any),
                                })}>
                                <Box
                                    sx={{
                                        display: "flex !important",
                                        "@media (min-width: 992px)": {
                                            display: "none !important",
                                        },
                                    }}>
                                    {!hideIcon && finalIcon}
                                </Box>
                                {title?.text}
                            </Typography>
                        )}
                        {onClose && (
                            <CloseOutlined
                                sx={theme => ({cursor: "pointer", color: theme.palette.neutral[600]})}
                                id={"cancel-alert" + id}
                                onClick={onClose}
                            />
                        )}
                    </Box>
                    {subTitle && (
                        <Typography
                            variant="body3"
                            fontInter
                            dangerouslySetInnerHTML={
                                subTitle.html
                                    ? {
                                          __html: DOMPurify.sanitize(subTitle.html.toString(), {
                                              ADD_ATTR: ["target"],
                                          }),
                                      }
                                    : undefined
                            }
                            sx={theme => ({
                                color: subTitle.color
                                    ? typeof subTitle.color === "string"
                                        ? subTitle.color
                                        : subTitle.color(theme)
                                    : theme.palette.neutral[800],
                                fontSize: "16px !important",
                                "& a": {
                                    color: theme.palette.blue[600],
                                    "&:hover": {
                                        color: theme.palette.blue[600],
                                    },
                                },
                                ...((typeof subTitle.sx === "function" ? subTitle.sx(theme) : subTitle.sx) as any),
                            })}>
                            {typeof subTitle?.text === "string" ? stripHtml(subTitle.text) : subTitle.text}
                        </Typography>
                    )}
                </Box>
            </Box>
            <Box display="flex" gap="8px">
                {button && (
                    <MuiButtonComponent
                        onClick={button.onClick}
                        id="noResultAction"
                        variant="outlined"
                        color="secondary"
                        sx={theme => ({
                            color: theme.palette.secondary.main,
                            border: `1px solid ${theme.palette.secondary.main}`,
                            fontWeight: 600,
                            ...(typeof button.sx === "function" ? button.sx(theme) : button.sx),
                        })}>
                        {button.text}
                    </MuiButtonComponent>
                )}
                {secondaryButton && (
                    <MuiButtonComponent
                        onClick={secondaryButton.onClick}
                        id="noResultAction"
                        variant="outlined"
                        color="secondary"
                        sx={theme => ({
                            color: theme.palette.secondary.main,
                            border: `1px solid ${theme.palette.secondary.main}`,
                            fontWeight: 600,
                            ...(typeof secondaryButton.sx === "function"
                                ? secondaryButton.sx(theme)
                                : secondaryButton.sx),
                        })}>
                        {secondaryButton.text}
                    </MuiButtonComponent>
                )}
                {customActions && customActions}
            </Box>
        </Box>
    );
}
