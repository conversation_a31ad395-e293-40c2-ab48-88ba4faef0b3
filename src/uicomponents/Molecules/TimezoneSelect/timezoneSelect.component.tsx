import {useEffect, useRef, useState} from "react";
import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";
import {timezoneService} from "../../../services/timezone.service";
import {AxiosError, AxiosResponse} from "axios";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";
import {useFormContext} from "react-hook-form";
import {
    getCurrentTimezone,
    getLongTimezoneName,
    getTimezoneAbbreviation,
    getUTCOffset,
    isDaylightTime,
} from "../../../utils/formatDate";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";

interface IProps extends IAutoCompleteProps<any, any, any, any> {
    renderInput: any;
    options: any;
    autoSelectUsersTimezone?: boolean;
    onChangeTimezoneId?: (tz: string) => any;
}

const TimezoneSelect = (props: IProps) => {
    const {setValue, watch} = useFormContext();
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<any[]>([]);
    const timezones = useRef<
        Array<{
            short: string;
            long: string;
            timezone_value: string;
            id: string;
        }>
    >([]);
    const notify = useNotification();
    const autoCompleteId = props.id + "_friendly";
    let usersTimezone = getCurrentTimezone(true);
    const unfriendlyValue = watch(props.id);
    //? This fixes an issue with Chromium browsers returning the incorrect canonical timezone
    if (usersTimezone === "Asia/Calcutta") {
        usersTimezone = "Asia/Kolkata";
    }

    const onSuccessGetTimezones = (res: AxiosResponse) => {
        timezones.current = res.data?.map(tz => ({
            ...tz,
            short: getTimezoneAbbreviation(tz.id),
            long: getLongTimezoneName(tz.id),
            isDST: isDaylightTime(tz.id),
        }));
        const unfilteredOptions = res.data?.map(item => {
            const offsetObj = getUTCOffset(item.id);
            return {
                id: getTimezoneAbbreviation(item.id),
                label: `(${offsetObj?.friendlyString}) ${getLongTimezoneName(item.id)}`,
                offset: offsetObj.offset,
            };
        });
        const filteredOptions = unfilteredOptions.reduce((unique, option) => {
            if (unique.findIndex(item => item.id === option.id) === -1 && option.id !== "GMT") {
                unique.push(option);
            }
            return unique;
        }, []);
        setOptions(filteredOptions.sort((a, b) => a.offset - b.offset));
        const foundUsersTimezone = timezones.current.find(timezone => timezone.timezone_value === usersTimezone);
        if (foundUsersTimezone && props.autoSelectUsersTimezone) {
            const usersTimezoneAbreviation = getTimezoneAbbreviation(foundUsersTimezone.timezone_value);
            setValue(autoCompleteId, usersTimezoneAbreviation);
            setValue(props.id, foundUsersTimezone.timezone_value);
        }
        setLoading(false);
    };

    const handleError = (error: AxiosError<any>) => {
        setLoading(false);
        notify(getErrorFromArray(error), "Error");
    };

    useEffect(() => {
        setLoading(true);
        timezoneService.getTimezones(onSuccessGetTimezones, handleError);
    }, []);

    useEffect(() => {
        if (unfriendlyValue) {
            const abr = getTimezoneAbbreviation(unfriendlyValue);
            setValue(autoCompleteId, abr);
            return;
        }
        setValue(autoCompleteId, undefined);
    }, [unfriendlyValue]);

    return (
        <>
            <TextBoxComponent id={props.id} hidden />
            <AutoCompleteComponent
                {...props}
                id={autoCompleteId}
                options={options}
                loading={loading}
                key={options.length}
                onValueChange={(_, v) => {
                    const found = timezones.current.find(tz => tz.short === v);
                    if (found) {
                        setValue(props.id, found.timezone_value);
                        return;
                    }
                    setValue(props.id, undefined);
                }}
            />
        </>
    );
};

TimezoneSelect.defaultProps = {
    renderInput: undefined,
    options: undefined,
    autoSelectUsersTimezone: true,
};

export default TimezoneSelect;
