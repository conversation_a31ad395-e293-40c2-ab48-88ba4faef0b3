import {<PERSON>Control, FormHelperText, useTheme} from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormGroup from "@mui/material/FormGroup";
import {ReactNode} from "react";
import {Validate, useFormContext} from "react-hook-form";
import Checkbox, {ICheckboxProps} from "../../Atoms/Checkbox/Checkbox.component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps extends ICheckboxProps {
    id: string;
    label?: string | ReactNode;
    isRequired?: boolean;
    validationSchema?: Validate<any, any>;
    validateOnTheFly?: boolean;
    formGroupSx?: any;
    formControlSx?: any;
    helperText?: string;
    testid?: string;
    labelProps?: any;
    formControlClassName?: string;
    keepRegistered?: boolean;
}

const CheckboxWithLabel = ({formControlSx, color = "primary", ...props}: IProps) => {
    const {
        label,
        isRequired,
        validationSchema,
        helperText,
        validateOnTheFly,
        formGroupSx,
        labelProps,
        onChange,
        ...checkboxProps
    } = props;
    const testid = props.testid ? props.testid.replace(/\s/g, "") : props.id;
    const {register, formState, setValue, watch, trigger} = useFormContext();
    const value = Boolean(watch(props.id));
    const theme = useTheme();
    const hasSubmitted = formState.isSubmitted;
    const isInvalid = validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : hasSubmitted && formState.errors[props.id]
        ? true
        : false;
    return (
        <FormControl
            error={isInvalid}
            className={props.formControlClassName}
            sx={{
                ...(formControlSx || {}),
            }}>
            <FormGroup
                sx={{
                    ".Mui-error": {"span, p, h1, h2, h3, h4, h5, h6": {color: theme.palette.error[600]}},
                    ...(formGroupSx || {}),
                }}>
                <FormControlLabel
                    sx={{
                        height: "48px",
                        "&>span:last-child": {
                            display: "flex",
                            alignItems: "center",
                        },
                    }}
                    control={
                        <Checkbox
                            {...checkboxProps}
                            sx={{
                                ".Mui-error": {
                                    color: `${theme.palette.error[600]} !important`,
                                    outline: `${theme.palette.error[600]} solid 1px`,
                                },
                                ...(formGroupSx || {}),
                            }}
                            inputProps={
                                {
                                    ...(checkboxProps.inputProps || {}),
                                    "data-testid": testid,
                                    "aria-invalid": isInvalid ? "true" : "false",
                                } as any
                            }
                            {...register(props.id, {
                                required: isRequired,
                                validate: validationSchema,
                                shouldUnregister: props.keepRegistered ? false : true,
                            })}
                            onChange={(e, checked) => {
                                setValue(props.id, checked);
                                onChange?.(e, checked);
                                trigger(props.id);
                            }}
                            checked={value}
                            color={color}
                        />
                    }
                    label={
                        <Typography size={14} weight={500} sx={{color: "neutral.700"}} {...labelProps}>
                            {label}
                        </Typography>
                    }
                />
            </FormGroup>
            {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </FormControl>
    );
};

export default CheckboxWithLabel;
