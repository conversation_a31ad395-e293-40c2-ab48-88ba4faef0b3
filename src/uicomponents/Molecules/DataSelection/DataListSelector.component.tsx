import React, {<PERSON>actNode, useEffect, useState} from "react";
import {NO_DATA_AVAILABLE} from "../../../constants/errorMessages.constant";
import {FormProvider, useForm} from "react-hook-form";
import CheckboxWithLabel from "../CheckboxWithLabel/CheckboxWithLabel.component";
import Loader from "../../../utils/loader";
import Grid from "@mui/material/Grid";
import FileDownload from "@mui/icons-material/FileDownload";
import Typography from "../../Atoms/Typography/Typography.component";
import {FormControl, FormLabel, RadioGroup} from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import Radio from "../../Atoms/Radio/Radio.component";
import styles from "../../../styles/uicomponents/dataListSelectorComponent.module.sass";

interface IProps {
    description: string;
    data: any;
    setSelectedData: Function;
}

const ALL_COLUMNS = "all_columns";
const SELECT_COLUMNS = "select_columns";

const DataListSelectorComponent = (props: IProps) => {
    const formMethods = useForm();
    const [items, setItems] = useState<ReactNode>(<Loader inline loading />);
    const [radioValue, setRadioValue] = useState<string>("all_data");
    const handleChange = () => {
        props.setSelectedData(formMethods.getValues());
    };
    const handleRadioChange = event => {
        if (event.target.value === ALL_COLUMNS) {
            initFormValues();
        }
        setRadioValue(event.target.value);
    };
    const initFormValues = () => {
        const optionsKeys = Object.keys(props.data);
        if (optionsKeys.length > 0) {
            optionsKeys.forEach(objectKey => {
                formMethods.setValue(objectKey, true);
            });
            handleChange();
        }
    };

    useEffect(() => {
        props.setSelectedData(formMethods.getValues());
    }, [radioValue]);

    useEffect(() => {
        initFormValues();
    }, [items]);

    useEffect(() => {
        const optionsKeys = Object.keys(props.data);
        if (optionsKeys.length > 0) {
            setItems(
                <form onChange={handleChange} className="h-100 px-3 px-md-0 cpMainForm">
                    {optionsKeys.map(function (objectKey) {
                        return (
                            <Grid key={objectKey} container className="ms-0 data-list-selector-data-item">
                                <Grid item xs={12}>
                                    <CheckboxWithLabel
                                        id={objectKey}
                                        label={props.data[objectKey]}
                                        size={"small"}
                                        defaultChecked
                                    />
                                </Grid>
                            </Grid>
                        );
                    })}
                </form>,
            );
        } else {
            setItems(<h2>{NO_DATA_AVAILABLE}</h2>);
        }
    }, [props.data]);

    return (
        <FormProvider {...formMethods}>
            <Grid container className="ms-0" id="data-list-selector-container">
                <Grid item xs={12}>
                    <FormControl>
                        <FormLabel id="data-list-selector-group-label">
                            <Typography variant="inherit" color="primary" textAlign={"center"}>
                                <FileDownload color={"primary"}></FileDownload>{" "}
                                {props.description ?? "Select to export all data or specific data"}
                            </Typography>
                        </FormLabel>
                        <RadioGroup
                            row
                            aria-labelledby="data-list-selector-group-label"
                            defaultValue={ALL_COLUMNS}
                            onChange={handleRadioChange}
                            name="radio-buttons-group">
                            <FormControlLabel
                                className={"mt-4 mb-0"}
                                value={ALL_COLUMNS}
                                control={<Radio size="small" />}
                                label="All columns"
                            />
                            <FormControlLabel
                                className={"mt-4 mb-0"}
                                value={SELECT_COLUMNS}
                                control={<Radio size="small" />}
                                label="Select columns"
                            />
                        </RadioGroup>
                    </FormControl>
                    <hr />
                </Grid>
                <Grid item xs={12} id="data-list-selector-data" className={styles.containerWithMaxHeight}>
                    {radioValue === SELECT_COLUMNS && items}
                </Grid>
            </Grid>
        </FormProvider>
    );
};
export default DataListSelectorComponent;
