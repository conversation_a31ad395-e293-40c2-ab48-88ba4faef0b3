import {<PERSON><PERSON><PERSON>rol, FormHelperText, useTheme} from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormGroup from "@mui/material/FormGroup";
import {ReactNode} from "react";
import {Validate, useFormContext} from "react-hook-form";
import {validateURL} from "../../../utils/validation.util";
import Radio, {IRadioProps} from "../../Atoms/Radio/Radio.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";

export interface IRadioWithLabelProps extends IRadioProps {
    id: string;
    label?: string | ReactNode;
    isRequired?: boolean;
    validationSchema?: Validate<any>;
    validateOnTheFly?: boolean;
    formGroupSx?: any;
    formControlSx?: any;
    helperText?: string;
    testid?: string;
    wrapperClassName?: string;
    labelProps?: any;
    showAdditionalTextboxWhenSelected?: boolean;
    additionalTextBoxLabel?: string;
    additionalTextBoxMultiLine?: boolean;
    additionalTextBoxHelperText?: string;
    additionalTextBoxValidation?: {
        type: string;
        min?: number;
        max?: number;
        required?: boolean;
    };
    disableDefaultError?: boolean;
    additionalTextBoxTrigger?: boolean;
}

const RadioWithLabel = (props: IRadioWithLabelProps) => {
    const {
        label,
        isRequired,
        validationSchema,
        helperText,
        validateOnTheFly,
        formGroupSx,
        wrapperClassName,
        disableDefaultError,
        ...radioProps
    } = props;
    const testid = props.testid ? props.testid.replace(/\s/g, "") : `${props.id}-${props.value}`;
    const {register, formState, setValue, watch, trigger} = useFormContext();
    const watchValue = watch(props.id);
    const theme = useTheme();
    const hasSubmitted = formState.isSubmitted;
    const isInvalid = validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : hasSubmitted && formState.errors[props.id]
          ? true
          : false;

    return (
        <>
            <FormControl
                error={disableDefaultError ? false : isInvalid}
                className={wrapperClassName}
                sx={{
                    minHeight: "42px",
                    alignItems: "center",
                    justifyContent: "center",
                    ...(props?.formControlSx || {}),
                }}>
                <FormGroup
                    sx={{
                        ".Mui-error": {"span, p, h1, h2, h3, h4, h5, h6": {color: theme.palette.error[600]}},
                        ...(formGroupSx || {}),
                    }}>
                    <FormControlLabel
                        control={
                            <Radio
                                {...radioProps}
                                sx={{
                                    ".Mui-error": {
                                        color: `${theme.palette.error[600]} !important`,
                                        outline: `${theme.palette.error[600]} solid 1px`,
                                    },
                                    ...(formGroupSx || {}),
                                }}
                                inputProps={
                                    {
                                        ...(radioProps.inputProps || {}),
                                        "data-testid": testid,
                                        "aria-invalid": isInvalid ? "true" : "false",
                                    } as any
                                }
                                {...register(props.id, {
                                    required: isRequired,
                                    validate: v => {
                                        let isValid = true;
                                        if (isRequired && !v) {
                                            isValid = false;
                                        }
                                        if (validationSchema) {
                                            const customValid = validationSchema(v);
                                            isValid = !isValid ? false : Boolean(customValid);
                                        }
                                        return isValid;
                                    },
                                    shouldUnregister: true,
                                })}
                                onChange={(e, checked) => {
                                    setValue(props.id, props.value);
                                    radioProps.onChange && radioProps.onChange(e, checked);
                                    if (props.validateOnTheFly) {
                                        trigger(props.id);
                                    }
                                }}
                                checked={props.value === watchValue}
                            />
                        }
                        label={
                            <Typography variant={"body2"} fontInter {...(props.labelProps || {})}>
                                {label}
                            </Typography>
                        }
                    />{" "}
                </FormGroup>
                {helperText && <FormHelperText>{helperText}</FormHelperText>}
            </FormControl>
            {props.value === watchValue && props.showAdditionalTextboxWhenSelected && (
                <TextBoxComponent
                    id={props.id + "_additional_text"}
                    label={props.additionalTextBoxLabel || ""}
                    placeholder={props.additionalTextBoxLabel || ""}
                    isRequired={props.additionalTextBoxValidation ? props.additionalTextBoxValidation?.required : false}
                    minLength={props.additionalTextBoxValidation?.min}
                    maxLength={props.additionalTextBoxValidation?.max}
                    tooltipHelperText={props.additionalTextBoxHelperText}
                    multiline={props.additionalTextBoxMultiLine}
                    validationSchema={
                        props.additionalTextBoxValidation?.type === "url" ? v => validateURL(v) : undefined
                    }
                    validateOnTheFly
                    showCharacterCount
                    className="me-2"
                    containerStyles={{
                        minWidth: 300,
                    }}
                    onKeyUp={() => {
                        props.additionalTextBoxTrigger && trigger(props.id + "_additional_text");
                    }}
                />
            )}
        </>
    );
};
export default RadioWithLabel;
