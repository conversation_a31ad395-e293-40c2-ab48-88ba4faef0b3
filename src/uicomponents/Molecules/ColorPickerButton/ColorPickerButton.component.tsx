import React, {useRef, useState} from "react";
import FormatColorTextIcon from "@mui/icons-material/FormatColorText";
import {useTheme} from "@mui/material/styles";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import {IconButtonProps} from "@mui/material/IconButton";

interface IProps extends IconButtonProps<any> {
    onChange?: Function;
    id: string;
}

const ColorPickerButton = (props: IProps) => {
    const [color, setColor] = useState<string>("");
    const theme = useTheme();
    const inputRef = useRef<HTMLInputElement>(null);
    const {onChange, id, ...iconButtonProps} = props;

    const handleButtonClick = () => {
        if (inputRef.current) {
            inputRef.current.click();
        }
    };

    const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedColor = event.target.value;
        onChange && onChange(selectedColor);
        setColor(selectedColor);
    };

    return (
        <div>
            <input
                type="color"
                style={{
                    position: "absolute",
                    opacity: 0,
                    zIndex: -1,
                }}
                ref={inputRef}
                onChange={handleColorChange}
            />
            <IconButton
                onClick={handleButtonClick}
                sx={{
                    border: `1px solid ${theme.palette.neutral ? theme.palette.neutral[300] : "#ccc"}`,
                    backgroundColor: "#fff !important",
                }}
                id={id}
                {...iconButtonProps}>
                <FormatColorTextIcon htmlColor={color} />
            </IconButton>
        </div>
    );
};

export default ColorPickerButton;
