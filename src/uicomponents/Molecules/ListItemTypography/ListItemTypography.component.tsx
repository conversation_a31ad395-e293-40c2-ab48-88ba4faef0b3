import {ListItem, SxProps, Theme, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";

interface IListItemTypography {
    children: React.ReactNode;
    sx?: SxProps<Theme> | undefined;
}

export const ListItemTypography = ({children, sx}: IListItemTypography) => {
    const theme = useTheme();
    return (
        <ListItem
            sx={{
                display: "list-item",
                padding: "0",
            }}>
            <Typography fontInter variant="body3" color={theme.palette.neutral[700]} sx={{fontWeight: 400, ...sx}}>
                {children}
            </Typography>
        </ListItem>
    );
};
