import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import useTheme from "@mui/system/useTheme";
import ProgressBar from "../ProgressBar/progressBar.component";
import useActiveCompany from "../../../hooks/useActiveCompany";
import CPSlider from "../CPSlider/cpSlider.component";
import {useCompany} from "../../../hooks/fetches/useCompany";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Skeleton from "@mui/material/Skeleton";
import {useCompanyDashboard} from "../../../hooks/fetches/useCompanyDashboard";
import React, {useEffect, useState} from "react";
import AdditionalInfoModal from "../../Organism/AdditionalInfoModal/AdditionalInfoModal.component";
import AddToStackDialog from "../../Organism/AddToStackDialog/addToStackDialog.component";
import useIsViewingAsAffiliateParent from "../../../hooks/useIsViewingAsAffiliateParent";
import useRandomAdvertisement from "../../../hooks/useRandomAdvertisement";
import PlaidLinkButton from "../Plaid/plaidLinkButton.component";
import InviteClientsFromCSVDialog from "../../Organism/InviteClientsFromCSVModal/InviteClientsFromCSVDialog.component";
import routeConfig from "../../../constants/routeConfig";
import {Link, useNavigate} from "react-router-dom";
import usePricingLink from "../../../hooks/usePricingLink";

interface IOnboardingStepProps {
    title: string;
    description: string;
    action?: () => void;
    actionText: React.ReactNode;
    isCompleted?: boolean | undefined | null;
    boxSx?: any;
}

const COMPANY_STEPS = {
    mspFree: ["additionalQuestions", "syncAccounts", "addProducts"],
    mspPremium: ["additionalQuestions", "syncAccounts", "addContracts", "addProducts", "importCustomers"],
    directIt: ["additionalQuestions", "syncAccounts", "addContracts", "addProducts"],
    mspClient: ["additionalQuestions", "addContracts", "addProducts"],
    mspClientWithExpenses: ["additionalQuestions", "syncAccounts", "addContracts", "addProducts"],
    directItFree: ["additionalQuestions", "syncAccounts", "addProducts"],
};

const OnboardingStep = ({title, description, action, actionText, isCompleted = false, boxSx}: IOnboardingStepProps) => {
    const theme = useTheme();
    return (
        <Box sx={boxSx} display="flex" flexDirection="column" gap={2} paddingBottom={3}>
            <Typography
                sx={{marginTop: "24px"}}
                variant="16"
                color={theme.palette.blue[800]}
                fontWeight={"bold"}
                fontInter>
                {title}
            </Typography>

            <Typography variant="14" color={theme.palette.neutral[700]} fontInter>
                {description}
            </Typography>

            {!isCompleted && (
                <Typography
                    sx={{cursor: "pointer"}}
                    variant="14"
                    color={theme.palette.blue[600]}
                    fontWeight={"bold"}
                    fontInter
                    onClick={() => action && action()}>
                    {actionText}
                </Typography>
            )}
            {isCompleted && (
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                    }}>
                    <CheckCircleIcon sx={{fontSize: 16, color: theme.palette.success[500]}} />
                    <Typography variant="14" color={theme.palette.success[600]} fontInter fontWeight={600}>
                        {" "}
                        Done!
                    </Typography>
                </Box>
            )}
        </Box>
    );
};

const HIDE_ONBOARDING_PROGRESS_KEY = "hideOnboardingProgress";

const getLocalStorageKey = (companyId: string) => `${HIDE_ONBOARDING_PROGRESS_KEY}_${companyId}`;

export const shouldHideOnboardingProgress = (companyId: string) => {
    const localStorageKey = getLocalStorageKey(companyId);
    return localStorage.getItem(localStorageKey) === "true";
};

const OnboardingProgress = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const subscribeLink = usePricingLink();

    const {
        activeCompany,
        isLoading: activeCompanyLoading,
        isPremiumMSP,
        isDirect,
        isBasicMSP,
        hasIntegrationsAccess,
        isDirectBasic,
        isClientMsp,
        hasExpensesAccess,
        clientMspHasNoExpensesAccess,
    } = useActiveCompany();
    const {featureCount} = useCompanyDashboard(activeCompany?.id, {
        calls: {
            all: false,
            featureCount: true,
        },
    });
    const [showAddToStackModal, setShowAddToStackModal] = useState<boolean>(false);
    const [isAdditionalInfoModalOpen, setAdditionalInfoModalOpen] = useState(false);
    const [showImportCSVModal, setShowImportCSVModal] = useState<boolean>(false);
    const [hideOnboardingProgress, setHideOnboardingProgress] = useState<boolean>(false);

    useEffect(() => {
        if (activeCompany?.id) {
            setHideOnboardingProgress(shouldHideOnboardingProgress(activeCompany.id));
        }
    }, [activeCompany?.id]);

    const isViewingAsMSPBrand = useIsViewingAsAffiliateParent();
    const {sponsoredNaviStackVendors} = useRandomAdvertisement(isViewingAsMSPBrand ? undefined : "NaviStack Sponsored");

    const friendlyUrl = activeCompany?.friendly_url || "";
    const {companyMoreInfo} = useCompany(friendlyUrl, {
        isCompany: true,
        calls: {company: true, companyMoreInfo: true},
    });

    const isLoading = activeCompanyLoading || companyMoreInfo.isLoading || featureCount.isLoading;

    let STEPS = [
        {
            key: "additionalQuestions",
            title: "Answer a few additional questions",
            description: "Answer some questions about your business and get more relevant content.",
            action: () => setAdditionalInfoModalOpen(true),
            actionText: "Take me there",
            isCompleted: companyMoreInfo && companyMoreInfo?.data && companyMoreInfo?.data?.length > 0,
        },
        {
            key: "importCustomers",
            title: "Import your Customers",
            description: "Set up customers to manage their tech stack and expenses.",
            action: () => setShowImportCSVModal(true),
            actionText: "Import Customers",
            isCompleted: featureCount && featureCount?.data && featureCount?.data?.customers > 0,
        },
        {
            key: "syncAccounts",
            title: "Sync your accounts",
            description: "Connect your financial accounts to track expenses automatically.",
            actionText:
                isBasicMSP || isDirectBasic || clientMspHasNoExpensesAccess ? (
                    <Link to={subscribeLink} target="_blank">
                        <Typography variant="14" color={theme.palette.blue[600]} fontWeight={600} fontInter>
                            Upgrade now
                        </Typography>
                    </Link>
                ) : (
                    <PlaidLinkButton
                        onSuccess={() => {
                            featureCount.refetch();
                        }}
                        isTypography
                        typographyProps={{
                            color: theme.palette.blue[600],
                            fontWeight: 600,
                            fontInter: true,
                            sx: {cursor: "pointer"},
                        }}>
                        Connect accounts
                    </PlaidLinkButton>
                ),
            isCompleted: featureCount && featureCount?.data && featureCount?.data?.accounts_synced > 0,
        },
        {
            key: "addContracts",
            title: "Add your Contract Information",
            description: "Upload your contract files, information and dates to keep track of your expenses.",
            action: () => {
                navigate(routeConfig.AddCompanyContract.path);
            },
            actionText: "Add Contracts",
            isCompleted: featureCount && featureCount?.data && featureCount?.data?.contracts > 0,
        },
        {
            key: "addProducts",
            title: isClientMsp ? "Start adding your products" : "Start Building your MSP Stack",
            description: isClientMsp ? "Add Vendors and Products to your ProductTracker." : "Add vendors to your NaviStack and experience an easier way to navigate your stack.",
            action: () => setShowAddToStackModal(true),
            actionText: "Add Product",
            isCompleted: featureCount && featureCount?.data && featureCount?.data?.navi_stacks > 0,
        },
    ];

    // Determine which steps to use based on company type
    let companyType = "mspFree";

    if (isPremiumMSP && hasIntegrationsAccess) {
        companyType = "mspPremium";
    } else if (isDirect) {
        companyType = isDirectBasic ? "directItFree" : "directIt";
    } else if (isClientMsp) {
        companyType = hasExpensesAccess ? "mspClientWithExpenses" : "mspClient";
    }

    const stepsForCompanyType = STEPS.filter(step => COMPANY_STEPS[companyType].includes(step.key));
    
    // Calculate completed steps
    const completedSteps = stepsForCompanyType.filter(step => step.isCompleted).length;
    // Calculate progress percentage
    const totalSteps = stepsForCompanyType.length;
    const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    // Find the next incomplete step
    const nextIncompleteStepIndex = stepsForCompanyType.findIndex(step => !step.isCompleted);

    const stepsCompleted = completedSteps === totalSteps;
    if (hideOnboardingProgress) {
        return null;
    }
    return (
        <BorderedBox
            sx={{
                gap: "0",
                borderRadius: 2,
            }}>
            <Box display={"flex"} justifyContent={"space-between"}>
                <Typography variant="18" color={theme.palette.neutral[700]} fontWeight={600}>
                    Next Steps:
                </Typography>
                <Typography variant="16" color={theme.palette.neutral[500]} fontWeight={700} poppins>
                    {isLoading ? <Skeleton variant="text" height={30} width={30} /> : `${completedSteps}/${totalSteps}`}
                </Typography>
            </Box>
            <ProgressBar
                value={progressPercentage}
                isLoading={isLoading}
                colors={{
                    25: "error",
                    50: "warning",
                    75: "blue",
                    100: "success",
                }}
                style={{
                    height: "10px",
                    marginTop: "5px",
                    borderRadius: "24px",
                    minWidth: "195px",
                    padding: "1px",
                }}
            />

            {isLoading && (
                <>
                    <Skeleton sx={{marginTop: "10px"}} variant="text" height={50} width={"100%"} />
                    <Skeleton variant="text" height={100} width={"100%"} />
                </>
            )}
            {!isLoading && (
                <>
                    {!stepsCompleted && (
                        <CPSlider slidesToShow={1} initialSlide={nextIncompleteStepIndex}>
                            {stepsForCompanyType.map(step => {
                                return <OnboardingStep {...step} />;
                            })}
                        </CPSlider>
                    )}

                    {stepsCompleted && (
                        <OnboardingStep
                            boxSx={{marginLeft: "0"}}
                            title={" You are all set up!"}
                            description={"Enjoy your company account and available features."}
                            actionText={"Close"}
                            action={() => {
                                if (activeCompany?.id) {
                                    const localStorageKey = getLocalStorageKey(activeCompany.id);
                                    localStorage.setItem(localStorageKey, "true");
                                    setHideOnboardingProgress(true);
                                }
                            }}
                        />
                    )}
                </>
            )}
            {isAdditionalInfoModalOpen && (
                <AdditionalInfoModal
                    open={isAdditionalInfoModalOpen}
                    onClose={() => setAdditionalInfoModalOpen(false)}
                    onChange={() => {
                        companyMoreInfo.refetch();
                    }}
                />
            )}
            {showAddToStackModal && (
                <AddToStackDialog
                    open={showAddToStackModal}
                    onClose={() => {
                        setShowAddToStackModal(false);
                    }}
                    onSave={() => {
                        featureCount.refetch();
                    }}
                    companyId={activeCompany?.id || ""}
                    allowMultipleCategories={false}
                    sponsoredNaviStackVendors={sponsoredNaviStackVendors}
                />
            )}
            {showImportCSVModal && (
                <InviteClientsFromCSVDialog
                    onVendorsImported={() => {
                        featureCount.refetch();
                    }}
                    friendlyURL={activeCompany?.friendly_url ?? ""}
                    open={showImportCSVModal}
                    onClose={() => setShowImportCSVModal(false)}
                />
            )}
        </BorderedBox>
    );
};

export default OnboardingProgress;
