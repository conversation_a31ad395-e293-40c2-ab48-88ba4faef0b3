import TextBoxComponent, {ITextBoxProps} from "../../../../FormControls/TextBox/textBox.component";

interface IProps extends Omit<ITextBoxProps, "id"> {
    id?: string;
}

const MAX_LENGTH = 250;

const OtherConsiderationsInput = ({...props}: IProps) => (
    <TextBoxComponent
        id="other_considerations"
        label="Other Considerations"
        placeholder={"Any other important things to consider?"}
        validateOnTheFly
        multiline
        rows={8}
        maxRows={8}
        maxLength={MAX_LENGTH}
        showCharacterCount
        maxLengthErrorMessage={`The detail entered exceeds the ${MAX_LENGTH}-character limit.`}
        {...props}
    />
);

export default OtherConsiderationsInput;
