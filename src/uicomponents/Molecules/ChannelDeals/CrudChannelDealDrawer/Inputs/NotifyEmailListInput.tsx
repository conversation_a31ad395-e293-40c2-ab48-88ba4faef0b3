import UserListInput, {IEmailListInputProps} from "../../../UserListInput/UserListInput.component";
import {validateEmail} from "../../../../../utils/validation.util";

interface IProps extends Omit<IEmailListInputProps, "id" | "options" | "renderInput"> {
    id?: string;
    isAdmin?: boolean;
    maxLimitTagsToSingleLine?: number;
}

const NotifyEmailListInput = ({...props}: IProps) => (
    <UserListInput
        sx={{
            // @todo remove workaround to hide input of autocomplete on readOnly
            "& input": {
                visibility: props.readOnly ? "hidden !important" : "initial",
                cursor: "pointer",
            },
        }}
        style={{width: "100%"}}
        id="email_receiving_leads"
        label="Notify Email"
        placeholder="Enter the emails to receive notifications"
        validateOption={v => validateEmail(v)}
        validateOnTheFly
        disableClearable
        isRequired
        helperText={isInValid =>
            isInValid
                ? "Required Field"
                : props.isAdmin
                ? "Enter the email address(es) or paste a comma separated list of email addresses. "
                : ""
        }
        showDropdownIcon={false}
        limitTagsToSingleLine={!props.readOnly}
        maxLimitTagsToSingleLine={props.maxLimitTagsToSingleLine || 3}
        {...props}
    />
);

export default NotifyEmailListInput;
