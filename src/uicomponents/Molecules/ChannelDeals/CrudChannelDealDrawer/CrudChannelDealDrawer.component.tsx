import Divider from "@mui/material/Divider";
import {FormProvider, useForm} from "react-hook-form";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import Box from "../../../../uicomponents/Atoms/Box/Box.component";
import Typography from "../../../../uicomponents/Atoms/Typography/Typography.component";
import Drawer from "../../../../uicomponents/Atoms/Drawer/Drawer.component";
import Button from "../../../../uicomponents/Atoms/Button/Button.Component";
import {useMediaQuery, useTheme} from "@mui/material";
import {IChannelDealCreateHeader, IChannelDealData, IUpdateHeader} from "../../../../Interfaces/channelDeal.interface";
import {useCallback, useEffect, useState} from "react";
import {channelDealService} from "../../../../services/channelDealService";
import useNotification from "../../../../hooks/useNotification";
import useChannelDealsStatuses, {IOptionsResponse} from "../../../../hooks/fetches/useChannelDealsStatuses";
import {DATE_FORMAT_Year_Month_Day, formatDate, MONTH_DAY_YEAR} from "../../../../utils/formatDate";
import ChannelDealsStatusBadge from "../../../../uicomponents/Badges/channelDealsStatusBadge.component";
import {
    CHANNEL_DEALS_STATUSES_APPROVED,
    CHANNEL_DEALS_STATUSES_DECLINED,
    CHANNEL_DEALS_STATUSES_INACTIVE,
    CHANNEL_DEALS_STATUSES_PENDING,
} from "../../../../constants/channeldeals.constant";
import {getErrorFromArray} from "../../../../utils/error.util";
import BlockIcon from "@mui/icons-material/Block";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import usePermissions from "../../../../hooks/usePermissions";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import ApproveIcon from "@mui/icons-material/Done";
import DeclineIcon from "@mui/icons-material/Close";
import DeclineConfirmation from "../../../../pages/Admin/ChannelDealsManagement/Deals/DeclineConfirmation";
import DeleteOutlineOutlined from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CrudChannelDealTab, {selectedStatusOption} from "./CrudChannelDealTab";
import ChannelDealHistoryTab from "./ChannelDealHistoryTab.component";
import BasicTabs from "../../Tabs/tabs.component";
import {useQueryHelper} from "../../../../hooks/helpers/useQueryHelper";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import {
    getApproveDialogConfigs,
    getDeactivationDialogConfigs,
    getDeleteDialogConfigs,
} from "../../../../pages/Admin/ChannelDealsManagement/Deals/getDialogConfigs.util";
import {useQueryClient} from "@tanstack/react-query";
import {QUERY_ADMIN_CHANNEL_DEALS_PENDING} from "../../../../constants/queryKeys.constant";

export interface ICrudChanelDealDrawerProps {
    open: boolean;
    onClose?: () => void;
    onCreate?: () => void;
    onUpdate?: () => void;
    onDeletedDeal?: () => void;
    channelDeal?: IChannelDealData;
    readMode?: boolean;
    isAdminPanel?: boolean;
}

const CrudChannelDealDrawer = ({
    open,
    onClose,
    channelDeal,
    onCreate,
    onUpdate,
    onDeletedDeal,
    readMode = true,
    isAdminPanel = false,
}: ICrudChanelDealDrawerProps) => {
    const {activeCompany} = useActiveCompany();
    const {invalidateMatchedQueries} = useQueryHelper();
    const formMethods = useForm({shouldUnregister: false});
    const theme = useTheme();
    const isMdOrSmaller = useMediaQuery(theme.breakpoints.down("md"));
    const {hasPermissions, isLoadingPermissions} = usePermissions();
    const queryClient = useQueryClient();
    const [activeTab, setActiveTab] = useState("info");

    const [isSaving, setIsSaving] = useState<{
        saving: boolean;
        approving: boolean;
        deactivating: boolean;
        declining: boolean;
        deleting: boolean;
    }>({
        saving: false,
        approving: false,
        deactivating: false,
        declining: false,
        deleting: false,
    });

    const [status, setStatus] = useState<Array<{id: string; name: string}>>([]);
    const [currentStatus, setCurrentStatus] = useState<IOptionsResponse>();
    const [currentReadMode, setCurrentReadMode] = useState<boolean>(readMode);

    const [updatedDeal, setUpdatedDeal] = useState<IChannelDealData | undefined>(undefined);
    const [declineDeal, setDeclineDeal] = useState<IChannelDealData | undefined>(undefined);

    const notify = useNotification();
    const {getStatus} = useChannelDealsStatuses();
    const isEditMode = channelDeal && !currentReadMode;
    const isReadMode = channelDeal && currentReadMode;

    const checkLoading = () =>
        isSaving.saving || isSaving.approving || isSaving.deactivating || isSaving.declining || isSaving.deleting;

    const hasEditAccess =
        !isLoadingPermissions &&
        hasPermissions([
            isAdminPanel
                ? PERMISSION_GROUPS.ADMIN_MANAGE_CHANNEL_DEALS_UPDATE
                : PERMISSION_GROUPS.MANAGE_CHANNEL_DEALS_UPDATE,
        ]);

    const dealsNameWatch = formMethods.watch("deal_name");

    useEffect(() => {
        setCurrentStatus(getStatus(status[0]?.id));
    }, [status]);

    useEffect(() => {
        if (!channelDeal && activeCompany && !isAdminPanel) {
            formMethods.setValue("company_id", activeCompany.id);
        }
    }, [activeCompany]);

    useEffect(() => {
        setCurrentReadMode(readMode);
    }, [readMode]);

    useEffect(() => {
        if (open && !!channelDeal?.id) {
            formMethods.reset(getDefaultValues(updatedDeal || channelDeal));
            invalidateMatchedQueries(["channelDealsHistory", channelDeal.id]);
            invalidateMatchedQueries(["channelDealsHistoryFilters", channelDeal.id]);
        } else if (open && !isAdminPanel) {
            formMethods.reset({
                company_name: activeCompany?.name,
            });
        }
    }, [channelDeal?.id, open, activeTab]);

    useEffect(() => {
        if (!open) {
            formMethods.reset({
                category_name: "",
                category_id: "",
                sub_category_name: "",
                sub_category_id: "",
                email_receiving_leads: "",
                publish_immediately: "",
            });
        } else {
            if (!channelDeal) return;
            invalidateMatchedQueries(["channelDealsHistory", channelDeal.id]);
            invalidateMatchedQueries(["channelDealsHistoryFilters", channelDeal.id]);
        }
    }, [open]);

    useEffect(() => {
        if (!channelDeal) {
            formMethods.setValue("company_id", "");
        }
    }, [channelDeal?.id, open]);

    const updateDeal = useCallback(
        (dealId: string, data: IUpdateHeader, message: string, onSuccess?: () => void) => {
            const updateChannelDealService = isAdminPanel
                ? channelDealService.adminUpdateChannelDeal
                : channelDealService.updateChannelDeal;

            updateChannelDealService(
                dealId,
                data,
                response => {
                    onUpdate && onUpdate();
                    onSuccess && onSuccess();
                    setCurrentReadMode(true);
                    formMethods.reset(getDefaultValues(response.data));
                    setUpdatedDeal(response.data);
                    invalidateMatchedQueries(["channelDealsHistory", response.data.id ?? ""]);
                    invalidateMatchedQueries(["channelDealsHistoryFilters", response.data.id ?? ""]);
                    queryClient.refetchQueries([QUERY_ADMIN_CHANNEL_DEALS_PENDING]);
                    notify(message);
                },
                err => notify(getErrorFromArray(err), "Error"),
            ).finally(() => {
                setIsSaving({declining: false, approving: false, saving: false, deactivating: false, deleting: false});
            });
        },
        [channelDeal?.id, open, onUpdate],
    );

    const handleSubmit = async () => {
        setIsSaving({...isSaving, saving: true});

        const passedValidation = await formMethods.trigger();

        if (!passedValidation) {
            setIsSaving({...isSaving, saving: false});
            return;
        }

        const values = formMethods.getValues();
        if (channelDeal) {
            updateDeal(channelDeal.id, values, "Deal Updated");
        } else {
            const createChannelDealService = isAdminPanel
                ? channelDealService.adminCreateChannelDeal
                : channelDealService.createChannelDeal;

            createChannelDealService(
                values as IChannelDealCreateHeader,
                () => {
                    onCreate && onCreate();
                    notify("Deal Created");
                    queryClient.refetchQueries([QUERY_ADMIN_CHANNEL_DEALS_PENDING]);
                },
                err => notify(getErrorFromArray(err), "Error"),
            ).finally(() => {
                setIsSaving({...isSaving, saving: false});
                onCreate && onCreate();
            });
        }
    };

    const handleOnclose = () => {
        setCurrentReadMode(true);
        setStatus([]);

        onClose && onClose();
    };

    const handleOnCancel = () => {
        formMethods.reset(getDefaultValues(updatedDeal || channelDeal));

        const values = formMethods.getValues();
        const status = getStatus(values.status_id);
        setStatus([{id: status?.id || "", name: status?.name || ""}]);
        setCurrentReadMode(true);
    };

    const handleDeactivateClick = async () => {
        if (channelDeal) {
            const answer = await getUserConfirmation("", getDeactivationDialogConfigs(channelDeal));
            if (answer.value) {
                setIsSaving({...isSaving, deactivating: true});
                const inactiveStatus = getStatus(CHANNEL_DEALS_STATUSES_INACTIVE);
                updateDeal(channelDeal.id, {status_id: inactiveStatus?.id || ""}, "Deal Deactivated");
            }
        }
    };

    const handleOnCloseDeclineDeal = useCallback(() => {
        setDeclineDeal(undefined);
    }, [declineDeal]);

    const handleApproveClick = async () => {
        if (channelDeal) {
            const answer = await getUserConfirmation("", getApproveDialogConfigs(channelDeal));
            setIsSaving({...isSaving, approving: true});
            if (answer.value) {
                const approvedStatus = getStatus(CHANNEL_DEALS_STATUSES_APPROVED);
                updateDeal(channelDeal.id, {status_id: approvedStatus?.id || ""}, "Deal Approved");
            }
        }
    };

    const handleDeclineDeal = useCallback(
        reason => {
            if (declineDeal) {
                setIsSaving({...isSaving, declining: true});
                const declineStatus = getStatus(CHANNEL_DEALS_STATUSES_DECLINED);
                updateDeal(
                    declineDeal.id,
                    {status_id: declineStatus?.id || "", reject_reason: reason},
                    "Deal Declined",
                    () => {
                        setDeclineDeal(undefined);
                    },
                );
            }
        },
        [declineDeal],
    );

    const handleDeleteClick = useCallback(async () => {
        if (!channelDeal) {
            return "";
        }
        const answer = await getUserConfirmation("", getDeleteDialogConfigs(channelDeal));
        if (answer.value) {
            setIsSaving({...isSaving, deleting: true});
            channelDealService
                .deleteChannelDeal(channelDeal.id)
                .then(() => {
                    notify("Deal Deleted");
                    onDeletedDeal && onDeletedDeal();
                })
                .catch(err => notify(getErrorFromArray(err), "Error"))
                .finally(() => {
                    setIsSaving({...isSaving, deleting: false});
                });
        }
    }, [channelDeal?.id]);

    const handleOnStatusChange = (status: selectedStatusOption) => {
        setStatus(status);
    };

    const getDefaultValues = (channelDealData: IChannelDealData | undefined) => {
        if (!channelDealData) {
            return {};
        }
        setStatus([{id: channelDealData.status_id, name: channelDealData.status_name}]);
        const endDate = channelDealData.end_date ? channelDealData.end_date.slice(0, 10) : "";

        return {
            status_name:
                !isAdminPanel && channelDealData.status_name === "Pending"
                    ? "Under Review"
                    : channelDealData.status_name,
            deal_name: channelDealData.deal_name,
            category_name: channelDealData.category_name,
            category_id: channelDealData.category_id,
            sub_category_id: channelDealData.sub_category_id,
            sub_category_name: channelDealData.sub_category_name,
            end_date: formatDate(endDate, DATE_FORMAT_Year_Month_Day),
            offer_details: channelDealData.offer_details,
            other_considerations: channelDealData.other_considerations,
            reject_reason: channelDealData.reject_reason,
            email_receiving_leads: channelDealData.email_receiving_leads.split(","),
            status_id: channelDealData.status_id,
            reviewer_name: channelDealData.reviewer_name,
            created_by_name: channelDealData.created_by_name,
            created_at: formatDate(channelDealData.created_at, MONTH_DAY_YEAR),
            company_has_approved_deals: channelDealData.company_has_approved_deals ? "Yes" : "No",
            is_featured: channelDealData.is_featured,
            company_name: channelDealData.company_name,
            company_id: channelDealData.company_id,
        };
    };

    const tabs = [{id: "info", label: "Deal Information", Component: null}];

    if (currentReadMode && channelDeal) {
        tabs.push({
            id: "history",
            label: "History",
            Component: null,
        });
    }
    const tabContent = (
        <Box
            sx={{
                flexGrow: 1,
                overflowY: "auto",
                paddingLeft: 1,
                maxHeight: "100%",
                height: "100%",
                width: "100%",
            }}>
            {activeTab === "info" ? (
                <CrudChannelDealTab
                    channelDeal={channelDeal}
                    readMode={currentReadMode}
                    isAdminPanel={isAdminPanel}
                    selectedStatus={status}
                    onStatusChange={handleOnStatusChange}
                />
            ) : activeTab === "history" ? (
                <ChannelDealHistoryTab deal_id={channelDeal?.id} active_company_id={activeCompany?.id} />
            ) : null}
        </Box>
    );

    const renderTitle = useCallback(() => {
        if (!channelDeal) {
            return "New Channel Deal";
        } else {
            if (isReadMode) {
                return "View Channel Deal Details";
            }
            return "Edit Channel Deal";
        }
    }, [channelDeal?.id, currentReadMode]);

    const renderButtons = useCallback(
        () => (
            <>
                {!isReadMode && (
                    <Button
                        id="save-changes"
                        variant="contained"
                        color="blue"
                        style={{textTransform: "uppercase"}}
                        onClick={handleSubmit}
                        loading={isSaving.saving}>
                        <CheckOutlinedIcon sx={{marginRight: 1}} />
                        Save {!isAdminPanel ? "& Submit" : ""}
                    </Button>
                )}

                {isReadMode && hasEditAccess && (
                    <>
                        {currentStatus?.value_key === CHANNEL_DEALS_STATUSES_PENDING && (
                            <>
                                {isAdminPanel && (
                                    <>
                                        <Button
                                            style={{textTransform: "uppercase"}}
                                            id="approve-deal"
                                            variant="contained"
                                            color="success"
                                            disabled={checkLoading()}
                                            onClick={handleApproveClick}
                                            loading={isSaving.approving}>
                                            <ApproveIcon sx={{marginRight: 1, color: "white", fontSize: "18px"}} />{" "}
                                            Approve
                                        </Button>

                                        <Button
                                            style={{textTransform: "uppercase"}}
                                            id="decline-deal"
                                            variant="contained"
                                            color="error"
                                            onClick={() => {
                                                setDeclineDeal(channelDeal);
                                            }}
                                            disabled={checkLoading()}
                                            loading={isSaving.declining}>
                                            <DeclineIcon sx={{marginRight: 1, color: "white", fontSize: "18px"}} />{" "}
                                            Decline
                                        </Button>
                                    </>
                                )}
                                <Button
                                    style={{textTransform: "uppercase"}}
                                    id="delete-deal"
                                    variant="outlined"
                                    color="error"
                                    onClick={handleDeleteClick}
                                    disabled={checkLoading()}
                                    loading={isSaving.deleting}>
                                    <DeleteOutlineOutlined sx={{marginRight: 1, fontSize: "18px"}} /> Delete
                                </Button>
                            </>
                        )}
                        <Button
                            style={{textTransform: "uppercase"}}
                            id="edit"
                            variant="contained"
                            color="blue"
                            onClick={() => {
                                setActiveTab("info");
                                setCurrentReadMode(false);
                            }}
                            disabled={checkLoading()}>
                            <EditOutlinedIcon sx={{marginRight: 1, fontSize: "18px"}} /> Edit
                        </Button>
                        {currentStatus?.value_key === CHANNEL_DEALS_STATUSES_APPROVED && (
                            <Button
                                style={{textTransform: "uppercase"}}
                                id="deactivate"
                                variant="outlined"
                                color="blue"
                                onClick={handleDeactivateClick}
                                disabled={checkLoading()}
                                loading={isSaving.deactivating}>
                                <BlockIcon sx={{marginRight: 1, fontSize: "18px"}} /> Deactivate
                            </Button>
                        )}
                    </>
                )}

                <Button
                    id="close"
                    variant="text"
                    color="blue"
                    onClick={() => {
                        if (isEditMode) {
                            handleOnCancel();
                            return;
                        }

                        handleOnclose();
                    }}
                    disabled={checkLoading()}>
                    {isReadMode ? "CLOSE" : "CANCEL"}
                </Button>
            </>
        ),
        [isReadMode, channelDeal, open, currentStatus, isSaving],
    );

    return (
        <ErrorBoundary>
            <Drawer
                open={open}
                onClose={handleOnclose}
                sx={{
                    ".MuiDrawer-paper": {
                        maxWidth: {xs: "100vw", lg: "50vw"},
                        width: {xs: "100vw", lg: "50vw"},
                        padding: 2,
                        maxHeight: "100vh",
                        overflowY: "hidden",
                    },
                }}
                anchor="right">
                <FormProvider {...formMethods}>
                    <Box
                        display="flex"
                        flexDirection="column"
                        paddingBottom={2}
                        borderBottom={`1px solid ${theme.palette.neutral[500]}`}
                        width="100%"
                        gap={1}>
                        <Typography
                            fontInter
                            variant="body2"
                            fontWeight={500}
                            fontSize="20px !important"
                            color={theme.palette.info[800]}>
                            {renderTitle()}
                        </Typography>
                        {channelDeal?.deal_name && (
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    fontWeight: 600,
                                    color: theme.palette.neutral[600],
                                    gap: 1,
                                }}>
                                <span>{dealsNameWatch}</span>
                                <ChannelDealsStatusBadge statusKey={currentStatus?.id || ""} />
                            </Box>
                        )}
                    </Box>
                    <Box
                        sx={{
                            flexGrow: 1,
                            flexShrink: 1,
                            paddingTop: 2,
                            overflowY: "hidden",
                        }}>
                        <BasicTabs
                            orientation={"horizontal"}
                            defaultTab={"info"}
                            selectedTab={!currentReadMode ? "info" : undefined}
                            tabs={tabs}
                            hideBorder
                            styleBox={{alignItems: "flex-start", flexDirection: "column"}}
                            variant="scrollable"
                            wrapperSx={{
                                display: "flex",
                                maxHeight: "100%",
                            }}
                            onBeforeChange={(_, newValue) => {
                                setActiveTab(tabs[newValue]?.id);
                                return true;
                            }}
                            tabsSx={{
                                minWidth: 218,
                                ".MuiTabs-indicator": {
                                    display: "none !important",
                                },
                            }}
                            tabSx={{
                                whiteSpace: "nowrap",
                                textAlign: "left",
                                alignItems: "flex-start",
                            }}
                            selectedTabSx={{
                                borderBottom: `2px solid ${theme.palette.primary["main"]}`,
                            }}
                            customTabContent={isMdOrSmaller ? tabContent : null}>
                            {isMdOrSmaller ? null : tabContent}
                        </BasicTabs>
                    </Box>

                    <Box sx={{width: "100%", paddingTop: 2, backgroundColor: "#fff"}}>
                        <Divider sx={{marginY: 2}} />
                        <Box width="100%" display="flex" flexDirection="row" gap={1}>
                            {renderButtons()}
                        </Box>
                    </Box>
                    {declineDeal && (
                        <DeclineConfirmation
                            open={true}
                            dealName={declineDeal.deal_name}
                            companyName={declineDeal.company_name}
                            onConfirm={handleDeclineDeal}
                            onClose={handleOnCloseDeclineDeal}
                        />
                    )}
                </FormProvider>
            </Drawer>
        </ErrorBoundary>
    );
};

export default CrudChannelDealDrawer;
