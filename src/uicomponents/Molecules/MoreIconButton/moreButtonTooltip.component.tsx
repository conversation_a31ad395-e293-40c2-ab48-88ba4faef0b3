import {styled} from "@mui/material/styles";
import Tooltip, {tooltipClasses} from "@mui/material/Tooltip";

const MoreButtonTooltip = styled(({className, ...props}: any) => <Tooltip {...props} classes={{popper: className}} />)(
    () => ({
        [`& .${tooltipClasses.tooltip}`]: {
            background: "#FFFFFF",
            boxShadow: "0px 2px 16px rgba(0, 0, 0, 0.15)",
            borderRadius: "16px",
            minWidth: 150,
        },
        [`& .${tooltipClasses.arrow}`]: {
            color: "#FFFFFF",
            width: 16,
        },
    }),
);

export default MoreButtonTooltip;
