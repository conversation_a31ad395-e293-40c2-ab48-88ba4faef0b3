import Checkbox from "@mui/material/Checkbox";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormGroup from "@mui/material/FormGroup";
import useTheme from "@mui/material/styles/useTheme";
import React from "react";
import {Controller, useFormContext} from "react-hook-form";
import {validateURL} from "../../../utils/validation.util";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";

interface ICheckboxGroupOption {
    label: string;
    value: any;
    showAdditionalTextboxWhenSelected?: boolean;
    additionalTextBoxLabel?: string;
    additionalTextBoxMultiLine?: boolean;
    additionalTextBoxValidation?: {
        type: string;
        min?: number;
        max?: number;
        required?: boolean;
    };
    additionalTextBoxTrigger?: boolean;
}

interface IProps {
    options: ICheckboxGroupOption[];
    id: string;
    label?: string;
    minSelected?: number;
    maxSelected?: number;
    validateOnTheFly?: boolean;
    disabled?: boolean;
}

const CheckboxGroup = (props: IProps) => {
    const theme = useTheme();
    const {formState, trigger} = useFormContext();
    const isInvalid = props.validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : formState.isSubmitted && formState.errors[props.id]
        ? true
        : false;

    return (
        <Controller
            name={props.id}
            rules={{
                validate: (v: any) => {
                    let valid = true;
                    const filteredLength = Object.values(v || {})?.filter(v => v)?.length;
                    if (props.minSelected && filteredLength < props.minSelected) {
                        valid = false;
                    }
                    if (props.maxSelected && filteredLength > props.maxSelected) {
                        valid = false;
                    }
                    return valid;
                },
            }}
            render={({field: {onChange, value, name, ref}}) => {
                const handleChange = (e: React.ChangeEvent<HTMLInputElement>, checked: boolean, optionValue: any) => {
                    const newValue = {...value, [optionValue]: checked};
                    onChange(newValue);
                    if (props.validateOnTheFly) {
                        trigger(props.id);
                    }
                };

                return (
                    <div className="d-flex flex-column">
                        <input hidden ref={ref} name={name} value={value} />
                        <Typography variant="body2" color={isInvalid ? theme.palette.error["main"] : "#000"}>
                            {props.label}
                        </Typography>
                        <div className="d-flex flex-row flex-wrap">
                            {props.options.map((option: ICheckboxGroupOption) => (
                                <>
                                    <FormControl key={option.value}>
                                        <FormGroup>
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        onChange={(e, checked) =>
                                                            handleChange(e, checked, option.value)
                                                        }
                                                        checked={value?.[option.value] || false}
                                                        disabled={props.disabled}
                                                    />
                                                }
                                                label={<Typography variant={"body2"}>{option.label}</Typography>}
                                            />
                                        </FormGroup>
                                    </FormControl>
                                    {value?.[option.value] && option.showAdditionalTextboxWhenSelected && (
                                        <TextBoxComponent
                                            id={option.value + "_additional_text"}
                                            label={option.additionalTextBoxLabel || ""}
                                            placeholder={option.additionalTextBoxLabel || ""}
                                            isRequired={
                                                option.additionalTextBoxValidation
                                                    ? option.additionalTextBoxValidation?.required
                                                    : false
                                            }
                                            multiline={option.additionalTextBoxMultiLine}
                                            minLength={option.additionalTextBoxValidation?.min}
                                            maxLength={option.additionalTextBoxValidation?.max}
                                            validationSchema={
                                                option.additionalTextBoxValidation?.type === "url"
                                                    ? v => validateURL(v)
                                                    : undefined
                                            }
                                            showCharacterCount
                                            className="me-2"
                                            disabled={props.disabled}
                                            containerStyles={{
                                                minWidth: 300,
                                            }}
                                            onKeyUp={() => {
                                                option.additionalTextBoxTrigger &&
                                                    trigger(option.value + "_additional_text");
                                            }}
                                            validateOnTheFly
                                        />
                                    )}
                                </>
                            ))}
                        </div>
                    </div>
                );
            }}
        />
    );
};

export default CheckboxGroup;
