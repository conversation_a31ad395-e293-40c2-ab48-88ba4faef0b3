import ErrorOutlined from "@mui/icons-material/ErrorOutlined";
import Search from "@mui/icons-material/Search";
import {AxiosResponse} from "axios";
import {useEffect, useMemo, useRef, useState} from "react";
import {useFormContext} from "react-hook-form";
import {ICompany} from "../../../Interfaces/company.interface";
import {TCompanyType} from "../../../constants/companyType.constant";
import {useDebounce} from "../../../hooks/useDebounce";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";

interface IProps extends IAutoCompleteProps<any, any, any, any> {
    noResultsCallback?: (isNoResults: boolean, id: string, searchValue: string) => void;
    clearOnSelect?: boolean;
    defaultCompanyId?: string;
    onValueChange?: (id: string, oldValue: ICompanyOption, newValue: ICompanyOption) => void;
    defaultSearch?: string;
    defaultOptions?: any[];
    preventCloseOnSelect?: boolean;
    showCheckboxes?: boolean;
    //? Options for showing checkbox with single select
    selectedcompanyIds?: string[];
    noOptionsContent?: string | React.ReactNode | undefined;
    companyTypes?: TCompanyType[];
    disabledCompanies?: string[];
    disabledCompaniesText?: string;
    keepRegistered?: boolean;
    companiesNeedClaimers?: boolean;
}

interface ICompanyOption {
    label: string;
    id: string;
    disabled?: boolean;
    disabledText?: string;
    avatar: {
        user: {
            avatar: string;
            type: string;
            is_distributor: boolean;
            profile_type: string;
            friendly_url: string;
            name: string;
        };
    };
    company: ICompany;
}

/**
 * companiesearch Component
 *
 * This component provides an autocomplete search functionality for companies. It fetches company data based on user input and displays relevant options.
 *
 * Props:
 *  - noResultsCallback?: (isNoResults: boolean, id: string, searchValue: string) => any - Callback function triggered when no search results are found.
 *  - clearOnSelect?: boolean - Determines whether the input field should be cleared on selecting an option.
 *  - defaultCompanyId?: string - Default company ID.
 *  - onValueChange?: any - Callback function triggered when the selected value changes.
 *  - defaultSearch?: string - Default search query.
 *  - defaultOptions?: any[] - Default options to be displayed.
 *  - preventCloseOnSelect?: boolean - Prevents the autocomplete dropdown from closing when an option is selected.
 *  - showCheckboxes?: boolean - Determines whether checkboxes should be displayed for single select.
 *  - selectedcompanyIds?: string[] - Array of selected company IDs.
 *  - companyTypes?: company_TYPES - Specific company type to filter by.
 *  - distributors?: "include" | "exclude" | "restrict" | undefined - Distributors filter type.
 *
 * @component
 * @param {IProps} props - Component props
 * @returns {JSX.Element} - Rendered companiesearch component
 */
const companiesearch = ({noResultsCallback, validateOnTheFly, clearOnSelect, onValueChange, ...props}: IProps) => {
    const [companies, setcompanies] = useState(props.defaultOptions || []);
    const [search, setSearch] = useState<string | undefined>(props.defaultSearch || undefined);
    const [loading, setLoading] = useState<boolean>(false);
    const [hideSelect, setHideSelect] = useState<boolean>(false);
    const [isOpen, setOpen] = useState<boolean>(false);
    const inputChanged = useRef(false);
    const {watch, formState} = useFormContext();
    const value = watch(props.id);
    const debouncedSearch = useDebounce(search);
    const options = useMemo(
        () =>
            (companies || []).map((v: any) => {
                const disabled = props?.disabledCompanies?.includes(v.id) || false;
                return {
                    label: v.name,
                    id: v.id,
                    disabled,
                    disabledText: disabled && props?.disabledCompaniesText,
                    avatar: {
                        user: {
                            avatar: v?.profile_images?.CompanyAvatar?.[0]?.src || "",
                            type: v.company_type,
                            is_distributor: v.is_distributor,
                            profile_type: v.company_profile_type,
                            friendly_url: v.friendly_url,
                            name: v.name,
                        },
                    },
                    company: v,
                };
            }),
        [companies, props.showCheckboxes, props.selectedcompanyIds],
    );
    const label = options?.find(o => o.id === value)?.label;
    const notify = useNotification();
    const isInvalid = validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : formState.isSubmitted && formState.errors[props.id]
        ? true
        : false;

    const handleInputChange = (e: any) => {
        if (!e?.target) return;
        setSearch(e?.target?.value || "");
    };

    const handleSearchSuccess = (res: AxiosResponse) => {
        setcompanies(res.data.data);
        // props.noResultsCallback && props.noResultsCallback(!Boolean(res.data.data?.length), props.id);
        if (noResultsCallback !== undefined && search) {
            if (res.data.data?.length === 0) {
                setHideSelect(true);
                setOpen(false);
                noResultsCallback(!!res.data.data, props.id, debouncedSearch || "");
            } else {
                setHideSelect(false);
                setOpen(true);
                noResultsCallback(false, props.id, debouncedSearch || "");
            }
        }
        setLoading(false);
    };

    useEffect(() => {
        if (debouncedSearch || props.defaultCompanyId) {
            if ((debouncedSearch?.length || 0) <= 1) {
                notify("The search word field must be at least 2 characters.", "Error");
                return;
            }
            const options: any = {
                company_types: props.companyTypes,
                must_have_claimers: props.companiesNeedClaimers,
            };

            setLoading(true);
            setOpen(true);
            companyService.search(debouncedSearch || "", options, handleSearchSuccess);
        } else {
            setHideSelect(false);
        }
    }, [debouncedSearch, props.defaultCompanyId]);

    const InputProps = {
        startAdornment: <Search color="secondary" sx={{height: "18px", width: "18px"}} />,
        endAdornment: isInvalid ? (
            <CPTooltip title="Please search for and select a company">
                <ErrorOutlined color="error" />
            </CPTooltip>
        ) : undefined,
        ...props.InputProps,
    };
    const InputLabelProps = {
        shrink: true,
        ...props.InputLabelProps,
    };

    return (
        <AutoCompleteComponent
            {...props}
            options={loading ? [] : options}
            placeholder={props.placeholder || "Search companies"}
            onInputChange={handleInputChange}
            loading={loading}
            inputValue={clearOnSelect ? search : label || inputChanged.current ? label : props.inputValue}
            onValueChange={(id, value1, value2) => {
                onValueChange?.(id, value1, value2);
                if (props.inputValue) inputChanged.current = true;
            }}
            filterOptions={v => v}
            noOptionsText={debouncedSearch ? props?.noOptionsContent || "No Options" : "Start typing to select"}
            onClose={(_, reason) => {
                if (props.preventCloseOnSelect && reason === "selectOption") return;
                setOpen(false);
            }}
            onOpen={() => setOpen(true)}
            onBlur={() => setHideSelect(false)}
            open={hideSelect && !loading ? false : isOpen}
            componentsProps={{
                popper: {
                    open: hideSelect && !loading ? false : isOpen,
                    sx: {
                        "& .MuiAutocomplete-noOptions": {
                            padding: "8px !important",
                            borderRadius: "8px",
                        },
                    },
                },
            }}
            InputProps={InputProps}
            sx={props.sx}
            InputLabelProps={InputLabelProps}
        />
    );
};

companiesearch.defaultProps = {
    renderInput: undefined,
    options: [],
    variant: "default",
};

export default companiesearch;
