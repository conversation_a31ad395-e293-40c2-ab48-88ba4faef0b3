import {MenuProps, SelectProps, SxProps, Theme} from "@mui/material";
import {useFormContext} from "react-hook-form";
import Dropdown from "../../Atoms/Dropdown/Dropdown.component";

type DropdownProps<TValue> = SelectProps<TValue> & {
    options: {value: TValue; label: string}[];
    id: string;
    isRequired?: boolean;
    validationSchema?: any;
    validateOnTheFly?: boolean;
    testid?: string;
    onChangeCallback?: (e: any) => void;
    autoFocusValue?: string;
    MenuProps?: Partial<MenuProps>;
    onOpen?: () => void;
    styles?: {
        formControl?: SxProps<Theme>;
        select?: SxProps<Theme>;
        menuItem?: SxProps<Theme>;
        root?: SxProps<Theme>;
    };
};

export default function ControlledSortOrFilter<TValue>({options, ...props}: DropdownProps<TValue>) {
    const {register, formState, watch} = useFormContext();
    const hasSubmitted = formState.isSubmitted;
    const testid = props.testid
        ? props.testid.replace(/\s/g, "")
        : props.id
        ? `sortOrFilterInput-${props.id}`
        : props.id;
    const isInvalid = props.validateOnTheFly
        ? formState.errors[props.id]
            ? true
            : false
        : hasSubmitted && formState.errors[props.id]
        ? true
        : false;
    const value = watch(props.id);
    const styles = {
        formControl: {
            margin: "8px",
            minWidth: "120px",
            ...(props.styles?.formControl || {}),
        },
        select: {
            fontFamily: "Inter",
            fontStyle: "normal",
            fontWeight: 400,
            fontSize: "14px !important",
            lineHeight: "22px",
            letterSpacing: "0.5px",
            color: "#9A9C9D",
            border: "none",
            height: "22px",
            ".MuiMenu-list": {
                maxHeight: "200px",
            },
            ...(props.styles?.select || {}),
        },
        menuItem: {
            fontFamily: "Inter",
            fontStyle: "normal",
            fontWeight: 400,
            fontSize: "14px !important",
            lineHeight: "22px",
            letterSpacing: "0.5px",
            color: "#9A9C9D",
            ...(props.styles?.menuItem || {}),
        },
        root: props.styles?.root || {},
    };

    return (
        <Dropdown
            options={options}
            error={isInvalid}
            inputProps={{
                "data-testid": testid,
                value: value,
            }}
            autoFocusValue={props.autoFocusValue}
            {...props}
            {...register(props.id, {
                required: props.isRequired ? true : false,
                validate: props.validationSchema,
                shouldUnregister: true,
                onChange: (e: any) => {
                    props.onChangeCallback && props.onChangeCallback(e);
                },
            })}
            value={value}
            styles={styles}
        />
    );
}
