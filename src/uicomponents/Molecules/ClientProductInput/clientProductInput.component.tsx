import {useQuery} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {useState} from "react";
import {useFormContext} from "react-hook-form";
import {useDebounce} from "../../../hooks/useDebounce";
import {mspService} from "../../../services/msp.service";
import AutoCompleteComponent, {IAutoCompleteProps} from "../Autocomplete/Autocomplete.component";
import Box from "../../Atoms/Box/Box.component";
import ProductInputTag from "../ProductSearch/productInputTag.component";

interface IProps extends Omit<IAutoCompleteProps<any, any, any, any>, "options"> {
    companyId: string;
    validateOption?: (value: string) => boolean;
    defaultInputValue?: string;
    handleChange?: (
        value: IClientProduct | IClientProduct[] | null,
        newOptions: Array<{id: string; label: string}>,
    ) => void;
    stateValue?: IClientProduct[] | IClientProduct;
    showCreateOption?: boolean;
    showTagForSingleSelect?: boolean;
    defaultSearchValue?: string;
    useIdForOptions?: boolean;
    defaultOptions?: any[];
}

type ISearchVendors = IClientProduct[];
export interface IClientProduct {
    id: string;
    name: string;
    created_at: string;
    updated_at: string;
    label?: string;
}

export default function ClientProductInput({companyId, ...props}: IProps) {
    const {setValue: setCtxValue, getValues, watch} = useFormContext();
    const [inputValue, setInputValue] = useState<string>(
        props.defaultSearchValue ? props.defaultSearchValue : props.multiple ? "" : getValues()[props.id] || "",
    );
    const [value, setValue] = useState<IClientProduct | IClientProduct[] | null>(
        props.multiple ? props.stateValue || [] : props.stateValue || null,
    );
    const {validateOption: _, defaultInputValue: __, useIdForOptions, ...autoCompleteProps} = props;
    const searchValue = useDebounce(inputValue, 350);

    const searchVendorsQuery = useQuery<ISearchVendors>(
        ["client-products", searchValue, companyId],
        () => {
            return new Promise(resolve => {
                mspService.searchClientProducts(companyId, searchValue, (res: AxiosResponse<ISearchVendors>) => {
                    resolve(res.data);
                });
            });
        },
        {
            enabled:
                searchValue?.length > 2 &&
                !(searchValue === props?.defaultOptions?.[0]?.name || searchValue === props?.defaultOptions?.[0]?.id),
        },
    );
    const foundVendors = Array.from(
        new Map(
            [...(searchVendorsQuery.data || []), ...(props.defaultOptions || [])].map(v => [
                useIdForOptions ? v.id : v.name,
                {id: useIdForOptions ? v.id : v.name, label: v.name},
            ]),
        ).values(),
    );
    const client_product = props.multiple ? undefined : foundVendors?.find(o => o.id === (value as IClientProduct)?.id);

    return (
        <AutoCompleteComponent
            inputValue={inputValue}
            onInputChange={(_event, newInputValue) => {
                setInputValue(newInputValue);
                !props.multiple && !useIdForOptions && setCtxValue(props.id, newInputValue);
            }}
            onChange={(_event: any, newValue: IClientProduct | null) => {
                setValue(newValue);
                setInputValue(newValue?.name ?? "");
                props.multiple
                    ? setCtxValue(props.id, newValue)
                    : setCtxValue(props.id, useIdForOptions ? newValue?.id : newValue?.name);
                props.handleChange && props.handleChange(newValue, foundVendors || []);
            }}
            {...(useIdForOptions
                ? {}
                : {value: props.multiple ? watch(props.id) : useIdForOptions ? watch(props.id) : value})}
            autoComplete
            includeInputInList
            filterSelectedOptions
            freeSolo
            noOptionsText={!!inputValue ? `Add "${inputValue}" as a new product` : "Type to select..."}
            helperText={"Product name is required"}
            {...autoCompleteProps}
            renderInput={autoCompleteProps.renderInput}
            placeholder={props.showTagForSingleSelect && client_product ? "" : props.placeholder}
            options={foundVendors}
            disableClearable={!!props.showTagForSingleSelect}
            getOptionLabel={(option: any) => {
                if (typeof option === "string") {
                    return option;
                }
                if (option && typeof option.label === "string") {
                    return option.label;
                }
                return "";
            }}
            filterOptions={
                props.showCreateOption
                    ? (options, params) => {
                          const filtered = options;
                          if (filtered.length === 0 && params.inputValue !== "" && props.showCreateOption) {
                              filtered.push({id: "noOptions", label: "No Results Found"});
                          }
                          if (params.inputValue !== "" && props.showCreateOption) {
                              filtered.push({
                                  id: params.inputValue,
                                  label: `${inputValue}`,
                                  isNew: true,
                                  inputValue: params.inputValue,
                                  isCreatableOption: true,
                              });
                          }
                          return filtered;
                      }
                    : x => x
            }
            loadingText={!!searchValue ? "Loading..." : "Search for a product"}
            loading={!!inputValue && searchVendorsQuery.isLoading}
            isOptionEqualToValue={(option, value) => {
                return option.label === value || option.id === value;
            }}
            InputProps={{
                startAdornment: props.showTagForSingleSelect ? (
                    client_product ? (
                        <Box sx={{paddingTop: "10px"}}>
                            <ProductInputTag
                                product={{...client_product, name: client_product?.label}}
                                hideAvatar
                                onDelete={
                                    props.disabled || props.readOnly
                                        ? undefined
                                        : (_, __) => {
                                              setValue(null);
                                          }
                                }
                                avatarSize={32}
                            />
                        </Box>
                    ) : null
                ) : null,
            }}
            popupIcon={autoCompleteProps.popupIcon}
        />
    );
}
