import {forwardRef} from "react";
import {Box, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";
import WarningOutlinedIcon from "@mui/icons-material/WarningOutlined";
import {TSelectedFile} from "../../../Interfaces/fileUpload.interface";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";
import formatString, {CAPITALIZE} from "../../../utils/formatString.util";

interface IFileItemStatusBadgeProps {
    file: TSelectedFile;
    hideStatusLabel?: boolean;
    hideErrorLabel?: boolean;
}

const FileItemStatusBadge = forwardRef<HTMLDivElement, IFileItemStatusBadgeProps>(
    ({file, hideStatusLabel = false, hideErrorLabel = false}, ref) => {
        const theme = useTheme();
        const {isDirectory, withErrors, status, progress} = file;
        const statusLabel = formatString(status, CAPITALIZE);
        const hasError = status === SelectedFileStatus.ERROR || (!!isDirectory && !!withErrors?.length);
        const statusText = [SelectedFileStatus.UPLOADING, SelectedFileStatus.PROCESSING].includes(status)
            ? `${isNaN(progress ?? 0) ? 0 : progress}%`
            : !!isDirectory && !!withErrors?.length
            ? withErrors?.length
            : !isDirectory && hasError
            ? "Error"
            : "";
        const width = [SelectedFileStatus.UPLOADING, SelectedFileStatus.PROCESSING].includes(status)
            ? "54px"
            : undefined;

        const sx = hasError
            ? {
                  backgroundColor: theme.palette.error[100],
                  color: theme.palette.error[600],
                  borderColor: theme.palette.error[300],
              }
            : {
                  backgroundColor: theme.palette.blue[100],
                  color: theme.palette.blue[600],
                  borderColor: theme.palette.blue[300],
              };
        if (statusText === "" || !!hideStatusLabel) return null;
        return (
            <Box
                ref={ref}
                sx={{
                    display: "flex",
                    alignItems: "center",
                    padding: "2px 6px",
                    gap: "4px",
                    borderWidth: "1px",
                    borderStyle: "solid",
                    borderRadius: "17px",
                    "& svg": {
                        width: "12px",
                        height: "12px",
                    },
                    height: "fit-content",
                    justifyContent: "center",
                    minWidth: width,
                    ...sx,
                }}>
                {hasError && <WarningOutlinedIcon />}
                {(!hasError || !hideErrorLabel) && (
                    <Typography
                        variant="body4"
                        fontInter
                        sx={{
                            color: "inherit",
                            fontWeight: 700,
                            lineHeight: "120%",
                        }}
                        title={statusLabel + `${hideErrorLabel ? "YES" : "NO"}`}>
                        {statusText}
                    </Typography>
                )}
            </Box>
        );
    },
);

export default FileItemStatusBadge;
