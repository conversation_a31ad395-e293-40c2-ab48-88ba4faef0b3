import {SxProps, Theme, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";
import {TSelectedFile} from "../../../Interfaces/fileUpload.interface";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";
import useServiceWorker from "../../../hooks/useServiceWorker";
import {useCallback} from "react";

interface IFileItemStatusProps {
    file: TSelectedFile;
    handleCloseBulkUpload?: () => void;
}

export default function FileItemStatus({file, handleCloseBulkUpload}: IFileItemStatusProps) {
    const theme = useTheme();
    const {postAction} = useServiceWorker();

    const triggerAction = (actionId: string, file: TSelectedFile) => {
        postAction(actionId, file);
        if (file.customProperties?.cache_token) {
            handleCloseBulkUpload?.();
        }
    };

    const renderFileItemStatus = useCallback(
        (label: string, title: string, sx: SxProps<Theme>, onClick?: () => void): React.ReactNode => (
            <Typography
                fontInter
                sx={{
                    width: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "16px",
                    padding: 1,
                    ...sx,
                }}
                title={title}
                onClick={onClick}>
                {label}
            </Typography>
        ),
        [file],
    );

    // Error uploading file
    if (file.status === SelectedFileStatus.ERROR) {
        return renderFileItemStatus(file.statusMessage, file.statusMessage, {
            color: theme.palette.error[600],
            fontWeight: 600,
            fontSize: "14px",
            lineHeight: "110%",
            cursor: "default",
        });
    }

    if (file.status === SelectedFileStatus.UPLOADED && !file.isDirectory) {
        // File Uploaded or File Incomplete with action
        const color =
            {
                [SelectedFileStatus.INCOMPLETE]: theme.palette.warning[600],
                [SelectedFileStatus.ERROR]: theme.palette.error[600],
            }[file.customProperties?.action_status ?? ""] ?? theme.palette.blue[600];
        // Checking if file has an associated action
        if (!!file.customProperties?.action_label) {
            const actionId = file.customProperties?.action_id ?? null;
            return renderFileItemStatus(
                file.customProperties?.action_label,
                file.customProperties?.action_title ?? file.status,
                {
                    color,
                    fontWeight: 700,
                    lineHeight: "130%",
                    cursor: !actionId ? "default" : "pointer",
                },
                !!actionId
                    ? () => {
                          triggerAction(actionId, file);
                      }
                    : undefined,
            );
        } else {
            // Displaying file status
            return renderFileItemStatus(file.status, file.status, {
                color,
                fontWeight: 700,
                lineHeight: "130%",
                cursor: "default",
            });
        }
    }

    if (!file.isDirectory) {
        // Any status but Error
        const label = `${file.statusMessage} ${file.status === SelectedFileStatus.UPLOADING ? "..." : ""}`;
        return renderFileItemStatus(label, file.status, {
            color: theme.palette.neutral[500],
            fontWeight: 600,
            lineHeight: "16px",
        });
    }
    return <></>;
}
