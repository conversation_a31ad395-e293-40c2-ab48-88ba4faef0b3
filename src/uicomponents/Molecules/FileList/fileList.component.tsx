import Box from "../../Atoms/Box/Box.component";
import FileItemComponent from "./fileItem.component";
import {SxProps, Theme, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";
import {generateSxFromProps} from "../../../utils/sx.util";
import {TSelectedFile, TSelectedFileList} from "../../../Interfaces/fileUpload.interface";
import {useCallback} from "react";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";

export interface IFileItemProps {
    itemSx?: SxProps<Theme>;
    showFileStatus?: boolean;
    hideErrorLabel?: boolean;
    expandable?: boolean;
    maxTextSize?: number;
    // Try again button
    tryAgainOnError?: boolean;
    onTryAgain?: (id: string) => void;
    // Delete button (trash-can)
    showDeleteButton?: boolean;
    onDelete?: (id: string) => void;
    // Cancel Upload (x-mark)
    showCancelButton?: boolean;
    onCancel?: (id: string) => void;
    // Edit file name (pen button)
    showEditButton?: boolean;
    onEdit?: (id: string, name: string) => void;

    customControls?: (file: TSelectedFile) => React.ReactNode;
}
interface IProps extends IFileItemProps {
    files: TSelectedFileList;
    setFiles: (files: TSelectedFileList) => void;
    sx?: SxProps<Theme>;
    handleCloseBulkUpload?: () => void;
}

/**
 * FileListComponent renders a TSelectedFileList object as react components.
 *
 * @param {IProps} props - The properties for the component.
 * @param {TSelectedFileList} itemProps.files - The list of files to display.
 * @param {SxProps<Theme>} [sx] - Custom styling to be applied to the component.
 * @param {SxProps<Theme>} [itemSx] - Custom styling to be applied to individual file items.
 * @param {boolean} [showFileStatus] - Flag to show the status of the file.
 * @param {boolean} [expandable] - Flag to make the folder expandable. When it's true, the user can see the files inside a folder.
 * @param {boolean} [tryAgainOnError] - Flag to show a "Try Again" button on error.
 * @param {(id: string) => void} [onTryAgain] - Callback function for the "Try Again" button.
 * @param {boolean} [showDeleteButton] - Flag to show a "Delete" button.
 * @param {(id: string) => void} [onDelete] - Callback function for the "Delete" button.
 * @param {boolean} [showCancelButton] - Flag to show a "Cancel" button.
 * @param {(id: string) => void} [onCancel] - Callback function for the "Cancel" button.
 * @param {boolean} [showEditButton] - Flag to show an "Edit" button to change a file name.
 * @param {(id: string, name: string) => void} [onEdit] - Callback function for the "Edit Name" action.
 * @param {(file: TSelectedFile) => React.ReactNode} [customControls] - Custom controls to be rendered for each file item.
 * @returns {JSX.Element} The rendered component.
 */
export default function FileListComponent({files, setFiles, sx, handleCloseBulkUpload, ...itemProps}: IProps) {
    const theme = useTheme();

    const handleUpdateFileInList = useCallback(
        (fileId: string, updateObj: Partial<TSelectedFile>) => {
            const newFiles = files.map(file => (file.id === fileId ? {...file, ...updateObj} : file));
            setFiles(newFiles);
        },
        [files],
    );

    const sortFileList = (files: TSelectedFileList): TSelectedFileList => {
        const sortedList = files
            .sort((a, b) => {
                if (
                    [SelectedFileStatus.ERROR, SelectedFileStatus.UPLOADING, SelectedFileStatus.PROCESSING].includes(
                        a.status,
                    ) &&
                    b.status !== SelectedFileStatus.ERROR
                ) {
                    return -1;
                } else if (a.status === SelectedFileStatus.WAITING && b.status === SelectedFileStatus.UPLOADED) {
                    return -1;
                } else if (a.status === b.status) {
                    return 1;
                } else {
                    return 0;
                }
            })
            .map(file => {
                if (file.isDirectory && !!file.content) {
                    file.content = sortFileList(file.content);
                }
                return file;
            });
        return sortedList;
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                maxHeight: "300px",
                overflowY: "auto",
                ...generateSxFromProps(sx, theme),
            }}>
            {files.length > 0 ? (
                sortFileList(files).map(file => (
                    <FileItemComponent
                        key={file.id}
                        setFile={updateObj => {
                            handleUpdateFileInList(file.id, updateObj);
                        }}
                        file={file}
                        setFiles={setFiles}
                        handleCloseBulkUpload={handleCloseBulkUpload}
                        {...itemProps}
                    />
                ))
            ) : (
                <Typography
                    variant="body2"
                    fontInter
                    sx={{
                        display: "-webkit-box",
                        flex: 1,
                        WebkitBoxOrient: "vertical",
                        WebkitLineClamp: 1,
                        width: "100%",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        maxHeight: "6em",
                        color: theme.palette.neutral[700],
                        fontWeight: 600,
                        lineHeight: "120%",
                    }}>
                    No files found
                </Typography>
            )}
        </Box>
    );
}
