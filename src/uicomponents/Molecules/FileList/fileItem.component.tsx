import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import {Box, useTheme} from "@mui/material";
import ModeEditOutlineOutlinedIcon from "@mui/icons-material/ModeEditOutlineOutlined";
import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import HighlightOffOutlinedIcon from "@mui/icons-material/HighlightOffOutlined";
import {FormProvider, useForm} from "react-hook-form";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import {generateSxFromProps} from "../../../utils/sx.util";
import FileIconComponent from "./fileIcon.component";
import {useMemo, useRef, useState} from "react";
import {TSelectedFile, TSelectedFileList} from "../../../Interfaces/fileUpload.interface";
import FileListComponent, {IFileItemProps} from "./fileList.component";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";
import FileItemStatus from "./fileItemStatus.component";
import FileItemStatusBadge from "./fileItemStatusBadge.component";
import ChevronRightOutlinedIcon from "@mui/icons-material/ChevronRightOutlined";
import LaunchOutlinedIcon from "@mui/icons-material/LaunchOutlined";
import CustomLink from "../../../components/Links/CustomLink.component";
import FileItemStatusIcon from "./fileItemStatusIcon.component";
import Ellipsis from "./ellipsis.component";
import FileItemDeleteButton from "./fileItemDeleteButton.component";
import FileItemCancelButton from "./fileItemCancelButton.component";
import FileItemRetryButton from "./fileItemRetryButton.component";

interface IProps extends IFileItemProps {
    file: TSelectedFile;
    setFile: (file: Partial<TSelectedFile>) => void;
    setFiles: (files: TSelectedFileList) => void;
    handleCloseBulkUpload?: () => void;
}
export default function FileItemComponent({
    file,
    itemSx,
    expandable,
    setFiles,
    handleCloseBulkUpload,
    ...props
}: IProps) {
    const theme = useTheme();
    const [isChangingName, setIsChangingName] = useState<boolean>(false);
    const [isExpanded, setIsExpanded] = useState<boolean>(false);
    const hasSubitems = file.isDirectory && !!(file.content ?? []).length;
    const formMethods = useForm<{fileName: string}>({
        defaultValues: {
            fileName: file.fileName,
        },
    });
    const fileStatusBadgeRef = useRef<HTMLDivElement>(null);

    const onStartChangingFileName = () => {
        formMethods.reset({
            fileName: file.fileName.replace(/\.[^.]+$/, ""),
        });
        setIsChangingName(true);
    };
    const onCancelChangingFileName = () => setIsChangingName(false);

    const onSaveFileName = data => {
        !!props.onEdit && props.onEdit(file.id, data.fileName);
        setIsChangingName(false);
    };

    const goToContentLink = useMemo(() => {
        if (!file.contentUrl || !file.contentUrl.relative_path) return <></>;
        const icon = (
            <LaunchOutlinedIcon
                sx={{
                    color: theme.palette.neutral[500],
                    "& svg": {
                        width: "24px",
                        height: "24px",
                    },
                }}
            />
        );
        return (
            <CustomLink
                subdomain={file.contentUrl.subdomain}
                to={file.contentUrl.relative_path}
                id={`open-content-${file.id}`}>
                {icon}
            </CustomLink>
        );
    }, [file.contentUrl]);

    return (
        <Box>
            <BorderedBox
                key={file.id}
                sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 2,
                    borderRadius: "8px",
                    padding: 1,
                    ...generateSxFromProps(itemSx, theme),
                    ...(file.status === SelectedFileStatus.ERROR ? {borderColor: theme.palette.error[300]} : {}),
                }}>
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 1,
                        ...(hasSubitems && !!expandable ? {cursor: "pointer"} : {}),
                    }}
                    onClick={hasSubitems && !!expandable ? () => setIsExpanded(prev => !prev) : undefined}>
                    {hasSubitems && !!expandable && (
                        <ChevronRightOutlinedIcon
                            sx={{
                                transform: `rotate(${isExpanded ? "90" : 0}deg)`,
                            }}
                        />
                    )}
                    <FileIconComponent fileType={file.fileType} isExpanded={isExpanded} />
                </Box>
                {isChangingName ? (
                    <FormProvider {...formMethods}>
                        <form onSubmit={formMethods.handleSubmit(onSaveFileName)} className="col-12 flex-shrink-1">
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    width: "100%",
                                    flex: 1,
                                    gap: "10px",
                                }}>
                                <Box
                                    sx={{
                                        flex: 1,
                                    }}>
                                    <TextBoxComponent
                                        containerClassName="col-12"
                                        id="fileName"
                                        isRequired
                                        label="File Name"
                                        autoFocus
                                        value={file.fileName}
                                        maxLength={props.maxTextSize ?? 50}
                                        placeholder="Enter the new file name"
                                    />
                                </Box>
                                <IconButton
                                    sx={{
                                        "& svg": {
                                            width: "32px",
                                            height: "32px",
                                        },
                                    }}
                                    onClick={onCancelChangingFileName}>
                                    <HighlightOffOutlinedIcon />
                                </IconButton>
                                <MuiButtonComponent
                                    className="cpBlueBtnThin"
                                    id="viewDetails"
                                    size="sm"
                                    color="blue"
                                    variant="outlined"
                                    type="submit">
                                    SAVE
                                </MuiButtonComponent>
                            </Box>
                        </form>
                    </FormProvider>
                ) : (
                    <>
                        <Box
                            sx={{
                                display: "flex",
                                flex: 1,
                                flexDirection: "column",
                                gap: "4px",
                                overflow: "hidden",
                            }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flex: 1,
                                    gap: "10px",
                                    overflow: "hidden",
                                    maxWidth: "100%",
                                }}>
                                <Box
                                    sx={{
                                        flex: "0 1 auto",
                                        maxWidth: `calc(100% - 10px - ${
                                            fileStatusBadgeRef.current?.getBoundingClientRect().width ?? 0
                                        }px)`,
                                    }}>
                                    <Ellipsis text={file.fileName} preserve={file.fileName.split('.').pop().length + 1}/>
                                </Box>
                                {!!props.showFileStatus && (
                                    <FileItemStatusBadge
                                        file={file}
                                        ref={fileStatusBadgeRef}
                                        hideErrorLabel={!!props?.hideErrorLabel}
                                    />
                                )}
                            </Box>
                            {!!props.showFileStatus && (
                                <FileItemStatus file={file} handleCloseBulkUpload={handleCloseBulkUpload} />
                            )}
                        </Box>
                        {file.status === SelectedFileStatus.ERROR && !!file.retryOnError && (
                            <FileItemRetryButton file={file} onRetry={props.onTryAgain ?? (() => {})} />
                        )}
                        {!!props.customControls && !!props.customControls && props.customControls(file)}
                        {!!file.contentUrl && goToContentLink}
                        {!!props.showEditButton && (
                            <IconButton
                                sx={{
                                    color: theme.palette.neutral[500],
                                    "& svg": {
                                        width: "32px",
                                        height: "32px",
                                    },
                                }}
                                onClick={onStartChangingFileName}>
                                <ModeEditOutlineOutlinedIcon />
                            </IconButton>
                        )}
                        {!!props.showDeleteButton && props.onDelete && (
                            <FileItemDeleteButton file={file} onDelete={props.onDelete} />
                        )}
                        {!!props.showCancelButton && (
                            <FileItemCancelButton
                                file={file}
                                onCancel={
                                    [SelectedFileStatus.WAITING, SelectedFileStatus.VALID].includes(file.status) ||
                                    (file.status === SelectedFileStatus.ERROR && !!file.retryOnError)
                                        ? props.onCancel ?? (() => {})
                                        : props.onDelete ?? (() => {})
                                }
                            />
                        )}
                        {!!props.showFileStatus && <FileItemStatusIcon file={file} />}
                    </>
                )}
            </BorderedBox>
            {!!expandable && !!file.isDirectory && !!(file.content ?? []).length && !!isExpanded && (
                <FileListComponent
                    files={file.content ?? []}
                    sx={{
                        maxHeight: "unset",
                        overflowY: "none",
                        padding: "8px 0 8px 16px",
                        backgroundColor: `${theme.palette.neutral[200]}`,
                    }}
                    setFiles={setFiles}
                    handleCloseBulkUpload={handleCloseBulkUpload}
                    {...props}
                />
            )}
        </Box>
    );
}
