import {IconButton, useTheme} from "@mui/material";
import {TSelectedFile} from "../../../Interfaces/fileUpload.interface";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";

interface IFileItemCancelButtonProps {
    file: TSelectedFile;
    onCancel: (id: string) => void;
}
export default function FileItemCancelButton({file, onCancel}: IFileItemCancelButtonProps) {
    const theme = useTheme();

    let color = theme.palette.blue[500];
    let title = "Remove this file from the list";
    if (
        [SelectedFileStatus.WAITING, SelectedFileStatus.VALID, SelectedFileStatus.UPLOADING].includes(file.status) ||
        (file.status === SelectedFileStatus.ERROR && !!file.retryOnError)
    ) {
        color = theme.palette.neutral[500];
        title = "Cancel upload";
    }
    if (file.status === SelectedFileStatus.PROCESSING) return null;
    return (
        <IconButton
            sx={{
                color,
                "& svg": {
                    width: "24px",
                    height: "24px",
                },
            }}
            title={title}
            onClick={() => !!onCancel && onCancel(file.id)}>
            <CancelOutlinedIcon />
        </IconButton>
    );
}
