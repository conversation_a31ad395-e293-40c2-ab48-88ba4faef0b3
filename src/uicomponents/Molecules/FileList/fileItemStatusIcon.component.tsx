import {Box, useTheme} from "@mui/material";
import {TSelectedFile} from "../../../Interfaces/fileUpload.interface";
import {SelectedFileStatus} from "../../../enums/fileUpload.enum";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import TimerOutlinedIcon from "@mui/icons-material/TimerOutlined";
import ErrorOutlineOutlinedIcon from "@mui/icons-material/ErrorOutlineOutlined";

interface IFileItemStatusProps {
    file: TSelectedFile;
}

export default function FileItemStatusIcon({file}: IFileItemStatusProps) {
    const theme = useTheme();
    if (
        (!file.isDirectory && file.status !== SelectedFileStatus.UPLOADED) ||
        (!!file.isDirectory && ![SelectedFileStatus.UPLOADED, SelectedFileStatus.ERROR].includes(file.status))
    )
        return <></>;

    const actionStatus = file.customProperties?.action_status ?? SelectedFileStatus.UPLOADED;
    const config =
        {
            processing: {color: theme.palette.blue[500], icon: <TimerOutlinedIcon />},
            uploaded: {
                color: theme.palette.success[500],
                icon: <CheckCircleOutlineOutlinedIcon />,
            },
            error: {color: theme.palette.error[600], icon: <ErrorOutlineOutlinedIcon />},
            incomplete: {color: theme.palette.warning[500], icon: <ErrorOutlineOutlinedIcon />},
        }[actionStatus] ?? undefined;

    if (!config) return <></>;
    return (
        <Box
            sx={{
                color: config.color,
                "& svg": {
                    width: "24px",
                    height: "24px",
                },
            }}>
            {config.icon}
        </Box>
    );
    return <></>;
}
