import {useEffect, useState} from "react";
import Box from "../../../Atoms/Box/Box.component";
import PageInnerHeader from "../../../Organism/PageInnerHeader/pageInnerHeader";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useMspContracts} from "../../../../hooks/fetches/useMspContracts";
import YourSubscriptionsListViewTable from "../../../Organism/MSPContracts/YourSubscriptionsListView/yourSubscriptionsListViewTable.component";
import {useDebounce} from "../../../../hooks/useDebounce";

const DashboardExpensesBreakdownDetailContractsTab = ({categoryId, isSubcategory}) => {
    const [search, setSearch] = useState("");
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {firstLoad, subscriptionListView, listViewFilterState} = useMspContracts(activeCompany?.id, {
        calls: {
            all: false,
            firstLoad: true,
            subscriptionListView: true,
        },
        defaultListViewFilters: {
            search_word: "",
            dynamic: {
                ...(isSubcategory && {sub_categories: [categoryId]}),
                ...(!isSubcategory && {categories: [categoryId]}),
            },
        },
    });
    const [filters, setFilters] = listViewFilterState;
    const debouncedSearch = useDebounce(search, 500);
    const contracts =
        subscriptionListView.data?.reduce((acc, cur) => {
            return [...acc, ...cur.contracts];
        }, []) || [];

    const setDynamicFilters = (newFilters: any) => {
        const newFiltersWithoutDynamic = {...newFilters};
        delete newFiltersWithoutDynamic.dynamic;
        delete newFiltersWithoutDynamic.search_word;
        setFilters({
            ...newFilters,
            dynamic: {
                ...(newFilters?.dynamic || {}),
                ...newFiltersWithoutDynamic,
            },
        });
    };

    useEffect(() => {
        setFilters({...filters, search_word: debouncedSearch});
    }, [debouncedSearch]);

    return (
        <Box display="flex" flexDirection="column" gap={1}>
            <PageInnerHeader
                id="dashboard-expenses-breakdown-detail-contracts-tab-header"
                searchState={[search, setSearch]}
                filterState={[filters, setDynamicFilters as (filters: {} | undefined) => void]}
                filters={firstLoad.data?.filters}
                fullWidthSearch
                hideFilterKeys={["categories"]}
            />
            <YourSubscriptionsListViewTable
                companyId={activeCompany?.id}
                data={contracts}
                isLoading={subscriptionListView.isLoading}
                isClientMsp={isClientMsp}
                readOnly={false}
                hideTotals
                includeSubcategoryColumn={!isSubcategory}
            />
        </Box>
    );
};

export default DashboardExpensesBreakdownDetailContractsTab;
