import {atom} from "jotai";
import type {
    IExpenseBreakdownItem,
    IExpenseSubscription,
    IPlaidAlertNotification,
    ITransactionItem,
} from "../../../Interfaces/plaid.interface";
import {type IBaseModal, INITIAL_MODAL_DATA} from "../base.constant";

interface IExpenseBreakdownDetail {
    type: "category" | "plaid_category" | "stack-categories";
    start_date?: string;
    end_date?: string;
    item?: IExpenseBreakdownItem;
}

interface IDashboardExpenseBreakdownDetail {
    start_date?: string;
    end_date?: string;
    item?: IExpenseBreakdownItem;
}

interface IExpenseDetailDrawerData {
    expense?: ITransactionItem | IExpenseSubscription;
    type: "transaction" | "subscription";
}

type TExpensesBreakdownDetailModalData = IBaseModal<IExpenseBreakdownDetail>;
type TExpenseDetailDrawerData = IBaseModal<IExpenseDetailDrawerData>;
type TAlertDetailsDrawerData = IBaseModal<IPlaidAlertNotification>;
type TDashboardExpensesBreakdownDetailModalData = IBaseModal<IDashboardExpenseBreakdownDetail>;

export const ExpensesBreakdownDetailAtom = atom<TExpensesBreakdownDetailModalData>(INITIAL_MODAL_DATA);
export const AlertSettingsDrawerOpenAtom = atom<IBaseModal>(INITIAL_MODAL_DATA);
export const ExpenseDetailDrawerAtom = atom<TExpenseDetailDrawerData>(INITIAL_MODAL_DATA);
export const AlertSettingsDrawerDataAtom = atom<TAlertDetailsDrawerData>(INITIAL_MODAL_DATA);
export const DashboardExpensesBreakdownDetailAtom =
    atom<TDashboardExpensesBreakdownDetailModalData>(INITIAL_MODAL_DATA);
export const isInitialSyncInProgressAtom = atom<boolean>(false);
