/**
 * Modal component used to allow Distributors to import their partner Vendors using a .csv file.
 *
 * @param {boolean} open - Flag indicating whether the dialog is open.
 * @param {function} onClose - Callback function triggered when the dialog is closed.
 * @param {object} company - Object containing information about the logged in company. (Optional)
 * @param {string} title - Title of the dialog. (Optional)
 * @returns {JSX.Element} Import partners from CSV dialog component.
 */

import AddIcon from "@mui/icons-material/Add";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError} from "axios";
import {useEffect, useMemo, useRef, useState} from "react";
import {uniqueId} from "react-bootstrap-typeahead/types/utils";
import {FormProvider, useForm} from "react-hook-form";
import type {ICompany} from "../../../Interfaces/company.interface";
import useNotification from "../../../hooks/useNotification";
import {MarketplacePageService} from "../../../services/marketplacePage.service";
import {getErrorFromArray} from "../../../utils/error.util";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import {ImportPartnersFromCSVContext} from "./importPartnersFromCSV.context";
import ImportPartnersFromCSVStepFour from "./stepFour.component";
import ImportPartnersFromCSVStepOne from "./stepOne.component";
import ImportPartnersFromCSVStepThree from "./stepThree.component";
import ImportPartnersFromCSVStepTwo from "./stepTwo.component";
import {INVALID_FILE, NO_FILE_SELECTED} from "../../../constants/errorMessages.constant";

interface IProps {
    open: boolean;
    onClose: () => void;
    onVendorsImported: () => void;
    company?: Pick<ICompany, "id" | "friendly_url">;
    title?: string;
}
interface IVendorsList {
    imported: any[];
    notFound: any[];
    count: number;
}
const ImportPartnersFromCSVDialog = (props: IProps) => {
    const [saving, setSaving] = useState<boolean>(false);
    const methods = useForm();
    const theme = useTheme();
    const notify = useNotification();
    const [file, setFile] = useState<File>();
    const [step, setStep] = useState<1 | 2 | 3 | 4>(1);
    const [vendors, setVendors] = useState<IVendorsList>({imported: [], notFound: [], count: 0});
    const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
    const csvCacheToken = useRef<string>("");
    const statusTimeout = useRef<NodeJS.Timeout | undefined>();

    useEffect(() => {
        if (!file) return;
        setErrorMessage(undefined);
    }, [file]);

    /**
     * Step 1 methods - Send the CSV file to be parsed
     */
    const onSendCSVFileSuccess = ({data}: any) => {
        setFile(undefined);
        if (!data?.token) {
            setSaving(false);
            setStep(1);
            setErrorMessage(INVALID_FILE);
            return;
        }
        csvCacheToken.current = data.token;
        checkCsvProcessingStatus(data.token);
    };

    const onSendCSVFileError = () => {
        setSaving(false);
        setFile(undefined);
        setStep(1);
        setErrorMessage(INVALID_FILE);
    };

    const sendCSVFile = () => {
        if (!props.company?.id) {
            setSaving(false);
            notify("CSV Import for company is not available.", "Error");
            return;
        }
        if (!file) {
            setErrorMessage(NO_FILE_SELECTED);
            setStep(1);
            return;
        }
        setStep(2);
        setSaving(true);
        return MarketplacePageService.importPartnersByCsv(
            props.company?.friendly_url,
            file,
            () => {},
            onSendCSVFileSuccess,
            onSendCSVFileError,
        );
    };

    /**
     * Step 2 methods - Check CSV processing status
     */
    const onCsvProcessingStatusSuccess = ({data}: any) => {
        switch (data?.status) {
            case "processing":
                statusTimeout.current = setTimeout(() => {
                    checkCsvProcessingStatus(csvCacheToken.current);
                }, 5000);
                break;
            case "complete":
                clearTimeout(statusTimeout.current);
                setVendors(() => {
                    const response = {
                        imported: data.vendors.filter(vendor => !!vendor.vendor),
                        notFound: data.vendors
                            .filter(vendor => vendor.vendor === null)
                            .map(vendor => ({
                                id: uniqueId(),
                                ...vendor,
                            })),
                        count: data.vendors.length,
                    };
                    if (response.imported.length > 0) {
                        props?.onVendorsImported();
                    }
                    return response;
                });
                setSaving(false);
                setStep(3);
                break;
            default:
                clearTimeout(statusTimeout.current);
                setSaving(false);
                setFile(undefined);
                setStep(1);
                setErrorMessage(INVALID_FILE);
                break;
        }
    };

    const onCsvProcessingStatusError = () => {
        setSaving(false);
        setFile(undefined);
        setStep(1);
        setErrorMessage(INVALID_FILE);
    };

    const checkCsvProcessingStatus = (token: string | null) => {
        if (!token || !props.company?.friendly_url) return;
        MarketplacePageService.checkCsvProcessingStatus(
            props.company?.friendly_url,
            token,
            () => {},
            onCsvProcessingStatusSuccess,
            onCsvProcessingStatusError,
        );
    };

    /**
     * Step 3 methods - Save matched Vendors or Send requests to new ones
     */
    const onSaveMatchedVendorsSuccess = ({data}: any) => {
        setSaving(false);
        setStep(4);
        clearInputFile();
        setVendors(prev => {
            const response = {
                imported: [...prev.imported, ...data.filter(v => !!v?.vendor)],
                notFound: data.filter(v => !v?.vendor),
                count: data.length,
            };
            if (response.imported.length > 0) {
                props?.onVendorsImported();
            }
            return response;
        });
    };

    const onSaveMatchedVendorsError = (err: AxiosError) => {
        setSaving(false);
        notify(getErrorFromArray(err), "Error");
    };

    const saveMatchedVendors = () => {
        if (!props.company?.id) {
            setSaving(false);
            notify("CSV Import for company is not available.", "Error");
            return;
        }
        const companies = vendors.notFound.map(v => ({
            name: v.name,
            description: v.description,
            company_id: v?.vendor?.id,
        }));

        setSaving(true);
        return MarketplacePageService.importPartners(
            props.company?.friendly_url,
            companies,
            () => {},
            onSaveMatchedVendorsSuccess,
            onSaveMatchedVendorsError,
        );
    };

    const updateVendor = (vendor_id: string, vendor: any) => {
        setVendors(prev => ({
            ...prev,
            notFound: prev.notFound.map(v =>
                v.id !== vendor_id
                    ? v
                    : {
                          ...v,
                          vendor,
                      },
            ),
        }));
        methods.reset();
    };

    const deleteVendor = (vendor_id: string) => {
        setVendors(prev => ({
            ...prev,
            notFound: prev.notFound.filter(v => v.id !== vendor_id),
        }));
    };

    const removeMatch = (vendor_id: string) => {
        setVendors(prev => ({
            ...prev,
            notFound: prev.notFound.map(v => (v.id !== vendor_id ? v : {...v, vendor: null})),
        }));
    };

    const handleSubmit = async () => {
        switch (step) {
            case 1:
                return sendCSVFile();
            case 3:
                if (vendors.notFound.length === 0) {
                    props.onClose();
                    break;
                }
                return saveMatchedVendors();
            case 4:
                props.onClose();
                break;
            default:
                break;
        }
    };

    const clearInputFile = () => {
        setFile(undefined);
    };

    useEffect(() => {
        if (props.open) setStep(1);
        setSaving(false);
        setErrorMessage(undefined);
        clearInputFile();
        setVendors({imported: [], notFound: [], count: 0});
    }, [props.open]);

    const content = useMemo(() => {
        switch (step) {
            case 1:
                return <ImportPartnersFromCSVStepOne errorMessage={errorMessage} />;
            case 2:
                return <ImportPartnersFromCSVStepTwo />;
            case 3:
                return <ImportPartnersFromCSVStepThree />;
            case 4:
                return <ImportPartnersFromCSVStepFour />;
        }
    }, [step, errorMessage]);

    const okButtonText = useMemo(() => {
        if (step === 4) return "CLOSE";
        if (step === 3) {
            return vendors.notFound.length === 0 ? "CLOSE" : "SAVE";
        }
        return "UPLOAD";
    }, [step, vendors]);

    const modalIcon = useMemo(() => {
        if (step === 3 && vendors.notFound.length > 0) {
            return <CheckCircleOutlinedIcon />;
        }
        return <AddIcon />;
    }, [step, vendors]);

    const modalTitle = useMemo(() => {
        if (step !== 3 || vendors.notFound.length === 0) {
            return "Import CSV";
        }
        return `${vendors.imported.length}/${vendors.count} items imported`;
    }, [step, vendors]);

    return (
        <>
            <ModalComponent
                open={props.open}
                onClose={props.onClose}
                onSuccess={handleSubmit}
                title={modalTitle}
                okButtonText={okButtonText}
                showCancelButton={![3, 4].includes(step) || (step === 3 && vendors.notFound.length !== 0)}
                contentClassName="mt-0"
                modalTheme="blue"
                icon={modalIcon}
                justifyButtons="flex-start"
                loading={saving}
                dialogSx={{
                    color: theme.palette.neutral[500],
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%", gap: "16px"},
                }}
                content={
                    <ImportPartnersFromCSVContext.Provider
                        value={{
                            file,
                            setFile,
                            clearInputFile,
                            vendors,
                            updateVendor,
                            deleteVendor,
                            removeMatch,
                        }}>
                        <FormProvider {...methods}>
                            <form onSubmit={methods.handleSubmit(handleSubmit)}>{content}</form>
                        </FormProvider>
                    </ImportPartnersFromCSVContext.Provider>
                }
            />
        </>
    );
};

export default ImportPartnersFromCSVDialog;
