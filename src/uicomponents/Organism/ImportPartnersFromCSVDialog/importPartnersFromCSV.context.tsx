/**
Context providing data and methods related to importing partners from a CSV file.
Includes information about the uploaded file, imported vendors, vendors not found, and methods to update, 
    delete, and remove matches.

@property {File | undefined} file - The uploaded CSV file.
@property {React.Dispatch<React.SetStateAction<File | undefined>> | undefined} setFile - Function to set the uploaded CSV file.
@property {Function} clearInputFile - Function to clear the uploaded CSV file input.
@property {Object} vendors - Object containing arrays of imported and not found vendors.
@property {Array} vendors.imported - Array of imported vendors.
@property {Array} vendors.notFound - Array of vendors not found during import.
@property {Function} updateVendor - Method to relate a existing vendor to a not found vendor name.
@property {Function} deleteVendor - Method to delete a not found vendor from list.
@property {Function} removeMatch - Method to remove a match between a not found vendor name and an existing vendor.
@type {React.Context<IImportPartnersFromCSVContext>}
*/

import React from "react";

interface IImportPartnersFromCSVContext {
    file: File | undefined;
    setFile: React.Dispatch<React.SetStateAction<File | undefined>> | undefined;
    clearInputFile: () => void;
    vendors: {
        imported: any[];
        notFound: any[];
    };
    updateVendor?: (vendor_id: string, vendor: any) => void;
    deleteVendor?: (vendor_id: string) => void;
    removeMatch?: (vendor_id: string) => void;
}
export const ImportPartnersFromCSVContext = React.createContext<IImportPartnersFromCSVContext>({
    file: undefined,
    setFile: undefined,
    clearInputFile: () => {},
    vendors: {
        imported: [],
        notFound: [],
    },
    updateVendor: (_, __) => {},
    deleteVendor: _ => {},
    removeMatch: _ => {},
});
