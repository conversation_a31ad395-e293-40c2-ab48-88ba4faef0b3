/**
 * Step one - Distributors can upload a CSV file.
 *
 * @returns {JSX.Element} Step one component for importing partners from CSV.
 */

import UploadOutlinedIcon from "@mui/icons-material/UploadOutlined";
import {Box, List, useTheme} from "@mui/material";
import {CSSProperties, ReactNode, useContext} from "react";
import {ErrorIcon} from "../../../components/Icons";
import OutboundLink from "../../../components/Links/OutboundLink.component";
import {UploadComponent} from "../../../components/Upload/upload.component";
import {MediaTypes} from "../../../enums/mediaTypes.enum";
import Typography from "../../Atoms/Typography/Typography.component";
import {ListItemTypography} from "../../Molecules/ListItemTypography/ListItemTypography.component";
import {ImportPartnersFromCSVContext} from "./importPartnersFromCSV.context";

interface IProps {
    errorMessage?: string;
    setErrorMessage?: React.Dispatch<React.SetStateAction<string | undefined>>;
    title?: {
        text: string;
        sx?: CSSProperties;
    };
    instructions?: ReactNode;
}
const ImportPartnersFromCSVStepOne = ({errorMessage, setErrorMessage, title, instructions: _}: IProps) => {
    const theme = useTheme();
    const {file, setFile, clearInputFile} = useContext(ImportPartnersFromCSVContext);

    return (
        <Box sx={{display: "flex", gap: 2, flexDirection: "column"}}>
            <Typography
                fontInter
                variant="body3"
                sx={{
                    color: theme.palette.neutral[800],
                    fontWeight: 600,
                    ...(title?.sx || {}),
                }}>
                {title?.text ?? "Select or Drag and Drop your file"}
            </Typography>
            {!!errorMessage && (
                <Box
                    sx={{
                        backgroundColor: theme.palette.error[100],
                        borderColor: theme.palette.error[300],
                        borderStyle: "solid",
                        borderWidth: "1px",
                        borderRadius: "8px",
                        display: "flex",
                        alignItems: "center",
                        padding: "16px",
                        gap: 2,
                    }}>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 1,
                            flexDirection: "column",
                            "& svg": {
                                fill: theme.palette.error[500],
                            },
                        }}>
                        <ErrorIcon version="circle" size="24" />
                    </Box>
                    <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                        <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 600}}>
                            Invalid file
                        </Typography>
                        <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 400}}>
                            {errorMessage}
                        </Typography>
                    </Box>
                </Box>
            )}
            <Box sx={{display: "flex", gap: 3}}>
                <UploadComponent
                    id="uploadFile"
                    file={file}
                    setFile={setFile}
                    clearInputFile={clearInputFile}
                    maxSize={"5"}
                    icon={
                        <UploadOutlinedIcon
                            sx={{
                                color: theme.palette.blue[800],
                                height: 40,
                                width: 40,
                            }}
                        />
                    }
                    type={MediaTypes.CSV}
                    onError={setErrorMessage}
                    noSupportText
                    dropZoneProps={{
                        style: {
                            height: "100%",
                            backgroundColor: theme.palette.blue[100],
                            borderStyle: "dashed",
                            borderColor: theme.palette.blue[400],
                            borderWidth: "2px",
                            borderRadius: "16px",
                        },
                    }}
                />
                <Box sx={{display: "flex", gap: 1, flexDirection: "column", padding: "10px 0"}}>
                    <Typography
                        fontInter
                        variant="body2"
                        sx={{
                            color: theme.palette.neutral[700],
                            fontWeight: 600,
                        }}>
                        Instructions
                    </Typography>
                    <List dense sx={{listStyleType: "disc", pl: 3}}>
                        <ListItemTypography>Header row must be present in the file.</ListItemTypography>
                        <ListItemTypography>Vendor Company Name (vendorName) is required</ListItemTypography>
                        <ListItemTypography>Description (description) is optional</ListItemTypography>
                        <ListItemTypography>Link to your website (externalUrl) is optional</ListItemTypography>
                        <ListItemTypography>
                            File format: <span style={{fontWeight: 700}}>.CSV</span>
                        </ListItemTypography>
                        <ListItemTypography>
                            Maximum file: <span style={{fontWeight: 700}}>5 MB</span>
                        </ListItemTypography>
                        <ListItemTypography>
                            <OutboundLink
                                style={{color: theme.palette.secondary[600], fontWeight: 700}}
                                href={
                                    `${window.location.protocol}//${window.location.host}/` +
                                    "DistributorImportTemplate.csv"
                                }>
                                You can download our sample file here.
                            </OutboundLink>
                        </ListItemTypography>
                    </List>
                </Box>
            </Box>
        </Box>
    );
};
export default ImportPartnersFromCSVStepOne;
