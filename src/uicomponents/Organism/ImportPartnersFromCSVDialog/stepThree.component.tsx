/**
 * Step Three
 *  If all Vendors have been imported successfully, a message will be displayed, and the user will be able to close the modal;
 *  If there are only Vendors that have not been imported correctly, the user can search for available vendors
 *      in the system that best match the provided options.
 *  The Vendors that have been imported correctly will be displayed below.
 *
 * @returns {JSX.Element} Step two component for importing partners from CSV.
 */

import {Box} from "@mui/material";
import StepThreeUploadCompleted from "./stepThreeUploadCompleted.component";
import StepThreeNotFoundVendors from "./stepThreeNotFoundVendors.component";
import {useContext} from "react";
import {ImportPartnersFromCSVContext} from "./importPartnersFromCSV.context";
import ImportedVendors from "./ImportedVendors.component";

const ImportPartnersFromCSVStepThree = () => {
    const {vendors} = useContext(ImportPartnersFromCSVContext);

    return (
        <Box sx={{display: "flex", gap: 2, flexDirection: "column"}}>
            {vendors.notFound.length === 0 ? <StepThreeUploadCompleted /> : <StepThreeNotFoundVendors />}
            {vendors.imported.length > 0 && <ImportedVendors />}
        </Box>
    );
};
export default ImportPartnersFromCSVStepThree;
