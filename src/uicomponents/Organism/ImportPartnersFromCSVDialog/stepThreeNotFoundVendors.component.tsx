import {Box, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";
import {ErrorIcon} from "../../../components/Icons";
import {useContext} from "react";
import {ImportPartnersFromCSVContext} from "./importPartnersFromCSV.context";
import StepThreeMatchVendor from "./stepThreeMatchVendor.component";

const StepThreeNotFoundVendors = () => {
    const theme = useTheme();
    const {vendors} = useContext(ImportPartnersFromCSVContext);

    return (
        <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
            <Box
                sx={{
                    backgroundColor: theme.palette.warning[100],
                    borderColor: theme.palette.warning[300],
                    borderStyle: "solid",
                    borderWidth: "1px",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    padding: "16px",
                    gap: 2,
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 1,
                        flexDirection: "column",
                        "& svg": {
                            fill: theme.palette.warning[600],
                        },
                    }}>
                    <ErrorIcon version="triangle" size="24" />
                </Box>
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography variant="body3" fontWeight="bold !important" color={theme.palette.neutral[800]}>
                        Ah, Snap! Don’t see your vendor?
                    </Typography>
                    <Typography variant="body3" color={theme.palette.neutral[800]}>
                        If you can't find the vendor, leave the field blank. After saving, a request will be sent to add
                        missing information. Thanks for your help.
                    </Typography>
                </Box>
            </Box>

            <Typography fontInter variant="body2" color={theme.palette.neutral[800]} sx={{fontWeight: 700}}>
                Please check the detail for data match
            </Typography>
            <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 400}}>
                Unfortunately, we couldn't find all your vendors with the provided name. Please use the fields below to
                search for the vendor.
            </Typography>
            <form>
                <Box sx={{display: "flex", gap: 1, flexDirection: "column", width: "100%"}}>
                    {vendors.notFound?.map(vendor => (
                        <StepThreeMatchVendor key={vendor.id} {...vendor} />
                    ))}
                </Box>
            </form>
        </Box>
    );
};

export default StepThreeNotFoundVendors;
