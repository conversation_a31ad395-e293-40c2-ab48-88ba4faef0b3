import {RefreshOutlined} from "@mui/icons-material";
import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import {useQueryClient} from "@tanstack/react-query";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IRole, ITemplateRole} from "../../../Interfaces/permissions.interface";
import {rolesService} from "../../../services/roles.service";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import RoleListInput from "../../Molecules/RoleListInput/RoleListInput.component";

interface IProps {
    role: ITemplateRole | IRole;
    open: boolean;
    onClose: () => void;
    isTemplate?: boolean;
}

export default function DeleteRoleDialog({role, open, onClose, isTemplate}: IProps) {
    const [deleting, setDeleting] = useState<boolean>(false);
    const queryClient = useQueryClient();
    const methods = useForm();
    const isReset = "template_role_id" in role && !!role.template_role_id;
    const needReassign = "users_count" in role && role.users_count > 0 && !isReset;

    const handleSubmit = () => {
        setDeleting(true);
        (isTemplate
            ? rolesService.deleteTemplate(role.id)
            : rolesService.deleteRole(
                  needReassign ? {id: role.id, role_id: methods.getValues("role_id")} : {id: role.id},
              )
        )
            .then(() => {
                queryClient.resetQueries(["roles"]);
                queryClient.resetQueries(["template-roles"]);
                onClose();
            })
            .finally(() => setDeleting(false));
    };

    return (
        <ModalComponent
            open={open}
            onClose={onClose}
            title={isReset ? "Reset Role" : "Delete Role?"}
            modalTheme="error"
            onSuccess={handleSubmit}
            okButtonText={
                isReset ? (
                    <>
                        <RefreshOutlined sx={{width: "16px", height: "16px", color: "neutral.100", marginRight: 1}} />
                        RESET
                    </>
                ) : (
                    <>
                        <DeleteOutlined sx={{width: "16px", height: "16px", color: "neutral.100", marginRight: 1}} />
                        DELETE
                    </>
                )
            }
            justifyButtons="flex-start"
            loading={deleting}
            content={
                <Box sx={{display: "flex", flexDirection: "column", gap: 3}}>
                    {needReassign ? (
                        <FormProvider {...methods}>
                            <Typography>
                                You are trying to delete a Role that has Users assigned to. To proceed with your
                                request, first choose a role to reassign all <b>{role.users_count}</b> users under{" "}
                                <b>{isTemplate ? role.title : role.display_name}</b>:
                            </Typography>
                            <RoleListInput
                                id="role_id"
                                sx={{
                                    width: "100%",
                                }}
                                label="Role"
                                placeholder="Select Role"
                                filterIds={[role.id]}
                            />
                        </FormProvider>
                    ) : (
                        <Typography>
                            Are you sure you want to {isReset ? "reset" : "delete"}{" "}
                            <b>{isTemplate ? role.title : role.display_name}</b>? This action can not be undone.
                        </Typography>
                    )}
                </Box>
            }
        />
    );
}
