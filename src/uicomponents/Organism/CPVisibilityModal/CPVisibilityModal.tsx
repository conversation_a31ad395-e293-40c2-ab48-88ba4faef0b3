import {Grid} from "@mui/material";
import {useEffect} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {VISIBILITY_OPTIONS} from "../../../constants/visibilityLevels.constant";
import useNotification from "../../../hooks/useNotification";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import RadioWithLabel from "../../Molecules/RadioWithLabel/RadioWithLabel.component";

interface IProps {
    setShowConfirmationModal?: any;
    showConfirmationModal?: any;
    handleSuccess?: any;
    okButtonText: string;
    title: string;
    description: string;
}

const CPVisibilityModal = (props: IProps) => {
    const {showConfirmationModal, setShowConfirmationModal} = props;
    const methods = useForm();
    const notify = useNotification();
    const visibilityType = methods.watch("visibilityType");
    const {show, type, data} = showConfirmationModal;
    const handleClick = () => {
        if (type === "add" && !visibilityType) {
            return notify("Please select visibility type", "Error");
        }
        props.handleSuccess(type, data, visibilityType);
    };

    useEffect(() => {
        if (show) {
            methods.setValue(
                "visibilityType",
                data?.custom_properties?.media_visibility ?? data?.media_visibility ?? "all",
            );
        }
    }, [show]);

    return (
        <ModalComponent
            onSuccess={handleClick}
            open={show}
            onClose={() => setShowConfirmationModal({show: false, type: ""})}
            okButtonText={props?.okButtonText}
            cancelButtonText="Cancel"
            title={props.title}
            content={
                <Grid container display={"flex"} flexDirection={"column"}>
                    <Grid container display={"flex"} alignItems={"center"} flexDirection={"column"}>
                        {props?.description}
                        {type === "add" && (
                            <FormProvider {...methods}>
                                <div className="d-flex flex-column align-items-start">
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.VENDORS_ONLY}
                                        id="visibilityType"
                                        value="vendor"
                                    />
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.MSP_ONLY}
                                        id="visibilityType"
                                        value="msp"
                                    />
                                    <RadioWithLabel
                                        label={VISIBILITY_OPTIONS.ALL_USERS}
                                        id="visibilityType"
                                        value="all"
                                    />
                                </div>
                            </FormProvider>
                        )}
                    </Grid>
                </Grid>
            }
            modalTheme={type === "add" ? "success" : "error"}
            justifyButtons={"center"}
        />
    );
};

export default CPVisibilityModal;
