import {useCallback, useEffect, useRef, useState} from "react";
import useCSVUpload from "../../../hooks/useCSVUpload";
import Box from "../../Atoms/Box/Box.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import useServiceWorker, {TFileUploadActionMessage} from "../../../hooks/useServiceWorker";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import {pluralizeString} from "../../../utils/formatString.util";
import Typography from "../../Atoms/Typography/Typography.component";
import useTheme from "@mui/material/styles/useTheme";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import ArrowRightAltOutlinedIcon from "@mui/icons-material/ArrowRightAltOutlined";
import {FormProvider, useForm} from "react-hook-form";
import TextField from "@mui/material/TextField";
import VendorSearch from "../../Molecules/VendorSearch/vendorSearch.component";
import ProductSearch from "../../Molecules/ProductSearch/productSearch.component";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {mspContractsService} from "../../../services/mspContract.service";
import {useQuery} from "@tanstack/react-query";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import Skeleton from "@mui/material/Skeleton";
import InputAdornment from "@mui/material/InputAdornment";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ClientVendorInput from "../../Molecules/ClientVendorInput/clientVendorInput.component";
import ClientProductInput from "../../Molecules/ClientProductInput/clientProductInput.component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import Button from "../../Atoms/Button/Button.Component";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";
import {IMatchedSubscription, IUnmatchedSubscription} from "../../../Interfaces/mspContracts.interface";
import {Chip} from "@mui/material";

export const CSV_EVENTS = {
    OPEN_IMPORT_CONTRACT_DETAILS: "openImportContractsDetails",
    OPEN_IMPORT_CONTRACT_REVIEW: "openImportContractsReview",
};

const UNMATCHED_MAP_IDENTIFIER_PREPENDS = {
    vendor: "vendor_",
    product: "product_",
};

const getItemIdentifier = (item: any) => {
    if (typeof item === "string" || typeof item === "number") return item;
    const objectHash = JSON.stringify(item)
        .split("")
        .reduce((acc, char) => {
            return acc + char.charCodeAt(0).toString(36);
        }, "");
    return objectHash;
};

const IMPORT_CSV_TYPES = {
    CONTRACT: "CONTRACT",
} as const;
type ImportCsvTypesKeys = (typeof IMPORT_CSV_TYPES)[keyof typeof IMPORT_CSV_TYPES];

const GlobalCSVUploadCompleteDialog = () => {
    const [successAlert, setSuccessAlert] = useState<{title: string; message: string} | undefined>();
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const {
        openGlobalCSVUploadCompleteDialog,
        setOpenGlobalCSVUploadCompleteDialog,
        status,
        updateStatus,
        reset,
        setPusherCacheId,
        pusherCacheId,
    } = useCSVUpload();
    const {registerActionCallback, unregisterActionCallback} = useServiceWorker();
    const theme = useTheme();
    const methods = useForm();
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {invalidateMatchedQueries} = useQueryHelper();
    const [label, setLabel] = useState("Item");
    const [isDistributorTracker, setIsDistributorTracker] = useState<{[key: string]: boolean}>({});
    const isIncomplete =
        (status.notFound && status.notFound?.length > 0) || (status.unmatched && status.unmatched.length > 0);
    const isSuccessful = !isIncomplete;
    const success_count = status?.successful?.length || 0;
    const total_items = Object.values(status || {})?.reduce((prev, cur) => prev + (cur?.length || 0), 0);
    const import_type = useRef<ImportCsvTypesKeys | undefined>();
    const companyOverride = useRef<{id?: string; friendly_url?: string; isClientMsp?: boolean} | undefined>();
    const companyId = companyOverride.current?.id || activeCompany?.id;

    const csvImportDetailsQuery = useQuery<{matched: any[]; unmatched: any[]}>(
        ["csvImportDetails", pusherCacheId, companyId, openGlobalCSVUploadCompleteDialog],
        () =>
            new Promise(resolve => {
                mspContractsService
                    .getCSVContractImportDetails(companyId || "", pusherCacheId)
                    .then(res => resolve(res.data));
            }),
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!pusherCacheId && !!companyId && !!openGlobalCSVUploadCompleteDialog,
        },
    );

    const handleClose = () => {
        reset();
        setOpenGlobalCSVUploadCompleteDialog(false);
        companyOverride.current = undefined;
        successAlert && setSuccessAlert(undefined);
    };

    const handleActionMessages = (e: MessageEvent<TFileUploadActionMessage>) => {
        const action = e.data.action;
        const custom_properties = e.data?.file?.customProperties;
        const pusher_id = custom_properties?.cache_token;
        const {file} = e.data;
        if (pusher_id) setPusherCacheId(pusher_id);
        if (file?.aditionalParams?.company_id && file?.aditionalParams?.isClientMsp !== undefined) {
            companyOverride.current = {
                id: file?.aditionalParams?.company_id,
                isClientMsp: file?.aditionalParams?.isClientMsp,
            };
        }
        switch (action) {
            case CSV_EVENTS.OPEN_IMPORT_CONTRACT_DETAILS:
                import_type.current = IMPORT_CSV_TYPES.CONTRACT;
                invalidateMatchedQueries(["firstLoad", activeCompany?.id || ""]);
                updateStatus({
                    successful: (custom_properties?.imported as IMatchedSubscription[]) || [],
                    unmatched: (custom_properties?.unmatched as IUnmatchedSubscription[]) || [],
                });
                setLabel("Contract");
                setOpenGlobalCSVUploadCompleteDialog(true);
                break;
            case CSV_EVENTS.OPEN_IMPORT_CONTRACT_REVIEW:
                import_type.current = IMPORT_CSV_TYPES.CONTRACT;
                invalidateMatchedQueries(["firstLoad", activeCompany?.id || ""]);
                updateStatus({
                    successful: (custom_properties?.imported as IMatchedSubscription[]) || [],
                    unmatched: (custom_properties?.unmatched as IUnmatchedSubscription[]) || [],
                });
                setLabel("Contract");
                setOpenGlobalCSVUploadCompleteDialog(true);
                break;
            default:
                break;
        }
    };

    const removeUnmatched = useCallback(
        (itemToRemove: any) => {
            updateStatus({
                ...status,
                unmatched:
                    status.unmatched?.filter(item => {
                        return typeof item === "object"
                            ? getItemIdentifier(item) !== getItemIdentifier(itemToRemove)
                            : item !== itemToRemove;
                    }) || [],
            });
        },
        [status],
    );

    const handleSubmit = async () => {
        if (await methods.trigger()) {
            const formValues = methods.getValues();
            const updateObj = {};
            Object.keys(formValues)?.forEach(fieldId => {
                const [field, identifier] = fieldId.split("_");
                const foundUnmatched = status.unmatched?.find(item => {
                    return getItemIdentifier(item) === identifier;
                });
                if (identifier && foundUnmatched) {
                    updateObj[identifier] = {
                        ...foundUnmatched,
                        [field]: formValues[fieldId],
                    };
                }
            });
            if (import_type.current === IMPORT_CSV_TYPES.CONTRACT) {
                const unmatched: IUnmatchedSubscription[] = [];
                csvImportDetailsQuery.data?.unmatched?.forEach((item: IUnmatchedSubscription) => {
                    if (item.product_contracts && item.product_contracts?.length > 1) {
                        item.product_contracts.forEach(product_contract => {
                            unmatched.push({
                                ...item,
                                product_contracts: [product_contract],
                            });
                        });
                    } else {
                        unmatched.push(item);
                    }
                });
                let allItemsMatched = true;
                const toSaveArray = unmatched?.map(item => {
                    const identifier = getItemIdentifier(item);
                    const newVendorValue = formValues[UNMATCHED_MAP_IDENTIFIER_PREPENDS.vendor + identifier];
                    const newProductValue = formValues[UNMATCHED_MAP_IDENTIFIER_PREPENDS.product + identifier];
                    if (!newVendorValue || !newProductValue) {
                        allItemsMatched = false;
                    }
                    return {
                        ...item,
                        [isClientMsp ? "client_vendor_id" : "company_id"]: newVendorValue || null,
                        product_contracts: item.product_contracts?.map(item => ({
                            ...item,
                            [isClientMsp ? "client_product_id" : "product_id"]: newProductValue || null,
                        })),
                    };
                });
                setIsSubmitting(true);
                mspContractsService
                    .saveCSVContractImportDetails(companyId || "", toSaveArray as IUnmatchedSubscription[])
                    .then(() => {
                        setSuccessAlert({
                            title: allItemsMatched
                                ? `${toSaveArray.length} ${pluralizeString(
                                      "item",
                                      toSaveArray.length,
                                  )} successfully imported`
                                : "Your contract uploads are complete.",
                            message: allItemsMatched
                                ? "Your data was successfully imported"
                                : "The team has been notified of any missing vendors or products and will add them shortly.",
                        });
                        invalidateMatchedQueries(["firstLoad", companyId || ""]);
                    })
                    .finally(() => {
                        setIsSubmitting(false);
                    });
            }
        }
    };

    const getCompanyName = (item: any) => {
        if (typeof item === "string" || typeof item === "number") return "";
        if (item?.company_name) return item?.company_name;
        if (item?.company?.name) {
            return item?.company?.name;
        } else if (item?.vendor && typeof item?.vendor === "string") {
            return item?.vendor;
        }
    };

    const getProductName = (item: any) => {
        if (typeof item === "string" || typeof item === "number") return "";
        if (item?.product_name) return item?.product_name;
        if (item?.product?.name) {
            return item?.product?.name;
        } else if (item?.product && typeof item?.product === "string") {
            return item?.product;
        }
    };

    const defaultAutocompleteTitle = (params, onClose, title: string) => (
        <>
            <Box
                display="flex"
                position="sticky"
                top={0}
                left={0}
                flexDirection="row"
                width="100%"
                justifyContent="space-between"
                alignItems="center"
                paddingX={2}
                paddingY={1}
                bgcolor="#fff"
                zIndex={2}>
                <Typography variant="body3" fontInter fontWeight={600} color={theme.palette.neutral[700]}>
                    {title}
                </Typography>
                <Button
                    id="close-options"
                    color="blue"
                    variant="text"
                    size="small"
                    sx={{
                        gap: 0.5,
                        padding: "8px 4px !important",
                    }}
                    onClick={() => onClose?.()}>
                    <CloseOutlinedIcon htmlColor={theme.palette.blue[600]} sx={{height: 18, width: 18}} />
                    <Typography variant="body4" fontInter color={theme.palette.blue[600]}>
                        Close
                    </Typography>
                </Button>
            </Box>
            {params.children}
        </>
    );

    useEffect(() => {
        if (!pusherCacheId || !companyId || !csvImportDetailsQuery.data) return;
        if (import_type.current === IMPORT_CSV_TYPES.CONTRACT) {
            const unmatched: any[] = [];
            if (csvImportDetailsQuery.data?.unmatched) {
                //? Adds a new row for each product_contract record for each parent
                csvImportDetailsQuery.data?.unmatched?.forEach(item => {
                    if (item.product_contracts?.length > 1) {
                        item.product_contracts.forEach(product_contract => {
                            unmatched.push({
                                ...item,
                                product_contracts: [product_contract],
                            });
                        });
                    } else {
                        unmatched.push(item);
                    }
                });
            }
            updateStatus({
                successful: csvImportDetailsQuery.data?.matched || [],
                unmatched,
            });
            unmatched?.forEach(unmatchedItem => {
                const identifier = getItemIdentifier(unmatchedItem);
                const vendorId =
                    unmatchedItem?.company_id || unmatchedItem?.company?.id || unmatchedItem?.client_vendor_id;
                if (identifier) {
                    methods.setValue(UNMATCHED_MAP_IDENTIFIER_PREPENDS.vendor + identifier, vendorId || "");
                }
                if (unmatchedItem?.product_contracts?.length) {
                    unmatchedItem?.product_contracts?.forEach(product_contract => {
                        methods.setValue(
                            UNMATCHED_MAP_IDENTIFIER_PREPENDS.product + identifier,
                            product_contract?.product_id || "",
                        );
                    });
                }
            });
            return;
        }
        updateStatus({
            successful: csvImportDetailsQuery.data?.matched || [],
            unmatched: csvImportDetailsQuery.data?.unmatched || [],
        });
    }, [csvImportDetailsQuery.data, openGlobalCSVUploadCompleteDialog]);

    useEffect(() => {
        registerActionCallback(handleActionMessages);
        return () => {
            unregisterActionCallback(handleActionMessages);
        };
    }, []);

    if (!openGlobalCSVUploadCompleteDialog) return null;
    return (
        <ErrorBoundary>
            <ModalComponent
                open={openGlobalCSVUploadCompleteDialog}
                onClose={handleClose}
                icon={isIncomplete ? <CheckCircleOutlinedIcon /> : <AddOutlinedIcon />}
                title={!successAlert && isIncomplete ? `${success_count}/${total_items} items imported` : "Import CSV"}
                onSuccess={isSuccessful || !!successAlert ? handleClose : handleSubmit}
                loading={isSubmitting}
                content={
                    csvImportDetailsQuery.isFetching ? (
                        <Box display="flex" flexDirection="column" gap={2}>
                            {Array(4)
                                .fill(null)
                                .map((_, i) => (
                                    <Skeleton key={i} width="100%" height={50} />
                                ))}
                        </Box>
                    ) : (
                        <Box display="flex" flexDirection="column" gap={2}>
                            {!!successAlert && (
                                <AlertBanner
                                    id="csv-upload-success"
                                    variant="success"
                                    title={{text: successAlert.title}}
                                    subTitle={{text: successAlert.message}}
                                />
                            )}
                            {!successAlert && isIncomplete && (
                                <>
                                    <AlertBanner
                                        id="csv-upload-success"
                                        variant="warning"
                                        title={{
                                            text: "Ah, Snap! Don’t see your vendor or product?",
                                            sx: {fontSize: "16px !important"},
                                        }}
                                        subTitle={{
                                            text: (
                                                <>
                                                    If the vendor or product isn't available, please leave the field
                                                    blank. Once you save, a request will automatically be sent to update
                                                    the missing information. Thank you for your assistance.
                                                </>
                                            ),
                                        }}
                                    />
                                    <Typography fontInter variant="body2" fontWeight={600}>
                                        Please check the Columns for data match
                                    </Typography>
                                    <Typography fontInter variant="body3">
                                        We couldn't locate all your vendors and/or products with the provided details.
                                        Please use the fields below to search for the missing information.
                                    </Typography>
                                    <FormProvider {...methods}>
                                        {status?.unmatched?.map(item => {
                                            const isContractImport = import_type.current === IMPORT_CSV_TYPES.CONTRACT;
                                            const identifier = getItemIdentifier(item);
                                            const productName = isContractImport
                                                ? item.product_contracts?.[0]?.product_name
                                                : getProductName(item);
                                            const csv_line = isContractImport
                                                ? item.product_contracts?.[0]?.csv_line + 1
                                                : undefined;
                                            const productObj = isContractImport
                                                ? item.product_contracts?.[0]?.product
                                                : null;
                                            const vendorName = isContractImport
                                                ? item.company?.name || item.company_name
                                                : getCompanyName(item);
                                            const vendorObj = isContractImport ? item.company : undefined;
                                            const vendorInputValue = methods.watch(
                                                UNMATCHED_MAP_IDENTIFIER_PREPENDS.vendor + identifier,
                                            );
                                            const is_distributor_selected =
                                                !!isDistributorTracker?.[identifier] || !!vendorObj?.is_distributor;
                                            return (
                                                <BorderedBox
                                                    key={identifier}
                                                    sx={{
                                                        paddingX: 2,
                                                        paddingY: 1,
                                                        width: "100%",
                                                        display: "flex",
                                                        flexDirection: "column",
                                                    }}>
                                                    <Typography
                                                        fontInter
                                                        variant="body3"
                                                        color={theme.palette.neutral[600]}
                                                        display="flex"
                                                        alignItems="center"
                                                        sx={{verticalAlign: "center"}}>
                                                        {csv_line !== undefined && `Line ${csv_line} - `}CSV Record:{" "}
                                                        {vendorName}{" "}
                                                        <ArrowForwardOutlinedIcon
                                                            sx={{height: 16, width: 16, marginX: 0.5}}
                                                        />{" "}
                                                        {productName}
                                                    </Typography>
                                                    <Box
                                                        display="grid"
                                                        gridTemplateColumns={"1fr 24px 1fr 24px"}
                                                        gap={3}
                                                        alignItems="center">
                                                        <Box display="flex" flexDirection="column" gap={0}>
                                                            <TextField
                                                                value={vendorName}
                                                                sx={{
                                                                    input: {
                                                                        backgroundColor: "transparent !important",
                                                                        paddingBottom: "16px !important",
                                                                    },
                                                                }}
                                                                InputProps={{
                                                                    readOnly: true,
                                                                    endAdornment: (
                                                                        <InputAdornment
                                                                            position="end"
                                                                            sx={{
                                                                                position: "absolute",
                                                                                right: 16,
                                                                                top: "50%",
                                                                            }}>
                                                                            <CPTooltip
                                                                                title={
                                                                                    !!vendorObj
                                                                                        ? "Vendor match found"
                                                                                        : "No matching vendor found, please review."
                                                                                }>
                                                                                <InfoOutlinedIcon
                                                                                    htmlColor={theme.palette.blue[900]}
                                                                                    sx={{width: 24, height: 24}}
                                                                                />
                                                                            </CPTooltip>
                                                                        </InputAdornment>
                                                                    ),
                                                                }}
                                                            />
                                                            <TextField
                                                                value={productName}
                                                                sx={{
                                                                    input: {
                                                                        backgroundColor: "transparent !important",
                                                                    },
                                                                }}
                                                                InputProps={{
                                                                    readOnly: true,
                                                                    endAdornment: (
                                                                        <InputAdornment
                                                                            position="end"
                                                                            sx={{
                                                                                position: "absolute",
                                                                                right: 16,
                                                                                top: "50%",
                                                                            }}>
                                                                            <CPTooltip
                                                                                title={
                                                                                    !!productObj
                                                                                        ? "Product match found"
                                                                                        : "No matching product found, please review."
                                                                                }>
                                                                                <InfoOutlinedIcon
                                                                                    htmlColor={theme.palette.blue[900]}
                                                                                    sx={{width: 24, height: 24}}
                                                                                />
                                                                            </CPTooltip>
                                                                        </InputAdornment>
                                                                    ),
                                                                }}
                                                            />
                                                        </Box>
                                                        <Box display="flex" alignItems="center" justifyContent="center">
                                                            <ArrowRightAltOutlinedIcon />
                                                        </Box>
                                                        <Box
                                                            display="flex"
                                                            flexDirection="column"
                                                            gap={0}
                                                            width="calc(100% - 24px)">
                                                            {isClientMsp ? (
                                                                <>
                                                                    <ClientVendorInput
                                                                        companyId={companyId ?? ""}
                                                                        id={
                                                                            UNMATCHED_MAP_IDENTIFIER_PREPENDS.vendor +
                                                                            identifier
                                                                        }
                                                                        placeholder="Input the vendor name"
                                                                        label="Vendor/Distributor"
                                                                        InputLabelProps={{
                                                                            shrink: true,
                                                                        }}
                                                                        validateOnTheFly
                                                                        isRequired
                                                                        helperText=""
                                                                        keepRegistered
                                                                        sx={{
                                                                            width: "100%",
                                                                            ".MuiInput-input": {
                                                                                paddingBottom: "24px !important",
                                                                            },
                                                                        }}
                                                                        showTagForSingleSelect
                                                                        showRadios
                                                                    />
                                                                    <ClientProductInput
                                                                        companyId={companyId ?? ""}
                                                                        id={
                                                                            UNMATCHED_MAP_IDENTIFIER_PREPENDS.product +
                                                                            identifier
                                                                        }
                                                                        placeholder="Start Typing..."
                                                                        label="Product"
                                                                        validateOnTheFly
                                                                        InputLabelProps={{
                                                                            shrink: true,
                                                                        }}
                                                                        sx={{
                                                                            width: "100%",
                                                                            ".MuiInput-input": {
                                                                                paddingBottom: "24px !important",
                                                                            },
                                                                        }}
                                                                        helperText=""
                                                                        clearOnBlur={false}
                                                                        getOptionLabel={(option: any) => option?.label}
                                                                        getOptionKey={(option: any) => option?.id}
                                                                        showTagForSingleSelect
                                                                        showRadios
                                                                    />
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <VendorSearch
                                                                        id={
                                                                            UNMATCHED_MAP_IDENTIFIER_PREPENDS.vendor +
                                                                            identifier
                                                                        }
                                                                        sx={{
                                                                            width: "100%",
                                                                            ".MuiInput-input": {
                                                                                paddingBottom: "26px !important",
                                                                            },
                                                                        }}
                                                                        clearOnBlur={false}
                                                                        clearOnSelect={false}
                                                                        label="Vendor"
                                                                        validateOnTheFly
                                                                        showTagForSingleSelect
                                                                        defaultOptions={
                                                                            vendorObj ? [vendorObj] : undefined
                                                                        }
                                                                        readOnly={!!vendorObj}
                                                                        showRadios
                                                                        groupBy={() => "default"}
                                                                        renderGroupWithClose={(params, onClose) =>
                                                                            defaultAutocompleteTitle(
                                                                                params,
                                                                                onClose,
                                                                                "Select a Vendor",
                                                                            )
                                                                        }
                                                                        onValueChange={(_, __, newValue) => {
                                                                            setIsDistributorTracker({
                                                                                ...isDistributorTracker,
                                                                                [identifier]:
                                                                                    !!newValue?.company?.is_distributor,
                                                                            });
                                                                        }}
                                                                    />
                                                                    <ProductSearch
                                                                        id={
                                                                            UNMATCHED_MAP_IDENTIFIER_PREPENDS.product +
                                                                            identifier
                                                                        }
                                                                        onValueChange={() => null}
                                                                        sx={{
                                                                            width: "100%",
                                                                            ".MuiFilledInput-input": {
                                                                                paddingBottom: "26px !important",
                                                                            },
                                                                        }}
                                                                        clearOnBlur={false}
                                                                        clearOnSelect={true}
                                                                        label="Product"
                                                                        validateOnTheFly
                                                                        showTagForSingleSelect
                                                                        defaultOptions={
                                                                            productObj ? [productObj] : undefined
                                                                        }
                                                                        readOnly={!!productObj}
                                                                        showRadios
                                                                        groupBy={() => "default"}
                                                                        renderGroupWithClose={(params, onClose) =>
                                                                            defaultAutocompleteTitle(
                                                                                params,
                                                                                onClose,
                                                                                "Select a Product",
                                                                            )
                                                                        }
                                                                        disabled={!vendorInputValue}
                                                                        companyId={
                                                                            is_distributor_selected
                                                                                ? undefined
                                                                                : vendorInputValue
                                                                        }
                                                                        key={vendorInputValue}
                                                                    />
                                                                </>
                                                            )}
                                                        </Box>
                                                        <Box
                                                            display="flex"
                                                            alignItems="center"
                                                            justifyContent="center"
                                                            sx={{cursor: "pointer"}}
                                                            onClick={() => removeUnmatched(item)}
                                                            title="Remove">
                                                            <CancelOutlinedIcon
                                                                htmlColor={theme.palette.neutral[600]}
                                                            />
                                                        </Box>
                                                    </Box>
                                                </BorderedBox>
                                            );
                                        })}
                                    </FormProvider>
                                </>
                            )}
                            {!successAlert && isSuccessful && (
                                <AlertBanner
                                    id="csv-upload-success"
                                    variant="success"
                                    title={{
                                        text: `${success_count} ${pluralizeString(
                                            "item",
                                            success_count,
                                        )} successfully imported`,
                                        sx: {fontSize: "16px !important"},
                                    }}
                                    subTitle={{
                                        text: "Your data was successfully imported",
                                    }}
                                />
                            )}
                            {!successAlert && !!status.successful?.length && (
                                <>
                                    <Typography
                                        fontInter
                                        variant="body2"
                                        color={theme.palette.neutral[700]}
                                        sx={{fontWeight: 600}}>
                                        Imported {pluralizeString(label, success_count)}
                                    </Typography>
                                    <Box sx={{display: "flex", gap: 1, flexWrap: "wrap"}}>
                                        {status.successful?.map((item, i) => {
                                            const chipLabel =
                                                import_type.current === IMPORT_CSV_TYPES.CONTRACT ? (
                                                    <>
                                                        {item.company?.name || item.client_vendor?.name}
                                                        <ArrowForwardOutlinedIcon
                                                            sx={{height: 14, width: 14, marginX: 0.5}}
                                                        />
                                                        {item.product?.name || item.client_product?.name}
                                                    </>
                                                ) : (
                                                    ""
                                                );
                                            return (
                                                <Box
                                                    key={i}
                                                    sx={{
                                                        borderRadius: 1,
                                                        backgroundColor: theme.palette.neutral[100],
                                                        border: `1px solid ${theme.palette.blue[300]}`,
                                                        display: "flex",
                                                        flexDirection: "row",
                                                        alignItems: "center",
                                                        gap: 1,
                                                        paddingX: 1,
                                                        paddingY: 0.5,
                                                        span: {
                                                            color: theme.palette.blue[800],
                                                            fontWeight: 600,
                                                            fontSize: 14,
                                                        },
                                                    }}>
                                                    {item.company && (
                                                        <AvatarComponentV2
                                                            isCompany
                                                            user={item.company}
                                                            showProfileType
                                                            profileTypeMinimized
                                                            size={32}
                                                            isRedirectEnabled={false}
                                                        />
                                                    )}
                                                    <Typography fontInter variant="subtitle3">
                                                        {chipLabel}{" "}
                                                        {!!item.custom_properties?.action ? (
                                                            <Chip
                                                                sx={{
                                                                    display: "inline-flex",
                                                                    gap: "4px",
                                                                    borderWidth: "1px",
                                                                    borderStyle: "solid",
                                                                    borderRadius: "17px",
                                                                    height: "fit-content",
                                                                    backgroundColor:
                                                                        item.custom_properties?.action === "updated"
                                                                            ? theme.palette.blue[100]
                                                                            : theme.palette.success[100],
                                                                    borderColor:
                                                                        item.custom_properties?.action === "updated"
                                                                            ? theme.palette.blue[300]
                                                                            : theme.palette.success[300],
                                                                }}
                                                                label={
                                                                    <span
                                                                        style={{
                                                                            fontSize: "12px",
                                                                            color:
                                                                                item.custom_properties?.action ===
                                                                                "updated"
                                                                                    ? theme.palette.blue[700]
                                                                                    : theme.palette.success[700],
                                                                        }}>
                                                                        {item.custom_properties?.action === "updated"
                                                                            ? "Updated"
                                                                            : "New"}
                                                                    </span>
                                                                }
                                                            />
                                                        ) : null}
                                                    </Typography>
                                                </Box>
                                            );
                                        })}
                                    </Box>
                                </>
                            )}
                        </Box>
                    )
                }
                showOkButton={!successAlert}
                justifyButtons="flex-start"
                okButtonText={isIncomplete ? "SAVE" : "CLOSE"}
                cancelButtonText={!successAlert ? "CANCEL" : "CLOSE"}
                cancelButtonVariant={!successAlert ? "outlined" : "contained"}
                showCancelButton={isIncomplete}
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflow: "hidden", overflowY: "auto"},
                }}
            />
        </ErrorBoundary>
    );
};

export default GlobalCSVUploadCompleteDialog;
