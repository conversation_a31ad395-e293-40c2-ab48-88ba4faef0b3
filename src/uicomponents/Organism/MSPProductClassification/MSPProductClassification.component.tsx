import AddIcon from "@mui/icons-material/Add";
import ArrowDropDownOutlinedIcon from "@mui/icons-material/ArrowDropDownOutlined";
import ArrowDropUpOutlinedIcon from "@mui/icons-material/ArrowDropUpOutlined";
import CheckIcon from "@mui/icons-material/Check";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import SearchIcon from "@mui/icons-material/Search";
import StarOutlinedIcon from "@mui/icons-material/StarOutlined";
import Divider from "@mui/material/Divider";
import Paper from "@mui/material/Paper";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {UseQueryResult} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import React, {Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState} from "react";
import {OverlayTrigger, Popover} from "react-bootstrap";
import {Link} from "react-router-dom";
import {INaviStackAdvertisement} from "../../../Interfaces/adminAdvertisements.interface";
import {ICategory} from "../../../Interfaces/categories.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {IBrandStackAdoptionDetailItem} from "../../../Interfaces/mspAffiliates.interface";
import {IStackCategorization, IStackCompany} from "../../../Interfaces/myStack.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import ContactVendorDialog from "../../../builder_cms/components/contactVendorModal";
import {addToStack, getStackTypeLabel} from "../../../constants/commonStrings.constant";
import CompanyType from "../../../constants/companyType.constant";
import {ADOPTION_STATUS, ADOPTION_STATUS_BADGE} from "../../../constants/mspAdoption.constant";
import routeConfig from "../../../constants/routeConfig";
import {useMspAffiliates} from "../../../hooks/fetches/useMspAffiliates";
import {useNaviStack} from "../../../hooks/fetches/useNaviStack";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useIsViewingAsAffiliateParent from "../../../hooks/useIsViewingAsAffiliateParent";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {categoriesService} from "../../../services/category.service";
import {getUniqueItemsByProperties} from "../../../utils/array.util";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {generateActivityLog} from "../../../utils/customTracking.util";
import {getErrorFromArray} from "../../../utils/error.util";
import fuzzySearch from "../../../utils/fuzzySearch.util";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import AffiliateBrandStackAdoptionWidget from "../AffiliatesBrandStackAdoptionWidget/AffiliateBrandStackAdoptionWidget.component";
import ChangePartnershipTypeModal from "../ChangePartnerShipTypeModal/ChangePartnerShipTypeModal.component";
import AffiliateParentStackLegend from "../ManageStack/AffiliateParentStackLegend.component";
import {IAvailableVendor} from "../MspTechStackTab/MspTechStackTab.component";
import NaviStackProductDetailsModal from "../NavistackProductDetailModal/NavistackProductDetailModal.component";
import VendorInfoModalV2 from "../VendorInfoModalV2/vendorInfoModalV2.component";
import ProductClassificationItem from "./ProductClassificationItem.component";
import SponsoredVendorsClassificationItem from "./SponsoredVendorsClassificationItem.component";
import styles from "./mspProductClassification.module.sass";
import {USER_CONTENT_STATUS_APPROVED} from "../../../Interfaces/statusScope.interface";
import useWhitelabeling from "../../../hooks/fetches/useWhitelabeling";
import {lighten} from "@mui/material";

interface IProps {
    className?: string;
    companyId?: string;
    companyFriendlyUrl?: string;
    onClickAddToStack?: Function;
    numOfCategoriesFilledCallback?: (num: number) => void;
    sponsoredNaviStackVendors?: INaviStackAdvertisement[];
    searchState?: [string, Dispatch<SetStateAction<string>>];
    filterState?: [INewFilters | undefined, (filters: INewFilters | undefined) => void];
    readOnly?: boolean;
    setShowPartnersFor?: (company_id: string) => void;
    isBrandStack?: boolean;
    brandStackAdoptionDetail?: IBrandStackAdoptionDetailItem[];
    openBrandStackManageCategories?: () => void;
    customerStack?: boolean;
    companyType?: string;
    onClickAddCategory?: () => void;
    onClickProductDetails?: (stack: IStackCategorization) => void;
    naviStack?: UseQueryResult<IStackCategorization[] | undefined, unknown>;
    naviStackCategories?: UseQueryResult<ICategory[] | undefined, unknown>;
    numOfCategoriesFilled?: number;
    simplifiedInfo?: boolean;
}

const MSPProductClassification = (props: IProps) => {
    const [loading, setLoading] = useState(false);
    const [allCategories, setAllCategories] = useState<Array<ICategory>>();
    const [removingFrom, setRemovingFrom] = useState<any>({});
    const [isHovered, setIsHovered] = useState<string>("");
    const [showMoreProductTooltip, setShowMoreProductTooltip] = useState<{show: boolean; id: string}>({
        show: false,
        id: "",
    });
    const [contactVendorDialogProps, setContactvendorDialogProps] = useState<{
        open: boolean;
        companyName: string;
        friendlyUrl: string;
    }>({
        open: false,
        friendlyUrl: "",
        companyName: "",
    });
    const [productDetailsDialogProps, setProductDetailsDialogProps] = useState<{
        open: boolean;
        productDetails: IStackCategorization | undefined;
        friendlyUrl: string;
    }>({
        open: false,
        productDetails: undefined,
        friendlyUrl: "",
    });

    const [expandProductId, setExpandProductId] = useState<string>("");
    let timer;
    const handleShow = (id: string) => {
        clearTimeout(timer);
        setShowMoreProductTooltip({show: true, id});
    };
    const handleHide = () => {
        timer = setTimeout(() => {
            setShowMoreProductTooltip({show: false, id: ""});
            setExpandProductId("");
        }, 500);
    };
    const [showEditPartnership, setShowEditPartnership] = useState<{show: boolean; stack: IStackCategorization}>({
        show: false,
        stack: {} as IStackCategorization,
    });
    const [companyInfoModal, setCompanyInfoModal] = useState({
        open: false,
        isDistributor: false,
        friendly_url: "",
        advertisement_id: "",
    });
    const {updateSettings} = useSettings();
    const {activeCompany} = useActiveCompany();
    const isViewingAsAffiliateParent = useIsViewingAsAffiliateParent();
    const isAffiliateMSP: boolean =
        !isViewingAsAffiliateParent &&
        !!activeCompany?.parent?.id &&
        (activeCompany.company_type === CompanyType.FRANCHISE_MSP ||
            activeCompany?.company_type === CompanyType.MSP_LOCATION);
    const {
        naviStack: hookNaviStack,
        naviStackCategories: hookNaviStackCategories,
        numOfCategoriesFilled: hookNumOfCategoriesFilled,
        updateNaviStack,
        missingAdoptionStack,
    } = useNaviStack(props.companyId || "", {
        isCompany: true,
        isMSP: activeCompany?.type_is_of_vendor === false,
        loadRecommendedStack: !!props.isBrandStack,
        customerStack: props.customerStack,
        companyType: props.companyType ? props.companyType : undefined,
        calls: {
            all: false,
            missingAdoptionStack: !props.isBrandStack && (isAffiliateMSP || isViewingAsAffiliateParent),
            naviStack: true,
            naviStackCategories: true,
        },
    });
    const search = props.searchState?.[0];
    const naviStack = props.naviStack || hookNaviStack;
    const naviStackCategories = (props.naviStackCategories || hookNaviStackCategories)?.data;
    const numOfCategoriesFilled = props.numOfCategoriesFilled || hookNumOfCategoriesFilled;
    const {naviStack: parentBrandStack} = useNaviStack(
        isViewingAsAffiliateParent && activeCompany?.id ? activeCompany.id : "",
        {
            isCompany: true,
            isMSP: true,
            loadRecommendedStack: true,
            calls: {
                all: false,
                naviStack: isViewingAsAffiliateParent,
            },
        },
    );
    const {mspBrandStackAdoptionDetail} = useMspAffiliates(
        props.isBrandStack || isViewingAsAffiliateParent ? activeCompany?.friendly_url : "",
        {
            calls: {all: false, mspBrandStackAdoptionDetail: true},
        },
    );
    const stackTypeLabel = getStackTypeLabel(activeCompany?.company_type !== CompanyType.FRANCHISE_CORPORATE_MSP);
    const rawMyStack = naviStack.data ?? [];
    const myStack =
        search && !props.naviStack
            ? fuzzySearch(search, rawMyStack, ["category.name", "product.name", "stack_company.name"] as any).results
            : rawMyStack;
    const parentsIdsInStack = myStack.map(stack => stack.parent_category_id);
    const parentsInStack = allCategories?.filter(c => parentsIdsInStack.includes(c.id)) || [];
    const defaultCategories = props.isBrandStack ? [] : naviStackCategories || [];
    const {sponsoredNaviStackVendors: sponsoredVendors} = props;
    const sponsoredNaviStackVendors = props.isBrandStack ? [] : sponsoredVendors || [];
    const theme = useTheme();
    const notify = useNotification();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([
        PERMISSION_GROUPS.MSP_CLIENT_PRODUCTS_UPDATE,
        props.isBrandStack ? PERMISSION_GROUPS.BRAND_STACK_UPDATE : PERMISSION_GROUPS.MANAGE_STACK_UPDATE,
    ]);
    const allMyStackCategoryIds: string[] = [];
    if (rawMyStack) {
        rawMyStack?.forEach(stack => {
            if (stack.category.parent_id) {
                allMyStackCategoryIds.push(stack.category.parent_id);
            }
            allMyStackCategoryIds.push(stack.category_id);
        });
    }
    const [myStackProductsIDs, setMyStackProductsIDs] = useState<string[]>([]);
    const brandStackAdoption = {};
    if (props.isBrandStack && props.brandStackAdoptionDetail) {
        props.brandStackAdoptionDetail?.forEach(parentCategoryItem => {
            brandStackAdoption[parentCategoryItem.category_id] = parentCategoryItem;
            parentCategoryItem.sub_categories?.forEach(subCategoryItem => {
                brandStackAdoption[subCategoryItem.sub_category_id] = subCategoryItem;
            });
        });
    }
    const rawParentCategories = allCategories?.filter(c => !c.parent_id) || [];
    const searchedParentCategories = useMemo(() => {
        return search
            ? getUniqueItemsByProperties(
                  [...fuzzySearch(search, rawParentCategories || [], ["name"]).results, ...parentsInStack],
                  "id",
              ).sort((a, b) => {
                  return a.name.localeCompare(b.name);
              })
            : rawParentCategories;
    }, [search, rawParentCategories, parentsInStack]);

    const {whitelabeling} = useWhitelabeling();

    useEffect(() => {
        if (naviStack.isLoading) return;
        setMyStackProductsIDs(rawMyStack?.flatMap(item => item.product).map(product => product?.id || "") || []);
        if (productDetailsDialogProps.open) {
            const selectedProduct = rawMyStack.filter(
                item => item.product_id === productDetailsDialogProps.productDetails?.product_id,
            );
            if (selectedProduct?.length) {
                setProductDetailsDialogProps({
                    open: true,
                    productDetails: selectedProduct[0],
                    friendlyUrl: selectedProduct[0]?.stack_company.friendly_url,
                });
            }
        }
    }, [rawMyStack]);

    const checkIfProductIsRecommendedBrandStackItem = (stack: IStackCategorization) => {
        if (!missingAdoptionStack.data) return false;
        let isRecommended = false;
        missingAdoptionStack.data?.items?.forEach(item => {
            if (item.products?.length) {
                item.products?.forEach(product => {
                    if (product.id === stack.product_id) {
                        isRecommended = true;
                    }
                });
            }
        });
        return isRecommended;
    };

    const handleRemoveFromStack = async (stack: IStackCategorization) => {
        const isDeletingItemFromRecommendedBrandStack = checkIfProductIsRecommendedBrandStackItem(stack);
        const answer = await getUserConfirmation("Remove Product?", {
            mui: true,
            modalTheme: "error",
            title: "Remove Product?",
            form: isDeletingItemFromRecommendedBrandStack ? (
                <Box display="flex" flexDirection="column" gap={2} justifyContent="flex-start" alignItems="flex-start">
                    <Typography variant="body3" fontWeight={600}>
                        This is a product from your {stackTypeLabel} Stack
                    </Typography>
                    <Typography variant="body3">
                        Are you sure you would like to remove this product from your Stack anyway? Information about
                        usage is always available to Corporation administrators.
                    </Typography>
                </Box>
            ) : (
                <Typography variant="body4" textAlign="center">
                    Are you sure you want to remove{" "}
                    <strong>
                        {stack.stack_company?.name} {stack.product?.name + " "}
                    </strong>
                    from your technology stack?
                </Typography>
            ),
            okButtonText: "REMOVE",
            customModalBtnTracking: generateActivityLog(stack, "stack", props.companyId),
        });
        if (!answer.value) return false;
        setRemovingFrom({...removingFrom, [stack.id]: true});
        updateNaviStack.mutate({
            mutate_type: MutateTypes.RemoveFromStack,
            subject_id: props.companyId || "",
            stack_id: stack.id,
            onSuccess: () => onSuccessRemove(stack),
            onError: handleError,
        });
    };

    const handleRemoveFromStackDialogEvent = async (product_id: string) => {
        const stackIndex = rawMyStack.findIndex(stack => stack.product_id === product_id);
        const response = await handleRemoveFromStack(rawMyStack[stackIndex]);
        if (!response) return;
        setMyStackProductsIDs(prev => prev.filter(id => id !== product_id));
    };

    const onSuccessRemove = (stack: IStackCategorization) => {
        setRemovingFrom({...removingFrom, [stack.id]: false});
        notify(`${stack.product?.name} has been successfully been removed from your stack.`, "Success");
        naviStack.refetch();
    };

    const onSuccessCategories = (res: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {all_categories: res.data?.filter((a: ICategory) => !a.is_hidden)});
        setAllCategories(res.data);
        setLoading(false);
    };

    const handleError = (err: AxiosError) => {
        notify(getErrorFromArray(err), "Error");
        setLoading(false);
    };

    const openCompanyInfoModal = useCallback(
        (vendor: IAvailableVendor | IStackCompany, advertisementId: string) => {
            setCompanyInfoModal({
                open: true,
                isDistributor: !!vendor?.is_distributor,
                friendly_url: vendor?.friendly_url,
                advertisement_id: advertisementId,
            });
        },
        [setCompanyInfoModal],
    );

    const closeCompanyInfoModal = () => {
        setCompanyInfoModal({
            ...companyInfoModal,
            open: false,
            isDistributor: false,
        });
    };

    const openContactVendorDialog = useCallback(
        (companyName: string, friendlyUrl: string) => {
            setContactvendorDialogProps({
                open: true,
                friendlyUrl,
                companyName,
            });
        },
        [setContactvendorDialogProps],
    );

    const closeContactVendorDialog = () => {
        setContactvendorDialogProps({
            open: false,
            friendlyUrl: "",
            companyName: "",
        });
    };

    const openProductDetailDialog = useCallback(
        (stack: IStackCategorization) => {
            props.customerStack
                ? props.onClickProductDetails?.(stack)
                : setProductDetailsDialogProps({
                      open: true,
                      productDetails: stack,
                      friendlyUrl: stack.stack_company.friendly_url,
                  });
        },
        [setProductDetailsDialogProps],
    );

    const closeProductDetailsModal = () => {
        setProductDetailsDialogProps({
            open: false,
            friendlyUrl: "",
            productDetails: undefined,
        });
    };

    const renderCountBadge = (count?: number | null) => {
        if (!count) return null;
        return (
            <Box data-show-when-collapsed="true" sx={{height: "21px", display: "flex"}}>
                <Badge color="secondary">{count}</Badge>
            </Box>
        );
    };

    useEffect(() => {
        props.numOfCategoriesFilledCallback &&
            typeof numOfCategoriesFilled === "number" &&
            props.numOfCategoriesFilledCallback(numOfCategoriesFilled);
    }, [numOfCategoriesFilled]);

    useEffect(() => {
        if (props.companyId && !loading && !allCategories?.length) {
            setLoading(true);
            categoriesService.getAllCategories(
                onSuccessCategories,
                handleError,
                "",
                1,
                undefined,
                undefined,
                undefined,
                props.companyId,
            );
        }
    }, [props.companyId]);

    if (
        !naviStack.isRefetching &&
        (naviStack?.isLoading || naviStack.isFetching || loading || !allCategories?.length)
    ) {
        return (
            <Box display="grid" gridTemplateColumns={"repeat(3, 1fr)"} gap={2}>
                {Array(12)
                    .fill(null)
                    .map((_, i) => (
                        <Skeleton key={i} height={200} />
                    ))}
            </Box>
        );
    }

    return (
        <>
            <Box display="flex" flexWrap="wrap" gap={2}>
                {isViewingAsAffiliateParent && <AffiliateParentStackLegend showOnMobile />}
                {props.isBrandStack && !mspBrandStackAdoptionDetail.data?.length ? (
                    <Box gridColumn="span 2" display="flex" alignItems="center" justifyContent="center" paddingX={3}>
                        <Typography textAlign="center" variant="body4" color="#000" fontInter>
                            {stackTypeLabel} Stack enables you to make category, subcategory, and product
                            recommendations to your affiliate locations. Click{" "}
                            <strong
                                style={{cursor: "pointer", color: theme.palette.blue[600]}}
                                onClick={() => {
                                    props.openBrandStackManageCategories?.();
                                }}>
                                Manage {stackTypeLabel} Stack
                            </strong>{" "}
                            to add your recommendations.
                        </Typography>
                    </Box>
                ) : (
                    searchedParentCategories.map((parentCategory: ICategory) => {
                        const foundDefaultParent = defaultCategories.find(c => c.id === parentCategory?.id);
                        const foundBrandStack = brandStackAdoption[parentCategory?.id || ""];
                        const foundAllParent = sponsoredNaviStackVendors?.filter(
                            s => s.category_id === parentCategory?.id || s.category?.parent_id === parentCategory?.id,
                        );
                        const foundSponsoredParent = foundAllParent?.filter(s => s.category_id === parentCategory?.id);
                        const isParentCategoryOnParentBrandStack = !!mspBrandStackAdoptionDetail.data?.find(item => {
                            return item.category_id === parentCategory?.id;
                        });
                        const missingBrandStackSubcategories = missingAdoptionStack?.data?.items?.filter(
                            c => c.sub_category.parent_id === parentCategory?.id,
                        );
                        const shouldShow =
                            allMyStackCategoryIds.includes(parentCategory?.id) ||
                            foundDefaultParent ||
                            foundAllParent?.length ||
                            foundBrandStack ||
                            missingBrandStackSubcategories?.length;
                        if (!shouldShow) return null;
                        const sub_categories = allCategories?.filter(
                            c =>
                                c.parent_id === parentCategory.id &&
                                (allMyStackCategoryIds.includes(c.id) ||
                                    (foundDefaultParent
                                        ? foundDefaultParent["sub-categories"]?.find(sub => sub.id === c.id)
                                        : false) ||
                                    sponsoredNaviStackVendors?.find(s => s.category_id === c.id) ||
                                    brandStackAdoption[c.id] ||
                                    missingBrandStackSubcategories?.find(
                                        subcategoryItem => subcategoryItem.sub_category.id === c.id,
                                    )),
                        );
                        const count = myStack.filter(s => s.parent_category_id === parentCategory.id).length;
                        return (
                            <Box
                                data-hide-when-collapsed={!count}
                                key={parentCategory.id}
                                sx={{
                                    border: `1px solid ${theme.palette.neutral[400]}`,
                                    borderRadius: "8px",
                                    padding: "8px",
                                    display: "flex",
                                    flexDirection: "column",
                                    gap: "8px",
                                    flexGrow: 1,
                                    minWidth: "calc(50% - 8px)",
                                    background: props.customerStack ? theme.palette.neutral[100] : undefined,
                                }}>
                                <Box display="flex" gap="4px" alignItems="center">
                                    <Link
                                        target="_blank"
                                        title="Click to view products in this category."
                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                            ":friendly_url",
                                            parentCategory?.friendly_url || "",
                                        )}>
                                        <Box display="flex" flexDirection="row" alignItems="center" gap="8px">
                                            <FiberManualRecordIcon
                                                htmlColor={parentCategory.color}
                                                sx={{width: "16px", height: "16px"}}
                                            />
                                            <Typography
                                                data-shrink-when-collapsed="true"
                                                color={theme.palette.neutral[700]}
                                                variant="body3"
                                                fontWeight="500">
                                                {parentCategory.name}
                                            </Typography>
                                            {renderCountBadge(count)}
                                            {isViewingAsAffiliateParent && (
                                                <Box>
                                                    {missingBrandStackSubcategories?.length
                                                        ? ADOPTION_STATUS_BADGE[ADOPTION_STATUS.MISSING]
                                                        : isParentCategoryOnParentBrandStack
                                                          ? ADOPTION_STATUS_BADGE[ADOPTION_STATUS.ADDED_TO_STACK]
                                                          : null}
                                                </Box>
                                            )}
                                            {!!props.isBrandStack && brandStackAdoption[parentCategory.id] && (
                                                <AffiliateBrandStackAdoptionWidget
                                                    item={brandStackAdoption[parentCategory.id]}>
                                                    <Typography
                                                        variant="body5"
                                                        fontWeight="800"
                                                        sx={{color: theme.palette.blue[600]}}>
                                                        {
                                                            brandStackAdoption[parentCategory.id]
                                                                ?.affiliates_using_it_count
                                                        }
                                                        /{brandStackAdoption[parentCategory.id]?.affiliates_count}
                                                    </Typography>
                                                </AffiliateBrandStackAdoptionWidget>
                                            )}
                                        </Box>
                                    </Link>
                                    {foundSponsoredParent?.length && !props.readOnly ? (
                                        <SponsoredVendorsClassificationItem
                                            sponsoredVendors={foundSponsoredParent}
                                            variant="icon"
                                            onClickAddToStack={props.onClickAddToStack as Function}
                                            onClickVendor={(v: any) => {
                                                openCompanyInfoModal(v.subject, v.id);
                                            }}
                                            category_id={parentCategory.id}
                                            msp_id={props.companyId}
                                        />
                                    ) : null}
                                </Box>
                                <Divider
                                    data-hide-when-collapsed="true"
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                        alignItems: "start",
                                        alignSelf: "stretch",
                                        color: theme.palette.neutral[500],
                                    }}
                                    color="black"
                                    variant="fullWidth"
                                />
                                <Box
                                    data-hide-when-collapsed="true"
                                    display="flex"
                                    flexWrap="wrap"
                                    alignItems="center"
                                    gap="8px"
                                    width="100%"
                                    justifyContent="center"
                                    flexGrow="1">
                                    {sub_categories?.length ? (
                                        sub_categories.map(sub_category => {
                                            const foundMissingParentStackSubcategoryProductIds =
                                                missingBrandStackSubcategories
                                                    ?.find(item => item.sub_category.id === sub_category.id)
                                                    ?.products?.map(p => p.id) || [];
                                            const isMissingSubcategoryFromBrandStack =
                                                !!missingBrandStackSubcategories?.find(
                                                    c => c.sub_category?.id === sub_category.id,
                                                );
                                            const missingBrandStackProducts = parentBrandStack?.data
                                                ?.filter(
                                                    parentStackItem =>
                                                        parentStackItem.category_id === sub_category.id &&
                                                        foundMissingParentStackSubcategoryProductIds.includes(
                                                            parentStackItem.product_id,
                                                        ),
                                                )
                                                ?.map(item => ({...item, isParentBrandStackItem: true}));
                                            const filteredMyStack = [
                                                ...(myStack || []),
                                                ...(missingBrandStackProducts || []),
                                            ].filter(s => s.category_id === sub_category.id);
                                            const foundSponsoredSubCategory = sponsoredNaviStackVendors?.filter(
                                                s => s.category_id === sub_category?.id,
                                            );
                                            const shouldShowSponsoredBanner = !!foundSponsoredSubCategory?.length;
                                            const maxStackSize = props.customerStack
                                                ? filteredMyStack?.length <= 2
                                                    ? 2
                                                    : 1
                                                : 3;
                                            return (
                                                <Box
                                                    flex="1"
                                                    onMouseEnter={() =>
                                                        props.readOnly ? void 0 : setIsHovered(sub_category.id)
                                                    }
                                                    onMouseLeave={() => (props.readOnly ? void 0 : setIsHovered(""))}
                                                    key={sub_category?.id}
                                                    title={!props.readOnly && hasEditAccess ? addToStack : ""}
                                                    minWidth="calc(33.3% - 6px)"
                                                    minHeight={85}
                                                    padding={2}
                                                    borderRadius="8px"
                                                    display="flex"
                                                    flexDirection="column"
                                                    justifyContent="center"
                                                    position="relative"
                                                    overflow="hidden"
                                                    gap={props.customerStack ? "8px" : "16px"}
                                                    border={
                                                        filteredMyStack.length > 0
                                                            ? `1px solid ${theme.palette.blue[300]}`
                                                            : `1px solid ${theme.palette.neutral[300]}`
                                                    }
                                                    bgcolor={
                                                        filteredMyStack.length > 0
                                                            ? theme.palette.blue[100]
                                                            : theme.palette.neutral[250]
                                                    }
                                                    alignItems="center"
                                                    alignSelf="stretch"
                                                    sx={{
                                                        cursor:
                                                            props.readOnly || !hasEditAccess ? "default" : "pointer",
                                                        ":hover":
                                                            props.readOnly || !hasEditAccess
                                                                ? undefined
                                                                : {
                                                                      bgcolor: whitelabeling?.accent_color
                                                                          ? lighten(whitelabeling?.accent_color, 0.9)
                                                                          : theme.palette.primary[100],
                                                                      border: `1px solid ${
                                                                          whitelabeling?.accent_color
                                                                              ? lighten(
                                                                                    whitelabeling?.accent_color,
                                                                                    0.5,
                                                                                )
                                                                              : theme.palette.primary[300]
                                                                      }`,
                                                                  },
                                                    }}
                                                    onClick={e => {
                                                        if (!hasEditAccess) return;
                                                        if (
                                                            typeof (e.target as any) === "string" &&
                                                            (e.target as any)
                                                                ?.className((e.target as any)?.className || "")
                                                                ?.includes("MuiTooltip")
                                                        )
                                                            return;

                                                        props.onClickAddToStack &&
                                                            props.onClickAddToStack(
                                                                sub_category?.id,
                                                                undefined,
                                                                shouldShowSponsoredBanner,
                                                            );
                                                    }}>
                                                    {props.customerStack && (
                                                        <Typography
                                                            variant="body5"
                                                            fontWeight="500"
                                                            sx={{
                                                                color: theme.palette.blue[800],
                                                            }}
                                                            textAlign="center">
                                                            {sub_category.name}
                                                        </Typography>
                                                    )}
                                                    <Box
                                                        display={!filteredMyStack?.length ? "none" : "flex"}
                                                        alignItems="center"
                                                        justifyContent="center"
                                                        flexDirection={props.customerStack ? "column" : "row"}
                                                        gap="4px">
                                                        {filteredMyStack
                                                            .slice(0, maxStackSize)
                                                            .map((stack: IStackCategorization) => (
                                                                <ProductClassificationItem
                                                                    key={stack.id}
                                                                    stack={stack}
                                                                    hasEditAccess={hasEditAccess}
                                                                    handleRemoveFromStack={handleRemoveFromStack}
                                                                    loading={removingFrom[stack.id] === true}
                                                                    handleEditPartnerShipType={() =>
                                                                        setShowEditPartnership({
                                                                            show: true,
                                                                            stack: stack,
                                                                        })
                                                                    }
                                                                    handleViewProfile={() => {
                                                                        openCompanyInfoModal(stack?.stack_company, "");
                                                                    }}
                                                                    handleViewProductDetails={() => {
                                                                        openProductDetailDialog(stack);
                                                                    }}
                                                                    readOnly={props.readOnly}
                                                                    isBrandStack={props.isBrandStack}
                                                                    customerStack={props.customerStack}
                                                                    simplifiedInfo={props.simplifiedInfo}
                                                                />
                                                            ))}
                                                        {filteredMyStack.length > maxStackSize && (
                                                            <OverlayTrigger
                                                                key={"overlay" + sub_category.id}
                                                                trigger={["hover", "focus"]}
                                                                placement="bottom"
                                                                show={
                                                                    loading
                                                                        ? true
                                                                        : showMoreProductTooltip?.show &&
                                                                          showMoreProductTooltip.id === sub_category.id
                                                                }
                                                                popperConfig={{
                                                                    modifiers: [
                                                                        {
                                                                            name: "preventOverflow",
                                                                            options: {
                                                                                enabled: true,
                                                                                boundariesElement: "scrollParent",
                                                                            },
                                                                        },
                                                                    ],
                                                                }}
                                                                overlay={overlayProps => (
                                                                    <Popover
                                                                        id={`popover_${sub_category.id}`}
                                                                        {...overlayProps}
                                                                        className="shadow border-0 rounded mw-100"
                                                                        onClick={e => {
                                                                            e.preventDefault();
                                                                            e.stopPropagation();
                                                                        }}
                                                                        onMouseEnter={() => handleShow(sub_category.id)}
                                                                        onMouseLeave={handleHide}>
                                                                        <Popover.Body>
                                                                            <Paper
                                                                                elevation={3}
                                                                                sx={{
                                                                                    display: "flex",
                                                                                    flexDirection: "column",
                                                                                    gap: "12px",
                                                                                    padding: "16px",
                                                                                    minWidth: "300px",
                                                                                    maxWidth: "500px",
                                                                                    maxHeight: "440px",
                                                                                    overflowY: "auto",
                                                                                }}
                                                                                className={`${styles.stackedProductTooltip}`}>
                                                                                {filteredMyStack
                                                                                    .slice(
                                                                                        maxStackSize,
                                                                                        filteredMyStack.length,
                                                                                    )
                                                                                    .map((stack, index) => {
                                                                                        return (
                                                                                            <>
                                                                                                <ProductClassificationTooltipContent
                                                                                                    stack={stack}
                                                                                                    handleExpandProduct={(
                                                                                                        id: string,
                                                                                                    ) =>
                                                                                                        setExpandProductId(
                                                                                                            id,
                                                                                                        )
                                                                                                    }
                                                                                                    minimized={
                                                                                                        expandProductId ===
                                                                                                        stack.id
                                                                                                            ? false
                                                                                                            : true
                                                                                                    }
                                                                                                    handleRemoveFromStack={
                                                                                                        handleRemoveFromStack
                                                                                                    }
                                                                                                    handleViewProfile={() => {
                                                                                                        openCompanyInfoModal(
                                                                                                            stack?.stack_company,
                                                                                                            "",
                                                                                                        );
                                                                                                    }}
                                                                                                    handleViewProductDetails={() => {
                                                                                                        openProductDetailDialog(
                                                                                                            stack,
                                                                                                        );
                                                                                                    }}
                                                                                                    hasEditAccess={
                                                                                                        hasEditAccess
                                                                                                    }
                                                                                                    handleEditPartnerShipType={() =>
                                                                                                        setShowEditPartnership(
                                                                                                            {
                                                                                                                show: true,
                                                                                                                stack: stack,
                                                                                                            },
                                                                                                        )
                                                                                                    }
                                                                                                    setShow={() =>
                                                                                                        setShowMoreProductTooltip(
                                                                                                            {
                                                                                                                show: false,
                                                                                                                id: "",
                                                                                                            },
                                                                                                        )
                                                                                                    }
                                                                                                    isExpanded={
                                                                                                        expandProductId
                                                                                                    }
                                                                                                    readOnly={
                                                                                                        props.readOnly
                                                                                                    }
                                                                                                    customerStack={
                                                                                                        props.customerStack
                                                                                                    }
                                                                                                    simplifiedInfo={
                                                                                                        props.simplifiedInfo ||
                                                                                                        props.customerStack
                                                                                                    }
                                                                                                />
                                                                                                {index +
                                                                                                    (maxStackSize +
                                                                                                        1) !==
                                                                                                    filteredMyStack.length && (
                                                                                                    <Divider
                                                                                                        sx={{
                                                                                                            borderSize:
                                                                                                                "5px",
                                                                                                            margin: "8px 0px",
                                                                                                        }}
                                                                                                        color={
                                                                                                            theme
                                                                                                                .palette
                                                                                                                .neutral[500]
                                                                                                        }
                                                                                                        variant="fullWidth"
                                                                                                    />
                                                                                                )}
                                                                                            </>
                                                                                        );
                                                                                    })}
                                                                            </Paper>
                                                                        </Popover.Body>
                                                                    </Popover>
                                                                )}>
                                                                <Box
                                                                    onMouseEnter={() => handleShow(sub_category.id)}
                                                                    sx={{
                                                                        marginLeft: "-8px",
                                                                        zIndex: "1",
                                                                        cursor: "default",
                                                                    }}
                                                                    onMouseLeave={handleHide}>
                                                                    {props.customerStack ? (
                                                                        <Typography
                                                                            size={12}
                                                                            weight={700}
                                                                            color="blue.600">
                                                                            +{filteredMyStack.length - maxStackSize}
                                                                        </Typography>
                                                                    ) : (
                                                                        <Badge
                                                                            style={{
                                                                                background: theme.palette.blue[800],
                                                                            }}
                                                                            color="tonal"
                                                                            padding="2px 6px">
                                                                            +{filteredMyStack.length - maxStackSize}
                                                                        </Badge>
                                                                    )}
                                                                </Box>
                                                            </OverlayTrigger>
                                                        )}
                                                    </Box>
                                                    {!props.customerStack && (
                                                        <Box
                                                            sx={{
                                                                display: "flex",
                                                                flexDirection: "column",
                                                                alignItems: "center",
                                                                justifyContent: "center",
                                                                gap: "8px",
                                                            }}>
                                                            {shouldShowSponsoredBanner && !props.readOnly ? (
                                                                <SponsoredVendorsClassificationItem
                                                                    sponsoredVendors={foundSponsoredSubCategory}
                                                                    variant="icon"
                                                                    onClickAddToStack={
                                                                        props.onClickAddToStack as Function
                                                                    }
                                                                    onClickVendor={(v: any) => {
                                                                        openCompanyInfoModal(v.subject, v.id);
                                                                    }}
                                                                    category_id={parentCategory.id}
                                                                    sx={{
                                                                        position: "absolute",
                                                                        top: "4px",
                                                                        right: "3.667px",
                                                                    }}
                                                                    msp_id={props.companyId}
                                                                />
                                                            ) : null}
                                                            {isViewingAsAffiliateParent &&
                                                                isMissingSubcategoryFromBrandStack &&
                                                                ADOPTION_STATUS_BADGE[ADOPTION_STATUS.MISSING]}
                                                            {!filteredMyStack?.length &&
                                                                hasEditAccess &&
                                                                isHovered === sub_category.id && (
                                                                    <AddIcon
                                                                        sx={{
                                                                            fontSize: "32px",
                                                                            width: "32px",
                                                                            height: "32px",
                                                                        }}
                                                                    />
                                                                )}

                                                            <Typography
                                                                variant="body5"
                                                                fontWeight="500"
                                                                sx={{
                                                                    color: theme.palette.blue[800],
                                                                }}
                                                                textAlign="center">
                                                                {sub_category.name}
                                                            </Typography>
                                                            {!!props.isBrandStack &&
                                                                brandStackAdoption[sub_category.id] && (
                                                                    <AffiliateBrandStackAdoptionWidget
                                                                        item={brandStackAdoption[sub_category.id]}>
                                                                        <Typography
                                                                            variant="body5"
                                                                            fontWeight="800"
                                                                            sx={{color: theme.palette.blue[600]}}
                                                                            textAlign="center">
                                                                            {
                                                                                brandStackAdoption[sub_category.id]
                                                                                    ?.affiliates_using_it_count
                                                                            }
                                                                            /
                                                                            {
                                                                                brandStackAdoption[sub_category.id]
                                                                                    ?.affiliates_count
                                                                            }
                                                                        </Typography>
                                                                    </AffiliateBrandStackAdoptionWidget>
                                                                )}
                                                        </Box>
                                                    )}
                                                </Box>
                                            );
                                        })
                                    ) : (
                                        <SponsoredVendorsClassificationItem
                                            sponsoredVendors={foundAllParent}
                                            onClickAddToStack={props.onClickAddToStack as Function}
                                            onClickVendor={(v: any) => {
                                                openCompanyInfoModal(v.subject, v.id);
                                            }}
                                            category_id={parentCategory.id}
                                            msp_id={props.companyId}
                                        />
                                    )}
                                </Box>
                            </Box>
                        );
                    })
                )}
            </Box>
            {(isViewingAsAffiliateParent || (!props.readOnly && hasEditAccess && !props.customerStack)) && (
                <Divider sx={{borderSize: "5px"}} color={theme.palette.neutral[500]} variant="fullWidth" />
            )}
            {isViewingAsAffiliateParent && <AffiliateParentStackLegend />}
            {!props.readOnly && hasEditAccess && !props.customerStack && (
                <Button
                    id="newCategory"
                    onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (props.isBrandStack) {
                            props.openBrandStackManageCategories?.();
                            return;
                        }
                        if (props.customerStack) {
                            props.onClickAddCategory?.();
                            return;
                        }
                        props.onClickAddToStack && props.onClickAddToStack();
                    }}
                    variant="text"
                    color="secondary"
                    sx={{
                        bgcolor: props.customerStack ? theme.palette.blue[100] : undefined,
                        letterSpacing: "0",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        cursor: "pointer",
                        fontWeight: "700!important",
                        fontSize: "14px!important",
                        color: theme.palette.secondary[600],
                        gap: "16px",
                        margin: "0 auto",
                        width: props.customerStack ? "100%" : undefined,
                        borderRadius: props.customerStack ? "0" : undefined,
                    }}>
                    <AddIcon style={{fontSize: "24px"}} /> New Category
                </Button>
            )}
            <VendorInfoModalV2
                open={!!companyInfoModal?.friendly_url && companyInfoModal?.open}
                onClose={() => {
                    closeCompanyInfoModal();
                }}
                isDistributor={companyInfoModal.isDistributor}
                companyFriendlyUrl={companyInfoModal?.friendly_url}
                onClickAddToStack={props.onClickAddToStack}
                onClickRemoveFromStack={handleRemoveFromStackDialogEvent}
                onClickContactVendor={openContactVendorDialog}
                advertisementId={companyInfoModal?.advertisement_id}
                myStackProductsIDs={myStackProductsIDs}
            />
            <NaviStackProductDetailsModal
                open={!!productDetailsDialogProps?.friendlyUrl && productDetailsDialogProps?.open}
                onClose={closeProductDetailsModal}
                companyFriendlyUrl={productDetailsDialogProps?.friendlyUrl}
                onClickRemoveFromStack={stack => {
                    handleRemoveFromStack(stack);
                    closeProductDetailsModal();
                }}
                onClickContactVendor={openContactVendorDialog}
                myStackProductsIDs={myStackProductsIDs}
                stack={productDetailsDialogProps.productDetails}
                handleEditPartnerShipType={stack =>
                    setShowEditPartnership({
                        show: true,
                        stack: stack,
                    })
                }
            />
            <ContactVendorDialog
                friendly_url={contactVendorDialogProps?.friendlyUrl}
                companyName={contactVendorDialogProps?.companyName}
                open={contactVendorDialogProps?.open}
                onClose={() => {
                    closeContactVendorDialog();
                }}
                customUri={window.location.href}
                hideToggleBtn
            />

            <ChangePartnershipTypeModal
                stack={showEditPartnership.stack}
                open={showEditPartnership.show}
                onClose={() => setShowEditPartnership({show: false, stack: {} as IStackCategorization})}
                companyId={props?.companyId ?? ""}
                onSave={naviStack.refetch}
                isBrandStack={!!props.isBrandStack}
            />
        </>
    );
};

interface IStackCategorizationWithParentFlag extends IStackCategorization {
    isParentBrandStackItem?: boolean;
}
export const ProductClassificationTooltipContent = ({
    stack,
    handleRemoveFromStack,
    handleViewProfile,
    hasEditAccess,
    handleEditPartnerShipType,
    setShow,
    handleExpandProduct,
    handleViewProductDetails,
    minimized = false,
    isExpanded = "",
    readOnly,
    isBrandStack,
    customerStack,
    simplifiedInfo,
}: {
    stack: IStackCategorizationWithParentFlag;
    handleRemoveFromStack?: (stack: IStackCategorization) => void;
    handleViewProfile?: Function;
    handleViewProductDetails?: Function;
    hasEditAccess?: boolean;
    handleEditPartnerShipType?: (stack: IStackCategorization) => void;
    setShow?: (value: boolean) => void;
    handleExpandProduct?: (id: string) => void;
    minimized?: boolean;
    isExpanded?: string;
    readOnly?: boolean;
    isBrandStack?: boolean;
    customerStack?: boolean;
    simplifiedInfo?: boolean;
}) => {
    const hideProductDetailsBtn =
        !!stack.product.status_scope && stack.product.status_scope?.status !== USER_CONTENT_STATUS_APPROVED;
    const hideVendorDetailsBtn =
        !!stack.stack_company.status_scope && stack.stack_company.status_scope?.status !== USER_CONTENT_STATUS_APPROVED;
    const theme = useTheme();
    const trackingProperties = JSON.stringify(generateActivityLog(stack, "stack"));
    const is_current_partner = stack.partner_status === "I am a current partner";
    const isViewingAsAffiliateParent = useIsViewingAsAffiliateParent();
    const {activeCompany} = useActiveCompany();
    const {naviStack: parentBrandStack} = useNaviStack(
        isViewingAsAffiliateParent && activeCompany?.id ? activeCompany.id : "",
        {
            isCompany: true,
            isMSP: true,
            loadRecommendedStack: true,
            calls: {
                all: false,
                naviStack: isViewingAsAffiliateParent,
            },
        },
    );
    const isMissingBrandStackProduct = stack.isParentBrandStackItem;
    const isAddedFromParentStack =
        !isMissingBrandStackProduct &&
        parentBrandStack.data?.find(
            item => item.product_id === stack.product_id && item.category_id === stack.category_id,
        );
    const adoptionStatus = isMissingBrandStackProduct
        ? ADOPTION_STATUS.MISSING
        : isAddedFromParentStack
          ? ADOPTION_STATUS.ADDED_TO_STACK
          : ADOPTION_STATUS.AFFILIATE_CHOICE;

    const handleRemove = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        setShow?.(false);
        handleRemoveFromStack?.(stack);
    };

    const handleEdit = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
        e.preventDefault();
        e.stopPropagation();
        setShow?.(false);
        handleEditPartnerShipType?.(stack);
    };

    const clickViewProfile = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        setShow?.(false);
        handleViewProfile?.();
    };

    const clickViewProductDetails = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        setShow?.(false);
        handleViewProductDetails?.();
    };

    return (
        <Box display="flex" flexDirection="column" gap="8px">
            <Box display="flex" gap="8px" alignItems="center">
                <Box position="relative">
                    <AvatarComponentV2
                        isCompany
                        size={32}
                        user={{
                            avatar: stack.stack_company?.avatar || "",
                            type: "company",
                            profile_type: "",
                            friendly_url: stack.stack_company?.friendly_url,
                            name: stack.stack_company?.name,
                            is_distributor: stack.stack_company?.is_distributor,
                            is_mdf: stack.stack_company?.is_mdf,
                        }}
                        showProfileType
                        profileTypeMinimized
                        isRedirectEnabled={false}
                        sx={{
                            padding: 0,
                        }}
                    />
                    {!isBrandStack && !isViewingAsAffiliateParent && !simplifiedInfo && (
                        <Badge
                            position={{left: "50%"}}
                            style={{transform: `translate(-50%, -50%)`, zIndex: 2}}
                            color={is_current_partner ? "success" : "warning"}
                            padding="0px">
                            {is_current_partner ? (
                                <CheckIcon
                                    sx={{
                                        fill: theme.palette.success[800],
                                        width: "12px",
                                        height: "12px",
                                    }}
                                />
                            ) : (
                                <SearchIcon
                                    sx={{
                                        fill: theme.palette.primary[700],
                                        width: "12px",
                                        height: "12px",
                                    }}
                                />
                            )}
                        </Badge>
                    )}
                </Box>
                <Box display="flex" flexDirection="column" gap="4px" flex="1">
                    <Typography
                        sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            cursor: (minimized || isExpanded === stack.id) && hasEditAccess ? "pointer" : "default",
                        }}
                        fontWeight="600"
                        variant="body3"
                        onClick={e => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleExpandProduct?.(minimized && isExpanded !== stack.id ? stack.id : "");
                        }}
                        color={theme.palette.neutral[700]}>
                        {stack.stack_company?.name}
                        {isExpanded === stack.id && <ArrowDropUpOutlinedIcon sx={{padding: "0px"}} color="neutral" />}
                        {minimized && isExpanded !== stack.id && (
                            <ArrowDropDownOutlinedIcon sx={{padding: "0px"}} color="neutral" />
                        )}
                    </Typography>
                    <Typography variant="body4" color={theme.palette.neutral[700]}>
                        {stack.product?.name}
                    </Typography>
                    <Box display="flex" gap="8px" alignItems="center">
                        {stack?.stack_company?.rating && (
                            <Box display="flex" gap="2px" alignItems="center">
                                <StarOutlinedIcon
                                    sx={{fill: theme.palette.warning[600], width: "16px", height: "16px"}}
                                />
                                <Typography
                                    letterSpacing="0px"
                                    variant="subtitle3"
                                    sx={{color: theme.palette.secondary["main"]}}>
                                    {Number(stack.stack_company.rating).toFixed(1)}
                                </Typography>
                            </Box>
                        )}
                        {!isBrandStack && !isViewingAsAffiliateParent && !simplifiedInfo && !customerStack && (
                            <>
                                {stack.partner_status === "I am a current partner" ? (
                                    <Badge color="success" padding="2px 6px" sx={{zIndex: 2}}>
                                        <CheckIcon
                                            sx={{
                                                fill: theme.palette.success[800],
                                                width: "12px",
                                                height: "12px",
                                                marginRight: "8px",
                                            }}
                                        />
                                        Current Partner
                                    </Badge>
                                ) : (
                                    <Badge color="warning" padding="2px 6px" sx={{zIndex: 2}}>
                                        <SearchIcon
                                            sx={{
                                                fill: theme.palette.primary[700],
                                                width: "12px",
                                                height: "12px",
                                                marginRight: "8px",
                                            }}
                                        />
                                        Looking for information
                                    </Badge>
                                )}
                                {!minimized && hasEditAccess && !readOnly && (
                                    <Button
                                        id="editConnection"
                                        variant="text"
                                        onClick={handleEdit}
                                        title="Edit connection type"
                                        color="secondary"
                                        sx={{
                                            letterSpacing: "0",
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            cursor: "pointer",
                                            gap: "8px",
                                            color: theme.palette.secondary[500],
                                        }}>
                                        <EditOutlinedIcon style={{fontSize: "18px", width: "18px", height: "18px"}} />{" "}
                                        Edit
                                    </Button>
                                )}
                            </>
                        )}
                    </Box>
                </Box>
            </Box>
            {!minimized && (
                <Typography variant="body4" color={theme.palette.neutral[700]}>
                    {stack.category?.name}
                </Typography>
            )}
            <Box>{isViewingAsAffiliateParent && adoptionStatus && ADOPTION_STATUS_BADGE[adoptionStatus]}</Box>
            {!!stack.client_usage && !isBrandStack && !isViewingAsAffiliateParent && (
                <Typography variant="body4" color={theme.palette.blue[900]} fontInter>
                    Customers Installed:{" "}
                    <Typography variant="subtitle3" color={theme.palette.blue[600]} fontInter>
                        {stack.client_usage.installed}/{stack.client_usage?.total}
                    </Typography>
                </Typography>
            )}
            {!minimized && !readOnly && (
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2,
                        flexDirection: "column",
                        "@media (min-width: 992px)": {
                            gap: 1,
                            flexDirection: "row",
                        },
                    }}>
                    {!hideProductDetailsBtn && (
                        <Button
                            id="writeReview"
                            variant="text"
                            onClick={clickViewProductDetails}
                            color="secondary"
                            title="View product details"
                            sx={{color: theme.palette.blue[500]}}
                            data_custom_properties={trackingProperties}>
                            Product Details
                        </Button>
                    )}
                    {!customerStack && !hideVendorDetailsBtn && (
                        <Button
                            id="viewProfileModal"
                            variant="text"
                            color="secondary"
                            title="View vendor details"
                            onClick={clickViewProfile}
                            sx={{color: theme.palette.blue[500]}}
                            data_custom_properties={trackingProperties}>
                            Vendor Details
                        </Button>
                    )}
                    {customerStack ? null : !!stack.stack_company.rating ? (
                        <Button
                            sx={{color: theme.palette.error[500]}}
                            id={`removeFromStack-${stack.id}`}
                            data-custom-properties={trackingProperties}
                            data-track="true"
                            color="error"
                            disabled={!hasEditAccess}
                            onClick={handleRemove}
                            variant="text">
                            <DeleteOutlineOutlinedIcon sx={{width: "18px", height: "18px"}} />
                        </Button>
                    ) : (
                        <Button
                            title="Remove from stack"
                            id="revomeFromStack"
                            onClick={handleRemove}
                            color="error"
                            variant="text"
                            disabled={!hasEditAccess}>
                            <Typography
                                fontInter
                                sx={{
                                    display: "flex",
                                    fontSize: "14px",
                                    lineHeight: "20px",
                                    fontWeight: 600,
                                    whiteSpace: "nowrap",
                                    color: theme.palette.error[500],
                                    gap: 1,
                                }}>
                                <DeleteOutlineOutlinedIcon
                                    sx={{width: "18px", height: "18px", color: theme.palette.error[500]}}
                                />
                                {!!hideProductDetailsBtn || !!hideVendorDetailsBtn ? "Remove from Stack" : ""}
                            </Typography>{" "}
                        </Button>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default MSPProductClassification;
