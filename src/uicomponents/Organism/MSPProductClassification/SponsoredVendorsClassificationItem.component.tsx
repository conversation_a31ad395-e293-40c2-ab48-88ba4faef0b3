import AddOutlined from "@mui/icons-material/AddOutlined";
import CampaignOutlinedIcon from "@mui/icons-material/CampaignOutlined";
import StarIcon from "@mui/icons-material/Star";
import Grid from "@mui/material/Grid";
import {SxProps, Theme} from "@mui/material/styles";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useRef} from "react";
import {AnalyticAction} from "../../../constants/analitycs.constant";
import {NAVISTACK_SPONSORED_LABEL} from "../../../constants/commonStrings.constant";
import useAuthState from "../../../hooks/useAuthState";
import usePermissions from "../../../hooks/usePermissions";
import {INaviStackAdvertisement} from "../../../Interfaces/adminAdvertisements.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {analyticService} from "../../../services/analytic.service";
import {wait} from "../../../utils/async.util";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";

interface IProps {
    sponsoredVendors: INaviStackAdvertisement[];
    onClickAddToStack?: Function;
    category_id?: string;
    onClickVendor?: Function;
    variant?: "icon" | "badge" | "classification";
    sx?: SxProps<Theme>;
    msp_id?: string;
}

const MAX_VENDORS_TO_SHOW = 4;
const SponsoredVendorsClassificationItem = ({
    sponsoredVendors,
    onClickAddToStack,
    category_id,
    onClickVendor,
    variant = "classification",
    sx,
    msp_id,
}: IProps) => {
    const theme = useTheme();
    const {authState} = useAuthState();
    const alreadyStoredAnalytic = useRef({});
    const {hasPermissions} = usePermissions();
    const customerStackPermissions = [PERMISSION_GROUPS.MSP_CLIENT_PRODUCTS_UPDATE];
    const mspStackPermissions = [PERMISSION_GROUPS.MANAGE_STACK_UPDATE];
    const hasEditAccess = hasPermissions([...customerStackPermissions, ...mspStackPermissions]);

    const storeAdvertisementAnalytics = (vendorAd: INaviStackAdvertisement) => {
        analyticService.add(AnalyticAction.AD_CLICK, vendorAd?.id, {
            name: vendorAd?.name,
            advertisement_card_type_id: vendorAd?.advertisement_card_type_id,
            advertisement_card_type_label: vendorAd?.advertisement_card_type?.label,
            locations: vendorAd?.locations,
            status: vendorAd?.status,
            viewed_page_id: vendorAd?.locations?.[0]?.id,
            viewed_page_label: vendorAd?.locations?.[0]?.label,
            vendor_id: vendorAd?.subject?.id,
            vendor_name: vendorAd?.subject?.name,
            vendor_friendly_url: vendorAd?.subject?.friendly_url,
            user_id: authState?.id,
            msp_id,
        });
    };

    const storeViewAnalytics = async (onlyFirstMaxVendors: boolean) => {
        await wait(500);
        const vendorAds: INaviStackAdvertisement[] = sponsoredVendors || [];
        const filteredVendorAds = vendorAds
            ?.slice(0, onlyFirstMaxVendors ? MAX_VENDORS_TO_SHOW : vendorAds.length)
            .filter(vendorAd => !alreadyStoredAnalytic.current[vendorAd.id]);
        const analyticCalls = filteredVendorAds.map(vendorAd => {
            alreadyStoredAnalytic.current[vendorAd.id] = true;
            return {
                action: AnalyticAction.AD_VIEW,
                subject_id: vendorAd?.id,
                custom_properties: {
                    name: vendorAd?.name,
                    advertisement_card_type_id: vendorAd?.advertisement_card_type_id,
                    advertisement_card_type_label: vendorAd?.advertisement_card_type?.label,
                    locations: vendorAd?.locations,
                    status: vendorAd?.status,
                    viewed_page_id: vendorAd?.locations?.[0]?.id,
                    viewed_page_label: vendorAd?.locations?.[0]?.label,
                    vendor_id: vendorAd?.subject?.id,
                    vendor_name: vendorAd?.subject?.name,
                    vendor_friendly_url: vendorAd?.subject?.friendly_url,
                    user_id: authState?.id,
                    msp_id,
                },
            };
        });
        if (!analyticCalls.length) return;
        analyticService.storeBulk(analyticCalls);
    };

    const handleTooltipOpened = () => {
        storeViewAnalytics(false);
    };

    const SponsoredVendorsTooltip = ({children}) => (
        <CPTooltip
            light
            onOpen={handleTooltipOpened}
            sx={{
                ".MuiTooltip-tooltip": {
                    maxWidth: {xs: "100%", md: "360px !important"},
                    minWidth: {xs: "100%", md: "360px !important"},
                    background: theme.palette.primary[100],
                    border: `1px solid ${theme.palette.primary[300]}`,
                    padding: "0 !important",
                },
            }}
            title={
                <Box display="flex" flexDirection="column" gap={2} padding={2}>
                    <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                        <Box sx={{background: theme.palette.gradient.primary}} padding={0.5} borderRadius="50px">
                            <CampaignOutlinedIcon
                                sx={{width: "16px", height: "16px"}}
                                htmlColor={theme.palette.neutral[100]}
                            />
                        </Box>
                        <Typography variant="subtitle3" color={theme.palette.primary[500]} fontInter>
                            {NAVISTACK_SPONSORED_LABEL}
                        </Typography>
                    </Box>
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        gap={2}
                        flexWrap="wrap"
                        maxHeight="380px"
                        sx={{overflowY: "auto"}}>
                        {sponsoredVendors
                            .slice(variant === "classification" ? MAX_VENDORS_TO_SHOW : 0, sponsoredVendors.length)
                            ?.map(item => (
                                <Box
                                    key={item.id}
                                    display="flex"
                                    flexDirection="row"
                                    alignItems="center"
                                    gap={1}
                                    width={{xs: "100%", md: "calc(50% - 16px)"}}
                                    overflow="hidden"
                                    sx={{cursor: hasEditAccess ? "pointer" : "default"}}
                                    onClick={e => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        if (!hasEditAccess) return;
                                        storeAdvertisementAnalytics(item);
                                        onClickVendor && onClickVendor(item);
                                    }}
                                    title="View vendor details">
                                    <Box paddingBottom="4px">
                                        <AvatarComponentV2
                                            isCompany
                                            user={item.subject as any}
                                            size={48}
                                            showProfileType
                                            profileTypeMinimized
                                            disableLink
                                            style={{cursor: hasEditAccess ? "pointer" : "default"}}
                                        />
                                    </Box>
                                    <Box display="flex" flexDirection="column">
                                        <Typography
                                            variant="body4"
                                            fontInter
                                            fontWeight={500}
                                            color={theme.palette.neutral[700]}
                                            sx={{
                                                lineClamp: 2,
                                                textOverflow: "ellipsis",
                                            }}>
                                            {item.subject.name}
                                        </Typography>
                                        {item.subject?.rating && (
                                            <Typography
                                                variant="body4"
                                                fontInter
                                                fontWeight={700}
                                                color={theme.palette.neutral[700]}>
                                                <StarIcon
                                                    htmlColor={theme.palette.warning[600]}
                                                    sx={{height: 16, width: 16, marginRight: 0.5}}
                                                />
                                                {item.subject.rating?.toFixed(1) || 0}
                                            </Typography>
                                        )}
                                    </Box>
                                </Box>
                            ))}
                    </Box>
                </Box>
            }>
            {children}
        </CPTooltip>
    );

    useEffect(() => {
        if (!sponsoredVendors) return;
        if (variant === "classification") {
            storeViewAnalytics(true);
        }
    }, [sponsoredVendors]);

    if (variant === "icon") {
        return (
            <SponsoredVendorsTooltip>
                <Box
                    sx={{
                        background: theme.palette.gradient.primary,
                        width: 24,
                        height: 24,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        borderRadius: "50%",
                        ...(sx || {}),
                    }}
                    data-testid="sponsored-vendors-icon"
                    padding={0.5}>
                    <CampaignOutlinedIcon sx={{width: "16px", height: "16px"}} htmlColor={theme.palette.neutral[100]} />
                </Box>
            </SponsoredVendorsTooltip>
        );
    } else if (variant === "badge") {
        return (
            <SponsoredVendorsTooltip>
                <Box>
                    <Button id="sponsored-vendors" variant="tonal" sx={sx}>
                        <Typography
                            variant="body5"
                            className="align-middle fw-bold"
                            fontWeight="bold !important"
                            sx={{display: "flex", gap: "4px", alignItems: "center"}}
                            color="#fff">
                            <CampaignOutlinedIcon sx={{width: "16px", height: "16px"}} htmlColor={"#fff"} />
                            Sponsored
                        </Typography>
                    </Button>
                </Box>
            </SponsoredVendorsTooltip>
        );
    }
    return (
        <Grid
            item
            sx={{
                minHeight: 85,
                cursor: hasEditAccess ? "pointer" : "default",
                padding: "0px 10px",
                ".hover-show": {
                    display: "none",
                },
                "&:hover": {
                    ".hover-show": {
                        display: hasEditAccess ? "flex" : "none",
                    },
                },
            }}
            onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                if (!hasEditAccess) return;
                onClickAddToStack && onClickAddToStack(category_id);
            }}
            width="100%"
            display="flex"
            flexDirection="row"
            justifyContent="center"
            alignItems="center"
            border={`1px solid ${theme.palette.primary[300]}`}
            bgcolor={theme.palette.primary[100]}
            borderRadius="5px"
            gap="10px">
            <Box display="flex" flexDirection="column" alignItems="center" gap={1} paddingX={1}>
                <Box display="flex" flexDirection="row" alignItems="center" gap={0.5}>
                    <CampaignOutlinedIcon sx={{width: "16px", height: "16px"}} htmlColor={theme.palette.primary[500]} />
                    <Typography variant="subtitle4" color={theme.palette.primary[500]} fontInter>
                        {NAVISTACK_SPONSORED_LABEL}
                    </Typography>
                </Box>
                <Box display="flex" flexDirection="row" alignItems="center" gap={0.5}>
                    {sponsoredVendors.slice(0, MAX_VENDORS_TO_SHOW)?.map(item => (
                        <Box
                            key={item.id}
                            onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                storeAdvertisementAnalytics(item);
                                onClickVendor && onClickVendor(item);
                            }}
                            sx={{cursor: "pointer !important"}}
                            title="View vendor details">
                            <AvatarComponentV2
                                isCompany
                                user={item.subject as any}
                                showProfileType
                                profileTypeMinimized
                                size={32}
                                disableLink
                                style={{cursor: "pointer"}}
                            />
                        </Box>
                    ))}
                    {sponsoredVendors.length > MAX_VENDORS_TO_SHOW && (
                        <SponsoredVendorsTooltip>
                            <Box>
                                <Badge
                                    style={{
                                        background: theme.palette.primary[500],
                                        zIndex: 2,
                                    }}
                                    inverted
                                    color="primary"
                                    padding="2px 6px">
                                    +{sponsoredVendors.length - MAX_VENDORS_TO_SHOW}
                                </Badge>
                            </Box>
                        </SponsoredVendorsTooltip>
                    )}
                </Box>
            </Box>
            <Box
                className="hover-show"
                flexDirection="row"
                alignItems="center"
                borderLeft={`1px solid ${theme.palette.primary[300]}`}
                height="100%"
                paddingX={2}
                gap={0.5}
                minHeight={60}>
                <AddOutlined sx={{height: 24, width: 24}} htmlColor={theme.palette.primary[600]} />
                <Typography variant="body5" fontWeight={700} color={theme.palette.blue[800]} whiteSpace="nowrap">
                    Add to Stack
                </Typography>
            </Box>
        </Grid>
    );
};

export default SponsoredVendorsClassificationItem;
