import useTheme from "@mui/material/styles/useTheme";
import Box from "../../Atoms/Box/Box.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import AddressAutoComplete from "../../Molecules/AddressAutocomplete/addressAutoComplete.component";
import {useEffect} from "react";
import {useFormContext} from "react-hook-form";
import CompanyType from "../../../constants/companyType.constant";

const BetterTrackerRegisterFormStepOne = ({isLoading}) => {
    const theme = useTheme();
    const {setValue} = useFormContext();

    useEffect(() => {
        setValue("company_type", CompanyType.DIRECT);
    }, []);

    return (
        <>
            <Box
                style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: 24,
                    width: "100%",
                }}>
                <TextBoxComponent
                    id="company_name"
                    label="Company Name"
                    isRequired
                    containerSx={{width: "100%"}}
                    keepRegistered
                />
                <AddressAutoComplete
                    id="addressAutoComplete"
                    isRequired
                    validateOnTheFly
                    label="Address"
                    placeholder="Enter Address"
                    sx={{width: "100%"}}
                    showDropdownIcon={false}
                    keepRegistered
                />
                <TextBoxComponent
                    id="company_type"
                    label="Company Type"
                    containerSx={{display: "none"}}
                    disabled
                    isRequired
                    keepRegistered
                />
            </Box>
        </>
    );
};

export default BetterTrackerRegisterFormStepOne;
