import Grid from "@mui/material/Grid";
import Typography from "../../Atoms/Typography/Typography.component";
import {useTheme} from "@mui/material";

interface IProps {
    companyInfo?: string | undefined;
}
export default function VendorAboutTab({companyInfo}: IProps) {
    const theme = useTheme();
    return (
        <Grid container>
            <Typography
                fontInter
                variant="body3"
                sx={{
                    color: theme.palette.neutral[800],
                    lineHeight: "160%",
                    letterSpacing: "0.2px",
                    "& p": {
                        color: theme.palette.neutral[800],
                        fontSize: "16px",
                    },
                }}
                dangerouslySetInnerHTML={{
                    __html: companyInfo || "",
                }}></Typography>
        </Grid>
    );
}
