import BookmarkBorderOutlined from "@mui/icons-material/BookmarkBorderOutlined";
import CloseIcon from "@mui/icons-material/Close";
import LaunchOutlinedIcon from "@mui/icons-material/LaunchOutlined";
import PersonAddAlt1OutlinedIcon from "@mui/icons-material/PersonAddAlt1Outlined";
import StoreOutlinedIcon from "@mui/icons-material/StoreOutlined";
import TimerOutlinedIcon from "@mui/icons-material/TimerOutlined";
import {Divider, Grid, Skeleton} from "@mui/material";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError, AxiosResponse} from "axios";
import {ReactNode, useEffect, useState} from "react";
import {Link, useLocation} from "react-router-dom";
import ControlledStarRating from "../../../components/FormControls/ControlledStarRating.component";
import OutboundLink from "../../../components/Links/OutboundLink.component";
import CompanyPartnersModal from "../../../components/Modal/companyPartnersModal.component";
import {ChannelDealsInfinityScroll} from "../../../components/VendorProfile/ChannelDealsInfinityScroll.component";
import {CHANNEL_PROGRAM_COMPANY_ID, CHANNEL_PROGRAM_SUBDOMAIN} from "../../../constants/commonStrings.constant";
import {PARTNER_INVITE_STATUS, PartnerInviteStatusType} from "../../../constants/partnerInviteStatus.constant";
import routeConfig from "../../../constants/routeConfig";
import {useCompany} from "../../../hooks/fetches/useCompany";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {companyService} from "../../../services/company.service";
import {mspPartnersService} from "../../../services/mspPartners.service";
import {PartnerPageService} from "../../../services/partnerpage.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import {generateSubdomainUrl} from "../../../utils/subdomain.util";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import DropdownItem from "../../Atoms/Dropdown/DropdownItem.component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import MdfActiveBadge from "../../Badges/mdfActiveBadge.component";
import PortalActiveBadge from "../../Badges/portalActiveBadge.component";
import PortalStatusBadge from "../../Badges/portalStatusBadge.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import SubcategoriesLineDisplay from "../../Molecules/SubcategoriesLineDisplay/subcategoriesLineDisplay.component";
import Tabs from "../../Molecules/Tabs/tabs.component";
import {IAvailableVendor} from "../MspTechStackTab/MspTechStackTab.component";
import VendorAboutTab from "./VendorAboutTab.component";
import VendorLineCardTab from "./VendorLineCard.component";
import VendorProductsTab from "./VendorProductsTab.component";

export interface ISubcategory {
    id?: string;
    name?: string;
    friendly_url?: string;
}

interface ITabProps {
    label: ReactNode;
    id: string;
    Component: ReactNode;
}

export interface IVendorInfoModalV2Props {
    open: boolean;
    onClose?: () => void;
    companyFriendlyUrl?: string;
    isDistributor?: boolean;
    onClickAddToStack?: Function;
    onClickRemoveFromStack?: Function;
    onClickContactVendor?: Function;
    advertisementId?: string;
    myStackProductsIDs?: string[] | undefined;
}

const BadgedLabel = ({title, counter}: {title: string; counter: number | null}) => {
    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
            }}>
            {title}
            {counter && (
                <Badge
                    color="secondary"
                    padding="2px 6px"
                    style={{
                        display: "flex",
                    }}
                    sx={{
                        margin: "0 !important",
                        lineHeight: "120%",
                    }}>
                    {counter}
                </Badge>
            )}
        </Box>
    );
};

const VendorInfoModalV2 = (props: IVendorInfoModalV2Props) => {
    const {open, onClose, companyFriendlyUrl} = props;
    const [productsParentCategories, setProductsParentCategories] = useState<ISubcategory[] | undefined>(undefined);
    const [marketplaceVendorsCategories, setMarketplaceVendorsCategories] = useState<ISubcategory[] | undefined>(
        undefined,
    );
    const [showCompanyPartners, setShowCompanyPartners] = useState<boolean>(false);
    const [partnerUsers, setPartnerUsers] = useState<any>([]);
    const [prmActionLoading, setPrmActionLoading] = useState<boolean>(false);
    const [requestedAcceptedPartners, setRequestedAcceptedPartners] = useState<IAvailableVendor[] | null>(null);
    const {settings, getAllCategories} = useSettings();
    const {company, companyProducts, companyChannelDeals} = useCompany(companyFriendlyUrl || "", {
        isCompany: !!companyFriendlyUrl,
        calls: {
            all: false,
            company: true,
            companyProducts: !props?.isDistributor,
            companyChannelDeals: true,
        },
    });

    const {authState} = useAuthState();
    const {activeCompany, userCompanies} = useActiveCompany();
    const isLoading = company.isLoading || companyProducts.isLoading;
    const location = useLocation();
    const theme = useTheme();
    const companyInfo = company?.data;
    const products = companyProducts?.data;
    const msp_friendly_url = useGetFriendlyUrl(location.pathname) || activeCompany?.friendly_url;
    const notify = useNotification();
    const {hasPermissions} = usePermissions();
    const hasRequestUpdate = hasPermissions([PERMISSION_GROUPS.MANAGE_VENDORS_UPDATE]);
    const msp_id = msp_friendly_url
        ? authState.company_friendly_url === msp_friendly_url
            ? authState.company_id
            : userCompanies?.find(c => c.friendly_url === msp_friendly_url)?.id
        : authState.company_id;
    const foundPartner = partnerUsers?.find(p => p.friendly_url === companyFriendlyUrl);
    const isDistributor = company?.data?.is_distributor || props.isDistributor;
    const company_tracking_info = {
        vendor_id: companyInfo?.id,
        vendor_name: companyInfo?.name,
        vendor_friendly_url: companyInfo?.friendly_url,
        advertisement_id: props.advertisementId || null,
    };
    const foundAvailableVendor = requestedAcceptedPartners?.find(v => v.friendly_url === companyFriendlyUrl);
    const isChannelProgram = companyInfo?.id === CHANNEL_PROGRAM_COMPANY_ID;

    useEffect(() => {
        if (open) return;
        setProductsParentCategories(undefined);
        setMarketplaceVendorsCategories(undefined);
    }, [open]);

    const getProductsParentCategories = () => {
        const parentCategories: ISubcategory[] = [];
        products?.forEach(p => {
            p.categories?.forEach(c => {
                parentCategories.push({
                    id: c.id || "",
                    name: c.name || "",
                    friendly_url: c.friendly_url || "",
                });
            });
        });
        setProductsParentCategories(parentCategories);
    };

    const onGetMarketplaceCategories = data => {
        setMarketplaceVendorsCategories(data || undefined);
    };

    const tabs: ITabProps[] = [
        ...(!!companyInfo?.description
            ? [
                  {
                      label: "About",
                      id: "aboutTab",
                      Component: <VendorAboutTab companyInfo={companyInfo?.description || ""} />,
                  },
              ]
            : []),
        ...(!!companyInfo?.is_distributor
            ? [
                  {
                      label: "Marketplace",
                      id: "marketplace",
                      Component: (
                          <VendorLineCardTab
                              friendlyUrl={companyFriendlyUrl}
                              onGetMarketplaceCategories={onGetMarketplaceCategories}
                          />
                      ),
                  },
              ]
            : []),
        ...(!!products?.length
            ? [
                  {
                      label: <BadgedLabel title="Products" counter={products.length} />,
                      id: "products",
                      Component: (
                          <VendorProductsTab
                              isLoading={isLoading}
                              products={products}
                              companyTrackingInfo={company_tracking_info}
                              onClickAddToStack={(category_id?: string, product?: any) => {
                                  if (!props?.onClickAddToStack || !props?.onClose) return;
                                  props.onClickAddToStack(category_id, {
                                      ...product,
                                      parent: {
                                          id: companyInfo?.id,
                                          name: companyInfo?.name,
                                          avatar: companyInfo?.profile_images?.CompanyAvatar?.[0]?.src,
                                      },
                                  });
                                  props.onClose();
                              }}
                              onClickRemoveFromStack={async (product_id: string) => {
                                  if (!props?.onClickRemoveFromStack) return;
                                  await props?.onClickRemoveFromStack(product_id);
                                  company.refetch();
                              }}
                              myStackProductsIDs={props?.myStackProductsIDs || []}
                          />
                      ),
                  },
              ]
            : []),
        ...(companyChannelDeals?.data?.meta.total
            ? [
                  {
                      label: <BadgedLabel title="Deals" counter={companyChannelDeals?.data?.meta.total} />,
                      id: "channel-deals",
                      Component: (
                          <ChannelDealsInfinityScroll hideHeaderAndFilters companyFriendlyUrl={companyFriendlyUrl} />
                      ),
                  },
              ]
            : []),
    ];

    const subcategories = productsParentCategories || marketplaceVendorsCategories || [];

    const SubcategoriesElement = (
        <>
            <SubcategoriesLineDisplay
                subcategories={subcategories}
                numCategories={2}
                openInNewTab={true}
                categorySx={{
                    display: {xs: "none", md: "flex"},
                    fontWeight: 400,
                    color: theme.palette.neutral[700],
                    lineHeight: "normal",
                    maxWidth: "100%",
                    marginTop: {xs: 1, md: 0},
                    whiteSpace: {xs: "break-spaces", md: "nowrap"},
                }}
                moreCategoriesText={{
                    display: {xs: "none", md: "flex"},
                }}
            />
            <Box display={{xs: "flex", md: "none"}} flexDirection="column">
                {subcategories.slice(0, 2)?.map(subcategory => (
                    <Link
                        key={subcategory.id}
                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                            ":friendly_url",
                            subcategory.friendly_url || "",
                        )}
                        title={subcategory.name}
                        target={"_blank"}
                        rel={"noopener"}
                        style={{
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                            lineClamp: 1,
                            maxWidth: "100%",
                            textAlign: "center",
                        }}>
                        <Typography variant="body3" fontInter color={theme.palette.blue[800]} title={subcategory.name}>
                            {subcategory.name}
                        </Typography>
                    </Link>
                ))}
                {(productsParentCategories || marketplaceVendorsCategories || []).length > 2 && (
                    <CPTooltip
                        light
                        title={
                            <Box display="flex" flexDirection="column" gap={1}>
                                {(productsParentCategories || marketplaceVendorsCategories || [])?.map(
                                    (subcategory, index) => {
                                        return (
                                            <Link
                                                key={index}
                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                    ":friendly_url",
                                                    subcategory.friendly_url || "",
                                                )}
                                                style={{width: "100%"}}
                                                target={"_blank"}
                                                rel={"noopener"}
                                                onClick={e => e.stopPropagation()}>
                                                <DropdownItem>
                                                    <Typography
                                                        variant="body3"
                                                        fontInter
                                                        color={theme.palette.blue[800]}
                                                        title={subcategory.name}
                                                        style={{
                                                            textOverflow: "ellipsis",
                                                            overflow: "hidden",
                                                            lineClamp: 1,
                                                            maxWidth: "100%",
                                                        }}>
                                                        {subcategory.name}
                                                    </Typography>
                                                </DropdownItem>
                                            </Link>
                                        );
                                    },
                                )}
                            </Box>
                        }>
                        {subcategories.length <= 2 ? (
                            <></>
                        ) : (
                            <Typography
                                variant={"body4"}
                                sx={{
                                    color: theme.palette.neutral[700],
                                    paddingLeft: "2px",
                                    whiteSpace: "nowrap",
                                    textAlign: "center",
                                }}>
                                {` +${subcategories.length - 2}`}
                            </Typography>
                        )}
                    </CPTooltip>
                )}
            </Box>
        </>
    );

    const onSuccessGetAllPartners = (res: AxiosResponse) => {
        setPartnerUsers(res.data);
    };

    const handleDeleteInvite = async () => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Cancel Request?",
            okButtonText: "Yes",
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                        Are you sure want to cancel this access request?
                    </Typography>
                </Grid>
            ),
        });
        if (!answer.value) {
            return;
        }
        setPrmActionLoading(true);
        PartnerPageService.removeInvite(
            msp_id || "",
            companyInfo?.id || "",
            true,
            () => {
                notify("Success! Your request for access has been removed.", "Success");
                setPartnerUsers(partnerUsers.map(p => ({...p, partner: {...(p.partner || {}), status: null}})));
                setRequestedAcceptedPartners(prev => (prev ?? []).map(p => ({...p, partner_status: null})));
                setPrmActionLoading(false);
            },
            handleError,
        );
    };

    const onSuccessRequestAccess = (res: AxiosResponse) => {
        setPrmActionLoading(false);
        notify("Thank you! Your request has been received", "Success");
        const inviteObj = res.data[0];
        const foundPendingPartner = (requestedAcceptedPartners ?? []).find(p => p.id === inviteObj.partner.id);
        if (foundPendingPartner) {
            setRequestedAcceptedPartners(prev =>
                (prev ?? []).map(p => ({
                    ...p,
                    partner_status: p.id === inviteObj.partner?.id ? inviteObj.status : p.partner_status,
                })),
            );
        } else if (companyInfo?.id) {
            const newRequested: IAvailableVendor = {
                avatar: companyInfo?.avatar || null,
                friendly_url: companyFriendlyUrl || "",
                id: companyInfo?.id,
                is_distributor: companyInfo?.is_distributor,
                name: companyInfo?.name,
                partner_flag: companyInfo?.partner_flag || false,
                partner_status: PARTNER_INVITE_STATUS.REQUESTED as "requested",
                subdomain: companyInfo?.subdomain,
            };
            setRequestedAcceptedPartners(prev => [...(prev ?? []), newRequested]);
        }
    };

    const handleRequestAccess = () => {
        if (msp_id) {
            setPrmActionLoading(true);
            mspPartnersService.inviteVendor(
                companyInfo?.id || "",
                {email: [authState.email], initiator: "user"},
                onSuccessRequestAccess,
                handleError,
            );
        } else {
            notify("Request access for company is not available.", "Error");
            setPrmActionLoading(false);
        }
    };

    const handleError = (error: AxiosError<any>) => {
        prmActionLoading && setPrmActionLoading(false);
        notify(getErrorFromArray(error), "Error");
    };

    const onSuccessGetRequestedAccepted = (res: AxiosResponse) => {
        setRequestedAcceptedPartners(res.data);
    };

    useEffect(() => {
        if (msp_id && open) {
            !settings.all_categories && getAllCategories();
            PartnerPageService.getCompanyPartners(msp_id, onSuccessGetAllPartners);
            companyService.getStackAvailableVendors(msp_id || "", {}, onSuccessGetRequestedAccepted);
        }
    }, [msp_id, open]);

    useEffect(() => {
        if (!!props?.isDistributor && !companyInfo?.products?.length) return;
        if (open && settings.all_categories && companyProducts && !productsParentCategories?.length) {
            getProductsParentCategories();
        }
    }, [open, settings.all_categories, companyProducts]);

    return (
        <>
            <ModalComponent
                id="vendorInfoModal"
                justifyButtons="flex-start"
                open={open}
                onClose={onClose as any}
                hideCloseBtn
                title={""}
                contentClassName="mt-0"
                modalTheme="blue"
                customModalBtnTracking={company_tracking_info}
                showOkButton={false}
                showCancelButton={false}
                maxWidth="md"
                showBottom={false}
                dialogSx={{
                    ".MuiDialog-paper": {
                        overflow: "unset",
                        maxWidth: "800px !important",
                        maxHeight: "100%",
                        width: "100%",
                    },
                    ".MuiDialogContent-root": {
                        overflow: "unset",
                    },
                }}
                content={
                    <Box
                        marginTop={2}
                        data-testid="vendor-info-modal-v2"
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            height: "100%",
                            gap: 2,
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                position: "sticky",
                                gap: 2,
                                flexDirection: {xs: "column", md: "row"},
                                width: "100%",
                            }}>
                            <AvatarComponentV2
                                isCompany
                                size={48}
                                user={{
                                    avatar: companyInfo?.profile_images?.CompanyAvatar?.[0]?.src || "",
                                    type: companyInfo?.company_type || "",
                                    is_distributor: companyInfo?.is_distributor,
                                    profile_type: companyInfo?.company_profile_type,
                                    friendly_url: companyInfo?.friendly_url || "",
                                    name: companyInfo?.name || "",
                                }}
                                badgeSx={{
                                    "&": {
                                        padding: "0 4px !important",
                                    },
                                }}
                                showProfileType
                                profileTypeMinimized
                            />
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    flex: 1,
                                }}>
                                {/* Company Name */}
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: {xs: "column", md: "row"},
                                        alignItems: "center",
                                        minHeight: "34px",
                                        flex: 1,
                                        gap: 1,
                                    }}>
                                    {isLoading ? (
                                        <Skeleton sx={{width: 200, height: 50}} />
                                    ) : (
                                        <>
                                            <Typography
                                                variant="h5"
                                                fontFamily="roc-grotesk"
                                                sx={{
                                                    fontSize: "24px !important",
                                                    fontWeight: "400 !important",
                                                    lineHeight: "normal",
                                                    color: theme.palette.neutral[800],
                                                }}>
                                                {companyInfo?.name}
                                            </Typography>
                                            <PortalStatusBadge
                                                partner_status={
                                                    foundAvailableVendor?.partner_status as PartnerInviteStatusType
                                                }
                                                reason={foundPartner?.partner?.accepted_reason_name}
                                            />
                                            <PortalActiveBadge partner_flag={!!companyInfo?.partner_flag} />
                                            {companyInfo?.is_mdf && <MdfActiveBadge />}
                                        </>
                                    )}
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: {xs: "column", md: "row"},
                                        alignItems: "center",
                                        flex: 1,
                                        gap: 2,
                                        width: "100%",
                                    }}>
                                    <div className="d-flex flex-row">
                                        {isLoading ? <Skeleton sx={{width: 150, height: 30}} /> : SubcategoriesElement}
                                    </div>
                                    {!!companyInfo?.rating && (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "4px",
                                            }}>
                                            {companyInfo?.rating ? (
                                                <>
                                                    <Link
                                                        to={routeConfig.VendorProfile.path.replace(
                                                            ":id",
                                                            companyInfo?.friendly_url || "",
                                                        )}
                                                        target="_blank">
                                                        <Box
                                                            display="flex"
                                                            flexDirection="row"
                                                            alignItems="center"
                                                            gap="4px">
                                                            <ControlledStarRating
                                                                id={`rating_${companyInfo?.id}`}
                                                                value={companyInfo?.rating || 0}
                                                                showLabels={false}
                                                                maxRating={1}
                                                                readOnly
                                                                iconSize={16}
                                                                containerClassName="m-0"
                                                                title={companyInfo?.rating?.toFixed(1)}
                                                            />
                                                            <Typography
                                                                variant="body4"
                                                                sx={{
                                                                    fontWeight: 700,
                                                                    color: theme.palette.neutral[700],
                                                                    lineHeight: "160%",
                                                                }}
                                                                letterSpacing={0}>
                                                                {companyInfo?.rating?.toFixed(1)}
                                                            </Typography>
                                                        </Box>
                                                    </Link>
                                                </>
                                            ) : (
                                                <Typography
                                                    variant="subtitle3"
                                                    fontWeight={600}
                                                    color={theme.palette.neutral[600]}
                                                    letterSpacing={0}>
                                                    No reviews yet
                                                </Typography>
                                            )}
                                        </Box>
                                    )}
                                </Box>
                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    position: "absolute",
                                    top: 0,
                                    right: 20,
                                }}>
                                <IconButton
                                    id="closeModal"
                                    sx={{
                                        backgroundColor: "#fff !important",
                                        fontSize: "32px",
                                        "& svg": {
                                            width: "32px !important",
                                            height: "32px !important",
                                        },
                                    }}
                                    title="Close"
                                    onClick={e => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        if (!!onClose) {
                                            onClose();
                                        }
                                    }}
                                    color="blue">
                                    <CloseIcon fontSize="inherit" htmlColor={theme.palette.neutral?.[500]} />
                                </IconButton>
                            </Box>
                        </Box>
                        <Divider />
                        {company.isLoading || companyProducts.isLoading ? (
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    width: "100%",
                                    height: "350px",
                                }}>
                                <Loader inline loading version="iconOrange" />
                            </Box>
                        ) : (
                            <Tabs
                                tabs={tabs}
                                hideBorder
                                defaultTab={!!props?.isDistributor ? "marketplace" : "products"}
                                tabsContentSx={{
                                    marginTop: 2,
                                    overflowY: "auto",
                                    maxHeight: "281px",
                                    paddingRight: 3,
                                    "&::-webkit-scrollbar": {
                                        width: "8px",
                                    },
                                    "&::-webkit-scrollbar-track-piece": {
                                        backgroundColor: theme.palette.neutral[200],
                                    },
                                    "&::-webkit-scrollbar-thumb:vertical, &::-webkit-scrollbar-thumb:horizontal": {
                                        backgroundColor: theme.palette.neutral[400],
                                    },
                                }}
                                tabsSx={{
                                    "& .tabCounterBadge": {
                                        display: "inline-flex",
                                        padding: "2px 6px",
                                        marginLeft: "2px",
                                        borderStyle: "solid",
                                        borderWidth: "1px",
                                        borderColor: theme.palette.blue[300],
                                        backgroundColor: theme.palette.blue[100],
                                        color: theme.palette.blue[700],
                                        borderRadius: "17px",
                                    },
                                }}
                            />
                        )}
                        <Divider />
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: {xs: "column", md: "row"},
                                flex: 1,
                                gap: 1,
                            }}>
                            <Button
                                sx={{
                                    width: {xs: "100%!important", md: "187px!important"},
                                    cursor: "cursor",
                                }}
                                onClick={() => {
                                    if (props?.onClickContactVendor) {
                                        props?.onClickContactVendor(companyInfo?.name, companyInfo?.friendly_url);
                                    }
                                    if (!!onClose) {
                                        onClose();
                                    }
                                }}
                                variant="tonal"
                                type="button"
                                title="Request demo"
                                id="requestDemo">
                                <BookmarkBorderOutlined sx={{marginRight: "8px", width: "18px", height: "18px"}} />
                                <Typography
                                    variant="body4"
                                    fontInter
                                    sx={{
                                        fontWeight: "600 !important",
                                        lineHeight: "20px",
                                        color: theme.palette.neutral[100],
                                    }}>
                                    REQUEST DEMO
                                </Typography>
                            </Button>
                            <OutboundLink
                                href={
                                    window.location.origin +
                                    routeConfig.VendorProfile.path.replace(":id", companyInfo?.friendly_url || "")
                                }>
                                <Button
                                    sx={{
                                        width: {xs: "100%!important", md: "187px!important"},
                                        alignSelf: "stretch",
                                        margin: 0,
                                    }}
                                    variant="contained"
                                    color="secondary"
                                    type="button"
                                    title={`View ${isDistributor ? "distributor" : "vendor"} Profile`}
                                    id="viewDistributor">
                                    <LaunchOutlinedIcon sx={{marginRight: "8px", width: "18px", height: "18px"}} />
                                    <Typography
                                        variant="body4"
                                        fontInter
                                        sx={{
                                            fontWeight: "600 !important",
                                            lineHeight: "20px",
                                            color: theme.palette.neutral[100],
                                        }}>
                                        {isDistributor ? "VIEW DISTRIBUTOR" : "SEE FULL PROFILE"}
                                    </Typography>
                                </Button>
                            </OutboundLink>
                            {companyInfo?.partner_flag && hasRequestUpdate && (
                                <Button
                                    sx={{
                                        width: {xs: "100%!important", md: "187px!important"},
                                        alignSelf: "stretch",
                                        margin: 0,
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                    onClick={
                                        foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ||
                                        isChannelProgram
                                            ? () => {
                                                  const url = generateSubdomainUrl(
                                                      isChannelProgram
                                                          ? CHANNEL_PROGRAM_SUBDOMAIN
                                                          : foundAvailableVendor?.subdomain,
                                                  );
                                                  const newTab = window.open(url, "_blank");
                                                  newTab?.focus();
                                              }
                                            : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.REQUESTED
                                            ? () => handleDeleteInvite()
                                            : () => handleRequestAccess()
                                    }
                                    variant="outlined"
                                    color="secondary"
                                    type="button"
                                    id="prmAction"
                                    disabled={prmActionLoading || !foundAvailableVendor}
                                    title={
                                        prmActionLoading || !foundAvailableVendor
                                            ? "This action is temporarily unavailable"
                                            : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                            ? "Access vendor portal"
                                            : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.REQUESTED
                                            ? "Cancel PRM access request"
                                            : "Request PRM access"
                                    }>
                                    <Typography
                                        variant="body4"
                                        fontInter
                                        sx={{
                                            fontWeight: "600 !important",
                                            lineHeight: "20px",
                                            display: "flex",
                                            alignItems: "center",
                                        }}>
                                        {foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ||
                                        isChannelProgram ? (
                                            <>
                                                <StoreOutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />{" "}
                                                VIEW PORTAL
                                            </>
                                        ) : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.REQUESTED ? (
                                            <>
                                                <TimerOutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />
                                                {prmActionLoading ? "Cancelling..." : "CANCEL REQUEST"}
                                            </>
                                        ) : (
                                            <>
                                                <PersonAddAlt1OutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />
                                                {prmActionLoading ? "REQUESTING..." : "REQUEST ACCESS"}
                                            </>
                                        )}
                                    </Typography>
                                </Button>
                            )}
                        </Box>
                    </Box>
                }
            />
            <CompanyPartnersModal
                show={showCompanyPartners}
                setShow={(shouldShow: boolean) => setShowCompanyPartners(shouldShow)}
                type="claimers"
                partner={foundPartner}
            />
        </>
    );
};

export default VendorInfoModalV2;
