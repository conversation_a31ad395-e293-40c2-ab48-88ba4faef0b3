import ContentCopyOutlinedIcon from "@mui/icons-material/ContentCopyOutlined";
import DoneOutlinedIcon from "@mui/icons-material/DoneOutlined";
import {useTheme} from "@mui/material";
import {Box} from "@mui/system";
import {useEffect, useState} from "react";
import type {ICompany} from "../../../Interfaces/company.interface";
import routeConfig from "../../../constants/routeConfig";
import {validateEmail} from "../../../utils/validation.util";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import ShareButton from "../../Molecules/ShareButton/shareButton.component";
import UserListInput from "../../Molecules/UserListInput/UserListInput.component";

interface IProps {
    company?: ICompany;
    handleShareSuccess?: Function;
}

export default function ShareALink(props: IProps) {
    const {company} = props;
    const theme = useTheme();
    const [copySuccess, setCopySuccess] = useState(false);
    const shareLink =
        window.location.protocol +
        "//" +
        window.location.host +
        routeConfig.MyStack.path +
        `?invited=${company?.subdomain}`;
    const shareText = `Hey there! ${company?.name} wants you to join their network.\n\nClick here to accept the invitation:\n${shareLink}`;

    const copyToClipBoard = async textToBeCopied => {
        try {
            await navigator.clipboard.writeText(textToBeCopied);
            setCopySuccess(true);
        } catch (err) {
            setCopySuccess(false);
        }
    };

    useEffect(() => {
        if (copySuccess) {
            setTimeout(() => {
                setCopySuccess(false);
            }, 2000);
        }
    }, [copySuccess]);

    return (
        <Box sx={{display: "flex", gap: "24px", flexDirection: "column"}}>
            <Typography fontInter variant="body3" color={theme.palette.neutral[700]}>
                Enter the emails you want to send the invite to:
            </Typography>
            <UserListInput
                id="emails"
                label="Emails"
                placeholder="Enter Email Addresses"
                validateOption={v => validateEmail(v)}
                helperText="Enter the email address(es) or copy and paste a comma separated list of email addresses."
            />
            <hr color={theme.palette.neutral[300]} style={{margin: 0}} />
            <Typography fontInter variant="body3" color={theme.palette.neutral[700]}>
                Alternatively, copy our drafted message and share anywhere you’d like:
            </Typography>

            <Box
                sx={{
                    border: `1px solid ${theme.palette.neutral[300]} !important`,
                    borderRadius: "8px",
                    padding: "16px",
                    display: "flex",
                    gap: "16px",
                    flexDirection: "column",
                }}>
                <Typography
                    fontInter
                    variant="body3"
                    sx={{color: theme.palette.neutral[700]}}
                    dangerouslySetInnerHTML={{__html: shareText.replaceAll("\n", "<br />")}}
                />
                <Box sx={{display: "flex", gap: "16px", justifyContent: "flex-end"}}>
                    <Button
                        id="copyShareALink"
                        onClick={() => copyToClipBoard(shareText)}
                        variant="text"
                        color="secondary"
                        startIcon={
                            copySuccess ? (
                                <DoneOutlinedIcon color="secondary" />
                            ) : (
                                <ContentCopyOutlinedIcon color="secondary" />
                            )
                        }>
                        {copySuccess ? "COPIED" : "COPY"}
                    </Button>
                    <ShareButton variant="textButton" />
                </Box>
            </Box>
        </Box>
    );
}
