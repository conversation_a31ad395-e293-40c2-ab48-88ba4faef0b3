.headerContainer
    padding: 32px 64px 0px 64px
    position: relative
    min-height: 190px
    background: radial-gradient(circle, rgba(27,65,115,1) 0%, rgba(12,37,86,1) 100%)
    .headerText
        background: linear-gradient(46.54deg, #F15A33 29.84%, #FEC461 99.14%) !important
        -webkit-background-clip: text !important
        -webkit-text-fill-color: transparent !important
.sticky
    position: -webkit-sticky
    position: sticky !important
    top: 0
    z-index: 100
    min-height: auto !important
    padding: 20px !important
@media (max-width: 600px)
    .headerContainer
        padding: 32px 32px 0px 32px
        .headerText
            font-size: 32px !important
@media (max-width: 400px)
    .headerContainer
        padding: 32px 16px 0px 16px
        min-height: 200px
