import {<PERSON>berManualR<PERSON>ord} from "@mui/icons-material";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {DialogProps} from "@mui/material/Dialog";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {ICategory} from "../../../Interfaces/categories.interface";
import {IProfile} from "../../../Interfaces/people.interface";
import {IProductListing} from "../../../Interfaces/products.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {DISTRIBUTOR_INFO_TEXT} from "../../../constants/distributors.constant";
import {useNaviStack} from "../../../hooks/fetches/useNaviStack";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useNotification from "../../../hooks/useNotification";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import Accordion from "../../Atoms/Accordion/Accordion.component";
import AccordionDetails from "../../Atoms/Accordion/AccordionDetails.component";
import AccordionSummary from "../../Atoms/Accordion/AccordionSummary.component";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import CheckboxWithLabel from "../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import RadioWithLabel from "../../Molecules/RadioWithLabel/RadioWithLabel.component";
import VendorSearch from "../../Molecules/VendorSearch/vendorSearch.component";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {getStackTypeLabel} from "../../../constants/commonStrings.constant";

interface IProps extends DialogProps {
    handleClose: (event?: any) => void;
    friendly_url?: string;
    onOpenAddToStack?: (categoryId: string) => void;
}

const AddCorporateRecommendedDialog = (props: IProps) => {
    const theme = useTheme();
    const {activeCompany, isMSPLocation} = useActiveCompany();
    const {missingAdoptionStack, updateNaviStack, isUpdating} = useNaviStack(activeCompany?.id || "", {
        isCompany: true,
        isMSP: activeCompany?.type_is_of_vendor === false,
        calls: {all: false, missingAdoptionStack: true},
    });
    const [selectedAdoptionStack, setSelectedAdoptionStack] = useState<any>({});
    const methods = useForm();
    const notify = useNotification();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_STACK_UPDATE]);
    const isSaving = isUpdating[MutateTypes.AddToStack];
    const stackTypeLabel = getStackTypeLabel(isMSPLocation);

    const handleSuccessClick = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const formValues = methods.getValues();
        const requestBody = {};
        Object.keys(selectedAdoptionStack).forEach(key => {
            const selectedValue = selectedAdoptionStack[key];
            if (!requestBody[selectedValue.sub_category.id]) {
                requestBody[selectedValue.sub_category.id] = [];
            }
            const newStackItem: any = {
                company_id: selectedValue.product?.company_id,
                product_id: selectedValue.product?.id,
                partner_status:
                    formValues["is_partner_" + selectedValue.sub_category.id + "_" + selectedValue.product.id],
            };
            if (formValues["distributor_id_" + selectedValue.sub_category.id + "_" + selectedValue.product.id]) {
                newStackItem.distributor_id =
                    formValues["distributor_id_" + selectedValue.sub_category.id + "_" + selectedValue.product.id];
                newStackItem.sold_by_distributor = true;
            }
            requestBody[selectedValue.sub_category.id].push(newStackItem);
        });
        updateNaviStack.mutate({
            mutate_type: MutateTypes.AddToStack,
            subject_id: activeCompany?.id,
            requestBody,
            onSuccess: onSuccessAddToStack,
            // onError: handleError,
            updateAdoptionDetails: true,
        });
    };

    const onSuccessAddToStack = () => {
        notify("Successfully added to stack.", "Success");
        props.handleClose && props.handleClose();
    };

    const handleSelectProduct = (sub_category: ICategory, product: IProductListing) => {
        setSelectedAdoptionStack({
            ...selectedAdoptionStack,
            [sub_category?.id + "-" + product.id]: {sub_category, product},
        });
    };

    const handleRemoveProduct = (sub_category: ICategory, product: IProductListing) => {
        const copy = {...selectedAdoptionStack};
        if (copy[sub_category?.id + "-" + product.id]) {
            delete copy[sub_category?.id + "-" + product.id];
            setSelectedAdoptionStack(copy);
        }
    };

    useEffect(() => {
        return () => {
            setSelectedAdoptionStack({});
            methods.reset();
        };
    }, [props.open]);

    return (
        <ErrorBoundary>
            <ModalComponent
                open={props.open}
                onClose={props.handleClose}
                onSuccess={handleSuccessClick}
                title="Brand Stack"
                customTitle={
                    <Box display="flex" flexDirection="column">
                        <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                            <AddCircleOutlineOutlinedIcon />
                            {stackTypeLabel} Stack
                        </Box>
                        <Typography variant="body4">
                            Your{!isMSPLocation ? " Franchise" : ""} Corporation has recommended categories and products
                            for your stack.{" "}
                            {hasEditAccess
                                ? `To add a
                            suggested product, expand the category and then click the + icon next to it. To add a different product to a suggested
                            category, click "Add a Product."`
                                : ""}
                        </Typography>
                    </Box>
                }
                justifyButtons="flex-start"
                okButtonText={"SAVE CHANGES"}
                loading={isSaving}
                showOkButton={hasEditAccess}
                okButtonDisabled={Object.keys(selectedAdoptionStack).length <= 0}
                cancelButtonText="CANCEL"
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflow: "hidden", gap: 2},
                }}
                showAdditionalBtn={false}
                content={
                    <FormProvider {...methods}>
                        <Box display="flex" flexDirection="column" sx={{overflowY: "auto"}}>
                            {missingAdoptionStack.isLoading ? (
                                "Loading..."
                            ) : (
                                <>
                                    {missingAdoptionStack.data?.items
                                        ?.sort((a, b) => a.sub_category?.name.localeCompare(b.sub_category?.name))
                                        .map((item, i) => {
                                            return (
                                                <Accordion key={item.sub_category?.id || i}>
                                                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                                        <Box
                                                            sx={{
                                                                display: "flex",
                                                                flexDirection: "row",
                                                                gap: 1,
                                                                alignItems: "center",
                                                            }}>
                                                            <FiberManualRecord
                                                                sx={{
                                                                    fill: item.sub_category?.color,
                                                                    height: "16px",
                                                                    width: "16px",
                                                                }}
                                                            />
                                                            <Typography variant="body3" fontInter>
                                                                {item.sub_category?.name}
                                                            </Typography>
                                                        </Box>
                                                    </AccordionSummary>
                                                    <AccordionDetails
                                                        sx={{display: "flex", flexDirection: "column", gap: 1}}>
                                                        {item.products?.length ? (
                                                            item.products.map(product => {
                                                                const isSelected =
                                                                    !!selectedAdoptionStack?.[
                                                                        item.sub_category?.id + "-" + product.id
                                                                    ];
                                                                const selectedIsPartner =
                                                                    methods.watch(
                                                                        `is_partner_${item.sub_category?.id}_${product.id}`,
                                                                    ) === "I am a current partner";
                                                                const soldByDistributorWatcher = methods.watch(
                                                                    `sold_by_distributor_${item.sub_category?.id}_${product.id}`,
                                                                );
                                                                return (
                                                                    <BorderedBox
                                                                        key={product.id}
                                                                        sx={{
                                                                            padding: 1,
                                                                            display: "flex",
                                                                            flexDirection: "column",
                                                                            gap: 0,
                                                                        }}>
                                                                        <Box
                                                                            sx={{
                                                                                display: "flex",
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                gap: 2,
                                                                            }}>
                                                                            <AvatarComponentV2
                                                                                isCompany
                                                                                size={36}
                                                                                user={product.company as IProfile}
                                                                            />
                                                                            <Typography
                                                                                variant="body3"
                                                                                fontInter
                                                                                fontWeight={500}>
                                                                                {product.company?.name} - {product.name}
                                                                            </Typography>
                                                                            {hasEditAccess && (
                                                                                <Box
                                                                                    marginLeft="auto"
                                                                                    display="flex"
                                                                                    flexDirection="row"
                                                                                    alignItems="center"
                                                                                    gap={2}>
                                                                                    {isSelected ? (
                                                                                        <>
                                                                                            <Box
                                                                                                sx={{cursor: "pointer"}}
                                                                                                title="Product will be added to stack">
                                                                                                <CheckCircleIcon
                                                                                                    htmlColor={
                                                                                                        theme.palette
                                                                                                            .success[600]
                                                                                                    }
                                                                                                />
                                                                                            </Box>
                                                                                            <Box
                                                                                                onClick={() =>
                                                                                                    handleRemoveProduct(
                                                                                                        item.sub_category,
                                                                                                        product,
                                                                                                    )
                                                                                                }
                                                                                                sx={{cursor: "pointer"}}
                                                                                                title="Remove Product from Stack">
                                                                                                <DeleteOutlinedIcon
                                                                                                    htmlColor={
                                                                                                        theme.palette
                                                                                                            .error[600]
                                                                                                    }
                                                                                                />
                                                                                            </Box>
                                                                                        </>
                                                                                    ) : (
                                                                                        <Box
                                                                                            onClick={() =>
                                                                                                handleSelectProduct(
                                                                                                    item.sub_category,
                                                                                                    product,
                                                                                                )
                                                                                            }
                                                                                            sx={{cursor: "pointer"}}
                                                                                            title="Add Product to Stack">
                                                                                            <AddCircleOutlineOutlinedIcon
                                                                                                htmlColor={
                                                                                                    theme.palette
                                                                                                        .neutral[500]
                                                                                                }
                                                                                            />
                                                                                        </Box>
                                                                                    )}
                                                                                </Box>
                                                                            )}
                                                                        </Box>
                                                                        {isSelected && (
                                                                            <Box
                                                                                display="flex"
                                                                                flexDirection="row"
                                                                                alignItems="center"
                                                                                sx={{
                                                                                    flexWrap: "wrap",
                                                                                    "@media (min-width: 992px)": {
                                                                                        flexWrap: "nowrap",
                                                                                        gap: 1,
                                                                                    },
                                                                                }}
                                                                                width="100%">
                                                                                <RadioWithLabel
                                                                                    id={`is_partner_${item.sub_category?.id}_${product.id}`}
                                                                                    label="Current Partner"
                                                                                    value="I am a current partner"
                                                                                    labelProps={{
                                                                                        color: "#000",
                                                                                        variant: "body3",
                                                                                        whiteSpace: "nowrap",
                                                                                    }}
                                                                                    isRequired
                                                                                    validateOnTheFly
                                                                                />
                                                                                <RadioWithLabel
                                                                                    id={`is_partner_${item.sub_category?.id}_${product.id}`}
                                                                                    label="Looking for Information"
                                                                                    value="I am looking for information"
                                                                                    labelProps={{
                                                                                        color: "#000",
                                                                                        variant: "body3",
                                                                                        whiteSpace: "nowrap",
                                                                                    }}
                                                                                    isRequired
                                                                                    validateOnTheFly
                                                                                />
                                                                            </Box>
                                                                        )}
                                                                        {selectedIsPartner && (
                                                                            <>
                                                                                <Box
                                                                                    sx={{
                                                                                        display: "flex",
                                                                                        flexDirection: "column",
                                                                                        alignItems: "start",
                                                                                        marginLeft: "0px",
                                                                                    }}>
                                                                                    <div
                                                                                        className="d-flex"
                                                                                        style={{width: "100%"}}>
                                                                                        <CheckboxWithLabel
                                                                                            id={`sold_by_distributor_${item.sub_category?.id}_${product.id}`}
                                                                                            label={
                                                                                                <Typography
                                                                                                    variant="body3"
                                                                                                    color={
                                                                                                        theme.palette
                                                                                                            .neutral[800]
                                                                                                    }>
                                                                                                    {
                                                                                                        DISTRIBUTOR_INFO_TEXT
                                                                                                    }
                                                                                                </Typography>
                                                                                            }
                                                                                            defaultChecked={false}
                                                                                            formControlSx={{
                                                                                                display: "flex",
                                                                                                alignItems: "start",
                                                                                                flexShrink: 1,
                                                                                            }}
                                                                                            labelProps={{
                                                                                                sx: {
                                                                                                    display: "block",
                                                                                                },
                                                                                            }}
                                                                                        />
                                                                                    </div>
                                                                                    {soldByDistributorWatcher && (
                                                                                        <VendorSearch
                                                                                            id={`distributor_id_${item.sub_category?.id}_${product.id}`}
                                                                                            placeholder="Search Distributors or Resellers"
                                                                                            label="Distributor/Reseller"
                                                                                            clearOnSelect={false}
                                                                                            clearOnBlur={false}
                                                                                            isRequired={methods.watch(
                                                                                                `sold_by_distributor_${item.sub_category?.id}_${product.id}`,
                                                                                            )}
                                                                                            validateOnTheFly
                                                                                            allVendorTypes={true}
                                                                                            // onValueChange={
                                                                                            //     handleDistributorChanged
                                                                                            // }
                                                                                        />
                                                                                    )}
                                                                                </Box>
                                                                            </>
                                                                        )}
                                                                    </BorderedBox>
                                                                );
                                                            })
                                                        ) : (
                                                            <Box
                                                                display="flex"
                                                                padding={1}
                                                                justifyContent="center"
                                                                alignItems="center"
                                                                gap={2}
                                                                alignSelf="stretch"
                                                                bgcolor={theme.palette.neutral[200]}
                                                                borderRadius={1}>
                                                                <Typography
                                                                    fontInter
                                                                    textAlign="center"
                                                                    fontWeight={500}
                                                                    variant="body4"
                                                                    color={theme.palette.neutral[600]}>
                                                                    No product added yet
                                                                </Typography>
                                                            </Box>
                                                        )}
                                                        {hasEditAccess && (
                                                            <Box
                                                                display="flex"
                                                                padding={1}
                                                                justifyContent="center"
                                                                alignItems="center"
                                                                gap={2}
                                                                alignSelf="stretch"
                                                                bgcolor={theme.palette.blue[100]}
                                                                borderRadius={1}
                                                                sx={{cursor: "pointer"}}
                                                                onClick={() => {
                                                                    props.onOpenAddToStack?.(item.sub_category?.id);
                                                                }}>
                                                                <Typography
                                                                    fontInter
                                                                    textAlign="center"
                                                                    variant="body3"
                                                                    fontWeight={700}
                                                                    color={theme.palette.blue[600]}>
                                                                    <AddOutlinedIcon /> Add a Product
                                                                </Typography>
                                                            </Box>
                                                        )}
                                                    </AccordionDetails>
                                                </Accordion>
                                            );
                                        })}
                                </>
                            )}
                        </Box>
                    </FormProvider>
                }
            />
        </ErrorBoundary>
    );
};

export default AddCorporateRecommendedDialog;
