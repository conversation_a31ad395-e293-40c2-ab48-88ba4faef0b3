import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import Box from "@mui/material/Box";
import Link from "@mui/material/Link";
import useTheme from "@mui/material/styles/useTheme";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useNavigate} from "react-router-dom";
import {MuiCalendarIcon} from "../../../components/Icons/muicalendar.icon";
import CustomLink from "../../../components/Links/CustomLink.component";
import {CHANNEL_PROGRAM_COMPANY_ID, DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import {eventService} from "../../../services/event.service";
import {DATE_HUGE_WITH_DATE_FIRST, formatDate} from "../../../utils/formatDate";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";
import CardComponent from "../../Cards/Card.component";
import styles from "../UpcomingEventsWidget/UpcomingEvents.module.sass";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import {useEffect} from "react";

interface IProps {
    company_id?: string;
    isMSP?: boolean;
    filterById?: string;
    isVisibleCallback?: (isVisible: boolean) => void;
}

interface IPartnerEvent {
    event_name: string;
    start_date: string;
    end_date: string;
    in_person_or_virtual: string;
    link: string;
    subject: {
        avatar: string;
        friendly_url: string;
        name: string;
        is_distributor?: boolean;
    };
    organization: string;
    cp_event: boolean;
}
const UpcomingEventsWidget = (props: IProps) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const getEventCategoryText = (category, type) => {
        switch (category) {
            case "virtual":
            case "webinar":
            case "virtual_product_demo":
                return type === "class" ? styles.virtualEvent : "Virtual Event";
            default:
                return type === "class" ? styles.inPerson : "In Person Event";
        }
    };

    const partnerEventsQuery = useQuery<IPartnerEvent[] | undefined>(
        ["partnerEvents", props?.company_id],
        async () => {
            const promiseToReturn: Promise<IPartnerEvent[] | any> = new Promise((resolve, reject) =>
                eventService
                    .getPartnersEventData(props?.company_id!, {
                        event_company_id: props.filterById,
                        exclude_cp_events: props.filterById ? props.filterById !== CHANNEL_PROGRAM_COMPANY_ID : false,
                    })
                    .then((r: AxiosResponse<IPartnerEvent[]>) => resolve(r.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(props?.company_id) && !!Boolean(props?.isMSP),
        },
    );
    const partnerEvents = partnerEventsQuery.data;

    useEffect(() => {
        if (!partnerEvents?.length && !partnerEventsQuery.isLoading) props.isVisibleCallback?.(false);
        else if (!partnerEventsQuery.isLoading && partnerEvents?.length) props.isVisibleCallback?.(true);
    }, [partnerEventsQuery.isLoading, partnerEvents]);

    return partnerEvents && partnerEvents.length > 0 ? (
        <CardComponent
            sx={{maxWidth: "350px !important", marginTop: 0}}
            elevation={0}
            contentStyles={{
                maxHeight: "500px !important",
                overflowY: "auto !important",
                padding: "0px 16px !important",
            }}
            cardActions={
                <div className="d-flex align-items-center justify-content-center m-auto w-100" style={{padding: "8px"}}>
                    <MuiButtonComponent
                        onClick={() => navigate(routeConfig.IndustryCalendar.path)}
                        variant="outlined"
                        color="secondary"
                        id={`seeAllEvents`}
                        sx={{
                            width: "100%",
                        }}>
                        See all Industry Events
                    </MuiButtonComponent>
                </div>
            }
            title={
                <div className="d-flex align-items-center">
                    <div className={styles.calendarIcon}>
                        <MuiCalendarIcon size={24} />
                    </div>
                    <Typography
                        fontWeight={"500 !important"}
                        fontInter
                        variant={"subtitle1"}
                        color={theme.palette.neutral[800]}>
                        Upcoming Events
                    </Typography>
                    <CPTooltip
                        title={
                            "To explore further details about the event, simply click the event listed below. Alternatively, click on See All Events to browse through the calendar."
                        }
                        placement="bottom">
                        <InfoOutlinedIcon htmlColor={theme.palette.neutral[600]} sx={{marginLeft: 1}} />
                    </CPTooltip>
                </div>
            }>
            {partnerEvents?.map((event, index) => {
                return (
                    <Box key={index} paddingY={"12px"}>
                        <Box display={"flex"} alignItems={"center"}>
                            <Box display={"flex"}>
                                <CustomLink
                                    target="_blank"
                                    to={routeConfig.VendorProfile.path.replace(":id", event?.subject?.friendly_url)}>
                                    <AvatarComponentV2
                                        size={50}
                                        isCompany
                                        isRedirectEnabled={false}
                                        user={{
                                            avatar: event.cp_event
                                                ? "/Media/short-logo.png"
                                                : event?.subject?.avatar || "",
                                            type: "",
                                            profile_type: "",
                                            friendly_url: event?.subject?.friendly_url,
                                            name: event?.subject?.name,
                                            is_distributor: event?.subject?.is_distributor,
                                        }}
                                        showProfileType={!event.cp_event}
                                        profileTypeMinimized
                                        border={event.cp_event ? "2px solid var(--orange-400, #FF7B45)" : ""}
                                        title={event?.organization}
                                    />
                                </CustomLink>
                            </Box>
                            <Box
                                display={"flex"}
                                flexDirection={"column"}
                                alignItems={"start"}
                                marginLeft={2}
                                gap={"4px"}
                                marginTop={0.5}>
                                <Typography
                                    fontInter
                                    fontWeight={600}
                                    variant={"subtitle3"}
                                    color={theme.palette.neutral[600]}>
                                    {formatDate(event?.start_date, DATE_HUGE_WITH_DATE_FIRST)}
                                </Typography>
                                <CPTooltip title="Click to register" placement="bottom">
                                    <Link
                                        textTransform={"capitalize"}
                                        variant="button"
                                        id={"goToevent"}
                                        href={event?.link}
                                        target="_blank">
                                        <Typography
                                            fontInter
                                            color={theme.palette.blue["main"]}
                                            variant={"body3"}
                                            fontWeight={"500 !important"}>
                                            {event?.event_name?.trim()?.substring(0, 45)}
                                            {event?.event_name?.length > 46 && "..."}
                                        </Typography>
                                    </Link>
                                </CPTooltip>
                                <span
                                    className={`${styles.eventBadge} ${
                                        event.cp_event
                                            ? styles.channelProgram
                                            : getEventCategoryText(event.in_person_or_virtual, "class")
                                    }`}>
                                    {event.cp_event
                                        ? "Channel Program"
                                        : getEventCategoryText(event.in_person_or_virtual, "text")}
                                </span>
                            </Box>
                        </Box>
                    </Box>
                );
            })}
        </CardComponent>
    ) : null;
};
export default UpcomingEventsWidget;
