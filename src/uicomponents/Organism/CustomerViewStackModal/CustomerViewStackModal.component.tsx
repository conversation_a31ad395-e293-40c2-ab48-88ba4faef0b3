import { DeleteOutlined, EditOutlined } from "@mui/icons-material";
import { Box } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import { useEffect } from "react";
import { IStackCategorization } from "../../../Interfaces/myStack.interface";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import CategoryAutocomplete from "../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";

interface CustomerViewStackModalProps {
    open: boolean;
    stack?: IStackCategorization;
    removing: boolean;
    hasEditAccess: boolean;
    companyType?: string;
    onClose: () => void;
    onRemove: (stack: IStackCategorization) => void;
    onEdit: (stack: IStackCategorization) => void;
}

export default function CustomerViewStackModal({
    open,
    stack,
    removing,
    hasEditAccess,
    companyType,
    onClose,
    onRemove,
    onEdit,
}: CustomerViewStackModalProps) {
    const viewMethods = useForm<IStackCategorization>();

    useEffect(() => {
        if (stack) {
            viewMethods.reset(stack);
        }
    }, [stack]);

    if (!stack) return null;

    return (
        <ModalComponent
            id="view-stack"
            justifyButtons="flex-start"
            open={open}
            onClose={onClose}
            contentClassName="mt-0"
            modalTheme="blue"
            maxWidth="md"
            dialogSx={{
                ".MuiDialog-paper": {
                    overflow: "auto",
                    maxWidth: "800px !important",
                    maxHeight: "100%",
                    width: "100%",
                },
                ".MuiDialogContent-root": {
                    overflow: "auto",
                },
            }}
            okButtonText="EDIT"
            okButtonStartIcon={<EditOutlined sx={{ fontSize: "18px" }} />}
            showAdditionalBtn
            showBottom={hasEditAccess}
            additionalBtnVariant="outlined"
            additionalBtnText="REMOVE"
            title="Edit"
            onAdditionalBtnClick={() => onRemove(stack)}
            additionalBtnProps={{
                color: "error",
            }}
            loading={removing}
            additionalBtnStartIcon={
                <DeleteOutlined
                    color="error"
                    sx={{
                        fontSize: "18px",
                    }}
                />
            }
            customTitle={
                <Box
                    sx={{
                        display: "flex",
                        gap: 0.5,
                        flexDirection: "column",
                    }}>
                    <Typography size={24} weight={600} color="neutral.800">
                        {stack.stack_company.name}
                    </Typography>
                    <Typography size={16} weight={600} color="neutral.600">
                        {stack.product.name}
                    </Typography>
                </Box>
            }
            showCancelButton={false}
            onSuccess={() => onEdit(stack)}
            content={
                <>
                    <FormProvider {...viewMethods}>
                        <Box
                            sx={{
                                display: "flex",
                                gap: 3,
                                flexWrap: "wrap",
                                "&>*": {
                                    flex: "1 1 calc(50% - 12px)",
                                },
                            }}>
                            <CategoryAutocomplete
                                categoryInputId="category.parent_id"
                                subCategoryInputId="category.id"
                                company_type={companyType}
                                isCustomer
                                isRequired
                                readOnly
                                wrapperSx={{
                                    flex: "1 1 100%",
                                    gap: 3,
                                    display: "flex",
                                    "&>*": {
                                        flex: "1 1 calc(50% - 12px)",
                                        minWidth: "calc(50% - 12px)!important",
                                    },
                                }}
                            />
                            <TextBoxComponent
                                id="product.description"
                                multiline
                                rows={3}
                                label="Description"
                                placeholder="Enter product description"
                                readOnly
                                sx={{
                                    maxHeight: "160px",
                                    overflowY: "auto",
                                    pointerEvents: "auto",
                                }}
                                containerSx={{
                                    "& label": {
                                        background: "white!important",
                                    },
                                }}
                            />
                        </Box>
                    </FormProvider>
                </>
            }
        />
    );
} 