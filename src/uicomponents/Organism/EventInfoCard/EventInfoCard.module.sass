.eventCTA
    border: 1px solid var(--cpNavy)
    padding: 12px
    border-radius: var(--Radius)
    flex: 1 0 calc(50% - .75rem)
    h4
        font-size: 17px!important
    p
        font-size: 14px!important
        margin: 4px 0!important

.eventDescriptionText
    p
        font: var(--Inter)
        color: var(--cpNavyShade4)

.vendorLegend
    display: flex
    gap: 4px
    align-items: center
    span
        width: 12px
        height: 12px
        border-radius: 50%
        background: var(--cpGreen)
.cpLegend
    display: flex
    gap: 4px
    align-items: center
    span
        width: 12px
        height: 12px
        border-radius: 50%
        background: var(--AccentColor)
.otherLegend
    display: flex
    gap: 4px
    align-items: center
    span
        width: 12px
        height: 12px
        border-radius: 50%
        background: var(--cpNavy)

.profilePicture
    height: 50px
    width: 50px
    marginTop: 12px
    border-radius: 50%

.typeBanner
    top: 0
    right: 0
    min-width: 30px
    height: 10px
    border-radius: 10px

.eventContainer
    border: 1px solid var(--cpNavy)
    border-radius: var(--Radius)
    padding: 8px
    margin: 8px 0
    background: white

.searchInputField
  width: 50%

.highlightEvent
    border: 5px solid var(--cpNavy)
    h4
        font-weight: bolder !important

.descriptionStyle
    font: var(--Inter)
    color: var(--cpNavyShade4)
    font-size: 15px !important
    font-weight: normal !important
    margin-bottom: 10px !important

.infoStyle
    font: var(--Inter) !important
    color: var(--cpNavyShade4) !important
    font-size: 15px !important
    font-weight: normal !important

.presentedBy
    margin-left: 8px
    font-size: 14px
    padding: 0px
    margin-bottom: 0px

.registerLinkStyle
    color: var(--AccentColor)!important
    font-weight: 600
    font-size: 14px !important
    cursor: pointer
    *
        color: var(--AccentColor)!important
        fill: var(--AccentColor)!important
