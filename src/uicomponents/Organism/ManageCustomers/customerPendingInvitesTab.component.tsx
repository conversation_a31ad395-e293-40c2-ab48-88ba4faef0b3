import useTheme from "@mui/material/styles/useTheme";
import {useCallback, useMemo, useRef, useState} from "react";
import useActiveCompany from "../../../hooks/useActiveCompany";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import {IndeterminateCheckbox, TableV2Component} from "../../../components/Table/TableV2.component";
import ResendInviteButton from "../../Molecules/ResendInviteButton/ResendInviteButton.component";
import {formatDate, FULL_DATETIME_SHORT_MONTH_TIMEZONE} from "../../../utils/formatDate";
import Loader from "../../../utils/loader";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import {IMspClient} from "../../../Interfaces/msp.interface";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import Button from "../../Atoms/Button/Button.Component";
import {pluralizeString} from "../../../utils/formatString.util";
import RefreshIcon from "@mui/icons-material/Refresh";
import {useDebounce} from "../../../hooks/useDebounce";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {mspService} from "../../../services/msp.service";
import {useQuery} from "@tanstack/react-query";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {buildFilters, clearFilters} from "../../../utils/filters.util";
import {IObject} from "../../../models/ObjectAny.interface";
import {CPSortType} from "../../../enums/sortType.enum";
import BulkSelectContainer from "../../../components/Table/bulkSelectContainer.component";

const CustomerPendingInvitesTab = () => {
    const [selectedRows, setSelectedRows] = useState<string[]>([]);
    const [search, setSearch] = useState<string>("");
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(20);
    const [filters, setFilters] = useState<INewFilters>({});
    const [isBulkResending, setIsBulkResending] = useState(false);
    const [isBulkDeleting, setIsBulkDeleting] = useState(false);
    const [deletingRows, setDeletingRows] = useState<Record<string, boolean>>({});
    const theme = useTheme();
    const notify = useNotification();
    const {activeCompany} = useActiveCompany();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([
        PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_UPDATE,
        PERMISSION_GROUPS.ADMIN_MSP_CLIENTS_MGNMT_READ,
    ]);
    const debouncedSearch = useDebounce(search, 500);
    const containerRef = useRef<HTMLDivElement | null>(null);

    const customerPendingInvites = useQuery<IPaginationData<any[]>>(
        [
            "customer-pending-invites",
            activeCompany?.friendly_url,
            debouncedSearch,
            itemsPerPage,
            page,
            JSON.stringify(filters),
        ],
        async () => {
            const cleanedFilters = {...filters};
            if (cleanedFilters?.sent_at) {
                delete cleanedFilters.sent_at;
            }
            const promiseToReturn: Promise<IPaginationData<IMspClient[]>> = new Promise((resolve, reject) =>
                mspService.getPendingCustomerInvites(
                    activeCompany?.friendly_url!,
                    {
                        items_per_page: itemsPerPage,
                        page,
                        search_word: debouncedSearch,
                        dynamic: clearFilters({
                            ...cleanedFilters,
                            ...((filters?.sent_at as IObject)?.start_date && (filters?.sent_at as IObject)?.end_date
                                ? {
                                      start_date: (filters.sent_at as IObject).start_date,
                                      end_date: (filters.sent_at as IObject).end_date,
                                  }
                                : {}),
                        }),
                    },
                    r => {
                        resolve(r.data);
                    },
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            enabled: !!activeCompany?.friendly_url,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const customerPendingInvitesFilters = useQuery(
        ["customer-pending-invites-filters", activeCompany?.friendly_url, debouncedSearch],
        async () => {
            const promiseToReturn = new Promise((resolve, reject) =>
                mspService.getPendingCustomerInvitesFilters(
                    activeCompany?.friendly_url!,
                    r => {
                        resolve(buildFilters(r.data?.filters, {}));
                    },
                    reject,
                ),
            );
            return promiseToReturn;
        },
        {
            enabled: !!activeCompany?.friendly_url,
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );

    const handleDeleteInvitation = async invite => {
        const answer = await getUserConfirmation("Delete?", {
            mui: true,
            title: "Remove Invitation?",
            modalTheme: "error",
            okButtonText: (
                <Box gap={1} display="flex" alignItems="center">
                    <DeleteOutlinedIcon sx={{height: 18, width: 18}} /> REMOVE
                </Box>
            ),
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Typography variant="subtitle2" fontInter fontWeight={"600 !important"}>
                        Are you sure you want to remove this invitation?
                    </Typography>
                    <Typography variant="body3" color={theme.palette.neutral[800]}>
                        {invite.email}
                    </Typography>
                </Box>
            ),
        });
        if (!answer.value) return;
        setDeletingRows({...deletingRows, [invite.id]: true});
        mspService.deleteCustomerInvite(
            activeCompany?.friendly_url!,
            [invite.id],
            () => {
                customerPendingInvites.refetch();
                notify("Invite deleted successfully", "Success");
                setDeletingRows({...deletingRows, [invite.id]: false});
            },
            err => {
                notify(getErrorFromArray(err), "Error");
                setDeletingRows({...deletingRows, [invite.id]: false});
            },
        );
    };

    const handleToggleRow = useCallback((rowData: any) => {
        setSelectedRows(prevRows =>
            prevRows.includes(rowData.id) ? prevRows.filter(row => row !== rowData.id) : [...prevRows, rowData.id],
        );
    }, []);

    const checkboxHeader = useCallback(
        header => {
            const rowsLength = header?.rows?.length || 0;
            const isIndeterminate =
                Array.isArray(selectedRows) && selectedRows.length > 0 && selectedRows.length < rowsLength;

            return (
                <IndeterminateCheckbox
                    indeterminate={isIndeterminate}
                    checked={!!selectedRows?.length}
                    onChange={() => {
                        if (selectedRows.length > 0) {
                            setSelectedRows([]);
                        } else {
                            const newSelectedRows = (header?.rows || [])
                                .map(row => row.original?.id || null)
                                .filter(Boolean);
                            setSelectedRows(newSelectedRows);
                        }
                    }}
                />
            );
        },
        [selectedRows],
    );

    const checkboxColumn = useCallback(
        ({row}) => {
            const isSelected = selectedRows.includes(row.original.id);
            return (
                <IndeterminateCheckbox
                    indeterminate={false}
                    checked={isSelected}
                    onChange={() => handleToggleRow(row.original)}
                    sx={{svg: {height: "24px !important", width: "24px !important"}}}
                />
            );
        },
        [selectedRows, handleToggleRow],
    );

    const resendCell = useCallback(
        ({row}) => (
            <Box display="flex" alignItems="center" justifyContent="center">
                <ResendInviteButton
                    key={row.original.id}
                    originalEmail={row.original.email}
                    subject={`Invitation to Join ${row.original.child_company?.name || "Company"}`}
                    sentDates={[
                        row.original.created_at,
                        ...(row.original.last_sent_at && row.original.last_sent_at !== row.original.created_at
                            ? [row.original.last_sent_at]
                            : []),
                    ]}
                    resendType="customer"
                    inviteId={row.original.id}
                />
            </Box>
        ),
        [],
    );

    const columns = useMemo(
        () => [
            ...(hasEditAccess ? [{id: "checkbox", Header: checkboxHeader, Cell: checkboxColumn}] : []),
            {
                accessor: "email",
                Header: "Email",
                sortable: true,
            },
            {
                acccessor: "customer",
                Header: "Customer",
                id: "customer",
                sortable: true,
                Cell: ({row}) => {
                    return <Typography>{row.original.child_company.name}</Typography>;
                },
            },
            {
                accessor: "role",
                Header: "Role",
                sortable: true,
                Cell: ({row}) => {
                    return <Typography>{row.original.role.display_name}</Typography>;
                },
            },
            {
                accessor: "last_sent_at",
                Header: "Sent On",
                sortable: true,
                Cell: ({row}) => {
                    return (
                        <Typography>
                            {formatDate(
                                row.original.last_sent_at || row.original.created_at,
                                FULL_DATETIME_SHORT_MONTH_TIMEZONE,
                            )}
                        </Typography>
                    );
                },
            },
            ...(hasEditAccess
                ? [
                      {
                          accessor: "action_resend",
                          Header: "Resend",
                          Cell: resendCell,
                          width: 50,
                      },
                      {
                          accessor: "action_remove",
                          Header: "Remove",
                          Cell: ({row}) => {
                              const isDeleting = deletingRows[row.original.id];
                              return isDeleting ? (
                                  <Loader inline loading />
                              ) : (
                                  <Box
                                      sx={{cursor: "pointer", display: "flex", justifyContent: "center"}}
                                      tooltip={{title: "Remove"}}
                                      onClick={() => handleDeleteInvitation(row.original)}>
                                      <DeleteOutlinedIcon
                                          htmlColor={theme.palette.error[600]}
                                          sx={{height: 24, width: 24}}
                                      />
                                  </Box>
                              );
                          },
                          width: 50,
                      },
                  ]
                : []),
        ],
        [selectedRows, handleToggleRow, hasEditAccess, deletingRows],
    );

    const handleResendInvites = async () => {
        const answer = await getUserConfirmation(
            <>
                Are you sure you want to resend the selected {pluralizeString("invite", selectedRows.length)}?
                <Typography variant="body3" fontWeight={600}>
                    {selectedRows.map(
                        (id, idx) =>
                            customerPendingInvites.data?.data?.find(inv => inv.id === id)?.email +
                            (idx < selectedRows.length - 1 ? ", " : ""),
                    )}
                </Typography>
            </>,
            {
                okButtonText: "RESEND",
                modalTheme: "blue",
                mui: true,
                title: `Resend ${pluralizeString("Invite", selectedRows.length)}`,
            },
        );
        if (answer.value) {
            setIsBulkResending(true);
            mspService.bulkResendCustomerInvite(
                activeCompany?.friendly_url || "",
                selectedRows,
                () => {
                    customerPendingInvites.refetch();
                    notify("Invites resent successfully", "Success");
                    setSelectedRows([]);
                    setIsBulkResending(false);
                },
                err => {
                    notify(getErrorFromArray(err), "Error");
                    setIsBulkResending(false);
                },
            );
        }
    };

    const handleDeleteInvites = async (invites: string[], isBulk: boolean) => {
        const answer = await getUserConfirmation(
            <>
                Are you sure you want to remove the selected {pluralizeString("invite", invites.length)}?
                <Typography variant="body3" fontWeight={600}>
                    {invites.map(
                        (id, idx) =>
                            customerPendingInvites.data?.data?.find(inv => inv.id === id)?.email +
                            (idx < invites.length - 1 ? ", " : ""),
                    )}
                </Typography>
            </>,
            {
                okButtonText: "REMOVE",
                modalTheme: "error",
                mui: true,
                title: `Remove ${pluralizeString("Invite", invites.length)}`,
            },
        );
        if (answer.value) {
            setDeletingRows(
                invites.reduce((acc, id) => {
                    acc[id] = true;
                    return acc;
                }, {}),
            );
            setIsBulkDeleting(true);
            mspService.deleteCustomerInvite(
                activeCompany?.friendly_url!,
                invites,
                () => {
                    customerPendingInvites.refetch();
                    notify("Invites deleted successfully", "Success");
                    if (isBulk) {
                        setSelectedRows([]);
                    }
                    setDeletingRows(
                        invites.reduce((acc, id) => {
                            acc[id] = false;
                            return acc;
                        }, {}),
                    );
                    setIsBulkDeleting(false);
                },
                err => {
                    notify(getErrorFromArray(err), "Error");
                    setDeletingRows(
                        invites.reduce((acc, id) => {
                            acc[id] = false;
                            return acc;
                        }, {}),
                    );
                    setIsBulkDeleting(false);
                },
            );
        }
    };

    const handleSortChange = (sortBy: string, order_by: string) => {
        setFilters({...filters, sort: `sorting_${order_by}__${sortBy}`});
    };

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={3} ref={containerRef}>
                <PageInnerHeader
                    id="customers-pending-invites-header"
                    searchState={[search, setSearch]}
                    filters={customerPendingInvitesFilters.data}
                    filterState={[filters, setFilters as (filters: INewFilters | undefined) => void]}
                    growSearch
                />
                <TableV2Component
                    id="pending-customer-invites-table"
                    headerBackground={theme.palette.blue[100]}
                    columns={columns}
                    customData={customerPendingInvites.data?.data || []}
                    isLoading={customerPendingInvites.isLoading}
                    onSortChange={(sort: CPSortType, order_by: string | number | symbol) =>
                        handleSortChange(sort, order_by as string)
                    }
                    tableStyle={{marginBottom: 0}}
                />
                <BulkSelectContainer
                    selectedRows={selectedRows}
                    itemText="customer"
                    containerRef={containerRef}
                    children={
                        <>
                            <Button
                                id="resend-all"
                                variant="contained"
                                onClick={handleResendInvites}
                                color="blue"
                                disabled={isBulkResending}>
                                {isBulkResending ? (
                                    <>
                                        <Loader inline loading />
                                        Resending...
                                    </>
                                ) : (
                                    <>
                                        <RefreshIcon sx={{marginRight: 1}} />
                                        Resend All
                                    </>
                                )}
                            </Button>
                            <Button
                                id="delete-many"
                                type="button"
                                variant="outlined"
                                color="error"
                                disabled={isBulkDeleting}
                                onClick={() => handleDeleteInvites(selectedRows, true)}>
                                {isBulkDeleting ? (
                                    <>
                                        <Loader inline loading />
                                        Removing...
                                    </>
                                ) : (
                                    <>
                                        <DeleteOutlinedIcon
                                            color="error"
                                            sx={{marginRight: 1, width: "16px", height: "16px"}}
                                        />
                                        Remove All
                                    </>
                                )}
                            </Button>
                        </>
                    }
                />
                <CustomPagination
                    metadata={customerPendingInvites.data?.meta || {total: 0, last_page: 0}}
                    page={page}
                    setPage={setPage}
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={setItemsPerPage}
                />
            </Box>
        </ErrorBoundary>
    );
};

export default CustomerPendingInvitesTab;
