import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import {Box, Typography, useTheme} from "@mui/material";
import {useEffect, useState} from "react";
import {useFormContext} from "react-hook-form";
import {DISTRIBUTOR_INFO_TEXT} from "../../../constants/distributors.constant";
import useActiveCompany from "../../../hooks/useActiveCompany";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import CheckboxWithLabel from "../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import CompanyClientsAutocomplete from "../../Molecules/CompanyClientsAutocomplete/CompanyClientsAutocomplete.component";
import {IProductSearchOption} from "../../Molecules/ProductSearch/productSearch.component";
import RadioWithLabel from "../../Molecules/RadioWithLabel/RadioWithLabel.component";
import VendorSearch from "../../Molecules/VendorSearch/vendorSearch.component";

export interface ISelectedProductProps {
    product: IProductSearchOption;
    sectionKey: string;
    onRemove: (sectionKey: string, productId: string) => void;
    isBrandStack?: boolean;
}

export default function SelectedProduct({product, sectionKey, onRemove, ...props}: ISelectedProductProps) {
    const theme = useTheme();
    const methods = useFormContext();
    const {watch} = methods;
    const [distributor, setDistributor] = useState<any | null>(null);
    const {activeCompany} = useActiveCompany();
    const canManageClients = activeCompany?.manage_clients && !props.isBrandStack;
    const soldByDistributorWatcher = watch(`sold_by_distributor_${sectionKey}_${product.id}`);
    const selectedIsPartner = watch(`is_partner_${sectionKey}_${product.id}`) === "I am a current partner";

    useEffect(() => {
        if (!soldByDistributorWatcher) {
            delete methods.formState.errors[`distributor_id_${sectionKey}_${product.id}`];
            methods.unregister(`distributor_id_${sectionKey}_${product.id}`, undefined);
            setDistributor(null);
        }
    }, [soldByDistributorWatcher]);

    const handleDistributorChanged = (_, __, distributor) => {
        setDistributor(distributor);
    };

    return (
        <Box
            {...props}
            className="d-flex flex-column mb-2"
            padding={1}
            borderRadius={2}
            style={{
                border: `1px solid ${theme.palette.neutral[300]}`,
            }}>
            <div className="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-2 mb-2">
                <div className="d-flex flex-row align-items-center w-100">
                    <AvatarComponentV2
                        isCompany
                        user={distributor?.avatar?.user || product.parent}
                        showProfileType
                        profileTypeMinimized
                        size={32}
                        isRedirectEnabled={false}
                        containerSx={{
                            padding: 0,
                            marginRight: 16,
                        }}
                    />

                    <Typography
                        key={product.id}
                        variant="body3"
                        sx={{overflowWrap: "anywhere", marginLeft: 1}}
                        color={theme.palette.blue[800]}>
                        {!!distributor ? product.product_name : product.label}
                    </Typography>
                    <div
                        className="d-block d-lg-none ms-auto"
                        onClick={() => onRemove(sectionKey, product.id)}
                        style={{cursor: "pointer"}}>
                        <DeleteOutlineOutlinedIcon color="error" />
                    </div>
                </div>
                <div className="d-flex flex-column flex-lg-row align-items-start align-items-lg-center ps-5 ms-none ms-lg-auto">
                    {!props.isBrandStack && (
                        <>
                            <RadioWithLabel
                                id={`is_partner_${sectionKey}_${product.id}`}
                                label="Current Partner"
                                value="I am a current partner"
                                labelProps={{
                                    color: "#000",
                                    variant: "body3",
                                    whiteSpace: "nowrap",
                                }}
                                isRequired={methods.watch("sub_category_" + sectionKey)}
                                validateOnTheFly
                            />
                            <RadioWithLabel
                                id={`is_partner_${sectionKey}_${product.id}`}
                                label="Looking for Information"
                                value="I am looking for information"
                                labelProps={{
                                    color: "#000",
                                    variant: "body3",
                                    whiteSpace: "nowrap",
                                }}
                                isRequired={methods.watch("sub_category_" + sectionKey)}
                                validateOnTheFly
                            />
                        </>
                    )}
                    <div
                        className="d-none d-lg-block"
                        onClick={() => onRemove(sectionKey, product.id)}
                        style={{cursor: "pointer"}}>
                        <DeleteOutlineOutlinedIcon color="error" />
                    </div>
                </div>
            </div>
            {(props.isBrandStack || selectedIsPartner) && (
                <>
                    <Box
                        display="flex"
                        flexDirection={{xs: "column", md: props.isBrandStack ? "column" : "row"}}
                        alignItems="flex-start"
                        marginLeft="none">
                        <div className="d-flex">
                            <CheckboxWithLabel
                                id={`sold_by_distributor_${sectionKey}_${product.id}`}
                                label={
                                    <Typography variant="body3" color={theme.palette.neutral[800]}>
                                        {props.isBrandStack
                                            ? "I would like to recommend a distributor or reseller"
                                            : DISTRIBUTOR_INFO_TEXT}
                                    </Typography>
                                }
                                defaultChecked={false}
                                formControlSx={{
                                    display: "flex",
                                    alignItems: "start",
                                    flexShrink: 1,
                                }}
                                labelProps={{
                                    sx: {
                                        display: "block",
                                    },
                                }}
                            />
                        </div>
                        {soldByDistributorWatcher && (
                            <VendorSearch
                                id={`distributor_id_${sectionKey}_${product.id}`}
                                placeholder="Search Distributors or Resellers"
                                label="Distributor/Reseller"
                                clearOnSelect={false}
                                clearOnBlur={false}
                                isRequired={methods.watch(`sold_by_distributor_${sectionKey}_${product.id}`)}
                                validateOnTheFly
                                allVendorTypes={true}
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                sx={{width: "100%"}}
                                onValueChange={handleDistributorChanged}
                            />
                        )}
                    </Box>
                    {canManageClients && (
                        <CompanyClientsAutocomplete
                            id={`client_ids_${sectionKey}_${product.id}`}
                            label="Customers Usage (optional)"
                            multiple
                            validateOnTheFly
                            msp_friendly_url={activeCompany?.friendly_url}
                            renderCheckboxes
                            disableCloseOnSelect
                            sx={{marginTop: 1}}
                        />
                    )}
                </>
            )}
        </Box>
    );
}
