import {Skeleton} from "@mui/material";
import useTheme from "@mui/material/styles/useTheme";
import {useQueryClient, type UseInfiniteQueryResult} from "@tanstack/react-query";
import {AxiosError} from "axios";
import {useMemo, useState} from "react";
import {Settings} from "react-slick";
import type {IFolder} from "../../../Interfaces/folders.interface";
import type {IPaginationData} from "../../../Interfaces/pagination.interface";
import {useInfiniteQueryObserver} from "../../../hooks/useInfinite";
import useNotification from "../../../hooks/useNotification";
import {folderServices} from "../../../services/folder.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import CPSlider from "../../Molecules/CPSlider/cpSlider.component";
import FolderItem from "./FolderItem.component";

interface IProps {
    foldersInfiniteQuery: UseInfiniteQueryResult<IPaginationData<IFolder[]> | undefined, unknown>;
    companyId?: string;
    seeAllFolders?: boolean;
    hasEditAccess?: boolean;
    onEditClick: (folder: IFolder) => void;
    onAddFilesClick: (folder: IFolder) => void;
    viewFolderDetails: (folder: IFolder) => void;
    onDownloadClick: (folder: IFolder) => void;
    onAddFolderClick?: () => void;
}

const emptyIsDeleting = {id: "", loading: false};

const Folders = (props: IProps) => {
    const notify = useNotification();
    const theme = useTheme();
    const [isDeleting, setIsDeleting] = useState<{id: string; loading: boolean}>(emptyIsDeleting);
    const {
        data: foldersData,
        refetch,
        isFetchingNextPage,
        hasNextPage,
        isLoading,
        fetchNextPage,
    } = props.foldersInfiniteQuery;
    const {lastElementRef} = useInfiniteQueryObserver(isFetchingNextPage, fetchNextPage, hasNextPage);
    const folders = useMemo(() => (foldersData?.pages.flatMap(page => page?.data) ?? []) as IFolder[], [foldersData]);
    const hasEditAccess = props.hasEditAccess;
    const queryClient = useQueryClient();

    const sliderExtraSettings: Settings = {
        slidesToShow: 3,
        slidesToScroll: 3,
    };

    const handleError = (error: AxiosError<any>) => {
        setIsDeleting({id: "", loading: false});
        notify(getErrorFromArray(error), "Error");
    };

    const handleDeleteSuccess = () => {
        setIsDeleting(emptyIsDeleting);
        notify("Folder deleted successfully.", "Success");
        refetch();
        queryClient.refetchQueries(["partnerPageFolders"]);
        queryClient.refetchQueries(["naviStackSavedDocumentsFolders"]);
        queryClient.refetchQueries(["allFoldersData"]);
    };

    const handleDeleteFolder = async (item: IFolder) => {
        if (!props.companyId) return;
        const answer = await getUserConfirmation("", {
            okButtonText: "DELETE",
            cancelButtonText: "CANCEL",
            title: "Delete Folder?",
            form: (
                <div className="d-flex flex-column align-items-center">
                    <Typography fontInter variant="body2" sx={{color: theme.palette.neutral[800]}}>
                        Deleting the folder will not delete the contents. All contents will still be available for view
                        and download.
                    </Typography>
                </div>
            ),
            mui: true,
            modalTheme: "error",
            justifyButtons: "flex-start",
        });
        if (answer.value) {
            setIsDeleting({id: item?.id, loading: true});
            folderServices.deleteFolder(props.companyId, item.id, handleDeleteSuccess, handleError);
        }
    };

    return folders && folders?.length > 0 ? (
        <>
            {props?.seeAllFolders ? (
                <>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "stretch",
                            flexWrap: "wrap",
                            gap: "16px",
                            "&>div": {
                                flex: "1 0 280px",
                                width: "100%",
                                maxWidth: "100%",
                                "@media (min-width: 500px)": {
                                    width: "calc(50% - 16px)",
                                    maxWidth: "calc(50% - 16px)",
                                },
                                "@media (min-width: 992px)": {
                                    width: "calc(33% - 16px)",
                                    maxWidth: "calc(33% - 16px)",
                                },
                            },
                        }}>
                        {folders?.map((v: IFolder) => {
                            return (
                                <FolderItem
                                    key={v.id}
                                    folderData={v}
                                    isDeleting={isDeleting?.id === v.id && isDeleting?.loading}
                                    hasEditAccess={hasEditAccess}
                                    onEditClick={props.onEditClick}
                                    onDeleteClick={handleDeleteFolder}
                                    viewFolderDetails={props.viewFolderDetails}
                                    onDownloadClick={props.onDownloadClick}
                                    onAddFilesClick={props.onAddFilesClick}
                                />
                            );
                        })}
                        <div
                            style={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                                marginTop: "3rem",
                            }}
                            ref={lastElementRef}>
                            {isFetchingNextPage && <Loader inline loading />}
                        </div>
                    </Box>
                </>
            ) : (
                <CPSlider {...sliderExtraSettings}>
                    {folders?.map((v: IFolder) => {
                        return (
                            <FolderItem
                                key={v.id}
                                folderData={v}
                                isDeleting={isDeleting?.id === v.id && isDeleting?.loading}
                                hasEditAccess={hasEditAccess}
                                onEditClick={props.onEditClick}
                                onDeleteClick={handleDeleteFolder}
                                viewFolderDetails={props.viewFolderDetails}
                                onDownloadClick={props.onDownloadClick}
                                onAddFilesClick={props.onAddFilesClick}
                            />
                        );
                    })}
                </CPSlider>
            )}
        </>
    ) : isLoading ? (
        <Box
            sx={{
                display: "flex",
                alignItems: "stretch",
                flexWrap: "wrap",
                gap: "16px",
                width: "100%",
            }}>
            {Array(3)
                .fill(null)
                .map((_, i) => (
                    <Skeleton
                        key={i}
                        sx={{
                            flex: "1 0 280px",
                            width: "100%",
                            maxWidth: "100%",
                            height: "200px",
                            "@media (min-width: 500px)": {
                                width: "calc(50% - 16px)",
                                maxWidth: "calc(50% - 16px)",
                            },
                            "@media (min-width: 992px)": {
                                width: "calc(33% - 16px)",
                                maxWidth: "calc(33% - 16px)",
                            },
                        }}
                    />
                ))}
        </Box>
    ) : (
        <>
            <AlertBanner
                variant="info"
                id="no-folder-alert"
                title={{text: "You don't have any folders yet."}}
                subTitle={{
                    text: (
                        <>
                            Now you can organize your content with folders!{" "}
                            <strong
                                style={{cursor: "pointer", fontWeight: "700", color: theme.palette.secondary[600]}}
                                onClick={props.onAddFolderClick}>
                                Add a folder now.
                            </strong>
                        </>
                    ),
                }}
            />
        </>
    );
};

export default Folders;
