import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import DownloadOutlined from "@mui/icons-material/DownloadOutlined";
import EditOutlined from "@mui/icons-material/EditOutlined";
import NoteAddOutlined from "@mui/icons-material/NoteAddOutlined";
import type {IFolder} from "../../../Interfaces/folders.interface";
import {ITag} from "../../../Interfaces/tags.interface";
import {FolderIcon} from "../../../components/Icons/folder.icon";
import Box from "../../Atoms/Box/Box.component";
import MuiCard from "../../Atoms/Cards/muiCard.component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps {
    folderData: IFolder;
    isDeleting?: boolean;
    hasEditAccess?: boolean;
    onEditClick: (folder: IFolder) => void;
    onDeleteClick: (folder: IFolder) => void;
    onDownloadClick: (folder: IFolder) => void;
    viewFolderDetails: (folder: IFolder) => void;
    onAddFilesClick: (folder: IFolder) => void;
}

const FolderItem = ({
    folderData,
    onEditClick,
    onDeleteClick,
    onDownloadClick,
    isDeleting,
    viewFolderDetails,
    hasEditAccess,
    onAddFilesClick,
}: IProps) => {
    const renderTags = (tags: ITag[]) => {
        if (tags?.length > 0) {
            const tagNames = tags.map((tag: ITag) => tag.name);
            return tagNames.join(", ");
        }
        return null;
    };
    return (
        <MuiCard
            onClickHeader={() => viewFolderDetails(folderData)}
            headerAvatar={
                <div style={{position: "relative"}}>
                    <FolderIcon size={56} color={folderData?.color ?? "#D4DAE2"} />
                    <Box
                        sx={theme => ({
                            border: `1px solid ${theme.palette.secondary[300]}`,
                            background: !!folderData?.contents_count
                                ? theme.palette.secondary.main
                                : theme.palette.neutral[200],
                            position: "absolute",
                            bottom: "0",
                            left: "50%",
                            color: !!folderData?.contents_count ? "white" : theme.palette.neutral[500],
                            borderRadius: "17px",
                            padding: "3px 6px",
                            fontSize: "11px",
                            fontFamily: "var(--Inter)",
                            lineHeight: "1",
                            fontWeight: "700",
                            transform: "translate(-50%)",
                        })}>
                        {folderData?.contents_count}
                    </Box>
                </div>
            }
            headerSubtitle={{text: folderData?.description}}
            headerTitle={{text: folderData?.folder_name, fontSize: "16px !important"}}
            threeDotActions={[
                ...(hasEditAccess
                    ? [
                          {
                              id: "add-files-" + folderData.id,
                              children: (
                                  <>
                                      <NoteAddOutlined color="neutral" /> Add files
                                  </>
                              ),
                              onClick: () => onAddFilesClick(folderData),
                          },
                          {
                              id: "edit-" + folderData.id,
                              children: (
                                  <>
                                      <EditOutlined color="neutral" /> Edit
                                  </>
                              ),
                              onClick: () => onEditClick(folderData),
                          },
                          {
                              id: "delete-" + folderData.id,
                              children: isDeleting ? (
                                  <>Deleting...</>
                              ) : (
                                  <>
                                      <DeleteOutlined color="neutral" /> Delete
                                  </>
                              ),
                              onClick: isDeleting ? undefined : () => onDeleteClick(folderData),
                          },
                      ]
                    : []),
                ...(folderData?.contents_count
                    ? [
                          {
                              id: "download-" + folderData.id,
                              children: (
                                  <>
                                      <DownloadOutlined color="neutral" /> Download
                                  </>
                              ),
                              onClick: () => onDownloadClick(folderData),
                          },
                      ]
                    : []),
            ]}>
            {folderData.tags?.length > 0 && (
                <Typography fontInter variant="body4" sx={theme => ({color: theme.palette.secondary.main})}>
                    Tags: <strong style={{fontWeight: "600!important"}}>{renderTags(folderData?.tags)}</strong>
                </Typography>
            )}
        </MuiCard>
    );
};

export default FolderItem;
