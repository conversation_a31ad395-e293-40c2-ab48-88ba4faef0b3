import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../hooks/useNotification";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import CheckboxWithLabel from "../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import Drawer from "../../Atoms/Drawer/Drawer.component";
import Button from "../../Atoms/Button/Button.Component";
import {ICategory} from "../../../Interfaces/categories.interface";
import {categoriesService} from "../../../services/category.service";
import {useQuery} from "@tanstack/react-query";
import {useState} from "react";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import Loader from "../../../utils/loader";
import {ITag} from "../../../Interfaces/tags.interface";
import {tagsService} from "../../../services/tags.service";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";

interface IProps {
    open: boolean;
    onClose?: () => void;
    tagToEdit?: ITag;
    defaultReadOnly?: boolean;
}

const UpsertTagDrawer = ({open, onClose, tagToEdit, defaultReadOnly}: IProps) => {
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [readOnly, setReadOnly] = useState<boolean>(!!tagToEdit && !!defaultReadOnly);
    const theme = useTheme();
    const notify = useNotification();
    const methods = useForm(
        tagToEdit
            ? {
                  defaultValues: tagToEdit,
              }
            : undefined,
    );
    const {invalidateMatchedQueries} = useQueryHelper();
    const isValid = methods.formState.isValid;
    const isEditing = !!tagToEdit?.id;

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const data = methods.getValues();
        setIsSubmitting(true);
        if (!!tagToEdit?.id) {
            tagsService
                .update({
                    id: tagToEdit?.id,
                    name: data.name,
                    is_hidden: data.is_hidden,
                })
                .then(() => {
                    notify("Successfully updated tag", "Success");
                    invalidateMatchedQueries(["admin-tags"]);
                    invalidateMatchedQueries(["all-admin-tags"]);
                    onClose?.();
                })
                .finally(() => {
                    setIsSubmitting(false);
                });
            return;
        }
        tagsService
            .create(data.name, data.is_hidden)
            .then(() => {
                notify("Successfully added tag", "Success");
                invalidateMatchedQueries(["admin-tags"]);
                invalidateMatchedQueries(["all-admin-tags"]);
                onClose?.();
            })
            .finally(() => {
                setIsSubmitting(false);
            });
    };

    return (
        <>
            <Drawer
                open={open}
                onClose={onClose}
                anchor="right"
                sx={{
                    ".MuiDrawer-paper": {
                        maxWidth: {xs: "100vw", lg: "50vw"},
                        width: {xs: "100vw", lg: "50vw"},
                        padding: 2,
                        overflowX: "hidden",
                    },
                }}>
                <Box
                    display="flex"
                    flexDirection="row"
                    gap={2}
                    paddingBottom={2}
                    borderBottom={`1px solid ${theme.palette.neutral[400]}`}>
                    {!readOnly && (
                        <AddCircleOutlineOutlinedIcon
                            htmlColor={theme.palette.neutral[500]}
                            sx={{height: 24, width: 24}}
                        />
                    )}
                    <Typography variant="20" fontWeight={500} color={theme.palette.neutral[700]}>
                        {readOnly ? `View Tag` : isEditing ? `Edit Tag` : `Add New Tag`}
                    </Typography>
                </Box>
                <Box display="flex" flexGrow={1} width="100%">
                    <Box
                        marginTop={2}
                        display="grid"
                        height="fit-content"
                        width="100%"
                        gridTemplateColumns="1fr"
                        columnGap={3}
                        rowGap={2}>
                        <FormProvider {...methods}>
                            <TextBoxComponent
                                id="name"
                                label="Name"
                                placeholder="Input category name"
                                isRequired
                                readOnly={readOnly}
                            />
                            <CheckboxWithLabel id="is_hidden" label="Hide Tag" disabled={readOnly} />
                        </FormProvider>
                    </Box>
                </Box>
                <Box
                    display="flex"
                    flexDirection="row"
                    gap={1}
                    paddingTop={2}
                    borderTop={`1px solid ${theme.palette.neutral[400]}`}>
                    {readOnly && (
                        <Button
                            id="toggle-edit-btn"
                            variant="contained"
                            color="blue"
                            onClick={() => {
                                setReadOnly(false);
                            }}>
                            EDIT
                        </Button>
                    )}
                    {!readOnly && (
                        <Button
                            id="add-tag-btn"
                            variant="contained"
                            color="blue"
                            disabled={!isValid}
                            onClick={handleSubmit}
                            sx={{gap: 0.5}}>
                            {isSubmitting ? (
                                <Loader inline loading version="iconWhite" />
                            ) : isEditing ? (
                                <>
                                    <CheckOutlinedIcon />
                                    SAVE CHANGES
                                </>
                            ) : (
                                <>
                                    <AddOutlinedIcon /> ADD TAG
                                </>
                            )}
                        </Button>
                    )}
                    <Button id="cancel-btn" variant="text" color="blue" onClick={onClose}>
                        {readOnly ? "Close" : "Cancel"}
                    </Button>
                </Box>
            </Drawer>
        </>
    );
};

export default UpsertTagDrawer;
