import {useQuery} from "@tanstack/react-query";
import Box from "../../Atoms/Box/Box.component";
import {categoriesService} from "../../../services/category.service";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import useTheme from "@mui/material/styles/useTheme";
import {ICategory} from "../../../Interfaces/categories.interface";
import Typography from "../../Atoms/Typography/Typography.component";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import {DATE_FULL_SHORT_MONTH, formatDate} from "../../../utils/formatDate";
import {pluralizeString} from "../../../utils/formatString.util";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import CompanyType from "../../../constants/companyType.constant";
import MenuButton from "../../Molecules/MenuButton/menuButton.component";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import {useCallback, useState} from "react";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import UpsertCategoryDrawer from "./UpsertCategoryDrawer.component";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {buildFilters} from "../../../utils/filters.util";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import fuzzySearch from "../../../utils/fuzzySearch.util";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";

const CategoryTypeLabel = {
    [CompanyType.MSP_CLIENT]: "MSP Client",
    [CompanyType.DIRECT]: "Direct",
};

const AdminCategoriesManagementTab = () => {
    const [showUpsert, setShowUpsert] = useState<{
        open: boolean;
        categoryToEdit?: ICategory;
        defaultReadOnly?: boolean;
    }>({
        open: false,
    });
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(20);
    const [filters, setFilters] = useState<INewFilters | undefined>({
        sort: "sorting_name__ASC",
    });
    const [search, setSearch] = useState("");
    const [isDeleting, setIsDeleting] = useState<{[key: string]: boolean}>({});
    const [isTogglingVisibility, setIsTogglingVisibility] = useState<{[key: string]: boolean}>({});
    const theme = useTheme();
    const notify = useNotification();
    const {invalidateMatchedQueries} = useQueryHelper();

    const all_categories = useQuery<ICategory[], Error>(["admin-all-categories"], () =>
        categoriesService.getAllAdmin().then(res => res.data),
    );

    const categories = useQuery<IPaginationData<ICategory[]>, Error>(
        ["admin-categories", page, itemsPerPage, search, JSON.stringify(filters)],
        () =>
            categoriesService
                .getAllAdminPaged({
                    search_word: search ? encodeURIComponent(search) : "",
                    items_per_page: itemsPerPage,
                    page: page || 1,
                    dynamic: {
                        only_parents: "1",
                        ...filters,
                    },
                })
                .then(res => res.data),
    );

    const categoriesFilters = useQuery(["admin-categories-filters"], () =>
        categoriesService.getAllAdminFilters().then(res =>
            buildFilters(
                {
                    ...res.data?.filters,
                    is_hidden: {
                        ...res.data.filters?.is_hidden,
                        items: res.data.filters?.is_hidden?.items?.map(item => ({...item, id: String(item.id)})),
                    },
                },
                res.data?.sorts,
            ),
        ),
    );

    const deleteCategory = useCallback(
        async (category: ICategory) => {
            if (!category?.id) return;
            const answer = await getUserConfirmation("", {
                mui: true,
                modalTheme: "error",
                title: "Delete Category?",
                form: (
                    <Typography variant="body3">
                        Are you sure you want to delete <strong>{category?.name || "this category"}</strong>?
                    </Typography>
                ),
                okButtonText: "Yes, delete",
            });
            if (!answer.value) return;
            setIsDeleting({...isDeleting, [category.id]: true});
            categoriesService
                .delete(category.id)
                .then(() => {
                    notify("Successfully deleted category", "Success");
                    categories.refetch();
                    all_categories.refetch();
                })
                .catch(err => {
                    notify(getErrorFromArray(err), "Error");
                })
                .finally(() => {
                    setIsDeleting({...isDeleting, [category.id]: false});
                });
        },
        [isDeleting, all_categories],
    );

    const handleSortChange = (sortType: string, order_by: string) => {
        setFilters(prev => ({...prev, sort: `sorting_${order_by}__${sortType}`}));
    };

    const handleToggleVisibility = useCallback(
        (category: ICategory) => {
            setIsTogglingVisibility({...isTogglingVisibility, [category?.id]: true});
            categoriesService
                .update({
                    id: category.id,
                    is_hidden: category.is_hidden ? "0" : "1",
                })
                .then(() => {
                    all_categories.refetch();
                    invalidateMatchedQueries(["admin-all-categories"]);
                    invalidateMatchedQueries(["admin-categories"]);
                    notify("Successfully toggled visibility", "Success");
                })
                .catch(err => {
                    notify(getErrorFromArray(err), "Error");
                })
                .finally(() => {
                    setIsTogglingVisibility({...isTogglingVisibility, [category?.id]: false});
                });
        },
        [isTogglingVisibility],
    );

    const tableIconDefaultSx = {height: 20, width: 20};

    const ThreeDotMenuCell = useCallback(
        ({row}) => {
            return (
                <MenuButton
                    id={`more-actions-${row.original.id}`}
                    buttonVariant="iconButton"
                    buttonProps={{
                        sx: {
                            width: "24px",
                            minWidth: "24px!important",
                            height: "24px",
                            boxShadow: "none",
                            background: "none",
                        },
                    }}
                    items={[
                        {
                            id: "view",
                            children: (
                                <>
                                    <VisibilityOutlinedIcon htmlColor={theme.palette.neutral[500]} /> View
                                </>
                            ),
                            onClick: () => {
                                setShowUpsert({open: true, categoryToEdit: row.original, defaultReadOnly: true});
                            },
                        },
                        {
                            id: "edit",
                            children: (
                                <>
                                    <EditOutlinedIcon htmlColor={theme.palette.neutral[500]} /> Edit
                                </>
                            ),
                            onClick: () => {
                                setShowUpsert({open: true, categoryToEdit: row.original});
                            },
                        },
                        {
                            id: "delete",
                            children: (
                                <>
                                    <DeleteOutlineOutlinedIcon color="error" />
                                    <Typography variant="body3" color="error">
                                        Delete
                                    </Typography>
                                </>
                            ),
                            onClick: () => {
                                deleteCategory(row.original);
                            },
                        },
                    ]}
                    aria-label="settings">
                    {isDeleting[row.original.id] ? (
                        <Loader inline loading />
                    ) : (
                        <MoreVert htmlColor={theme.palette.blue[600]} />
                    )}
                </MenuButton>
            );
        },
        [isDeleting],
    );

    const VisibilityColumn = useCallback(
        ({row}) => {
            const isLoading = isTogglingVisibility[row.original.id];
            return (
                <Box display="flex" alignItems="center" justifyContent="flex-start">
                    <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        onClick={isLoading ? undefined : () => handleToggleVisibility(row.original)}
                        sx={{
                            borderRadius: "50%",
                            padding: 0.5,
                            transition: "all 0.2s ease-in-out",
                            cursor: "pointer",
                            "&:hover": {
                                backgroundColor: theme.palette.blue[200],
                            },
                            pointerEvents: isLoading ? "none" : "all",
                        }}>
                        {isLoading ? (
                            <Loader inline loading style={{margin: 0}} />
                        ) : row.original.is_hidden ? (
                            <VisibilityOffOutlinedIcon htmlColor={theme.palette.neutral[500]} sx={tableIconDefaultSx} />
                        ) : (
                            <VisibilityOutlinedIcon htmlColor={theme.palette.blue[600]} sx={tableIconDefaultSx} />
                        )}
                    </Box>
                </Box>
            );
        },
        [isTogglingVisibility],
    );

    const columns = [
        {
            accessor: "name",
            Header: "Category",
            Cell: ({row}) => {
                const isSubcategory = !!row.original?.parent_id;
                return (
                    <Typography
                        variant="body4"
                        fontWeight={700}
                        color={theme.palette.blue[600]}
                        sx={{cursor: "pointer"}}
                        onClick={() =>
                            setShowUpsert({
                                open: true,
                                categoryToEdit: row.original,
                                defaultReadOnly: true,
                            })
                        }>
                        {isSubcategory ? (
                            <Box sx={{height: 24, width: 24, marginRight: 1, display: "inline-block"}} />
                        ) : (
                            <FiberManualRecordIcon
                                htmlColor={row.original.color || "transparent"}
                                sx={{marginRight: 1}}
                            />
                        )}
                        {row.original?.name}
                    </Typography>
                );
            },
            sortable: true,
        },
        {
            accessor: "company_types",
            Header: "Type",
            Cell: ({row}) => {
                return (
                    <Typography variant="body4">
                        {row.original?.company_types?.length
                            ? row.original?.company_types?.map(type => CategoryTypeLabel[type] || "").join(", ")
                            : "Default"}
                    </Typography>
                );
            },
        },
        {
            accessor: "is_hidden",
            Header: "Visibility",
            Cell: VisibilityColumn,
            sortable: true,
        },
        {
            accessor: "total_usage_count",
            Header: "Overall Usage",
            Cell: ({row}) => {
                const total_usage = row.original?.total_usage_count || 0;
                return (
                    <Box display="flex" alignItems="center" justifyContent="flex-start" gap="10px">
                        <Typography variant="body4" width={60}>
                            {total_usage} {pluralizeString("Item", total_usage)}
                        </Typography>
                        <Box
                            tooltip={{
                                title: (
                                    <Box display="flex" flexDirection="column" gap={0.5} minWidth={200}>
                                        <Typography variant="body4" fontWeight={600}>
                                            Overall Usage
                                        </Typography>
                                        <Box display="flex" flexDirection="column">
                                            <Typography variant="body4">
                                                Vendors: {row.original.vendor_usage_count || 0}
                                            </Typography>
                                            <Typography variant="body4">
                                                Products: {row.original.product_usage_count || 0}
                                            </Typography>
                                            <Typography variant="body4">
                                                Videos: {row.original.video_usage_count || 0}
                                            </Typography>
                                        </Box>
                                        <Typography variant="body4" fontWeight={600}>
                                            Total: {row.original?.total_usage_count || 0}
                                        </Typography>
                                    </Box>
                                ),
                                light: true,
                            }}>
                            <InfoOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
                        </Box>
                    </Box>
                );
            },
        },
        {
            accessor: "created_at",
            Header: "Creation Date",
            Cell: ({row}) => (
                <Typography variant="body4">{formatDate(row.original.created_at, DATE_FULL_SHORT_MONTH)}</Typography>
            ),
            sortable: true,
        },
        {
            accessor: "actions",
            Header: "Actions",
            Cell: ThreeDotMenuCell,
        },
    ];

    return (
        <ErrorBoundary>
            <Box
                marginTop={2}
                display="flex"
                flexDirection="column"
                gap={3}
                sx={{
                    "& table > thead > tr > th:last-child": {
                        width: "72px !important",
                        maxWidth: "72px !important",
                    },
                    "& table > tbody > tr > td:last-child": {
                        width: "72px !important",
                        maxWidth: "72px !important",
                    },
                }}>
                <PageInnerHeader
                    id="admin-manage-categories-header"
                    filters={categoriesFilters?.data}
                    filterState={[filters, setFilters]}
                    searchState={[search, setSearch]}
                    growSearch
                />
                <TableV2Component
                    id="categories-table"
                    columns={columns as []}
                    customData={categories.data?.data?.filter(c => !c.parent_id) || []}
                    headerBackground={theme.palette.blue[100]}
                    subTables={{
                        enabled: row => !row?.parent_id,
                        customData: row => {
                            const parent_id = row?.id;
                            const subcategories = all_categories.data?.filter(c => c.parent_id === parent_id) || [];
                            return search ? fuzzySearch(search, subcategories, "name")?.results : subcategories;
                        },
                        rowStyle: {backgroundColor: `${theme.palette.neutral[200]}`},
                        tdStyle: {backgroundColor: "transparent"},
                        disableExpandOnEmpty: true,
                    }}
                    isLoading={categories.isLoading}
                    noResultsMessage={
                        search
                            ? "No categories found based on your search or filter criteria. Please update your search or filter and try again."
                            : "No categories found."
                    }
                    tableHeaderRowStyle={{height: 56}}
                    sortType="ASC"
                    orderBy="name"
                    onSortChange={handleSortChange}
                    pinColumns={["actions"]}
                    subTablesExpanded={!!search}
                />
                <CustomPagination
                    metadata={categories.data?.meta || {total: 0, last_page: 1}}
                    page={page}
                    itemsPerPage={itemsPerPage}
                    setPage={setPage}
                    setItemsPerPage={setItemsPerPage}
                />
            </Box>
            {showUpsert.open && (
                <UpsertCategoryDrawer
                    open
                    onClose={() => setShowUpsert({open: false})}
                    categoryToEdit={showUpsert?.categoryToEdit}
                    defaultReadOnly={showUpsert?.defaultReadOnly}
                />
            )}
        </ErrorBoundary>
    );
};

export default AdminCategoriesManagementTab;
