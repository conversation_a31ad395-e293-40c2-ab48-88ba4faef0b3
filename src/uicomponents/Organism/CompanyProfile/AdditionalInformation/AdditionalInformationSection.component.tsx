import {DeleteOutlined} from "@mui/icons-material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {DEFAULT_QUERY_CONFIGS, PERMANENT_ACTION_CONFIRMATION} from "../../../../constants/commonStrings.constant";
import {ProfileQuestionTypes} from "../../../../enums/profileQuestions.enum";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import usePermissions from "../../../../hooks/usePermissions";
import {IAnswerOptions, ICompanyMoreInfo, IMoreInfoQuestion} from "../../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {companyService} from "../../../../services/company.service";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import {useGetFriendlyUrl} from "../../../../utils/profile.util";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import Box from "../../../Atoms/Box/Box.component";
import Button from "../../../Atoms/Button/Button.Component";
import Typography from "../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import CheckboxWithLabel from "../../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import RadioWithLabel from "../../../Molecules/RadioWithLabel/RadioWithLabel.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import AdditionalInfoCard from "./AdditionalInfoCard.component";

interface IProps {
    canEditData?: boolean;
    friendlyUrl?: string;
    title?: string;
    onChange?: () => void;
    openByDefault?: boolean;
    useDialogStyles?: boolean;
}

const AdditionalInformationSection = ({
    canEditData = false,
    friendlyUrl,
    title = "Additional Information",
    onChange,
    openByDefault,
    useDialogStyles,
}: IProps) => {
    const [showAddForm, setShowAddForm] = useState<boolean>(!!openByDefault);
    const theme = useTheme();
    const friendly_url = friendlyUrl || useGetFriendlyUrl(location.pathname);
    const {company, companyMoreInfo, updateCompany, isUpdating} = useCompany(friendly_url, {
        isCompany: true,
        calls: {company: true, companyMoreInfo: true},
    });
    const {hasPermissions} = usePermissions({overrideCompanyId: company.data?.id});
    const methods = useForm();
    const companyInfo = company.data;
    const moreInfo = companyMoreInfo.data;
    const isEditable = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE]) || canEditData;
    const isAdding = isUpdating?.[MutateTypes.AddMoreInfo];
    const selectedQuestionId = methods.watch("question_id");
    const isLoading = company.isLoading || companyMoreInfo.isLoading;

    const moreInfoQuestions = useQuery<IMoreInfoQuestion[] | null>(
        ["company-moreInfoQuestions", friendly_url],
        () => {
            if (!friendly_url) return new Promise(resolve => resolve(null));
            const promiseToReturn: Promise<any> = new Promise((resolve, reject) =>
                companyService
                    .getMoreInfoQuestions(companyInfo?.type_is_of_vendor ? "1" : "0")
                    .then((r: AxiosResponse) => resolve(r.data))
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!friendly_url && !!isEditable,
        },
    );
    const selectedQuestion = moreInfoQuestions?.data?.find(q => q.id === selectedQuestionId);

    const handleDelete = async (questionId: string) => {
        const answer = await getUserConfirmation("Delete?", {
            mui: true,
            title: "Delete?",
            modalTheme: "error",
            okButtonText: (
                <Box gap={1} display="flex" alignItems="center">
                    <DeleteOutlined sx={{height: 18, width: 18}} /> DELETE
                </Box>
            ),
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Typography variant="subtitle2" fontInter fontWeight={"600 !important"}>
                        Are you sure you want to delete this answer?
                    </Typography>
                    <Typography variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (!answer.value) return;
        updateCompany.mutate({
            id: questionId,
            friendly_url: friendly_url,
            mutate_type: MutateTypes.DelMoreInfo,
            mutate_is_updating_append: questionId,
            onSuccess: () => {
                methods.reset();
                setShowAddForm(false);
                if (onChange) {
                    onChange();
                }
            },
        });
    };

    const handleSubmit = data => {
        const sq = selectedQuestion;
        let profile_enrichment_option_ids: string[] = [];
        let newInfoAnswer: string = data.answer;
        if (sq?.type === ProfileQuestionTypes.SingleAnswer) {
            profile_enrichment_option_ids = [data.answer];
            newInfoAnswer = "";
        } else {
            const filteredAnswers = Object.entries(data).filter(
                ([key, value]) => key !== "profile_enrichment_question_id" && key !== "answer" && value === true,
            );
            profile_enrichment_option_ids = filteredAnswers.map(([key]) => key);
        }
        if (selectedQuestion?.type === ProfileQuestionTypes.Date && newInfoAnswer) {
            newInfoAnswer = DateTime.fromSQL(newInfoAnswer).toISO() ?? "";
        }
        updateCompany.mutate({
            more_info_request: {
                profile_enrichment_question_id: selectedQuestionId,
                profile_enrichment_option_ids,
                answer: newInfoAnswer,
                friendlyUrl: friendly_url,
                question: sq?.question,
                type: sq?.type,
                options: sq?.options,
                order: (moreInfo?.length || 50) + 1,
            },
            mutate_type: MutateTypes.AddMoreInfo,
            onSuccess: () => {
                methods.reset();
                setShowAddForm(false);
                if (onChange) {
                    onChange();
                }
            },
        });
    };

    return (
        <Box display="flex" flexDirection="column" gap={2}>
            <PageInnerHeader
                id="about-additional-information-section"
                title={{
                    text: title,
                    sx: {fontSize: "23px !important", ...(useDialogStyles && {fontFamily: "var(--Inter) !important"})},
                }}
                indicator={null}
                actions={{
                    primary:
                        isEditable &&
                        !!moreInfoQuestions.data &&
                        (moreInfo?.length || 0) !== (moreInfoQuestions?.data?.length || 0)
                            ? {
                                  id: "addNewCountry",
                                  children: (
                                      <>
                                          <AddOutlinedIcon />
                                          Add New
                                      </>
                                  ),
                                  onClick: () => setShowAddForm(true),
                                  variant: "outlined",
                              }
                            : undefined,
                }}
            />
            <Box display="flex" flexDirection="column" flexWrap="wrap" gap={"10px"}>
                {showAddForm && !isLoading && (
                    <FormProvider {...methods}>
                        <form style={{width: "100%"}} onSubmit={methods.handleSubmit(handleSubmit)}>
                            <BorderedBox
                                width="100%"
                                display="flex"
                                flexDirection="column"
                                flexWrap="wrap"
                                marginTop={3}
                                gap={1}
                                borderRadius={1}>
                                <Typography variant="subtitle2" fontInter fontWeight={"600 !important"}>
                                    Select a question to add additional information about your company.
                                </Typography>
                                <Box display="flex" flexDirection="row" flexWrap="wrap" alignItems="center" gap={1}>
                                    <AutoCompleteComponent
                                        id="question_id"
                                        options={
                                            moreInfoQuestions?.data
                                                ?.filter(q =>
                                                    moreInfo?.find((item: ICompanyMoreInfo) => item.id === q.id)
                                                        ? false
                                                        : true,
                                                )
                                                .map(q => ({
                                                    id: q.id,
                                                    label: q.question,
                                                    type: q.type,
                                                })) || []
                                        }
                                        sx={{flex: "1"}}
                                        label="Question"
                                        placeholder="Please select a question"
                                        isRequired
                                        validateOnTheFly
                                        loading={moreInfoQuestions.isLoading}
                                    />
                                    <Button
                                        id="save-more-info"
                                        color="blue"
                                        variant="contained"
                                        type="submit"
                                        loading={isAdding}
                                        sx={{display: {xs: "none", md: "block"}}}>
                                        SAVE
                                    </Button>
                                    <Button
                                        id="cancel-more-info"
                                        color="blue"
                                        variant="outlined"
                                        disabled={isAdding}
                                        onClick={() => {
                                            methods.reset();
                                            setShowAddForm(false);
                                        }}
                                        sx={{display: {xs: "none", md: "block"}}}>
                                        CANCEL
                                    </Button>
                                    <Box width="100%">
                                        {selectedQuestion &&
                                            (selectedQuestion.type === ProfileQuestionTypes.OpenText ? (
                                                <TextBoxComponent
                                                    id="answer"
                                                    placeholder="Answer"
                                                    label="Answer"
                                                    isRequired
                                                    containerSx={{width: "100%"}}
                                                />
                                            ) : selectedQuestion?.type === ProfileQuestionTypes.Date ? (
                                                <TextBoxComponent
                                                    id="answer"
                                                    type="date"
                                                    label="Answer"
                                                    placeholder="Answer"
                                                    isRequired
                                                    containerSx={{width: "100%"}}
                                                />
                                            ) : selectedQuestion?.type === ProfileQuestionTypes.SingleAnswer ? (
                                                <div>
                                                    {selectedQuestion?.options?.map(o => (
                                                        <RadioWithLabel
                                                            id="answer"
                                                            testid={`answer-${o.option}`}
                                                            value={o.id}
                                                            key={o.id}
                                                            label={o.option}
                                                            labelProps={{
                                                                color: theme.palette.neutral[800],
                                                                fontWeight: "600 !important",
                                                                fontInter: true,
                                                                fontSize: "16px !important",
                                                            }}
                                                        />
                                                    ))}
                                                </div>
                                            ) : (
                                                <div>
                                                    {selectedQuestion.options
                                                        ?.sort((a, b) => a.order - b.order)
                                                        .map((o: IAnswerOptions) => (
                                                            <CheckboxWithLabel
                                                                id={o.id}
                                                                key={o.id}
                                                                testid={`selected-question-${o.option}`}
                                                                label={o.option}
                                                                labelProps={{
                                                                    color: theme.palette.neutral[800],
                                                                    fontWeight: "600 !important",
                                                                    fontInter: true,
                                                                    fontSize: "16px !important",
                                                                }}
                                                            />
                                                        ))}
                                                </div>
                                            ))}
                                    </Box>
                                    <Button
                                        id="save-more-info"
                                        color="blue"
                                        variant="contained"
                                        type="submit"
                                        loading={isAdding}
                                        sx={{display: {xs: "block", md: "none"}}}>
                                        SAVE
                                    </Button>
                                    <Button
                                        id="cancel-more-info"
                                        color="blue"
                                        variant="text"
                                        disabled={isAdding}
                                        onClick={() => {
                                            methods.reset();
                                            setShowAddForm(false);
                                        }}
                                        sx={{display: {xs: "block", md: "none"}}}>
                                        CANCEL
                                    </Button>
                                </Box>
                            </BorderedBox>
                        </form>
                    </FormProvider>
                )}
                {isLoading ? (
                    <>
                        {Array(2)
                            .fill(null)
                            .map((_, i) => (
                                <Skeleton height="100px" width="100%" key={i} />
                            ))}
                    </>
                ) : moreInfo?.length ? (
                    moreInfo?.map((info: ICompanyMoreInfo, i: number) => {
                        const isDeleting = isUpdating[MutateTypes.DelMoreInfo + info.id];
                        return (
                            <AdditionalInfoCard
                                key={info.id}
                                info={info}
                                handleDelete={handleDelete}
                                friendly_url={friendly_url}
                                type_is_of_vendor={companyInfo?.type_is_of_vendor}
                                isDeleting={isDeleting}
                                isEditable={isEditable}
                                index={i}
                            />
                        );
                    })
                ) : showAddForm ? null : (
                    <Typography variant="body2" fontInter>
                        No additional information available
                    </Typography>
                )}
            </Box>
        </Box>
    );
};

export default AdditionalInformationSection;
