import {useLocation} from "react-router-dom";
import Box from "../../../Atoms/Box/Box.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import {useGetFriendlyUrl} from "../../../../utils/profile.util";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import Skeleton from "@mui/material/Skeleton";
import VerticalFocusCard from "./VerticalFocusCard.component";
import useVerticalFocuses from "../../../../hooks/fetches/useVerticalFocuses";

const VerticalFocusSection = () => {
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const {company} = useCompany(friendly_url, {
        isCompany: true,
        isMsp: true,
        calls: {all: false, company: true},
        companyParams: ["focuses"],
    });
    const info = company.data;
    const isLoading = company.isLoading;
    const focuses = useVerticalFocuses(friendly_url);
    const all_focuses = focuses.data || [];

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={2}>
                <PageInnerHeader
                    id="vertical-focus-section"
                    title={{
                        text: "Vertical Focus",
                        infoTooltip:
                            "Vertical Focus refers to a targeted approach within a specific industry or niche, offering tailored services to meet the unique needs of clients within that sector.",
                        sx: {fontSize: "23px !important"},
                    }}
                    indicator={null}
                />
                <Box display="flex" flexDirection="row" flexWrap="wrap" gap="10px">
                    {isLoading ? (
                        <>
                            {Array(5)
                                .fill(null)
                                .map((_, i) => (
                                    <Skeleton height="40px" width="120px" key={i} />
                                ))}
                        </>
                    ) : all_focuses?.length ? (
                        all_focuses?.map(focus => (
                            <VerticalFocusCard
                                key={focus.id}
                                focus={focus}
                                companyId={info?.id}
                                companyFriendlyUrl={friendly_url}
                            />
                        ))
                    ) : null}
                </Box>
            </Box>
        </ErrorBoundary>
    );
};

export default VerticalFocusSection;
