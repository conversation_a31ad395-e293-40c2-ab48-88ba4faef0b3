import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useMemo, useState} from "react";
import {Link, useLocation} from "react-router-dom";
import {ProductsModal} from "../../../../components/VendorProfile/productsModal.component";
import routeConfig from "../../../../constants/routeConfig";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import useAuthState from "../../../../hooks/useAuthState";
import usePermissions from "../../../../hooks/usePermissions";
import useSettings from "../../../../hooks/useSettings";
import {ICategory} from "../../../../Interfaces/categories.interface";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IProducts} from "../../../../Interfaces/products.interface";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {useGetFriendlyUrl} from "../../../../utils/profile.util";
import Box from "../../../Atoms/Box/Box.component";
import Chip from "../../../Atoms/Chip/Chip.Component";
import Typography from "../../../Atoms/Typography/Typography.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";

const AboutUsSection = () => {
    const [productsMainCategories, setProductsMainCategories] = useState<ICategory[]>([]);
    const [showProductModal, setShowProductModal] = useState<boolean>(false);
    const theme = useTheme();
    const location = useLocation();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const {authState} = useAuthState();
    const {company, companyProducts} = useCompany(friendly_url, {
        isCompany: true,
        calls: {all: false, companyProducts: true, company: true},
        companyParams: ["categories"],
    });
    const info = company.data;
    const companyCategories = useMemo(() => info?.categories || [], [info]);
    const {hasPermissions} = usePermissions({overrideCompanyId: info?.id});
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PRODUCTS_UPDATE]);
    const isEditable = authState?.id ? hasEditAccess : false;
    const {settings, getAllCategories} = useSettings();
    const isLoading = company.isLoading || companyProducts.isLoading;

    useEffect(() => {
        if (!settings.all_categories) {
            getAllCategories();
            return;
        }
        let allProductCategories: ICategory[] = [];
        companyProducts.data?.forEach((p: IProducts) => {
            if (p.categoriesSelected) {
                p.categoriesSelected.forEach((categoryId: string) => {
                    const found = settings.all_categories?.find(c => c.id === categoryId);
                    if (found) {
                        allProductCategories.push(found);
                    }
                });
            } else if (p.categories) {
                allProductCategories = [...allProductCategories, ...p.categories];
            }
        });
        const parentCategories: ICategory[] = [];
        allProductCategories.forEach(category => {
            if (category.parent_id === null) return;
            const found = settings.all_categories?.find((c: ICategory) => c.id === category.parent_id);
            if (found && parentCategories.findIndex(parentCategory => parentCategory.id === found?.id) === -1) {
                parentCategories.push(found);
            }
        });
        setProductsMainCategories(parentCategories);
        // eslint-disable-next-line
    }, [companyProducts.data, settings.all_categories]);

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={2}>
                <PageInnerHeader
                    id="about-product-segment"
                    title={{text: "Product Segments", sx: {fontSize: "23px !important"}}}
                    indicator={null}
                    actions={{
                        primary: isEditable
                            ? {
                                  id: "addProduct",
                                  children: (
                                      <>
                                          <AddOutlinedIcon />
                                          Add New
                                      </>
                                  ),
                                  onClick: () => setShowProductModal(true),
                                  variant: "outlined",
                              }
                            : undefined,
                    }}
                />
                <Box display="flex" flexDirection="row" flexWrap="wrap" gap="10px">
                    {!!!productsMainCategories.length && !isLoading && (
                        <Typography variant="body2" fontInter>
                            No products available
                        </Typography>
                    )}
                    {isLoading ? (
                        <>
                            {Array(5)
                                .fill(null)
                                .map((_, i) => (
                                    <Skeleton height="40px" width="120px" key={i} />
                                ))}
                        </>
                    ) : productsMainCategories?.length ? (
                        productsMainCategories.map((category, index) => (
                            <Link
                                key={"productMain" + category.id}
                                to={`${routeConfig.Categories.path}/${category.friendly_url}`}
                                id={`categoryPath_${category.id}`}
                                target="_blank">
                                <Chip
                                    key={index}
                                    label={category.name}
                                    color="default"
                                    sx={{
                                        borderRadius: 1,
                                        backgroundColor: theme.palette.neutral[100],
                                        border: `1px solid ${theme.palette.neutral[300]}`,
                                        cursor: "pointer",
                                        span: {
                                            color: theme.palette.blue[800],
                                            fontWeight: 600,
                                            fontSize: 14,
                                        },
                                        "&:hover": {
                                            backgroundColor: theme.palette.neutral[300],
                                            transition: "all 0.2s ease-in-out",
                                        },
                                    }}
                                />
                            </Link>
                        ))
                    ) : null}
                    {productsMainCategories.length === 0 && companyCategories?.length
                        ? companyCategories.map((category, index) => (
                              <Link
                                  key={"company" + category.id}
                                  to={`${routeConfig.Categories.path}/${category.friendly_url}`}
                                  id={`categoryPath_${category.id}`}>
                                  <Chip
                                      key={index}
                                      label={category.name}
                                      color="default"
                                      sx={{
                                          borderRadius: 1,
                                          backgroundColor: theme.palette.neutral[100],
                                          border: `1px solid ${theme.palette.neutral[300]}`,
                                          cursor: "pointer",
                                          span: {
                                              color: theme.palette.blue[800],
                                              fontWeight: 600,
                                              fontSize: 14,
                                          },
                                          "&:hover": {
                                              backgroundColor: theme.palette.neutral[300],
                                              transition: "all 0.2s ease-in-out",
                                          },
                                      }}
                                  />
                              </Link>
                          ))
                        : null}
                </Box>
            </Box>
            {showProductModal && info && (
                <ProductsModal
                    companyId={info?.id}
                    onClose={() => setShowProductModal(false)}
                    company_friendly_url={friendly_url}
                />
            )}
        </ErrorBoundary>
    );
};

export default AboutUsSection;
