import {act, fireEvent, render, screen, waitFor} from "@testing-library/react";
import {Mock, beforeEach, describe, expect, it, vi} from "vitest";
import "../../../../../__mocks__/hooks/usePermissions";
import TestWrapper from "../../../../../__test_utils__/TestWrapper";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import {usePeople} from "../../../../hooks/fetches/usePeople";
import {useAccess} from "../../../../hooks/useAccess";
import useAuthState from "../../../../hooks/useAuthState";
import useCompanyProfile from "../../../../hooks/useCompanyProfile";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import AboutUsSection from "./AboutUsSection.component";

import {InitialConfigType, LexicalComposer} from "@lexical/react/LexicalComposer";
vi.mock("../../../../hooks/fetches/useCompany");
vi.mock("../../../../hooks/fetches/usePeople");
vi.mock("../../../../hooks/useAccess");
vi.mock("../../../../hooks/useRoles");
vi.mock("../../../../hooks/useAuthState");
vi.mock("../../../../hooks/useCompanyProfile");
vi.mock("lexical");

describe("AboutUsSection", () => {
    beforeEach(() => {
        (useCompany as Mock).mockReturnValue({
            company: {data: {address: "123 Street", description: "Company description", affiliate_id: "AFF123"}},
            updateCompany: {mutate: vi.fn()},
            companyRules: {data: []},
            isUpdating: {[MutateTypes.Update]: false},
            isLoading: false,
        });

        (usePeople as Mock).mockReturnValue({
            people: {data: {address: "123 Street", description: "Person description"}},
            updatePeople: {mutate: vi.fn()},
            peopleRules: {data: []},
            isUpdating: {[MutateTypes.Update]: false},
            isLoading: false,
        });

        (useAccess as Mock).mockReturnValue({
            hasAccess: vi.fn(() => true),
        });

        (useAuthState as Mock).mockReturnValue({
            authState: {id: 1, claimed_companies: [{id: 1}]},
        });

        (useCompanyProfile as Mock).mockReturnValue({isAffiliateBrandChildCompany: true});

        vi.mock("lexical", async () => {
            const actual = await vi.importActual<typeof import("lexical")>("lexical");
            return {
                ...actual,
                createEditor: vi.fn(() => ({
                    isEditable: vi.fn(() => true),
                    setEditable: vi.fn(),
                    update: vi.fn(),
                    getEditorState: vi.fn(),
                    setEditorState: vi.fn(),
                    registerCommand: vi.fn(),
                })),
            };
        });

        vi.mock("@lexical/html", async () => {
            const actual = await vi.importActual<typeof import("@lexical/html")>("@lexical/html");
            return {
                ...actual,
                $generateNodesFromDOM: () => [],
            };
        });
    });
    const editorConfig: InitialConfigType = {
        namespace: "Rich Text Editor",
        nodes: [],
        onError(error: Error) {
            throw error;
        },
        editable: true,
    };
    const renderComponent = (p = {}) =>
        render(
            <TestWrapper>
                <LexicalComposer initialConfig={editorConfig}>
                    <AboutUsSection profile_type="company" {...p} />
                </LexicalComposer>
            </TestWrapper>,
        );

    it("renders without crashing", () => {
        renderComponent();
        expect(screen.getByText("About Us")).toBeDefined();
    });

    it("renders company description and address correctly", () => {
        renderComponent({showHeadOfficeAddress: true});
        expect(screen.getAllByText("Company description")).toBeDefined();
        expect(screen.getByText("123 Street")).toBeDefined();
    });

    it("enables editing mode", async () => {
        renderComponent({showHeadOfficeAddress: true});
        expect(screen.getByText("Edit")).toBeDefined();
        await act(() => {
            fireEvent.click(screen.getByText("Edit"));
        });
        const addressInput = screen.getAllByText("Head Office Address");
        expect(addressInput).toBeDefined();
        expect(screen.getByText("Save")).toBeDefined();
    });

    it("sends state, zip, country, and city correctly", async () => {
        renderComponent({showHeadOfficeAddress: true});
        fireEvent.click(screen.getByText("Edit"));

        const addressInput = screen.getByTestId("textInput-company_address");
        fireEvent.change(addressInput, {target: {value: "New Address"}});

        fireEvent.change(screen.getByTestId("company_address_state"), {target: {value: "New State"}});
        fireEvent.change(screen.getByTestId("company_address_zip"), {target: {value: "12345"}});
        fireEvent.change(screen.getByTestId("company_address_city"), {target: {value: "New City"}});
        fireEvent.change(screen.getByTestId("company_address_country"), {target: {value: "New Country"}});

        fireEvent.click(screen.getByText("Save"));

        await waitFor(() => {
            expect(useCompany("").updateCompany.mutate).toHaveBeenCalledWith(
                expect.objectContaining({
                    state: "New State",
                    zip: "12345",
                    city: "New City",
                    country: "New Country",
                }),
            );
        });
    });
});
