import {BookmarkBorderOutlined} from "@mui/icons-material";
import LaunchOutlinedIcon from "@mui/icons-material/LaunchOutlined";
import StarIcon from "@mui/icons-material/Star";
import Divider from "@mui/material/Divider";
import ListItem from "@mui/material/ListItem";
import useTheme from "@mui/material/styles/useTheme";
import {useState} from "react";
import {Link} from "react-router-dom";
import ContactVendorDialog from "../../../builder_cms/components/contactVendorModal";
import routeConfig from "../../../constants/routeConfig";
import {useCompany} from "../../../hooks/fetches/useCompany";
import usePermissions from "../../../hooks/usePermissions";
import {IMarketplaceItem} from "../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {LinkAndSanitizeRichText} from "../../../utils/miscellaneous";
import {makeAbsoluteUrl} from "../../../utils/url.util";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import DialogCPSlider from "../../Molecules/DialogCPSlider/dialogCpSlider.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";

interface IProps {
    open: boolean;
    onClose: () => void;
    friendlyUrl?: string;
    defaultItemId?: string;
}

const DetailLineCardDialog = (props: IProps) => {
    const [contactVendorModal, setContactVendorModal] = useState<IMarketplaceItem>();
    const {companyMarketplace, company} = useCompany(props.friendlyUrl || "", {
        isCompany: true,
        calls: {all: false, companyMarketplace: true, company: true},
        companyParams: ["id"],
    });
    const {hasPermissions} = usePermissions({overrideCompanyId: company.data?.id});
    const canEdit = hasPermissions([PERMISSION_GROUPS.MANAGE_CONNECTIONS_UPDATE], {id: company.data?.id});
    const theme = useTheme();
    const marketplaceItems: IMarketplaceItem[] =
        companyMarketplace.data?.pages.reduce((cur: IMarketplaceItem[], page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || [];
    const initialSlide = marketplaceItems?.findIndex(i => i.id === props.defaultItemId) ?? -1;

    return (
        <ErrorBoundary>
            <ModalComponent
                open={props.open}
                fullWidth={true}
                showCancelButton={false}
                showOkButton={false}
                maxWidth="lg"
                onClose={props.onClose}
                showBottom={false}
                dialogSx={{
                    ".MuiDialog-paper": {
                        overflow: "unset",
                        maxWidth: "800px !important",
                        width: "100%",
                    },
                    ".MuiDialogContent-root": {overflowY: "auto"},
                }}
                content={
                    <Box
                        display={"relative"}
                        maxWidth="100%"
                        alignItems="start"
                        maxHeight="100%"
                        sx={{
                            ".slick-slider .slick-slide:not(.slick-active)": {
                                height: "1px !important",
                            },
                        }}>
                        {props.open && !!marketplaceItems?.length && (
                            <DialogCPSlider initialSlide={initialSlide >= 0 ? initialSlide : undefined}>
                                {marketplaceItems.map((item: IMarketplaceItem, index: number) => {
                                    return (
                                        <Box key={index} sx={{width: "100%", height: "100%"}}>
                                            <Box
                                                display="flex"
                                                gap={2}
                                                flexDirection="row"
                                                width="100%"
                                                alignItems="start"
                                                sx={{marginTop: {xs: "40px!important", lg: "0!important"}}}>
                                                <AvatarComponentV2
                                                    isCompany={true}
                                                    user={{
                                                        avatar: item.avatar ?? "",
                                                        type: "",
                                                        name: item.name,
                                                        friendly_url: item.friendly_url,
                                                    }}
                                                    size={48}
                                                    showProfileType={true}
                                                    profileTypeMinimized={true}
                                                    isRedirectEnabled={false}
                                                />
                                                <Box alignItems="start" display="flex" flexDirection="column">
                                                    <Typography
                                                        sx={theme => ({
                                                            color: theme.palette.neutral[800],
                                                            maxWidth: {xs: "100%", lg: "450px"},
                                                            textOverflow: "ellipsis",
                                                            overflow: "hidden",
                                                            display: "-webkit-box",
                                                            WebkitLineClamp: 1,
                                                            WebkitBoxOrient: "vertical",
                                                            wordBreak: "break-word",
                                                            fontFamily: "'roc-grotesk'!important",
                                                            fontSize: "24px",
                                                        })}
                                                        variant="body1"
                                                        fontInter
                                                        title={item.name}>
                                                        {item.name}
                                                    </Typography>
                                                    <Box
                                                        display="flex"
                                                        flexDirection="row"
                                                        flexWrap="wrap"
                                                        gap={2}
                                                        alignItems="center">
                                                        {item.subcategories.length ? (
                                                            <Typography variant="body4" fontInter>
                                                                {item.subcategories
                                                                    .slice(0, 2)
                                                                    ?.map((subcategory, i) => (
                                                                        <>
                                                                            <Link
                                                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                                                    ":friendly_url",
                                                                                    subcategory.friendly_url || "",
                                                                                )}
                                                                                style={{
                                                                                    color: theme.palette.neutral[700],
                                                                                }}
                                                                                target="_blank">
                                                                                {subcategory.name}
                                                                            </Link>
                                                                            {i + 1 !== item.subcategories.length
                                                                                ? ", "
                                                                                : ""}
                                                                        </>
                                                                    ))}
                                                                {item.subcategories.length > 2 ? (
                                                                    <CPTooltip
                                                                        light
                                                                        title={
                                                                            <Box
                                                                                display="flex"
                                                                                flexDirection="column"
                                                                                gap={1}>
                                                                                {item.subcategories
                                                                                    .slice(2, item.subcategories.length)
                                                                                    ?.map(subcategory => {
                                                                                        return (
                                                                                            <Link
                                                                                                key={subcategory.id}
                                                                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                                                                    ":friendly_url",
                                                                                                    subcategory.friendly_url ||
                                                                                                        "",
                                                                                                )}
                                                                                                style={{width: "100%"}}
                                                                                                target="_blank"
                                                                                                onClick={e =>
                                                                                                    e.stopPropagation()
                                                                                                }>
                                                                                                <ListItem
                                                                                                    style={{
                                                                                                        width: "100%",
                                                                                                        whiteSpace:
                                                                                                            "nowrap",
                                                                                                        overflow:
                                                                                                            "hidden",
                                                                                                    }}>
                                                                                                    <Typography
                                                                                                        variant="body3"
                                                                                                        fontInter
                                                                                                        color={
                                                                                                            theme
                                                                                                                .palette
                                                                                                                .blue[800]
                                                                                                        }
                                                                                                        style={{
                                                                                                            textOverflow:
                                                                                                                "ellipsis",
                                                                                                            overflow:
                                                                                                                "hidden",
                                                                                                            lineClamp: 1,
                                                                                                            maxWidth:
                                                                                                                "100%",
                                                                                                        }}>
                                                                                                        {
                                                                                                            subcategory.name
                                                                                                        }
                                                                                                    </Typography>
                                                                                                </ListItem>
                                                                                            </Link>
                                                                                        );
                                                                                    })}
                                                                            </Box>
                                                                        }>
                                                                        <Typography
                                                                            variant="body4"
                                                                            color={theme.palette.neutral[700]}>{` +${
                                                                            item.subcategories.length - 2
                                                                        }`}</Typography>
                                                                    </CPTooltip>
                                                                ) : null}
                                                            </Typography>
                                                        ) : null}
                                                        {item.rating ? (
                                                            <Box alignItems="center" display="flex" gap="4px">
                                                                <StarIcon
                                                                    sx={theme => ({
                                                                        display: "flex",
                                                                        fill: theme.palette.warning[600],
                                                                        fontSize: "18px",
                                                                    })}
                                                                />
                                                                <Typography
                                                                    sx={{paddingTop: "3px"}}
                                                                    fontWeight="bold!important"
                                                                    fontSize="14px!important"
                                                                    letterSpacing={0}>
                                                                    {item.rating?.toFixed(1) || "No ratings yet."}
                                                                </Typography>
                                                            </Box>
                                                        ) : (
                                                            <Typography
                                                                fontWeight={600}
                                                                sx={theme => ({
                                                                    color: theme.palette.neutral[600],
                                                                })}
                                                                fontSize="14px!important"
                                                                letterSpacing={0}>
                                                                No reviews yet
                                                            </Typography>
                                                        )}
                                                    </Box>
                                                </Box>
                                            </Box>
                                            <Divider
                                                sx={{
                                                    padding: "8px 0px",
                                                    borderColor: "#94A3B8",
                                                    opacity: "0.7",
                                                }}
                                            />
                                            <Box
                                                sx={{
                                                    minHeight: item.profile_company_website_url
                                                        ? {xs: "310px", md: "347px"}
                                                        : {xs: "355px", md: "345px"},
                                                    maxHeight: item.profile_company_website_url
                                                        ? {xs: "310px", md: "347px"}
                                                        : {xs: "355px", md: "345px"},
                                                    overflowY: "auto",
                                                    padding: "16px 0",
                                                }}>
                                                <Typography
                                                    variant="body4"
                                                    dangerouslySetInnerHTML={LinkAndSanitizeRichText(
                                                        item.marketplace_description || "",
                                                    )}
                                                />
                                            </Box>
                                            <Divider
                                                sx={{
                                                    borderColor: "#94A3B8",
                                                    opacity: "0.7",
                                                }}
                                            />
                                            <Box
                                                display="flex"
                                                flexWrap="wrap"
                                                sx={{paddingTop: "16px"}}
                                                gap={1}
                                                flexDirection="row"
                                                alignItems={{xs: "center", md: "start"}}>
                                                <Button
                                                    sx={{
                                                        width: {xs: "100%!important", md: "187px!important"},
                                                        cursor: canEdit ? "cursor" : "not-allowed",
                                                    }}
                                                    onClick={() => {
                                                        setContactVendorModal(item);
                                                        props.onClose();
                                                    }}
                                                    variant="tonal"
                                                    type="button"
                                                    id="addNewAnswer">
                                                    <BookmarkBorderOutlined
                                                        sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                    />
                                                    REQUEST DEMO
                                                </Button>
                                                {/* <Button
                                                    sx={{
                                                        width: {xs: "100%!important", md: "187px!important"},
                                                        marginBottom: item.profile_company_website_url ? "0" : "5px",
                                                    }}
                                                    variant="contained"
                                                    color="secondary"
                                                    onClick={() =>
                                                        navigate(
                                                            routeConfig.VendorProfile.path.replace(
                                                                ":id",
                                                                item.friendly_url,
                                                            ),
                                                            {state: {entityId: item.id}},
                                                        )
                                                    }
                                                    type="button"
                                                    id="addNewAnswer">
                                                    <StoreOutlined
                                                        sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                    />
                                                    VENDOR PROFILE
                                                </Button> */}
                                                {item.marketplace_external_url && (
                                                    <Button
                                                        sx={{
                                                            width: {xs: "100%!important", md: "187px!important"},
                                                        }}
                                                        onClick={() =>
                                                            window.open(
                                                                makeAbsoluteUrl(item.marketplace_external_url || ""),
                                                                "_blank",
                                                            )
                                                        }
                                                        variant="outlined"
                                                        color="secondary"
                                                        type="button"
                                                        id="addNewAnswer">
                                                        <LaunchOutlinedIcon
                                                            sx={{
                                                                marginRight: "8px!important",
                                                                width: "18px",
                                                                height: "18px",
                                                            }}
                                                        />
                                                        VIEW MORE
                                                    </Button>
                                                )}
                                            </Box>
                                        </Box>
                                    );
                                })}
                            </DialogCPSlider>
                        )}
                    </Box>
                }
            />
            {contactVendorModal?.id && (
                <ContactVendorDialog
                    friendly_url={props.friendlyUrl}
                    companyName={`${company?.data?.name} - ${contactVendorModal.name}`}
                    open={contactVendorModal !== undefined}
                    onClose={() => {
                        setContactVendorModal(undefined);
                    }}
                    customUri={window.location.href}
                    hideToggleBtn
                />
            )}
        </ErrorBoundary>
    );
};

export default DetailLineCardDialog;
