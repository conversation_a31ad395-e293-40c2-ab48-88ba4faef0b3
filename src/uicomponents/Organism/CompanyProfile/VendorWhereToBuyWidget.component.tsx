import useTheme from "@mui/material/styles/useTheme";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import ShoppingBagOutlinedIcon from "@mui/icons-material/ShoppingBagOutlined";
import {useQuery} from "@tanstack/react-query";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import {companyService} from "../../../services/company.service";
import {AxiosError, AxiosResponse} from "axios";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import {IMarketplaceItem} from "../../../Interfaces/company.interface";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import {Link} from "react-router-dom";
import routeConfig from "../../../constants/routeConfig";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import {SxProps, Theme} from "@mui/material/styles";
import {Fragment} from "react";

interface IProps {
    containerSx?: SxProps<Theme>;
}

const VendorWhereToBuyWidget = (props: IProps) => {
    const theme = useTheme();
    const friendlyUrl = useGetFriendlyUrl(location.pathname);
    const companyDistributors = useQuery<IMarketplaceItem[] | null>(
        ["company-distributors", friendlyUrl],
        () => {
            if (!friendlyUrl) return new Promise(resolve => resolve(null));
            const promiseToReturn: Promise<IMarketplaceItem[]> = new Promise((resolve, reject) =>
                companyService
                    .getVendorsDistributors(friendlyUrl)
                    .then((r: AxiosResponse<IMarketplaceItem[]>) => {
                        resolve(r.data);
                    })
                    .catch((e: AxiosError<any>) => reject(e)),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            refetchOnWindowFocus: false,
        },
    );

    if (!companyDistributors.data?.length) return null;
    return (
        <BorderedBox border="none" sx={props.containerSx}>
            <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                <ShoppingBagOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                <Typography variant="body2" fontWeight={500} color={theme.palette.neutral[700]}>
                    How to Buy
                </Typography>
            </Box>
            <Box display="flex" flexDirection="column" maxHeight={200} sx={{overflowY: "auto"}} gap={2}>
                {companyDistributors.data?.map((item: IMarketplaceItem) => (
                    <Box display="flex" flexDirection="row" gap={2} key={item.id}>
                        <AvatarComponentV2
                            isCompany
                            user={{
                                avatar: item.avatar || "",
                                name: item.name,
                                friendly_url: item.friendly_url,
                                type: "",
                                is_distributor: true,
                            }}
                            size={40}
                            isRedirectEnabled
                            target="_blank"
                            profileTypeMinimized
                            showProfileType
                        />
                        <Box display="flex" flexDirection="column" gap={0.5}>
                            <Typography fontInter fontWeight={"500 !important"}>
                                {item.name}
                            </Typography>
                            <Typography variant="subtitle3" color={theme.palette.blue[600]} fontInter>
                                {item.subcategories.slice(0, 2)?.map((subcategory, i) => (
                                    <Fragment key={i}>
                                        <Link
                                            to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                ":friendly_url",
                                                subcategory.friendly_url || "",
                                            )}
                                            target="_blank"
                                            onClick={e => e.stopPropagation()}>
                                            {subcategory.name}
                                        </Link>
                                        {i + 1 !== item.subcategories.length ? ", " : ""}
                                    </Fragment>
                                ))}
                                {item.subcategories.length > 2 ? (
                                    <CPTooltip
                                        light
                                        title={
                                            <Box display="flex" flexDirection="column" gap={1}>
                                                {item.subcategories
                                                    .slice(2, item.subcategories.length)
                                                    ?.map(subcategory => {
                                                        return (
                                                            <Link
                                                                key={subcategory.id}
                                                                to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                                    ":friendly_url",
                                                                    subcategory.friendly_url || "",
                                                                )}
                                                                target="_blank"
                                                                onClick={e => e.stopPropagation()}>
                                                                <Typography
                                                                    variant="subtitle3"
                                                                    color={theme.palette.blue[600]}>
                                                                    {subcategory.name}
                                                                </Typography>
                                                            </Link>
                                                        );
                                                    })}
                                            </Box>
                                        }>
                                        <Typography
                                            onClick={e => {
                                                e.stopPropagation();
                                            }}
                                            zIndex={100}
                                            variant="subtitle3"
                                            color={theme.palette.blue[600]}>{` +${
                                            item.subcategories.length - 2
                                        }`}</Typography>
                                    </CPTooltip>
                                ) : null}
                            </Typography>
                        </Box>
                    </Box>
                ))}
            </Box>
        </BorderedBox>
    );
};

export default VendorWhereToBuyWidget;
