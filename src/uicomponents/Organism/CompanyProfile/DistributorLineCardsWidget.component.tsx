import {useTheme} from "@mui/material/styles";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import {IMarketplaceItem} from "../../../Interfaces/company.interface";
import {useNavigate} from "react-router-dom";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {SxProps, Theme} from "@mui/material/styles";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import VerifiedUserOutlinedIcon from "@mui/icons-material/VerifiedUserOutlined";
import Skeleton from "@mui/material/Skeleton";
import Button from "../../Atoms/Button/Button.Component";
import useQueryParam from "../../../utils/query.util";
import {Fragment, useState} from "react";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import DetailLineCardDialog from "./DetailLineCardDialog.component";
import {DISTRIBUTOR_LINE_CARD_TEXT} from "../../../constants/commonStrings.constant";

interface IProps {
    containerSx?: SxProps<Theme>;
}

const DistributorLineCardsWidget = (props: IProps) => {
    const [showDetailFor, setShowDetailFor] = useState<string>("");
    const friendlyUrl = useGetFriendlyUrl(location.pathname);
    const {companyMarketplace} = useCompany(friendlyUrl || "", {
        isCompany: true,
        calls: {all: false, companyMarketplace: true},
    });
    const marketplaceItems =
        companyMarketplace.data?.pages.reduce((cur: IMarketplaceItem[], page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || [];
    const theme = useTheme();
    const navigate = useNavigate();
    const queryParams = useQueryParam();
    const tab = queryParams.get("tab");

    if (!marketplaceItems.length) return null;
    return (
        <ErrorBoundary>
            <BorderedBox border="none" sx={props.containerSx}>
                <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                    <VerifiedUserOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                    <Typography variant="body2" fontWeight={500} color={theme.palette.neutral[700]}>
                        {DISTRIBUTOR_LINE_CARD_TEXT}
                    </Typography>
                </Box>
                <Box display="flex" flexDirection="column" maxHeight={200} sx={{overflowY: "auto"}} gap={2}>
                    {companyMarketplace.isLoading ? (
                        Array(5)
                            .fill(null)
                            .map((_, i) => (
                                <Box display="flex" flexDirection="row" alignItems="center" gap={2} key={i}>
                                    <Skeleton variant="circular" height={40} width={40} />
                                    <Skeleton width="100%" height={40} />
                                </Box>
                            ))
                    ) : marketplaceItems?.length ? (
                        marketplaceItems?.map((item: IMarketplaceItem) => (
                            <Box
                                display="flex"
                                flexDirection="row"
                                gap={2}
                                key={item.id}
                                sx={{cursor: "pointer"}}
                                onClick={() => setShowDetailFor(item.id)}>
                                <AvatarComponentV2
                                    isCompany
                                    user={{
                                        avatar: item.avatar || "",
                                        name: item.name,
                                        friendly_url: item.friendly_url,
                                        type: "",
                                        is_distributor: false,
                                    }}
                                    size={40}
                                    isRedirectEnabled={false}
                                    target="_blank"
                                    profileTypeMinimized
                                    showProfileType
                                />
                                <Box display="flex" flexDirection="column" gap={0.5}>
                                    <Typography fontInter fontWeight={"500 !important"}>
                                        {item.name}
                                    </Typography>
                                    <Typography variant="subtitle3" color={theme.palette.blue[600]} fontInter>
                                        {item.subcategories.slice(0, 2)?.map((subcategory, i) => (
                                            <Fragment key={i}>
                                                {subcategory.name}
                                                {i + 1 !== item.subcategories.length ? ", " : ""}
                                            </Fragment>
                                        ))}
                                        {item.subcategories.length > 2 ? (
                                            <Typography
                                                onClick={e => {
                                                    e.stopPropagation();
                                                }}
                                                zIndex={100}
                                                variant="subtitle3"
                                                color={theme.palette.blue[600]}>{` +${
                                                item.subcategories.length - 2
                                            }`}</Typography>
                                        ) : null}
                                    </Typography>
                                </Box>
                            </Box>
                        ))
                    ) : (
                        <Typography
                            variant="subtitle1"
                            fontInter
                            sx={{color: theme.palette.neutral[600], display: "flex", gap: "6px"}}>
                            No Results Found...
                        </Typography>
                    )}
                </Box>
                <Button
                    id="see-all-vendors"
                    variant="outlined"
                    color="blue"
                    sx={{fontWeight: "600 !important", display: tab && tab !== "marketplace" ? "block" : "none"}}
                    onClick={() => {
                        navigate(`?tab=marketplace`, {replace: true});
                    }}>
                    See all Vendors
                </Button>
            </BorderedBox>
            <DetailLineCardDialog
                open={!!showDetailFor}
                onClose={() => setShowDetailFor("")}
                friendlyUrl={friendlyUrl}
                defaultItemId={showDetailFor}
            />
        </ErrorBoundary>
    );
};

export default DistributorLineCardsWidget;
