import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import UploadOutlinedIcon from "@mui/icons-material/UploadOutlined";
import ListItem from "@mui/material/ListItem";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useState} from "react";
import {Link} from "react-router-dom";
import ContactVendorDialog from "../../../builder_cms/components/contactVendorModal";
import ControlledStarRating from "../../../components/FormControls/ControlledStarRating.component";
import {DISTRIBUTOR_LINE_CARD_TEXT, REQUEST_DEMO_TEXT} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import {useCompany} from "../../../hooks/fetches/useCompany";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {useInfinite} from "../../../hooks/useInfinite";
import usePermissions from "../../../hooks/usePermissions";
import {IMarketplaceItem} from "../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import Loader from "../../../utils/loader";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import MenuButton from "../../Molecules/MenuButton/menuButton.component";
import ImportPartnersFromCSVDialog from "../ImportPartnersFromCSVDialog/importPartnersFromCSVDialog.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import AddLineCardDialog from "./AddLineCardDialog.component";
import DetailLineCardDialog from "./DetailLineCardDialog.component";

interface IProps {
    friendly_url?: string;
}

const MarketplaceTab = (props: IProps) => {
    const {activeCompany} = useActiveCompany();
    const [detailDialogOpen, setDetailDialogOpen] = useState<boolean>(false);
    const [defaultItemId, setDefaultItemId] = useState<string>("");
    const [lineCardDialog, setLineCardDialog] = useState<{open: boolean; id?: string}>({
        open: false,
        id: undefined,
    });
    const [importDialog, setImportDialog] = useState<{open: boolean}>({open: false});
    const [contactVendorModal, setContactVendorModal] = useState({
        open: false,
        friendly_url: "",
        companyName: "",
    });
    const {company, companyMarketplace, updateCompany, marketPlaceOptionsState, isUpdating} = useCompany(
        props.friendly_url || "",
        {
            isCompany: true,
            calls: {all: false, companyMarketplace: true, company: true},
            companyParams: ["id"],
        },
    );
    const [marketPlaceOptions, setMarketplaceOptions] = marketPlaceOptionsState;
    const {lastElementRef, pageNum} = useInfinite(
        companyMarketplace.hasNextPage ? 99 : 0,
        companyMarketplace.isLoading || companyMarketplace.isFetchingNextPage,
    );
    const theme = useTheme();
    const {hasPermissions} = usePermissions({overrideCompanyId: company.data?.id});
    const canEdit = hasPermissions([PERMISSION_GROUPS.COMPANY_PROFILE_UPDATE], {id: company.data?.id});
    const marketplaceItems = companyMarketplace.data?.pages.reduce((cur: IMarketplaceItem[], page) => {
        return [...cur, ...(page?.data || [])];
    }, []);
    const marketplaceItemToEdit: IMarketplaceItem | undefined = lineCardDialog.id
        ? marketplaceItems?.find(item => item.id === lineCardDialog.id)
        : undefined;

    const handleOpenDetailLineCardDialog = (itemId: string) => {
        setDetailDialogOpen(true);
        setDefaultItemId(itemId);
    };

    const handleDelete = async (item: IMarketplaceItem) => {
        const answer = await getUserConfirmation("Remove Vendor?", {
            mui: true,
            modalTheme: "error",
            title: `Remove Vendor?`,
            form: (
                <Box display="flex" flexDirection="column" justifyContent="flex-start">
                    <Typography variant="body2" fontWeight="bold">
                        Are you sure you want to remove this {item.name || "this vendor"} from your{" "}
                        {DISTRIBUTOR_LINE_CARD_TEXT.toLowerCase()}?
                    </Typography>
                    <Typography variant="body2">
                        This cannot be undone. Please confirm if you would like to proceed.
                    </Typography>
                </Box>
            ),
            okButtonText: (
                <Box gap={1}>
                    <DeleteOutlineOutlinedIcon /> REMOVE
                </Box>
            ),
        });
        if (answer.value) {
            updateCompany.mutate({
                mutate_type: MutateTypes.DeleteMarketplacePartner,
                friendly_url: props.friendly_url,
                id: item.partner_id,
                mutate_is_updating_append: item.id,
            });
        }
    };

    const handleEdit = (item: IMarketplaceItem) => {
        setLineCardDialog({open: true, id: item.id});
    };

    useEffect(() => {
        if (pageNum && companyMarketplace.hasNextPage) {
            companyMarketplace.fetchNextPage();
        }
    }, [pageNum]);

    return (
        <ErrorBoundary>
            <Box>
                <PageInnerHeader
                    id="distributor-marketplace-header"
                    title={{text: DISTRIBUTOR_LINE_CARD_TEXT, sx: {fontSize: "26px !important"}}}
                    indicator={null}
                    actions={{
                        primary: canEdit
                            ? {
                                  id: "importPartnersFromCSV",
                                  children: (
                                      <>
                                          <UploadOutlinedIcon />
                                          Import
                                      </>
                                  ),
                                  onClick: () => setImportDialog({open: true}),
                                  variant: "outlined",
                              }
                            : undefined,
                        secondary: canEdit
                            ? {
                                  id: "addSubscription",
                                  children: (
                                      <>
                                          <AddOutlinedIcon />
                                          Add New
                                      </>
                                  ),
                                  onClick: () => setLineCardDialog({open: true, id: undefined}),
                                  variant: "outlined",
                              }
                            : undefined,
                        tertiary: undefined,
                        back: undefined,
                    }}
                    searchState={[
                        marketPlaceOptions.search,
                        (newValue: string) => setMarketplaceOptions({...marketPlaceOptions, search: newValue}),
                    ]}
                    filters={{
                        sort: {
                            multiple: false,
                            placeholder: "Sort",
                            is_public: true,
                            is_sorting: true,
                            is_date: false,
                            items: [
                                {id: "ASC", children: "ASCENDING (Aa-Zz)"},
                                {id: "DESC", children: "DESCENDING (Zz-Aa)"},
                            ],
                        },
                    }}
                    filterState={[
                        {},
                        (newFilters: any) => {
                            setMarketplaceOptions({...marketPlaceOptions, sort: newFilters?.sort});
                        },
                    ]}
                />
                <Box
                    // display="grid"
                    // gridTemplateColumns={{sm: "1fr", md: "repeat(3, 1fr)", xl: "repeat(3, 1fr)"}}
                    display="flex"
                    flexDirection="row"
                    flexWrap="wrap"
                    alignItems="flex-start"
                    alignContent="flex-start"
                    alignSelf="stretch"
                    gap={3}
                    marginTop={3}>
                    {companyMarketplace.isLoading ? (
                        <>
                            {Array(9)
                                .fill(null)
                                .map((_, i) => (
                                    <Skeleton height={300} key={i} />
                                ))}
                        </>
                    ) : marketplaceItems?.length ? (
                        marketplaceItems?.map(item => {
                            const isDeleting = isUpdating[MutateTypes.DeleteMarketplacePartner + item.id];
                            const firstTwo = item.subcategories.slice(0, 2);
                            const firstCategoryNameLength = firstTwo[0]?.name?.length;
                            const firstCategoryTakesFullWidth = firstCategoryNameLength >= 32;
                            return (
                                <BorderedBox
                                    key={item.id}
                                    sx={{
                                        position: "relative",
                                        cursor: "pointer",
                                        minWidth: 280,
                                        maxWidth: 300,
                                        padding: 2,
                                        flex: "1 0 0",
                                        height: {xs: "fit-content", md: 285},
                                    }}
                                    onClick={() => handleOpenDetailLineCardDialog(item.id)}>
                                    {canEdit && (
                                        <Box position="absolute" top={8} right={8} zIndex={2}>
                                            <MenuButton
                                                id={`more-actions-${item.id}`}
                                                preventDefaultClick
                                                buttonVariant="iconButton"
                                                buttonProps={{
                                                    sx: {
                                                        width: "24px",
                                                        minWidth: "24px!important",
                                                        height: "24px",
                                                        boxShadow: "none",
                                                        position: "relative",
                                                        zIndex: 10,
                                                    },
                                                    disabled: isDeleting,
                                                }}
                                                containerStyles={{
                                                    flexShrink: 0,
                                                }}
                                                items={[
                                                    {
                                                        id: "edit_" + item.id,
                                                        children: (
                                                            <>
                                                                <EditOutlinedIcon color="neutral" /> Edit
                                                            </>
                                                        ),
                                                        onClick: () => handleEdit(item),
                                                    },
                                                    {
                                                        id: "delete_" + item.id,
                                                        children: (
                                                            <>
                                                                <DeleteOutlineOutlinedIcon color="error" />
                                                                <Typography color="error" variant="body4">
                                                                    Remove
                                                                </Typography>
                                                            </>
                                                        ),
                                                        onClick: () => handleDelete(item),
                                                    },
                                                ]}
                                                aria-label="settings">
                                                {isDeleting ? <Loader inline loading /> : <MoreVert color="neutral" />}
                                            </MenuButton>
                                        </Box>
                                    )}
                                    <Box
                                        display="grid"
                                        gridTemplateColumns={{sm: "1fr", md: "100px 1fr"}}
                                        gap={2}
                                        alignItems="center">
                                        <AvatarComponentV2
                                            isCompany
                                            user={{
                                                avatar: item.avatar || "",
                                                name: item.name,
                                                friendly_url: item.friendly_url,
                                                type: "",
                                            }}
                                            size={100}
                                            sx={{
                                                cursor: "pointer",
                                                borderRadius: 1,
                                                border: `1px solid ${theme.palette.neutral[300]}`,
                                                "&, & img": {
                                                    borderRadius: 2,
                                                    objectFit: "cover",
                                                },
                                            }}
                                            isRedirectEnabled={false}
                                        />
                                        <Typography
                                            variant="body1"
                                            fontInter
                                            color={theme.palette.blue[800]}
                                            sx={{
                                                display: "-webkit-box",
                                                WebkitBoxOrient: "vertical",
                                                WebkitLineClamp: 3,
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                maxHeight: "6em",
                                            }}
                                            title={item.name}>
                                            {item.name}
                                        </Typography>
                                    </Box>
                                    <Box
                                        display="flex"
                                        flexDirection="row"
                                        alignItems="center"
                                        justifyContent={item.rating ? "flex-start" : "center"}
                                        gap={1}
                                        borderRadius={1}
                                        padding={item.rating ? 0 : "4px 8px"}
                                        bgcolor={item.rating ? "none" : theme.palette.neutral[200]}>
                                        {item.rating ? (
                                            <>
                                                <ControlledStarRating
                                                    id={`rating_${item.id}`}
                                                    value={item.rating || 0}
                                                    showLabels={false}
                                                    maxRating={5}
                                                    iconSize={24}
                                                    containerClassName="m-0"
                                                />
                                                <Typography
                                                    variant="subtitle2"
                                                    fontWeight={"700 !important"}
                                                    letterSpacing={0}>
                                                    {item.rating?.toFixed(1) || "No ratings yet."}
                                                </Typography>
                                            </>
                                        ) : (
                                            <Typography
                                                variant="subtitle3"
                                                fontWeight={600}
                                                color={theme.palette.neutral[600]}
                                                letterSpacing={0}>
                                                No reviews yet
                                            </Typography>
                                        )}
                                    </Box>
                                    <Box
                                        position="relative"
                                        width="100%"
                                        overflow="hidden"
                                        display="flex"
                                        height="16px">
                                        <Typography
                                            variant="subtitle3"
                                            color={theme.palette.blue[600]}
                                            sx={{
                                                whiteSpace: "nowrap",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                maxWidth: "calc(100% - 24px)",
                                            }}>
                                            {firstTwo?.map((subcategory, i) => (
                                                <>
                                                    <Link
                                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                            ":friendly_url",
                                                            subcategory.friendly_url || "",
                                                        )}
                                                        target="_blank"
                                                        onClick={e => e.stopPropagation()}
                                                        title={subcategory.name}>
                                                        {subcategory.name}
                                                    </Link>
                                                    {i + 1 !== item.subcategories.length ? ", " : ""}
                                                </>
                                            ))}
                                        </Typography>
                                        {item.subcategories.length > (firstCategoryTakesFullWidth ? 1 : 2) ? (
                                            <CPTooltip
                                                light
                                                leaveDelay={250}
                                                title={
                                                    <Box display="flex" flexDirection="column" gap={0}>
                                                        {item.subcategories
                                                            .slice(
                                                                firstCategoryTakesFullWidth ? 1 : 2,
                                                                item.subcategories.length,
                                                            )
                                                            ?.map(subcategory => {
                                                                return (
                                                                    <Link
                                                                        key={subcategory.id}
                                                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                                            ":friendly_url",
                                                                            subcategory.friendly_url || "",
                                                                        )}
                                                                        style={{width: "100%"}}
                                                                        target="_blank"
                                                                        onClick={e => e.stopPropagation()}>
                                                                        <ListItem
                                                                            style={{
                                                                                width: "100%",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                            }}>
                                                                            <Typography
                                                                                variant="body3"
                                                                                fontInter
                                                                                color={theme.palette.blue[800]}
                                                                                style={{
                                                                                    textOverflow: "ellipsis",
                                                                                    overflow: "hidden",
                                                                                    lineClamp: 1,
                                                                                    maxWidth: "100%",
                                                                                }}>
                                                                                {subcategory.name}
                                                                            </Typography>
                                                                        </ListItem>
                                                                    </Link>
                                                                );
                                                            })}
                                                    </Box>
                                                }>
                                                <Typography
                                                    onClick={e => {
                                                        e.stopPropagation();
                                                    }}
                                                    sx={{
                                                        position: "absolute",
                                                        right: 0,
                                                        top: 0,
                                                        backgroundColor: "#fff",
                                                        paddingLeft: 1,
                                                    }}
                                                    zIndex={100}
                                                    variant="subtitle3"
                                                    color={theme.palette.blue[600]}>{` +${
                                                    firstCategoryTakesFullWidth
                                                        ? item.subcategories?.length - 1
                                                        : item.subcategories.length - 2
                                                }`}</Typography>
                                            </CPTooltip>
                                        ) : null}
                                    </Box>
                                    <Button
                                        id={`request-demo-${item.id}`}
                                        color="secondary"
                                        variant="outlined"
                                        onClick={e => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            setContactVendorModal({
                                                open: true,
                                                friendly_url: item.friendly_url,
                                                companyName: company?.data?.name + " - " + item.name,
                                            });
                                        }}>
                                        {REQUEST_DEMO_TEXT}
                                    </Button>
                                </BorderedBox>
                            );
                        })
                    ) : (
                        <Typography
                            variant="subtitle1"
                            fontInter
                            sx={{color: theme.palette.neutral[600], display: "flex", gap: "6px"}}>
                            There are currently no vendor line cards available.
                        </Typography>
                    )}
                    <Box
                        gridColumn="1/-1"
                        ref={lastElementRef}
                        display="grid"
                        gridTemplateColumns={{sm: "1fr", md: "repeat(3, 1fr)", lg: "repeat(3, 1fr)"}}
                        gap={3}>
                        {companyMarketplace.isFetchingNextPage ? (
                            <>
                                {Array(3)
                                    .fill(null)
                                    .map((_, i) => (
                                        <Skeleton height={300} key={i} />
                                    ))}
                            </>
                        ) : null}
                    </Box>
                </Box>
            </Box>
            <AddLineCardDialog
                open={lineCardDialog.open}
                onClose={() => setLineCardDialog({...lineCardDialog, open: false})}
                friendly_url={props.friendly_url}
                mode={lineCardDialog.id ? "EDIT" : "CREATE"}
                marketplaceItemToEdit={marketplaceItemToEdit}
            />
            <ImportPartnersFromCSVDialog
                open={importDialog.open}
                onClose={() => {
                    setImportDialog({open: false});
                }}
                onVendorsImported={() => {
                    companyMarketplace.refetch();
                }}
                company={activeCompany}
                title="Import CSV"
            />
            <DetailLineCardDialog
                open={detailDialogOpen}
                onClose={() => setDetailDialogOpen(false)}
                friendlyUrl={props.friendly_url}
                defaultItemId={defaultItemId}
            />
            {contactVendorModal.open && (
                <ContactVendorDialog
                    {...contactVendorModal}
                    onClose={() => {
                        setContactVendorModal({
                            open: false,
                            friendly_url: "",
                            companyName: "",
                        });
                    }}
                    customUri={window.location.href}
                    hideToggleBtn
                />
            )}
        </ErrorBoundary>
    );
};

export default MarketplaceTab;
