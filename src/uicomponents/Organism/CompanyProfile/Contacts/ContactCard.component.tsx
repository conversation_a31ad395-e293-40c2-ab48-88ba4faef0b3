import useTheme from "@mui/material/styles/useTheme";
import Box from "../../../Atoms/Box/Box.component";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import {useGetFriendlyUrl} from "../../../../utils/profile.util";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {useEffect, useState} from "react";
import Button from "../../../Atoms/Button/Button.Component";
import {FormProvider, useForm} from "react-hook-form";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined";
import EmailOutlinedIcon from "@mui/icons-material/EmailOutlined";
import {validateEmail} from "../../../../utils/validation.util";
import Typography from "../../../Atoms/Typography/Typography.component";
import EditOutlined from "@mui/icons-material/EditOutlined";
import Loader from "../../../../utils/loader";
import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import {VERIFICATION_INVALID_EMAIL_ERROR} from "../../../../constants/errorMessages.constant";

const ContactCard = ({contact, handleDelete, isDeleting, isEditable}) => {
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [showError, setShowError] = useState<boolean>(false);
    const theme = useTheme();
    const methods = useForm();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const {company, updateCompany, isUpdating} = useCompany(friendly_url, {
        isCompany: true,
        calls: {all: false, company: true},
    });
    const isSaving = isUpdating[MutateTypes.UpdateCompanyContact + contact.id];

    const handleSave = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const values = methods.getValues();
        const filledInEitherOr = !!values["email"] || !!values["phone"];
        if (!filledInEitherOr) {
            setShowError(true);
            return;
        }
        showError && setShowError(false);
        updateCompany.mutate({
            mutate_type: MutateTypes.UpdateCompanyContact,
            company_id: company.data?.id,
            mutate_is_updating_append: contact.id,
            body: {
                id: contact.id,
                title: values["title"],
                email: values["email"],
                phone: values["phone"],
            },
            onSuccess: () => {
                methods.reset();
                setIsEditing(false);
            },
        });
    };

    useEffect(() => {
        if (isEditing) {
            methods.setValue("email", contact.email);
            methods.setValue("phone", contact.phone);
            methods.setValue("title", contact.title);
        }
        if (!isEditing && showError) {
            setShowError(false);
        }
    }, [isEditing]);

    return (
        <BorderedBox sx={{display: "flex", flexDirection: "column", gap: 1, overflow: "hidden"}}>
            {showError && isEditing && (
                <AlertBanner
                    id={"error-banner-" + contact.id}
                    title={{text: "Please enter a phone number and/or an email", sx: {gap: 0}}}
                    subTitle={{}}
                    variant="error"
                    contentContainerSx={{gap: 0}}
                />
            )}
            {isEditing ? (
                <FormProvider {...methods}>
                    <TextBoxComponent id="title" label="Title" placeholder="Input title" validateOnTheFly isRequired />
                    <Box display="flex" flexDirection="row" gap="10px" alignItems="center">
                        <PhoneOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                        <TextBoxComponent
                            id={"phone"}
                            label="Phone"
                            placeholder="Input phone number"
                            validateOnTheFly
                            containerSx={{flex: 1}}
                        />
                    </Box>
                    <Box display="flex" flexDirection="row" gap="10px" alignItems="center">
                        <EmailOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                        <TextBoxComponent
                            id="email"
                            label="Email"
                            placeholder="Input email address"
                            validateOnTheFly
                            validationSchema={v => (v ? validateEmail(v as string) : true)}
                            containerSx={{flex: 1}}
                            tooltipHelperText={VERIFICATION_INVALID_EMAIL_ERROR}
                        />
                    </Box>
                    <Box display="flex" flexDirection="row" gap={1} alignItems="center" marginTop={2}>
                        <Button id="save-new" color="blue" variant="contained" onClick={handleSave} loading={isSaving}>
                            SAVE
                        </Button>
                        <Button
                            id="cancel-new"
                            color="blue"
                            variant="text"
                            onClick={() => setIsEditing(false)}
                            disabled={isSaving}>
                            CANCEL
                        </Button>
                    </Box>
                </FormProvider>
            ) : (
                <>
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        justifyContent="space-between"
                        marginBottom={3}>
                        <Typography
                            variant="subtitle1"
                            fontInter
                            fontWeight={"700 !important"}
                            color={theme.palette.neutral[700]}>
                            {contact.title || "Contact"}
                        </Typography>
                        {isEditable && (
                            <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                                <Box onClick={() => setIsEditing(true)}>
                                    <EditOutlined
                                        sx={{height: 24, width: 24, cursor: "pointer"}}
                                        htmlColor={theme.palette.blue[600]}
                                    />
                                </Box>
                                <Box onClick={() => handleDelete(contact.id)}>
                                    {isDeleting ? (
                                        <Loader inline loading />
                                    ) : (
                                        <DeleteOutlined sx={{height: 24, width: 24, cursor: "pointer"}} color="error" />
                                    )}
                                </Box>
                            </Box>
                        )}
                    </Box>
                    {!!contact.phone && (
                        <Box display="flex" flexDirection="row" gap={1} alignItems="center">
                            <PhoneOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                            <Typography
                                variant="body3"
                                fontInter
                                color={theme.palette.neutral[800]}
                                title={contact.phone || "No phone number set"}
                                sx={{
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    flex: 1,
                                    minWidth: 0,
                                }}>
                                {contact.phone || "No phone number set"}
                            </Typography>
                        </Box>
                    )}
                    {!!contact.email && (
                        <Box display="flex" flexDirection="row" gap={1} alignItems="center">
                            <EmailOutlinedIcon sx={{width: 24, height: 24}} htmlColor={theme.palette.neutral[500]} />
                            <Typography
                                variant="body3"
                                fontInter
                                color={theme.palette.neutral[800]}
                                title={contact.email || "No email set"}
                                sx={{
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    flex: 1,
                                    minWidth: 0,
                                }}>
                                {contact.email || "No email set"}
                            </Typography>
                        </Box>
                    )}
                </>
            )}
        </BorderedBox>
    );
};

export default ContactCard;
