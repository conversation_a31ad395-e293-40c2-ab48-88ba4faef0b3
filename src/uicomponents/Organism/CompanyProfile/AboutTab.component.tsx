import Grid from "@mui/material/Grid";
import Box from "../../Atoms/Box/Box.component";
import AboutUsSection from "./AboutUs/AboutUsSection.component";
import AdditionalInformationSection from "./AdditionalInformation/AdditionalInformationSection.component";
import LinksSection from "./CompanyLinks/LinksSection.component";
import PhoneAndEmailSection from "./Contacts/PhoneAndEmailSection.component";
import CountriesSupportedSection from "./CountriesSupported/CountriesSupportedSection.component";
import LanguagesSupportedSection from "./LanguagesSupported/LanguagesSupportedSection.component";
import ProductSegmentsSection from "./ProductSegment/ProductSegmentsSection.component";
import VerticalFocusSection from "./VerticalFocus/VerticalFocusSection.component";

interface IProps {
    profile_type: "company" | "user" | "msp" | "client" | "direct";
    canEditData?: boolean;
    isLoadingCompany?: boolean;
}

const AboutTab = ({canEditData = false, isLoadingCompany, ...props}: IProps) => {
    const isVendor: boolean = props.profile_type === "company";
    const isMSP: boolean = props.profile_type === "msp";
    const isMSPClient: boolean = props.profile_type === "client";
    const isDirect: boolean = props.profile_type === "direct";

    return (
        <Box display="flex" flexDirection="column" gap={3}>
            <AboutUsSection
                profile_type={isDirect ? "msp" : (props.profile_type as "company" | "user" | "client")}
                showHeadOfficeAddress
                canEditData={canEditData}
            />
            {isVendor && <ProductSegmentsSection />}
            {isMSP && !isLoadingCompany ? <VerticalFocusSection /> : null}
            {isMSP || isMSPClient || isDirect ? <PhoneAndEmailSection canEditData={canEditData} /> : null}
            {!isDirect && <AdditionalInformationSection canEditData={canEditData} />}
            {isVendor ? <PhoneAndEmailSection /> : null}
            {isVendor && (
                <Grid container display="grid" gridTemplateColumns={{sm: "1fr", lg: "1fr 1fr"}} gap={3}>
                    <Grid item>
                        <CountriesSupportedSection />
                    </Grid>
                    <Grid item>
                        <LanguagesSupportedSection />
                    </Grid>
                </Grid>
            )}
            <LinksSection canEditData={canEditData} />
        </Box>
    );
};

AboutTab.defaultProps = {
    profile_type: "company",
};

export default AboutTab;
