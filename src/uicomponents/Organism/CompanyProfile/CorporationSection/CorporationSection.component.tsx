import Box from "../../../Atoms/Box/Box.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import useTheme from "@mui/material/styles/useTheme";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {ICompany} from "../../../../Interfaces/company.interface";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {VENDOR_TYPES} from "../../../../constants/vendorProfiles.constants";

interface IProps {
    corporation: ICompany;
}

const CorporationSection = (props: IProps) => {
    const theme = useTheme();
    const {corporation} = props;

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={2}>
                <PageInnerHeader
                    id="corporation-section"
                    title={{text: "Corporation", sx: {fontSize: "23px !important"}}}
                    indicator={null}
                    dividerColor={theme.palette.neutral[500]}
                />
                <Box display="flex" flexDirection="row" flexWrap="wrap" alignItems="center" gap={2}>
                    <AvatarComponentV2
                        size={45}
                        user={{
                            avatar: corporation?.avatar || "",
                            is_distributor: corporation?.is_distributor,
                            profile_type: VENDOR_TYPES.MSP_BUSINESS_PREMIUM,
                            friendly_url: corporation?.friendly_url,
                            name: corporation?.name,
                            type: "",
                        }}
                        isCompany
                        showProfileType
                        profileTypeMinimized
                    />
                    <Typography variant="body2" color={theme.palette.neutral[700]} fontWeight={500}>
                        {corporation?.name}
                    </Typography>
                </Box>
            </Box>
        </ErrorBoundary>
    );
};

export default CorporationSection;
