import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import BallotOutlined from "@mui/icons-material/BallotOutlined";
import CollectionsBookmarkOutlined from "@mui/icons-material/CollectionsBookmarkOutlined";
import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import EditOutlined from "@mui/icons-material/EditOutlined";
import FileCopyOutlined from "@mui/icons-material/FileCopyOutlined";
import VideoLibraryOutlined from "@mui/icons-material/VideoLibraryOutlined";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useRef, useState} from "react";
import {useNavigate, useSearchParams} from "react-router-dom";
import AddContentModal from "../../../../components/Modal/addContentModal.component";
import {DEFAULT_QUERY_CONFIGS} from "../../../../constants/commonStrings.constant";
import routeConfig from "../../../../constants/routeConfig";
import {MediaTypes} from "../../../../enums/mediaTypes.enum";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import {usePeople} from "../../../../hooks/fetches/usePeople";
import useAuthState from "../../../../hooks/useAuthState";
import usePermissions from "../../../../hooks/usePermissions";
import {IBlog} from "../../../../Interfaces/blog.interface";
import {ICompany} from "../../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IMediaGallery} from "../../../../Interfaces/mediaGallery.interface";
import {IPeople} from "../../../../Interfaces/people.interface";
import {IDocument, IVideos} from "../../../../Interfaces/profiles.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {companyService} from "../../../../services/company.service";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import {buildFilters} from "../../../../utils/filters.util";
import {useGetFriendlyUrl} from "../../../../utils/profile.util";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {IMenuButtonItem} from "../../../Molecules/MenuButton/menuButton.component";
import ViewGalleryDialog from "../../../Molecules/ViewGalleryDialog/ViewGalleryDialog.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import ContentSection, {IContentSectionProps} from "./ContentSection.component";
import AssetPostingV2 from "../../../../components/Modal/AssetsPostingV2.component";
import NewFileDialog from "../../NewFileDialog/NewFileDialog.component";

interface IProps {
    profile_type: "user" | "company" | "msp";
}

const SECTION_IDS = {
    IMAGES: "images",
    BLOGS: "blogs",
    DOCS: "documents",
    VIDEOS: "videos",
};

const CARD_ACTIONS: {
    EDIT: "EDIT";
    DELETE: "DELETE";
    OPEN: "OPEN";
    DOWNLOAD: "DOWNLOAD";
} = {
    EDIT: "EDIT",
    DELETE: "DELETE",
    OPEN: "OPEN",
    DOWNLOAD: "DOWNLOAD",
};

type ICardActions = "EDIT" | "DELETE" | "OPEN" | "DOWNLOAD";

type IContentSectionTypes = "video" | "article" | "images" | "document";

const ContentTab = (props: IProps) => {
    const [editVideo, setEditVideo] = useState<IVideos>();
    const [editDocument, setEditDocument] = useState<IDocument>();
    const [galleryToEdit, setGalleryToEdit] = useState<IMediaGallery>();
    const [galleryToView, setGalleryToView] = useState<IMediaGallery>();
    const [search, setSearch] = useState("");
    const [filters, setFilters] = useState<any>({sort: "sorting_created_at__DESC"});
    const [totalItems, setTotalItems] = useState(0);
    const {authState} = useAuthState();
    const navigate = useNavigate();
    const friendly_url = useGetFriendlyUrl(location.pathname);
    const isCompany = props.profile_type === "company" || props.profile_type === "msp";
    const {
        company,
        updateCompany,
        isUpdating: isUpdatingCompany,
    } = useCompany(friendly_url, {isCompany, calls: {all: false, company: true}, companyParams: ["id"]});
    const {
        people,
        updatePeople,
        isUpdating: isUpdatingPeople,
    } = usePeople(friendly_url, {isCompany, calls: {all: false, people: true}});
    const isUpdating = isCompany ? isUpdatingCompany : isUpdatingPeople;
    const info = isCompany ? company.data : people.data;
    const {hasPermissions} = usePermissions(isCompany ? {overrideCompanyId: info?.id} : undefined);
    const isEditable = authState?.id ? hasPermissions([PERMISSION_GROUPS.MANAGE_CONTENT_UPDATE]) : false;
    const [params, setParams] = useSearchParams();
    const addNew = params.get("add");
    const totals = useRef<{[key: string]: number}>({});

    const sectionsFilters = useQuery<any | null>(
        ["company-content-sections-filters", friendly_url],
        () => {
            if (!friendly_url) return new Promise(resolve => resolve(null));
            const promiseToReturn: Promise<any | any> = new Promise((resolve, reject) => {
                return companyService
                    .getCompanyContentSectionsFilters(friendly_url)
                    .then((r: AxiosResponse<any>) => {
                        resolve(r.data);
                    })
                    .catch((e: AxiosError<any>) => reject(e));
            });
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!friendly_url && !!info?.id,
            refetchOnWindowFocus: false,
            getNextPageParam: res => {
                return res.meta?.last_page > res.meta?.current_page ? res.meta?.current_page + 1 : null;
            },
        },
    );

    const deleteContent = async (subject: IVideos | IDocument | IMediaGallery | IBlog, type: IContentSectionTypes) => {
        const title =
            (subject as IVideos | IDocument)?.title ||
            (subject as IVideos | IDocument | IMediaGallery).custom_properties?.title ||
            "";
        const answer = await getUserConfirmation("", {
            form: (
                <div style={{textAlign: "center", lineHeight: "1.7"}}>
                    <div>Are you sure you want to delete the {type},</div>
                    <div>
                        <strong>{title}</strong>
                    </div>
                    <div>This process cannot be undone.</div>
                </div>
            ),
            okButtonText: "DELETE",
            cancelButtonText: "CANCEL",
            title: `Delete ${type}?`,
            mui: true,
            modalTheme: "error",
        });
        if (!answer.value) return;
        if (type === "video") {
            const mutate_data = {
                video_id: subject.id,
                company_id: info?.id,
                people_id: info?.id,
                mutate_type: MutateTypes.DelVideo,
                mutate_is_updating_append: subject.id,
            };
            if (isCompany) {
                return updateCompany.mutate(mutate_data);
            }
            return updatePeople.mutate(mutate_data);
        } else if (type === "document") {
            return updateCompany.mutate({
                company_id: info?.id,
                doc_id: subject.id,
                mutate_type: MutateTypes.DelDoc,
                mutate_is_updating_append: subject.id,
            });
        } else if (type === "article") {
            const updateObj = {
                company_id: info?.id || "",
                people_id: info?.id || "",
                blog_id: subject.id,
                mutate_type: MutateTypes.DelBlog,
                status: (subject as IBlog).status,
                mutate_is_updating_append: subject.id,
            };
            if (isCompany) return updateCompany.mutate(updateObj);
            return updatePeople.mutate(updateObj);
        } else if (type === "images") {
            if (isCompany)
                return updateCompany.mutate({
                    gallery_id: subject.id,
                    mutate_type: MutateTypes.DelGallery,
                    mutate_is_updating_append: subject.id,
                });
            return updatePeople.mutate({
                gallery_id: subject.id,
                mutate_type: MutateTypes.DelGallery,
                mutate_is_updating_append: subject.id,
            });
        }
    };

    const handleOptionClick = (
        action: ICardActions,
        type: IContentSectionTypes,
        subject: IMediaGallery | IVideos | any,
    ) => {
        switch (action) {
            case CARD_ACTIONS.EDIT:
                switch (type) {
                    case "document":
                        setEditDocument(subject as IDocument);
                        break;
                    case "article":
                        navigate(routeConfig.UpsertBlog.path + `?blogToEdit=${(subject as IBlog)?.friendly_url}`);
                        break;
                    case "images":
                        setGalleryToEdit(subject?.id || "");
                        break;
                    case "video":
                        setEditVideo(subject as IVideos);
                        break;
                    default:
                        break;
                }
                break;
            case CARD_ACTIONS.DELETE:
                switch (type) {
                    case "document":
                    case "article":
                    case "images":
                    case "video":
                        deleteContent(subject as IDocument | IBlog | IMediaGallery | IVideos, type);
                        break;
                    default:
                        break;
                }
                break;
            case CARD_ACTIONS.OPEN:
                break;
            case CARD_ACTIONS.DOWNLOAD:
                break;
            default:
                break;
        }
    };

    const getContentCardActions = (subject: any, type: IContentSectionTypes): IMenuButtonItem<false>[] | undefined => {
        if (!subject || !info?.id) return undefined;
        const cardActions: IMenuButtonItem<false>[] = [
            {
                id: `edit-${type}`,
                sort: "c",
                children: (
                    <Typography
                        variant="subtitle4"
                        fontInter
                        fontWeight={600}
                        sx={theme => ({
                            color: theme.palette.secondary.main,
                            display: "flex",
                            alignItems: "center",
                            gap: "16px",
                        })}>
                        <EditOutlined color="neutral" /> Edit
                    </Typography>
                ),
                onClick: () => handleOptionClick(CARD_ACTIONS.EDIT, type, subject),
            },
            {
                id: `delete-${type}`,
                sort: "z",
                children: (
                    <Typography
                        variant="subtitle4"
                        fontInter
                        fontWeight={600}
                        sx={theme => ({
                            color: theme.palette.error.main,
                            display: "flex",
                            alignItems: "center",
                            gap: "16px",
                        })}>
                        <DeleteOutlined color="error" /> Delete
                    </Typography>
                ),
                onClick: () => handleOptionClick(CARD_ACTIONS.DELETE, type, subject),
            },
        ];
        return isEditable ? cardActions : undefined;
    };

    const handleTotalItemsChange = useCallback(
        (total_items: number, id: string) => {
            totals.current = {...totals.current, [id]: total_items};
            if (Object.keys(totals.current)?.length === Object.keys(SECTION_IDS).length) {
                const total: number = Object.values(totals.current)?.reduce((prev, cur) => {
                    return (prev += cur);
                }, 0);
                setTotalItems(total);
            }
        },
        [totals],
    );

    const handleSetGalleryToView = useCallback(gallery => {
        setGalleryToView(gallery);
    }, []);

    const imagesSection: IContentSectionProps = {
        id: SECTION_IDS.IMAGES + friendly_url,
        title: "Images",
        service: options => companyService.getCompanyGalleryContent(friendly_url || "", options),
        contentCardProps: (gallery: IMediaGallery) => ({
            headerActions: getContentCardActions(gallery, "images"),
            headerActionsLoading: isUpdating[MutateTypes.DelGallery + gallery.id],
            content: {
                id: gallery.id,
                name: gallery?.custom_properties?.title,
                description: gallery?.custom_properties?.description,
                tags: gallery?.tags,
                created_at: gallery.created_at,
                type: "image",
                image: gallery.media?.[0]?.url || "",
                images: gallery.media,
                author: gallery.author?.author_name,
            },
            cardAction: {
                onClick: () => {
                    handleSetGalleryToView(gallery);
                },
            },
        }),
    };

    const blogSection: IContentSectionProps = {
        id: SECTION_IDS.BLOGS + friendly_url,
        title: "Blogs",
        service: options => companyService.getCompanyBlogContent(friendly_url || "", options),
        contentCardProps: (article: IBlog) => {
            return {
                content: {
                    id: article.id,
                    name: article?.title,
                    description: article?.excerpt,
                    tags: article.tags,
                    created_at: article.created_at,
                    image: article?.images?.[0]?.src,
                    mediaVisibility: undefined,
                    type: "blog",
                    author: article.author?.name,
                },
                headerActions: getContentCardActions(article, "article"),
                headerActionsLoading: isUpdating[MutateTypes.DelBlog + article.id],
                cardAction: {
                    to: routeConfig.PublicBlog.path.replace(":friendly_url", article.friendly_url || article.id),
                    target: "_blank",
                },
                statusBadge: {
                    color: article.status === "Published" ? "success" : "warning",
                    title: article.status,
                    children: article.status,
                },
            };
        },
    };

    const documentsSection: IContentSectionProps = {
        id: SECTION_IDS.DOCS + friendly_url,
        title: "Documents",
        service: options => companyService.getCompanyDocumentsContent(friendly_url || "", options),
        contentCardProps: (doc: IDocument) => {
            return {
                content: {
                    id: doc.id,
                    name: doc.custom_properties?.title,
                    description: doc.custom_properties?.description,
                    tags: doc.tags,
                    created_at: doc.created_at,
                    image: "",
                    type: "profile-document",
                    mime_type: doc.mime_type,
                    file_name: doc.file_name,
                    author: doc.author?.name,
                },
                headerActions: getContentCardActions(doc, "document"),
                headerActionsLoading: isUpdating[MutateTypes.DelDoc + doc.id],
                canDownload: true,
                canOpen: true,
                companyId: info?.id,
                cardAction: {
                    onClick: () => {
                        doc.url && window.open(doc.url, "_blank");
                    },
                },
            };
        },
    };

    const videosSection: IContentSectionProps = {
        id: SECTION_IDS.VIDEOS + friendly_url,
        title: "Videos",
        service: options => companyService.getCompanyVideosContent(friendly_url || "", options),
        contentCardProps: (video: IVideos) => ({
            headerActions: getContentCardActions(video, "video"),
            headerActionsLoading: isUpdating[MutateTypes.DelVideo + video.id],
            content: {
                id: video.id,
                name: video?.custom_properties?.title,
                description: video?.custom_properties?.description,
                tags: video?.tags,
                created_at: video.created_at,
                image: video?.thumbnail_url,
                type: "video",
                mediaVisibility: undefined,
                author: video?.custom_properties?.author_name,
            },
            cardAction: {
                to: routeConfig.WatchVideo.path.replace(":id", video.id),
                target: "_blank",
            },
        }),
    };

    return (
        <Box display="flex" flexDirection="column" gap={3}>
            <PageInnerHeader
                id="content-public-content-header"
                title={{text: "Public Content", sx: {fontSize: "23px !important"}}}
                actions={{
                    primary: isEditable
                        ? {
                              id: "add-new-content",
                              children: (
                                  <>
                                      <AddOutlinedIcon />
                                      Add New
                                  </>
                              ),
                              variant: "outlined",
                              items: [
                                  {
                                      id: "add-new-public-content-blog",
                                      onClick: () => {
                                          navigate(routeConfig.UpsertBlog.path);
                                      },
                                      children: (
                                          <Typography
                                              variant="subtitle4"
                                              fontInter
                                              fontWeight={600}
                                              sx={theme => ({
                                                  color: theme.palette.secondary.main,
                                                  display: "flex",
                                                  alignItems: "center",
                                                  gap: "16px",
                                              })}>
                                              <FileCopyOutlined color="neutral" />
                                              Blog Post
                                          </Typography>
                                      ),
                                  },
                                  {
                                      id: "add-new-public-content-document",
                                      onClick: () => {
                                          if (params.has("add")) {
                                              params.set("add", "document");
                                          } else {
                                              params.append("add", "document");
                                          }
                                          setParams(params);
                                      },
                                      children: (
                                          <Typography
                                              variant="subtitle4"
                                              fontInter
                                              fontWeight={600}
                                              sx={theme => ({
                                                  color: theme.palette.secondary.main,
                                                  display: "flex",
                                                  alignItems: "center",
                                                  gap: "16px",
                                              })}>
                                              <CollectionsBookmarkOutlined color="neutral" />
                                              Document
                                          </Typography>
                                      ),
                                  },
                                  {
                                      id: "add-new-public-content-gallery",
                                      onClick: () => {
                                          if (params.has("add")) {
                                              params.set("add", "album");
                                          } else {
                                              params.append("add", "album");
                                          }
                                          setParams(params);
                                      },
                                      children: (
                                          <Typography
                                              variant="subtitle4"
                                              fontInter
                                              fontWeight={600}
                                              sx={theme => ({
                                                  color: theme.palette.secondary.main,
                                                  display: "flex",
                                                  alignItems: "center",
                                                  gap: "16px",
                                              })}>
                                              <BallotOutlined color="neutral" />
                                              Image Gallery
                                          </Typography>
                                      ),
                                  },
                                  {
                                      id: "add-new--public-content-video",
                                      onClick: () => {
                                          if (params.has("add")) {
                                              params.set("add", "video");
                                          } else {
                                              params.append("add", "video");
                                          }
                                          setParams(params);
                                      },
                                      children: (
                                          <Typography
                                              variant="subtitle4"
                                              fontInter
                                              fontWeight={600}
                                              sx={theme => ({
                                                  color: theme.palette.secondary.main,
                                                  display: "flex",
                                                  alignItems: "center",
                                                  gap: "16px",
                                              })}>
                                              <VideoLibraryOutlined color="neutral" />
                                              Video
                                          </Typography>
                                      ),
                                  },
                              ],
                          }
                        : undefined,
                }}
                indicator={{
                    text: totalItems,
                }}
                growSearch
                searchState={[search, setSearch]}
                filterState={[filters, setFilters]}
                filters={buildFilters(sectionsFilters?.data?.filters || {}, sectionsFilters?.data?.sorts || {})}
            />
            <ContentSection
                sliderKey={galleryToView?.id}
                id={imagesSection.id}
                service={imagesSection.service}
                title={imagesSection.title}
                contentCardProps={imagesSection.contentCardProps || {}}
                search={search}
                filters={filters}
                showSeeAllAtCarouselEnd
                totalItemsCb={handleTotalItemsChange}
            />
            <ContentSection
                id={blogSection.id}
                service={blogSection.service}
                title={blogSection.title}
                contentCardProps={blogSection.contentCardProps || {}}
                search={search}
                filters={filters}
                showSeeAllAtCarouselEnd
                totalItemsCb={handleTotalItemsChange}
            />
            <ContentSection
                id={documentsSection.id}
                service={documentsSection.service}
                title={documentsSection.title}
                contentCardProps={documentsSection.contentCardProps || {}}
                search={search}
                filters={filters}
                showSeeAllAtCarouselEnd
                totalItemsCb={handleTotalItemsChange}
            />
            <ContentSection
                id={videosSection.id}
                service={videosSection.service}
                title={videosSection.title}
                contentCardProps={videosSection.contentCardProps || {}}
                search={search}
                filters={filters}
                showSeeAllAtCarouselEnd
                totalItemsCb={handleTotalItemsChange}
            />
            {isEditable && info && (!!editVideo || !!editDocument) && (
                <NewFileDialog
                    onClose={() => {
                        setEditVideo(undefined);
                        setEditDocument(undefined);
                    }}
                    entityId={info?.id || ""}
                    maxSize="500"
                    hasDescription
                    type={!!editVideo ? MediaTypes.Video : MediaTypes.Document}
                    profile_type={"company"}
                    fileId={(editDocument || editVideo)?.id}
                    friendlyUrl={friendly_url}
                    vendorType={(info as typeof company.data)?.company_type || (info as IPeople)?.type}
                    useRte={!!editVideo}
                    entity={info}
                    contentData={[editDocument || editVideo]}
                />
            )}
            {isEditable && !!galleryToEdit && info?.id && (
                <AssetPostingV2
                    open
                    onClose={() => setGalleryToEdit(undefined)}
                    assetToEdit={galleryToEdit}
                    isCompany={!!isCompany}
                />
            )}
            {isEditable && !!addNew && addNew !== "blog" && !!info && (
                <AddContentModal
                    showAsModal
                    entity={info as ICompany | IPeople}
                    isCompany={props.profile_type === "company"}
                    setShow={() => {
                        params.delete("add");
                        setParams(params);
                    }}
                />
            )}
            {galleryToView && (
                <ViewGalleryDialog
                    open={!!galleryToView}
                    onClose={() => {
                        setGalleryToView(undefined);
                    }}
                    gallery={galleryToView}
                    companyId={info?.id}
                />
            )}
        </Box>
    );
};

export default ContentTab;
