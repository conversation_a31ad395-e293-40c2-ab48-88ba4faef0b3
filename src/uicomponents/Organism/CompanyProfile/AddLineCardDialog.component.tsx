import AddOutlined from "@mui/icons-material/AddOutlined";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import EastOutlinedIcon from "@mui/icons-material/EastOutlined";
import SearchOutlined from "@mui/icons-material/SearchOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useCompany} from "../../../hooks/fetches/useCompany";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {IMarketplaceItem} from "../../../Interfaces/company.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {stripHtml} from "../../../utils/formatString.util";
import {validateURL} from "../../../utils/validation.util";
import Box from "../../Atoms/Box/Box.component";
import RTE from "../../Atoms/RTE/rte.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import VendorSearch from "../../Molecules/VendorSearch/vendorSearch.component";
import MissingRequestDialog from "../MissingRequestDialog/missingRequestDialog.component";
import {DISTRIBUTOR_LINE_CARD_TEXT} from "../../../constants/commonStrings.constant";

interface IProps {
    open: boolean;
    onClose?: Function;
    mode?: "CREATE" | "EDIT";
    friendly_url?: string;
    marketplaceItemToEdit?: IMarketplaceItem;
}

const descriptionMaxLength = 2500;

const AddLineCardDialog = (props: IProps) => {
    const [showSuccess, setShowSuccess] = useState(false);
    const [showNoResults, setShowNoResults] = useState({show: false, search: ""});
    const [showSendRequest, setShowSendRequest] = useState(false);
    const theme = useTheme();
    const methods = useForm();
    const {updateCompany, isUpdating} = useCompany(props.friendly_url || "", {
        isCompany: true,
        calls: {all: false},
    });
    const {activeCompany} = useActiveCompany();
    const isSaving =
        isUpdating[MutateTypes.StoreMarketplacePartner] || isUpdating[MutateTypes.UpdateMarketplacePartner];
    const isDeleting = isUpdating[MutateTypes.DeleteMarketplacePartner + props.marketplaceItemToEdit?.id];
    const isEditMode = !!(props.mode === "EDIT" && props.marketplaceItemToEdit);
    const descriptionWatcher = methods.watch("description");
    const partnerIdWatcher = methods.watch("partner_id");

    const handleSubmit = async () => {
        if (showSuccess) {
            props.onClose?.();
            return;
        }
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const {partner_id, description, external_url} = methods.getValues();
        if (stripHtml(description)?.length > descriptionMaxLength) {
            methods.setError("description", {message: `Description must be under ${descriptionMaxLength} characters.`});
            return;
        }
        const body = isEditMode
            ? {description: description || null, id: props.marketplaceItemToEdit?.id, external_url: external_url || ""}
            : {partner_id, description: description || null, external_url: external_url || ""};
        updateCompany.mutate({
            mutate_type: isEditMode ? MutateTypes.UpdateMarketplacePartner : MutateTypes.StoreMarketplacePartner,
            mutate_is_updating_append: isEditMode ? props.marketplaceItemToEdit?.id : undefined,
            friendly_url: props.friendly_url,
            body,
            onSuccess: () => {
                if (isEditMode) {
                    props.onClose?.();
                } else {
                    methods.reset();
                    setShowSuccess(true);
                }
            },
        });
    };

    const handleDelete = async () => {
        const item = props.marketplaceItemToEdit;
        const answer = await getUserConfirmation("Remove Vendor?", {
            mui: true,
            modalTheme: "error",
            title: `Remove Vendor?`,
            form: (
                <Box display="flex" flexDirection="column" justifyContent="flex-start">
                    <Typography variant="body2" fontWeight="bold">
                        Are you sure you want to remove {item?.name || "this vendor"} from your{" "}
                        {DISTRIBUTOR_LINE_CARD_TEXT}?
                    </Typography>
                    <Typography variant="body2">
                        This cannot be undone. Please confirm if you would like to proceed.
                    </Typography>
                </Box>
            ),
            okButtonText: (
                <Box gap={1}>
                    <DeleteOutlinedIcon /> REMOVE
                </Box>
            ),
        });
        if (answer.value) {
            updateCompany.mutate({
                mutate_type: MutateTypes.DeleteMarketplacePartner,
                friendly_url: props.friendly_url,
                id: item?.partner_id,
                mutate_is_updating_append: item?.id,
                onSuccess: () => {
                    props.onClose?.();
                },
            });
        }
    };

    const handleAddAnother = () => {
        setShowSuccess(false);
    };

    const handleNoVendorResults = (isNoResults: boolean, _, searchValue: string) => {
        setShowNoResults({
            show: isNoResults,
            search: searchValue,
        });
    };

    useEffect(() => {
        if (props.open && isEditMode) {
            methods.setValue("description", props.marketplaceItemToEdit?.marketplace_description || "");
            methods.setValue("external_url", props.marketplaceItemToEdit?.marketplace_external_url || "");
        }
        return () => {
            methods.reset();
            showSuccess && setShowSuccess(false);
            showNoResults.show && setShowNoResults({show: false, search: ""});
        };
    }, [props.open]);

    return (
        <ErrorBoundary>
            <ModalComponent
                open={props.open}
                onClose={props.onClose as any}
                onSuccess={handleSubmit}
                title={"new-line-card-vendor"}
                loading={isSaving || isDeleting}
                customTitle={
                    <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                        {!isEditMode ? (
                            <>
                                <AddOutlined /> New {DISTRIBUTOR_LINE_CARD_TEXT}
                            </>
                        ) : (
                            <>
                                <AvatarComponentV2
                                    isCompany
                                    user={{
                                        avatar: props.marketplaceItemToEdit?.avatar || "",
                                        name: props.marketplaceItemToEdit?.name || "",
                                        friendly_url: props.marketplaceItemToEdit?.friendly_url || "",
                                        type: "",
                                    }}
                                    size={32}
                                    showProfileType
                                    profileTypeMinimized
                                />
                                <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                                    Editing <EastOutlinedIcon /> {props.marketplaceItemToEdit?.name || "Company"}
                                </Box>
                            </>
                        )}
                    </Box>
                }
                showOkButton
                okButtonText={
                    <>
                        <CheckOutlinedIcon sx={{marginRight: 1}} />
                        {showSuccess ? "OK" : "SAVE"}
                    </>
                }
                showCancelButton={!showSuccess}
                cancelButtonText={"Cancel"}
                modalTheme="blue"
                justifyButtons="flex-start"
                customModalBtnTracking={{
                    company_friendly_url: props.friendly_url,
                }}
                dialogSx={{
                    ".MuiDialog-paper": {
                        overflow: "unset",
                        maxWidth: "800px !important",
                        width: "100%",
                    },
                    ".MuiDialogContent-root": {overflowY: "auto"},
                }}
                content={
                    <FormProvider {...methods}>
                        {showSuccess ? (
                            <AlertBanner
                                id="success-add-line-card"
                                variant="success"
                                title={{text: `Vendor added to your ${DISTRIBUTOR_LINE_CARD_TEXT}.`}}
                                subTitle={{text: "You can close this dialog or add another one"}}
                            />
                        ) : (
                            <form style={{display: "flex", flexDirection: "column", gap: 24}}>
                                {showNoResults.show && (
                                    <AlertBanner
                                        id="send-request-banner"
                                        variant="warning"
                                        title={{text: "Ah, Snap! Don’t see your vendor?"}}
                                        subTitle={{
                                            text: (
                                                <>
                                                    We are always growing our network.{" "}
                                                    <strong
                                                        style={{color: theme.palette.blue[600], cursor: "pointer"}}
                                                        onClick={() => setShowSendRequest(true)}>
                                                        Send your request
                                                    </strong>{" "}
                                                    and we will get right to it!
                                                </>
                                            ),
                                        }}
                                    />
                                )}
                                {props.mode === "CREATE" && (
                                    <VendorSearch
                                        id="partner_id"
                                        sx={{width: "100%"}}
                                        placeholder="Start typing to search"
                                        isRequired
                                        validateOnTheFly
                                        noResultsCallback={handleNoVendorResults}
                                        clearOnSelect={false}
                                        onValueChange={(_, __, newValue) => {
                                            methods.setValue("description", newValue.company?.description);
                                        }}
                                        label="Vendor"
                                        distributors="exclude"
                                    />
                                )}
                                <TextBoxComponent
                                    id="external_url"
                                    label="Vendor URL"
                                    placeholder="Input the address to vendor's website"
                                    endAdornment={<SearchOutlined sx={{height: 24, width: 24}} color="blue" />}
                                    validateOnTheFly
                                    validationSchema={v => (v ? validateURL(v) : true)}
                                    tooltipHelperText="URL must be a valid link"
                                    helperText="Provide the URL from your website leading to a vendor details page."
                                    showHelperText
                                />
                                <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    fontInter
                                    color={theme.palette.neutral[700]}>
                                    Vendor Description
                                </Typography>
                                <RTE
                                    id="description"
                                    key={partnerIdWatcher}
                                    maxLength={descriptionMaxLength}
                                    placeholder="Type your description here"
                                    defaultValue={
                                        isEditMode
                                            ? props.marketplaceItemToEdit?.marketplace_description
                                            : descriptionWatcher || ""
                                    }
                                />
                            </form>
                        )}
                    </FormProvider>
                }
                showAdditionalBtn={isEditMode || showSuccess}
                additionalBtnText={
                    showSuccess ? (
                        <>
                            <AddOutlined />
                            ADD ANOTHER
                        </>
                    ) : (
                        <>
                            <DeleteOutlinedIcon sx={{marginRight: 1}} color="error" />
                            {isDeleting ? "REMOVING..." : "REMOVE"}
                        </>
                    )
                }
                additionalBtnProps={
                    showSuccess
                        ? {variant: "outlined"}
                        : {
                              disabled: isDeleting,
                              color: "error",
                              variant: "outlined",
                              sx: {color: theme.palette.error["main"]},
                          }
                }
                onAdditionalBtnClick={showSuccess ? handleAddAnother : handleDelete}
                shouldCloseOnClickOutside={false}
            />
            <MissingRequestDialog
                open={showSendRequest}
                companyId={activeCompany?.id || ""}
                onClose={() => setShowSendRequest(false)}
                onSave={() => setShowSendRequest(false)}
                onlyVendors
                defaultInputValue={showNoResults.search}
            />
        </ErrorBoundary>
    );
};

export default AddLineCardDialog;
