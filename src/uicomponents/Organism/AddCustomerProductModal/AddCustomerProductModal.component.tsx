import {AddCircleOutline, EditOutlined} from "@mui/icons-material";
import {Box} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {FormProvider, useForm} from "react-hook-form";
import {ErrorIcon} from "../../../components/Icons";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import CategoryAutocomplete from "../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import CustomerProductSearch from "../../Molecules/CustomerProductSearch/customerProductSearch.component";
import CustomerVendorSearch from "../../Molecules/CustomerVendorSearch/customerVendorSearch.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import CompanyType from "../../../constants/companyType.constant";
import {useEffect} from "react";
import {stackService} from "../../../services/stack.service";
import useNotification from "../../../hooks/useNotification";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";
import { IStackCategorization } from "../../../Interfaces/myStack.interface";
import isMobileView from "../../../utils/isMobileView.util";

interface IAddToStackForm {
    product: {id: string; label: string; isCreatableOption?: boolean} | null;
    vendor: {id: string; label: string; isCreatableOption?: boolean} | null;
    vendor_id: string;
    product_id: string;
    vendor_name: string;
    product_name: string;
    category: string;
    subcategory: string;
    description: string;
    create: boolean;
}

interface IAddCustomerProductModalProps {
    open: boolean;
    onClose: () => void;
    saving: boolean;
    setSaving: (saving: boolean) => void;
    companyId: string;
    defaultCategory?: string;
    defaultProduct?: any;
    stack?: IStackCategorization;
    errorMessage?: string;
    errorDescription?: string;
    onError: (err: any) => void;
}

export default function AddCustomerProductModal({
    open,
    onClose,
    saving,
    setSaving,
    companyId,
    defaultCategory,
    defaultProduct,
    stack,
    errorMessage,
    errorDescription,
    onError,
}: IAddCustomerProductModalProps) {
    const theme = useTheme();
    const notify = useNotification();
    const {invalidateMatchedQueries} = useQueryHelper();
    const isMobile = isMobileView();

    const methods = useForm<IAddToStackForm>({
        defaultValues: {
            category: "",
            subcategory: "",
            product: null,
            description: "",
            vendor: null,
            vendor_id: "",
            product_id: "",
            create: !stack,
        },
    });

    useEffect(() => {
        if (open) {
            methods.reset({
                category: stack?.category.parent_id || "",
                subcategory: stack?.category_id || "",
                product: stack ? {
                    id: stack.product_id,
                    label: stack.product.name,
                } : null,
                description: stack?.product.description || "",
                vendor: stack ? {
                    id: stack.stack_company_id,
                    label: stack.stack_company.name,
                } : null,
                vendor_id: stack?.stack_company.name || "",
                product_id: stack?.product.name || "",
                vendor_name: stack?.stack_company.name || "",
                product_name: stack?.product.name || "",
                create: !stack,
            });
        }
    }, [open, stack]);

    const handleSubmit = async () => {
        const values = methods.getValues();
        if (!values.subcategory) {
            methods.setError("subcategory", {
                type: "manual",
                message: "Subcategory is required"
            });
            return;
        }

        setSaving(true);

        if (values.create) {
            const payload = {
                [values.subcategory]: [{
                    stack_company_name: values.vendor?.isCreatableOption ? values.vendor.label : undefined,
                    stack_company_id: values.vendor?.isCreatableOption ? undefined : values.vendor?.id,
                    product_name: values.product?.isCreatableOption ? values.product.label : undefined,
                    product_id: values.product?.isCreatableOption ? undefined : values.product?.id,
                    product_description: values.description || undefined,
                    partner_status: "I am a current partner"
                }]
            };

            stackService
                .addToCustomerStack(companyId, payload)
                .then(() => {
                    notify("Product added successfully", "Success");
                    invalidateMatchedQueries(["naviStack", companyId]);
                    onClose();
                })
                .catch(onError)
                .finally(() => setSaving(false));
        } else {
            if (!stack?.id) return;

            const payload = {
                id: stack.id,
                category_id: values.subcategory,
                product_description: values.description || undefined,
                product_id: values.product?.id,
                product_name: values.product_name,
                stack_company_id: values.vendor?.id,
                stack_company_name: values.vendor_name,
                partner_status: "I am a current partner" as const,
            };

            stackService
                .updateCustomerStack(companyId, payload)
                .then(() => {
                    notify("Product updated successfully", "Success");
                    invalidateMatchedQueries(["naviStack", companyId]);
                    onClose();
                })
                .catch(onError)
                .finally(() => setSaving(false));
        }
    };

    return (
        <ModalComponent
            id="add-to-stack"
            justifyButtons="flex-start"
            open={open}
            onClose={onClose}
            title={methods.watch("create") ? "Add Product" : "Edit Product"}
            icon={methods.watch("create") ? <AddCircleOutline /> : <EditOutlined />}
            contentClassName="mt-0"
            modalTheme="blue"
            maxWidth="md"
            dialogSx={{
                ".MuiDialog-paper": {
                    overflow: "auto",
                    maxWidth: "800px !important",
                    maxHeight: "100%",
                    width: "100%",
                },
                ".MuiDialogContent-root": {
                    overflow: "auto",
                },
            }}
            okButtonText={methods.watch("create") ? "ADD PRODUCT" : "SAVE PRODUCT"}
            cancelButtonText="CANCEL"
            loading={saving}
            onSuccess={handleSubmit}
            content={
                <>
                    <FormProvider {...methods}>
                        {!!errorMessage && (
                            <Box
                                sx={{
                                    backgroundColor: theme.palette.error[100],
                                    borderColor: theme.palette.error[300],
                                    borderStyle: "solid",
                                    borderWidth: "1px",
                                    borderRadius: "8px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between",
                                    padding: "16px",
                                    gap: 2,
                                    width: "100%",
                                }}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        gap: 1,
                                        flexDirection: "column",
                                        "& svg": {
                                            fill: theme.palette.error[500],
                                        },
                                    }}>
                                    <ErrorIcon version="circle" size="24" />
                                </Box>
                                <Box sx={{display: "flex", flex: 1, gap: 1, flexDirection: "column"}}>
                                    <Typography
                                        fontInter
                                        variant="body3"
                                        color={theme.palette.neutral[800]}
                                        sx={{fontWeight: 700}}>
                                        {errorMessage}
                                    </Typography>
                                    {!!errorDescription && (
                                        <Typography
                                            fontInter
                                            variant="body3"
                                            color={theme.palette.neutral[800]}
                                            sx={{fontWeight: 400}}>
                                            {errorDescription}
                                        </Typography>
                                    )}
                                </Box>
                                <IconButton
                                    onClick={() => {
                                        onClose();
                                    }}
                                    sx={{
                                        "& svg": {
                                            width: "24px",
                                            height: "24px",
                                            fill: theme.palette.neutral[600],
                                        },
                                    }}>
                                    <CloseOutlinedIcon />
                                </IconButton>
                            </Box>
                        )}
                        <Box
                            sx={{
                                display: "flex",
                                gap: 3,
                                flexWrap: "wrap",
                                ...(isMobile && {
                                    flexDirection: "column",
                                }),
                                "&>*": {
                                    flex: "1 1 calc(50% - 12px)",
                                },
                            }}>
                            {methods.watch("create") ? (
                                <>
                                    <CustomerVendorSearch
                                        id="vendor_id"
                                        onValueChange={(_: any, __: any, value) => methods.setValue("vendor", value)}
                                        validateOnTheFly
                                        placeholder="Enter Vendor Name"
                                        companyId={companyId}
                                        isRequired
                                        defaultSearch={stack?.stack_company.name}
                                        keepRegistered
                                    />
                                    <CustomerProductSearch
                                        id="product_id"
                                        onValueChange={(_: any, __: any, value) => methods.setValue("product", value)}
                                        validateOnTheFly
                                        placeholder="Enter Product Name"
                                        categoryId={defaultCategory}
                                        defaultProductId={defaultProduct}
                                        companyId={companyId}
                                        keepRegistered
                                        isRequired
                                    />
                                </>
                            ) : (
                                <>
                                    <TextBoxComponent
                                        id="vendor_name"
                                        label="Vendor"
                                        isRequired
                                        placeholder="Enter Vendor Name"
                                    />
                                    <TextBoxComponent
                                        id="product_name"
                                        label="Product"
                                        isRequired
                                        placeholder="Enter Product Name"
                                    />
                                </>
                            )}
                            <CategoryAutocomplete
                                categoryInputId="category"
                                subCategoryInputId="subcategory"
                                company_type={CompanyType.MSP_CLIENT}
                                isCustomer
                                isRequired
                                wrapperSx={{
                                    flex: "1 1 100%",
                                    gap: 3,
                                    display: "flex",
                                    "&>*": {
                                        flex: "1 1 calc(50% - 12px)",
                                        minWidth: "calc(50% - 12px)!important",
                                    },
                                    ...(isMobile && {
                                        flexDirection: "column",
                                    }),
                                }}
                            />
                            <TextBoxComponent
                                id="description"
                                multiline
                                rows={3}
                                label="Description"
                                placeholder="Enter product description"
                            />
                        </Box>
                    </FormProvider>
                </>
            }
        />
    );
} 