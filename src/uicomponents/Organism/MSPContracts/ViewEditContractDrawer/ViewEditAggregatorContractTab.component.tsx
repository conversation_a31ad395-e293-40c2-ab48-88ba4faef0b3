import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import InfoOutlined from "@mui/icons-material/InfoOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import NotificationsOutlinedIcon from "@mui/icons-material/NotificationsOutlined";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import {useTheme} from "@mui/material/styles";
import {useCallback, useMemo, useState} from "react";
import {TableV2Component} from "../../../../components/Table/TableV2.component";
import {PERMANENT_ACTION_CONFIRMATION} from "../../../../constants/commonStrings.constant";
import {CPSortType} from "../../../../enums/sortType.enum";
import {useMspContracts} from "../../../../hooks/fetches/useMspContracts";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IContract, IProductContract} from "../../../../Interfaces/mspContracts.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {sortBySortAndOrderBy} from "../../../../utils/array.util";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {numberToCurrency} from "../../../../utils/formatNumber.util";
import formatString, {CAPITALIZE_FIRST} from "../../../../utils/formatString.util";
import Loader from "../../../../utils/loader";
import Box from "../../../Atoms/Box/Box.component";
import Button from "../../../Atoms/Button/Button.Component";
import Typography from "../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import MenuButton from "../../../Molecules/MenuButton/menuButton.component";
import ContractDocumentsDialog from "../AddContract/ContractDocumentsDialog/ContractDocumentsDialog.component";
import ContractNotificationDialog from "../AddContract/ContractNotificationDialog/ContractNotificationDialog.component";
import ContractProductDetailsDialog from "../AddContract/ContractProductDetailsDialog.component";

const ViewEditAggregatorContractTab = ({
    contract,
    mode,
    onClickProductContract,
    refreshContract,
    isMspClient,
    companyId,
    closeDrawer,
}: {
    contract?: IContract;
    mode: "READ" | "EDIT";
    onClickProductContract?: (product_contract_id: string, mode: "EDIT" | "READ") => void;
    refreshContract?: () => void;
    isMspClient?: boolean;
    companyId?: string;
    closeDrawer?: () => void;
}) => {
    const [isAddNewOpen, setIsAddNewOpen] = useState<boolean>(false);
    const [tableSortOrder, setTableSortOrder] = useState<any>({
        sort: CPSortType.ASC,
        order_by: "product.name",
    });
    const [showNotificationDialogFor, setShowNotificationDialogFor] = useState<string>("");
    const [showAttachmentsDialogFor, setShowAttachmentsDialogFor] = useState<string>("");
    const {activeCompany, isClientMsp: isActiveCompanyClientMsp} = useActiveCompany();
    const isClientMsp = isMspClient !== undefined ? isMspClient : isActiveCompanyClientMsp;
    const theme = useTheme();
    const product_contracts = sortBySortAndOrderBy(contract?.product_contracts || [], {
        sort: tableSortOrder.sort,
        orderBy: tableSortOrder.order_by,
    });
    const isProductContractsEmpty = product_contracts.length === 0;
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]);
    const {updateMspContracts, isUpdating} = useMspContracts(activeCompany?.id, {
        calls: {all: false},
    });
    const tableTotals = useMemo(() => {
        const totals = {
            cost: 0,
            total_monthly_cost: 0,
        };
        product_contracts?.forEach(c => {
            totals.cost += Number(c.total_contract_cost) || 0;
            totals.total_monthly_cost += c.total_monthly_cost || 0;
        });
        return totals;
    }, [product_contracts]);

    const ThreeDotMenuCell = useCallback(
        ({row}) => {
            return (
                <MenuButton
                    id={`more-actions-${row.original.id}`}
                    buttonVariant="iconButton"
                    buttonProps={{
                        sx: {
                            width: "24px",
                            minWidth: "24px!important",
                            height: "24px",
                            boxShadow: "none",
                            background: "none",
                        },
                    }}
                    items={[
                        {
                            id: "view",
                            children: (
                                <>
                                    <VisibilityOutlinedIcon htmlColor={theme.palette.neutral[500]} /> View
                                </>
                            ),
                            onClick: () => {
                                onClickProductContract && onClickProductContract(row.original.id, "READ");
                            },
                        },
                        {
                            id: "edit",
                            children: (
                                <>
                                    <EditOutlinedIcon htmlColor={theme.palette.neutral[500]} /> Edit Information
                                </>
                            ),
                            onClick: () => {
                                onClickProductContract && onClickProductContract(row.original.id, "EDIT");
                            },
                        },
                        {
                            id: "manage-notifications",
                            children: (
                                <>
                                    <NotificationsOutlinedIcon htmlColor={theme.palette.neutral[500]} /> Manage
                                    Notifications
                                </>
                            ),
                            onClick: () => setShowNotificationDialogFor(row.original.id),
                        },
                        {
                            id: "manage-documents",
                            children: (
                                <>
                                    <AttachFileOutlinedIcon htmlColor={theme.palette.neutral[500]} /> Manage Documents
                                </>
                            ),
                            onClick: () => setShowAttachmentsDialogFor(row.original.id),
                        },
                        {
                            id: "delete",
                            children: (
                                <>
                                    <DeleteOutlineOutlinedIcon color="error" />
                                    <Typography variant="body3" color="error">
                                        Delete Product
                                    </Typography>
                                </>
                            ),
                            onClick: () => handleDeleteProductContract(row.original),
                        },
                    ].filter(item => (hasEditAccess ? true : item.id === "view"))}
                    aria-label="settings">
                    {isUpdating[MutateTypes.DeleteMSPContract + row.original.id] ? (
                        <Loader inline loading />
                    ) : (
                        <MoreVert color="neutral" />
                    )}
                </MenuButton>
            );
        },
        [isUpdating],
    );

    const columns = [
        {
            Header: "Product",
            accessor: "product.name",
            sortable: true,
            Cell: ({row}) => (
                <Box
                    display="flex"
                    flexDirection="row"
                    alignItems="center"
                    gap="10px"
                    sx={{cursor: "pointer"}}
                    onClick={() => {
                        onClickProductContract && onClickProductContract(row.original.id, "READ");
                    }}>
                    {!isClientMsp && (
                        <AvatarComponentV2
                            size={32}
                            showProfileType
                            profileTypeMinimized
                            isCompany
                            user={row.original?.company}
                            isRedirectEnabled={false}
                        />
                    )}
                    <Typography variant="body4" fontWeight="600 !important" color={theme.palette.blue[600]}>
                        {isClientMsp
                            ? `${row.original.product?.name || row.original?.name}`
                            : `${row.original.product?.name} - ${row.original?.company?.name}`}
                    </Typography>
                </Box>
            ),
        },
        {
            Header: "",
            accessor: "attachments_count",
            Cell: ({row}) =>
                !!row.original.attachments_count ? (
                    <AttachFileOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.neutral[500]} />
                ) : (
                    "--"
                ),
        },
        {
            Header: "Category",
            accessor: "category.name",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4" fontInter>
                    {row.original?.category?.name || "--"}
                </Typography>
            ),
        },
        {
            Header: "Total Cost",
            accessor: "total_contract_cost",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4">
                    {row.original?.total_contract_cost ? numberToCurrency(row.original.total_contract_cost) : "--"}
                </Typography>
            ),
        },
        {
            Header: "Billed",
            accessor: "billing_frequency",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4">
                    {formatString(
                        row.original?.billing_frequency === "onetimepayment"
                            ? "One Time Payment"
                            : row.original?.billing_frequency || "",
                        CAPITALIZE_FIRST,
                    ) || "--"}
                </Typography>
            ),
        },
        {
            Header: "Total Per Payment",
            accessor: "total_per_payment",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4">
                    {row.original?.total_per_payment ? numberToCurrency(row.original.total_per_payment) : "--"}
                </Typography>
            ),
        },
        {
            Header: "Total Monthly Cost",
            accessor: "total_monthly_cost",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4">
                    {row.original?.total_monthly_cost ? numberToCurrency(row.original.total_monthly_cost) : "--"}
                </Typography>
            ),
        },
        {
            Header: "",
            id: "edit",
            Cell: ThreeDotMenuCell,
        },
    ];

    const handleDeleteProductContract = async (product_contract: IProductContract) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Selected Item?",
            justifyButtons: "flex-start",
            okButtonText: "Delete",
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" flexDirection="row" gap={2} marginBottom={1}>
                        {!isClientMsp && (
                            <AvatarComponentV2
                                size={48}
                                user={(product_contract?.company || {}) as any}
                                isCompany
                                showProfileType
                                profileTypeMinimized
                                disableLink
                                isRedirectEnabled={false}
                            />
                        )}
                        <Box display="flex" flexDirection="column" justifyContent="space-around">
                            <Typography fontInter variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                                {product_contract?.client_product?.name || product_contract?.product?.name}
                            </Typography>
                            <Typography fontInter variant="subtitle3" color={theme.palette.blue[600]}>
                                {product_contract?.client_vendor?.name || product_contract?.company?.name}
                            </Typography>
                        </Box>
                    </Box>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (answer.value) {
            updateMspContracts.mutate({
                mutate_type: MutateTypes.DeleteMSPContract,
                company_id: companyId,
                contract_id: product_contract?.id,
                onSuccess: () => {
                    if (product_contracts?.length <= 1) {
                        closeDrawer?.();
                        return;
                    }
                    refreshContract?.();
                },
            });
        }
    };

    const handleAddNewClick = () => {
        setIsAddNewOpen(true);
    };

    const onSaveProductDetails = () => {
        refreshContract?.();
        setIsAddNewOpen(false);
    };

    const tableColStyle = width => ({
        width,
        maxWidth: width,
        minWidth: width,
    });

    const ProductContractsTable = useMemo(() => {
        return (
            <Box
                display="flex"
                flexDirection="column"
                sx={{
                    table: {
                        minWidth: 720,
                    },
                    "table th:first-child, table tr td:first-child": tableColStyle(200),
                    "table th:nth-child(2), table tr td:nth-child(2)": tableColStyle(40),
                    "table th:nth-child(3), table tr td:nth-child(3)": tableColStyle(180),
                    "table th:nth-child(4), table tr td:nth-child(4)": tableColStyle(100),
                    "table th:nth-child(5), table tr td:nth-child(5)": tableColStyle(100),
                    "table th:nth-child(6), table tr td:nth-child(6)": tableColStyle(120),
                    "table th:last-child, table tr td:last-child": tableColStyle(40),
                    td: {
                        padding: 1,
                    },
                    width: "100%",
                    maxWidth: "100%",
                    overflowX: "auto",
                }}>
                <TableV2Component
                    id="product_contract_table"
                    columns={columns as []}
                    customData={product_contracts || []}
                    headerBackground={theme.palette.blue[100]}
                    containerStyle={{overflow: "visible"}}
                    onSortChange={(sort, order_by) => {
                        setTableSortOrder({sort, order_by});
                    }}
                    sortType={tableSortOrder.sort}
                    orderBy={tableSortOrder.order_by}
                />
                <table>
                    <tbody>
                        <tr>
                            <td>
                                <Typography fontInter variant="body3" fontWeight={600}>
                                    Totals
                                </Typography>
                            </td>
                            <td />
                            <td />
                            <td>
                                <Typography fontInter variant="body3" fontWeight={600}>
                                    {numberToCurrency(tableTotals.cost)}
                                </Typography>
                            </td>
                            <td />
                            <td />
                            <td>
                                <Typography fontInter variant="body3" fontWeight={600}>
                                    {numberToCurrency(tableTotals.total_monthly_cost)}
                                </Typography>
                            </td>
                            <td />
                        </tr>
                    </tbody>
                </table>
            </Box>
        );
    }, [columns, product_contracts, isProductContractsEmpty, tableSortOrder, setTableSortOrder, isClientMsp]);

    return (
        <ErrorBoundary>
            <Box sx={{display: "flex", flexDirection: "column", gap: 2, maxWidth: "100%", overflow: "hidden"}}>
                <TextBoxComponent
                    id="name"
                    label="Contract Name"
                    readOnly={mode === "READ"}
                    isRequired
                    keepRegistered
                    validateOnTheFly
                    tooltipHelperText="Enter the contract name"
                />
                {isProductContractsEmpty ? (
                    <TextBoxComponent
                        id="cost"
                        label="Overall Cost"
                        type="number"
                        readOnly={mode === "READ"}
                        allowDecimals
                        keepRegistered
                        validateOnTheFly
                        isRequired={!product_contracts.length}
                        tooltipHelperText="Enter the contract cost"
                    />
                ) : (
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        justifyContent="space-between"
                        width="100%">
                        <Typography color={theme.palette.neutral[700]} fontInter variant="body3" fontWeight={600}>
                            Contract Products
                        </Typography>
                        {hasEditAccess && (
                            <Button
                                id="add_new_product_contract"
                                variant="outlined"
                                color="blue"
                                onClick={handleAddNewClick}>
                                ADD NEW
                            </Button>
                        )}
                    </Box>
                )}
                {isProductContractsEmpty ? (
                    <AlertBanner
                        id="no_product_contracts"
                        variant="info"
                        title={{
                            text: (
                                <Typography variant="body3" fontInter fontSize="16px !important">
                                    <InfoOutlined htmlColor={theme.palette.blue[600]} />
                                    No product contracts found.{" "}
                                    <strong
                                        onClick={handleAddNewClick}
                                        style={{color: theme.palette.blue[600], cursor: "pointer"}}>
                                        Add Now
                                    </strong>
                                </Typography>
                            ),
                        }}
                        styles={{
                            height: 175,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                        contentContainerSx={{display: "flex", alignItems: "center", justifyContent: "center"}}
                        icon={null}
                    />
                ) : (
                    ProductContractsTable
                )}
                {!!showNotificationDialogFor && (
                    <ContractNotificationDialog
                        open={!!showNotificationDialogFor}
                        onClose={() => setShowNotificationDialogFor("")}
                        contract={product_contracts?.find(item => item.id === showNotificationDialogFor)}
                        onSave={() => refreshContract?.()}
                    />
                )}
                {!!showAttachmentsDialogFor && (
                    <ContractDocumentsDialog
                        open={!!showAttachmentsDialogFor}
                        onClose={() => setShowAttachmentsDialogFor("")}
                        contract={product_contracts?.find(item => item.id === showAttachmentsDialogFor)}
                        onSave={() => refreshContract?.()}
                    />
                )}
                {isAddNewOpen && (
                    <ContractProductDetailsDialog
                        open={isAddNewOpen}
                        onClose={() => setIsAddNewOpen(false)}
                        onSave={onSaveProductDetails}
                        companyId={companyId || activeCompany?.id}
                        parent_id={contract?.parent_id || contract?.id}
                        isClientMsp={isClientMsp}
                    />
                )}
            </Box>
        </ErrorBoundary>
    );
};

export default ViewEditAggregatorContractTab;
