import ImportExportOutlinedIcon from "@mui/icons-material/ImportExportOutlined";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import useTheme from "@mui/material/styles/useTheme";
import {MSP_CONTRACT_TEXT} from "../../../../constants/commonStrings.constant";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IStackCategorization} from "../../../../Interfaces/myStack.interface";
import Box from "../../../Atoms/Box/Box.component";
import Button from "../../../Atoms/Button/Button.Component";
import Typography from "../../../Atoms/Typography/Typography.component";
import CardComponent from "../../../Cards/Card.component";
import MspSuggestedSubscriptions from "../MspSuggestions/MspSuggestedSubscriptions.component";
import {ADD_CONTRACT_FROM_STACK_TEXT} from "../../../../constants/siteFlags";

interface IProps {
    companyId: string;
    onClick?: (naviStackProduct: IStackCategorization) => void;
    onOpenImportCSVDialogClick?: () => void;
    containerSx?: any;
    noDataCallback?: (hasNoData: boolean) => void;
    isPremiumMsp?: boolean;
}

const AddStackSubscriptionsWidget = (props: IProps) => {
    const theme = useTheme();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]);

    return (
        <>
            <CardComponent
                sx={{width: "100%", ...(props.containerSx || {})}}
                contentStyles={{
                    paddingTop: 1,
                    height: "100%",
                }}
                elevation={0}
                subtitle={
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                        }}>
                        <Typography variant="body2" fontWeight={500} fontInter color={theme.palette.neutral[700]}>
                            <WarningAmberIcon sx={{marginRight: 2}} htmlColor={theme.palette.neutral[500]} />
                            {ADD_CONTRACT_FROM_STACK_TEXT}
                        </Typography>
                        {hasEditAccess && (
                            <Button
                                id="import-csv-btn"
                                onClick={() => props.onOpenImportCSVDialogClick?.()}
                                color="blue"
                                variant="outlined">
                                <ImportExportOutlinedIcon />
                                Import from CSV
                            </Button>
                        )}
                    </Box>
                }>
                <Box
                    sx={{
                        maxHeight: 180,
                        overflowY: "auto",
                        position: "relative",
                        height: "100%",
                        width: "100%",
                    }}>
                    <MspSuggestedSubscriptions
                        companyId={props.companyId}
                        fullWidth
                        onClick={props.onClick}
                        noDataCallback={props.noDataCallback}
                        containerSx={{
                            height: "100%",
                            display: "flex",
                            flexDirection: "column",
                            flexWrap: "nowrap",
                            overflowY: !props.isPremiumMsp ? "hidden" : "auto",
                        }}
                        isPremiumMsp={props.isPremiumMsp}
                    />
                </Box>
            </CardComponent>
        </>
    );
};

export default AddStackSubscriptionsWidget;
