import useTheme from "@mui/material/styles/useTheme";
import {useFormContext} from "react-hook-form";
import {CONTRACT_NOTIFICATION_TYPE_KEYS} from "../../../../constants/mspContracts.constant";
import Typography from "../../../Atoms/Typography/Typography.component";
import CardComponent from "../../../Cards/Card.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import RegisteredSwitch from "../../../Molecules/RegisteredSwitch/registeredSwitch.component";

const ContractNotificationFormCard = ({readOnly, id, name, notificationKey}) => {
    const theme = useTheme();
    const {watch} = useFormContext();
    const notice_date = watch("notice_date");
    const notice_period = watch("notice_period");
    const expiration_date = watch("end_date");
    const prepend = `notification__${id}__`;
    const noticeFieldsFilled = notice_date && notice_period;
    const expirationDateFilled = !!expiration_date;
    const enabled = watch(prepend + "enabled");
    const isRenewalNotification = notificationKey === CONTRACT_NOTIFICATION_TYPE_KEYS.RENEWAL;

    return (
        <CardComponent
            elevation={0}
            subtitle={
                <Typography
                    fontInter
                    variant="subtitle2"
                    color={theme.palette.neutral[800]}
                    fontWeight="700 !important">
                    {name}
                </Typography>
            }
            sx={{gridColumn: {xs: "span 12", lg: "span 6"}, ".MuiCardHeader-root": {paddingBottom: 1}}}
            headerActions={
                <RegisteredSwitch
                    id={prepend + "enabled"}
                    disabled={isRenewalNotification ? !expirationDateFilled : !noticeFieldsFilled}
                    readOnly={readOnly}
                    sx={{pointerEvents: readOnly ? "none" : "all"}}
                />
            }>
            <TextBoxComponent
                id={`${prepend}days_before`}
                type="number"
                label="Amount of days"
                placeholder="X days before"
                containerSx={{width: "100%", marginBottom: 3}}
                readOnly={readOnly}
                disabled={isRenewalNotification ? !expirationDateFilled : !noticeFieldsFilled}
                isRequired={enabled}
                validateOnTheFly
                min={1}
            />
            {/* <UserListInput
                id={`${prepend}recipients`}
                label="Notification Receipients"
                placeholder="Optional"
                validateOption={v => validateEmail(v)}
                helperText="Enter the email address(es) or paste a comma separated list of email addresses."
                sx={{gridColumn: "1 / span 12"}}
                readOnly={readOnly}
                disabled={!noticeFieldsFilled || sameEmailChecked}
                isRequired={enabled && !same_email}
                validateOnTheFly
            /> */}
        </CardComponent>
    );
};

export default ContractNotificationFormCard;
