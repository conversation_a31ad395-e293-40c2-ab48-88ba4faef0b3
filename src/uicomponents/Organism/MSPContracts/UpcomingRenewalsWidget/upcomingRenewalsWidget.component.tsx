import AddTaskOutlinedIcon from "@mui/icons-material/AddTaskOutlined";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import CalendarTodayOutlinedIcon from "@mui/icons-material/CalendarTodayOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useState} from "react";
import {Link} from "react-router-dom";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IContract} from "../../../../Interfaces/mspContracts.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {PERMANENT_ACTION_CONFIRMATION} from "../../../../constants/commonStrings.constant";
import {useMspContracts} from "../../../../hooks/fetches/useMspContracts";
import usePermissions from "../../../../hooks/usePermissions";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {DATE_DAY_MONTH_ABBR, formatDate, getDateXDaysFromNow, getFriendlyDate} from "../../../../utils/formatDate";
import Loader from "../../../../utils/loader";
import {getUpcomingDateColor} from "../../../../utils/mspContracts.util";
import Badge from "../../../Atoms/Badge/badge.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import CardComponent from "../../../Cards/Card.component";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import CardOverlay from "../../../Molecules/CardOverlay/CardOverlay.component";
import MenuButton from "../../../Molecules/MenuButton/menuButton.component";
import ViewEditContractDrawer from "../ViewEditContractDrawer/ViewEditContractDrawer.component";
import usePricingLink from "../../../../hooks/usePricingLink";

interface IProps {
    companyId: string;
    containerSx?: any;
    fullWidth?: boolean;
    handleClickEdit?: Function;
    isPremiumMsp?: boolean;
    isClientMsp?: boolean;
}

const UpcomingRenewalsWidget = (props: IProps) => {
    const [label, setLabel] = useState("Next 180 Days");
    const [contractToEdit, setContractToEdit] = useState<{id: string; mode: "READ" | "EDIT"}>({id: "", mode: "READ"});
    const startDate = new Date().toISOString();
    const {upcomingRenewals, filterStateUpcomingRenewals, isUpdating, updateMspContracts} = useMspContracts(
        props.isPremiumMsp ? props.companyId : "",
        {
            calls: {
                all: false,
                upcomingRenewals: true,
            },
        },
    );
    const [_, setFilterState] = filterStateUpcomingRenewals;
    const theme = useTheme();
    const {hasPermissions} = usePermissions();
    const isEditable = hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]);
    const link = usePricingLink();
    const upcoming_renewals = upcomingRenewals.data;
    const isLoading = !props.isPremiumMsp ? false : upcomingRenewals.isLoading;

    const handleFilterChange = (option: any) => {
        setFilterState({
            startDate,
            endDate: option.id,
        });
        setLabel(option.children);
    };

    const handleClickDelete = async (contract: IContract) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Selected Item?",
            justifyButtons: "flex-start",
            okButtonText: "Delete",
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" flexDirection="row" gap={2} marginBottom={1}>
                        {!props.isClientMsp && (
                            <AvatarComponentV2
                                size={48}
                                user={(contract?.company || {}) as any}
                                isCompany
                                showProfileType
                                profileTypeMinimized
                                disableLink
                                isRedirectEnabled={false}
                            />
                        )}
                        <Box display="flex" flexDirection="column" justifyContent="space-around">
                            <Typography fontInter variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                                {contract?.client_product?.name || contract?.product?.name}
                            </Typography>
                            <Typography fontInter variant="subtitle3" color={theme.palette.blue[600]}>
                                {contract?.client_vendor?.name || contract?.company?.name}
                            </Typography>
                        </Box>
                    </Box>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (answer.value) {
            updateMspContracts.mutate({
                mutate_type: MutateTypes.DeleteMSPContract,
                company_id: props.companyId,
                contract_id: contract.id,
            });
        }
    };

    const handleClickEdit = (contract: IContract) => {
        if (contract.parent_id) {
            setContractToEdit({id: contract.parent_id, mode: "EDIT"});
            return;
        }
        setContractToEdit({id: contract.id, mode: "EDIT"});
    };

    const handleClickView = (contract: IContract) => {
        if (contract.parent_id) {
            setContractToEdit({id: contract.parent_id, mode: "READ"});
            return;
        }
        setContractToEdit({id: contract.id, mode: "READ"});
    };

    return (
        <ErrorBoundary>
            <CardComponent
                sx={{width: "100%", ...(props.containerSx || {})}}
                contentStyles={{
                    paddingTop: 1,
                    maxHeight: 250,
                    overflowY: "auto",
                    height: "100%",
                }}
                elevation={0}
                subtitle={
                    <Box display="flex" flexDirection="row" alignItems="center" justifyContent="space-between">
                        <Typography variant="body2" fontWeight={500} fontInter color={theme.palette.neutral[700]}>
                            <CalendarTodayOutlinedIcon sx={{marginRight: 2}} htmlColor={theme.palette.neutral[500]} />{" "}
                            Upcoming Renewals
                        </Typography>
                        {!!props.isPremiumMsp && (
                            <MenuButton
                                id="date-select"
                                buttonProps={{
                                    color: "secondary",
                                    variant: "text",
                                    sx: {
                                        color: theme.palette.blue[600],
                                        height: 28,
                                        borderRadius: 0,
                                        border: "0px !important",
                                        backgroundColor: "none",
                                    },
                                }}
                                onChange={handleFilterChange}
                                items={[
                                    {id: getDateXDaysFromNow(30), children: <>Next 30 Days</>},
                                    {id: getDateXDaysFromNow(60), children: <>Next 60 Days</>},
                                    {id: getDateXDaysFromNow(90), children: <>Next 90 Days</>},
                                    {id: getDateXDaysFromNow(120), children: <>Next 120 Days</>},
                                    {id: getDateXDaysFromNow(180), children: <>Next 180 Days</>},
                                    // {id: getDateXDaysFromNow(365), children: <>Next Year</>},
                                ]}>
                                {label}
                                <ArrowDropDownIcon />
                            </MenuButton>
                        )}
                    </Box>
                }>
                <Grid
                    container
                    display="flex"
                    flexDirection="column"
                    gap={1}
                    sx={{
                        maxHeight: 180,
                        overflowY: "auto",
                        height: "100%",
                        flexWrap: "nowrap",
                    }}>
                    {isLoading ? (
                        Array(4)
                            .fill(null)
                            .map((_, i) => (
                                <Skeleton
                                    key={i}
                                    sx={{
                                        borderRadius: 1,
                                        padding: 1,
                                        display: "flex",
                                        alignItems: "center",
                                        border: `1px solid ${theme.palette.neutral[300]}`,
                                        gap: 2,
                                        width: props.fullWidth
                                            ? "calc(100% - 16px)"
                                            : {xs: "calc(100% - 16px)", lg: "calc(50% - 16px)"},
                                        justifyContent: "space-between",
                                        height: 40,
                                    }}
                                />
                            ))
                    ) : upcoming_renewals?.length ? (
                        upcoming_renewals?.map(contract => {
                            const nextPaymentDate = contract?.end_date;
                            const badgeColor = getUpcomingDateColor(nextPaymentDate);
                            return (
                                <Paper
                                    key={contract?.id}
                                    sx={{
                                        borderRadius: 1,
                                        padding: 1,
                                        display: "flex",
                                        alignItems: "center",
                                        border: `1px solid ${theme.palette.neutral[300]}`,
                                        gap: 2,
                                        width: props.fullWidth
                                            ? "calc(100% - 16px)"
                                            : {xs: "calc(100% - 16px)", lg: "calc(50% - 16px)"},
                                        justifyContent: "space-between",
                                        height: "fit-content",
                                    }}
                                    elevation={0}>
                                    {props.isClientMsp ? (
                                        <Box display="flex" flexDirection="column" marginLeft={"8px !important"}>
                                            <Box display="flex" flexDirection="row">
                                                <Typography
                                                    fontInter
                                                    fontWeight={600}
                                                    variant="body4"
                                                    marginRight={"8px !important"}
                                                    color={theme.palette.neutral[600]}>
                                                    {formatDate(nextPaymentDate, DATE_DAY_MONTH_ABBR)}
                                                </Typography>
                                                <Badge color={badgeColor}>
                                                    {getFriendlyDate(nextPaymentDate || "", "default")}
                                                </Badge>
                                            </Box>
                                            <Typography fontInter variant="body4" color={theme.palette.neutral[700]}>
                                                {contract.client_vendor?.name} - {contract.client_product?.name}
                                            </Typography>
                                        </Box>
                                    ) : (
                                        <Box display="flex" flexDirection="row" alignItems="center">
                                            <AvatarComponentV2
                                                isCompany
                                                user={contract?.company as any}
                                                size={32}
                                                showProfileType
                                                profileTypeMinimized
                                                target="_blank"
                                            />
                                            <Box display="flex" flexDirection="column" marginLeft={"8px !important"}>
                                                <Box display="flex" flexDirection="row">
                                                    <Typography
                                                        fontInter
                                                        fontWeight={props.isClientMsp ? 700 : 500}
                                                        variant="body2"
                                                        marginRight={"8px !important"}
                                                        color={
                                                            props.isClientMsp
                                                                ? theme.palette.neutral[600]
                                                                : theme.palette.blue[800]
                                                        }>
                                                        {formatDate(nextPaymentDate, DATE_DAY_MONTH_ABBR)}
                                                    </Typography>
                                                    <Badge color={badgeColor}>
                                                        {getFriendlyDate(nextPaymentDate || "", "default")}
                                                    </Badge>
                                                </Box>
                                                <Typography
                                                    fontInter
                                                    variant="body4"
                                                    color={theme.palette.neutral[700]}>
                                                    {contract.product?.name}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    )}

                                    <div className="d-flex flex-row align-items-center">
                                        <MenuButton
                                            id={`more-actions-${contract.id}`}
                                            buttonVariant="iconButton"
                                            buttonProps={{
                                                sx: {
                                                    width: "24px",
                                                    minWidth: "24px!important",
                                                    height: "24px",
                                                    boxShadow: "none",
                                                    background: "none",
                                                },
                                            }}
                                            items={[
                                                {
                                                    id: "view",
                                                    children: (
                                                        <>
                                                            <VisibilityOutlinedIcon /> View
                                                        </>
                                                    ),
                                                    onClick: () => handleClickView?.(contract),
                                                },
                                                {
                                                    id: "edit",
                                                    children: (
                                                        <>
                                                            <EditOutlinedIcon /> Edit
                                                        </>
                                                    ),
                                                    onClick: () => handleClickEdit?.(contract),
                                                },
                                                {
                                                    id: "delete",
                                                    children: (
                                                        <>
                                                            <DeleteOutlineOutlinedIcon color="error" />
                                                            <Typography variant="body4" color="error">
                                                                Delete
                                                            </Typography>
                                                        </>
                                                    ),
                                                    onClick: () => handleClickDelete(contract),
                                                },
                                            ].filter(item => {
                                                if (isEditable) return true;
                                                return item.id === "view";
                                            })}
                                            aria-label="settings">
                                            {isUpdating[MutateTypes.DeleteMSPContract + contract.id] ? (
                                                <Loader inline loading />
                                            ) : (
                                                <MoreVert color="neutral" />
                                            )}
                                        </MenuButton>
                                    </div>
                                </Paper>
                            );
                        })
                    ) : !props.isPremiumMsp ? null : (
                        <CardOverlay show variant="opaque" title="There are currently no Upcoming Renewals." />
                    )}
                    <CardOverlay
                        show={!props.isPremiumMsp}
                        variant="blur"
                        icon={<AddTaskOutlinedIcon color="primary" fontSize="large" />}
                        title={
                            <Typography
                                variant="body3"
                                color={theme.palette.secondary[900]}
                                fontWeight="600 !important"
                                fontInter>
                                <Link to={link} style={{color: theme.palette.primary["main"]}} target="_blank">
                                    Subscribe now
                                </Link>{" "}
                                to manage your Renewal Dates
                            </Typography>
                        }
                    />
                </Grid>
            </CardComponent>
            {!!contractToEdit.id && (
                <ViewEditContractDrawer
                    open={!!contractToEdit.id}
                    onClose={() => setContractToEdit({id: "", mode: "READ"})}
                    contract_id={contractToEdit.id}
                    companyId={props.companyId}
                    mode={contractToEdit.mode}
                    isClientMsp={props.isClientMsp}
                />
            )}
        </ErrorBoundary>
    );
};

export default UpcomingRenewalsWidget;
