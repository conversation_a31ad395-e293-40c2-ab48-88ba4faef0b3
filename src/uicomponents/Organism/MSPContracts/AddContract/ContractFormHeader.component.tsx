import {IStackCompany} from "../../../../Interfaces/myStack.interface";
import {IProfile} from "../../../../Interfaces/people.interface";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import IconButton from "../../../Atoms/IconButton/IconButton.Component";
import Typography from "../../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import useTheme from "@mui/material/styles/useTheme";

interface IProps {
    company?: IProfile | IStackCompany;
    onGoBack?: Function;
    isClientMsp?: boolean;
}

const ContractFormHeader = ({company, onGoBack, isClientMsp}: IProps) => {
    const theme = useTheme();
    return (
        <BorderedBox
            width="100%"
            sx={{display: "flex", flexDirection: "row", alignItems: "center", gap: 2, padding: 1}}>
            <IconButton onClick={() => onGoBack?.()}>
                <ArrowBackIcon htmlColor={theme.palette.blue[600]} />
            </IconButton>
            {!isClientMsp && (
                <AvatarComponentV2
                    target="_blank"
                    showProfileType
                    profileTypeMinimized
                    isCompany
                    user={(company as IProfile) || ({} as IProfile)}
                    size={32}
                />
            )}
            <Typography variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                {company?.name}
            </Typography>
        </BorderedBox>
    );
};

export default ContractFormHeader;
