import useTheme from "@mui/material/styles/useTheme";
import {useFormContext} from "react-hook-form";
import {CONTRACT_NOTIFICATION_TYPE_KEYS} from "../../../../constants/mspContracts.constant";
import Typography from "../../../Atoms/Typography/Typography.component";
import CardComponent from "../../../Cards/Card.component";
import RegisteredSwitch from "../../../Molecules/RegisteredSwitch/registeredSwitch.component";
import Box from "../../../Atoms/Box/Box.component";
import CounterInput from "../../../Molecules/CounterInput/CounterInput.component";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import {DATE_FULL_NODAY, formatDate, getDateXDaysFromNow} from "../../../../utils/formatDate";
import {useEffect, useRef} from "react";

const ContractNotificationCard = ({
    readOnly,
    id,
    name,
    notificationKey,
    notice_date = undefined,
    notice_period = undefined,
    expiration_date = undefined,
}) => {
    const theme = useTheme();
    const {watch, setValue, unregister} = useFormContext();
    const prepend = `notification__${id}__`;
    const noticeFieldsFilled = notice_date && notice_period;
    const expirationDateFilled = !!expiration_date;
    const enabled = watch(prepend + "enabled");
    const days_before = watch(prepend + "days_before");
    const isRenewalNotification = notificationKey === CONTRACT_NOTIFICATION_TYPE_KEYS.RENEWAL;
    const dateToCheck = isRenewalNotification ? expiration_date : notice_date;
    const notificationDate =
        (typeof days_before === "string" || typeof days_before === "number") && dateToCheck
            ? getDateXDaysFromNow(-Number(days_before), dateToCheck, true)
            : null;
    const unkownDateText = "Unknown";
    const unsetDateText = "Unset";
    const firstRender = useRef(true);
    useEffect(() => {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }
        if (enabled !== undefined && !enabled && id) {
            days_before && setValue(prepend + "days_before", null);
        }
        if (!enabled) {
            unregister(prepend + "days_before");
        }
    }, [enabled]);

    return (
        <CardComponent
            elevation={0}
            subtitle={
                <Typography
                    fontInter
                    variant="subtitle2"
                    color={theme.palette.neutral[800]}
                    fontWeight="700 !important">
                    {name === "Renewal" ? "Expiration/Renewal" : name}
                </Typography>
            }
            sx={{gridColumn: {xs: "span 12", lg: "span 6"}, ".MuiCardHeader-root": {paddingBottom: 0}}}
            headerActions={
                <RegisteredSwitch
                    id={prepend + "enabled"}
                    disabled={isRenewalNotification ? !expirationDateFilled : !noticeFieldsFilled}
                    readOnly={readOnly}
                    sx={{pointerEvents: readOnly ? "none" : "all"}}
                />
            }>
            <Box display="flex" flexDirection="column" gap={2}>
                <Typography fontInter variant="body4">
                    How many days in advance would you like to be notified about the{" "}
                    {name === "Renewal" ? "upcoming expiration/renewal" : name.toLowerCase()}?
                </Typography>
                <CounterInput
                    id={`${prepend}days_before`}
                    type="number"
                    label="Amount of days"
                    placeholder="X days before"
                    containerSx={{width: "100%", marginBottom: 0}}
                    readOnly={readOnly}
                    disabled={!enabled || (isRenewalNotification ? !expirationDateFilled : !noticeFieldsFilled)}
                    isRequired={enabled}
                    validateOnTheFly={enabled}
                    min={1}
                    keepRegistered
                />
                {!!enabled && (
                    <AlertBanner
                        variant="info"
                        id={prepend + "info-banner"}
                        title={{
                            text: (
                                <Box display="flex" flexDirection="column" alignItems="flex-start">
                                    <Typography fontInter variant="body4">
                                        {name === "Renewal" ? "Expiration/Renewal" : name}:{" "}
                                        <Typography fontInter variant="body4" fontWeight={"400 !important"}>
                                            {dateToCheck ? formatDate(dateToCheck, DATE_FULL_NODAY) : unsetDateText}
                                        </Typography>
                                    </Typography>
                                    <Typography fontInter variant="body4">
                                        Notification date:{" "}
                                        <Typography fontInter variant="body4" fontWeight={"400 !important"}>
                                            {notificationDate
                                                ? formatDate(notificationDate, DATE_FULL_NODAY)
                                                : unkownDateText}
                                        </Typography>
                                    </Typography>
                                </Box>
                            ),
                        }}
                    />
                )}
            </Box>
        </CardComponent>
    );
};

export default ContractNotificationCard;
