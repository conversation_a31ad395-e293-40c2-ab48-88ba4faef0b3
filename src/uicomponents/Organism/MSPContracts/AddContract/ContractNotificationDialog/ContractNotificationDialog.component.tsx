import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {
    IProductContract,
    IProductContractItem,
    IStoreContractNotification,
} from "../../../../../Interfaces/mspContracts.interface";
import {MutateTypes} from "../../../../../Interfaces/queries.interface";
import {useMspContracts} from "../../../../../hooks/fetches/useMspContracts";
import useActiveCompany from "../../../../../hooks/useActiveCompany";
import ErrorBoundary from "../../../../../utils/errorBoundary.util";
import Box from "../../../../Atoms/Box/Box.component";
import Typography from "../../../../Atoms/Typography/Typography.component";
import AlertBanner from "../../../../Molecules/AlertBanner/alertBanner.component";
import ModalComponent from "../../../../Molecules/Modal/Modal.component";
import ContractNotificationCard from "../ContractNotificationCard.component";
// import CompanyEmailSearch from "../../../../Molecules/CompanyEmailSearch/CompanyEmailSearch.component";
import {validateEmail} from "../../../../../utils/validation.util";
import UserListInput from "../../../../Molecules/UserListInput/UserListInput.component";
import {NO_REPLY_CP_EMAIL} from "../../../../../constants/siteFlags";
interface IProps {
    open: boolean;
    onClose?: Function;
    product?: IProductContractItem;
    onSave?: (notifications: IStoreContractNotification[], productId: string) => void;
    contract?: IProductContract;
    onClickShowDetails?: (productId: string) => void;
    isClientMsp?: boolean;
}

const ContractNotificationDialog = ({product, contract, onClickShowDetails, isClientMsp, ...props}: IProps) => {
    const theme = useTheme();
    const methods = useForm();
    const {activeCompany} = useActiveCompany();
    const companyId = activeCompany?.id;
    const {contractNotificationTypes, updateMspContracts, isUpdating} = useMspContracts(companyId);
    const contractTypes = contractNotificationTypes?.data;
    const isEditing = !!contract?.id;
    const notice_date = contract?.notice_date || product?.data?.notice_date;
    const notice_period = contract?.notice_period || product?.data?.notice_period;
    const expiration_date = contract?.end_date || product?.data?.end_date;
    const noticeFieldsFilled = !!(notice_date && notice_period);
    const expirationFieldsFilled = !!expiration_date;
    const parentName = contract?.company?.name || product?.product?.parent?.name;
    const productName = contract?.product?.name || product?.product?.product_name;
    const isSaving = isUpdating[MutateTypes.UpdateMSPContract];

    const handleSubmit = () => {
        const values = methods.getValues();
        let hasEnabledNotification = false;
        const saveValue = (contractNotificationTypes?.data || []).map(type => {
            if (values[`notification__${type.id}__enabled`]) {
                hasEnabledNotification = true;
            }
            return {
                contract_notification_type_id: type.id,
                days_before: values[`notification__${type.id}__days_before`] || null,
                recipients: values["recipients"]?.map(r => (typeof r === "string" ? r : r.email)) || [],
                enabled: values[`notification__${type.id}__enabled`] || false,
            };
        });
        if (hasEnabledNotification && !values["recipients"]?.length) {
            methods.setError("recipients", {type: "required"});
            return;
        }
        if (isEditing) {
            updateMspContracts.mutate({
                mutate_type: MutateTypes.UpdateMSPContract,
                company_id: activeCompany?.id,
                body: {
                    id: contract?.id,
                    contract_agreement_id: contract?.contract_agreement_id || null,
                    parent_id: contract?.parent_id || null,
                    notifications: saveValue,
                },
                onSuccess: () => {
                    props.onSave?.(saveValue, product?.product?.id || "");
                    props.onClose?.();
                },
            });
            return;
        }
        props.onSave?.(saveValue, product?.product?.id || "");
    };

    useEffect(() => {
        if (contract?.notifications && props.open) {
            const newValues = contract.notifications.reduce((acc, notification) => {
                acc[`notification__${notification.contract_notification_type_id}__enabled`] = !!notification.enabled;
                acc[`notification__${notification.contract_notification_type_id}__days_before`] =
                    notification.days_before;
                acc["recipients"] = notification.recipients || [];
                return acc;
            }, {});
            setTimeout(() => {
                Object.keys(newValues).forEach(key => {
                    methods.setValue(key, newValues[key]);
                });
            }, 250);
        }
    }, [props.open, contract]);

    useEffect(() => {
        setTimeout(() => {
            if (props.open && product && !contract?.id) {
                const defaultValues =
                    product?.data?.notifications?.reduce((acc, notification) => {
                        acc[`notification__${notification.contract_notification_type_id}__days_before`] =
                            notification.days_before;
                        acc[`notification__${notification.contract_notification_type_id}__recipients`] =
                            notification.recipients;
                        acc[`notification__${notification.contract_notification_type_id}__enabled`] =
                            notification.enabled;
                        return acc;
                    }, {}) || {};
                defaultValues["recipients"] = product?.data?.notifications?.[0]?.recipients || [];
                Object.keys(defaultValues).forEach(key => {
                    methods.setValue(key, defaultValues[key]);
                });
            }
        }, 0);
    }, [props.open, product]);

    return (
        <ErrorBoundary>
            <ModalComponent
                open={props.open}
                onClose={() => props.onClose?.()}
                onSuccess={handleSubmit}
                title={`Contract Notification`}
                loading={isSaving}
                customTitle={
                    <Box
                        display="flex"
                        flexDirection={{xs: "column", md: "row"}}
                        alignItems={{xs: "flex-start", md: "center"}}>
                        Contract Notification{" "}
                        <ArrowForwardIcon
                            htmlColor={theme.palette.neutral[600]}
                            fontSize="small"
                            sx={{marginLeft: 1}}
                        />
                        <Typography variant="body2" fontInter color={theme.palette.neutral[600]} textAlign="center">
                            {parentName ? parentName + " - " : ""}
                            {productName}
                        </Typography>
                    </Box>
                }
                okButtonText={"Save"}
                okBtnProps={{
                    color: "secondary",
                }}
                additionalBtnProps={{
                    variant: "contained",
                }}
                cancelButtonText={"Cancel"}
                modalTheme="blue"
                justifyButtons="flex-start"
                customModalBtnTracking={{
                    company_id: companyId,
                }}
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
                }}
                shouldCloseOnClickOutside={false}
                content={
                    <FormProvider {...methods}>
                        <Grid
                            container
                            display="grid"
                            gap={2}
                            gridTemplateColumns={"repeat(12, 1fr)"}
                            width="100%"
                            alignItems="flex-start"
                            padding={1}
                            bgcolor={theme.palette.neutral[100]}>
                            {!noticeFieldsFilled || !expirationFieldsFilled ? (
                                <AlertBanner
                                    id="configure_notification"
                                    subTitle={{
                                        text: contract?.id ? (
                                            <>
                                                Before configuring notifications, set <strong>notice period(s)</strong>{" "}
                                                and/or <strong>expiration date(s)</strong>.
                                            </>
                                        ) : (
                                            <>
                                                Before setting up notifications, please ensure the expiration/renewal
                                                date and notice period are configured.{" "}
                                                <strong
                                                    style={{cursor: "pointer", color: theme.palette.blue[600]}}
                                                    onClick={() => onClickShowDetails?.(product?.product?.id || "")}>
                                                    Click here
                                                </strong>{" "}
                                                to edit these details now.
                                            </>
                                        ),
                                    }}
                                    titleContainerSx={{display: "none"}}
                                    styles={{gridColumn: "1 / span 12"}}
                                    variant="warning"
                                />
                            ) : null}
                            <Typography
                                variant="body2"
                                fontWeight={600}
                                fontInter
                                gridColumn="span 12"
                                color={theme.palette.neutral[700]}
                                paddingBottom={1}>
                                Would you like to enable notifications?
                            </Typography>
                            {!contractTypes
                                ? null
                                : [...(contractTypes || [])]
                                      ?.reverse()
                                      ?.map(notificationType => (
                                          <ContractNotificationCard
                                              key={notificationType.id}
                                              id={notificationType.id}
                                              name={notificationType.name}
                                              notificationKey={notificationType.key}
                                              readOnly={false}
                                              notice_date={notice_date}
                                              notice_period={notice_period}
                                              expiration_date={expiration_date}
                                          />
                                      ))}
                            {/* <CompanyEmailSearch
                                id="recipients"
                                label="Notification Recipients"
                                disabled={!noticeFieldsFilled && !expirationFieldsFilled}
                            /> */}
                            <UserListInput
                                id="recipients"
                                label="Notification Recipients"
                                placeholder="Type to select a user or add an email"
                                validateOption={v => validateEmail(v)}
                                helperText="Select a user from your company, and/or input the emails separated by commas. If no email recipient is selected or inputted, no email notifications will be received."
                                sx={{gridColumn: "1 / span 12"}}
                                disabled={!noticeFieldsFilled && !expirationFieldsFilled}
                                clearOnBlur={false}
                                validateOnTheFly
                            />
                            <AlertBanner
                                id="whitelist-cp-tip"
                                subTitle={{
                                    text: (
                                        <>
                                            <strong>Tip: </strong>To avoid missing any notifications, please add this
                                            email,
                                            {` ${NO_REPLY_CP_EMAIL}`}, to your allowlist.
                                        </>
                                    ),
                                }}
                                styles={{gridColumn: "1 / span 12"}}
                                variant="info"
                                titleContainerSx={{display: "none"}}
                            />
                        </Grid>
                    </FormProvider>
                }
            />
        </ErrorBoundary>
    );
};

export default ContractNotificationDialog;
