import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IProductContractItem, IStoreContractNotification} from "../../../../../Interfaces/mspContracts.interface";
import {CONTRACT_NOTIFICATION_TYPE_KEYS} from "../../../../../constants/mspContracts.constant";
import {useMspContracts} from "../../../../../hooks/fetches/useMspContracts";
import useActiveCompany from "../../../../../hooks/useActiveCompany";
import {DATE_FULL_NODAY, formatDate, getDateXDaysFromNow} from "../../../../../utils/formatDate";
import {validateEmail} from "../../../../../utils/validation.util";
import Box from "../../../../Atoms/Box/Box.component";
import Typography from "../../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../../FormControls/TextBox/textBox.component";
import AlertBanner from "../../../../Molecules/AlertBanner/alertBanner.component";
import AutoCompleteComponent from "../../../../Molecules/Autocomplete/Autocomplete.component";
import CounterInput from "../../../../Molecules/CounterInput/CounterInput.component";
import ModalComponent from "../../../../Molecules/Modal/Modal.component";
import UserListInput from "../../../../Molecules/UserListInput/UserListInput.component";

interface IProps {
    open: boolean;
    onClose?: Function;
    product?: IProductContractItem;
    onSave?: (notification: IStoreContractNotification, productId: string) => void;
    notificationId?: string;
}

const ContractPeriodNotificationDialog = (props: IProps) => {
    const theme = useTheme();
    const defaultValues = props.product?.data;
    const defaultNotificationValue = props.product?.data?.notifications?.find(
        n => n.contract_notification_type_id === props.notificationId,
    );
    const defaultFormValues = {
        days_before: defaultNotificationValue?.days_before || "",
        end_date: defaultValues?.end_date || "",
        contract_agreement_id: defaultValues?.contract_agreement_id || "",
        recipients: defaultNotificationValue?.recipients || [],
    };
    const methods = useForm({
        defaultValues: defaultFormValues,
    });
    const {activeCompany} = useActiveCompany();
    const companyId = activeCompany?.id;
    const {contractAgreementTypes, contractNotificationTypes} = useMspContracts(companyId);
    const contractNotificationKey =
        (contractNotificationTypes?.data || []).find(type => type.id === props.notificationId)?.key || "";
    const notice_date = defaultValues?.notice_date;
    const notice_period = defaultValues?.notice_period;
    const expiration_date = defaultValues?.end_date;
    const days_before = methods.watch("days_before");
    const noticeFieldsFilled = !!(notice_date && notice_period);
    const expirationFieldsFilled = !!expiration_date;
    const isRenewalNotification = contractNotificationKey === CONTRACT_NOTIFICATION_TYPE_KEYS.RENEWAL;
    const name = isRenewalNotification ? "Expiration Date" : "Notice";
    const dateToCheck = isRenewalNotification ? expiration_date : notice_date;
    const notificationDate =
        typeof days_before === "string" && dateToCheck
            ? getDateXDaysFromNow(-Number(days_before), dateToCheck, true)
            : null;
    const contractAgreementOptions =
        contractAgreementTypes?.data?.map(type => ({
            id: type.id,
            label: type.name,
            key: type.key.replace("QUATERLY", "QUARTERLY"),
        })) || [];

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const values = methods.getValues();
        const saveValue: IStoreContractNotification = {
            contract_id: "",
            contract_notification_type_id: props.notificationId || "",
            days_before: values["days_before"],
            recipients: values["recipients"] || [],
        };
        props.onSave?.(saveValue, props.product?.product?.id || "");
        props.onClose?.();
    };

    useEffect(() => {
        if (props.open) {
            methods.reset(defaultFormValues);
        }
    }, [props.open]);

    return (
        <ModalComponent
            open={props.open}
            onClose={() => props.onClose?.()}
            onSuccess={handleSubmit}
            title={`Contract Notification`}
            customTitle={
                <Box display="flex" flexDirection="row" alignItems="center">
                    Contract Notification <ArrowForwardIcon htmlColor={theme.palette.neutral[600]} fontSize="small" />{" "}
                    <Typography variant="body2" fontInter color={theme.palette.neutral[600]}>
                        {props.product?.product?.parent?.name} - {props.product?.product?.product_name}
                    </Typography>
                </Box>
            }
            okButtonText={"Save"}
            okBtnProps={{
                color: "secondary",
            }}
            additionalBtnProps={{
                variant: "contained",
            }}
            cancelButtonText={"Cancel"}
            modalTheme="blue"
            justifyButtons="flex-start"
            customModalBtnTracking={{
                company_id: companyId,
            }}
            dialogSx={{
                ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                ".MuiDialogContent-root": {overflow: "hidden"},
            }}
            shouldCloseOnClickOutside={false}
            content={
                <FormProvider {...methods}>
                    <Grid
                        container
                        display="grid"
                        gap={2}
                        gridTemplateColumns={"repeat(12, 1fr)"}
                        width="100%"
                        alignItems="end"
                        padding={1}
                        bgcolor={theme.palette.neutral[100]}>
                        <Typography
                            variant="body2"
                            fontWeight={600}
                            fontInter
                            gridColumn="span 12"
                            color={theme.palette.neutral[700]}
                            paddingBottom={1}>
                            Would you like to enable notifications?
                        </Typography>
                        <AlertBanner
                            variant="info"
                            id={"contract-period-info-banner"}
                            title={{
                                text: (
                                    <Box display="flex" flexDirection="row" alignItems="flex-start">
                                        <Typography fontInter variant="body4">
                                            {name} date: {formatDate(dateToCheck, DATE_FULL_NODAY)}
                                        </Typography>
                                        <Typography fontInter variant="body4">
                                            Notification date:{" "}
                                            {notificationDate && formatDate(notificationDate, DATE_FULL_NODAY)}
                                        </Typography>
                                    </Box>
                                ),
                            }}
                            containerSx={{gridColumn: "span 12"}}
                        />
                        <Box display="flex" flexDirection="column" gap={2} sx={{gridColumn: "span 12"}}>
                            <Typography fontInter variant="body4">
                                How many days in advance would you like to be notified about the{" "}
                                {isRenewalNotification ? "renewal period" : name.toLowerCase()}?
                            </Typography>
                            <CounterInput
                                id={`days_before`}
                                type="number"
                                label="Amount of days"
                                placeholder="X days before"
                                containerSx={{width: "100%", marginBottom: 0}}
                                // disabled={!enabled || (isRenewalNotification ? !expirationDateFilled : !noticeFieldsFilled)}
                                // isRequired={enabled}
                                validateOnTheFly
                                min={1}
                            />
                            <Box display="grid" gridTemplateColumns={"1fr 2fr"} gap={1}>
                                <TextBoxComponent
                                    id="end_date"
                                    label={"Expiration Date "}
                                    type="date"
                                    validateOnTheFly
                                    // minDate={
                                    //     minExpirationDate
                                    //         ? formatDate(minExpirationDate.toISOString(), DATE_FORMAT_Year_Month_Day)
                                    //         : undefined
                                    // }
                                />
                                <AutoCompleteComponent
                                    id="contract_agreement_id"
                                    options={contractAgreementOptions}
                                    label="Contract Term"
                                    validateOnTheFly
                                    isOptionEqualToValue={(option, value) => {
                                        const valueAsString = String(value);
                                        return option.id === valueAsString || option.label === valueAsString;
                                    }}
                                    infoText="Indicates the duration of the contract."
                                    // onValueChange={() => {
                                    //     methods.setValue("billing_frequency", "");
                                    // }}
                                    helperText="It defines the duration of the agreement between the provider and the customer. During this term, the terms and conditions, including pricing and services, are fixed."
                                />
                            </Box>
                        </Box>
                        <Typography variant="body2" fontWeight={600} fontInter>
                            Recipients
                        </Typography>
                        <UserListInput
                            id="recipients"
                            label="Notification Recipients"
                            placeholder="Type to select a user or add an email"
                            validateOption={v => validateEmail(v)}
                            helperText="Select a user from your company, and/or input the emails separated by commas. If no email recipient is selected or inputted, no email notifications will be received."
                            sx={{gridColumn: "1 / span 12"}}
                            disabled={!noticeFieldsFilled && !expirationFieldsFilled}
                        />
                    </Grid>
                </FormProvider>
            }
        />
    );
};

export default ContractPeriodNotificationDialog;
