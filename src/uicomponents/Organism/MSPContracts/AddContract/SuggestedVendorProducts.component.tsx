import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect} from "react";
import {DEFAULT_QUERY_CONFIGS, MSP_CONTRACT_TEXT} from "../../../../constants/commonStrings.constant";
import {useInfinite} from "../../../../hooks/useInfinite";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IPaginationData} from "../../../../Interfaces/pagination.interface";
import {productService} from "../../../../services/products.service";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import {IProductSearchItem} from "../../../Molecules/ProductSearch/productSearch.component";

interface IProps {
    companyId?: string;
    onClickProduct?: (product: IProductSearchItem) => void;
    title?: string;
    filterIds?: string[];
}

const SuggestedVendorProducts = ({companyId, onClickProduct, title, filterIds}: IProps) => {
    const theme = useTheme();
    const {hasPermissions} = usePermissions();
    const isEditable = hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]);
    const productsQuery = useInfiniteQuery<IPaginationData<IProductSearchItem[]>>(
        ["suggested-vendor-products", companyId],
        ({pageParam}) => {
            return new Promise<IPaginationData<IProductSearchItem[]>>((resolve, reject) => {
                if (!companyId) reject();
                const body: any = {
                    paged: 1,
                    page: pageParam || 1,
                    items_per_page: 9,
                    company_id: companyId,
                };
                return productService.searchProducts(body).then(res => resolve(res.data));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!companyId,
            getNextPageParam: res => {
                return res?.meta
                    ? res?.meta?.last_page > res?.meta?.current_page
                        ? res?.meta?.current_page + 1
                        : null
                    : null;
            },
        },
    );
    const isLoading = productsQuery.isLoading;
    const {lastElementRef, pageNum} = useInfinite(productsQuery?.data?.pages?.[0]?.meta?.last_page || 0, isLoading);
    const products: IProductSearchItem[] = (
        productsQuery.data?.pages?.reduce((cur: IProductSearchItem[], page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || []
    )?.filter(p => !filterIds?.includes(p.id));

    const handleClick = (e: any, product: IProductSearchItem) => {
        e.stopPropagation();
        onClickProduct?.(product);
    };

    useEffect(() => {
        if (productsQuery.hasNextPage) {
            productsQuery.fetchNextPage();
        }
    }, [pageNum]);

    if ((!isLoading && !products.length && !productsQuery.isFetchingNextPage) || !companyId) return null;
    return (
        <Grid container display="grid" gridTemplateColumns={"100%"} gap={1} width="100%">
            {title ? (
                <Typography
                    variant="body3"
                    fontInter
                    color={theme.palette.neutral[800]}
                    width="100%"
                    fontWeight={"600 !important"}>
                    {title}
                </Typography>
            ) : null}
            <Grid
                container
                display="grid"
                gridTemplateColumns={{
                    xs: "repeat(1, 1fr)",
                    md: "repeat(2, 1fr)",
                    lg: "repeat(3, 1fr)",
                }}
                gap={1}
                width="100%"
                maxHeight={160}
                sx={{overflowY: "auto"}}>
                {isLoading ? (
                    Array(4)
                        .fill(null)
                        .map((_, i) => (
                            <Skeleton
                                key={i}
                                sx={{
                                    borderRadius: 1,
                                    padding: 1,
                                    display: "flex",
                                    alignItems: "center",
                                    border: `1px solid ${theme.palette.neutral[300]}`,
                                    gap: 2,
                                    width: "100%",
                                    justifyContent: "space-between",
                                    height: 40,
                                }}
                            />
                        ))
                ) : (
                    <>
                        {products.map((product, index) => (
                            <Grid item gridColumn="span 1" key={product.friendly_url + index}>
                                <Paper
                                    elevation={0}
                                    sx={{
                                        borderRadius: 1,
                                        padding: 1,
                                        display: "flex",
                                        alignItems: "center",
                                        border: `1px solid ${theme.palette.neutral[300]}`,
                                        gap: 2,
                                        justifyContent: "space-between",
                                        height: "fit-content",
                                        width: "100%",
                                    }}>
                                    <div className="d-flex flex-row align-items-center">
                                        <div style={{height: 32, width: 32}}>
                                            <AvatarComponentV2
                                                isCompany
                                                user={product.company as any}
                                                size={32}
                                                showProfileType
                                                profileTypeMinimized
                                                target="_blank"
                                            />
                                        </div>
                                        <Box display="flex" flexDirection="column" marginLeft={2} gap={0.5}>
                                            <Typography
                                                fontInter
                                                lineHeight={1}
                                                fontWeight={700}
                                                variant="body3"
                                                sx={{
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    textOverflow: "ellipsis",
                                                    maxWidth: "fit-content",
                                                }}
                                                color={theme.palette.blue[600]}
                                                whiteSpace="nowrap"
                                                title={product.name}>
                                                {product.name}
                                            </Typography>
                                            <Typography
                                                variant="body4"
                                                lineHeight={1}
                                                fontInter
                                                fontWeight={500}
                                                color={theme.palette.blue[800]}>
                                                {product?.company?.name}
                                            </Typography>
                                        </Box>
                                    </div>
                                    <div className="d-flex flex-row align-items-center">
                                        <div
                                            style={{marginLeft: 16}}
                                            onClick={isEditable ? e => handleClick(e, product) : undefined}>
                                            {isEditable && (
                                                <AddCircleOutlineIcon
                                                    titleAccess={MSP_CONTRACT_TEXT.ADD}
                                                    sx={{
                                                        height: "24px !important",
                                                        width: "24px !important",
                                                        cursor: "pointer",
                                                    }}
                                                    htmlColor={
                                                        isEditable
                                                            ? theme.palette.blue[600]
                                                            : theme.palette.neutral["main"]
                                                    }
                                                />
                                            )}
                                        </div>
                                    </div>
                                </Paper>
                            </Grid>
                        ))}
                        <div ref={lastElementRef} />
                    </>
                )}
            </Grid>
        </Grid>
    );
};

export default SuggestedVendorProducts;
