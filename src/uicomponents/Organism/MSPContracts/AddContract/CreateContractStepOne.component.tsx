import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import SearchIcon from "@mui/icons-material/Search";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useState} from "react";
import {useFormContext} from "react-hook-form";
import {IStackCategorization, IStackCompany} from "../../../../Interfaces/myStack.interface";
import {IProfile} from "../../../../Interfaces/people.interface";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import VendorSearch, {ISearchVendorItem} from "../../../Molecules/VendorSearch/vendorSearch.component";
import MissingRequestDialog from "../../MissingRequestDialog/missingRequestDialog.component";
import MspSuggestedSubscriptions from "../MspSuggestions/MspSuggestedSubscriptions.component";
import Skeleton from "@mui/material/Skeleton";
import LocalCompanyProductModal, {
    ILocalCompanyProductModalProps,
} from "../../../../components/LocalCompanyProductModal/localCompanyProductModal.component";
import {ICompany} from "../../../../Interfaces/company.interface";
import ClientVendorInput from "../../../Molecules/ClientVendorInput/clientVendorInput.component";

interface IProps {
    companyId?: string;
    setContractCompany?: (company: IProfile) => void;
    onOptionsChange?: (newVendorOptions) => void;
    defaultVendorOptions?: ISearchVendorItem[];
    isClientMsp?: boolean;
}

const CreateContractStepOne = ({isClientMsp, companyId, ...props}: IProps) => {
    const [contactForm, setContactForm] = useState<{noResults: boolean; open: boolean}>({
        noResults: false,
        open: false,
    });
    const {activeCompany, isLoading} = useActiveCompany();
    const [showSuggested, setShowSuggested] = useState(true);
    const [overrideVendors, setOverrideVendors] = useState<IStackCompany[] | undefined>(undefined);
    const theme = useTheme();
    const {setValue, watch} = useFormContext();
    const sold_by_distributor = watch("sold_by_distributor");

    const handleClickSuggested = (suggestedNaviStackProduct: IStackCategorization) => {
        props.onOptionsChange?.([suggestedNaviStackProduct.stack_company]);
        props.setContractCompany?.(suggestedNaviStackProduct.stack_company as unknown as IProfile);
        setOverrideVendors([suggestedNaviStackProduct.stack_company]);
        setValue("vendor_id", suggestedNaviStackProduct?.stack_company?.id || "");
    };

    useEffect(() => {
        if (isClientMsp && activeCompany?.id && sold_by_distributor === undefined) {
            setValue("sold_by_distributor", "0");
        }
    }, [isClientMsp, activeCompany?.id, sold_by_distributor]);

    /**
     * Create Vendor/Product methods
     */
    const [localCompanyProductModalProps, setLocalCompanyProductModalProps] = useState<ILocalCompanyProductModalProps>({
        open: false,
    });

    // Open create company/product modal
    const onCreateOptionSelected = (newItem: any): void => {
        setLocalCompanyProductModalProps({
            companyName: newItem.inputValue ?? "",
            open: true,
        });
    };

    // Set the brand new company as selected company for contract
    const onCreateCompanyProductSuccess = (vendor: ICompany) => {
        console.info("CreateContractStepOne::onCreateCompanyProductSuccess.vendor: ", vendor);
        props.onOptionsChange?.([vendor as unknown as IStackCompany]);
        props.setContractCompany?.(vendor as unknown as IProfile);
        setOverrideVendors([vendor as unknown as IStackCompany]);
        setValue("vendor_id", vendor?.id || "");
    };

    // Close create company/product modal
    const onCloseCompanyProductModal = () => {
        setLocalCompanyProductModalProps({
            open: false,
        });
    };

    return (
        <>
            <Grid container gap={3}>
                {isLoading ? (
                    <Skeleton width="100%" height={60} />
                ) : isClientMsp ? (
                    <Box
                        sx={{
                            display: "grid",
                            alignItems: "center",
                            gridTemplateColumns: "2fr 1fr",
                            gap: 2,
                            width: "100%",
                            "&>*": {
                                flexGrow: 1,
                            },
                        }}>
                        <Typography
                            fontInter
                            variant="subtitle2"
                            fontWeight="600 !important"
                            sx={{gridColumn: "span 2"}}>
                            Begin by selecting your Vendor or Distributor
                        </Typography>
                        <ClientVendorInput
                            companyId={companyId ?? activeCompany?.id ?? ""}
                            id="client_vendor_name"
                            placeholder="Input the vendor name"
                            label="Vendor/Distributor"
                            InputLabelProps={{
                                shrink: true,
                            }}
                            validateOnTheFly
                            isRequired
                            InputProps={{
                                startAdornment: <SearchIcon />,
                            }}
                            helperText=""
                            keepRegistered
                            sx={{
                                gridColumn: "span 1",
                            }}
                            handleInputChange={(newValue: string) => {
                                props.setContractCompany?.({name: newValue || ""} as IProfile);
                                return true;
                            }}
                            showCreateOption
                        />
                        <AutoCompleteComponent
                            id="sold_by_distributor"
                            label="Type"
                            options={[
                                {id: "0", label: "Vendor"},
                                {id: "1", label: "Distributor"},
                            ]}
                            keepRegistered={true}
                            sx={{
                                gridColumn: "span 1",
                            }}
                            validationSchema={v => v === "0" || v === "1"}
                            key={sold_by_distributor}
                            defaultValue={"0"}
                        />
                    </Box>
                ) : (
                    <VendorSearch
                        id="vendor_id"
                        label="Vendor/Distributor"
                        placeholder="Start Typing..."
                        sx={{width: "100%"}}
                        onValueChange={(_, __, newItem) => {
                            if (newItem.isNew ?? false) {
                                setValue("vendor_id", undefined);
                                onCreateOptionSelected(newItem);
                                return;
                            }
                            if (!newItem) {
                                setOverrideVendors([]);
                                props.onOptionsChange?.([]);
                            }
                            props.setContractCompany?.(
                                ((newItem?.company as IProfile) ?? (newItem?.avatar?.user as any)) || null,
                            );
                        }}
                        onVendorOptionsChange={vendors => {
                            props.onOptionsChange?.(vendors);
                        }}
                        clearOnSelect={false}
                        validateOnTheFly
                        isRequired
                        keepRegistered
                        defaultOptions={props.defaultVendorOptions || []}
                        overrideVendors={overrideVendors as []}
                        key={overrideVendors?.length}
                        showCreateOption
                    />
                )}
                {contactForm.noResults && (
                    <>
                        <Typography
                            variant="body3"
                            fontWeight="bold !important"
                            marginBottom={1}
                            color={theme.palette.neutral[800]}>
                            <ErrorOutlineIcon color="warning" sx={{marginRight: 1}} />
                            Ah, Snap! Don’t see your vendor or product?
                        </Typography>
                        <Typography variant="body3" color={theme.palette.neutral[800]}>
                            We are always adding dozens of new products every month. <br /> Help us by
                            <span
                                onClick={() => setContactForm({...contactForm, open: true})}
                                style={{
                                    fontSize: 16,
                                    fontWeight: 500,
                                    fontFamily: "var(--Inter)",
                                    color: theme.palette.blue[600],
                                    cursor: "pointer",
                                }}>
                                {" "}
                                sending your request
                            </span>{" "}
                            and we will get right on it!
                        </Typography>
                    </>
                )}
                {showSuggested && !isClientMsp && (
                    <Grid item xs={12} maxHeight="160px" sx={{maxHeight: 160, overflowY: "auto"}}>
                        <MspSuggestedSubscriptions
                            companyId={activeCompany?.id || ""}
                            onClick={handleClickSuggested}
                            title="Or choose from the suggested Vendors or Distributors:"
                            noDataCallback={(hasNoData: boolean) => {
                                setShowSuggested(!hasNoData);
                            }}
                            isPremiumMsp
                            showVendors
                            hidePartnerStatus
                            items_per_page={9}
                            allowThreeColumns
                        />
                    </Grid>
                )}
                <Grid item xs={12}>
                    <Typography fontInter variant="subtitle2" fontWeight="600 !important">
                        Optionally, you can enter a custom name for this contract.
                    </Typography>
                </Grid>
                <TextBoxComponent
                    id="name"
                    label="Contract Name"
                    placeholder="Enter a custom name (Optional)"
                    containerSx={{width: "100%"}}
                    keepRegistered
                />
            </Grid>
            {!!localCompanyProductModalProps.open && (
                <LocalCompanyProductModal
                    {...localCompanyProductModalProps}
                    onClose={onCloseCompanyProductModal}
                    onSuccess={onCreateCompanyProductSuccess}
                />
            )}
            {!!contactForm.open && (
                <MissingRequestDialog
                    open={contactForm.open}
                    companyId={activeCompany?.id || ""}
                    onClose={() => setContactForm({open: false, noResults: false})}
                    onSave={() => setContactForm({open: false, noResults: false})}
                />
            )}
        </>
    );
};

export default CreateContractStepOne;
