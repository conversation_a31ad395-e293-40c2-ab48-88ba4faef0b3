import {ArrowDownward, <PERSON>Up<PERSON>, InfoOutlined, WarningAmberOutlined} from "@mui/icons-material";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import AttachFileOutlined from "@mui/icons-material/AttachFileOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import MoreVert from "@mui/icons-material/MoreVert";
import NotificationsActiveOutlinedIcon from "@mui/icons-material/NotificationsActiveOutlined";
import NotificationsOffOutlinedIcon from "@mui/icons-material/NotificationsOffOutlined";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {useCallback, useEffect, useMemo, useState} from "react";
import {Column} from "react-table";
import {TableV2Component} from "../../../../components/Table/TableV2.component";
import {PERMANENT_ACTION_CONFIRMATION} from "../../../../constants/commonStrings.constant";
import {CONTRACT_NOTIFICATION_TYPE_KEYS, CONTRACT_TYPE_KEYS} from "../../../../constants/mspContracts.constant";
import {CPSortType} from "../../../../enums/sortType.enum";
import {useMspContracts} from "../../../../hooks/fetches/useMspContracts";
import {useQueryHelper} from "../../../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import useCurrency from "../../../../hooks/useCurrency";
import useIsPlaidAvailable from "../../../../hooks/useIsPlaidAvailable";
import usePermissions from "../../../../hooks/usePermissions";
import {useSubdomain} from "../../../../hooks/useSubdomain";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IContract} from "../../../../Interfaces/mspContracts.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {DATE_FULL_SHORT_MONTH, formatDate} from "../../../../utils/formatDate";
import {pluralizeString} from "../../../../utils/formatString.util";
import Loader from "../../../../utils/loader";
import {getNextPaymentDate, getNextRelevantDate} from "../../../../utils/mspContracts.util";
import {getValueAtKey} from "../../../../utils/object.util";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import ExpenseAccountAvatar from "../../../Molecules/ExpenseAccountAvatar/ExpenseAccountAvatar.component";
import MenuButton from "../../../Molecules/MenuButton/menuButton.component";
import LinkExpenseDrawer from "../../CompanyExpenses/LinkExpenseDrawer/LinkExpenseDrawer.component";
import ViewEditContractDrawer from "../ViewEditContractDrawer/ViewEditContractDrawer.component";

interface IProps {
    data?: IContract[];
    companyId?: string;
    isLoading?: boolean;
    isClientMsp?: boolean;
    readOnly?: boolean;
    hideTotals?: boolean;
    includeSubcategoryColumn?: boolean;
}

const DEFAULT_BE_SORT: {sort: CPSortType.ASC | CPSortType.DESC; order_by: string} = {
    sort: CPSortType.ASC,
    order_by: "important_dates",
};

const getSortedData = (data: IContract[], sort: {sort: CPSortType.ASC | CPSortType.DESC; order_by: string}) =>
    [...(data || [])]?.sort((a: IContract, b: IContract) => {
        if (sort.order_by === "important_dates") {
            const aDate = getNextRelevantDate(a)?.date;
            const bDate = getNextRelevantDate(b)?.date;
            if (aDate && bDate) {
                if (sort.sort === CPSortType.ASC) {
                    return aDate.getTime() - bDate.getTime();
                } else if (sort.sort === CPSortType.DESC) {
                    return bDate.getTime() - aDate.getTime();
                }
            }
            return 0;
        }
        if (
            sort.order_by === "name" ||
            sort.order_by === "contract_billing_type.name" ||
            sort.order_by === "recurrence" ||
            sort.order_by === "auto_renew" ||
            sort.order_by === "client_vendor.name"
        ) {
            const aValue = String(getValueAtKey(sort.order_by, a));
            const bValue = String(getValueAtKey(sort.order_by, b));
            return (sort.sort === CPSortType.ASC ? aValue : bValue).localeCompare(
                sort.sort === CPSortType.ASC ? bValue : aValue,
            );
        }
        if (sort.order_by === "cost" || sort.order_by === "amount") {
            const keys = {
                cost: "cost_after_discount",
                amount: "custom_properties.amount_purchased",
            };
            const aCost = Number(getValueAtKey(keys[sort.order_by], a) || "0");
            const bCost = Number(getValueAtKey(keys[sort.order_by], b) || "0");
            return sort.sort === CPSortType.ASC ? aCost - bCost : bCost - aCost;
        }
        if (sort.order_by === "contract_next_payment_dates") {
            const aDate = getNextPaymentDate(a.contract_next_payment_dates)?.__date || new Date();
            const bDate = getNextPaymentDate(b.contract_next_payment_dates)?.__date || new Date();
            //@ts-ignore
            return sort.sort === CPSortType.ASC ? aDate - bDate : bDate - aDate;
        }
        if (sort.order_by === "end_date" || sort.order_by === "notice_date") {
            const aValue = getValueAtKey(sort.order_by, a);
            const bValue = getValueAtKey(sort.order_by, b);
            const aDate = aValue ? new Date((aValue || "") as string) : new Date(0);
            const bDate = bValue ? new Date((bValue || "") as string) : new Date(0);
            //@ts-ignore
            return sort.sort === CPSortType.ASC ? aDate - bDate : bDate - aDate;
        }
        return 0;
    });

const YourSubscriptionsListViewTable = (props: IProps) => {
    const {data, isLoading, hideTotals} = props;
    const [contractDialog, setContractDialog] = useState<{contract_id: string; mode: "READ" | "EDIT"}>();
    const [sort, setSort] = useState<{sort: CPSortType.ASC | CPSortType.DESC; order_by: string}>(DEFAULT_BE_SORT);
    const [linkedExpenseDrawerContract, setLinkedExpenseDrawerContract] = useState<IContract | undefined>();
    const theme = useTheme();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = !props.readOnly && hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]);
    const {isUpdating, updateMspContracts, contractBillingTypeOptions, frequencyOptions, contractNotificationTypes} =
        useMspContracts(props.companyId, {
            calls: {all: false, contractBillingTypeOptions: true},
        });
    const {activeCompany, isClientMsp, isLoading: isLoadingActiveCompany, hasIntegrationsAccess, clientMspHasNoExpensesAccess} = useActiveCompany();
    const {isLoading: isLoadingPlaidEnabled, isAvailable} = useIsPlaidAvailable();
    const isPlaidEnabled =
        (!isLoadingPlaidEnabled && isAvailable) ||
        (!isLoadingActiveCompany && isClientMsp && !activeCompany?.client_plaid_integration_enabled) ||
        (!isLoadingActiveCompany && !hasIntegrationsAccess);
    const subdomainObj = useSubdomain();
    const {formatCurrency, companyCurrency, convertCurrency, exchangeRates} = useCurrency({companyId: props.companyId});
    const {invalidateMatchedQueries} = useQueryHelper();
    const PerOptions = useMemo(() => {
        const options: Record<string, string> = {};
        contractBillingTypeOptions.data?.forEach(item => {
            options[item.id] = item.name;
        });
        return options;
    }, [contractBillingTypeOptions]);

    const frequencyNames = useMemo(() => {
        const names: Record<string, string> = {};
        frequencyOptions.data?.forEach(item => {
            item.intervals?.forEach(i => {
                names[`${i.key}`] = i.name;
            });
        });
        return names;
    }, [frequencyOptions]);

    const renewalContractNotificationTypeId = contractNotificationTypes.data?.find(
        type => type.key === CONTRACT_NOTIFICATION_TYPE_KEYS.RENEWAL,
    )?.id;
    const noticeContractNotificationTypeId = contractNotificationTypes.data?.find(
        type => type.key === CONTRACT_NOTIFICATION_TYPE_KEYS.NOTICE_PERIOD,
    )?.id;

    const sortedData = useMemo(() => getSortedData(data || [], sort), [data, sort]);

    const handleClickEdit = useCallback((contract: IContract | string) => {
        setContractDialog({contract_id: typeof contract === "string" ? contract : contract.id, mode: "EDIT"});
    }, []);

    const handleClickView = useCallback((contract: IContract | string) => {
        setContractDialog({contract_id: typeof contract === "string" ? contract : contract.id, mode: "READ"});
    }, []);

    const handleClickDelete = async (contract: IContract) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Selected Item?",
            justifyButtons: "flex-start",
            okButtonText: "Delete",
            form: (
                <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" flexDirection="row" gap={2} marginBottom={1}>
                        {!props.isClientMsp && (
                            <AvatarComponentV2
                                size={48}
                                user={(contract?.company || {}) as any}
                                isCompany
                                showProfileType
                                profileTypeMinimized
                                disableLink
                                isMSPClientPage={subdomainObj.isMSPClientPage}
                                isRedirectEnabled={false}
                            />
                        )}
                        <Box display="flex" flexDirection="column" justifyContent="space-around">
                            <Typography fontInter variant="body3" fontWeight={500} color={theme.palette.blue[800]}>
                                {contract?.client_product?.name || contract?.product?.name}
                            </Typography>
                            <Typography fontInter variant="subtitle3" color={theme.palette.blue[600]}>
                                {contract?.client_vendor?.name || contract?.company?.name}
                            </Typography>
                        </Box>
                    </Box>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {PERMANENT_ACTION_CONFIRMATION}
                    </Typography>
                </Box>
            ),
        });
        if (answer.value) {
            updateMspContracts.mutate({
                mutate_type: MutateTypes.DeleteMSPContract,
                company_id: props.companyId,
                contract_id: contract.id,
                onSuccess: () => {
                    invalidateMatchedQueries(["contracts-kpis", activeCompany?.id]);
                },
            });
        }
    };

    const ProductNameColumn = useCallback(({row}) => {
        const isProductContract = row.original?.contract_type?.name === "Product Contract";
        const name = row.original.name;
        return isProductContract ? (
            <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                <Typography
                    fontInter
                    variant="subtitle3"
                    color={theme.palette.blue[600]}
                    sx={{cursor: "pointer"}}
                    onClick={() => handleClickView(row.original)}>
                    {name}
                </Typography>
            </Box>
        ) : (
            "--"
        );
    }, []);

    const ParentNameColumn = useCallback(({row}) => {
        const isProductContract = row.original?.contract_type?.name === "Product Contract";
        return (
            <Typography
                fontInter
                variant="subtitle3"
                color={theme.palette.blue[600]}
                sx={{cursor: "pointer"}}
                onClick={() => handleClickView(row.original.parent_id ?? row.original.id)}>
                {isProductContract ? row.original?.parent?.name : row.original?.name}
            </Typography>
        );
    }, []);

    const calculateGrowthOrDecline = useCallback(
        (rowData: IContract) => {
            if (!rowData?.currency?.key || !rowData?.exchange_rate) {
                return null;
            }

            const currentRate =
                (rowData?.currency?.key
                    ? exchangeRates?.exchange_rates?.[0]?.rates?.[rowData?.currency?.key]?.rate
                    : 1) ?? 1;
            const previousRate = rowData.exchange_rate;

            if (Math.abs(currentRate - previousRate) <= 0.001) {
                return null;
            }

            if (currentRate > previousRate) {
                const percentChange = (currentRate / previousRate - 1) * 100;
                return (
                    <Typography
                        size={14}
                        weight={700}
                        color={theme.palette.blue[400]}
                        sx={{display: "flex", alignItems: "center"}}>
                        <ArrowUpward sx={{color: "blue.400", height: "14px", width: "14px"}} />
                        {percentChange.toFixed(2)}%
                    </Typography>
                );
            } else {
                const percentChange = (1 - currentRate / previousRate) * 100;
                return (
                    <Typography
                        size={14}
                        weight={700}
                        color={theme.palette.blue[400]}
                        sx={{display: "flex", alignItems: "center"}}>
                        <ArrowDownward sx={{color: "blue.400", height: "14px", width: "14px"}} />
                        {percentChange.toFixed(2)}%
                    </Typography>
                );
            }
        },
        [exchangeRates, theme.palette.blue[400]],
    );

    const getRate = (rowData: IContract): number => {
        return (
            (rowData?.currency?.key ? exchangeRates?.exchange_rates?.[0]?.rates?.[rowData?.currency?.key]?.rate : 1) ??
            1
        );
    };

    const columns: Column<any>[] = useMemo(
        () =>
            [
                {
                    Header: "",
                    accessor: "product.company.avatar",
                    Cell: ({row}) => (
                        <Box sx={{height: 40, overflow: "none", display: "flex", gap: 1, alignItems: "center"}}>
                            {row.original?.custom_properties?.outdated_exchange_rate && (
                                <Box
                                    tooltip={{
                                        title: (
                                            <Box sx={{display: "flex", flexDirection: "column"}}>
                                                <Typography size={14} weight={700} color={theme.palette.neutral[200]}>
                                                    Outdated Exchange Rate
                                                </Typography>
                                                <Typography size={14} weight={400} color={theme.palette.neutral[200]}>
                                                    Please update this contract
                                                </Typography>
                                            </Box>
                                        ),
                                        placement: "bottom",
                                    }}>
                                    <WarningAmberOutlined sx={{color: "warning.600"}} />
                                </Box>
                            )}
                            <AvatarComponentV2
                                size={36}
                                user={row.original?.company as any}
                                isCompany
                                showProfileType
                                profileTypeMinimized
                                disableLink
                            />
                        </Box>
                    ),
                },
                {
                    Header: "Product",
                    accessor: "name",
                    Cell: ProductNameColumn,
                    sortable: true,
                },
                {
                    Header: "",
                    accessor: "attachments_count",
                    Cell: ({row}) => {
                        return (
                            <Typography
                                fontInter
                                variant="body4"
                                whiteSpace="nowrap"
                                color={theme.palette.neutral[800]}>
                                {row.original.attachments_count ? (
                                    <AttachFileOutlined
                                        sx={{height: 24, width: 24}}
                                        htmlColor={theme.palette.neutral[500]}
                                    />
                                ) : (
                                    "--"
                                )}
                            </Typography>
                        );
                    },
                },
                ...(isPlaidEnabled && !clientMspHasNoExpensesAccess
                    ? [
                          {
                              Header: "Sync",
                              accessor: "link_plaid_account",
                              Cell: ({row}) => {
                                  if (!!row.original.link_plaid_account)
                                      return (
                                          <Box title={row.original.link_plaid_account?.name}>
                                              <ExpenseAccountAvatar
                                                  name={row.original.link_plaid_account?.name}
                                                  logoUrl={row.original.link_plaid_account?.institution_logo}
                                              />
                                          </Box>
                                      );
                                  if (!hasEditAccess) return "--";
                                  return (
                                      <Box
                                          sx={{cursor: "pointer", paddingRight: 2}}
                                          display="flex"
                                          justifyContent="center"
                                          title="Sync Expense"
                                          onClick={() => setLinkedExpenseDrawerContract(row.original)}>
                                          <AddCircleOutlineOutlinedIcon
                                              sx={{height: 24, width: 24}}
                                              htmlColor={theme.palette.blue[600]}
                                          />
                                      </Box>
                                  );
                              },
                              sortable: true,
                          },
                      ]
                    : []),
                {
                    Header: "Contract Name",
                    accessor: "parent_id",
                    Cell: ParentNameColumn,
                },
                ...(props.isClientMsp
                    ? [
                          {
                              Header: "Vendor",
                              accessor: "client_vendor.name",
                              Cell: ({row}) => {
                                  const name = row.original?.client_vendor?.name ?? "";
                                  return (
                                      <Typography
                                          fontInter
                                          variant="body4"
                                          whiteSpace="nowrap"
                                          color={theme.palette.neutral[800]}>
                                          {name || "--"}
                                      </Typography>
                                  );
                              },
                              sortable: true,
                          },
                      ]
                    : []),
                {
                    Header: "Total Cost",
                    accessor: "cost_after_discount",
                    Cell: ({row}) => {
                        return (
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                }}>
                                {row.original?.has_fixed_rate || row.original?.currency?.id !== companyCurrency?.id ? (
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: "column",
                                        }}>
                                        <Typography
                                            size={14}
                                            weight={400}
                                            color={theme.palette.neutral[800]}
                                            sx={{
                                                whiteSpace: "nowrap",
                                            }}>
                                            {companyCurrency?.symbol}
                                            {row.original?.has_fixed_rate && row.original?.exchange_rate !== 1
                                                ? formatCurrency(
                                                      row.original?.cost_after_discount /
                                                          (row.original?.exchange_rate || 1),
                                                  )
                                                : formatCurrency(
                                                      convertCurrency(
                                                          row.original?.cost_after_discount,
                                                          row.original?.currency?.key || "",
                                                          true,
                                                      ),
                                                      row.original.currency?.key,
                                                  )}{" "}
                                            {companyCurrency?.key}
                                        </Typography>
                                        {!!row.original?.cost_after_discount && (
                                            <Typography
                                                size={12}
                                                weight={500}
                                                color={theme.palette.neutral[600]}
                                                sx={{
                                                    whiteSpace: "nowrap",
                                                }}>
                                                {row.original?.currency?.symbol}
                                                {formatCurrency(row.original?.cost_after_discount || 0)}{" "}
                                                {row.original?.currency?.key}
                                            </Typography>
                                        )}
                                    </Box>
                                ) : (
                                    <Typography
                                        size={14}
                                        weight={400}
                                        color={theme.palette.neutral[800]}
                                        sx={{
                                            whiteSpace: "nowrap",
                                        }}>
                                        {companyCurrency?.symbol}
                                        {formatCurrency(row.original?.cost_after_discount || 0)} {companyCurrency?.key}
                                    </Typography>
                                )}
                                {row.original?.has_fixed_rate && row.original?.exchange_rate !== 1 && (
                                    <Box
                                        tooltip={{
                                            maxWidth: 300,
                                            title: (
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        flexDirection: "column",
                                                        gap: 1,
                                                    }}>
                                                    <Typography
                                                        size={14}
                                                        weight={700}
                                                        color={theme.palette.neutral[200]}>
                                                        Fixed Exchange Rate
                                                    </Typography>
                                                    <Typography
                                                        size={14}
                                                        weight={400}
                                                        color={theme.palette.neutral[200]}>
                                                        {companyCurrency?.symbol}1 {companyCurrency?.key} ={" "}
                                                        {row.original?.currency?.symbol}
                                                        {row.original?.exchange_rate} {row.original?.currency?.key} (
                                                        {formatDate(
                                                            row.original?.custom_properties?.last_fixed_rate_update ??
                                                                "2025-03-21T00:00:00Z",
                                                            DATE_FULL_SHORT_MONTH,
                                                        )}
                                                        )
                                                    </Typography>
                                                    <Typography
                                                        size={14}
                                                        weight={400}
                                                        color={theme.palette.neutral[200]}
                                                        sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                                                        {companyCurrency?.symbol}1 {companyCurrency?.key} ={" "}
                                                        {row.original?.currency?.symbol}
                                                        {Math.round(getRate(row.original) * 100) / 100}{" "}
                                                        {row.original?.currency?.key} (Today){" "}
                                                        {calculateGrowthOrDecline(row.original)}
                                                    </Typography>
                                                </Box>
                                            ),
                                        }}>
                                        <InfoOutlined sx={{width: "14px", height: "14px", color: "blue.600"}} />
                                    </Box>
                                )}
                            </Box>
                        );
                    },
                    sortable: true,
                },
                {
                    Header: "Amount",
                    accessor: "amount",
                    Cell: ({row}) => {
                        return (
                            <Typography
                                fontInter
                                variant="body4"
                                whiteSpace="nowrap"
                                color={theme.palette.neutral[800]}>
                                {row.original.contract_billing_type_id
                                    ? row.original?.custom_properties?.amount_purchased !== undefined &&
                                      row.original.custom_properties?.unit_cost
                                        ? `${row.original.custom_properties.amount_purchased || 1} ${
                                              row.original.custom_properties?.unit_cost
                                                  ? `(${row.original?.currency?.symbol}${row.original.custom_properties?.unit_cost})`
                                                  : ""
                                          }`
                                        : "--"
                                    : "--"}
                            </Typography>
                        );
                    },
                    sortable: true,
                },
                {
                    Header: "Billing Type",
                    accessor: "contract_billing_type.name",
                    Cell: ({row}) => (
                        <Typography fontInter variant="body4" color={theme.palette.neutral[800]}>
                            {row.original?.contract_billing_type?.name || "--"}
                            {row.original?.custom_properties?.per
                                ? " " + PerOptions[row.original.custom_properties.per]
                                : ""}
                        </Typography>
                    ),
                    sortable: true,
                },
                ...(props.includeSubcategoryColumn
                    ? [
                          {
                              Header: "Subcategory",
                              accessor: "category.name",
                              Cell: ({row}) => (
                                  <Typography fontInter variant="body4" color={theme.palette.neutral[800]}>
                                      {row.original?.category?.name || "--"}
                                  </Typography>
                              ),
                              sortable: true,
                          },
                      ]
                    : []),
                {
                    Header: "Billed Every",
                    accessor: "recurrence",
                    Cell: ({row}) => (
                        <Typography fontInter variant="body4" color={theme.palette.neutral[800]}>
                            {row.original.billing_frequency
                                ? frequencyNames[`${row.original.billing_frequency}`]
                                : "--"}
                        </Typography>
                    ),
                    sortable: true,
                },
                {
                    Header: "Next Payment",
                    accessor: "contract_next_payment_dates",
                    Cell: ({row}) => {
                        const nextPayment = row.original.contract_next_payment_dates[0] || undefined;
                        return (
                            <Typography fontInter variant="body4" marginRight={1} color={theme.palette.neutral[800]}>
                                {nextPayment
                                    ? formatDate(nextPayment?.date?.replace("Z", "") || "", DATE_FULL_SHORT_MONTH)
                                    : "--"}
                            </Typography>
                        );
                    },
                    sortable: true,
                },
                {
                    Header: "Renewal Date",
                    accessor: "end_date",
                    Cell: ({row}) => {
                        const foundNotification = row.original?.notifications?.find(
                            n => n.contract_notification_type_id === renewalContractNotificationTypeId,
                        );
                        const notificationEnabled = foundNotification?.enabled && foundNotification?.recipients?.length;
                        return (
                            <Box display="flex" flexDirection="row" alignItems="center">
                                <Typography
                                    fontInter
                                    variant="body4"
                                    marginRight={1}
                                    color={theme.palette.neutral[800]}>
                                    {row.original?.end_date
                                        ? formatDate(
                                              row.original?.end_date?.replace("Z", "") || "",
                                              DATE_FULL_SHORT_MONTH,
                                          )
                                        : "--"}
                                </Typography>
                                <div title={notificationEnabled ? "Reminders enabled" : "Reminders disabled"}>
                                    {notificationEnabled ? (
                                        <NotificationsActiveOutlinedIcon htmlColor={theme.palette.blue[600]} />
                                    ) : (
                                        <NotificationsOffOutlinedIcon htmlColor={theme.palette.neutral[500]} />
                                    )}
                                </div>
                            </Box>
                        );
                    },
                    sortable: true,
                },
                {
                    Header: "Notice Date",
                    accessor: "notice_date",
                    Cell: ({row}) => {
                        const foundNotification = row.original?.notifications?.find(
                            n => n.contract_notification_type_id === noticeContractNotificationTypeId,
                        );
                        const notificationEnabled = foundNotification?.enabled && foundNotification?.recipients?.length;
                        return (
                            <Box display="flex" flexDirection="row" alignItems="center">
                                <Typography
                                    fontInter
                                    variant="body4"
                                    marginRight={1}
                                    color={theme.palette.neutral[800]}>
                                    {row.original?.notice_date
                                        ? formatDate(
                                              row.original?.notice_date.replace("Z", "") || "",
                                              DATE_FULL_SHORT_MONTH,
                                          )
                                        : "--"}
                                </Typography>
                                <div title={notificationEnabled ? "Reminders enabled" : "Reminders disabled"}>
                                    {notificationEnabled ? (
                                        <NotificationsActiveOutlinedIcon htmlColor={theme.palette.blue[600]} />
                                    ) : (
                                        <NotificationsOffOutlinedIcon htmlColor={theme.palette.neutral[500]} />
                                    )}
                                </div>
                            </Box>
                        );
                    },
                    sortable: true,
                },
                {
                    Header: "Auto Renew",
                    accessor: "auto_renew",
                    Cell: ({row}) => (
                        <Box display="flex" flexDirection="row" alignItems="center">
                            <Typography fontInter variant="body4" marginRight={1} color={theme.palette.neutral[800]}>
                                {row.original.auto_renew ? "Yes" : "No"}
                            </Typography>
                        </Box>
                    ),
                    sortable: true,
                },
                {
                    Header: "",
                    accessor: "edit",
                    Cell: ({row}) => {
                        const isAggregator = row.original?.contract_type?.key === CONTRACT_TYPE_KEYS.CONTRACT;
                        const hasProductContracts = !!row.original?.product_contracts?.length;
                        return (
                            <MenuButton
                                id={`more-actions-${row.original.id}`}
                                buttonVariant="iconButton"
                                buttonProps={{
                                    sx: {
                                        width: "24px",
                                        minWidth: "24px!important",
                                        height: "24px",
                                        boxShadow: "none",
                                        background: "none",
                                    },
                                }}
                                items={[
                                    {
                                        id: "view",
                                        children: (
                                            <>
                                                <VisibilityOutlinedIcon /> View
                                            </>
                                        ),
                                        onClick: () => handleClickView(row.original),
                                    },
                                    {
                                        id: "edit",
                                        children: (
                                            <>
                                                <EditOutlinedIcon /> Edit
                                            </>
                                        ),
                                        onClick: () => handleClickEdit(row.original),
                                    },
                                    ...(isAggregator && hasProductContracts
                                        ? []
                                        : [
                                              {
                                                  id: "delete",
                                                  children: (
                                                      <>
                                                          <DeleteOutlineOutlinedIcon color="error" />
                                                          <Typography variant="body4" color="error">
                                                              Delete
                                                          </Typography>
                                                      </>
                                                  ),
                                                  onClick: () => handleClickDelete(row.original),
                                              },
                                          ]),
                                ].filter(item => (hasEditAccess ? true : item.id === "view"))}
                                aria-label="settings">
                                {isUpdating[MutateTypes.DeleteMSPContract + row.original.id] ? (
                                    <Loader inline loading />
                                ) : (
                                    <MoreVert htmlColor={theme.palette.blue[600]} />
                                )}
                            </MenuButton>
                        );
                    },
                },
            ].filter(col => (props.isClientMsp && col.accessor === "product.company.avatar" ? false : true)),
        [handleClickEdit, handleClickView, props.isClientMsp, isUpdating, frequencyNames, PerOptions, companyCurrency],
    );

    const Table = useMemo(
        () => (
            <TableV2Component
                id={"list-view" + sortedData?.[0]?.category_id}
                customData={sortedData}
                headerBackground={theme.palette.blue[100]}
                columns={columns}
                isLoading={isLoading}
                sortType={sort.sort}
                orderBy={sort.order_by as keyof IContract}
                onSortChange={(sort: CPSortType.ASC | CPSortType.DESC, order_by) => {
                    setSort({sort, order_by: order_by as string});
                }}
                conditionTdStyle={[
                    {backgroundColor: "#fef9cd"},
                    row =>
                        (row.original.expire_in_days != null && row.original.expire_in_days <= 30) ||
                        row.original?.custom_properties?.outdated_exchange_rate,
                ]}
                showTooltip={[
                    row => {
                        const absoluteValue = Math.abs(row.original.expire_in_days);
                        return row.original.expire_in_days < 0
                            ? `Your contract has been expired for ${absoluteValue} ${pluralizeString(
                                  "day",
                                  absoluteValue,
                              )}`
                            : `This contract will expire in ${row.original.expire_in_days} ${pluralizeString(
                                  "day",
                                  row.original.expire_in_days,
                              )}`;
                    },
                    row => row.original.expire_in_days != null && row.original.expire_in_days <= 30,
                    [0, -95],
                ]}
                pinColumns={["edit"]}
            />
        ),
        [columns, sortedData, isLoading, sort],
    );

    const [totalsWidth, setTotalsWidth] = useState(0);

    const tableTotals = useMemo(() => {
        const totals = {
            totalContractCost: 0,
            totalUnitCost: 0,
            totalAmount: 0,
        };
        sortedData?.forEach(contract => {
            totals.totalContractCost +=
                contract?.has_fixed_rate && contract?.exchange_rate !== 1
                    ? contract?.cost_after_discount / (contract?.exchange_rate || 1)
                    : contract?.cost_after_discount /
                      (exchangeRates.exchange_rates?.[0]?.rates[contract?.currency?.key]?.rate || 1);
            totals.totalUnitCost +=
                Number(contract.cost_after_discount / contract.custom_properties?.amount_purchased) || 0;
            totals.totalAmount += Number(contract.custom_properties?.amount_purchased) || 0;
        });
        return totals;
    }, [sortedData, exchangeRates, isLoading]);

    useEffect(() => {
        const headers = document.getElementById("list-view" + sortedData?.[0]?.category_id)?.getElementsByTagName("th");
        // calculate the total width of the first 5 headers so we can set the total properly
        let totalWidth = 0;
        for (let i = 0; i < 5; i++) {
            totalWidth += headers?.[i]?.clientWidth || 0;
        }
        setTotalsWidth(totalWidth);
    }, [sortedData]);

    return (
        <ErrorBoundary>
            {Table}
            <Box
                sx={{
                    "table th:first-child, table td:first-child": {
                        width: `${totalsWidth + 8}px`,
                        paddingLeft: "16px",
                    },
                    "table th:nth-child(2), table td:nth-child(2)": {
                        width: "auto",
                    },
                }}>
                {!hideTotals && (
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <Typography
                                        sx={{
                                            color: "neutral.800",
                                            fontWeight: "700 !important",
                                        }}>
                                        Total
                                    </Typography>
                                </td>
                                <td>
                                    <Typography
                                        variant="body3"
                                        fontInter
                                        sx={{
                                            color: "neutral.800",
                                            fontWeight: "700 !important",
                                            width: 300,
                                        }}>
                                        {companyCurrency?.symbol}
                                        {formatCurrency(tableTotals.totalContractCost)} {companyCurrency?.key}
                                    </Typography>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                )}
            </Box>
            {!!contractDialog ? (
                <ViewEditContractDrawer
                    open={!!contractDialog?.contract_id}
                    onClose={() => setContractDialog(undefined)}
                    contract_id={contractDialog?.contract_id}
                    companyId={props.companyId}
                    mode={contractDialog?.mode}
                    isClientMsp={props.isClientMsp}
                />
            ) : null}
            {!!linkedExpenseDrawerContract && (
                <LinkExpenseDrawer
                    open
                    onClose={() => setLinkedExpenseDrawerContract(undefined)}
                    contract={linkedExpenseDrawerContract}
                />
            )}
        </ErrorBoundary>
    );
};

export default YourSubscriptionsListViewTable;
