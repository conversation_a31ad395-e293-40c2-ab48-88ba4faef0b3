import {CampaignOutlined, StoreOutlined} from "@mui/icons-material";
import SendOutlined from "@mui/icons-material/SendOutlined";
import {useTheme} from "@mui/material";
import {useState} from "react";
import {useNavigate} from "react-router-dom";
import routeConfig from "../../../../constants/routeConfig";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import useCompanyProfile from "../../../../hooks/useCompanyProfile";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import Box from "../../../Atoms/Box/Box.component";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import MassMessagingModal from "./massMessaging.modal";

export default function MassMessaging() {
    const [showMassMessageModal, setShowMassmessageModal] = useState(false);
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const friendly_url = activeCompany?.friendly_url;
    const {hasPermissions} = usePermissions();
    const hasMassMessagePermissions = hasPermissions([PERMISSION_GROUPS.MASS_MESSAGING_UPDATE]);
    const {companyInfo} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.MASS_MESSAGING_UPDATE],
            read: [PERMISSION_GROUPS.MASS_MESSAGING_READ],
        },
        companyFriendlyUrl: friendly_url || "",
    });
    const subdomain = companyInfo?.subdomain;
    const navigate = useNavigate();

    return (
        <>
            <Box
                sx={{
                    background: theme.palette.neutral[100],
                    border: `1px solid ${theme.palette.neutral[300]}`,
                    borderRadius: "4px",
                    padding: "16px",
                    display: "flex",
                    flexDirection: "column",
                    gap: "24px",
                }}>
                <PageInnerHeader
                    id="mass-message"
                    actions={{
                        primary:
                            subdomain && companyInfo?.friendly_url
                                ? {
                                      id: "mass-message-view-portal",
                                      children: (
                                          <>
                                              <StoreOutlined width={12} htmlColor="white" /> View Portal
                                          </>
                                      ),
                                      to: `${window.location.protocol}//${subdomain || ""}.${
                                          window.location.host
                                      }${routeConfig.VendorPortal.path.replace(
                                          ":friendly_url",
                                          companyInfo?.friendly_url ?? "",
                                      )}`,
                                  }
                                : undefined,
                    }}
                    title={{
                        text: "My Channel",
                    }}
                />
                {hasMassMessagePermissions && (
                    <AlertBanner
                        id="mass-message-alert"
                        title={{
                            text: "Mass Messaging",
                            sx: {
                                fontWeight: "500 !important",
                            },
                        }}
                        subTitle={{
                            text: "Have an important message you would like to share with all partners?",
                        }}
                        icon={<CampaignOutlined htmlColor={theme.palette.secondary[600]} />}
                        button={{
                            text: (
                                <>
                                    <SendOutlined fontSize="small" color="secondary" /> Send Message
                                </>
                            ),
                            onClick: () => navigate("/company-portal/mass-messaging"),
                            sx: {
                                display: "flex",
                                alignItems: "center",
                                gap: "4px",
                                "& *, &": {
                                    fontSize: "14px !important",
                                },
                            },
                        }}
                        variant="info"
                    />
                )}
            </Box>
            <MassMessagingModal modalState={[showMassMessageModal, setShowMassmessageModal]} />
        </>
    );
}
