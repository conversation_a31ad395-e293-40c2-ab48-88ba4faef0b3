import {useTheme} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IReviewForUserType} from "../../../../Interfaces/reviews.interface";
import ClaimerReviewNotifications from "../../../../components/Admin/CompanyPortal/ClaimerReviewNotifications.component";
import {DEFAULT_QUERY_CONFIGS} from "../../../../constants/commonStrings.constant";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import useCompanyProfile from "../../../../hooks/useCompanyProfile";
import useNotification from "../../../../hooks/useNotification";
import {companyService} from "../../../../services/company.service";
import {reviewService} from "../../../../services/review.service";
import {downloadDoc} from "../../../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../../../utils/error.util";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";

export default function ProductReviews() {
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const friendly_url = activeCompany?.friendly_url;
    const notify = useNotification();
    const {companyInfo} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.ADMIN_VENDOR_PRODUCT_REVIEWS_UPDATE],
            read: [],
        },
        companyFriendlyUrl: friendly_url || "",
        companyParams: ["products"],
    });

    const reviewsQuery = useQuery<IReviewForUserType[]>(
        ["productReviews", companyInfo?.id],
        () => {
            return reviewService.getReviewsByCompany(companyInfo?.id!).then(r => r.data);
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!companyInfo?.id,
        },
    );
    const reviews = reviewsQuery.data || [];

    const handleDownloadCSV = () => {
        notify("CSV file is being generated, the download will start soon.", "Success");
        companyService.getProductReviewCSV(
            companyInfo?.id || "",
            response => {
                downloadDoc(response.data, `${companyInfo?.subdomain}_products_reviews.csv`);
            },
            err => {
                notify(getErrorFromArray(err), "Error");
            },
        );
    };

    return (
        <Box
            sx={{
                background: theme.palette.neutral[100],
                border: `1px solid ${theme.palette.neutral[300]}`,
                borderRadius: "4px",
                padding: "16px",
                display: "flex",
                flexDirection: "column",
                gap: "24px",
            }}>
            <PageInnerHeader
                id="product-reviews-header"
                title={{
                    text: "Product Reviews",
                }}
                indicator={reviews?.length > 0 ? reviews?.length : undefined}
                actions={{
                    primary:
                        reviews?.length > 0
                            ? {
                                  id: "export-reviews",
                                  children: "Export Reviews",
                                  onClick: handleDownloadCSV,
                                  permissions: [PERMISSION_GROUPS.DATA_EXPORT],
                              }
                            : undefined,
                }}
            />
            {!!reviews?.length ? (
                <ClaimerReviewNotifications
                    isLoading={reviewsQuery.isLoading}
                    notifications={reviews.filter(r => companyInfo?.products?.find(p => p.id === r.model.id))}
                />
            ) : (
                <Typography variant="body3" fontInter>
                    No reviews found
                </Typography>
            )}
        </Box>
    );
}
