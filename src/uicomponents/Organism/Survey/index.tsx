import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import {useTheme} from "@mui/material";
import Box from "@mui/material/Box";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {
    IQuestionAnswerOptions,
    IQuestionAnswers,
    IQuestionOptions,
    IQuestions,
} from "../../../Interfaces/questions.interface";
import {ROUTE_CLASS, ROUTE_CONTAINER_CLASS} from "../../../constants/commonStrings.constant";
import {REQUIRED_FIELD} from "../../../constants/errorMessages.constant";
import {QuestionFormTypeEnum} from "../../../enums/questionFormTypes.enum";
import {QuestionTypeEnum} from "../../../enums/questionType.enum";
import useNotification from "../../../hooks/useNotification";
import {questionService} from "../../../services/questions.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import Button from "../../Atoms/Button/Button.Component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import AutocompleteComponent from "../../Molecules/Autocomplete/Autocomplete.component";
import CheckboxWithLabel from "../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import HeadingComponent from "../../Typography/Heading.component";
import LabelComponent from "../../Typography/Label.component";
import ParagraphComponent from "../../Typography/Paragraph.component";

interface IProps {
    surveyType: QuestionFormTypeEnum;
    headerTitle: string;
    descriptionLine1?: string;
    descriptionLine2?: string;
}

export default function Survey(props: IProps) {
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [showThankYou, setShowThankYou] = useState(false);
    const [questions, setQuestions] = useState<IQuestions[]>([]);
    const [errors, setErrors] = useState<string[]>([]);
    const [multipleChoice, setMultipleChoice] = useState<any>({});
    const [showSingleAnswerTextbox, setSingleAnswerTextbox] = useState(false);
    const [singleAnswerForQuestion, setSingleAnswerForQuestion] = useState<string[]>([]);

    const methods = useForm();
    const notify = useNotification();

    const theme = useTheme();

    useEffect(() => {
        questionService.getQuestions(props.surveyType, handleLoadQuestions, handleError);
    }, []);

    const handleLoadQuestions = (res: AxiosResponse) => {
        const ordered = res.data.sort((a: any, b: any) => a.question_order - b.question_order);
        setQuestions(ordered);
        setLoading(false);
    };

    const validateRequiredCheckboxFields = () => {
        const errors: string[] = [];
        questions.forEach(async (question: IQuestions) => {
            if (question.question_type === QuestionTypeEnum.MultipleAnswer && question.is_required === true) {
                const optionsChosen: string[] = [];
                Object.keys(multipleChoice).map(function (keyName) {
                    if (keyName.startsWith(question.id + "|") && multipleChoice[keyName] === true) {
                        const optionId: string = keyName.split("|")[1];
                        optionsChosen.push(optionId);
                    }
                });

                if (optionsChosen.length === 0) {
                    errors.push(question.id);
                }
            }
        });

        if (errors.length > 0) {
            // some required fields have not been filled in
            setErrors(errors);
            setSaving(false);
            return true;
        }

        return false;
    };

    const handleSave = async () => {
        setSaving(true);

        // validate required checkboxes are filled in
        const errorsHappened: boolean = validateRequiredCheckboxFields();

        if (errorsHappened) {
            // some required fields have not been filled in
            setSaving(false);
            return;
        }

        const currentValues = methods.getValues();

        let count = 0;
        await new Promise<void>(resolve => {
            questions.forEach(async (question: IQuestions) => {
                try {
                    if (currentValues[question.id]) {
                        const option: IQuestionAnswerOptions = {answer: "", option_id: ""};
                        const options: IQuestionAnswers = {answer: "", question_option_ids: []};

                        if (question.question_type === QuestionTypeEnum.SingleAnswer) {
                            option.option_id = currentValues[question.id];
                            if (singleAnswerForQuestion.includes(question.id)) {
                                option.answer = currentValues[question.id + "answer"];
                            }

                            options.question_option_ids.push(option);

                            await questionService.saveQuestionAnswer(question.id, options);
                        } else if (question.question_type === QuestionTypeEnum.SingleLineText) {
                            options.answer = currentValues[question.id];

                            await questionService.saveQuestionAnswer(question.id, options);
                        }
                    } else if (question.question_type === QuestionTypeEnum.MultipleAnswer) {
                        const options: IQuestionAnswers = {answer: "", question_option_ids: []};

                        Object.keys(multipleChoice).map(function (keyName) {
                            if (keyName.startsWith(question.id + "|") && multipleChoice[keyName] === true) {
                                const option: IQuestionAnswerOptions = {answer: "", option_id: ""};
                                const optionId: string = keyName.split("|")[1];

                                option.answer = currentValues[optionId + "answer"];
                                option.option_id = optionId;
                                options.question_option_ids.push(option);
                            }
                        });

                        await questionService.saveQuestionAnswer(question.id, options);
                    }
                } catch (e) {
                    // hiding from user for now
                    console.error(e);
                } finally {
                    // resolve this promise so we can show success screen when all answers have been saved.
                    count += 1;
                    if (count === questions.length) {
                        resolve();
                    }
                }
            });
        }).then(() => {
            handleSuccess();
        });
    };

    const handleSuccess = () => {
        setSaving(false);
        setShowThankYou(true);
        window.scrollTo(0, 0);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
        setSaving(false);
    };

    const makeOptionsArray = (options: IQuestionOptions[]) => {
        const optionArray: any = [];

        options.map((option: IQuestionOptions) => {
            optionArray.push({id: option.id, label: option.display_value});
        });

        return optionArray;
    };

    const multipleChoiceChanged = (e: any, option, question) => {
        // used for the checkbox controller to know what is checked
        const test: string = question.id + "|" + option.id;

        setMultipleChoice({
            ...multipleChoice,
            [test]: e.target.checked,
        });
    };

    const dropdownChanged = (questionId: string, value: any) => {
        if (questionId) {
            if (value) {
                const q = questions.filter(item => item.id === questionId);
                const option = q[0].options.filter(o => value === o.id);

                if (option[0].show_answer_option) {
                    setSingleAnswerTextbox(true);
                    setSingleAnswerForQuestion([...singleAnswerForQuestion, questionId]);
                } else {
                    const newQs = singleAnswerForQuestion.filter(function (q) {
                        return q !== questionId;
                    });

                    setSingleAnswerForQuestion(newQs);

                    setSingleAnswerTextbox(newQs.length > 0);
                }
            }
        }
    };

    return (
        <>
            <div className={ROUTE_CLASS}>
                <div className={ROUTE_CONTAINER_CLASS}>
                    <HeadingComponent
                        text={props.headerTitle}
                        position="start"
                        noConnector
                        size={32}
                        color={theme.palette.primary["main"]}
                    />
                    {!showThankYou && (
                        <>
                            <ParagraphComponent text={props.descriptionLine1} position="start" size={"lg"} />
                            <ParagraphComponent text={props.descriptionLine2} position="start" size={18} />
                        </>
                    )}
                    {loading && (
                        <div className="d-flex align-items-center justify-content-center my-5 py-5">
                            <Loader loading big inline />
                        </div>
                    )}

                    {showThankYou ? (
                        <>
                            <ParagraphComponent
                                text="Thank you! Your responses have been received. We will be in touch."
                                position="start"
                                size={"lg"}
                            />
                        </>
                    ) : (
                        <FormProvider {...methods}>
                            <form onSubmit={methods.handleSubmit(handleSave)}>
                                <Box sx={{lineHeight: 4}}>
                                    {questions.map((question: IQuestions) => {
                                        switch (question.question_type) {
                                            case "MultipleAnswer":
                                                return (
                                                    <div key={question.id}>
                                                        <LabelComponent
                                                            text={`${question.question}${
                                                                question.is_required ? "*" : ""
                                                            }`}
                                                            position="start"
                                                            size={18}
                                                            sx={{marginTop: "26px!important"}}
                                                            uppercase={false}
                                                            color={
                                                                errors.filter(qId => qId === question.id).length > 0
                                                                    ? theme.palette.error[600]
                                                                    : ""
                                                            }
                                                        />
                                                        <Box
                                                            id={"checkboxes" + question.question_key}
                                                            component={"div"}
                                                            sx={{lineHeight: 1}}>
                                                            {question.options.map((option: IQuestionOptions) => {
                                                                return (
                                                                    <>
                                                                        <CheckboxWithLabel
                                                                            key={option.id}
                                                                            id={option.id}
                                                                            testid={`option-${option.display_value}`}
                                                                            name={question.id}
                                                                            checked={
                                                                                multipleChoice[
                                                                                    question.id + "|" + option.id
                                                                                ] || false
                                                                            }
                                                                            onChange={e => {
                                                                                multipleChoiceChanged(
                                                                                    e,
                                                                                    option,
                                                                                    question,
                                                                                );
                                                                            }}
                                                                            label={option.display_value}
                                                                            formControlSx={{
                                                                                minHeight: "50px",
                                                                            }}
                                                                        />
                                                                        {option.show_answer_option &&
                                                                            multipleChoice[
                                                                                question.id + "|" + option.id
                                                                            ] && (
                                                                                <>
                                                                                    <TextBoxComponent
                                                                                        id={option.id + "answer"}
                                                                                        isRequired={true}
                                                                                        validateOnTheFly={
                                                                                            question.is_required
                                                                                        }
                                                                                        label={option.display_value}
                                                                                        key={option.id + "answer"}
                                                                                    />
                                                                                </>
                                                                            )}
                                                                    </>
                                                                );
                                                            })}
                                                        </Box>
                                                        {errors.filter(qId => qId === question.id).length > 0 && (
                                                            <ParagraphComponent
                                                                text={REQUIRED_FIELD}
                                                                position="start"
                                                                size={12}
                                                                fontWeight={"500"}
                                                                color={theme.palette.error[600]}
                                                                sx={{lineHeight: "1!important"}}
                                                            />
                                                        )}
                                                    </div>
                                                );
                                            case "SingleAnswer":
                                                return (
                                                    <div className="d-flex align-items-center">
                                                        <AutocompleteComponent
                                                            options={makeOptionsArray(question.options)}
                                                            id={question.id}
                                                            sx={{width: "50%"}}
                                                            loading={!question.options.length}
                                                            isRequired={question.is_required}
                                                            size="small"
                                                            placeholder={question.question}
                                                            label={question.question}
                                                            validateOnTheFly
                                                            key={question.id}
                                                            helperText={isInvalid =>
                                                                isInvalid ? "This is required" : ""
                                                            }
                                                            onValueChange={(questionId, value) =>
                                                                dropdownChanged(questionId, value)
                                                            }
                                                        />
                                                        {showSingleAnswerTextbox &&
                                                            singleAnswerForQuestion.includes(question.id) && (
                                                                <TextBoxComponent
                                                                    id={question.id + "answer"}
                                                                    isRequired={true}
                                                                    validateOnTheFly={question.is_required}
                                                                    label={question.question}
                                                                    key={question.id + "answer"}
                                                                    className="ms-2"
                                                                />
                                                            )}
                                                    </div>
                                                );
                                            case "SingleLineText":
                                                return (
                                                    <TextBoxComponent
                                                        id={question.id}
                                                        isRequired={question.is_required}
                                                        validateOnTheFly={question.is_required}
                                                        label={question.question}
                                                        key={question.id}
                                                    />
                                                );
                                            default:
                                                return <></>;
                                        }
                                    })}

                                    {!loading && (
                                        <Button
                                            id="submitBtn"
                                            type="submit"
                                            loading={saving}
                                            variant="primaryButton"
                                            size="medium"
                                            sx={{marginTop: "25px"}}
                                            endIcon={<ArrowForwardOutlinedIcon sx={{color: "white"}} />}>
                                            {saving ? "Submitting" : "Submit form"}
                                        </Button>
                                    )}
                                </Box>
                            </form>
                        </FormProvider>
                    )}
                </div>
            </div>
        </>
    );
}
