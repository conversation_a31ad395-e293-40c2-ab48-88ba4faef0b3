import DeleteOutlined from "@mui/icons-material/DeleteOutlined";
import {useTheme} from "@mui/material";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {useCallback, useRef, useState} from "react";
import {Column} from "react-table";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {useDebounce} from "../../../hooks/useDebounce";
import {ICompanyInvite} from "../../../Interfaces/companyInvite.interface";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {companyService} from "../../../services/company.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {buildFilters, clearFilters} from "../../../utils/filters.util";
import {DATE_TIME_FORMAT, formatDate} from "../../../utils/formatDate";
import {pluralizeString} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Checkbox from "../../Atoms/Checkbox/Checkbox.component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import ResendInviteButton from "../../Molecules/ResendInviteButton/ResendInviteButton.component";
import RefreshIcon from "@mui/icons-material/Refresh";
import useNotification from "../../../hooks/useNotification";
import {getErrorFromArray} from "../../../utils/error.util";
import {SITE_NAME} from "../../../constants/siteFlags";
import {INewFilters} from "../../../Interfaces/filters.interface";
import {IObject} from "../../../models/ObjectAny.interface";
import BulkSelectContainer from "../../../components/Table/bulkSelectContainer.component";

export default function PendingInvitesTab() {
    const [deletingInvite, setDeletingInvite] = useState<Record<string, boolean>>({});
    const [search, setSearch] = useState<string>("");
    const [itemsPerPage, setItemsPerPage] = useState<number>(40);
    const [page, setPage] = useState<number>(1);
    const [selectedInvites, setSelectedInvites] = useState<string[]>([]);
    const [filterContents, setFilterContents] = useState<INewFilters>({sort: "sorting_email__ASC"});
    const [isBulkResending, setIsBulkResending] = useState(false);
    const [isBulkDeleting, setIsBulkDeleting] = useState(false);

    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const debouncedSearch = useDebounce(search, 500);
    const queryClient = useQueryClient();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.USER_MANAGEMENT_UPDATE]);
    const notify = useNotification();

    const invitesQuery = useQuery<IPaginationData<ICompanyInvite[]>>(
        ["companyInvites", JSON.stringify(filterContents), debouncedSearch, page, itemsPerPage, activeCompany?.id],
        () => {
            return new Promise(resolve => {
                const cleanedFilters = {...filterContents};
                if (cleanedFilters?.sent_at) {
                    delete cleanedFilters.sent_at;
                }
                companyService
                    .getInvites(activeCompany?.friendly_url!, {
                        page,
                        items_per_page: itemsPerPage,
                        dynamic: clearFilters({
                            ...cleanedFilters,
                            ...((filterContents?.sent_at as IObject)?.start_date &&
                            (filterContents?.sent_at as IObject)?.end_date
                                ? {
                                      start_date: (filterContents.sent_at as IObject).start_date,
                                      end_date: (filterContents.sent_at as IObject).end_date,
                                  }
                                : {}),
                        }),
                        search_word: debouncedSearch,
                    })
                    .then(res => {
                        queryClient.refetchQueries(["companyUsersCounts"]);
                        resolve(res.data);
                    });
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!activeCompany?.id},
    );
    const invitesFilterQuery = useQuery(
        ["companyInvitesFilters", activeCompany?.id],
        () => {
            return new Promise(resolve => {
                companyService.getInvitesFilters(activeCompany?.friendly_url!).then(res => {
                    const filtersCopy = {...res.data?.filters};
                    delete filtersCopy?.type;
                    resolve(buildFilters(filtersCopy, {}));
                });
            });
        },
        {...DEFAULT_QUERY_CONFIGS(), enabled: !!activeCompany?.id},
    );
    const invites = invitesQuery.data?.data || [];
    const metadata = invitesQuery.data?.meta || {total: 0, last_page: 0};
    const allInvitesIds = invites.map(invite => invite.id);
    const containerRef = useRef<HTMLDivElement | null>(null);

    const ActionsCell = useCallback(
        tableData => {
            return tableData?.row?.original ? (
                <Box sx={{display: "flex", justifyContent: "center", width: "100%"}}>
                    {!!deletingInvite[tableData.row.original.id] ? (
                        <Loader inline loading version="iconBlue" />
                    ) : (
                        <Box
                            id={"delete-invite-" + tableData.row.original?.id}
                            color="error"
                            onClick={() => handleDeleteInvite([tableData.row.original.id])}
                            tooltip={{title: "Revoke invite"}}
                            sx={{cursor: "pointer"}}>
                            <DeleteOutlined color="error" sx={{width: "24px", height: "24px"}} />
                        </Box>
                    )}
                </Box>
            ) : null;
        },
        [deletingInvite, invites],
    );

    const columns = [
        ...(hasEditAccess
            ? [
                  {
                      id: "select-invite",
                      accessor: "checkbox-col",
                      Header: () => (
                          <Checkbox
                              checked={
                                  !!allInvitesIds.length && allInvitesIds.every(inv => selectedInvites.includes(inv))
                              }
                              onChange={(_, checked) =>
                                  checked ? setSelectedInvites(allInvitesIds) : setSelectedInvites([])
                              }
                          />
                      ),
                      Cell: tableData => (
                          <Checkbox
                              checked={selectedInvites.includes(tableData.row.original.id)}
                              onChange={(_, checked) =>
                                  setSelectedInvites(
                                      checked
                                          ? [...selectedInvites, tableData.row.original.id]
                                          : selectedInvites.filter(inv => inv !== tableData.row.original.id),
                                  )
                              }
                          />
                      ),
                  },
              ]
            : []),
        {
            id: "email",
            Header: "Email",
            accessor: "email",
            defaultCanSort: true,
        },
        {
            id: "role",
            Header: "Role",
            accessor: "role.display_name",
            defaultCanSort: true,
        },
        {
            id: "updated_at",
            Header: "Sent on",
            accessor: "created_at",
            defaultCanSort: true,
            Cell: ({row}) =>
                row.original.last_sent_at || row.original.created_at
                    ? formatDate(row.original.last_sent_at || row.original.created_at, DATE_TIME_FORMAT)
                    : "--",
        },
        ...(hasEditAccess
            ? [
                  {
                      id: "resend",
                      Header: "Resend",
                      accessor: "invited_at",
                      Cell: ({row}) => (
                          <Box display="flex" alignItems="center" justifyContent="center">
                              <ResendInviteButton
                                  subject={`Join Us at ${activeCompany?.name}: Invitation to Our ${SITE_NAME}`}
                                  originalEmail={row.original.email}
                                  sentDates={[
                                      row.original.created_at,
                                      ...(row.original.last_sent_at &&
                                      row.original.last_sent_at !== row.original.created_at
                                          ? [row.original.last_sent_at]
                                          : []),
                                  ]}
                                  resendType="company-user"
                                  inviteId={row.original.id}
                              />
                          </Box>
                      ),
                      width: 50,
                  },
                  {
                      id: "remove",
                      accessor: "remove-col",
                      Header: <span style={{margin: "0 auto"}}>Remove</span>,
                      Cell: ActionsCell,
                      width: 50,
                  },
              ]
            : []),
    ];

    const handleSortChange = (sortBy: string, order_by: string) => {
        setFilterContents({...filterContents, sort: `sorting_${order_by}__${sortBy}`});
    };

    const handleDeleteInvite = async (inviteIds: string[]) => {
        const answer = await getUserConfirmation(
            <>
                Are you sure you want to revoke the selected {pluralizeString("invite", inviteIds.length)}?
                <Typography variant="body3" fontWeight={600}>
                    {inviteIds.map(
                        (id, idx) =>
                            invites.find(inv => inv.id === id)?.email + (idx < inviteIds.length - 1 ? ", " : ""),
                    )}
                </Typography>
                This action cannot to be undone.
            </>,
            {
                okButtonText: "REMOVE",
                modalTheme: "error",
                mui: true,
                title: `Remove ${pluralizeString("Invite", inviteIds.length)}`,
            },
        );
        if (!answer.value) return;
        if (inviteIds.length > 1) {
            setIsBulkDeleting(true);
        }
        setDeletingInvite(
            inviteIds.reduce((acc, id) => {
                acc[id] = true;
                return acc;
            }, {}),
        );
        companyService.deleteInvite(activeCompany?.friendly_url!, inviteIds).then(
            () => {
                setDeletingInvite(
                    inviteIds.reduce((acc, id) => {
                        acc[id] = false;
                        return acc;
                    }, {}),
                );
                setSelectedInvites([]);
                invitesQuery.refetch();
                setIsBulkDeleting(false);
                notify(`${pluralizeString("Invite", inviteIds.length)} deleted successfully`, "Success");
            },
            err => {
                notify(getErrorFromArray(err), "Error");
                setDeletingInvite(
                    inviteIds.reduce((acc, id) => {
                        acc[id] = false;
                        return acc;
                    }, {}),
                );
                setIsBulkDeleting(false);
            },
        );
    };

    const handleResendInvites = async (inviteIds: string[]) => {
        const answer = await getUserConfirmation(
            <>
                Are you sure you want to resend the selected {pluralizeString("invite", inviteIds.length)}?
                <Typography variant="body3" fontWeight={600}>
                    {inviteIds.map(
                        (id, idx) =>
                            invites.find(inv => inv.id === id)?.email + (idx < inviteIds.length - 1 ? ", " : ""),
                    )}
                </Typography>
            </>,
            {
                okButtonText: "RESEND",
                modalTheme: "blue",
                mui: true,
                title: `Resend ${pluralizeString("Invite", inviteIds.length)}`,
            },
        );
        if (answer.value) {
            setIsBulkResending(true);
            companyService.bulkResendCompanyInvite(
                activeCompany?.friendly_url || "",
                inviteIds,
                () => {
                    invitesQuery.refetch();
                    notify("Invites resent successfully", "Success");
                    setIsBulkResending(false);
                    setSelectedInvites([]);
                },
                err => {
                    notify(getErrorFromArray(err), "Error");
                    setIsBulkResending(false);
                },
            );
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                paddingTop: 3,
                gap: 2,
                "table th:first-child, table tr td:first-child": {
                    width: "50px",
                },
            }}
            ref={containerRef}>
            <PageInnerHeader
                id="active-users-tab-header"
                title={{text: "Pending Invites"}}
                searchState={[search, setSearch]}
                isSearching={invitesQuery.isFetching && !!search}
                growSearch
                filters={invitesFilterQuery.data}
                filterState={[filterContents, setFilterContents as (filters: INewFilters | undefined) => void]}
            />
            <TableV2Component
                id="companyUsersManagement-pending-invites"
                columns={columns as []}
                customData={invites}
                isLoading={invitesQuery.isFetching}
                headerBackground={theme.palette.blue[100]}
                noResultsMessage={
                    search
                        ? "No invites found based on your search or filter criteria. Please update your search or filter and try again."
                        : "No invites found."
                }
                onSortChange={(sort: string, order_by: string) => handleSortChange(sort, order_by)}
                orderBy="email"
                sortType="ASC"
            />
            <BulkSelectContainer
                selectedRows={selectedInvites}
                itemText="invite"
                containerRef={containerRef}
                children={
                    <>
                        <Button
                            id="resend-many"
                            type="button"
                            variant="outlined"
                            color="blue"
                            disabled={isBulkResending}
                            onClick={() => handleResendInvites(selectedInvites)}>
                            {isBulkResending ? (
                                <>
                                    <Loader inline loading version="iconBlue" />
                                    Resending...
                                </>
                            ) : (
                                <>
                                    <RefreshIcon
                                        sx={{marginRight: 1, height: 16, width: 16}}
                                        htmlColor={theme.palette.blue[800]}
                                    />
                                    Resend All
                                </>
                            )}
                        </Button>
                        <Button
                            id="delete-many"
                            type="button"
                            variant="outlined"
                            color="error"
                            disabled={isBulkDeleting}
                            onClick={() => handleDeleteInvite(selectedInvites)}>
                            {isBulkDeleting ? (
                                <>
                                    <Loader inline loading />
                                    Removing...
                                </>
                            ) : (
                                <>
                                    <DeleteOutlined
                                        color="error"
                                        sx={{marginRight: 1, width: "16px", height: "16px"}}
                                    />
                                    Remove All
                                </>
                            )}
                        </Button>
                    </>
                }
            />
            <CustomPagination
                itemsPerPage={itemsPerPage}
                metadata={metadata}
                page={page}
                setItemsPerPage={setItemsPerPage}
                setPage={setPage}
            />
        </Box>
    );
}
