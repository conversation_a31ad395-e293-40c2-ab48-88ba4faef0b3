import CancelIcon from "@mui/icons-material/Cancel";
import CheckIcon from "@mui/icons-material/Check";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import FlagIcon from "@mui/icons-material/Flag";
import FlagOutlinedIcon from "@mui/icons-material/FlagOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {useCallback, useMemo, useState} from "react";
import {Column} from "react-table";
import {ICompanyRejectReason, IUserRequest} from "../../../Interfaces/companyUserManagement.interface";
import {IPaginationData} from "../../../Interfaces/pagination.interface";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import {CP_FLAGGED_USER_MESSAGE, DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import {UserCompanyRequestStatus} from "../../../constants/companyUserManagement.constant";
import useFilters from "../../../hooks/fetches/useFilters";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {companyUserManagementService} from "../../../services/companyUserManagement.service";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {clearFilters} from "../../../utils/filters.util";
import {formatDate, FULL_DATETIME_SHORT_MONTH} from "../../../utils/formatDate";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import ApproveRequestDialog from "./ApproveRequestDialog.component";
import DeclineRequestDialog from "./DeclineRequestDialog.component";
import FlagRequestDialog from "./FlagRequestDialog.component";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";

const UserRequestsTab = () => {
    const [itemsPerPage, setItemsPerPage] = useState<number>(40);
    const [page, setPage] = useState(1);
    const [requestToFlag, setRequestToFlag] = useState<IUserRequest>();
    const [requestToApprove, setRequestToApprove] = useState<IUserRequest>();
    const [requestToDecline, setRequestToDecline] = useState<IUserRequest>();
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const emptyValue = "--";
    const {filtersQuery: _userRequestFiltersQuery, filterState: userRequestsFilterState} = useFilters({
        filterKey: "companyUserRequestFilters",
        defaultFilters: {
            sort: "sorting_created_at__ASC",
        },
        queryParams: {
            company_id: activeCompany?.id,
        },
        enabled: !!activeCompany?.id,
    });
    const [filters, setFilters] = userRequestsFilterState;
    const queryClient = useQueryClient();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.USER_MANAGEMENT_UPDATE]);

    const userCompanyRequests = useQuery<IPaginationData<IUserRequest[]>>(
        ["user-company-requests", activeCompany?.id, page, JSON.stringify(userRequestsFilterState[0])],
        () => {
            return new Promise<IPaginationData<IUserRequest[]>>((resolve, reject) =>
                companyUserManagementService.getCompanyUserRequests(
                    activeCompany?.id || "",
                    {
                        page,
                        items_per_page: itemsPerPage,
                        dynamic: clearFilters(userRequestsFilterState[0]),
                    },
                    res => {
                        queryClient.refetchQueries(["companyUsersCounts"]);
                        resolve(res.data);
                    },
                    err => reject(err),
                ),
            );
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.id,
        },
    );
    const {data: userCompanyData, isLoading} = userCompanyRequests;
    const data = userCompanyData?.data || [];
    const metadata = userCompanyData?.meta || {total: 0, last_page: 0};

    const rejectReasons = useQuery<ICompanyRejectReason[] | null>(
        ["company-user-requests-reject-reasons", activeCompany?.id],
        () =>
            !activeCompany?.id
                ? new Promise(resolve => resolve(null))
                : new Promise<ICompanyRejectReason[]>((resolve, reject) =>
                      companyUserManagementService.getCompanyRejectReasons(
                          activeCompany?.id || "123",
                          res => resolve(res.data),
                          err => reject(err),
                      ),
                  ),
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.id,
        },
    );

    const handleSortChange = useCallback((sort, orderBy) => {
        setFilters?.({
            ...(filters || {}),
            sort: `sorting_${orderBy}__${sort}`,
        });
    }, []);

    const columns = useMemo(
        () => [
            {
                Header: "Requested Date",
                accessor: "created_at",
                Cell: ({row}) => (
                    <Typography variant="body4">
                        {row.original.created_at
                            ? formatDate(row.original.created_at, FULL_DATETIME_SHORT_MONTH)
                            : emptyValue}
                    </Typography>
                ),
                sortable: true,
            },
            {
                Header: "Name",
                accessor: "name",
                Cell: ({row}) => (
                    <Typography variant="body4">
                        {`${row.original.user?.first_name} ${row.original.user?.last_name}` || emptyValue}
                    </Typography>
                ),
                sortable: true,
            },
            {
                Header: "Email",
                accessor: "email",
                Cell: ({row}) => <Typography variant="body4">{row.original.user?.email || emptyValue}</Typography>,
                sortable: true,
            },
            {
                Header: "Flag Notes",
                accessor: "flag_notes",
                Cell: ({row}) => (
                    <Box width={200} maxWidth={200} overflow="hidden" textOverflow="ellipsis">
                        <Typography
                            title={row.original.red_flag_reason}
                            variant="body4"
                            width="200px"
                            textOverflow="ellipsis">
                            {row.original.user.red_flag_reason || emptyValue}
                        </Typography>
                    </Box>
                ),
                sortable: true,
            },
            {
                Header: "Flagged",
                accessor: "flag-user",
                Cell: ({row}) => (
                    <Box tooltip={{title: row.original.user.red_flagged ? CP_FLAGGED_USER_MESSAGE : "Flag User"}}>
                        {row.original.user.red_flagged ? (
                            <FlagIcon htmlColor={theme.palette.error[500]} />
                        ) : (
                            <FlagOutlinedIcon htmlColor={theme.palette.neutral[400]} />
                        )}
                    </Box>
                ),
            },
            ...(hasEditAccess
                ? [
                      {
                          Header: "Approve",
                          accessor: "approve-user",
                          Cell: ({row}) => {
                              const isApproved = row.original.status === UserCompanyRequestStatus.APPROVED;
                              return (
                                  <Box
                                      sx={{cursor: "pointer"}}
                                      title={isApproved ? "Request is Approved" : "Approve Request"}
                                      onClick={isApproved ? undefined : () => setRequestToApprove(row.original)}>
                                      {isApproved ? (
                                          <CheckCircleIcon htmlColor={theme.palette.neutral[500]} />
                                      ) : (
                                          <CheckIcon htmlColor={theme.palette.success[600]} />
                                      )}
                                  </Box>
                              );
                          },
                      },
                      {
                          Header: "Decline",
                          accessor: "decline-user",
                          Cell: ({row}) => {
                              const isDeclined = row.original.status === UserCompanyRequestStatus.DECLINED;
                              return (
                                  <Box
                                      sx={{cursor: "pointer"}}
                                      title={
                                          isDeclined
                                              ? rejectReasons.data?.find(r => r.id === row.original.status_reason)?.name
                                              : "Decline Request"
                                      }
                                      onClick={isDeclined ? undefined : () => setRequestToDecline(row.original)}>
                                      {isDeclined ? (
                                          <CancelIcon htmlColor={theme.palette.neutral[500]} />
                                      ) : (
                                          <CloseIcon htmlColor={theme.palette.error[600]} />
                                      )}
                                  </Box>
                              );
                          },
                      },
                  ]
                : []),
        ],
        [rejectReasons.data],
    );

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" paddingTop={3}>
                <TableV2Component
                    id="users-requests-table"
                    columns={columns as Column<IUserRequest>[]}
                    customData={data || []}
                    headerBackground={theme.palette.blue[100]}
                    noResultsMessage="No user requests found."
                    isLoading={isLoading}
                    onSortChange={handleSortChange}
                    sortType="ASC"
                    orderBy="created_at"
                />
                {!!metadata && (
                    <CustomPagination
                        metadata={metadata}
                        page={page}
                        itemsPerPage={itemsPerPage}
                        setPage={setPage}
                        setItemsPerPage={setItemsPerPage}
                    />
                )}
            </Box>
            {!!requestToFlag && (
                <FlagRequestDialog request={requestToFlag} open handleClose={() => setRequestToFlag(undefined)} />
            )}
            {!!requestToApprove && (
                <ApproveRequestDialog
                    request={requestToApprove}
                    open
                    handleClose={() => setRequestToApprove(undefined)}
                />
            )}
            {!!requestToDecline && (
                <DeclineRequestDialog
                    request={requestToDecline}
                    open
                    handleClose={() => setRequestToDecline(undefined)}
                />
            )}
        </ErrorBoundary>
    );
};

export default UserRequestsTab;
