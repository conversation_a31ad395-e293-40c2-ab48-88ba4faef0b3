import FolderOutlinedIcon from "@mui/icons-material/FolderOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import type {IFolder, IFolderContentStoreData, TFolderContentTypes} from "../../../Interfaces/folders.interface";
import {DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import useNotification from "../../../hooks/useNotification";
import {folderServices} from "../../../services/folder.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Typography from "../../Atoms/Typography/Typography.component";
import AutoCompleteComponent from "../../Molecules/Autocomplete/Autocomplete.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import {CONTENT_SUBJECT_TYPES} from "../../../constants/subjectTypes.constant";
interface IProps {
    open: boolean;
    onClose: () => void;
    selectedFile?: {
        id: string;
        name: string;
        type?: TFolderContentTypes;
    };
    companyId: string;
    onSave: () => void;
}

interface IFolderForm {
    folder_name: string;
}

const AddToFolderDialog = ({open, onClose, selectedFile, companyId, onSave}: IProps) => {
    const methods = useForm<IFolderForm>();
    const folderWatcher = methods.watch("folder_name");
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const notify = useNotification();
    const theme = useTheme();
    const {data: allFoldersData} = useQuery<IFolder[]>(
        ["allFoldersData", companyId],
        async () => {
            const promiseToReturn: Promise<IFolder[]> = new Promise((resolve, reject) =>
                folderServices.getAllFolders<AxiosResponse<IFolder[]>>(companyId!, {}, r => resolve(r.data), reject),
            );
            return promiseToReturn;
        },
        {
            enabled: Boolean(companyId),
            ...DEFAULT_QUERY_CONFIGS({refetch: {onMount: true}}),
        },
    );
    const options = allFoldersData?.length
        ? allFoldersData
              ?.map((item: IFolder) => {
                  return {id: item.id, label: item.folder_name};
              })
              .sort((a, b) => a.label.localeCompare(b.label))
        : [];

    const handleError = (err: AxiosError<any>, saving?: boolean) => {
        if (saving) {
            setIsSaving(false);
        }
        notify(getErrorFromArray(err), "Error");
    };

    const onSuccessAddFile = () => {
        notify("Content added successfully!", "Success");
        setIsSaving(false);
        onClose();
        onSave();
    };

    const handleSubmit = async () => {
        if (!folderWatcher) {
            notify("Please select the destination folder.", "Error");
            return;
        }
        const data: IFolderContentStoreData = {
            folder_id: folderWatcher,
            subjects: [
                {
                    subject_id: selectedFile?.id ?? "",
                    subject_type: selectedFile?.type ?? (CONTENT_SUBJECT_TYPES.PARTNER_TEMPLATE as TFolderContentTypes),
                },
            ],
        };
        setIsSaving(true);
        folderServices.storeFiles(companyId, {contents: [data]}, onSuccessAddFile, err => handleError(err, true));
    };

    return (
        <ModalComponent
            open={open}
            onClose={onClose}
            onSuccess={handleSubmit}
            title={"Add to Folder"}
            okButtonText="ADD"
            modalTheme="blue"
            icon={<FolderOutlinedIcon />}
            justifyButtons="flex-start"
            loading={isSaving}
            customModalBtnTracking={{
                file_id: selectedFile?.id,
            }}
            dialogSx={{
                ".MuiDialog-paper": {overflow: "unset", maxWidth: "500px !important", width: "100%"},
            }}
            content={
                <FormProvider {...methods}>
                    <Typography
                        fontWeight={"600 !important"}
                        fontInter
                        variant={"body2"}
                        color={theme.palette.neutral[800]}>
                        Select the destination folder for:
                    </Typography>
                    <Typography variant={"body3"} fontInter color={theme.palette.neutral[800]}>
                        {selectedFile?.name}
                    </Typography>
                    <AutoCompleteComponent
                        id="folder_name"
                        label="Folders"
                        options={options}
                        placeholder="Select Folder"
                        validationSchema={v => v !== "0"}
                        isRequired
                    />
                </FormProvider>
            }
        />
    );
};
export default AddToFolderDialog;
