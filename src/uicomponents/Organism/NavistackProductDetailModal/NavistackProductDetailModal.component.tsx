import BookmarkBorderOutlined from "@mui/icons-material/BookmarkBorderOutlined";
import ChatBubbleOutlineOutlinedIcon from "@mui/icons-material/ChatBubbleOutlineOutlined";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import LaunchOutlinedIcon from "@mui/icons-material/LaunchOutlined";
import PersonAddAlt1OutlinedIcon from "@mui/icons-material/PersonAddAlt1Outlined";
import RateReviewOutlinedIcon from "@mui/icons-material/RateReviewOutlined";
import SearchIcon from "@mui/icons-material/Search";
import StarOutlinedIcon from "@mui/icons-material/StarOutlined";
import StoreOutlinedIcon from "@mui/icons-material/StoreOutlined";
import TimerOutlinedIcon from "@mui/icons-material/TimerOutlined";
import {Divider, Grid, Skeleton} from "@mui/material";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError, AxiosResponse} from "axios";
import {useCallback, useEffect, useRef, useState} from "react";
import OutboundLink from "../../../components/Links/OutboundLink.component";
import CompanyPartnersModal from "../../../components/Modal/companyPartnersModal.component";
import {useDealsRegistrations} from "../../../hooks/fetches/useDealsRegistrations";

import {
    CHANNEL_PROGRAM_COMPANY_ID,
    CHANNEL_PROGRAM_SUBDOMAIN,
    REQUEST_DEMO_TEXT,
} from "../../../constants/commonStrings.constant";
import {PARTNER_INVITE_STATUS} from "../../../constants/partnerInviteStatus.constant";
import routeConfig from "../../../constants/routeConfig";
import {useCompany} from "../../../hooks/fetches/useCompany";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IStackCategorization} from "../../../Interfaces/myStack.interface";
import {companyService} from "../../../services/company.service";
import {mspPartnersService} from "../../../services/mspPartners.service";
import {PartnerPageService} from "../../../services/partnerpage.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import {useGetFriendlyUrl} from "../../../utils/profile.util";
import {generateSubdomainUrl} from "../../../utils/subdomain.util";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import MdfActiveBadge from "../../Badges/mdfActiveBadge.component";
import PortalActiveBadge from "../../Badges/portalActiveBadge.component";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import ClientUsage from "../ClientUsageWidget/ClientUsage.component";
import {IAvailableVendor} from "../MspTechStackTab/MspTechStackTab.component";
export interface ISubcategory {
    id?: string;
    name?: string;
    friendly_url?: string;
}

interface IProps {
    open: boolean;
    onClose?: () => void;
    companyFriendlyUrl?: string;
    onClickRemoveFromStack?: Function;
    onClickContactVendor?: Function;
    myStackProductsIDs?: string[] | undefined;
    stack: IStackCategorization | undefined;
    handleEditPartnerShipType: (stack: IStackCategorization) => void;
}

const NaviStackProductDetailsModal = (props: IProps) => {
    const {open, onClose, companyFriendlyUrl, stack} = props;
    const [showCompanyPartners, setShowCompanyPartners] = useState<boolean>(false);
    const [partnerUsers, setPartnerUsers] = useState<any>([]);
    const [prmActionLoading, setPrmActionLoading] = useState<boolean>(false);
    const [requestedAcceptedPartners, setRequestedAcceptedPartners] = useState<IAvailableVendor[]>([]);
    const {authState} = useAuthState();
    const {company} = useCompany(companyFriendlyUrl || "", {
        isCompany: !!companyFriendlyUrl,
        calls: {
            all: false,
            company: true,
        },
    });
    const companyInfo = company?.data;
    const {activeCompany, userCompanies} = useActiveCompany();
    const {availableVendors} = useDealsRegistrations(activeCompany?.id, {
        calls: {
            all: false,
            availableVendors: true,
        },
    });
    const availableVendorsData = availableVendors?.data;
    const is_current_partner = stack?.partner_status === "I am a current partner";
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_STACK_UPDATE]);
    const theme = useTheme();
    const foundPartner = partnerUsers?.find(p => p.friendly_url === companyFriendlyUrl);
    const isAvailableVendor = !!availableVendorsData?.find(p => p.id === stack?.product?.company_id);
    const msp_friendly_url = useGetFriendlyUrl(location.pathname);
    const notify = useNotification();
    const msp_id = msp_friendly_url
        ? authState.company_friendly_url === msp_friendly_url
            ? authState.company_id
            : userCompanies?.find(c => c.friendly_url === msp_friendly_url)?.id
        : authState.company_id;
    const apiCallMadeRef = useRef(false);

    const onSuccessGetAllPartners = (res: AxiosResponse) => {
        setPartnerUsers(res.data);
    };
    const isChannelProgram = companyInfo?.id === CHANNEL_PROGRAM_COMPANY_ID;
    const foundAvailableVendor = requestedAcceptedPartners.find(v => v.friendly_url === companyFriendlyUrl);

    const isLoading = company.isLoading;

    const handleDeleteInvite = async () => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Cancel Request?",
            okButtonText: "Yes",
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                        Are you sure want to cancel this access request?
                    </Typography>
                </Grid>
            ),
        });
        if (!answer.value) {
            return;
        }
        setPrmActionLoading(true);
        PartnerPageService.removeInvite(
            msp_id || "",
            companyInfo?.id || "",
            true,
            () => {
                notify("Success! Your request for access has been removed.", "Success");
                setPartnerUsers(partnerUsers.map(p => ({...p, partner: {...(p.partner || {}), status: null}})));
                setRequestedAcceptedPartners(requestedAcceptedPartners.map(p => ({...p, partner_status: null})));
                setPrmActionLoading(false);
            },
            handleError,
        );
    };

    const onSuccessRequestAccess = useCallback(
        (res: AxiosResponse) => {
            setPrmActionLoading(false);
            notify("Thank you! Your request has been received", "Success");
            const inviteObj = res.data[0];
            const foundPendingPartner = requestedAcceptedPartners.find(p => p.id === inviteObj.partner.id);
            if (foundPendingPartner) {
                setRequestedAcceptedPartners(
                    requestedAcceptedPartners.map(p => ({
                        ...p,
                        partner_status: p.id === inviteObj.partner?.id ? inviteObj.status : p.partner_status,
                    })),
                );
            } else if (companyInfo?.id) {
                const newRequested: IAvailableVendor = {
                    avatar: companyInfo?.avatar || null,
                    friendly_url: companyFriendlyUrl || "",
                    id: companyInfo?.id,
                    is_distributor: companyInfo?.is_distributor,
                    name: companyInfo?.name,
                    partner_flag: companyInfo?.partner_flag || false,
                    partner_status: PARTNER_INVITE_STATUS.REQUESTED as "requested",
                    subdomain: companyInfo?.subdomain,
                };
                setRequestedAcceptedPartners([...requestedAcceptedPartners, newRequested]);
            }
        },
        [partnerUsers],
    );

    const handleRequestAccess = () => {
        if (msp_id) {
            setPrmActionLoading(true);
            mspPartnersService.inviteVendor(
                companyInfo?.id || "",
                {email: [authState.email], initiator: "user"},
                onSuccessRequestAccess,
                handleError,
            );
        } else {
            notify("Request access for company is not available.", "Error");
            setPrmActionLoading(false);
        }
    };

    const handleError = (error: AxiosError<any>) => {
        prmActionLoading && setPrmActionLoading(false);
        notify(getErrorFromArray(error), "Error");
    };
    const onSuccessGetRequestedAccepted = (res: AxiosResponse) => {
        setRequestedAcceptedPartners(res.data);
    };
    useEffect(() => {
        if (msp_id && open && !apiCallMadeRef.current) {
            apiCallMadeRef.current = true;
            PartnerPageService.getCompanyPartners(msp_id, onSuccessGetAllPartners);
            companyService.getStackAvailableVendors(msp_id || "", {}, onSuccessGetRequestedAccepted);
        }
    }, [msp_id, open]);

    if (showCompanyPartners) {
        return (
            <CompanyPartnersModal
                show={showCompanyPartners}
                setShow={(shouldShow: boolean) => setShowCompanyPartners(shouldShow)}
                type="claimers"
                partner={foundPartner}
            />
        );
    }
    return (
        <>
            <ModalComponent
                id="productDetailsModal"
                justifyButtons="flex-start"
                open={open}
                onClose={onClose as any}
                hideCloseBtn
                title={""}
                contentClassName="mt-0"
                modalTheme="blue"
                showOkButton={false}
                showCancelButton={false}
                maxWidth="md"
                showBottom={false}
                dialogSx={{
                    ".MuiDialog-paper": {
                        overflow: "auto",
                        maxWidth: "800px !important",
                        maxHeight: "100%",
                        width: "100%",
                    },
                    ".MuiDialogContent-root": {
                        overflow: "auto",
                    },
                }}
                content={
                    <Box
                        marginTop={2}
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            height: "100%",
                            gap: 2,
                        }}>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                // justifyContent: "center",
                                position: "sticky",
                                gap: 2,
                                width: "100%",
                                flexDirection: "column",
                                "@media (min-width: 768px)": {
                                    flexDirection: "row",
                                },
                            }}>
                            <Box position="relative">
                                <AvatarComponentV2
                                    isCompany
                                    size={48}
                                    user={{
                                        avatar: companyInfo?.profile_images?.CompanyAvatar?.[0]?.src || "",
                                        type: companyInfo?.company_type || "",
                                        is_distributor: companyInfo?.is_distributor,
                                        profile_type: companyInfo?.company_profile_type,
                                        friendly_url: companyInfo?.friendly_url || "",
                                        name: companyInfo?.name || "",
                                    }}
                                    isRedirectEnabled={false}
                                    sx={{
                                        padding: 0,
                                    }}
                                />
                                <Badge
                                    position={{left: "50%"}}
                                    style={{transform: `translate(-50%, -50%)`}}
                                    color={is_current_partner ? "success" : "warning"}
                                    padding="0px">
                                    {is_current_partner ? (
                                        <CheckIcon
                                            sx={{
                                                fill: theme.palette.success[800],
                                                width: "12px",
                                                height: "12px",
                                            }}
                                        />
                                    ) : (
                                        <SearchIcon
                                            sx={{
                                                fill: theme.palette.primary[700],
                                                width: "12px",
                                                height: "12px",
                                            }}
                                        />
                                    )}
                                </Badge>
                            </Box>

                            <Box
                                display="flex"
                                justifyContent="space-between"
                                sx={{
                                    width: "100%",
                                    flexDirection: "column",
                                    gap: "8px",
                                    "@media (min-width: 768px)": {
                                        flexDirection: "row",
                                    },
                                }}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        flex: 1,
                                        "@media (min-width: 768px)": {
                                            gap: "0px",
                                        },
                                        gap: "8px",
                                    }}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: {xs: "column", md: "row"},
                                            alignItems: "center",
                                            minHeight: "34px",
                                            flex: 1,
                                            gap: 1,
                                        }}>
                                        {isLoading ? (
                                            <Skeleton sx={{width: 200, height: 50}} />
                                        ) : (
                                            <>
                                                <Typography
                                                    variant="h5"
                                                    fontFamily="roc-grotesk"
                                                    title={companyInfo?.name || ""}
                                                    sx={{
                                                        fontSize: "24px !important",
                                                        fontWeight: "400 !important",
                                                        lineHeight: "normal",
                                                        color: theme.palette.neutral[800],
                                                        textOverflow: "ellipsis",
                                                        overflow: "hidden",
                                                        display: "-webkit-box",
                                                        WebkitLineClamp: 1,
                                                        WebkitBoxOrient: "vertical",
                                                        wordBreak: "break-word",
                                                    }}>
                                                    {companyInfo?.name || ""}
                                                </Typography>
                                            </>
                                        )}
                                    </Box>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: {xs: "column", md: "row"},
                                            alignItems: "center",
                                            flex: 1,
                                            gap: 2,
                                            width: "100%",
                                        }}>
                                        <Typography
                                            letterSpacing="0px"
                                            variant="subtitle2"
                                            sx={{color: theme.palette.neutral["600"], fontWeight: 600}}>
                                            {stack?.product.name}
                                        </Typography>
                                        {companyInfo?.rating ? (
                                            <Box display="flex" gap="2px" alignItems="center">
                                                <StarOutlinedIcon
                                                    sx={{
                                                        fill: theme.palette.warning[600],
                                                        width: "16px",
                                                        height: "16px",
                                                    }}
                                                />
                                                <Typography
                                                    letterSpacing="0px"
                                                    variant="subtitle3"
                                                    sx={{color: theme.palette.secondary["main"]}}>
                                                    {Number(companyInfo.rating).toFixed(1)}
                                                </Typography>
                                            </Box>
                                        ) : (
                                            <Typography
                                                variant="subtitle3"
                                                fontWeight={600}
                                                color={theme.palette.neutral[600]}
                                                letterSpacing={0}>
                                                No reviews yet
                                            </Typography>
                                        )}
                                    </Box>
                                </Box>
                                <Box display="flex" alignItems="center" gap={2}>
                                    <Box display="flex" gap="4px">
                                        {is_current_partner ? (
                                            <Badge color="success" padding="2px 6px">
                                                <CheckIcon
                                                    sx={{
                                                        fill: theme.palette.success[800],
                                                        width: "12px",
                                                        height: "12px",
                                                        marginRight: "8px",
                                                    }}
                                                />
                                                Current Partner
                                            </Badge>
                                        ) : (
                                            <Badge color="warning" padding="2px 6px" sx={{color: "#945100"}}>
                                                <SearchIcon
                                                    sx={{
                                                        fill: theme.palette.warning[600],
                                                        width: "12px",
                                                        height: "12px",
                                                        marginRight: "8px",
                                                    }}
                                                />
                                                Looking for information
                                            </Badge>
                                        )}
                                        {hasEditAccess && (
                                            <EditOutlinedIcon
                                                titleAccess="Edit connection type"
                                                onClick={() => props.handleEditPartnerShipType(stack!)}
                                                style={{
                                                    fontSize: "24px",
                                                    width: "24px",
                                                    height: "24px",
                                                    fill: theme.palette.neutral[500],
                                                    cursor: "pointer",
                                                }}
                                            />
                                        )}
                                    </Box>
                                </Box>
                                {!!companyInfo?.partner_flag &&
                                foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ? (
                                    <Button
                                        sx={{alignSelf: "center"}}
                                        type="button"
                                        color="secondary"
                                        variant="outlined"
                                        title="Send message"
                                        onClick={() => setShowCompanyPartners(true)}
                                        id="send"
                                        disabled={!hasEditAccess}>
                                        <ChatBubbleOutlineOutlinedIcon
                                            sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                        />
                                        <Typography
                                            fontInter
                                            variant="body3"
                                            sx={{
                                                fontWeight: "600 !important",
                                                lineHeight: "20px",
                                                color: theme.palette.blue[800],
                                            }}>
                                            {"SEND MESSAGE"}
                                        </Typography>
                                    </Button>
                                ) : (
                                    <Button
                                        title={REQUEST_DEMO_TEXT}
                                        sx={{
                                            width: {xs: "100%!important", md: "187px!important"},
                                            cursor: hasEditAccess ? "cursor" : "not-allowed",
                                            alignSelf: "center",
                                        }}
                                        onClick={() => {
                                            if (props?.onClickContactVendor) {
                                                props?.onClickContactVendor(
                                                    companyInfo?.name,
                                                    companyInfo?.friendly_url,
                                                );
                                            }
                                            if (!!onClose) {
                                                onClose();
                                            }
                                        }}
                                        variant="tonal"
                                        type="button"
                                        id="requestDemo">
                                        <BookmarkBorderOutlined
                                            sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                        />
                                        <Typography
                                            variant="body4"
                                            fontInter
                                            sx={{
                                                fontWeight: "600 !important",
                                                lineHeight: "20px",
                                                color: theme.palette.neutral[100],
                                            }}>
                                            REQUEST DEMO
                                        </Typography>
                                    </Button>
                                )}
                            </Box>
                            <IconButton
                                id="closeModal"
                                sx={{
                                    backgroundColor: "#fff !important",
                                    fontSize: "32px",
                                    "& svg": {
                                        width: "32px !important",
                                        height: "32px !important",
                                    },
                                    "@media (max-width: 768px)": {
                                        position: "absolute",
                                        right: 0,
                                    },
                                }}
                                onClick={e => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    if (!!onClose) {
                                        onClose();
                                    }
                                }}
                                color="blue">
                                <CloseIcon fontSize="inherit" htmlColor={theme.palette.neutral?.[500]} />
                            </IconButton>
                        </Box>
                        <Divider />
                        <Box
                            display="grid"
                            gridTemplateColumns={
                                is_current_partner && activeCompany?.manage_clients
                                    ? {xs: "1fr", md: "1fr 320px"}
                                    : {xs: "1fr", md: "1fr"}
                            }
                            gap="16px">
                            <Box display="flex" flexDirection="column" gap="16px">
                                <Box
                                    id="aboutVendor"
                                    display="flex"
                                    flexDirection="column"
                                    gap="4px"
                                    maxWidth="fit-content">
                                    <Typography
                                        variant="subtitle4"
                                        sx={{
                                            color: theme.palette.neutral[600],
                                            fontWeight: 600,
                                            fontSize: "10px",
                                            letterSpacing: "0.4px",
                                        }}>
                                        ABOUT THE VENDOR
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            gap: 0.5,
                                        }}>
                                        {companyInfo?.is_mdf && <MdfActiveBadge version="long" />}
                                        <PortalActiveBadge partner_flag={!!companyInfo?.partner_flag} />
                                    </Box>
                                </Box>
                                <Box id="aboutProduct" display="flex" flexDirection="column" gap="4px">
                                    <Typography
                                        variant="subtitle4"
                                        sx={{
                                            color: theme.palette.neutral[600],
                                            fontWeight: 600,
                                            fontSize: "10px",
                                            letterSpacing: "0.4px",
                                        }}>
                                        ABOUT THE PRODUCT
                                    </Typography>
                                    <Typography
                                        variant="body3"
                                        sx={{
                                            color: theme.palette.neutral[800],
                                            lineHeight: "160%",
                                            letterSpacing: "0.4px",
                                            maxHeight: "250px",
                                            overflow: "auto",
                                            "& p": {
                                                color: theme.palette.neutral[800],
                                                fontSize: "16px",
                                            },
                                        }}
                                        dangerouslySetInnerHTML={{
                                            __html: stack?.product.description || "",
                                        }}></Typography>
                                </Box>
                            </Box>
                            {is_current_partner && activeCompany?.manage_clients && <ClientUsage stackId={stack.id} />}
                        </Box>
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: {xs: "column", md: "row"},
                                flex: 1,
                                gap: 1,
                            }}>
                            <OutboundLink
                                href={
                                    is_current_partner
                                        ? window.location.origin +
                                          routeConfig.CreateReview.path.replace(
                                              ":friendly_url",
                                              stack?.product.friendly_url,
                                          )
                                        : window.location.origin +
                                          routeConfig.ProductDetails.path.replace(
                                              ":friendly_url",
                                              stack?.product.friendly_url || "",
                                          )
                                }>
                                <Button
                                    sx={{
                                        width: {xs: "100%!important", md: "187px!important"},
                                        alignSelf: "stretch",
                                        margin: 0,
                                    }}
                                    variant="contained"
                                    color="secondary"
                                    type="button"
                                    title={is_current_partner ? "Write a review" : "See product details"}
                                    id="productDetails">
                                    {is_current_partner ? (
                                        <RateReviewOutlinedIcon
                                            sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                        />
                                    ) : (
                                        <LaunchOutlinedIcon sx={{marginRight: "8px", width: "18px", height: "18px"}} />
                                    )}

                                    <Typography
                                        variant="body4"
                                        fontInter
                                        sx={{
                                            fontWeight: "600 !important",
                                            lineHeight: "20px",
                                            color: theme.palette.neutral[100],
                                        }}>
                                        {is_current_partner ? "WRITE A REVIEW" : "SEE FULL DETAILS"}
                                    </Typography>
                                </Button>
                            </OutboundLink>
                            {!isAvailableVendor && (
                                <Button
                                    sx={{
                                        width: {xs: "100%!important", md: "187px!important"},
                                        alignSelf: "stretch",
                                        margin: 0,
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                    disabled={
                                        prmActionLoading
                                            ? true
                                            : hasEditAccess
                                              ? false
                                              : foundAvailableVendor?.partner_status !== PARTNER_INVITE_STATUS.ACCEPTED
                                    }
                                    onClick={
                                        foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ||
                                        isChannelProgram
                                            ? () => {
                                                  const url = generateSubdomainUrl(
                                                      isChannelProgram
                                                          ? CHANNEL_PROGRAM_SUBDOMAIN
                                                          : foundAvailableVendor?.subdomain,
                                                  );
                                                  const newTab = window.open(url, "_blank");
                                                  newTab?.focus();
                                              }
                                            : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.REQUESTED
                                              ? () => handleDeleteInvite()
                                              : () => handleRequestAccess()
                                    }
                                    variant="outlined"
                                    color="secondary"
                                    type="button"
                                    tooltip={{
                                        title:
                                            foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ||
                                            isChannelProgram
                                                ? "Access vendor portal"
                                                : foundAvailableVendor?.partner_status ===
                                                    PARTNER_INVITE_STATUS.REQUESTED
                                                  ? "Cancel PRM access request"
                                                  : "Request PRM access",
                                        placement: "bottom",
                                    }}
                                    id="prmAction">
                                    <Typography
                                        variant="body4"
                                        fontInter
                                        sx={{
                                            fontWeight: "600 !important",
                                            lineHeight: "20px",
                                            display: "flex",
                                            alignItems: "center",
                                        }}>
                                        {foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.ACCEPTED ||
                                        isChannelProgram ? (
                                            <>
                                                <StoreOutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />{" "}
                                                VENDOR PORTAL
                                            </>
                                        ) : foundAvailableVendor?.partner_status === PARTNER_INVITE_STATUS.REQUESTED ? (
                                            <>
                                                <TimerOutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />
                                                {prmActionLoading ? "Cancelling..." : "CANCEL REQUEST"}
                                            </>
                                        ) : (
                                            <>
                                                <PersonAddAlt1OutlinedIcon
                                                    sx={{marginRight: "8px", width: "18px", height: "18px"}}
                                                />
                                                {prmActionLoading ? "REQUESTING..." : "REQUEST ACCESS"}
                                            </>
                                        )}
                                    </Typography>
                                </Button>
                            )}
                            <Button
                                sx={{
                                    color: theme.palette.error[500],
                                    margin: 0,
                                    cursor: hasEditAccess ? "cursor" : "not-allowed",
                                }}
                                id={`removeFromStack`}
                                data-track="true"
                                tooltip={{title: "Remove from stack"}}
                                color="error"
                                disabled={!hasEditAccess}
                                onClick={e => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    props?.onClickRemoveFromStack?.(stack);
                                }}
                                variant="outlined">
                                <DeleteOutlineOutlinedIcon sx={{marginRight: "8px", width: "18px", height: "18px"}} />
                                <Typography
                                    variant="body4"
                                    fontInter
                                    sx={{
                                        fontWeight: "600 !important",
                                        lineHeight: "20px",
                                        color: theme.palette.error[500],
                                    }}>
                                    {"REMOVE FROM STACK"}
                                </Typography>
                            </Button>
                        </Box>
                    </Box>
                }
            />
        </>
    );
};

export default NaviStackProductDetailsModal;
