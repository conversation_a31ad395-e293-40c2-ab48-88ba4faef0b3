import {render} from "@testing-library/react";
import {beforeEach, describe, expect, it, Mock, vi} from "vitest";
import TestWrapper from "../../../../__test_utils__/TestWrapper";
import {useCompany} from "../../../hooks/fetches/useCompany";
import {IStackCategorization} from "../../../Interfaces/myStack.interface";
import NaviStackProductDetailsModal from "./NavistackProductDetailModal.component";

vi.mock("../../../hooks/fetches/useCompany", () => ({
    useCompany: vi.fn().mockReturnValue({company: {data: {id: "1233456", is_mdf: true}}}),
}));

describe("NaviStackProductDetailsModal Component", () => {
    const onCloseMock = vi.fn();
    const handleEditPartnerShipTypeMock = vi.fn();

    beforeEach(() => {
        (useCompany as Mock).mockReturnValue({company: {data: {id: "1233456", is_mdf: true}}});
    });

    const renderComponent = open =>
        render(
            <TestWrapper>
                <NaviStackProductDetailsModal
                    open={open}
                    onClose={onCloseMock}
                    handleEditPartnerShipType={handleEditPartnerShipTypeMock}
                    stack={mockedStack}
                    companyFriendlyUrl="test"
                />
            </TestWrapper>,
        );

    it("should display modal when open prop is true", () => {
        const {getByTestId} = renderComponent(true);
        expect(getByTestId("productDetailsModal")).toBeTruthy();
    });

    it("should not display modal when open prop is false", () => {
        const {queryByTestId} = renderComponent(false);
        expect(queryByTestId("productDetailsModal")).toBeNull();
    });

    it("should render MDF badge if company has is_mdf", () => {
        const {getByTestId} = renderComponent(true);
        expect(getByTestId("mdf-badge")).toBeTruthy();
    });
});

const mockedStack = {
    id: "923987621687099393",
    stack_company_id: "699791233831282449",
    prm_partner_status: "requested",
    stack_company: {
        id: "699791233831282449",
        name: "Wasabi",
        description: "A global leader in cloud infrastructure and business mobility",
        handle: "dracula1",
        friendly_url: "wasabidennis",
        subdomain: "wasabi",
        avatar: "https://pedro-cp-public.s3.us-east-005.backblazeb2.com/952560216348426241/CP-Virtualbackround-1.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=00529fd9a0348060000000001%2F20240710%2Fus-east-005%2Fs3%2Faws4_request&X-Amz-Date=20240710T142938Z&X-Amz-SignedHeaders=host&X-Amz-Expires=259200&X-Amz-Signature=8dbfd0febcd04955fb4f2e15b53c6b88dded0d085d51e768fa6f16f0602d25e6",
        is_distributor: false,
        manage_clients: false,
        subcategories: [],
        product_reviews_count: null,
        partner_flag: false,
        show_distributor_banner: true,
        show_manage_clients_banner: true,
        show_manage_affiliates_banner: true,
        show_as_sponsored: false,
        is_mdf: true,
    },
    category_id: "914121889178779649",
    category: {
        id: "914121889178779649",
        parent_id: "914121888924631041",
        name: "Active Directory",
        color: "#000000",
        featured: true,
        is_hidden: false,
        friendly_url: "active-directory",
        is_top_category: true,
        stack_chart_min_y: "0",
        stack_chart_max_y: "1",
        stack_chart_min_x: "0",
        stack_chart_max_x: "5",
        default_my_stack_category: false,
        navistack_reminder: false,
    },
    parent_category_id: "914121888924631041",
    parent_category: {
        id: "914121888924631041",
        parent_id: null,
        name: "Authentication",
        color: "#000000",
        featured: false,
        is_hidden: false,
        friendly_url: "authentication",
        is_top_category: false,
        default_my_stack_category: false,
        navistack_reminder: false,
    },
    product_id: "914134182096601089",
    product: {
        id: "914134182096601089",
        company_id: "699791233831282449",
        company: {
            id: "699791233831282449",
            name: "Wasabi",
            description: "A global leader in cloud infrastructure and business mobility",
            handle: "dracula1",
            friendly_url: "wasabidennis",
            subdomain: "wasabi",
            avatar: "https://pedro-cp-public.s3.us-east-005.backblazeb2.com/952560216348426241/CP-Virtualbackround-1.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=00529fd9a0348060000000001%2F20240710%2Fus-east-005%2Fs3%2Faws4_request&X-Amz-Date=20240710T142938Z&X-Amz-SignedHeaders=host&X-Amz-Expires=259200&X-Amz-Signature=8dbfd0febcd04955fb4f2e15b53c6b88dded0d085d51e768fa6f16f0602d25e6",
            is_distributor: false,
            manage_clients: false,
            subcategories: [],
            product_reviews_count: null,
            partner_flag: false,
            show_distributor_banner: true,
            show_manage_clients_banner: true,
            show_manage_affiliates_banner: true,
            show_as_sponsored: false,
            is_mdf: true,
        },
        name: "Product 1",
        friendly_url: "product-1",
        rating: "5",
        total_reviews: 2,
        description: "<p>adafdsffads</p>",
        categories: [],
    },
    client_usage: {
        total: 5,
        installed: 0,
    },
    partner_status: "I am a current partner",
    created: "2024-03-11T14:35:10.000000Z",
    updated: "2024-03-11T14:35:10.000000Z",
} as IStackCategorization;
