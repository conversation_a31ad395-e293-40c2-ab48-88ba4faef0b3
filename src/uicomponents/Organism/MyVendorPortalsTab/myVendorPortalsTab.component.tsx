import Divider from "@mui/material/Divider";
import Grid from "@mui/material/Grid";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/system/useTheme";
import {useQuery} from "@tanstack/react-query";
import type {AxiosError, AxiosResponse} from "axios";
import {Fragment, useMemo, useState} from "react";
import {useSearchParams} from "react-router-dom";
import {
    DEFAULT_QUERY_CONFIGS,
    missedMessageText,
    portalAccess,
    portalNoAccess,
    portalRequestPending,
} from "../../../constants/commonStrings.constant";
import {PARTNER_INVITE_STATUS} from "../../../constants/partnerInviteStatus.constant";
import routeConfig from "../../../constants/routeConfig";
import {useDebounce} from "../../../hooks/useDebounce";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import usePusherState from "../../../hooks/usePusherState";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {companyService} from "../../../services/company.service";
import {PartnerPageService} from "../../../services/partnerpage.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {getErrorFromArray} from "../../../utils/error.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import CondensedSubjectCard from "../../Molecules/CondensedSubjectCard/condensedSubjectCard.component";
import MoreIconButton from "../../Molecules/MoreIconButton/moreIconButton.component";
import SearchBar from "../../Molecules/SearchBar/SearchBar.component";
import type {IAvailableVendor} from "../MspTechStackTab/MspTechStackTab.component";

interface IProps {
    company_id?: string;
    setShowPartnersFor?: any;
    isMSP?: boolean;
}

const MyVendorPortalsTab = (props: IProps) => {
    const {company_id} = props;
    const [loadingAction, setLoadingAction] = useState<any>({});
    const [search, setSearch] = useState<string>("");
    const debouncedSearch = useDebounce(search, 500);
    const {pusherState} = usePusherState();
    const theme = useTheme();
    const notify = useNotification();
    const {hasPermissions, isAdmin} = usePermissions();
    const connectionPermissions = [PERMISSION_GROUPS.MANAGE_CONNECTIONS_UPDATE];
    const hasConnectionPermissions = hasPermissions(connectionPermissions);
    const [params] = useSearchParams();
    const invited_by = useMemo(() => params.get("invited"), [params]);

    const typesToShow = ["Accepted", "Requested"];
    const handleViewPortal = (vendor: IAvailableVendor) => {
        const url = `${window.location.protocol}//${vendor.subdomain || ""}.${
            window.location.host
        }?as=${company_id}&cn=${vendor?.name || ""}`;
        const newTab = window.open(url, "_blank");
        newTab?.focus();
    };

    const handleDeleteInvite = async (vendor: IAvailableVendor, type: string, successCallback?: Function) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: type === "Remove" ? "Remove Access?" : "Cancel Request?",
            okButtonText: type === "Remove" ? "Remove" : "Yes",
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    {type === "Remove" ? (
                        <Typography fontInter variant="body2" textAlign={"center"}>
                            You will no longer have access to the Channel Command Portal for{" "}
                            <strong>{vendor?.name}</strong>. <br />
                            To regain access in the future, you’ll need to submit a new request for access.
                        </Typography>
                    ) : (
                        <Typography
                            fontInter
                            variant="body2"
                            style={{textAlign: "center", fontSize: "20px !important"}}>
                            Are you sure want to cancel this access request?
                        </Typography>
                    )}
                </Grid>
            ),
        });
        if (!answer.value) return;
        setLoadingAction({...loadingAction, [vendor.id]: true});
        PartnerPageService.removeInvite(
            company_id || "",
            vendor.id,
            true,
            () => {
                notify(
                    type === "Remove"
                        ? "Success! Access has been removed."
                        : "Success! Your request for access has been removed.",
                    "Success",
                );
                refetchVendors();
                setLoadingAction({...loadingAction, [vendor.id]: false});
                successCallback && successCallback(type === "Remove" ? "removeAccess" : "cancelRequest", vendor.id);
            },
            err => {
                setLoadingAction({...loadingAction, [vendor.id]: false});
                notify(getErrorFromArray(err), "Error");
            },
        );
    };

    const handleOptionSelect = (option: any, vendor: IAvailableVendor, successCallback?: Function) => {
        const selectedOption = typeof option === "string" ? option : option.id;
        switch (selectedOption) {
            case "viewPortal":
                handleViewPortal(vendor);
                break;
            case "messagePartner":
                props.setShowPartnersFor && props.setShowPartnersFor(vendor.id);
                break;
            case "removeAccess":
                handleDeleteInvite(vendor, "Remove", successCallback);
                break;
            case "cancelRequest":
                handleDeleteInvite(vendor, "Invitation", successCallback);
                break;
        }
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    const {
        data: vendorsData,
        isLoading: isLoadingVendors,
        refetch: refetchVendors,
    } = useQuery<IAvailableVendor[] | undefined>(
        ["available-vendors", props?.company_id, debouncedSearch],
        async () => {
            const promiseToReturn: Promise<IAvailableVendor[]> = new Promise((resolve, reject) =>
                companyService
                    .getStackAvailableVendors(company_id!, {
                        dynamic: {partner_status: typesToShow},
                        partner_page: true,
                        search_word: debouncedSearch,
                    })
                    .then((r: AxiosResponse<IAvailableVendor[]>) => resolve(r.data))
                    .catch(err => {
                        handleError(err);
                        reject(err);
                    }),
            );
            return promiseToReturn;
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: Boolean(company_id) && props.isMSP,
        },
    );
    const vendors = vendorsData || [];

    return (
        <Box
            sx={{
                width: "100%",
            }}>
            <Box>
                <Typography
                    fontInter
                    variant="subtitle1"
                    sx={{
                        fontSize: "24px !important",
                        color: theme.palette.neutral[700],
                        fontWeight: "500 !important",
                    }}>
                    My Vendor Portals
                </Typography>
                <Divider
                    sx={{
                        borderBottomWidth: 2,
                        borderColor: theme.palette.neutral[500],
                        marginY: "10px",
                        display: "block",
                        width: "100%",
                    }}
                />
            </Box>
            <Box>
                <SearchBar
                    style={{
                        backgroundColor: "var(--cpWhite)",
                        padding: "16px",
                    }}
                    placeholder="Search stack..."
                    onChange={e => {
                        setSearch(e.target.value);
                    }}
                />
            </Box>
            {isLoadingVendors ? (
                Array(10)
                    .fill(null)
                    .map((_, i) => <Skeleton key={i} width="150px" height="300px" />)
            ) : vendors?.length ? (
                typesToShow.map(type => (
                    <Box
                        key={type}
                        sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: "16px",
                            margin: "16px 0",
                        }}>
                        {vendors
                            .filter((vendor: IAvailableVendor) => vendor.partner_status === type.toLowerCase())
                            .map((vendor: IAvailableVendor) => {
                                const customLink =
                                    vendor.subdomain && vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                        ? routeConfig.VendorPortal.path + `?as=${company_id}`
                                        : undefined;
                                const customSubdomain =
                                    vendor.subdomain && vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                        ? vendor.subdomain
                                        : undefined;
                                const hasMissedMessages = pusherState.unreadChats?.[vendor.id];
                                const custom_tracking_properties = {
                                    vendor_name: vendor.name,
                                    vendor_id: vendor.id,
                                    vendor_friendly_url: vendor.friendly_url,
                                    msp_company_id: company_id,
                                };
                                return (
                                    <Fragment key={vendor.id}>
                                        <CondensedSubjectCard
                                            key={vendor.id}
                                            title={vendor.name}
                                            avatarUser={{
                                                name: vendor.name,
                                                avatar: vendor.avatar || "",
                                                type: "company",
                                                profile_type: "",
                                                friendly_url: vendor.friendly_url,
                                                is_distributor: vendor.is_distributor,
                                            }}
                                            showNotificationDot={hasMissedMessages}
                                            notificationDotTitle={missedMessageText}
                                            rating={vendor.rating ? vendor.rating.toFixed(1) : undefined}
                                            highlighted={!!invited_by && invited_by === vendor.subdomain}
                                            sx={{marginTop: "10px", marginRight: "10px"}}
                                            badge={
                                                vendor.partner_flag
                                                    ? {
                                                          label: "Portal",
                                                          backgroundColor:
                                                              vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                                                  ? theme.palette.success[200]
                                                                  : vendor.partner_status ===
                                                                    PARTNER_INVITE_STATUS.REQUESTED
                                                                  ? "#FCF0EC"
                                                                  : theme.palette.error[200],
                                                          color:
                                                              vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                                                  ? theme.palette.success[600]
                                                                  : vendor.partner_status ===
                                                                    PARTNER_INVITE_STATUS.REQUESTED
                                                                  ? theme.palette.primaryOrange["main"]
                                                                  : theme.palette.error["main"],
                                                          title:
                                                              vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                                                                  ? portalAccess
                                                                  : vendor.partner_status ===
                                                                    PARTNER_INVITE_STATUS.REQUESTED
                                                                  ? portalRequestPending
                                                                  : portalNoAccess,
                                                      }
                                                    : undefined
                                            }
                                            action={
                                                !hasConnectionPermissions &&
                                                vendor.partner_status !== PARTNER_INVITE_STATUS.ACCEPTED ? null : (
                                                    <MoreIconButton
                                                        loading={loadingAction[vendor.id]}
                                                        onOptionSelect={(option: any) => {
                                                            handleOptionSelect(option, vendor);
                                                        }}
                                                        options={
                                                            vendor.partner_flag
                                                                ? vendor.partner_status ===
                                                                  PARTNER_INVITE_STATUS.ACCEPTED
                                                                    ? [
                                                                          {
                                                                              id: "viewPortal",
                                                                              label: "View Portal",
                                                                              track_properties:
                                                                                  custom_tracking_properties,
                                                                          },
                                                                          {
                                                                              id: "messagePartner",
                                                                              label: "Message Partner",
                                                                              track_properties:
                                                                                  custom_tracking_properties,
                                                                          },
                                                                          {
                                                                              id: "removeAccess",
                                                                              label: "Remove Access",
                                                                              track_properties:
                                                                                  custom_tracking_properties,
                                                                              permissions: connectionPermissions,
                                                                          },
                                                                      ]
                                                                    : vendor.partner_status ===
                                                                      PARTNER_INVITE_STATUS.REQUESTED
                                                                    ? [
                                                                          {
                                                                              id: "cancelRequest",
                                                                              label: "Cancel Request",
                                                                              track_properties:
                                                                                  custom_tracking_properties,
                                                                              permissions: connectionPermissions,
                                                                          },
                                                                      ]
                                                                    : [
                                                                          {
                                                                              id: "requestAccess",
                                                                              label: "Request Access",
                                                                              track_properties:
                                                                                  custom_tracking_properties,
                                                                              permissions: connectionPermissions,
                                                                          },
                                                                      ]
                                                                : [
                                                                      {
                                                                          id: "contactVendor",
                                                                          label: "Contact Vendor",
                                                                          track_properties: custom_tracking_properties,
                                                                          permissions: connectionPermissions,
                                                                      },
                                                                  ]
                                                        }
                                                    />
                                                )
                                            }
                                            customAvatarLink={customLink}
                                            customAvatarSubdomain={customSubdomain}
                                            customTitleLink={
                                                customLink ||
                                                routeConfig.VendorProfile.path.replace(":id", vendor.friendly_url)
                                            }
                                            customTitleSubdomain={customSubdomain}
                                            linkTarget="_blank"
                                            showChatBtn={
                                                vendor.partner_flag &&
                                                vendor.partner_status === PARTNER_INVITE_STATUS.ACCEPTED &&
                                                hasConnectionPermissions
                                            }
                                            onChatBtnClick={() => {
                                                handleOptionSelect("messagePartner", vendor);
                                            }}
                                            chatBtnProps={{
                                                partner_name: vendor.name,
                                                partner_friendly_url: vendor.friendly_url,
                                                partner_id: vendor.id,
                                                disabled: isAdmin,
                                            }}
                                        />
                                    </Fragment>
                                );
                            })}
                    </Box>
                ))
            ) : (
                <div className="d-flex justify-content-center align-items-center mt-3">
                    <Typography fontInter variant="body3">
                        No Vendors Found.
                    </Typography>
                </div>
            )}
        </Box>
    );
};

export default MyVendorPortalsTab;
