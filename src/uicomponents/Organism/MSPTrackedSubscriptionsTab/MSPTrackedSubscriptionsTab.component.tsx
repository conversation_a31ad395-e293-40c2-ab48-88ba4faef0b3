import {CheckOutlined} from "@mui/icons-material";
import Add from "@mui/icons-material/Add";
import CalendarTodayOutlinedIcon from "@mui/icons-material/CalendarTodayOutlined";
import DownloadOutlinedIcon from "@mui/icons-material/DownloadOutlined";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import ImportExportOutlinedIcon from "@mui/icons-material/ImportExportOutlined";
import {List} from "@mui/material";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import Box from "@mui/system/Box";
import {useQuery} from "@tanstack/react-query";
import {AxiosError, AxiosResponse} from "axios";
import {useState} from "react";
import {useNavigate} from "react-router-dom";
import {
    BETTERTRACKER_CONTRACT_TEXT,
    DEFAULT_QUERY_CONFIGS,
    MSP_CONTRACT_TEXT,
} from "../../../constants/commonStrings.constant";
import CompanyType from "../../../constants/companyType.constant";
import routeConfig from "../../../constants/routeConfig";
import {VENDOR_TYPES} from "../../../constants/vendorProfiles.constants";
import {useMspContracts} from "../../../hooks/fetches/useMspContracts";
import {useQueryHelper} from "../../../hooks/helpers/useQueryHelper";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useCurrency from "../../../hooks/useCurrency";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import {ICompanyProfileType} from "../../../Interfaces/company.interface";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IStackCategorization} from "../../../Interfaces/myStack.interface";
import {mspContractsService} from "../../../services/mspContract.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {downloadDoc} from "../../../utils/downloadDoc.util";
import {getErrorFromArray} from "../../../utils/error.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {pluralizeString} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";
import Button from "../../Atoms/Button/Button.Component";
import ListItemTypography from "../../Atoms/ListItemTypography/listItemTypography.component";
import Typography from "../../Atoms/Typography/Typography.component";
import {IMenuButtonItem} from "../../Molecules/MenuButton/menuButton.component";
import BasicTabs from "../../Molecules/Tabs/tabs.component";
import CompanyExpensesUpgradeCard from "../CompanyExpenses/CompanyExpensesUpgradeCard.component";
import FileUploadModal, {IFileUploadModalProps} from "../FileUploadModal/fileUploadModal.component";
import AddStackSubscriptionsWidget from "../MSPContracts/AddStackSubscriptionsWidget/AddStackSubscriptionsWidget.component";
import ContractCostPieChart from "../MSPContracts/ContractCostPieChart/ContractCostPieChart.component";
import MspContractVideoCard from "../MSPContracts/MspContractVideoCard/MspContractVideoCard.component";
import NearestUpcomingMspContractRenewal from "../MSPContracts/NearestUpcomingMspContractRenewal/NearestUpcomingRenewal.component";
import UpcomingRenewalsWidget from "../MSPContracts/UpcomingRenewalsWidget/upcomingRenewalsWidget.component";
import YourSubscriptionsCalendarView from "../MSPContracts/YourSubscriptionsCalendarView.component.tsx/yourSubscriptionsCalendarView.component";
import YourSubscriptionsListView from "../MSPContracts/YourSubscriptionsListView/yourSubscriptionsListView.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import ConversionBadges from "./conversionsBadges.component";
import {IS_BETTERTRACKER_SITE, IS_DIRECT_IT_SITE} from "../../../constants/siteFlags";
import {DIRECT_PLAN_TYPES} from "../../../constants/profiles.constant";
import usePricingLink from "../../../hooks/usePricingLink";
import Helmet from "react-helmet";

interface IProps {
    companyId?: string;
    company_name?: string;
    company_profile_type?: ICompanyProfileType;
    company_friendly_url?: string;
    readOnly?: boolean;
    isClientMsp?: boolean;
    isViewingAsAffiliateParent?: boolean;
    isParentClaimer?: boolean;
    canEditData?: boolean;
}

const MSPTrackedSubscriptionsTab = (props: IProps) => {
    const [search, setSearch] = useState<string>("");
    const [filters, setFilters] = useState<any>({});
    const [viewMode, setViewMode] = useState<"list-view" | "calendar-view">("list-view");
    const [exportingCSV, setExportingCSV] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const theme = useTheme();
    const notify = useNotification();
    const navigate = useNavigate();
    const {activeCompany, isDirect, isBasicMSP, isDirectBasic} = useActiveCompany();
    const {hasPermissions} = usePermissions(props.companyId ? {overrideCompanyId: props.companyId} : undefined);
    const hasEditAccess =
        (hasPermissions([PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE]) || !!props.canEditData) &&
        !props.isViewingAsAffiliateParent;
    const link = usePricingLink();
    const isClientMsp = props.isClientMsp || activeCompany?.company_type === CompanyType.MSP_CLIENT;
    const isPremium = props.company_profile_type
        ? props.company_profile_type?.value === VENDOR_TYPES.PREMIUM ||
          props.company_profile_type?.value === VENDOR_TYPES.MSP_BUSINESS_PREMIUM ||
          props.company_profile_type?.value === DIRECT_PLAN_TYPES.DIRECT_PREMIUM
        : false;
    const {firstLoad} = useMspContracts(isPremium ? props.companyId : "", {
        calls: {
            all: false,
            firstLoad: true,
        },
    });
    const isLoading = isPremium ? firstLoad.isLoading : false;
    const hasNoData = isPremium ? !firstLoad.data?.subscriptions_list_view?.length && !firstLoad?.isLoading : true;
    const {allCurrencies, companyCurrency, updateCompanyCurrency} = useCurrency({companyId: props.companyId});
    const {invalidateMatchedQueries} = useQueryHelper();

    /**
     * Methods for Import Contracts CSV
     */
    const [importCSVDialogConfig, setImportCSVDialogConfig] = useState<IFileUploadModalProps>({
        open: false,
        onClose: () => setImportCSVDialogConfig(prev => ({...prev, open: false})),
        companyFriendlyUrl: activeCompany?.friendly_url ?? "",
        uploadMode: "files",
        context: "importContractsCSV",
        multiple: false,
        acceptedFormatsAndSizes: [
            {
                type: "document",
                formats: ["CSV"],
                size: 5,
            },
        ],
    });
    const openImportCSVDialog = () => setImportCSVDialogConfig(prev => ({...prev, open: true}));

    const handleClickSuggested = (suggestedProduct: IStackCategorization) => {
        const locationState = {suggestedProduct};
        if (props.isParentClaimer) {
            locationState["customer_company"] = {
                id: props.companyId,
                name: props.company_name,
                friendly_url: props.company_friendly_url,
            };
        }
        navigate(routeConfig.AddCompanyContract.path, {state: locationState});
    };

    const onSuccessExportCSV = (res: AxiosResponse) => {
        downloadDoc(res.data, `${props.company_name || "company"}_subscriptions.csv`);
        setExportingCSV(false);
    };

    const onSuccessExportTemplateCSV = (res: AxiosResponse) => {
        downloadDoc(res.data, "subscriptionUploadTemplate.csv");
    };

    const handleError = (error: AxiosError<any>) => {
        setExportingCSV(false);
        notify(getErrorFromArray(error), "Error");
    };

    const handleClickExportCsv = async () => {
        setExportingCSV(true);
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        mspContractsService.exportContractsCSV(props.companyId || "", onSuccessExportCSV, handleError);
    };

    const handleClickExportTemplateCsv = async () => {
        notify("Your download will start shortly. Please don't close the tab or your browser.", "Success");
        mspContractsService.exportContractsTemplateCSV(onSuccessExportTemplateCSV, handleError);
    };

    const fixedRateCountQuery = useQuery(
        ["fixed-rate-count", props.companyId],
        () => {
            return new Promise<{contract_names: string[]}>((resolve, reject) => {
                mspContractsService
                    .getFixedRateCount(props.companyId!)
                    .then(res => {
                        resolve(res.data);
                    })
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!props.companyId,
        },
    );
    const fixedRateCount = fixedRateCountQuery.data?.contract_names?.length || 0;
    const contractNames = fixedRateCountQuery.data?.contract_names || [];
    const manageContractsText = IS_BETTERTRACKER_SITE
        ? BETTERTRACKER_CONTRACT_TEXT
        : MSP_CONTRACT_TEXT.VENDOR_CONTRACTS;

    const handleCompanyCurrencyChange = async (selectedItem: unknown) => {
        let res = {value: "true"};
        if (fixedRateCount > 0) {
            res = await getUserConfirmation("", {
                title: "Confirm changing default currency?",
                mui: true,
                form: (
                    <>
                        <Typography
                            size={14}
                            sx={{color: "neutral.800", display: "flex", flexDirection: "column", gap: 2}}>
                            All past payments will be updated to reflect the new currency. Contracts with a fixed rate
                            will need to be updated manually. Confirm the change?
                            <b>
                                {fixedRateCount} {pluralizeString("contract", fixedRateCount)} will require manual
                                update.
                            </b>
                        </Typography>
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                gap: 0.5,
                                maxHeight: "200px",
                                overflowY: "auto",
                            }}>
                            {contractNames.map((name, index) => (
                                <Typography key={index}>{name}</Typography>
                            ))}
                        </Box>
                    </>
                ),
                okButtonText: "CONFIRM",
            });
        }
        if (res.value) {
            setLoading(true);
            const currencyItem = selectedItem as IMenuButtonItem;
            await updateCompanyCurrency(currencyItem.id, props.companyId);
            setLoading(false);
            firstLoad.refetch();
            invalidateMatchedQueries(["contracts-kpis", props.companyId]);
        }
    };

    return (
        <ErrorBoundary>
            <Helmet>
                <title>Contracts</title>
            </Helmet>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 3,
                }}>
                <Paper
                    sx={{padding: props.isViewingAsAffiliateParent ? 0 : 2}}
                    elevation={props.isViewingAsAffiliateParent ? 0 : 1}>
                    {!props.isViewingAsAffiliateParent && (
                        <PageInnerHeader
                            id="tracked-subscriptions-header"
                            title={{
                                text: manageContractsText,
                                sx: {fontSize: "26px !important", fontWeight: "600 !important"},
                            }}
                            indicator={null}
                            actions={
                                isPremium && !isLoading
                                    ? {
                                          tertiary: {
                                              id: "default-subscription",
                                              menuButtonProps: {
                                                  showDropdownIcon: true,
                                                  buttonVariant: "text",
                                                  items: allCurrencies.map(c => ({
                                                      id: c.id,
                                                      name: c.name,
                                                      customProps: {
                                                          sx:
                                                              companyCurrency?.id === c.id
                                                                  ? {
                                                                        backgroundColor: "rgba(255, 97, 32, 0.12)",
                                                                        "& *": {
                                                                            fontWeight: "600 !important",
                                                                        },
                                                                    }
                                                                  : undefined,
                                                      },
                                                      children: (
                                                          <Typography
                                                              sx={{display: "flex", alignItems: "center", gap: 1}}>
                                                              {companyCurrency?.id === c.id && (
                                                                  <CheckOutlined
                                                                      sx={{color: "secondary.800", fontSize: "20px"}}
                                                                  />
                                                              )}
                                                              {c.name} ({c.key} {c.symbol})
                                                          </Typography>
                                                      ),
                                                  })),
                                                  selectedItem: companyCurrency
                                                      ? {
                                                            id: companyCurrency.id,
                                                            name: companyCurrency.name,
                                                            children: `${companyCurrency.name} (${companyCurrency.key} ${companyCurrency.symbol})`,
                                                        }
                                                      : undefined,
                                                  onChange: handleCompanyCurrencyChange,
                                                  buttonProps: {
                                                      color: "blue",
                                                      disabled: isLoading,
                                                      sx: {
                                                          padding: "4px 8px!important",
                                                          fontSize: "11px",
                                                          color: "blue.600",
                                                          fontWeight: "600",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          lineHeight: "11px",
                                                      },
                                                  },
                                              },
                                              children: (
                                                  <>
                                                      Default Currency:{" "}
                                                      <Box
                                                          component="span"
                                                          sx={{
                                                              width: 16,
                                                              height: 16,
                                                              borderRadius: "50%",
                                                              overflow: "hidden",
                                                              display: "inline-block",
                                                              marginRight: 0.5,
                                                          }}>
                                                          <img
                                                              src={`/Media/Flags/currencies/${
                                                                  companyCurrency?.key || "USD"
                                                              }.webp`}
                                                              alt={companyCurrency?.name || "USD"}
                                                              style={{
                                                                  width: "100%",
                                                                  height: "100%",
                                                                  objectFit: "cover",
                                                              }}
                                                          />
                                                      </Box>
                                                      {companyCurrency
                                                          ? `${companyCurrency.key} ${companyCurrency.symbol}`
                                                          : "US Dollar (USD $)"}
                                                      {loading ? (
                                                          <>
                                                              <Loader inline loading version="iconBlue" />
                                                          </>
                                                      ) : null}
                                                  </>
                                              ),
                                          },
                                      }
                                    : undefined
                            }
                            hideDivider={IS_DIRECT_IT_SITE}
                        />
                    )}
                    {isPremium ? (
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                gap: 2,
                            }}>
                            {isPremium || isLoading ? (
                                <Box
                                    sx={{
                                        display: "flex",
                                        gap: 2,
                                        flexWrap: "wrap",
                                        justifyContent: "space-around",
                                        alignItems: "stretch",
                                        paddingTop: 2,
                                        "& > div:not(:last-child)": {
                                            flex: "1 0 calc(33.3% - 12px)",
                                            width: "auto",
                                        },
                                        "& > div:last-child": {
                                            flex: {xs: "1 0 calc(33.3% - 12px)", xl: "1 1 calc(25% - 12px)"},
                                            width: "auto",
                                        },
                                    }}
                                    marginTop={0}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            gap: 2,
                                            justifyContent: {xs: "center", xl: "flex-start"},
                                            border: `1px solid ${theme.palette.neutral[300]}`,
                                            borderRadius: "8px",
                                            padding: "16px 24px",
                                        }}>
                                        <ContractCostPieChart companyId={props.companyId} range="month" />
                                    </Box>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            gap: 2,
                                            justifyContent: {xs: "center", xl: "flex-start"},
                                            border: `1px solid ${theme.palette.neutral[300]}`,
                                            borderRadius: "8px",
                                            padding: "16px 24px",
                                        }}>
                                        <ContractCostPieChart companyId={props.companyId} range="year" />
                                    </Box>
                                    <Box
                                        sx={{
                                            border: `1px solid ${theme.palette.neutral[300]}`,
                                            borderRadius: "8px",
                                            padding: "16px 24px",
                                            display: "flex",
                                            justifyContent: "center",
                                            gap: 12,
                                        }}>
                                        <NearestUpcomingMspContractRenewal company_id={props.companyId} />
                                    </Box>
                                </Box>
                            ) : null}
                            <ConversionBadges companyId={props.companyId} />
                        </Box>
                    ) : isDirect ? null : (
                        <MspContractVideoCard style={{marginTop: 16, display: "flex"}} />
                    )}
                </Paper>
                {!isBasicMSP && !isDirectBasic && <CompanyExpensesUpgradeCard />}
                {!firstLoad.isLoading && !isPremium && !isClientMsp && <MspContractVideoCard />}
                {!isLoading && !props.isViewingAsAffiliateParent ? (
                    <Grid container display="grid" gridTemplateColumns="calc(50% - 12px) calc(50% - 12px)" gap={3}>
                        <Grid
                            item
                            overflow="hidden"
                            gridColumn={{
                                xs: "1 / span 2",
                                lg: isClientMsp ? "span 2" : "1 / span 1",
                            }}>
                            <UpcomingRenewalsWidget
                                companyId={props.companyId || ""}
                                fullWidth
                                containerSx={{height: 280}}
                                isPremiumMsp={isPremium}
                                isClientMsp={isClientMsp}
                            />
                        </Grid>
                        {!isClientMsp && (
                            <Grid
                                item
                                gridColumn={{
                                    xs: "1 / span 2",
                                    lg: "2 / span 1",
                                }}>
                                <AddStackSubscriptionsWidget
                                    companyId={props.companyId || ""}
                                    onClick={handleClickSuggested}
                                    containerSx={{height: 280}}
                                    isPremiumMsp={isPremium}
                                    onOpenImportCSVDialogClick={openImportCSVDialog}
                                />
                            </Grid>
                        )}
                    </Grid>
                ) : isLoading ? (
                    <Box display="flex" gap={3}>
                        <Skeleton width="calc(50% - 12px)" height={200} />
                        <Skeleton width="calc(50% - 12px)" height={200} />
                    </Box>
                ) : null}
                {!isLoading ? (
                    <Box
                        sx={{
                            padding: "24px 16px",
                            background: theme.palette.neutral[100],
                            display: "flex",
                            flexDirection: "column",
                            gap: 3,
                        }}>
                        <PageInnerHeader
                            id="your-subscriptions-header"
                            title={{
                                text: props.isViewingAsAffiliateParent
                                    ? manageContractsText
                                    : `Your ${MSP_CONTRACT_TEXT.CONTRACT}s`,
                                sx: {fontSize: "23px !important"},
                            }}
                            hideDivider={IS_DIRECT_IT_SITE}
                            indicator={
                                firstLoad.data?.subscriptions_list_view?.reduce(
                                    (prev, cur) => (prev += cur.total_contracts),
                                    0,
                                ) || 0
                            }
                            actions={{
                                primary: props.readOnly
                                    ? undefined
                                    : isPremium && hasEditAccess
                                      ? {
                                            id: "addSubscription",
                                            children: (
                                                <>
                                                    <Add sx={{marginRight: 1}} />
                                                    {MSP_CONTRACT_TEXT.ADD}
                                                </>
                                            ),
                                            onClick: () => {
                                                navigate(
                                                    routeConfig.AddCompanyContract.path,
                                                    props.isParentClaimer
                                                        ? {
                                                              state: {
                                                                  customer_company: {
                                                                      id: props.companyId,
                                                                      name: props.company_name,
                                                                      friendly_url: props.company_friendly_url,
                                                                  },
                                                              },
                                                          }
                                                        : undefined,
                                                );
                                            },
                                            // disabled: !hasEditAccess,
                                        }
                                      : !isPremium
                                        ? {
                                              id: "subscribe_now",
                                              children: <>Subscribe to Access</>,
                                              to: link,
                                              variant: "tonal",
                                              target: "_blank",
                                          }
                                        : undefined,
                                secondary:
                                    isLoading || hasNoData || props.isViewingAsAffiliateParent
                                        ? undefined
                                        : {
                                              id: "exportSubscriptions",
                                              children: (
                                                  <>
                                                      <DownloadOutlinedIcon sx={{marginRight: 1}} />
                                                      {exportingCSV ? "Exporting..." : "Export CSV"}
                                                  </>
                                              ),
                                              onClick: handleClickExportCsv,
                                              disabled: exportingCSV,
                                          },
                                tertiary: isClientMsp
                                    ? {
                                          id: "importSubscriptions",
                                          children: (
                                              <>
                                                  <ImportExportOutlinedIcon />
                                                  Import from CSV
                                              </>
                                          ),
                                          onClick: openImportCSVDialog,
                                          permissions: [PERMISSION_GROUPS.MANAGE_CONTRACTS_UPDATE],
                                      }
                                    : undefined,
                            }}
                            searchState={isPremium ? [search, setSearch] : undefined}
                            filterState={[filters, setFilters]}
                            filters={firstLoad.data?.filters}
                            filterKeysOrder={["vendors", "expiration_dates", "billing_types", "categories"]}
                            fullWidthSearch
                            filtersWrapperSx={{
                                marginTop: 0.5,
                            }}
                        />
                        {props.isViewingAsAffiliateParent ? undefined : (
                            <BasicTabs
                                hideBorder
                                hideIndicator
                                {...{
                                    tabs: [
                                        {
                                            id: "calendar-view",
                                            label: (
                                                <Box display="flex" alignItems="center">
                                                    <CalendarTodayOutlinedIcon sx={{marginRight: 1}} />
                                                    Calendar View
                                                </Box>
                                            ),
                                        },
                                        {
                                            id: "list-view",
                                            label: (
                                                <Box display="flex" alignItems="center">
                                                    <FormatListBulletedIcon sx={{marginRight: 1}} /> List View
                                                </Box>
                                            ),
                                        },
                                    ] as any,
                                    defaultTab: "list-view",
                                    handleChange: (tab: any) => {
                                        setViewMode(tab.id);
                                    },
                                }}
                                tabSx={{
                                    "&.Mui-selected": {
                                        backgroundColor: theme.palette.blue[100],
                                        color: theme.palette.blue[600],
                                        borderBottom: "none",
                                        borderRadius: "105px",
                                    },
                                    "&::before, &::after, &, & *": {
                                        border: "none",
                                    },
                                    color: theme.palette.blue[600],
                                    fontSize: "14px",
                                    lineHeight: "1.4",
                                    padding: "8px 24px 8px 16px",
                                }}
                                tabsSx={{
                                    ".MuiTabs-indicator": {
                                        display: "none",
                                    },
                                }}
                                wrapperSx={
                                    viewMode === "list-view"
                                        ? {
                                              marginTop: "-8px",
                                          }
                                        : undefined
                                }
                            />
                        )}
                        {/* {
                                tabs: props.isViewingAsAffiliateParent
                                    ? undefined
                                    : ,
                            } */}
                        {viewMode === "list-view" ? (
                            <YourSubscriptionsListView
                                companyId={props.companyId}
                                search={search}
                                filters={filters}
                                isPremiumMSP={isPremium}
                                isClientMsp={isClientMsp}
                                isCustomerParentClaimer={props.isParentClaimer}
                            />
                        ) : (
                            <YourSubscriptionsCalendarView
                                companyId={props.companyId}
                                search={search}
                                filters={filters}
                                isPremiumMSP={isPremium}
                                isCustomerParentClaimer={props.isParentClaimer}
                            />
                        )}
                    </Box>
                ) : null}
            </Box>
            {importCSVDialogConfig.open && (
                <FileUploadModal
                    {...importCSVDialogConfig}
                    title="Import CSV"
                    showEditButton={false}
                    invalidFilesPosition="top"
                    invalidFilesSx={{
                        backgroundColor: theme.palette.error[100],
                        borderColor: theme.palette.error[400],
                        "& svg": {
                            fill: theme.palette.error[600],
                        },
                    }}
                    invalidFilesTitle="Invalid file"
                    subtitle={
                        <Typography
                            fontInter
                            variant="body3"
                            sx={{
                                color: theme.palette.neutral[800],
                                fontWeight: 600,
                            }}>
                            Select or Drag and Drop your file
                        </Typography>
                    }
                    instructions={
                        <Box sx={{display: "flex", gap: 1, flexDirection: "column", padding: "10px 0"}}>
                            <Typography
                                fontInter
                                variant="body2"
                                sx={{
                                    color: theme.palette.neutral[700],
                                    fontWeight: 600,
                                    lineHeight: "120%",
                                }}>
                                Instructions
                            </Typography>
                            <List dense sx={{listStyleType: "disc", pl: 3}}>
                                <ListItemTypography>Header row must be present in the file.</ListItemTypography>
                                <ListItemTypography>
                                    Vendor Company Name (vendorName) and Product Name (productName) are required
                                </ListItemTypography>
                                <ListItemTypography>
                                    Maximum file: <span style={{fontWeight: 700}}>5 MB</span>
                                </ListItemTypography>
                                <ListItemTypography>
                                    <Button
                                        id="downloadTemplateCSV"
                                        variant="text"
                                        disableRipple
                                        sx={{
                                            padding: 0,
                                            fontSize: "16px !important",
                                            whiteSpace: "break-spaces !important",
                                            textAlign: "left !important",
                                            color: `${theme.palette.secondary[600]} !important`,
                                            fontWeight: "700px !important",
                                            background: "none !important",
                                            "&:hover,&:active,&:focus": {
                                                background: "none !important",
                                            },
                                        }}
                                        onClick={handleClickExportTemplateCsv}>
                                        You can download our sample file here.
                                    </Button>
                                </ListItemTypography>
                            </List>
                        </Box>
                    }
                    companyFriendlyUrl={(props.company_friendly_url || activeCompany?.friendly_url) ?? ""}
                    aditionalParamsConfig={{
                        fileUpload: {
                            company_id: props.companyId,
                            isClientMsp,
                        },
                    }}
                />
            )}
        </ErrorBoundary>
    );
};

export default MSPTrackedSubscriptionsTab;
