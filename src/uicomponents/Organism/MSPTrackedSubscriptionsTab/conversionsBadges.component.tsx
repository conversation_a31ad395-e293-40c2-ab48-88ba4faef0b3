import {InfoOutlined} from "@mui/icons-material";
import {useTheme} from "@mui/material";
import useCurrency from "../../../hooks/useCurrency";
import {formatDate, FULL_DATE_AT_TIME} from "../../../utils/formatDate";
import Badge from "../../Atoms/Badge/badge.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";

interface Props {
    companyId?: string;
}

export default function ConversionBadges({companyId}: Props) {
    const {formatCurrency, companyCurrency, exchangeRates} = useCurrency({companyId});
    const theme = useTheme();

    if (!companyCurrency) return null;

    // Get the rates from the exchange rates object
    const rates = exchangeRates.exchange_rates?.[0]?.rates || {};

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                alignItems: "center",
                flexWrap: "wrap",
            }}>
            {Object.entries(rates)
                .filter(([k]) => k !== companyCurrency?.key)
                .map(([currencyKey, currencyData]) => (
                    <Badge key={currencyKey} padding="5px 8px" color="neutral">
                        <CPTooltip title={`Full Rate: ${currencyData.rate}`} placement={"bottom-end"}>
                            <Box
                                sx={{
                                    display: "flex",
                                    gap: 0.5,
                                    alignItems: "center",
                                }}>
                                <Typography
                                    size={12}
                                    weight={600}
                                    sx={{
                                        color: "neutral.600",
                                        whiteSpace: "nowrap",
                                    }}>
                                    {`${companyCurrency.key} ${companyCurrency.symbol}`}
                                    <span style={{color: theme.palette.neutral[800]}}>1</span>
                                </Typography>
                                <Typography
                                    size={12}
                                    weight={700}
                                    sx={{
                                        color: "neutral.500",
                                        textAlign: "center",
                                    }}>
                                    =
                                </Typography>
                                <Typography
                                    size={12}
                                    weight={600}
                                    sx={{
                                        color: "neutral.600",
                                        whiteSpace: "nowrap",
                                    }}>
                                    {`${currencyKey} ${currencyData.symbol}`}
                                    <span style={{color: theme.palette.neutral[800]}}>
                                        {formatCurrency(currencyData.rate, currencyKey)}
                                    </span>
                                </Typography>
                            </Box>
                        </CPTooltip>
                    </Badge>
                ))}
            {exchangeRates?.updated_at && Object.keys(rates).length > 0 && (
                <Box
                    sx={{
                        marginLeft: 2,
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                    }}>
                    <Typography
                        size={12}
                        sx={{
                            color: theme.palette.neutral[700],
                            textAlign: "center",
                        }}>
                        Last Updated:{" "}
                        <b>{formatDate(exchangeRates.exchange_rates?.[0]?.last_rate_update, FULL_DATE_AT_TIME)}</b>
                    </Typography>
                    <Box
                        tooltip={{
                            title: (
                                <Box sx={{display: "flex", gap: 1, alignItems: "center"}}>
                                    <InfoOutlined sx={{fontSize: 14, color: "blue.600"}} />
                                    <Typography size={14} sx={{color: "neutral.200"}}>
                                        Conversions for totals and forecasting are based on the current day exchange
                                        rates.
                                    </Typography>
                                </Box>
                            ),
                        }}>
                        <InfoOutlined sx={{fontSize: 14, color: "blue.600"}} />
                    </Box>
                </Box>
            )}
        </Box>
    );
}
