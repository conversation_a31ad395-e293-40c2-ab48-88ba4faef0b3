import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import {Skeleton, useTheme} from "@mui/material";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Tooltip from "@mui/material/Tooltip";
import {AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {Link} from "react-router-dom";
import {IPopularCategory} from "../../../Interfaces/categories.interface";
import {categoryToolTip} from "../../../constants/category.constant";
import routeConfig from "../../../constants/routeConfig";
import useSettings from "../../../hooks/useSettings";
import {categoriesService} from "../../../services/category.service";
import styles from "../../../styles/pages/categoriesPage.module.sass";
import Typography from "../../Atoms/Typography/Typography.component";

const MostPopularCategories = () => {
    const [categories, setCategories] = useState<IPopularCategory[]>([]);
    const theme = useTheme();
    const {settings} = useSettings();

    const onSuccess = (res: AxiosResponse) => {
        setCategories(res.data);
    };

    useEffect(() => {
        categoriesService.getPopularCategories(onSuccess);
    }, []);

    return (
        <Grid
            id="mostPopularGrid"
            key="mostPopularGrid"
            display="flex"
            flexDirection="row"
            alignItems="start"
            justifyContent="space-evenly"
            flexWrap="wrap"
            gap={2}>
            {categories.length === 0
                ? Array(4)
                      .fill(null)
                      .map((_, i) => (
                          <Skeleton key={i} variant="rounded" className={styles.popularCategoryCard} height={200} />
                      ))
                : categories
                      ?.sort((a: IPopularCategory, b: IPopularCategory) =>
                          a.name === b.name ? 0 : a.name < b.name ? -1 : 1,
                      )
                      .map((category: IPopularCategory) => (
                          <Paper
                              key={category.id}
                              className={styles.popularCategoryCard}
                              sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  position: "relative",
                              }}>
                              {settings.all_categories?.find(c => c.id === category.id)?.is_top_category ? (
                                  <Tooltip
                                      title={categoryToolTip}
                                      placement="bottom"
                                      classes={{
                                          tooltip: "bg-dark text-light p-2",
                                      }}>
                                      <Link
                                          to={routeConfig.CategoriesVendorsProducts.path.replace(
                                              ":friendly_url",
                                              category.friendly_url,
                                          )}>
                                          {settings.all_categories?.find(c => c.id === category.id)
                                              ?.is_top_category && (
                                              <img
                                                  src="Media/ParentCategoryBadge.png"
                                                  alt="parentCategoryBatch"
                                                  style={{position: "absolute", top: "0px", left: "0px", width: "25px"}}
                                              />
                                          )}
                                          <Typography
                                              variant="body1"
                                              color="#1F2D3E"
                                              marginY={4}
                                              borderBottom={`2px solid ${
                                                  category.color || (theme.palette as any).blue["main"]
                                              }`}>
                                              {category.name}
                                          </Typography>
                                      </Link>
                                  </Tooltip>
                              ) : (
                                  <Link
                                      to={routeConfig.CategoriesVendorsProducts.path.replace(
                                          ":friendly_url",
                                          category.friendly_url,
                                      )}>
                                      <Typography
                                          variant="body1"
                                          color="#1F2D3E"
                                          marginY={4}
                                          borderBottom={`2px solid ${
                                              category.color || (theme.palette as any).blue["main"]
                                          }`}>
                                          {category.name}
                                      </Typography>
                                  </Link>
                              )}
                              {category["sub-categories"]
                                  ?.sort((a: IPopularCategory, b: IPopularCategory) =>
                                      a.number_of_products > b.number_of_products
                                          ? -1
                                          : a.number_of_products === b.number_of_products
                                          ? 0
                                          : 1,
                                  )
                                  .slice(0, 5)
                                  .sort((a: IPopularCategory, b: IPopularCategory) =>
                                      a.name === b.name ? 0 : a.name < b.name ? -1 : 1,
                                  )
                                  .map(subcategory =>
                                      settings.all_categories?.find(c => c.id === subcategory.id)?.is_top_category ? (
                                          <Tooltip
                                              key={subcategory.id}
                                              title={categoryToolTip}
                                              placement="bottom"
                                              classes={{
                                                  tooltip: "bg-dark text-light p-2",
                                              }}>
                                              <Link
                                                  to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                      ":friendly_url",
                                                      subcategory.friendly_url,
                                                  )}
                                                  className="py-2">
                                                  <Typography variant="body2">
                                                      {subcategory.name}
                                                      {settings.all_categories?.find(c => c.id === subcategory.id)
                                                          ?.is_top_category && (
                                                          <img
                                                              src="Media/SubCategoryBadge.png"
                                                              alt="subCategoryBadge"
                                                              style={{marginLeft: "5px"}}
                                                          />
                                                      )}
                                                  </Typography>
                                              </Link>
                                          </Tooltip>
                                      ) : (
                                          <Link
                                              key={subcategory.id}
                                              to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                  ":friendly_url",
                                                  subcategory.friendly_url,
                                              )}
                                              className="py-2">
                                              <Typography variant="body2">
                                                  {subcategory.name}
                                                  {settings.all_categories?.find(c => c.id === subcategory.id)
                                                      ?.is_top_category && (
                                                      <img
                                                          src="Media/SubCategoryBadge.png"
                                                          alt="subCategoryBadge"
                                                          style={{marginLeft: "5px"}}
                                                      />
                                                  )}
                                              </Typography>
                                          </Link>
                                      ),
                                  )}
                              <Link
                                  to={routeConfig.CategoriesVendorsProducts.path.replace(
                                      ":friendly_url",
                                      category.friendly_url,
                                  )}
                                  className="pt-3 pb-2"
                                  style={{borderTop: `1px solid ${theme.palette.neutral[400]}`}}>
                                  <Typography variant="body2" fontSize="14px !important">
                                      See all from{" "}
                                      <strong>
                                          {category.name} <ChevronRightIcon />
                                      </strong>
                                  </Typography>
                              </Link>
                          </Paper>
                      ))}
        </Grid>
    );
};

export default MostPopularCategories;
