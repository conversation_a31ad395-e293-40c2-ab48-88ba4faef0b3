import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import InfoOutlined from "@mui/icons-material/InfoOutlined";
import {useTheme} from "@mui/material";
import {useQueryClient} from "@tanstack/react-query";
import {AxiosResponse} from "axios";
import {useCallback, useEffect, useMemo, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {formatPlural} from "../../../components/FormattedPlural/formatPlural.util";
import {ContactListTypes} from "../../../enums/contactListTypes.enum";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useCompanyProfile from "../../../hooks/useCompanyProfile";
import useNotification from "../../../hooks/useNotification";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IContactList, ISelectedCompany} from "../../../Interfaces/massMessaging.interface";
import {ICompanyPartner} from "../../../Interfaces/oneToOneChat.interface";
import {contactListsService} from "../../../services/contactLists.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Box from "../../Atoms/Box/Box.component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import MyPartnersSearch from "../../Molecules/MyPartnersSearch/myPartnersSearch.component";

interface IContactListForm {
    name: string;
}
interface IProps {
    show: boolean;
    list: IContactList | undefined;
    temporaryListItems?: ISelectedCompany[];
    onClose: () => void;
    onSuccess: (list: IContactList, isNewList: boolean, isFromTemporary: boolean) => void;
    onCreateTemporaryList: (selected_companies: ISelectedCompany[]) => void;
}
export default function CreateContactListModal({
    show = false,
    list,
    temporaryListItems = undefined,
    onClose,
    onSuccess,
    onCreateTemporaryList,
}: IProps) {
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const theme = useTheme();
    const notify = useNotification();
    const methods = useForm<IContactListForm>();
    const queryClient = useQueryClient();
    const {activeCompany} = useActiveCompany();
    const friendly_url = activeCompany?.friendly_url;
    const {companyInfo} = useCompanyProfile({
        groupsToCheck: {
            edit: [PERMISSION_GROUPS.MASS_MESSAGING_UPDATE],
            read: [PERMISSION_GROUPS.MASS_MESSAGING_UPDATE],
        },
        companyFriendlyUrl: friendly_url || "",
    });
    const [selectedCompanies, setSelectedCompanies] = useState<ISelectedCompany[]>(temporaryListItems ?? []);
    const [showInstructions, setShowInstructions] = useState<boolean>(true);
    const [companiesCounterMessage, setCompaniesCounterMessage] = useState<React.ReactNode | string>("");

    const handlePartnerSelectionChange = useCallback((partner: ICompanyPartner, isSelected: boolean) => {
        const partnership_id = partner.partner?.id || "";
        const contacts_ids = partner?.contacts_ids ?? [];
        setSelectedCompanies(prev => [
            ...prev.filter(company => company.partnership_id !== partnership_id),
            ...(isSelected
                ? [
                      {
                          partnership_id,
                          contacts_ids,
                      },
                  ]
                : []),
        ]);
    }, []);

    useEffect(() => {
        setCompaniesCounterMessage(() => {
            if (selectedCompanies.length === 0) {
                return "No companies selected yet";
            }
            const contacts = Array.from(
                new Set(
                    selectedCompanies.reduce(
                        (response: string[], company) => [...response, ...company.contacts_ids],
                        [],
                    ),
                ),
            );
            return (
                <>
                    <b>{contacts.length}</b> selected {formatPlural(contacts.length, "contacts", {one: "contact"})} from{" "}
                    <b>{selectedCompanies.length}</b>{" "}
                    {formatPlural(selectedCompanies.length, "companies", {one: "company"})}.
                </>
            );
        });
    }, [selectedCompanies]);

    const onSuccessSavedContactList = ({data}: AxiosResponse) => {
        const isFromTemporary = !!list && list.type === ContactListTypes.TEMPORARY;
        const isNewList = !list || isFromTemporary;
        notify(`Contact list was ${isNewList ? "created" : "changed"} successfully`, "Success");
        onSuccess && onSuccess(data, isNewList, isFromTemporary);
        onClose();
        setIsSaving(false);
    };

    const onErrorSavingContactList = err => {
        notify(getErrorFromArray(err), "Error");
        setIsSaving(false);
        onClose();
    };

    const validateFields = (name: string): boolean => {
        methods.clearErrors();
        if (selectedCompanies.length === 0) {
            notify("You must select at least one company.", "Error");
            return false;
        }
        if (name === "" && !!list && list.type !== "TEMPORARY") {
            notify("The name field is required for this list.", "Error");
            methods.setError("name", {type: "custom"});
            return false;
        }
        return true;
    };

    const handleCreateOrUpdateContactList = () => {
        if (!companyInfo?.id) return;
        const allValues = methods.getValues();
        const name = allValues.name.replace(/[^ a-zA-Z0-9]/g, "");
        if (!validateFields(name)) return;
        if (name === "") {
            onCreateTemporaryList(selectedCompanies);
            onClose();
            return;
        }
        setIsSaving(true);
        if (!!list && list.type !== ContactListTypes.TEMPORARY) {
            contactListsService.updateContactList(
                companyInfo.id,
                list.id,
                allValues.name.trim(),
                selectedCompanies.map(company => company.partnership_id),
                onSuccessSavedContactList,
                onErrorSavingContactList,
            );
        } else {
            contactListsService.createNewContactList(
                companyInfo?.id,
                allValues.name.trim(),
                selectedCompanies.map(company => company.partnership_id),
                onSuccessSavedContactList,
                onErrorSavingContactList,
            );
        }
    };

    const loadContactListdata = () => {
        if (!list || !companyInfo?.id) return;
        setIsSaving(true);
        contactListsService.getContactList(
            companyInfo.id,
            {
                contactListType: list.type,
                contactListId: list.id,
                includePartnerships: true,
            },
            (res: AxiosResponse) => {
                setSelectedCompanies(
                    res.data.partners.map(partner => ({
                        partnership_id: `${partner.id}`,
                        contacts_ids: partner.contacts_ids ?? [],
                    })),
                );
                setIsSaving(false);
            },
            err => {
                notify(getErrorFromArray(err), "Error");
                setIsSaving(false);
                onClose();
            },
        );
    };

    useEffect(() => {
        if (!show) {
            queryClient.resetQueries(["myPartners-contact-list-items"]);
            return;
        }
        setShowInstructions(!list || list.type === ContactListTypes.TEMPORARY);
        setSelectedCompanies(temporaryListItems ?? []);
        if (!list || list.type === ContactListTypes.TEMPORARY) {
            methods.setValue("name", "");
        } else {
            methods.setValue("name", list.name);
            loadContactListdata();
        }
    }, [show]);

    const myPartnerSearch = useMemo(
        () => (
            <MyPartnersSearch
                selectedPartners={selectedCompanies.map(company => company.partnership_id)}
                onTogglePartnerSelection={handlePartnerSelectionChange}
            />
        ),
        [selectedCompanies, handlePartnerSelectionChange],
    );

    return (
        <ModalComponent
            open={show}
            onClose={() => onClose()}
            onSuccess={handleCreateOrUpdateContactList}
            modalTheme="blue"
            okButtonText="SAVE"
            okButtonStartIcon={<CheckOutlinedIcon htmlColor="white" fontSize="small" />}
            cancelButtonText="CANCEL"
            title={!list ? "Create Contact List" : "Edit Contact List"}
            loading={isSaving}
            justifyButtons="flex-start"
            dialogSx={{
                "& .MuiDialog-paper": {
                    padding: "16px !important",
                    overflow: "unset",
                    display: "flex",
                    flexDirection: "column",
                    gap: "16px",
                },
            }}
            content={
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                    }}>
                    {showInstructions && (
                        <Box
                            sx={{
                                backgroundColor: theme.palette.blue[100],
                                borderRadius: "8px",
                                border: `solid 1px ${theme.palette.blue[300]}`,
                                display: "flex",
                                alignItems: "center",
                                padding: 2,
                                gap: 2,
                                "& svg": {
                                    width: "24px !important",
                                    height: "24px !important",
                                    fill: theme.palette.blue[500],
                                },
                            }}>
                            <InfoOutlined htmlColor={theme.palette.secondary[500]} />
                            <Typography
                                fontInter
                                variant="body3"
                                color={theme.palette.neutral[800]}
                                sx={{fontWeight: 400}}>
                                To send a message without saving contacts, skip naming the list. If you want to save
                                selected contacts for future use, name the list before clicking Save.
                            </Typography>
                            <IconButton
                                sx={{
                                    "& svg": {
                                        width: "24px",
                                        height: "24px",
                                        fill: theme.palette.neutral[600],
                                    },
                                }}
                                onClick={() => setShowInstructions(false)}>
                                <CloseOutlinedIcon />
                            </IconButton>
                        </Box>
                    )}
                    <FormProvider {...methods}>
                        <TextBoxComponent
                            id="name"
                            maxLength={75}
                            placeholder="Input a name to save this contact list for later"
                            isRequired
                            validateOnTheFly={false}
                            label={`List Name ${!list ? "(optional)" : ""}`}
                            containerSx={{
                                "& .css-1w6vse8-MuiInputBase-root-MuiInput-root:before": {
                                    borderBottom: `solid 1px ${theme.palette.neutral[500]}`,
                                },
                            }}
                            inputStyles={{
                                padding: "24px 16px 8px 16px !important",
                                lineHeight: "24px !important",
                                "&::placeholder, &::-webkit-input-placeholder": {
                                    fontSize: "14px !important",
                                    lineHeight: "24px !important",
                                    opacity: 1,
                                    transform: "translate(-33px, 0px) scale(0.875)",
                                    "@media(max-width:768px)": {
                                        transform: "translate(-16px, 0px) scale(0.875)",
                                    },
                                    color: `${theme.palette.neutral[600]} !important`,
                                },
                            }}
                            InputLabelProps={{
                                shrink: true,
                                sx: {
                                    fontWeight: 400,
                                    lineHeight: "16px !important",
                                    transform: "translate(16px, 8px) scale(0.95) !important",
                                },
                            }}
                        />
                    </FormProvider>
                    <Box>
                        <Typography
                            fontInter
                            sx={{
                                color: theme.palette.neutral[700],
                            }}
                            variant={"body3"}>
                            {companiesCounterMessage}
                        </Typography>
                    </Box>
                    {!!show && myPartnerSearch}
                </Box>
            }
        />
    );
}
