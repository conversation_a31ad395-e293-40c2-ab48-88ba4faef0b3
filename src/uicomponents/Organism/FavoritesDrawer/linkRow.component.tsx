import {StarOutlined, StarOutlineOutlined} from "@mui/icons-material";
import {Dispatch, SetStateAction} from "react";
import {IUserFavoriteData} from "../../../Interfaces/company.interface";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps {
    link: {key: string; name: string};
    setFavorites: Dispatch<SetStateAction<IUserFavoriteData[]>>;
    reachedMaxFavorites: boolean;
}

export default function LinkRow({link, setFavorites, reachedMaxFavorites}: IProps) {
    return (
        <Box
            key={link.key}
            sx={{
                padding: 2,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                border: "1px solid",
                borderColor: "neutral.300",
                boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.1)",
                borderRadius: "8px",
            }}>
            <Typography fontInter size={16} weight={500} color="neutral.700">
                {link.name}
            </Typography>
            <Box
                tooltip={{
                    title: reachedMaxFavorites
                        ? "You have reached the maximum number of favorites"
                        : "Add to favorites",
                }}>
                <Button
                    id={link.key + "-toggleFav"}
                    disabled={reachedMaxFavorites}
                    sx={{
                        padding: 0,
                        position: "relative",
                        "&:hover": {bgcolor: "transparent"},
                        "& svg[data-type='outlined']": {display: "block"},
                        "&:hover svg[data-type='outlined']": {display: "none"},
                        "& svg[data-type='filled']": {display: "none"},
                        "&:hover svg[data-type='filled']": {display: "block"},
                    }}
                    onClick={() => {
                        setFavorites(prev => {
                            const newFavorites = [...prev];
                            newFavorites.push({
                                key: link.key,
                                order: newFavorites.length,
                            });
                            return newFavorites;
                        });
                    }}>
                    <StarOutlined
                        data-type="filled"
                        sx={{width: "24px", height: "24px", color: "#F29202", opacity: 0.5}}
                    />
                    <StarOutlineOutlined
                        data-type="outlined"
                        sx={{width: "24px", height: "24px", color: "neutral.600"}}
                    />
                </Button>
            </Box>
        </Box>
    );
}
