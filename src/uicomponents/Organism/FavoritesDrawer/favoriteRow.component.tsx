import {Drag<PERSON><PERSON>leOutlined, StarOutlined} from "@mui/icons-material";
import type {Identifier, XYCoord} from "dnd-core";
import {CSSProperties, Dispatch, SetStateAction, useRef} from "react";
import {useDrag, useDrop} from "react-dnd";
import {IUserFavoriteData} from "../../../Interfaces/company.interface";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";

interface IProps {
    favorite: IUserFavoriteData;
    names: {key: string; name: string}[];
    index: number;
    moveRow: (dragIndex: number, hoverIndex: number) => void;
    setFavorites: Dispatch<SetStateAction<IUserFavoriteData[]>>;
}

export default function FavoriteRow({favorite, names, index, moveRow, setFavorites}: IProps) {
    const ref = useRef<HTMLDivElement>(null);
    const [{handlerId}, drop] = useDrop<IUserFavoriteData, void, {handlerId: Identifier | null}>({
        accept: "favorite-row",
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item: IUserFavoriteData, monitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.order;
            const hoverIndex = index;
            if (dragIndex === hoverIndex) {
                return;
            }
            const hoverBoundingRect = ref.current?.getBoundingClientRect();
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
            const clientOffset = monitor.getClientOffset();
            const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }
            moveRow(dragIndex, hoverIndex);
            item.order = hoverIndex;
        },
    });
    const [{css, isDragging}, drag] = useDrag<IUserFavoriteData, void, {css: CSSProperties; isDragging: boolean}>(
        () => ({
            type: "favorite-row",
            item: () => favorite,
            collect: monitor => ({
                css: {
                    opacity: monitor.isDragging() ? 0.5 : 1,
                },
                isDragging: monitor.isDragging(),
            }),
        }),
        [],
    );
    drag(drop(ref));
    const {opacity} = css;
    return (
        <Box
            ref={ref}
            data-handler-id={handlerId}
            sx={{
                padding: 2,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                border: "1px solid",
                borderColor: "neutral.300",
                boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.1)",
                borderRadius: "8px",
                opacity,
                cursor: isDragging ? "grabbing!important" : "grab",
            }}>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                }}>
                <DragHandleOutlined sx={{width: "24px", height: "24px", color: "neutral.500"}} />
                <Typography fontInter size={16} weight={500} color="neutral.700">
                    {names.find(fav => fav.key === favorite.key)?.name}
                </Typography>
            </Box>
            <Button
                id={favorite.key + "-toggleFav"}
                sx={{
                    padding: 0,
                    position: "relative",
                    "&:hover": {bgcolor: "transparent"},
                    "& svg[data-type='outlined']": {display: "block"},
                    "&:hover svg[data-type='outlined']": {display: "none"},
                    "& svg[data-type='filled']": {display: "none"},
                    "&:hover svg[data-type='filled']": {display: "block"},
                }}
                onClick={() => {
                    setFavorites(prev => {
                        const newFavorites = [...prev].filter(fav => fav.key !== favorite.key);
                        return newFavorites;
                    });
                }}>
                <StarOutlined data-type="filled" sx={{width: "24px", height: "24px", color: "#F29202", opacity: 0.5}} />
                <StarOutlined data-type="outlined" sx={{width: "24px", height: "24px", color: "#F29202"}} />
            </Button>
        </Box>
    );
}
