import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {Skeleton, useTheme} from "@mui/material";
import Grid from "@mui/material/Grid";
import Tooltip from "@mui/material/Tooltip";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {Link} from "react-router-dom";
import {ICategory} from "../../../Interfaces/categories.interface";
import {categoryToolTip} from "../../../constants/category.constant";
import routeConfig from "../../../constants/routeConfig";
import useNotification from "../../../hooks/useNotification";
import useSettings from "../../../hooks/useSettings";
import useWindowSize from "../../../hooks/useWindowSize";
import {UPDATE_SETTINGS} from "../../../reducers/settings.reducer";
import {categoriesService} from "../../../services/category.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Accordion from "../../Atoms/Accordion/Accordion.component";
import AccordionDetails from "../../Atoms/Accordion/AccordionDetails.component";
import AccordionSummary from "../../Atoms/Accordion/AccordionSummary.component";
import Typography from "../../Atoms/Typography/Typography.component";
import styles from "./allCategoriesCollapse.module.sass";

//? This component will show all available categories in our application
//? and display them by parent category with all subcategories listed in
//? a collapsible region. All categories link to respective category page

const AllCategoriesCollapse = () => {
    const [expandedCategory, setExpandedCategory] = useState<string>("");
    const notify = useNotification();
    const theme = useTheme();
    const windowSize = useWindowSize();
    const isTwoRows = windowSize.width > theme.breakpoints.values.md;
    const {settings, updateSettings} = useSettings();
    const parentCategories = settings.all_categories?.filter(c => !c.parent_id);

    const onSuccess = (res: AxiosResponse) => {
        updateSettings(UPDATE_SETTINGS, {all_categories: res.data});
    };

    const handleError = (err: AxiosError) => {
        notify(getErrorFromArray(err), "Error");
    };

    const renderAccordion = (parentCategory: ICategory, subcategories: ICategory[]) => {
        return (
            <Grid item key={parentCategory.id}>
                <Accordion
                    className={styles.categoryWrapper}
                    elevation={0}
                    expanded={expandedCategory === parentCategory.id}
                    onChange={() =>
                        setExpandedCategory(parentCategory.id === expandedCategory ? "" : parentCategory.id)
                    }>
                    <AccordionSummary className={styles.categorySummary}>
                        <div className="d-flex flex-row justify-content-between align-items-end w-100">
                            <div className={styles.categoryInfo}>
                                {parentCategory.is_top_category ? (
                                    <Tooltip
                                        title={categoryToolTip}
                                        placement="bottom"
                                        classes={{
                                            tooltip: "bg-dark text-light p-2",
                                        }}>
                                        <Link
                                            to={routeConfig.CategoriesVendorsProducts.path.replace(
                                                ":friendly_url",
                                                parentCategory?.friendly_url || "",
                                            )}
                                            className={styles.parentCategoryLink}>
                                            <Typography variant="body1" title={categoryToolTip}>
                                                {parentCategory.name}
                                                <img
                                                    src="/Media/SubCategoryBadge.png"
                                                    alt="subCategoryBadge"
                                                    style={{marginLeft: "5px"}}
                                                />
                                            </Typography>
                                        </Link>
                                    </Tooltip>
                                ) : (
                                    <Link
                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                            ":friendly_url",
                                            parentCategory?.friendly_url || "",
                                        )}
                                        className={styles.parentCategoryLink}
                                        title={categoryToolTip}>
                                        <Typography variant="body1">{parentCategory.name}</Typography>
                                    </Link>
                                )}
                                <Typography variant="body4">
                                    <strong>{subcategories.length.toString().padStart(2, "0")} </strong>
                                    Sub-categories
                                </Typography>
                            </div>
                            {expandedCategory === parentCategory?.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </div>
                    </AccordionSummary>
                    <AccordionDetails className={styles.categoryDetails}>
                        {subcategories.map(subcategory =>
                            subcategory.is_top_category ? (
                                <Tooltip
                                    key={subcategory.id}
                                    title={categoryToolTip}
                                    placement="bottom"
                                    classes={{
                                        tooltip: "bg-dark text-light p-2",
                                    }}>
                                    <Link
                                        to={routeConfig.CategoriesVendorsProducts.path.replace(
                                            ":friendly_url",
                                            subcategory?.friendly_url || "",
                                        )}
                                        className={styles.subcategoryLink}>
                                        <Typography variant="body3">
                                            {subcategory.name}
                                            {subcategory.is_top_category && (
                                                <img
                                                    src="/Media/SubCategoryBadge.png"
                                                    alt="subCategoryBadge"
                                                    style={{marginLeft: "5px"}}
                                                    title={categoryToolTip}
                                                />
                                            )}
                                        </Typography>
                                    </Link>
                                </Tooltip>
                            ) : (
                                <Link
                                    key={subcategory.id}
                                    to={routeConfig.CategoriesVendorsProducts.path.replace(
                                        ":friendly_url",
                                        subcategory?.friendly_url || "",
                                    )}
                                    className={styles.subcategoryLink}>
                                    <Typography variant="body3">
                                        {subcategory.name}
                                        {subcategory.is_top_category && (
                                            <img
                                                src="/Media/SubCategoryBadge.png"
                                                alt="subCategoryBadge"
                                                style={{marginLeft: "5px"}}
                                            />
                                        )}
                                    </Typography>
                                </Link>
                            ),
                        )}
                    </AccordionDetails>
                </Accordion>
            </Grid>
        );
    };

    useEffect(() => {
        categoriesService.getAllCategories(onSuccess, handleError);
    }, []);

    return (
        <Grid container display="flex" flexDirection="row" columnSpacing={2}>
            {parentCategories?.length ? (
                <>
                    <Grid item xs={12} md={6}>
                        {parentCategories
                            ?.filter((c, i) => !c.parent_id && (isTwoRows ? i % 2 === 0 : true))
                            ?.map(c => {
                                const subcategories = settings?.all_categories?.filter(
                                    subcategory => subcategory.parent_id === c.id,
                                );
                                if (!subcategories?.length) return null;
                                return renderAccordion(c, subcategories);
                            })}
                    </Grid>
                    {isTwoRows && (
                        <Grid item xs={12} md={6}>
                            {parentCategories
                                .filter((c, i) => !c.parent_id && i % 2 !== 0)
                                ?.map(c => {
                                    const subcategories = settings.all_categories?.filter(
                                        subcategory => subcategory.parent_id === c.id,
                                    );
                                    if (!subcategories?.length) return null;
                                    return renderAccordion(c, subcategories);
                                })}
                        </Grid>
                    )}
                </>
            ) : (
                Array(18)
                    .fill(null)
                    .map((_, i) => (
                        <Grid key={i} item xs={12} md={6}>
                            <Skeleton key={i} sx={{width: "100%", height: 100}} />
                        </Grid>
                    ))
            )}
        </Grid>
    );
};

export default AllCategoriesCollapse;
