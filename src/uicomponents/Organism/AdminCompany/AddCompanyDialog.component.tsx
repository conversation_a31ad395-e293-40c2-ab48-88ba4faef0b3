import AddIcon from "@mui/icons-material/Add";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {COMPANY_TYPES_FILTER} from "../../../constants/commonStrings.constant";
import CompanyType from "../../../constants/companyType.constant";
import useProfileTypes, {IProfileType} from "../../../hooks/fetches/useCompanyProfileTypes";
import useCompanyTypes from "../../../hooks/fetches/useCompanyTypes";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import AddressAutoComplete from "../../Molecules/AddressAutocomplete/addressAutoComplete.component";
import AutoCompleteComponent from "../../Molecules/Autocomplete/Autocomplete.component";
import BrandAutocomplete from "../../Molecules/BrandAutocomplete/BrandAutocomplete.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import {COMPANY_TYPE} from "../../../enums/companyTypes.enum";

interface IProps {
    open: boolean;
    onClose?: () => void;
    onCreateAffiliateBrand?: (name?: string) => void;
    defaultValues?: Partial<ICompanyForm & {brand_name: string}>;
    disableFields?: Partial<keyof ICompanyForm>[];
}

interface ICompanyForm {
    name: string;
    description: string;
    company_type: string;
    affiliate_brand_id: string;
    address: string;
    profile_vendor_handle: string;
    affiliate_id: string;
    company_profile_types_id: string;
    subscription_status: string;
    subscription_start_date: string;
    subscription_end_date: string;
    parent_id?: string;
}

const AddCompanyDialog = (props: IProps) => {
    const {open, onClose} = props;
    const [selectedBrandInfo, setSelectedBrandInfo] = useState<{
        id: string;
        name: string;
        isNew: boolean;
        main_company_id?: string;
    } | null>(
        props.defaultValues?.affiliate_brand_id && props.defaultValues?.brand_name
            ? {id: props.defaultValues.affiliate_brand_id, name: props.defaultValues.brand_name, isNew: false}
            : null,
    );
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const theme = useTheme();
    const notify = useNotification();
    const {allCompanyTypes} = useCompanyTypes();
    const {allCompanyProfileTypes} = useProfileTypes();
    const {authState} = useAuthState();
    const defaultValues = props.defaultValues || {};
    if (props.defaultValues?.company_profile_types_id) {
        const foundValue = allCompanyProfileTypes?.find(
            t => t.id === String(props.defaultValues?.["company_profile_types_id"]),
        )?.value;
        defaultValues["company_profile_types_id"] = foundValue || props.defaultValues["company_profile_types_id"];
    }
    const methods = useForm<ICompanyForm>({
        defaultValues,
        mode: "onChange",
    });
    const companyTypeId = methods.watch("company_type");
    const isFranchiseMsp = companyTypeId
        ? companyTypeId === CompanyType.FRANCHISE_MSP
        : props.defaultValues?.company_type === CompanyType.FRANCHISE_MSP;
    const isFranchiseBrand = companyTypeId === CompanyType.FRANCHISE_CORPORATE_MSP;
    const isValid = methods.formState.isValid;
    const companyTypeIsVendorType = allCompanyTypes?.find(t => t.value === companyTypeId)?.type_is_of_vendor;
    const companyTypeIsInternalIT =
        allCompanyTypes?.find(t => t.value === companyTypeId)?.value === COMPANY_TYPE.DIRECT;

    const handleError = (error: AxiosError<any>) => {
        const errMsg: string = getErrorFromArray(error);
        notify(errMsg, "Error");
        setIsSaving(false);
    };

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        setIsSaving(true);
        const values: any = methods.getValues();
        let successfullyStoredAffiliateBrand = true;
        if (values["affiliate_brand_id"] && selectedBrandInfo?.isNew) {
            const requestBody = {
                is_corporate_location: 0,
                company_type: companyTypeId,
                id: authState?.company_id,
            };
            if (selectedBrandInfo?.isNew || !selectedBrandInfo) {
                delete requestBody["affiliate_brand_id"];
                requestBody["affiliate_brand_name"] = selectedBrandInfo?.name;
            }
            await companyService.updateAffiliateBrandDetails(
                authState?.company_friendly_url ?? "",
                requestBody,
                (res: AxiosResponse) => {
                    values["affiliate_brand_id"] = res.data.affiliate_brand.id;
                },
                (err: AxiosError) => {
                    handleError(err);
                    successfullyStoredAffiliateBrand = false;
                },
            );
        }
        if (!values["description"]) {
            delete values["description"];
        }
        if (!successfullyStoredAffiliateBrand) return;
        if (values["address"]) {
            values["state"] = values["address_state"];
            values["country"] = values["address_country"];
            values["zip"] = values["address_zip"];
            values["city"] = values["address_city"];
            delete values["address_state"];
            delete values["address_country"];
            delete values["address_zip"];
            delete values["address_city"];
        }
        companyService.storeCompany(
            values,
            () => {
                setIsSaving(false);
                notify("Successfully created company", "Success");
                props.onClose?.();
            },
            handleError,
        );
    };

    useEffect(() => {
        if (!isFranchiseMsp && selectedBrandInfo?.name) {
            setSelectedBrandInfo(null);
        }
    }, [isFranchiseMsp]);

    return (
        <>
            <ModalComponent
                justifyButtons="flex-start"
                open={open}
                onClose={onClose as any}
                icon={<AddIcon />}
                title={"New Company"}
                contentClassName="mt-0"
                modalTheme="blue"
                maxWidth="md"
                okButtonText="Save"
                loading={isSaving}
                okButtonDisabled={!isValid}
                onSuccess={handleSubmit}
                dialogSx={{
                    ".MuiDialog-paper": {
                        overflow: "unset",
                        maxWidth: "800px !important",
                        maxHeight: "100%",
                        width: "100%",
                    },
                    ".MuiDialogContent-root": {
                        overflow: "unset",
                    },
                }}
                content={
                    <Box marginTop={2}>
                        <FormProvider {...methods}>
                            <Box
                                component="form"
                                sx={{
                                    display: "grid",
                                    gridTemplateColumns: {xs: "1fr", md: "repeat(6, 1fr)"},
                                    gap: 2,
                                }}>
                                <Box gridColumn="span 6">
                                    <Typography
                                        variant="body3"
                                        fontInter
                                        color={theme.palette.neutral[700]}
                                        fontWeight={500}>
                                        Company Information
                                    </Typography>
                                </Box>
                                <TextBoxComponent
                                    id="name"
                                    label="Company Name"
                                    placeholder="Input Company Name"
                                    containerSx={{
                                        gridColumn: "span 6",
                                    }}
                                    isRequired
                                    readOnly={props.disableFields?.includes("name")}
                                    prependInputText={
                                        isFranchiseBrand
                                            ? ""
                                            : selectedBrandInfo?.name
                                              ? selectedBrandInfo.name + "-"
                                              : undefined
                                    }
                                    minLength={1}
                                    validateOnTheFly
                                />
                                <TextBoxComponent
                                    id="description"
                                    multiline
                                    label="Description"
                                    placeholder="Input company description"
                                    containerSx={{
                                        gridColumn: "span 6",
                                    }}
                                    readOnly={props.disableFields?.includes("description")}
                                    maxLength={4000}
                                    validateOnTheFly
                                    showCharacterCount
                                    rows={4}
                                    maxRows={20}
                                />
                                <AutoCompleteComponent
                                    id="company_type"
                                    label="Type"
                                    placeholder="Input type"
                                    options={allCompanyTypes
                                        .map(t => ({id: t.value, label: t.label}))
                                        .filter(ct => !COMPANY_TYPES_FILTER.includes(ct.id))}
                                    readOnly={props.disableFields?.includes("company_type")}
                                    sx={{
                                        gridColumn: isFranchiseMsp ? "span 2" : "span 3",
                                    }}
                                    isRequired
                                    validateOnTheFly
                                />
                                {(isFranchiseMsp || isFranchiseBrand) && (
                                    <BrandAutocomplete
                                        id="affiliate_brand_id"
                                        label="Brand"
                                        placeholder="Input brand"
                                        readOnly={props.disableFields?.includes("affiliate_brand_id")}
                                        sx={{
                                            gridColumn: isFranchiseBrand ? "span 3" : "span 2",
                                        }}
                                        isRequired
                                        company_type="MSSP"
                                        onValueChange={(id: string, oldOption, newOption: any) => {
                                            setSelectedBrandInfo({
                                                id: newOption.id,
                                                name: newOption?.name || newOption.inputValue || "",
                                                isNew: !!newOption.isNew,
                                                main_company_id: newOption.main_company_id,
                                            });
                                        }}
                                        defaultValue={props.defaultValues?.affiliate_brand_id}
                                        defaultSearch={props.defaultValues?.brand_name}
                                        freeSolo
                                        getOptionLabel={option => option.label}
                                        showCreateOption
                                        allowFreeSoloInput
                                        validateOnTheFly
                                        filterBrandsWithMainCompanyId={isFranchiseBrand}
                                    />
                                )}
                                <AddressAutoComplete
                                    id="address"
                                    label="Location"
                                    readOnly={props.disableFields?.includes("address")}
                                    sx={{gridColumn: isFranchiseMsp ? "span 2" : "span 3"}}
                                    validateOnTheFly
                                    registerDetails
                                />
                                {/* {(isFranchiseMsp || companyTypeIsVendorType) && (
                                    <TextBoxComponent
                                        id="profile_vendor_handle"
                                        label="Company Handle"
                                        placeholder="Input Company Handle"
                                        containerSx={{
                                            gridColumn: "span 3",
                                        }}
                                        readOnly={props.disableFields?.includes("profile_vendor_handle")}
                                        prependInputText={
                                            selectedBrandInfo?.name ? selectedBrandInfo.name + "-" : undefined
                                        }
                                        validateOnTheFly
                                        isRequired={true}
                                    />
                                )} */}
                                {isFranchiseMsp && (
                                    <TextBoxComponent
                                        id="affiliate_id"
                                        label="Location ID"
                                        placeholder="Input Location ID"
                                        readOnly={props.disableFields?.includes("affiliate_id")}
                                        containerSx={{
                                            gridColumn: "span 3",
                                        }}
                                        validateOnTheFly
                                    />
                                )}
                                <Box gridColumn="span 6">
                                    <Typography
                                        variant="body3"
                                        fontInter
                                        color={theme.palette.neutral[700]}
                                        fontWeight={500}>
                                        Subscription Information
                                    </Typography>
                                </Box>
                                <AutoCompleteComponent
                                    id="company_profile_types_id"
                                    label="Tier"
                                    placeholder="Input tier"
                                    options={
                                        allCompanyProfileTypes?.filter((profileType: IProfileType) =>
                                            companyTypeIsInternalIT
                                                ? profileType.value.includes(COMPANY_TYPE.DIRECT + "_")
                                                : companyTypeIsVendorType
                                                  ? profileType.label.includes("Vendor")
                                                  : profileType.label.includes("MSP"),
                                        ) || []
                                    }
                                    sx={{
                                        gridColumn: "span 3",
                                    }}
                                    isRequired
                                />
                                {/* ? - Not needed for version 1 */}
                                {/* <AutoCompleteComponent
                                    id="subscription_status"
                                    label="Status"
                                    placeholder="Input status"
                                    options={[
                                        {id: "1", label: "Enabled"},
                                        {id: "0", label: "Disabled"},
                                    ]}
                                    sx={{
                                        gridColumn: "span 3",
                                    }}
                                    isRequired
                                />
                                {isEnabled && (
                                    <>
                                        <TextBoxComponent
                                            id="subscription_start_date"
                                            label="Effective Date"
                                            placeholder="Enter effective date"
                                            type="date"
                                            containerSx={{
                                                gridColumn: "span 3",
                                            }}
                                            isRequired
                                        />
                                        <TextBoxComponent
                                            id="subscription_end_date"
                                            label="Expiry Date"
                                            placeholder="Enter expiry date"
                                            type="date"
                                            containerSx={{
                                                gridColumn: "span 3",
                                            }}
                                            minDate={methods.watch("subscription_start_date")}
                                        />
                                    </>
                                )} */}
                            </Box>
                        </FormProvider>
                    </Box>
                }
            />
        </>
    );
};

export default AddCompanyDialog;
