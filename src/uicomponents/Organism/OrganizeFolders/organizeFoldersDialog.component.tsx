import DragHandleIcon from "@mui/icons-material/DragHandle";
import {Skeleton} from "@mui/material";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/material/styles/useTheme";
import {useQueryClient, type UseQueryResult} from "@tanstack/react-query";
import type {AxiosError} from "axios";
import {useEffect, useState} from "react";
import {useDrag, useDrop} from "react-dnd";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../hooks/useNotification";
import type {IFolder} from "../../../Interfaces/folders.interface";
import {folderServices} from "../../../services/folder.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Typography from "../../Atoms/Typography/Typography.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";

interface IProps {
    open: boolean;
    onClose: () => void;
    companyId: string;
    onSave: Function;
    allFoldersData: UseQueryResult<IFolder[]>;
}

interface DraggableItemProps {
    id: string;
    label: string;
    index: number;
    moveItem: (fromIndex: number, toIndex: number) => void;
}

interface Ioption {
    id: string;
    label: string;
    order: number;
}

const OrganizeFoldersDailog = (props: IProps) => {
    const methods = useForm();
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [options, setOptions] = useState<Ioption[]>([]);
    const notify = useNotification();
    const theme = useTheme();
    const queryClient = useQueryClient();

    const handleError = (err: AxiosError<any>) => {
        setIsSaving(false);
        notify(getErrorFromArray(err), "Error");
    };
    const onSuccessReorder = () => {
        notify("Folders organized successfully!", "Success");
        setIsSaving(false);
        props.onSave();
        props.onClose();
        queryClient.refetchQueries(["allFoldersData"]);
    };
    const handleSubmit = async () => {
        const updatedOptions = options.map((option, index) => ({
            ...option,
            order: index + 1,
        }));

        const json = {
            folder_order: updatedOptions.map(({id, order}) => ({id, order})),
        };
        setIsSaving(true);
        folderServices.reorderFolders(props?.companyId, json, onSuccessReorder, handleError);
    };

    const allFoldersData = props.allFoldersData.data || [];
    const isLoading = props.allFoldersData.isLoading;

    useEffect(() => {
        const mappedData =
            allFoldersData
                ?.map((item: IFolder) => {
                    return {id: item.id, label: item.folder_name, order: item?.folder_order};
                })
                .sort((a, b) => a.order - b.order) || [];
        if (mappedData.length) {
            setOptions([...mappedData]);
        }
    }, [allFoldersData]);

    return (
        <ModalComponent
            open={props.open}
            onClose={props.onClose}
            onSuccess={handleSubmit}
            title={"Organize Folders"}
            okButtonText="SAVE"
            modalTheme="blue"
            justifyButtons="flex-start"
            loading={isSaving}
            customModalBtnTracking={{
                company_id: props?.companyId,
            }}
            dialogSx={{
                ".MuiDialog-paper": {overflow: "unset", maxWidth: "500px !important", width: "100%"},
            }}
            content={
                <Grid marginTop={2} padding={"16px 0px"}>
                    <FormProvider {...methods}>
                        <Typography variant={"body2"} color={theme.palette.neutral[800]} paddingBottom={"16px"}>
                            Drag to order your folders
                        </Typography>
                        {isLoading
                            ? Array.from(new Array(5)).map((_, index) => (
                                  <Skeleton key={index} sx={{marginBottom: "8px", width: "100%", height: "40px"}} />
                              ))
                            : options?.length > 0 && <DragAndDropForm options={options} setOptions={setOptions} />}
                    </FormProvider>
                </Grid>
            }
        />
    );
};
export default OrganizeFoldersDailog;

const DragAndDropForm = ({options, setOptions}) => {
    const moveItem = (fromIndex, toIndex) => {
        setOptions((prevOptions: Ioption[]) => {
            const newOptions = [...prevOptions];
            const dragAnswer = newOptions[fromIndex];
            newOptions.splice(fromIndex, 1);
            newOptions.splice(toIndex, 0, dragAnswer);
            return newOptions;
        });
    };
    return options?.map((option: Ioption, index: number) => {
        return <DraggableItem key={option.id} id={option.id} label={option.label} index={index} moveItem={moveItem} />;
    });
};

const DraggableItem: React.FC<DraggableItemProps> = ({id, label, index, moveItem}) => {
    const theme = useTheme();

    const [{isDragging}, drag] = useDrag({
        type: "FOLDER",
        item: {id, index},
        collect: monitor => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const [{isOver}, drop] = useDrop({
        accept: "FOLDER",
        hover(draggedItem: any) {
            if (draggedItem.index !== index) {
                moveItem(draggedItem.index, index);
                draggedItem.index = index;
            }
        },
        collect: monitor => ({
            isOver: monitor.isOver(),
        }),
    });

    return (
        <div
            ref={node => drag(drop(node))}
            key={id}
            style={{
                display: "flex",
                padding: "8px",
                alignItems: "center",
                cursor: isDragging ? "grabbing" : "grab",
                backgroundColor: isOver ? theme.palette.secondary[100] : undefined,
            }}>
            <div style={{opacity: isDragging ? 0.5 : 1, display: "flex", alignItems: "center"}}>
                <DragHandleIcon htmlColor={theme.palette.neutral[500]} sx={{marginRight: 2}} />
                <Typography fontInter variant={"body4"} color={theme.palette.blue["main"]}>
                    {label}
                </Typography>
            </div>
        </div>
    );
};
