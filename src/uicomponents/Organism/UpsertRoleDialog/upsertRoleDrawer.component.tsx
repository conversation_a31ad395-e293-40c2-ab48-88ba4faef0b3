import {InfoOutlined} from "@mui/icons-material";
import {<PERSON><PERSON><PERSON>, FormControlLabel, RadioGroup, useTheme} from "@mui/material";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {useCallback, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Column} from "react-table";
import {CHANNEL_PROGRAM_COMPANY_ID, DEFAULT_QUERY_CONFIGS} from "../../../constants/commonStrings.constant";
import CompanyType, {MSPCompanyTypes, TCompanyType, VendorCompanyTypes} from "../../../constants/companyType.constant";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import {
    PERMISSION_FEATURE_TAB_KEYS,
    PERMISSION_FEATURE_TAB_KEYS_LABELS,
    PERMISSION_GROUPS,
    TPermissionGroupsKeysArr,
} from "../../../Interfaces/features/features.interface";
import {
    IPermissionFeatureGroup,
    IPermissionGroupFeature,
    IRole,
    ITemplateRole,
    TRoleStore,
    TRoleUpdate,
} from "../../../Interfaces/permissions.interface";
import {rolesService} from "../../../services/roles.service";
import Loader from "../../../utils/loader";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Drawer from "../../Atoms/Drawer/Drawer.component";
import Radio from "../../Atoms/Radio/Radio.component";
import CPTooltip from "../../Atoms/Tooltip/tooltip.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ToggleFeature from "./toggleFeature.component";
import PermissionsTableSection from "./permissionsTableSection.component";
import BasicTabs from "../../Molecules/Tabs/tabs.component";
import {IS_DIRECT_IT_SITE} from "../../../constants/siteFlags";

interface IProps {
    defaultRole: ITemplateRole | IRole;
    open: boolean;
    onClose: () => void;
    resetToDefault: () => void;
    isTemplate?: boolean;
    viewOnly?: boolean;
}

export default function UpsertRoleDrawer({defaultRole, open, onClose, isTemplate, viewOnly, resetToDefault}: IProps) {
    const [role, setRole] = useState<ITemplateRole | IRole | undefined>(defaultRole);
    const [editMode, setEditMode] = useState<boolean>(!viewOnly);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions(
        isTemplate ? [PERMISSION_GROUPS.ADMIN_ROLE_MNGMT_UPDATE] : [PERMISSION_GROUPS.ROLE_MANAGEMENT_UPDATE],
    );
    const [companyType, setCompanyType] = useState<TCompanyType>(
        isTemplate
            ? role && "company_types" in role && !!role.company_types.length
                ? role.company_types[0].value
                : "CHANNEL_PROGRAM"
            : activeCompany?.company_type || "CHANNEL_PROGRAM",
    );
    const [currentPermissions, setCurrentPermissions] = useState<TPermissionGroupsKeysArr>(
        role?.permission_groups?.map(p => p.key) || [],
    );
    const notify = useNotification();
    const translatedCompanyType =
        companyType === CompanyType.DIRECT
            ? CompanyType.DIRECT
            : companyType === CompanyType.CHANNEL_PROGRAM
              ? CompanyType.CHANNEL_PROGRAM
              : companyType === CompanyType.MSP_CLIENT
                ? CompanyType.MSP_CLIENT
                : MSPCompanyTypes.includes(companyType)
                  ? CompanyType.ISP_ALL
                  : CompanyType.VENDOR_ALL;

    const theme = useTheme();
    const queryClient = useQueryClient();
    const isAddingNew = !role?.id;
    const models = [
        {label: "Channel Program", key: CompanyType.CHANNEL_PROGRAM},
        {label: "Direct IT", key: CompanyType.DIRECT},
        {label: "MSP", key: CompanyType.ISP_ALL},
        {label: "MSP Client", key: CompanyType.MSP_CLIENT},
        {label: "Vendor", key: CompanyType.VENDOR_ALL},
    ];
    const permission_groups = useRef<string[]>(role?.permission_groups?.map(p => p.key) || []);
    const methods = useForm<TRoleStore>({
        defaultValues: role
            ? {
                  id: role.id,
                  title: role.title,
                  display_name: role.display_name,
                  description: role.description,
                  permission_groups: permission_groups.current,
              }
            : undefined,
    });
    const isDirectITSite = IS_DIRECT_IT_SITE;

    const resetForm = () => {
        if (defaultRole.created_at !== defaultRole.updated_at) {
            resetToDefault();
            return;
        }
        const defaultRole_permission_groups = defaultRole?.permission_groups?.map(p => p.key) || [];
        methods.reset({
            title: defaultRole?.title,
            display_name: defaultRole?.display_name,
            description: defaultRole?.description,
            permission_groups: defaultRole_permission_groups,
        });
    };

    const handleSubmit = (data: TRoleStore) => {
        if (currentPermissions.length === 0) {
            notify("Please select at least one permission", "Error");
            return;
        }
        setIsSaving(true);
        !data.title ? (data.title = data.display_name) : null;
        const finalCompanyTypes =
            companyType === CompanyType.CHANNEL_PROGRAM
                ? [CompanyType.CHANNEL_PROGRAM]
                : companyType === CompanyType.MSP_CLIENT
                  ? [CompanyType.MSP_CLIENT]
                  : MSPCompanyTypes.includes(companyType)
                    ? MSPCompanyTypes
                    : VendorCompanyTypes;
        (isTemplate
            ? isAddingNew
                ? rolesService.storeTemplates({
                      ...data,
                      permission_group_keys: currentPermissions,
                      company_types: finalCompanyTypes,
                  } as any)
                : rolesService.updateTemplates({
                      ...data,
                      permission_group_keys: currentPermissions,
                      company_types: finalCompanyTypes,
                  } as any)
            : isAddingNew
              ? rolesService.storeRole({...data, permission_groups: currentPermissions})
              : rolesService.updateRole({...data, permission_groups: currentPermissions} as TRoleUpdate)
        )
            .then(response => {
                setRole(response.data);
                queryClient.resetQueries([isTemplate ? "template-roles" : "roles"]);
                onClose();
            })
            .finally(() => setIsSaving(false));
    };

    const permissionFeatureGroups = useQuery<IPermissionFeatureGroup[]>(
        ["ctpfgf", companyType],
        () => {
            return new Promise<IPermissionFeatureGroup[]>((resolve, reject) => {
                rolesService
                    .getPermissionFeatureGroups(companyType)
                    .then(res => resolve(res.data))
                    .catch(err => reject(err));
            });
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
        },
    );
    const permissionGroups = permissionFeatureGroups.data || [];

    const ToggleCell = useCallback(
        ({row}, {id, toggleKey}) => {
            return (
                <ToggleFeature
                    id={id + row.original.id}
                    toggleKey={toggleKey}
                    disabled={!editMode}
                    currentValue={currentPermissions}
                    setValue={setCurrentPermissions}
                />
            );
        },
        [role, editMode, currentPermissions],
    );

    const HelperCell = useCallback(
        ({row}) => {
            return row.original.description ? (
                <Box sx={{display: "flex", alignItems: "center", justifyContent: "flex-end"}}>
                    <CPTooltip
                        title={
                            IS_DIRECT_IT_SITE
                                ? row.original.description.replace("NaviStack", "products")
                                : row.original.description
                        }>
                        <InfoOutlined sx={{color: "secondary.600"}} />
                    </CPTooltip>
                </Box>
            ) : null;
        },
        [role],
    );

    const betterTrackerLabelConversions = {
        "Stack Management": "ProductTracker Management",
        "Contracts Management": "ContractTracker",
        "Plaid Management": "ExpensesTracker",
        NaviStack: "ProductTracker",
    };

    const columns: Column<IPermissionGroupFeature>[] = [
        {
            id: "view",
            Header: "View",
            Cell: tableData =>
                ToggleCell(tableData, {
                    id: "toggle-view-",
                    toggleKey: tableData.row.original.permission_groups.find(p => p.key.includes("_READ"))?.key || "",
                }),
        },
        {
            id: "edit",
            Header: "Edit",
            Cell: tableData =>
                ToggleCell(tableData, {
                    id: "toggle-edit-",
                    toggleKey: tableData.row.original.permission_groups.find(p => p.key.includes("_UPDATE"))?.key || "",
                }),
        },
        {
            id: "name",
            Header: "Feature",
            accessor: "title",
            Cell: ({row}) => (
                <>
                    {isDirectITSite || isClientMsp
                        ? betterTrackerLabelConversions[row.original.title] || row.original.title
                        : row.original.title}
                </>
            ),
        },
        {
            id: "helper",
            Header: "",
            width: "100%",
            Cell: HelperCell,
        },
    ];

    return (
        <Drawer
            open={open}
            onClose={onClose}
            sx={{
                position: "relative",
                ".MuiDrawer-paper": {
                    maxWidth: {xs: "100vw", lg: "50vw"},
                    minWidth: {xs: "100vw", lg: "768px"},
                    padding: "16px 16px 0",
                },
            }}>
            <FormProvider {...methods}>
                <form onSubmit={methods.handleSubmit(handleSubmit)}>
                    <Box sx={{display: "flex", flexDirection: "column", gap: 2}}>
                        <Typography fontInter fontSize={18} weight={500} color="neutral.700">
                            {role?.id ? (editMode ? "Edit Role" : "View Role") : "New Role"}
                        </Typography>
                        <Divider />
                        <Box sx={{display: "flex", flexDirection: "column", gap: 2}}>
                            <Box>
                                {isTemplate && (
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: "column",
                                            gap: 1,
                                        }}>
                                        <Typography fontInter fontSize={16} weight={400} color="neutral.800">
                                            Role Model
                                        </Typography>
                                        <RadioGroup
                                            value={translatedCompanyType}
                                            onChange={e => setCompanyType(e.target.value as TCompanyType)}
                                            sx={{flexDirection: "row", gap: 2, "& *": {margin: "0!important"}}}
                                            name="role-model-selector">
                                            {models.map(model => (
                                                <FormControlLabel
                                                    key={model.key}
                                                    value={model.key}
                                                    disabled={!editMode || !!role?.id}
                                                    control={<Radio value={model.key} color="primary" size="small" />}
                                                    sx={{
                                                        padding: "4px 0",
                                                        ".MuiFormControlLabel-label": {
                                                            fontSize: "16px !important",
                                                            display: "flex",
                                                            alignItems: "center",
                                                            gap: 1,
                                                        },
                                                        div: {
                                                            display: "flex",
                                                            alignItems: "center",
                                                            gap: 1,
                                                        },
                                                    }}
                                                    label={
                                                        <Typography
                                                            fontInter
                                                            fontSize={14}
                                                            weight={400}
                                                            color="neutral.800">
                                                            {model.label}
                                                        </Typography>
                                                    }
                                                />
                                            ))}
                                        </RadioGroup>
                                    </Box>
                                )}
                            </Box>
                            {isTemplate && (
                                <TextBoxComponent
                                    id="title"
                                    label="Role Title"
                                    isRequired
                                    placeholder="Role title"
                                    tooltipHelperText="Role Title is required and needs to be at least 3 characters."
                                    disabled={!editMode}
                                    minLength={3}
                                    containerSx={{
                                        width: "100%",
                                    }}
                                />
                            )}
                            <TextBoxComponent
                                id="display_name"
                                label="Role Friendly Name"
                                isRequired
                                placeholder="Role name"
                                disabled={!editMode}
                                tooltipHelperText="Role Friendly Name is required and needs to be at least 3 characters."
                                minLength={3}
                                containerSx={{
                                    width: "100%",
                                }}
                            />
                            <Box>
                                <TextBoxComponent
                                    id="description"
                                    label="Description"
                                    isRequired
                                    tooltipHelperText="Description is required and needs to be at least 3 characters."
                                    minLength={3}
                                    placeholder="Role description"
                                    type="textarea"
                                    multiline
                                    rows={3}
                                    showCharacterCount
                                    disabled={!editMode}
                                    maxLength={255}
                                    containerSx={{
                                        width: "100%",
                                    }}
                                    sx={{
                                        maxHeight: "none",
                                    }}
                                />
                            </Box>
                            {permissionGroups.length ? (
                                activeCompany?.id === CHANNEL_PROGRAM_COMPANY_ID && !isTemplate ? (
                                    <BasicTabs
                                        tabs={Object.keys(PERMISSION_FEATURE_TAB_KEYS)
                                            .filter(key => key !== PERMISSION_FEATURE_TAB_KEYS.CP_SUPER_ADMIN)
                                            .map(roleType => {
                                                return {
                                                    label: PERMISSION_FEATURE_TAB_KEYS_LABELS?.[roleType],
                                                    Component: (
                                                        <PermissionsTableSection
                                                            permissionGroups={permissionGroups}
                                                            columns={columns}
                                                            editMode={editMode}
                                                            currentPermissions={currentPermissions}
                                                            tabKey={roleType}
                                                        />
                                                    ),
                                                };
                                            })}
                                        tabsContentSx={{
                                            marginTop: 2,
                                        }}
                                        hideBorder
                                    />
                                ) : (
                                    <PermissionsTableSection
                                        permissionGroups={permissionGroups}
                                        columns={columns}
                                        editMode={editMode}
                                        currentPermissions={currentPermissions}
                                    />
                                )
                            ) : (
                                <Loader inline loading version="iconBlue" />
                            )}
                        </Box>
                    </Box>
                    <Box
                        sx={{
                            position: "sticky",
                            bottom: 0,
                            left: 0,
                            width: "100%",
                            background: theme.palette.neutral[100],
                            padding: "0 0 16px",
                        }}>
                        <Divider />
                        <Box
                            sx={{
                                display: "flex",
                                gap: 2,
                                paddingTop: 3,
                            }}>
                            <Button
                                id="save-form"
                                type="submit"
                                color="secondary"
                                variant="contained"
                                sx={{
                                    display: editMode ? "block" : "none",
                                }}
                                disabled={isSaving}>
                                {isSaving ? <Loader inline loading version="iconWhite" /> : "SAVE"}
                            </Button>
                            <Button
                                id="edit-form"
                                type="button"
                                color="secondary"
                                variant="contained"
                                sx={{
                                    display: !editMode && hasEditAccess ? "block" : "none",
                                }}
                                onClick={() => setEditMode(true)}>
                                EDIT
                            </Button>
                            {"template_role_id" in defaultRole &&
                                !!defaultRole.template_role_id &&
                                defaultRole.created_at !== defaultRole.updated_at &&
                                hasEditAccess && (
                                    <Button
                                        id="reset-form"
                                        type="button"
                                        color="secondary"
                                        variant="outlined"
                                        onClick={resetForm}>
                                        RESET TO DEFAULT
                                    </Button>
                                )}
                            <Button id="cancel-form" type="button" color="secondary" variant="text" onClick={onClose}>
                                CANCEL
                            </Button>
                        </Box>
                    </Box>
                </form>
            </FormProvider>
        </Drawer>
    );
}
