import Typography from "../../Atoms/Typography/Typography.component";
import ErrorOutlineOutlined from "@mui/icons-material/ErrorOutlineOutlined";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import {pluralizeString} from "../../../utils/formatString.util";
import useCSVUpload from "../../../hooks/useCSVUpload";
import useTheme from "@mui/material/styles/useTheme";
import Box from "../../Atoms/Box/Box.component";
import Chip from "../../Atoms/Chip/Chip.Component";

/**
 * Renders the third step of the CSV upload process. Shows errors and successful imports.
 *
 * @param {IProps} props - The props object containing the label for the CSV upload.
 * @return {JSX.Element} The JSX element representing the third step of the CSV upload process.
 */

interface IProps {
    label: string;
    importedLabel: string;
}

const CSVUploadStepThree = ({label, importedLabel}: IProps) => {
    const theme = useTheme();
    const {status} = useCSVUpload();
    const totalSuccessful = status?.successful?.length || 0;
    const totalFailed = status?.notFound?.length || 0;
    const isPartiallySuccessful = totalSuccessful > 0 || (!!status.success_message && totalFailed === 0);

    return (
        <Box display="flex" flexDirection="column" gap="24px">
            <Box
                sx={{
                    backgroundColor: isPartiallySuccessful ? theme.palette.success[100] : theme.palette.error[100],
                    borderColor: isPartiallySuccessful ? theme.palette.success[300] : theme.palette.error[300],
                    borderStyle: "solid",
                    borderWidth: "1px",
                    borderRadius: "16px",
                    display: "flex",
                    alignItems: "center",
                    padding: "40px 48px",
                    gap: 2,
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 1,
                        flexDirection: "column",
                        "& svg": {
                            fill: isPartiallySuccessful ? theme.palette.success[500] : theme.palette.error[500],
                        },
                    }}>
                    {isPartiallySuccessful ? (
                        <CheckCircleOutlinedIcon fontSize="large" sx={{width: "48px", height: "48px"}} />
                    ) : (
                        <ErrorOutlineOutlined fontSize="large" sx={{width: "48px", height: "48px"}} />
                    )}
                </Box>
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 600}}>
                        {totalSuccessful} {pluralizeString(label, totalSuccessful)} {importedLabel}
                    </Typography>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 400}}>
                        {isPartiallySuccessful
                            ? status.success_message ||
                              `We have successfully ${importedLabel} your ${pluralizeString(
                                  label.toLowerCase(),
                                  totalSuccessful,
                              )}.`
                            : `We encountered an issue while importing your ${pluralizeString(
                                  label.toLowerCase(),
                                  totalFailed,
                              )}. Please review the file and attempt the import process again.`}
                    </Typography>
                </Box>
            </Box>

            {!!status.successful?.length && (
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body2" color={theme.palette.neutral[700]} sx={{fontWeight: 600}}>
                        {importedLabel} {pluralizeString(label, totalSuccessful)}
                    </Typography>
                    <Box sx={{display: "flex", gap: 1, flexWrap: "wrap"}}>
                        {status.successful?.map((item, i) => {
                            const label = typeof item === "string" ? item : item.name || item.label || "";
                            return (
                                <Chip
                                    key={i}
                                    label={label}
                                    color="default"
                                    title={label}
                                    sx={{
                                        borderRadius: 1,
                                        backgroundColor: theme.palette.neutral[100],
                                        border: `1px solid ${theme.palette.blue[300]}`,
                                        span: {
                                            color: theme.palette.blue[800],
                                            fontWeight: 600,
                                            fontSize: 14,
                                        },
                                    }}
                                />
                            );
                        })}
                    </Box>
                </Box>
            )}
            {!!status.notFound?.length && (
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body2" color={theme.palette.neutral[700]} sx={{fontWeight: 600}}>
                        Not {importedLabel} {pluralizeString(label, totalFailed)}
                    </Typography>
                    <Box sx={{display: "flex", gap: 1, flexWrap: "wrap"}}>
                        {status.notFound?.map((item, i) => {
                            const label = typeof item === "string" ? item : item.name || item.label || "";
                            return (
                                <Chip
                                    key={i}
                                    label={label}
                                    color="default"
                                    title={label}
                                    sx={{
                                        borderRadius: 1,
                                        backgroundColor: theme.palette.neutral[100],
                                        border: `1px solid ${theme.palette.blue[300]}`,
                                        span: {
                                            color: theme.palette.blue[800],
                                            fontWeight: 600,
                                            fontSize: 14,
                                        },
                                    }}
                                />
                            );
                        })}
                    </Box>
                </Box>
            )}
        </Box>
    );
};

CSVUploadStepThree.defaultProps = {
    label: "Item",
    importedLabel: "Imported",
};

export default CSVUploadStepThree;
