import {render, screen, fireEvent} from "@testing-library/react";
import {describe, it, expect} from "vitest";
import CSVUploadStepOne from "./CSVUploadStepOne.component";
import {setFileMock} from "../../../../__mocks__/useCSVUploadMock";
import TestWrapper from "../../../../__test_utils__/TestWrapper";

describe("CSVUploadStepOne", () => {
    it("renders the component with default title", () => {
        render(
            <TestWrapper>
                <CSVUploadStepOne instructions={["Instruction 1", "Instruction 2"]} />
            </TestWrapper>,
        );
        expect(screen.getByText("Select or Drag and Drop your file")).toBeDefined();
    });

    it("renders the component with a custom title", () => {
        render(
            <TestWrapper>
                <CSVUploadStepOne title={{text: "Custom Title"}} instructions={["Instruction 1", "Instruction 2"]} />
            </TestWrapper>,
        );
        expect(screen.getByText("Custom Title")).toBeDefined();
    });

    it("calls setFile when a file is uploaded", async () => {
        render(
            <TestWrapper>
                <CSVUploadStepOne title={{text: "Custom Title"}} instructions={["Instruction 1", "Instruction 2"]} />
            </TestWrapper>,
        );
        const input = await screen.getByTestId("inputFilePicker");
        const dropzone = await screen.getByTestId("uploadFileDropZone");
        expect(input).toBeDefined();
        expect(dropzone).toBeDefined();
        const testButton = screen.getByTestId("testButton");
        expect(testButton).toBeDefined();
        fireEvent.click(testButton);
        const sampleCsvName = "test.csv";
        const sampleCsv = new File(["test"], sampleCsvName, {type: "text/csv"});
        await new Promise(resolve => setTimeout(resolve, 100));
        expect(setFileMock).toHaveBeenCalledTimes(1);
        expect(setFileMock).toHaveBeenCalledWith(sampleCsv);
    });

    it("displays instructions and sample file link", () => {
        const maxSizeMb = "10";
        render(
            <TestWrapper>
                <CSVUploadStepOne
                    instructions={["Instruction 1", "Instruction 2"]}
                    sampleFileUrl="sample.csv"
                    maxSizeMb={maxSizeMb}
                    headerRowRequired={true}
                />
            </TestWrapper>,
        );

        expect(screen.getByText("Instruction 1")).toBeDefined();
        expect(screen.getByText("Instruction 2")).toBeDefined();
        expect(screen.getByText("Maximum file:")).toBeDefined();
        expect(screen.getByText(maxSizeMb + " MB")).toBeDefined();
        expect(screen.getByText("You can download our sample file here.")).toBeDefined();
    });
});
