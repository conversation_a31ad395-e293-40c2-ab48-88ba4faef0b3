import EditOutlined from "@mui/icons-material/EditOutlined";
import {AxiosResponse} from "axios";
import {ReactNode, useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import type {
    IVendorConfirmationModalData,
    TConfirmationWithData,
    TVendorConfirmationModalTypes,
} from "../../../Interfaces/partnerPage.interface";
import useNotification from "../../../hooks/useNotification";
import {PartnerPageService} from "../../../services/partnerpage.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import MuiSelect, {IMuiSelectObj} from "../../Molecules/MuiSelect/MuiSelect.component";

interface IVendorConfirmationForm {
    otherReason: string;
}

interface IProps<TData, T> {
    setShowConfirmationModal?: (data?: T) => void;
    showConfirmationModal?: T;
    handleSuccess?: (data: {reason: string; other: string; value: TData}, type: TVendorConfirmationModalTypes) => void;
    okButtonText: ReactNode;
    title: string;
    defaultValue?: IMuiSelectObj;
    loading?: boolean;
}

type TAcceptRejectOption = {
    id: string;
    name: string;
    show_answer_option: boolean;
};

type TRawOptions = {
    accept_invite_options: TAcceptRejectOption[] | null;
    reject_invite_options: TAcceptRejectOption[] | null;
};

export default function VendorConfirmationModal<TData, T extends TConfirmationWithData<TData>>(
    props: IProps<TData, T>,
) {
    const [errorSelect, setErrorSelect] = useState(false);
    const {showConfirmationModal, setShowConfirmationModal} = props;
    const initialDefaultValue = showConfirmationModal?.defaultValue || props.defaultValue;
    const defaultValue = initialDefaultValue
        ? ({
              ...initialDefaultValue,
              name: initialDefaultValue?.name.split("---")[0] || "",
          } as IMuiSelectObj)
        : undefined;
    const defaultOtherReason = initialDefaultValue?.name.includes("---")
        ? initialDefaultValue?.name.split("---")[1]
        : "";
    const methods = useForm<IVendorConfirmationForm>({
        defaultValues: {otherReason: defaultOtherReason},
    });
    const [selected, setSelected] = useState<IMuiSelectObj[]>(defaultValue ? [defaultValue] : []);
    const [rawOptions, setRawOptions] = useState<TRawOptions>({
        reject_invite_options: null,
        accept_invite_options: null,
    });
    const {accept_invite_options, reject_invite_options} = rawOptions;
    const {show, type, data} = showConfirmationModal || ({} as TConfirmationWithData<TData>);
    const shouldUseAcceptOptions = type === "Accept" || type === "Edit";
    const showReasonBox = selected?.[0]?.name === "Other";
    const selectedItem = (shouldUseAcceptOptions ? accept_invite_options : reject_invite_options)?.filter(
        a => a.id === selected?.[0]?.id,
    )?.[0];
    const options: IMuiSelectObj[] =
        (shouldUseAcceptOptions ? accept_invite_options : reject_invite_options)?.map(item => ({
            id: item.id,
            name: item?.name,
        })) || [];

    const notify = useNotification();

    const handleSuccess = () => {
        const values = methods.getValues();
        if (
            (selectedItem?.hasOwnProperty("id") && selectedItem?.id !== "") ||
            (selectedItem?.hasOwnProperty("show_answer_option") &&
                selectedItem?.show_answer_option &&
                values?.otherReason !== "")
        ) {
            const reasonValue = selectedItem?.show_answer_option ? values.otherReason : selectedItem?.id;
            if (reasonValue) {
                return props.handleSuccess?.(
                    {
                        reason: selectedItem?.id,
                        other: values.otherReason,
                        value: data,
                    },
                    type!,
                );
            }
            return selectedItem?.show_answer_option
                ? methods.setError("otherReason", {type: "required"})
                : setErrorSelect(true);
        }
        return selectedItem?.show_answer_option
            ? methods.setError("otherReason", {type: "required"})
            : setErrorSelect(true);
    };

    const getOptions = async () => {
        try {
            const response: AxiosResponse = shouldUseAcceptOptions
                ? await PartnerPageService.getInvitationAcceptReasons()
                : await PartnerPageService.getInvitationRejectReasons();

            if (response?.data) {
                const updateKey = shouldUseAcceptOptions ? "accept_invite_options" : "reject_invite_options";
                setRawOptions(prev => ({...prev, [updateKey]: response.data}));
            }
        } catch (e: any) {
            if (e?.code !== "ERR_CANCELED") {
                notify(getErrorFromArray(e), "Error");
            }
        }
    };
    useEffect(() => {
        if (!type) return;
        if ((shouldUseAcceptOptions && !accept_invite_options) || (!shouldUseAcceptOptions && !reject_invite_options)) {
            getOptions();
        }
    }, []);

    const assertLabel = () => {
        switch (type) {
            case "Accept":
                return "Select a type";
            case "Edit":
                return "Partnership Type";
            default:
                return "Select a reason";
        }
    };

    const assertModalType = () => {
        switch (type) {
            case "Accept":
                return "blue";
            case "Edit":
                return "blue";
            default:
                return "error";
        }
    };

    return (
        <ModalComponent
            onSuccess={handleSuccess}
            open={!!show}
            onClose={() => setShowConfirmationModal?.(undefined)}
            okButtonText={props?.okButtonText}
            cancelButtonText="Cancel"
            title={props.title}
            //okButtonSx={{padding: "10px 24px 10px 16px!important"}}
            cancelButtonSx={{padding: "10px 16px 10px 24px!important"}}
            loading={props.loading}
            icon={type === "Edit" ? <EditOutlined color="neutral" fontSize="small" /> : undefined}
            content={
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "24px",
                    }}>
                    <Typography
                        fontInter
                        variant="body2"
                        sx={theme => ({
                            color: theme.palette.neutral[800],
                            fontWeight: "400!important",
                            fontSize: "16px!important",
                        })}>
                        {type === "Edit" ? (
                            "Please select:"
                        ) : type === "Accept" ? (
                            <>
                                That’s great news! Now, please tell us more about{" "}
                                {Array.isArray(data) && data.length > 1 ? "the" : "this"} partnership:
                                <br />
                                <Typography
                                    variant="subtitle3"
                                    fontInter
                                    sx={{fontWeight: "600!important", fontSize: "16px!important"}}>
                                    {Array.isArray(data)
                                        ? data.length > 1
                                            ? `${data.length} companies selected`
                                            : `${data[0]?.follower_partner?.name}`
                                        : null}
                                </Typography>
                            </>
                        ) : type === "Invitation" ? (
                            <>
                                Are you sure you want to remove{" "}
                                {Array.isArray(data) && data.length > 1 ? "the invitations" : "this invitation"}?
                                <br />
                                <Typography
                                    variant="subtitle3"
                                    fontInter
                                    sx={{fontWeight: "600!important", fontSize: "16px!important"}}>
                                    {(data as IVendorConfirmationModalData)?.email || ""}
                                    {Array.isArray(data)
                                        ? data.length > 1
                                            ? `${data.length} invitations selected`
                                            : `${data[0]?.email}`
                                        : null}
                                </Typography>
                            </>
                        ) : (
                            <>
                                Are you sure you want to {type === "Remove" ? "remove" : "decline"}
                                {Array.isArray(data) && data.length > 1 ? " access to" : ""}{" "}
                                <Typography
                                    variant="subtitle3"
                                    fontInter
                                    sx={{fontWeight: "600!important", fontSize: "16px!important"}}>
                                    {Array.isArray(data)
                                        ? data.length > 1
                                            ? `${data.length} requests`
                                            : `${data[0]?.follower_partner?.name || ""}`
                                        : type === "Remove"
                                        ? (data as IVendorConfirmationModalData)?.name ||
                                          (data as IVendorConfirmationModalData)?.invited_by?.name ||
                                          ""
                                        : `${(data as IVendorConfirmationModalData)?.invited_by?.name || ""}${` ${
                                              (data as IVendorConfirmationModalData)?.name || ""
                                          }`}`}
                                </Typography>{" "}
                                {Array.isArray(data) && data.length > 1 ? "for" : "access to"} your Channel Command
                                (PRM) portal?
                            </>
                        )}
                    </Typography>

                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                        }}>
                        <MuiSelect
                            sx={{fontSize: "18px !important", height: "45px !important"}}
                            variant="outlined"
                            labelId="select-label"
                            id="selectOption"
                            value={selected}
                            label={assertLabel()}
                            defaultValue={defaultValue}
                            options={options}
                            onChange={v => {
                                setSelected(v);
                                setErrorSelect(false);
                            }}
                            onClear={() => {
                                setSelected([]);
                                setErrorSelect(false);
                            }}
                            showDropdownIcon
                            error={errorSelect}
                            tooltipHelperText="Type is required"
                        />
                    </Box>
                    {showReasonBox && (
                        <FormProvider {...methods}>
                            <TextBoxComponent
                                containerStyles={{}}
                                id="otherReason"
                                label="Description"
                                showError
                                isRequired
                                tooltipHelperText="Description is required"
                                isInvalid={!!methods.formState.errors.otherReason}
                                placeholder={
                                    type === "Edit"
                                        ? "Enter new type"
                                        : type === "Accept"
                                        ? "Enter Description"
                                        : type === "Invitation"
                                        ? "Enter reason for removing invitation"
                                        : type === "Remove"
                                        ? "Enter reason for removing access"
                                        : "Enter reason for decline"
                                }
                            />
                        </FormProvider>
                    )}
                </Box>
            }
            modalTheme={assertModalType()}
            justifyButtons={"flex-start"}
        />
    );
}
