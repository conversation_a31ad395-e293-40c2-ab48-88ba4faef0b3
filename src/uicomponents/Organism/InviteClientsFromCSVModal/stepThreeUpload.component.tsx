import {Box, useTheme} from "@mui/material";
import Typography from "../../Atoms/Typography/Typography.component";
import ErrorOutlineOutlined from "@mui/icons-material/ErrorOutlineOutlined";
import CompanyTag from "../../Molecules/CompanyTag/companyTag.component";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import {MSP_BRAND_TEXT} from "../../../constants/commonStrings.constant";
import {pluralizeString} from "../../../utils/formatString.util";

interface IVendorsList {
    imported: any[];
    notFound: any[];
}
interface IProps {
    vendors: IVendorsList;
}
const StepThreeUploadCompleted = (props: IProps) => {
    const theme = useTheme();
    const {vendors} = props;
    const totalImported = vendors.imported.length;
    return (
        <Box display="flex" flexDirection="column" gap="24px">
            <Box
                sx={{
                    backgroundColor: totalImported ? theme.palette.success[100] : theme.palette.error[100],
                    borderColor: totalImported ? theme.palette.success[300] : theme.palette.error[300],
                    borderStyle: "solid",
                    borderWidth: "1px",
                    borderRadius: "16px",
                    display: "flex",
                    alignItems: "center",
                    padding: "40px 48px",
                    gap: 2,
                }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 1,
                        flexDirection: "column",
                        "& svg": {
                            fill: totalImported ? theme.palette.success[500] : theme.palette.error[500],
                        },
                    }}>
                    {totalImported ? (
                        <CheckCircleOutlinedIcon fontSize="large" sx={{width: "48px", height: "48px"}} />
                    ) : (
                        <ErrorOutlineOutlined fontSize="large" sx={{width: "48px", height: "48px"}} />
                    )}
                </Box>
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 600}}>
                        {totalImported} {pluralizeString(MSP_BRAND_TEXT.CUSTOMER, totalImported)} Imported
                    </Typography>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]} sx={{fontWeight: 400}}>
                        {totalImported
                            ? `We have successfully imported your ${MSP_BRAND_TEXT.CUSTOMERS.toLowerCase()}.`
                            : `We encountered an issue while importing your ${MSP_BRAND_TEXT.CUSTOMERS.toLowerCase()}. Please review the file and attempt the import process again.`}
                    </Typography>
                </Box>
            </Box>

            {!!vendors.imported?.length && (
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body2" color={theme.palette.neutral[700]} sx={{fontWeight: 600}}>
                        Imported {MSP_BRAND_TEXT.CUSTOMERS}
                    </Typography>
                    <Box sx={{display: "flex", gap: 1, flexWrap: "wrap"}}>
                        {vendors.imported?.map((vendor, vIndex) => (
                            <CompanyTag sx={{padding: "6px 16px"}} key={vIndex} company={{name: vendor}} hideAvatar />
                        ))}
                    </Box>
                </Box>
            )}
            {!!vendors.notFound?.length && (
                <Box sx={{display: "flex", gap: 1, flexDirection: "column"}}>
                    <Typography fontInter variant="body2" color={theme.palette.neutral[700]} sx={{fontWeight: 600}}>
                        Not Imported {MSP_BRAND_TEXT.CUSTOMERS}
                    </Typography>
                    <Box sx={{display: "flex", gap: 1, flexWrap: "wrap"}}>
                        {vendors.notFound?.map((vendor, vIndex) => (
                            <CompanyTag sx={{padding: "6px 16px"}} key={vIndex} company={{name: vendor}} hideAvatar />
                        ))}
                    </Box>
                </Box>
            )}
        </Box>
    );
};

export default StepThreeUploadCompleted;
