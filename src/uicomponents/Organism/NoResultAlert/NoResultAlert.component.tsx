import Box from "@mui/material/Box";
import useTheme from "@mui/material/styles/useTheme";
import type {ReactNode} from "react";
import Typography from "../../Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";

interface IProps {
    title: ReactNode;
    subTitle: ReactNode;
    buttonTitle?: string;
    buttonOnClick?: Function;
    styles?: React.CSSProperties;
    classNames?: string;
    titleColor?: string;
    subTitleColor?: string;
    icon: ReactNode;
    buttonSx?: any;
    boxSx?: any;
}
const NoResultAlert = (props: IProps) => {
    const theme = useTheme();
    return (
        <Box
            display={"flex"}
            alignItems={"center"}
            padding={1}
            justifyContent={"space-between"}
            border={`1px solid ${theme.palette.blue[300]}`}
            borderRadius={"8px"}
            sx={{backgroundColor: theme.palette.blue[100], width: "100%", ...(props?.boxSx || {})}}>
            <Box display={"flex"} alignItems={"center"} gap={2}>
                {props.icon}
                <Box display={"flex"} flexDirection={"column"}>
                    <Typography variant={"subtitle2"} fontInter color={props?.titleColor ?? theme.palette.neutral[800]}>
                        {props.title}
                    </Typography>
                    {props.subTitle && (
                        <Typography
                            variant={"body3"}
                            fontInter
                            color={props?.subTitleColor ?? theme.palette.neutral[800]}>
                            {props.subTitle}
                        </Typography>
                    )}
                </Box>
            </Box>
            {props?.buttonOnClick && (
                <MuiButtonComponent
                    onClick={props?.buttonOnClick}
                    id="noResultAction"
                    variant="outlined"
                    sx={{
                        color: theme.palette.blue["main"],
                        border: `1px solid ${theme.palette.blue["main"]}`,
                        fontWeight: 600,
                        ...(props?.buttonSx || {}),
                    }}>
                    {props.buttonTitle}
                </MuiButtonComponent>
            )}
        </Box>
    );
};

export default NoResultAlert;
