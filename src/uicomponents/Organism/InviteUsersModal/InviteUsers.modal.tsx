import SendOutlined from "@mui/icons-material/SendOutlined";
import {useTheme} from "@mui/material";
import Box from "@mui/material/Box";
import type {AxiosError, AxiosResponse} from "axios";
import {Dispatch, SetStateAction, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import {validateEmail} from "../../../utils/validation.util";
import Typography from "../../Atoms/Typography/Typography.component";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import RoleListInput from "../../Molecules/RoleListInput/RoleListInput.component";
import UserListInput from "../../Molecules/UserListInput/UserListInput.component";
import useActiveCompany from "../../../hooks/useActiveCompany";

interface IProps {
    modalState: [boolean, Dispatch<SetStateAction<boolean>>];
    friendlyUrl: string;
    onSuccess?: () => void;
}

interface IInviteForm {
    emails: string[];
    role_id: string;
}

type TInviteResponse = {
    "not-ok-emails": string[];
    "not-ok-reason": string;
    "ok-emails": string[];
};

export default function InviteUsersModal(props: IProps) {
    const [sendingInvites, setSendingInvites] = useState(false);
    const [open, handleClose] = props.modalState;
    const [invitedEmails, setInvitedEmails] = useState<string[]>([]);
    const [erroredEmails, setErroredEmails] = useState<string[]>([]);
    const [erroredReason, setErroredReason] = useState<string>("");
    const methods = useForm<IInviteForm>();
    const notify = useNotification();
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const hasErroredEmails = erroredEmails.length > 0;
    const hasInvitedEmails = invitedEmails.length > 0;
    const canTryInvite = !hasErroredEmails || (hasErroredEmails && !hasInvitedEmails);

    const onSuccessRequestAccess = (response: AxiosResponse<TInviteResponse>) => {
        setSendingInvites(false);
        if (response.data["not-ok-emails"].length > 0) {
            setErroredEmails(response.data["not-ok-emails"]);
            response.data["ok-emails"] && setInvitedEmails(response.data["ok-emails"]);
            setErroredReason(response.data["not-ok-reason"]);
            return;
        }
        notify("Thank you! Your invite was sent successfully.", "Success");
        handleClose(false);
        props.onSuccess?.();
    };

    const onError = (err: AxiosError) => {
        setSendingInvites(false);
        notify(getErrorFromArray(err), "Error");
    };

    const sendInvites = (data: IInviteForm) => {
        if (sendingInvites) return;
        const emails = data.emails;
        if (!emails || !emails.length) return notify("Please enter at least one email address.", "Error");
        setSendingInvites(true);
        const enteredEmails = emails;
        const role_id = data.role_id;
        const verifiedEmails: string[] = [];
        enteredEmails.map(item => {
            if (validateEmail(item.trim())) {
                verifiedEmails.push(item.trim());
            }
        });
        if (verifiedEmails.length === 0) {
            setSendingInvites(false);
            return;
        }
        companyService.inviteUsersToCompany(
            props.friendlyUrl,
            verifiedEmails,
            role_id,
            onSuccessRequestAccess,
            onError,
        );
    };

    return (
        <FormProvider {...methods}>
            <ModalComponent
                open={open}
                onClose={() => handleClose(false)}
                onSuccess={!canTryInvite ? () => handleClose(false) : methods.handleSubmit(sendInvites)}
                title="Invite to your Company"
                justifyButtons="flex-start"
                okButtonText={
                    !canTryInvite ? (
                        "Close"
                    ) : sendingInvites ? (
                        <Loader inline loading version="iconWhite" />
                    ) : (
                        <>
                            <SendOutlined htmlColor="white" fontSize="small" sx={{marginRight: "8px"}} /> SEND
                        </>
                    )
                }
                showCancelButton={!(!canTryInvite || sendingInvites)}
                icon={<SendOutlined color="neutral" />}
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "400px !important", width: "100%"},
                }}
                content={
                    <>
                        <Box sx={{display: "flex", gap: "24px", flexDirection: "column"}}>
                            {hasErroredEmails && (
                                <>
                                    {hasInvitedEmails && (
                                        <AlertBanner
                                            id="notOkEmailsBanner"
                                            variant="success"
                                            title={{
                                                text: `Invitation${invitedEmails.length > 1 ? "s" : ""} Sent`,
                                                color: theme.palette.neutral[700],
                                            }}
                                            subTitle={{
                                                text: (
                                                    <>
                                                        <Typography fontInter variant="body3">
                                                            Your invitation
                                                            {invitedEmails.length > 1 ? "s have " : " has "}
                                                            been successfully sent to the following email address
                                                            {invitedEmails.length > 1 ? "es" : ""}:
                                                        </Typography>
                                                        <br />
                                                        <Typography fontInter variant="body3" weight={600}>
                                                            {invitedEmails.map(e => (
                                                                <>
                                                                    - {e}
                                                                    <br />
                                                                </>
                                                            ))}
                                                        </Typography>
                                                    </>
                                                ),
                                            }}
                                        />
                                    )}
                                    <AlertBanner
                                        id="notOkEmailsBanner"
                                        variant="info"
                                        title={{
                                            text: "Unable to Invite",
                                            color: theme.palette.neutral[700],
                                        }}
                                        subTitle={{
                                            text: (
                                                <>
                                                    <Typography fontInter variant="body3">
                                                        {erroredReason}
                                                    </Typography>
                                                    <br />
                                                    <Typography fontInter variant="body3" weight={600}>
                                                        {erroredEmails.map(e => (
                                                            <>
                                                                - {e}
                                                                <br />
                                                            </>
                                                        ))}
                                                    </Typography>
                                                </>
                                            ),
                                        }}
                                    />
                                </>
                            )}
                            {canTryInvite && (
                                <>
                                    <Typography fontInter variant="body3" color={theme.palette.neutral[700]}>
                                        Enter the emails you want to send the invite to:
                                    </Typography>
                                    <UserListInput
                                        sx={{
                                            width: "100%",
                                        }}
                                        id="emails"
                                        label="Emails"
                                        placeholder="Type the recipient's Email"
                                        validateOption={v => validateEmail(v)}
                                        helperText="Enter the email address(es) or paste a comma separated list of email addresses."
                                    />
                                    <RoleListInput
                                        id="role_id"
                                        sx={{
                                            width: "100%",
                                        }}
                                        placeholder="Select Role"
                                        isRequired
                                        tooltipHelperText="Role is required."
                                        companyId={activeCompany?.id || ""}
                                    />
                                </>
                            )}
                        </Box>
                    </>
                }
            />
        </FormProvider>
    );
}
