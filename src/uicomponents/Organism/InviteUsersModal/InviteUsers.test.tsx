import {fireEvent, render, waitFor} from "@testing-library/react";
import {beforeEach, describe, expect, it, Mock, vi} from "vitest";
import TestWrapper from "../../../../__test_utils__/TestWrapper";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import InviteUsersModal from "./InviteUsers.modal";

vi.mock("../../../hooks/useNotification", () => ({
    default: vi.fn(),
}));

describe("InviteUsersModal", () => {
    const mockClose = vi.fn();
    const mockNotify = vi.fn();
    const renderComponent = () =>
        render(
            <TestWrapper>
                <InviteUsersModal modalState={[true, mockClose]} friendlyUrl="test-url" />,
            </TestWrapper>,
        );

    beforeEach(() => {
        vi.clearAllMocks();
        (useNotification as Mock).mockReturnValue(mockNotify);
    });

    it("initializes the form correctly", () => {
        const {getByTestId} = renderComponent();
        expect(getByTestId("textInput-emails")).toBeTruthy();
    });

    it("validates email input correctly", async () => {
        const {getByTestId, getByText} = renderComponent();

        const input = getByTestId("textInput-emails");
        const sendButton = getByText("SEND");
        fireEvent.change(input, {target: {value: "invalidemail"}});
        fireEvent.click(sendButton);
        await waitFor(() => expect(mockNotify).toHaveBeenCalledWith(expect.any(String), "Error"));
    });

    it("does not send invites without emails", () => {
        const {getByText} = renderComponent();
        const sendButton = getByText("SEND");
        fireEvent.click(sendButton);
        expect(mockNotify).toHaveBeenCalledWith("Please enter at least one email address.", "Error");
        expect(companyService.inviteUsersToCompany).not.toHaveBeenCalled();
    });

    it("does not send invites with invalid emails", () => {
        const {getByText, getByTestId} = renderComponent();

        const input = getByTestId("textInput-emails");
        fireEvent.change(input, {target: {value: "invalidemail"}});
        const sendButton = getByText("SEND");
        fireEvent.click(sendButton);
        expect(companyService.inviteUsersToCompany).not.toHaveBeenCalled();
    });

    /* it("sends invites with valid emails", () => {
        const {getByText, getByTestId} = renderComponent();

        const input = getByTestId("textInput-emails");
        fireEvent.change(input, {target: {value: "<EMAIL>"}});
        const sendButton = getByText("SEND");
        fireEvent.click(sendButton);
        expect(companyService.inviteUsersToCompany).toHaveBeenCalled();
    });

    it("handles success and error responses correctly", async () => {
        (companyService.inviteUsersToCompany as Mock).mockImplementationOnce((url, emails, onSuccess) => onSuccess());
        (companyService.inviteUsersToCompany as Mock).mockImplementationOnce((url, emails, onSuccess, onError) =>
            onError({response: {data: ["Error message"]}}),
        );

        const {getByText, getByTestId, rerender} = renderComponent();

        const input = getByTestId("textInput-emails");
        fireEvent.change(input, {target: {value: "<EMAIL>"}});
        const sendButton = getByText("SEND");

        // Test success
        fireEvent.click(sendButton);
        await waitFor(() => expect(mockClose).toHaveBeenCalledWith(false));

        // Test error
        rerender(
            <TestWrapper>
                <InviteUsersModal modalState={[true, mockClose]} friendlyUrl="test-url" />
            </TestWrapper>,
        );
        fireEvent.click(sendButton);
        await waitFor(() => expect(mockNotify).toHaveBeenCalledWith("Error message", "Error"));
    }); */
});
