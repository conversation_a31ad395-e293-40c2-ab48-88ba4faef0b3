import ContentCopyOutlinedIcon from "@mui/icons-material/ContentCopyOutlined";
import DoneOutlinedIcon from "@mui/icons-material/DoneOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {Box} from "@mui/system";
import {useEffect, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import CustomLink from "../../../components/Links/CustomLink.component";
import routeConfig from "../../../constants/routeConfig";
import Button, {CustomMuiButtonProps} from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ShareButton, {IShareButtonProps} from "../../Molecules/ShareButton/shareButton.component";

interface IProps {
    valueToBeCopied: string;
    label?: string;
    readOnly?: boolean;
    showCopyButton: boolean;
    sxBeforeSuccess?: object;
    sxAfterSuccess?: object;
    hideTermsAndService?: boolean;
    showShareButton?: boolean;
    copyCodeBtnProps?: Partial<CustomMuiButtonProps>;
    shareBtnProps?: Partial<IShareButtonProps>;
    copyIconColor?: string;
    shareUrl?: string;
    richText?: boolean;
    stripHtmlOnCopy?: boolean;
    beforeCopy?: (value: string) => string;
    useTextInput?: boolean;
}

export default function CodeCopyTextBox({
    valueToBeCopied,
    label,
    showCopyButton,
    sxBeforeSuccess,
    sxAfterSuccess,
    hideTermsAndService,
    showShareButton,
    copyCodeBtnProps,
    shareBtnProps,
    copyIconColor,
    shareUrl,
    richText,
    beforeCopy,
    useTextInput,
}: IProps) {
    const [copySuccess, setCopySuccess] = useState(false);
    const theme = useTheme();
    const methods = useForm();

    const copyToClipBoard = async textToBeCopied => {
        try {
            let finalText = textToBeCopied;
            if (beforeCopy) {
                finalText = beforeCopy(finalText);
            }
            await navigator.clipboard.writeText(finalText);
            setCopySuccess(true);
        } catch (err) {
            setCopySuccess(false);
        }
    };

    useEffect(() => {
        let timeOut;
        if (copySuccess) {
            timeOut = setTimeout(() => {
                setCopySuccess(false);
            }, 1000);
        }

        return () => clearTimeout(timeOut);
    }, [copySuccess]);

    useEffect(() => {
        if (valueToBeCopied) {
            if (richText) {
                const tempElement = document.createElement("div");
                tempElement.innerHTML = valueToBeCopied;
                const strippedText = tempElement.textContent || tempElement.innerText || "";
                if (strippedText) {
                    methods.setValue("link", strippedText);
                }
            } else {
                methods.setValue("link", valueToBeCopied);
            }
        }
    }, [valueToBeCopied]);

    return (
        <FormProvider {...methods}>
            <Box sx={{flexGrow: 1}}>
                {useTextInput ? (
                    <TextBoxComponent
                        id="link"
                        label={label}
                        containerSx={{
                            width: "100%",
                            "& div.MuiInpuAdornment-positionEnd": {
                                fontSize: "24px",
                            },
                            "& input": {
                                padding: "24px 48px 8px 16px!important",
                            },
                            "& label": {
                                tranform: "translate(16px, 8px) scale(0.75)!important",
                            },
                        }}
                        endAdornment={
                            <Box
                                display="flex"
                                flexDirection="row"
                                justifyContent="flex-end"
                                alignItems="center"
                                gap={2}>
                                {showCopyButton && (
                                    <Button
                                        id="copyCode"
                                        variant="contained"
                                        color="secondary"
                                        onClick={() => copyToClipBoard(valueToBeCopied)}
                                        sx={{
                                            padding: 1.5,
                                            border: "none",
                                            minWidth: "0",
                                            "& span": {
                                                margin: 0,
                                            },
                                            ...(copySuccess ? sxAfterSuccess || {} : sxBeforeSuccess || {}),
                                        }}
                                        tooltip={{
                                            title: copySuccess ? "COPIED" : "COPY",
                                            placement: "left",
                                        }}
                                        startIcon={
                                            copySuccess ? (
                                                <DoneOutlinedIcon
                                                    sx={{
                                                        color: copyIconColor || theme.palette.blue[600],
                                                        fontSize: "24px",
                                                    }}
                                                />
                                            ) : (
                                                <ContentCopyOutlinedIcon
                                                    sx={{
                                                        color: copyIconColor || theme.palette.blue[600],
                                                        fontSize: "24px",
                                                    }}
                                                />
                                            )
                                        }
                                        {...(copyCodeBtnProps || {})}
                                    />
                                )}
                                {showShareButton && (
                                    <ShareButton
                                        sx={{
                                            color: theme.palette.blue[600],
                                            fontWeight: "700!important",
                                            lineHeight: "19px",
                                        }}
                                        variant="textButton"
                                        url={shareUrl || ""}
                                        emailBody={valueToBeCopied.replaceAll("Click here", "")}
                                        {...(shareBtnProps || {})}
                                    />
                                )}
                            </Box>
                        }
                    />
                ) : (
                    <>
                        {label && (
                            <Typography
                                variant="body2"
                                fontInter
                                sx={{
                                    fontSize: "14px !important",
                                }}>
                                {label}
                            </Typography>
                        )}
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: richText ? "flex-end" : "center",
                                flexDirection: richText ? "column" : "row",
                                gap: 3,
                                border: richText ? "1px solid " + theme.palette.neutral[300] : "none",
                                borderRadius: "8px",
                                padding: richText ? 2 : 0,
                            }}>
                            <Box sx={{width: richText ? "100%" : "auto", flexGrow: 1}}>
                                {richText ? (
                                    <Box
                                        borderRadius={1}
                                        sx={{
                                            fontFamily: "var(--Inter)",
                                            color: theme.palette.neutral[700],
                                            pointerEvents: "none",
                                            "& a": {
                                                fontWeight: 600,
                                            },
                                        }}
                                        dangerouslySetInnerHTML={{__html: valueToBeCopied}}
                                    />
                                ) : (
                                    <Box
                                        borderRadius={1}
                                        sx={{
                                            fontFamily: "var(--Inter)",
                                            color: theme.palette.neutral[700],
                                            pointerEvents: "none",
                                            padding: 2,
                                            border: `1px solid ${theme.palette.neutral[300]}`,
                                            borderRadius: "8px",
                                            wordBreak: "break-all",
                                        }}>
                                        {valueToBeCopied}
                                    </Box>
                                )}
                                {!hideTermsAndService && (
                                    <Box sx={{fontFamily: "roc-grotesk, arial, verdana, helvetica, sans-serif"}}>
                                        <Box
                                            component="span"
                                            sx={{
                                                fontSize: "12px",
                                                color: "#0F0F0F",
                                                fontFamily: "roc-grotesk, arial, verdana, helvetica, sans-serif",
                                            }}>
                                            By embedding Channel Program code on your site, you are agreeing to the
                                            &nbsp;
                                        </Box>
                                        <CustomLink
                                            style={{
                                                fontSize: "12px",
                                                color: "#0F0F0F",
                                                fontFamily: "roc-grotesk, arial, verdana, helvetica, sans-serif",
                                                textDecoration: "underline",
                                            }}
                                            to={routeConfig.Terms.path}
                                            target="_blank">
                                            <u>Terms and Services</u>
                                        </CustomLink>
                                    </Box>
                                )}
                            </Box>
                            <Box
                                display="flex"
                                flexDirection="row"
                                justifyContent="flex-end"
                                alignItems="center"
                                gap={2}>
                                {showCopyButton && (
                                    <Button
                                        id="copyCode"
                                        variant="contained"
                                        color="secondary"
                                        onClick={() => copyToClipBoard(valueToBeCopied)}
                                        sx={{
                                            color: theme.palette.blue[600],
                                            fontWeight: "700!important",
                                            lineHeight: "19px",
                                            "& span": {
                                                marginRight: 0,
                                            },
                                            ...(copySuccess ? sxAfterSuccess || {} : sxBeforeSuccess || {}),
                                        }}
                                        startIcon={
                                            copySuccess ? (
                                                <DoneOutlinedIcon
                                                    sx={{color: copyIconColor || theme.palette.blue[600]}}
                                                />
                                            ) : (
                                                <ContentCopyOutlinedIcon
                                                    sx={{color: copyIconColor || theme.palette.blue[600]}}
                                                />
                                            )
                                        }
                                        {...(copyCodeBtnProps || {})}>
                                        {copySuccess ? "COPIED" : "COPY"}
                                    </Button>
                                )}
                                {showShareButton && (
                                    <ShareButton
                                        sx={{
                                            color: theme.palette.blue[600],
                                            fontWeight: "700!important",
                                            lineHeight: "19px",
                                        }}
                                        variant="textButton"
                                        url={shareUrl || ""}
                                        emailBody={valueToBeCopied.replaceAll("Click here", "")}
                                        {...(shareBtnProps || {})}
                                    />
                                )}
                            </Box>
                        </Box>
                    </>
                )}
            </Box>
        </FormProvider>
    );
}
