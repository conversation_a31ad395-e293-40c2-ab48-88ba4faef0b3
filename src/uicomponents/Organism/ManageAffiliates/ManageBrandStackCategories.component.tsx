import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import {DialogProps} from "@mui/material/Dialog";
import useTheme from "@mui/material/styles/useTheme";
import {useMspAffiliates} from "../../../hooks/fetches/useMspAffiliates";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import Loader from "../../../utils/loader";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
// import DragHandleOutlinedIcon from "@mui/icons-material/DragHandleOutlined";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import DeleteOutlineOutlined from "@mui/icons-material/DeleteOutlineOutlined";
import {Divider} from "@mui/material";
import {AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {ICategory} from "../../../Interfaces/categories.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {SUBCATEGORY_TEXT, addToStack, getStackTypeLabel} from "../../../constants/commonStrings.constant";
import {useNaviStack} from "../../../hooks/fetches/useNaviStack";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useSettings from "../../../hooks/useSettings";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import Badge from "../../Atoms/Badge/badge.component";
import Button from "../../Atoms/Button/Button.Component";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import CategoryAutocomplete from "../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import CompanyType from "../../../constants/companyType.constant";

interface IProps extends DialogProps {
    handleClose: (event?: any) => void;
    friendly_url?: string;
    onOpenAddToStack?: (categoryId?: string) => void;
}

const ManageBrandStackCategories = (props: IProps) => {
    const [isAddingCategory, setIsAddingCategory] = useState<boolean>(false);
    const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
    const [defaultCategoryId, setDefaultCategoryId] = useState<string>("");
    const saving = false;
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {mspBrandStackAdoptionDetail} = useMspAffiliates(props.friendly_url, {
        calls: {all: false, mspBrandStackAdoptionDetail: true},
    });
    const {naviStack, updateNaviStack, isUpdating} = useNaviStack(activeCompany?.id || "", {
        isCompany: true,
        isMSP: activeCompany?.type_is_of_vendor === false,
        calls: {all: false},
        loadRecommendedStack: true,
    });
    const isSaving = isUpdating[MutateTypes.AddToStack];
    const {settings, getAllCategories} = useSettings();
    const methods = useForm();
    const {all_categories} = settings;
    const category_by_key =
        all_categories?.reduce((prev, cur) => {
            prev[cur.id] = cur;
            return prev;
        }, {}) || {};
    const parentCategoryWatcher = methods.watch("main_category");
    const subCategoryWatcher = methods.watch("sub_category");
    const selectedSubCategory = subCategoryWatcher ? category_by_key[subCategoryWatcher] : null;
    const isEmpty = !mspBrandStackAdoptionDetail.data?.length;
    const categoryRecentlyAdded = useRef<string>("");
    const stackTypeLabel = getStackTypeLabel(activeCompany?.company_type !== CompanyType.FRANCHISE_CORPORATE_MSP);

    const handleSuccessClick = async () => {
        if (showSuccessMessage) {
            setShowSuccessMessage(false);
            setIsAddingCategory(true);
            methods.reset();
            return;
        }
        if (isAddingCategory) {
            const passedValidation = await methods.trigger();
            if (!passedValidation) return;
            const {sub_category} = methods.getValues();
            const requestBody = {
                company_id: activeCompany?.id,
                partner_status: null,
                is_recommended_stack: true,
            };
            updateNaviStack.mutate({
                subject_id: activeCompany?.id,
                requestBody: {[sub_category]: [requestBody]},
                mutate_type: MutateTypes.AddToStack,
                onSuccess: (res: AxiosResponse) => onSuccessAddCategory(res),
                msp_friendly_url: activeCompany?.friendly_url,
                updateAdoptionDetails: true,
            });
            return;
        }
        props.onOpenAddToStack?.(undefined);
    };

    const handleAdditionalBtnClick = () => {
        if (showSuccessMessage) {
            props.onOpenAddToStack?.(categoryRecentlyAdded.current);
            props.handleClose?.();
            return;
        }
        if (isAddingCategory) {
            setIsAddingCategory(false);
            return;
        }
        if (!isAddingCategory) {
            setIsAddingCategory(true);
        }
    };

    const onSuccessAddCategory = (res?: AxiosResponse) => {
        defaultCategoryId && setDefaultCategoryId("");
        setIsAddingCategory(false);
        methods.reset();
        setShowSuccessMessage(true);
        categoryRecentlyAdded.current = res?.data?.[0]?.category_id || "";
    };

    const handleRemoveCategory = async (category: ICategory) => {
        const isParentCategory = !category.parent_id;
        const answer = await getUserConfirmation("Remove Product?", {
            mui: true,
            modalTheme: "error",
            title: `Remove ${isParentCategory ? "Category" : SUBCATEGORY_TEXT}?`,
            form: (
                <Typography variant="body4" textAlign="center">
                    Are you sure you want to remove <strong>{category.name} </strong>
                    from the {stackTypeLabel.toLowerCase()} stack?{" "}
                    {isParentCategory
                        ? "This action will remove all related subcategories and products."
                        : "This action will remove all related products."}
                </Typography>
            ),
            okButtonText: "REMOVE",
        });
        if (!answer.value) return false;
        updateNaviStack.mutate({
            mutate_type: MutateTypes.RemoveBrandStackCategory,
            subject_id: activeCompany?.id || "",
            category_id: category.id,
            mutate_is_updating_append: category.id,
            msp_friendly_url: activeCompany?.friendly_url,
            updateAdoptionDetails: true,
        });
    };

    useEffect(() => {
        if (isAddingCategory && defaultCategoryId) {
            methods.setValue("main_category", defaultCategoryId);
        }
        if (!isAddingCategory && defaultCategoryId) {
            setDefaultCategoryId("");
        }
    }, [isAddingCategory, defaultCategoryId]);

    useEffect(() => {
        props.open && getAllCategories();
        return () => {
            methods.reset();
            setIsAddingCategory(false);
            setShowSuccessMessage(false);
            setDefaultCategoryId("");
        };
    }, [props.open]);

    return (
        <ErrorBoundary>
            <ModalComponent
                open={props.open}
                onClose={props.handleClose}
                onSuccess={handleSuccessClick}
                title={isAddingCategory ? "Add Category" : `Manage ${stackTypeLabel} Stack`}
                justifyButtons="flex-start"
                okButtonText={
                    showSuccessMessage ? (
                        "Add Another"
                    ) : saving ? (
                        <Loader inline loading version="iconWhite" />
                    ) : isAddingCategory ? (
                        <>ADD TO STACK</>
                    ) : (
                        <>
                            <AddOutlinedIcon sx={{marginRight: 1}} /> ADD PRODUCT
                        </>
                    )
                }
                okBtnProps={{
                    disabled: isAddingCategory && !parentCategoryWatcher && !subCategoryWatcher,
                }}
                loading={isSaving}
                additionalBtnText={
                    showSuccessMessage ? "ADD PRODUCTS" : isAddingCategory ? "Back to Manage" : "ADD CATEGORY"
                }
                additionalBtnStartIcon={showSuccessMessage || isAddingCategory ? undefined : <AddOutlinedIcon />}
                onAdditionalBtnClick={handleAdditionalBtnClick}
                additionalBtnProps={{
                    variant: "outlined",
                }}
                cancelButtonText="CANCEL"
                icon={isAddingCategory ? <AddCircleOutlineOutlinedIcon /> : <SettingsOutlinedIcon color="neutral" />}
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflow: "hidden", gap: 2},
                }}
                showAdditionalBtn={true}
                content={
                    showSuccessMessage ? (
                        <AlertBanner
                            id="add-category-to-stack-success"
                            variant="success"
                            title={{
                                text: `Categories/Subcategories successfully added to your ${stackTypeLabel} Stack.`,
                            }}
                            subTitle={{text: "Click the buttons below to add more, or start adding products"}}
                        />
                    ) : isAddingCategory ? (
                        <FormProvider {...methods}>
                            <Box display="flex" flexDirection={{xs: "column", md: "row"}} gap={3}>
                                <Box flex={1}>
                                    <CategoryAutocomplete
                                        wrapperSx={{display: "flex", flexDirection: "column", width: "100%", gap: 3}}
                                        parentCategoryLabel="Category"
                                        subCategoryLabel={SUBCATEGORY_TEXT}
                                        isRequired
                                        validateOnTheFly
                                    />
                                </Box>
                                <Box flex={1}>
                                    <Box
                                        flex="1"
                                        title={addToStack}
                                        minWidth={116}
                                        minHeight={85}
                                        padding={1}
                                        borderRadius="8px"
                                        display="flex"
                                        flexDirection="column"
                                        justifyContent="center"
                                        position="relative"
                                        overflow="hidden"
                                        gap="16px"
                                        border={`1px solid ${theme.palette.neutral[300]}`}
                                        bgcolor={theme.palette.blue[100]}
                                        alignItems="center"
                                        alignSelf="stretch"
                                        sx={{
                                            cursor: "pointer",
                                            minWidth: 135,
                                            ":hover": {
                                                bgcolor: theme.palette.primary[100],
                                                border: `1px solid ${theme.palette.primary[300]}`,
                                            },
                                        }}>
                                        <Box
                                            display="flex"
                                            flexDirection="row"
                                            alignItems="center"
                                            gap={1}
                                            width="100%"
                                            padding={0.5}
                                            borderBottom={`1px solid ${theme.palette.neutral[500]}`}>
                                            <Box
                                                sx={{
                                                    height: 16,
                                                    width: 16,
                                                    minWidth: 16,
                                                    minHeight: 16,
                                                    borderRadius: "50%",
                                                    backgroundColor:
                                                        selectedSubCategory?.color || theme.palette.neutral[400],
                                                }}
                                            />
                                            <Typography
                                                variant="body3"
                                                fontWeight={500}
                                                fontInter
                                                color={theme.palette.neutral[700]}>
                                                {selectedSubCategory?.name ? selectedSubCategory.name : "Please Select"}
                                            </Typography>
                                        </Box>
                                        <Box
                                            display="flex"
                                            height={80}
                                            minWidth={116}
                                            padding={1}
                                            alignSelf="stretch"
                                            gap={1}
                                            bgcolor={theme.palette.neutral[200]}></Box>
                                    </Box>
                                </Box>
                            </Box>
                        </FormProvider>
                    ) : (
                        <Box display="flex" flexDirection="column" gap={1} sx={{overflowY: "auto"}}>
                            {mspBrandStackAdoptionDetail.isLoading ? (
                                <Box>Loading...</Box>
                            ) : !isEmpty ? (
                                mspBrandStackAdoptionDetail.data
                                    ?.sort((a, b) => {
                                        const aName = category_by_key[a.category_id]?.name || "";
                                        const bName = category_by_key[b.category_id]?.name || "";
                                        return aName.localeCompare(bName);
                                    })
                                    ?.map(parentCategoryDetails => {
                                        const parentCategory = category_by_key[parentCategoryDetails?.category_id];
                                        const parentProductCount =
                                            naviStack?.data?.filter(i => i.category?.parent_id === parentCategory?.id)
                                                ?.length || 0;
                                        const isRemovingParentCategory =
                                            isUpdating[MutateTypes.RemoveBrandStackCategory + parentCategory?.id];
                                        return (
                                            <BorderedBox
                                                key={parentCategoryDetails.category_id}
                                                display="flex"
                                                flexDirection="column"
                                                sx={{
                                                    padding: 1,
                                                    gap: 0,
                                                    boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.10)",
                                                }}>
                                                <Box
                                                    display="flex"
                                                    alignItems="center"
                                                    flexWrap="wrap"
                                                    justifyContent="space-between"
                                                    borderBottom={`1px solid ${theme.palette.neutral[300]}`}
                                                    sx={{
                                                        flexDirection: "column",
                                                        alignItems: "center",
                                                        paddingBottom: 1,
                                                        "@media (min-width: 768px)": {
                                                            flexDirection: "row",
                                                            paddingBottom: 0,
                                                            gap: 1,
                                                            paddingRight: 2,
                                                        },
                                                    }}>
                                                    <Box display="flex" alignItems="center" gap={1} padding={1}>
                                                        {/* <DragHandleOutlinedIcon /> */}
                                                        <Box
                                                            sx={{
                                                                fill: parentCategory?.color,
                                                                backgroundColor: parentCategory?.color,
                                                                borderRadius: "50%",
                                                                height: 11,
                                                                width: 11,
                                                            }}
                                                        />
                                                        <Typography
                                                            fontInter
                                                            variant="body3"
                                                            weight={600}
                                                            color={theme.palette.neutral[700]}>
                                                            {parentCategory?.name}
                                                        </Typography>
                                                        <Badge
                                                            color="blue"
                                                            tooltip={{
                                                                title: "Number of recommended products in all subcategories.",
                                                                placement: "top",
                                                            }}>
                                                            {parentProductCount}
                                                        </Badge>
                                                    </Box>
                                                    <Box
                                                        display="flex"
                                                        flexDirection="row"
                                                        alignItems="center"
                                                        gap={2}
                                                        marginLeft={0}>
                                                        <Button
                                                            id={`add-subcategory-${parentCategory?.id}`}
                                                            color="blue"
                                                            sx={{padding: "4px 8px"}}
                                                            onClick={() => {
                                                                setDefaultCategoryId(parentCategory?.id || "");
                                                                setIsAddingCategory(true);
                                                            }}>
                                                            <AddCircleOutlineOutlinedIcon /> Add Subcategory
                                                        </Button>
                                                        <Box
                                                            sx={{cursor: "pointer"}}
                                                            title="Delete Category"
                                                            onClick={() => handleRemoveCategory(parentCategory)}>
                                                            {isRemovingParentCategory ? (
                                                                <Loader inline loading />
                                                            ) : (
                                                                <DeleteOutlineOutlined color="error" />
                                                            )}
                                                        </Box>
                                                    </Box>
                                                </Box>
                                                <Box
                                                    display="flex"
                                                    flexDirection="column"
                                                    sx={{
                                                        gap: 1,
                                                        "@media (min-width: 768px)": {
                                                            gap: 0,
                                                        },
                                                    }}>
                                                    {parentCategoryDetails.sub_categories
                                                        ?.sort((a, b) => {
                                                            const aName =
                                                                category_by_key[a.sub_category_id]?.name || "";
                                                            const bName =
                                                                category_by_key[b.sub_category_id]?.name || "";
                                                            return aName.localeCompare(bName);
                                                        })
                                                        ?.map(subCategoryDetails => {
                                                            const subCategory =
                                                                category_by_key[subCategoryDetails.sub_category_id];
                                                            const productCount =
                                                                naviStack?.data?.filter(
                                                                    i => i.category?.id === subCategory?.id,
                                                                )?.length || 0;
                                                            const isRemovingSubcategory =
                                                                isUpdating[
                                                                    MutateTypes.RemoveBrandStackCategory +
                                                                        subCategory?.id
                                                                ];
                                                            return (
                                                                <Box
                                                                    key={subCategoryDetails.sub_category_id}
                                                                    display="flex"
                                                                    sx={{
                                                                        flexDirection: "column",
                                                                        columnGap: 2,
                                                                        border: `1px solid ${theme.palette.neutral[300]}`,
                                                                        borderRadius: 1,
                                                                        alignItems: "center",
                                                                        padding: "8px 16px",
                                                                        "@media (min-width: 768px)": {
                                                                            justifyContent: "space-between",
                                                                            flexDirection: "row",
                                                                            border: "none",
                                                                        },
                                                                    }}>
                                                                    <Box
                                                                        display="flex"
                                                                        flexDirection="row"
                                                                        alignItems="center"
                                                                        marginLeft={0}
                                                                        sx={{
                                                                            gap: "4px",
                                                                            "@media (min-width: 768px)": {
                                                                                gap: 2,
                                                                            },
                                                                        }}>
                                                                        <Typography
                                                                            fontInter
                                                                            variant="body4"
                                                                            color={theme.palette.blue[800]}
                                                                            sx={{
                                                                                textAlign: "center",
                                                                                "@media (min-width: 768px)": {
                                                                                    textAlign: "left",
                                                                                },
                                                                            }}>
                                                                            {subCategory?.name}
                                                                        </Typography>
                                                                        {!!productCount && (
                                                                            <Badge
                                                                                color="blue"
                                                                                tooltip={{
                                                                                    title: "Number of recommended products in this subcategory.",
                                                                                    placement: "top",
                                                                                }}>
                                                                                {productCount}
                                                                            </Badge>
                                                                        )}
                                                                    </Box>
                                                                    <Box
                                                                        display="flex"
                                                                        flexDirection="row"
                                                                        alignItems="center"
                                                                        justifyContent="flex-end"
                                                                        gap={2}
                                                                        flex="1 0 auto">
                                                                        <Button
                                                                            id={`add-subcategory-${subCategory?.id}`}
                                                                            color="blue"
                                                                            onClick={() => {
                                                                                props.onOpenAddToStack?.(
                                                                                    subCategory?.id,
                                                                                );
                                                                            }}
                                                                            variant="text"
                                                                            sx={{padding: "4px 8px"}}>
                                                                            <AddCircleOutlineOutlinedIcon /> Add Product
                                                                        </Button>
                                                                        <Box
                                                                            sx={{cursor: "pointer"}}
                                                                            title={`Delete ${SUBCATEGORY_TEXT}`}
                                                                            onClick={() =>
                                                                                handleRemoveCategory(subCategory)
                                                                            }>
                                                                            {isRemovingSubcategory ? (
                                                                                <Loader inline loading />
                                                                            ) : (
                                                                                <DeleteOutlineOutlined color="error" />
                                                                            )}
                                                                        </Box>
                                                                    </Box>
                                                                </Box>
                                                            );
                                                        })}
                                                </Box>
                                            </BorderedBox>
                                        );
                                    })
                            ) : (
                                <>
                                    <Typography variant="body4" fontInter color="#000">
                                        Manage your {stackTypeLabel.toLowerCase()} stack recommendations by adding a new
                                        category, an applicable subcategory, or recommending a specific vendor product.
                                    </Typography>
                                    <Divider sx={{marginY: 2}} />
                                    <Box
                                        width="100%"
                                        display="flex"
                                        justifyContent="center"
                                        alignItems="center"
                                        height={400}>
                                        <Typography variant="body4" fontInter color="#000" textAlign="center">
                                            You haven't recommended any categories, subcategories, or products to your
                                            locations yet. <br /> Click{" "}
                                            <strong
                                                style={{cursor: "pointer", color: theme.palette.blue[600]}}
                                                onClick={() => {
                                                    setIsAddingCategory(true);
                                                }}>
                                                Add Category
                                            </strong>{" "}
                                            to add your recommendations.
                                        </Typography>
                                    </Box>
                                </>
                            )}
                        </Box>
                    )
                }
            />
        </ErrorBoundary>
    );
};

export default ManageBrandStackCategories;
