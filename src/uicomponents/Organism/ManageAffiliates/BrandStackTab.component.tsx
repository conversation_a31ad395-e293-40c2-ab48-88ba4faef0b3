import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import GridOnOutlinedIcon from "@mui/icons-material/GridOnOutlined";
import Grid from "@mui/material/Grid";
import {AxiosError, AxiosResponse} from "axios";
import {useState} from "react";
import {IStackCategorization} from "../../../Interfaces/myStack.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import {BuilderModal} from "../../../builder_cms/components/contactVendorModal";
import CompanyPartnersModal from "../../../components/Modal/companyPartnersModal.component";
import {getStackTypeLabel} from "../../../constants/commonStrings.constant";
import CompanyType from "../../../constants/companyType.constant";
import {useMspAffiliates} from "../../../hooks/fetches/useMspAffiliates";
import {useNaviStack} from "../../../hooks/fetches/useNaviStack";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import useRandomAdvertisement from "../../../hooks/useRandomAdvertisement";
import {mspPartnersService} from "../../../services/mspPartners.service";
import {PartnerPageService} from "../../../services/partnerpage.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {generateActivityLog} from "../../../utils/customTracking.util";
import {getErrorFromArray} from "../../../utils/error.util";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import AddToStackDialog from "../AddToStackDialog/addToStackDialog.component";
import MSPProductClassification from "../MSPProductClassification/MSPProductClassification.component";
import ManageStack from "../ManageStack/ManageStack.component";
import {IAvailableVendor} from "../MspTechStackTab/MspTechStackTab.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import VendorInfoModalV2 from "../VendorInfoModalV2/vendorInfoModalV2.component";
// import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";
// import DownloadOutlinedIcon from "@mui/icons-material/DownloadOutlined";

interface IProps {
    company_id?: string;
    friendly_url?: string;
    openManageBrandStack?: () => void;
}

const BrandStackTab = (props: IProps) => {
    const [viewMode, setViewMode] = useState<"list-view" | "category-view">("category-view");
    const [contactVendorModal, setContactVendorModal] = useState<any>();
    const [loadingAction, setLoadingAction] = useState<any>({});
    const [addToStackModalProps, setAddToStackModalProps] = useState<{
        open: boolean;
        showError: boolean;
        defaultCategory?: string;
        defaultProduct?: any;
    }>({
        open: false,
        showError: false,
        defaultCategory: undefined,
        defaultProduct: undefined,
    });
    const {naviStack, myStackSearchState, myStackFiltersState, updateNaviStack} = useNaviStack(props.company_id || "", {
        isCompany: true,
        isMSP: true,
        calls: {myStackFilters: true, naviStack: true},
        loadRecommendedStack: true,
    });
    const [companyInfoModal, setCompanyInfoModal] = useState({
        open: false,
        friendly_url: "",
    });
    const [showPartnersFor, setShowPartnersFor] = useState<string>("");
    // const [exportingCSV, setExportingCSV] = useState<boolean>(false);
    const partnerToShow = null;
    const {activeCompany} = useActiveCompany();
    const {authState} = useAuthState();
    const company_id = activeCompany?.id;
    const {sponsoredNaviStackVendors} = useRandomAdvertisement("NaviStack Sponsored");
    const notify = useNotification();
    const {mspBrandStackAdoption, mspBrandStackAdoptionDetail} = useMspAffiliates(props.friendly_url, {
        calls: {all: true},
    });
    const stackTypeLabel = getStackTypeLabel(activeCompany?.company_type !== CompanyType.FRANCHISE_CORPORATE_MSP);
    const brandStackAdoptionProgress = mspBrandStackAdoption.data?.brand_stack_adoption ?? 0;
    const myStackProductsIDs = naviStack.data?.flatMap(item => item.product).map(product => product?.id || "") || [];
    let uniqueVendors: string[] = [];
    if (naviStack) {
        uniqueVendors =
            naviStack.data?.reduce((acc: string[], current) => {
                if (!acc.includes(current.stack_company_id)) {
                    acc.push(current.stack_company_id);
                }
                return acc;
            }, [] as string[]) || [];
    }

    const onSuccessAddProducts = () => {
        notify("New product(s) added to your stack.", "Success");
        naviStack.refetch();
    };

    const handleViewPortal = (vendor: IAvailableVendor) => {
        const url = `${window.location.protocol}//${vendor.subdomain || ""}.${window.location.host}?as=${
            activeCompany?.id
        }&cn=${vendor?.name || ""}`;
        const newTab = window.open(url, "_blank");
        newTab?.focus();
    };

    const handleDeleteInvite = async (vendor: IAvailableVendor, type: string, successCallback?: Function) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: type === "Remove" ? "Remove Access?" : "Cancel Request?",
            okButtonText: type === "Remove" ? "Remove" : "Yes",
            form: (
                <Grid container display={"flex"} flexDirection={"column"}>
                    {type === "Remove" ? (
                        <Typography variant="body2" textAlign={"center"}>
                            You will no longer have access to the Channel Command Portal for{" "}
                            <strong>{vendor?.name}</strong>. <br />
                            To regain access in the future, you’ll need to submit a new request for access.
                        </Typography>
                    ) : (
                        <Typography variant="body2" style={{textAlign: "center", fontSize: "20px !important"}}>
                            Are you sure want to cancel this access request?
                        </Typography>
                    )}
                </Grid>
            ),
        });
        if (!answer.value) return;
        setLoadingAction({...loadingAction, [vendor.id]: true});
        PartnerPageService.removeInvite(
            activeCompany?.id || "",
            vendor.id,
            true,
            () => {
                notify(
                    type === "Remove"
                        ? "Success! Access has been removed."
                        : "Success! Your request for access has been removed.",
                    "Success",
                );
                // refetchVendors();
                setLoadingAction({...loadingAction, [vendor.id]: false});
                successCallback && successCallback(type === "Remove" ? "removeAccess" : "cancelRequest", vendor.id);
            },
            err => {
                setLoadingAction({...loadingAction, [vendor.id]: false});
                notify(getErrorFromArray(err), "Error");
                // refetchVendors();
            },
        );
    };

    const handleRequestAccess = (vendorId: any, successCallback?: Function) => {
        if (company_id) {
            setLoadingAction({...loadingAction, [vendorId]: true});
            mspPartnersService.inviteVendor(
                vendorId,
                {email: [authState.email], initiator: "user"},
                (res: any) => {
                    onSuccessRequestAccess(res, vendorId);
                    successCallback && successCallback("requestAccess", vendorId);
                },
                handleError,
            );
        } else {
            notify("Request access for company is not available.", "Error");
        }
    };

    const onSuccessRequestAccess = (res: AxiosResponse, vendorId) => {
        setLoadingAction({...loadingAction, [vendorId]: false});
        notify("Thank you! Your request has been received", "Success");
    };

    const handleOptionSelect = (option: any, vendor: IAvailableVendor, successCallback?: Function) => {
        const selectedOption = typeof option === "string" ? option : option.id;
        switch (selectedOption) {
            case "viewPortal":
                handleViewPortal(vendor);
                break;
            case "messagePartner":
                setShowPartnersFor(vendor.id);
                break;
            case "removeAccess":
                handleDeleteInvite(vendor, "Remove", successCallback);
                break;
            case "cancelRequest":
                handleDeleteInvite(vendor, "Invitation", successCallback);
                break;
            case "requestAccess":
                handleRequestAccess(vendor.id, successCallback);
                break;
            case "contactVendor":
                setContactVendorModal({
                    friendly_url: vendor.friendly_url,
                    company_name: vendor.name,
                    name: vendor.name,
                });
                break;
        }
    };

    const handleRemoveFromStack = async (stack: IStackCategorization) => {
        const answer = await getUserConfirmation("Remove Product?", {
            mui: true,
            modalTheme: "error",
            title: "Remove Product?",
            form: (
                <Typography variant="body4" textAlign="center">
                    Are you sure you want to remove{" "}
                    <strong>
                        {stack.stack_company?.name} {stack.product?.name + " "}
                    </strong>
                    from your technology stack?
                </Typography>
            ),
            okButtonText: "REMOVE",
            customModalBtnTracking: generateActivityLog(stack, "stack", props.company_id),
        });
        if (!answer.value) return false;
        updateNaviStack.mutate({
            mutate_type: MutateTypes.RemoveFromStack,
            subject_id: props.company_id || "",
            stack_id: stack.id,
            onSuccess: () => onSuccessRemove(stack),
            onError: handleError,
        });
    };

    const onSuccessRemove = (stack: IStackCategorization) => {
        notify(`${stack.product?.name} has been successfully been removed from your stack.`, "Success");
        naviStack.refetch();
    };

    const handleRemoveFromStackDialogEvent = async (product_id: string) => {
        const myStack = naviStack.data ?? [];
        const stackIndex = myStack.findIndex(stack => stack.product_id === product_id);
        handleRemoveFromStack(myStack[stackIndex]);
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    // const handleClickExportCsv = () => {};

    return (
        <Box sx={{marginTop: 3}}>
            <PageInnerHeader
                id="your-stack"
                // ? Will be reenabled next release for brand stack
                // filters={myStackFilters?.data}
                // searchState={myStackSearchState}
                // filterState={myStackFiltersState}
                filterKeysOrder={["rating", "partner_flag", "partner_status"]}
                noData={!myStackSearchState?.[0] && !myStackFiltersState?.[0] && !naviStack.data?.length}
                title={{
                    text: `${stackTypeLabel} Stack`,
                }}
                indicator={{
                    text: brandStackAdoptionProgress + "%",
                    color:
                        brandStackAdoptionProgress < 25
                            ? "error"
                            : brandStackAdoptionProgress < 50
                              ? "warning"
                              : brandStackAdoptionProgress < 75
                                ? "blue"
                                : "success",
                }}
                actions={{
                    tabs: {
                        id: "msp_contract_tabs",
                        props: {
                            tabsSx: {".MuiTabs-flexContainer": {flexWrap: "wrap"}},
                            tabs: [
                                {
                                    id: "category-view",
                                    label: (
                                        <Box display="flex" alignItems="center">
                                            <GridOnOutlinedIcon sx={{marginRight: 1}} />
                                            Category View
                                        </Box>
                                    ),
                                },
                                {
                                    id: "list-view",
                                    label: (
                                        <Box display="flex" alignItems="center">
                                            <FormatListBulletedIcon sx={{marginRight: 1}} /> List View
                                        </Box>
                                    ),
                                },
                            ] as any,
                            defaultTab: "category-view",
                            handleChange: (tab: any) => {
                                setViewMode(tab.id);
                            },
                            hideIndicator: true,
                        },
                    },
                }}
                growSearch
            />
            {viewMode === "category-view" ? (
                <Box display="flex" flexDirection="column" width="100%" marginTop={2}>
                    <MSPProductClassification
                        companyId={props.company_id}
                        numOfCategoriesFilledCallback={() => null}
                        isBrandStack
                        onClickAddToStack={(category_id?: string, product?: any) => {
                            setAddToStackModalProps({
                                open: true,
                                showError: false,
                                defaultCategory: category_id,
                                defaultProduct: product,
                            });
                        }}
                        brandStackAdoptionDetail={mspBrandStackAdoptionDetail.data}
                        companyFriendlyUrl={props.friendly_url}
                        sponsoredNaviStackVendors={sponsoredNaviStackVendors}
                        openBrandStackManageCategories={props.openManageBrandStack}
                    />
                </Box>
            ) : (
                <ManageStack
                    percentage={0}
                    companyId={props.company_id}
                    handleOptionSelect={handleOptionSelect}
                    loadingActions={loadingAction}
                    openAddToStack={(category_id?: string) => {
                        setAddToStackModalProps({
                            open: true,
                            showError: false,
                            defaultCategory: category_id,
                        });
                    }}
                    openManageBrandStack={props.openManageBrandStack}
                    openCompanyProfileModal={(open, friendly_url) => setCompanyInfoModal({open, friendly_url})}
                    setContactVendorModal={(friendly_url: string, company_name: string, name: string) =>
                        setContactVendorModal({friendly_url, company_name, name})
                    }
                    setShowPartnersFor={setShowPartnersFor}
                    sponsoredNaviStackVendors={sponsoredNaviStackVendors}
                    searchState={myStackSearchState}
                    filterState={myStackFiltersState}
                    naviStack={naviStack}
                    isBrandStack
                    // isMSP={isMSP}
                    // readOnly={readOnly}
                    uniqueVendors={uniqueVendors}
                    openBrandStackManageCategories={props.openManageBrandStack}
                />
            )}
            {addToStackModalProps?.open && (
                <AddToStackDialog
                    onClose={(vendorFriendlyUrl?: string) => {
                        setAddToStackModalProps({
                            open: false,
                            showError: false,
                            defaultCategory: undefined,
                            defaultProduct: undefined,
                        });
                        if (vendorFriendlyUrl) {
                            setCompanyInfoModal({
                                open: true,
                                friendly_url: vendorFriendlyUrl,
                            });
                        }
                    }}
                    onSave={onSuccessAddProducts}
                    companyId={props.company_id || ""}
                    allowMultipleCategories={false}
                    isBrandStack
                    sponsoredNaviStackVendors={sponsoredNaviStackVendors}
                    {...addToStackModalProps}
                />
            )}
            <VendorInfoModalV2
                open={!!companyInfoModal?.friendly_url && companyInfoModal?.open}
                onClose={() => {
                    setCompanyInfoModal({
                        ...companyInfoModal,
                        open: false,
                    });
                }}
                companyFriendlyUrl={companyInfoModal?.friendly_url}
                onClickAddToStack={(category_id?: string, product?: any) => {
                    setAddToStackModalProps({
                        open: true,
                        showError: false,
                        defaultCategory: category_id,
                        defaultProduct: product,
                    });
                }}
                myStackProductsIDs={myStackProductsIDs}
                onClickRemoveFromStack={handleRemoveFromStackDialogEvent}
            />
            {contactVendorModal?.friendly_url && contactVendorModal?.company_name && (
                <BuilderModal
                    friendly_url={contactVendorModal?.friendly_url}
                    companyName={contactVendorModal?.company_name}
                    open={contactVendorModal?.friendly_url && contactVendorModal?.company_name}
                    hideToggleBtn
                    onClose={() => {
                        setContactVendorModal(undefined);
                    }}
                    customUri={window.location.href}
                    customPageName={`NaviStack - ${contactVendorModal?.company_name}`}
                />
            )}
            {partnerToShow && showPartnersFor ? (
                <CompanyPartnersModal
                    show={true}
                    setShow={() => setShowPartnersFor("")}
                    type="claimers"
                    partner={partnerToShow || undefined}
                />
            ) : null}
        </Box>
    );
};

export default BrandStackTab;
