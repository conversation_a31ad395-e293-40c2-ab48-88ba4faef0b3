import useTheme from "@mui/material/styles/useTheme";
import CompanyType from "../../../constants/companyType.constant";
import {useMspAffiliates} from "../../../hooks/fetches/useMspAffiliates";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {pluralizeString} from "../../../utils/formatString.util";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import ProgressBar from "../../Molecules/ProgressBar/progressBar.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import usePermissions from "../../../hooks/usePermissions";
import useAppConfig from "../../../hooks/useAppConfig";
import {useNavigate} from "react-router-dom";
import {getStackTypeLabel} from "../../../constants/commonStrings.constant";
import StickyNote2Outlined from "@mui/icons-material/StickyNote2Outlined";
import StoreOutlined from "@mui/icons-material/StoreOutlined";

const ManageAffiliatesKPIHeader = ({subTitle}: {subTitle?: string}) => {
    const {activeCompany} = useActiveCompany();
    const {mspBrandStackAdoption} = useMspAffiliates(activeCompany?.friendly_url, {
        calls: {
            all: false,
            mspBrandStackAdoption: true,
        },
        useLocations: activeCompany?.company_type !== CompanyType.FRANCHISE_CORPORATE_MSP,
    });
    const theme = useTheme();
    const {config, isLoadingConfig} = useAppConfig({config_key: "MSP_MULTISTACK_SUBSCRIBE_LINK"});
    const navigate = useNavigate();
    const subscribeNowLink = isLoadingConfig ? "" : config?.value;
    const showSubscribeNow = !activeCompany?.manage_affiliates;
    const totalAffiliates = mspBrandStackAdoption.data?.registered_affiliates ?? 0;
    const totalContracts = mspBrandStackAdoption.data?.active_vendor_contracts ?? 0;
    const brandStackAdoptionProgress = mspBrandStackAdoption.data?.brand_stack_adoption ?? 0;
    const isCorporationMSP = activeCompany?.company_type === CompanyType.FRANCHISE_CORPORATE_MSP;
    const stackTypeLabel = getStackTypeLabel(!isCorporationMSP);

    const goToSubscibeNow = () => {
        if (subscribeNowLink?.includes("http") || subscribeNowLink?.includes("www.")) {
            window.location.href = subscribeNowLink;
            return;
        }
        navigate(subscribeNowLink || "/");
    };

    return (
        <>
            <PageInnerHeader
                id="manage-affiliates-header"
                title={{
                    text: "Manage Your Locations",
                }}
            />
            {showSubscribeNow ? (
                <Box
                    height={170}
                    padding="16px 24px"
                    justifyContent="center"
                    alignItems="center"
                    display="flex"
                    flexDirection="column"
                    gap={2}
                    flex="1 0 0"
                    borderRadius={2}
                    bgcolor={theme.palette.primary[100]}>
                    <Typography variant="body2" fontInter fontSize="20px !important" textAlign="center">
                        As a corporation, you can create your {stackTypeLabel} Stack,{" "}
                        <strong>
                            a selection of recommended and required products for your locations, follow up on adoption
                            rates and keep track of vendor expenses.
                        </strong>
                    </Typography>
                    <Button id="subscribe-now" variant="tonal" onClick={goToSubscibeNow}>
                        Subscribe Now
                    </Button>
                </Box>
            ) : (
                <>
                    <Typography
                        variant="body3"
                        fontInter
                        sx={{
                            color: "neutral.800",
                            fontSize: "16px",
                            fontWeight: "400!important",
                        }}>
                        {subTitle ||
                            "You can mandate specific products for your locations, monitor their adoption, track expenses at each location, and manage customer relationships effectively."}
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 3,
                            flexWrap: "wrap",
                            "@media (min-width: 768px)": {
                                flexWrap: "nowrap",
                            },
                        }}>
                        <Box
                            sx={{
                                padding: 2,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 2,
                                flexGrow: 1,
                                maxWidth: "100%",
                                border: "1px solid",
                                borderColor: "neutral.300",
                                borderRadius: 1,
                                "@media (min-width: 768px)": {
                                    maxWidth: "calc(50% - 14px)",
                                },
                            }}>
                            <StoreOutlined color="neutral" sx={{width: "32px", height: "32px"}} />
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "32px!important",
                                    fontWeight: "600!important",
                                }}>
                                {totalAffiliates ?? 0}
                            </Typography>
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "16px!important",
                                    fontWeight: "500!important",
                                }}>
                                Registered
                                <br />
                                {pluralizeString("Location", totalAffiliates)}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                padding: 2,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 2,
                                flexGrow: 1,
                                maxWidth: "100%",
                                border: "1px solid",
                                borderColor: "neutral.300",
                                borderRadius: 1,
                                "@media (min-width: 768px)": {
                                    maxWidth: "calc(50% - 14px)",
                                },
                            }}>
                            <StickyNote2Outlined color="neutral" sx={{width: "32px", height: "32px"}} />
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "32px!important",
                                    fontWeight: "600!important",
                                }}>
                                {totalContracts}
                            </Typography>
                            <Typography
                                variant="body3"
                                fontInter
                                sx={{
                                    color: "neutral.800",
                                    fontSize: "16px!important",
                                    fontWeight: "500!important",
                                }}>
                                Active Vendor
                                <br />
                                {pluralizeString("Contract", totalContracts)}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                padding: 2,
                                display: "flex",
                                justifyContent: "center",
                                flexDirection: "column",
                                gap: 2,
                                flexGrow: 1,
                                maxWidth: "100%",
                                border: "1px solid",
                                borderColor: "neutral.300",
                                borderRadius: 1,
                                "@media (min-width: 768px)": {
                                    maxWidth: "calc(50% - 14px)",
                                },
                            }}>
                            <Typography sx={{color: theme.palette.neutral[800]}} variant="body3" fontWeight="500">
                                {stackTypeLabel} Stack Adoption
                            </Typography>
                            <ProgressBar
                                colors={{
                                    25: "error",
                                    50: "warning",
                                    75: "blue",
                                    100: "success",
                                }}
                                value={brandStackAdoptionProgress}
                                labels={{
                                    0: "0%",
                                    25: "25%",
                                    50: "50%",
                                    75: "75%",
                                    100: "100%",
                                }}
                                progressBarTitle={(progress: string) => `${progress}% adoption rate`}
                                style={{
                                    height: "10px",
                                    marginTop: "5px",
                                }}
                            />
                        </Box>
                    </Box>
                </>
            )}
        </>
    );
};

export default ManageAffiliatesKPIHeader;
