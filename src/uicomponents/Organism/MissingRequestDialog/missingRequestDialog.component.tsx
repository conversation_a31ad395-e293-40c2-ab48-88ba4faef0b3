import SendOutlinedIcon from "@mui/icons-material/SendOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {AxiosError} from "axios";
import {useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import useNotification from "../../../hooks/useNotification";
import {companyService} from "../../../services/company.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ModalComponent from "../../Molecules/Modal/Modal.component";
import UserListInput from "../../Molecules/UserListInput/UserListInput.component";

interface IProps {
    open: boolean;
    onClose?: Function;
    companyId: string;
    onSave?: Function;
    onlyVendors?: boolean;
    defaultInputValue?: string;
}

const ahSnapText =
    "We are always adding dozens of new products every month. Help us by sending your request and we will get right on it!";

const MissingRequestDialog = (props: IProps) => {
    const [saving, setSaving] = useState(false);
    const methods = useForm();
    const notify = useNotification();
    const theme = useTheme();

    const onSuccess = () => {
        setSaving(false);
        notify("Your request has been received", "Success");
        props.onSave && props.onSave();
    };

    const handleError = (error: AxiosError<any>) => {
        setSaving(false);
        notify(getErrorFromArray(error), "Error");
    };

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const formValues = methods.getValues();
        setSaving(true);
        if (props.onlyVendors) {
            companyService.sendMissingVendorRequests(props.companyId, formValues.vendor_names, onSuccess, handleError);
        } else {
            companyService.sendMissingRequest(
                props.companyId,
                {
                    vendor_name: formValues.vendor_name,
                    product_name: formValues.product_name,
                },
                onSuccess,
                handleError,
            );
        }
    };

    return (
        <ModalComponent
            open={props.open}
            onClose={props.onClose as any}
            onSuccess={handleSubmit}
            icon={<SendOutlinedIcon />}
            title="Send Request"
            okButtonText="SEND"
            okButtonClassName="me-4"
            contentClassName="mt-0"
            justifyButtons="flex-start"
            modalTheme="blue"
            loading={saving}
            content={
                <FormProvider {...methods}>
                    <div style={{marginTop: 24}}>
                        <Typography variant="body4" marginBottom={3} color={theme.palette.neutral[800]}>
                            {props.onlyVendors
                                ? "We are always growing our network. Help us by letting us know which vendors are missing!"
                                : ahSnapText}
                        </Typography>
                        <form
                            className="d-flex flex-row align-items-center justify-content-around"
                            style={{marginBottom: 24, marginTop: 24}}>
                            {props.onlyVendors ? (
                                <UserListInput
                                    id="vendor_names"
                                    sx={{width: "100%"}}
                                    label="Vendors"
                                    placeholder="Enter the name of the missing vendor(s)"
                                    isRequired
                                    defaultInputValue={props.defaultInputValue}
                                />
                            ) : (
                                <>
                                    <TextBoxComponent
                                        id="vendor_name"
                                        placeholder="Enter Vendor Name"
                                        label="Vendor Name"
                                        isRequired
                                        validateOnTheFly
                                    />
                                    <TextBoxComponent
                                        id="product_name"
                                        placeholder="Enter Product Name"
                                        label="Product Name"
                                        isRequired
                                        validateOnTheFly
                                    />
                                </>
                            )}
                        </form>
                    </div>
                </FormProvider>
            }
        />
    );
};

export default MissingRequestDialog;
