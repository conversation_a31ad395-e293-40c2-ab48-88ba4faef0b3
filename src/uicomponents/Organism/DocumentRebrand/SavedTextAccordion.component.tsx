import {Add} from "@mui/icons-material";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import SaveIcon from "@mui/icons-material/Save";
import Tooltip from "@mui/material/Tooltip";
import {AxiosError, AxiosResponse} from "axios";
import {useEffect, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IPartnerTextAsset} from "../../../Interfaces/partnerAssets.interface";
import {InfoIcon} from "../../../components/Icons/info.icon";
import ReactIcon from "../../../components/Icons/reactIcon.component";
import {useInfinite} from "../../../hooks/useInfinite";
import useNotification from "../../../hooks/useNotification";
import {partnerAssetService} from "../../../services/partnerAssets.service";
import {getErrorFromArray} from "../../../utils/error.util";
import Loader from "../../../utils/loader";
import Accordion from "../../Atoms/Accordion/Accordion.component";
import AccordionDetails from "../../Atoms/Accordion/AccordionDetails.component";
import AccordionSummary from "../../Atoms/Accordion/AccordionSummary.component";
import Button from "../../Atoms/Button/Button.Component";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ParagraphComponent from "../../Typography/Paragraph.component";
import PartnerSavedText from "./PartnerSavedText.component";

interface IProps {
    buttonTrackingCustomProperties: string;
    company_id: string;
    hasEditAccess?: boolean;
}

const SavedTextAccordion = (props: IProps) => {
    const {buttonTrackingCustomProperties, hasEditAccess} = props;
    const [loadingTextAssets, setLoadingTextAssets] = useState<boolean>(false);
    const [partnerTextAssets, setPartnerTextAssets] = useState<IPartnerTextAsset[]>([]);
    const [showAddNew, setShowAddNew] = useState<boolean>(false);
    const [isAddingNew, setIsAddingNew] = useState<boolean>(false);
    const methods = useForm({
        defaultValues: {
            title: "",
            content: "",
        },
    });
    const textToEditId = useRef("");
    const meta = useRef<any>({});
    const {lastElementRef, pageNum} = useInfinite(meta.current?.last_page || 0, loadingTextAssets);
    const notify = useNotification();

    const handleSave = (formData: any) => {
        if (textToEditId.current) {
            partnerAssetService.updateBrandableContentInfo(
                props.company_id,
                {
                    id: textToEditId.current,
                    title: formData.title,
                    content: formData.content,
                },
                (res: AxiosResponse) => {
                    setPartnerTextAssets(
                        partnerTextAssets.map((a: IPartnerTextAsset) => (a.id === textToEditId.current ? res.data : a)),
                    );
                    textToEditId.current = "";
                },
                handleError,
            );
        } else {
            setIsAddingNew(true);
            partnerAssetService.storeBrandableContentInfo(
                props.company_id,
                {
                    title: formData.title,
                    content: formData.content,
                },
                onSuccessAdd,
                (err: AxiosError) => {
                    setIsAddingNew(false);
                    handleError(err);
                },
            );
        }
        methods.reset();
        setShowAddNew(false);
    };

    const onSuccessAdd = (res: AxiosResponse) => {
        setIsAddingNew(false);
        setPartnerTextAssets([res.data, ...partnerTextAssets]);
    };

    const handleEditTextAsset = (asset: IPartnerTextAsset) => {
        textToEditId.current = asset.id;
        setShowAddNew(true);
        methods.setValue("title", asset.title);
        methods.setValue("content", asset.content);
    };

    const deleteTextAsset = (asset_id: string) => {
        const filtered = [...partnerTextAssets].filter(a => a.id !== asset_id);
        setPartnerTextAssets(filtered);
    };

    const getTextAssets = async () => {
        setLoadingTextAssets(true);
        partnerAssetService.getBrandableContentInfo(
            props.company_id,
            {paged: true, page: pageNum || 1, items_per_page: 6},
            res => {
                setPartnerTextAssets([...partnerTextAssets, ...res.data.data]);
                meta.current = res.data.meta;
                setLoadingTextAssets(false);
            },
            () => {
                setLoadingTextAssets(false);
            },
        );
    };

    const handleError = (error: AxiosError<any>) => {
        notify(getErrorFromArray(error), "Error");
    };

    useEffect(() => {
        getTextAssets();
    }, [pageNum]);

    return (
        <Accordion>
            <AccordionSummary expandIcon={<ReactIcon size="24px" iconName="MdExpandMore" />}>
                <ParagraphComponent text="Information" size={16} />
                <Tooltip
                    title="Drag the appropriate informational text and drop it on the document template in the preferred location"
                    placement="bottom-start"
                    classes={{
                        tooltip: "bg-light text-dark p-2",
                    }}>
                    <div>
                        <InfoIcon size={20} color="#8790A2" />
                    </div>
                </Tooltip>
            </AccordionSummary>
            <AccordionDetails>
                {!showAddNew && (
                    <div className="d-flex flex-row align-items-center justify-content-end mb-2">
                        <Button
                            id="add_new_text_asset"
                            variant="outlined"
                            color="secondary"
                            disabled={isAddingNew || !hasEditAccess}
                            onClick={() => setShowAddNew(true)}
                            data_custom_properties={buttonTrackingCustomProperties}>
                            {isAddingNew ? (
                                <Loader inline loading />
                            ) : (
                                <>
                                    <Add sx={{width: "18px", height: "18px"}} />
                                    Add
                                </>
                            )}
                        </Button>
                    </div>
                )}
                <FormProvider {...methods}>
                    {showAddNew && (
                        <form
                            className="d-flex flex-column align-items-start mb-3"
                            onSubmit={methods.handleSubmit(handleSave)}>
                            <div className="d-flex flex-row align-items-center w-100">
                                <TextBoxComponent id="title" containerClassName="w-75" isRequired label="Name" />
                            </div>
                            <TextBoxComponent
                                id="content"
                                multiline
                                containerClassName="w-100 mt-2"
                                isRequired
                                maxLength={250}
                                showCharacterCount
                                label="Informational Text"
                            />
                            <div className="d-flex flex-row justify-content-end w-100">
                                <IconButton
                                    id="cancel-add-text"
                                    size="large"
                                    onClick={() => {
                                        setShowAddNew(false);
                                        textToEditId.current = "";
                                    }}
                                    title="Cancel">
                                    <HighlightOffIcon
                                        htmlColor="#000"
                                        fontSize="large"
                                        sx={{
                                            height: "24px !important",
                                            width: "24px !important",
                                        }}
                                    />
                                </IconButton>
                                <IconButton
                                    id="save-brandable-text"
                                    size="large"
                                    type="submit"
                                    title="Save Informational Text">
                                    <SaveIcon
                                        htmlColor="#000"
                                        fontSize="large"
                                        sx={{
                                            height: "24px !important",
                                            width: "24px !important",
                                        }}
                                    />
                                </IconButton>
                            </div>
                        </form>
                    )}
                </FormProvider>
                <div style={{maxHeight: 250, overflow: "auto"}}>
                    {loadingTextAssets && pageNum <= 1 ? (
                        <div className="d-flex flex-row align-items-center justify-content-center">
                            <Loader inline loading />
                        </div>
                    ) : partnerTextAssets.length === 0 ? (
                        <ParagraphComponent text="No saved text assets available." position="center" size="sm" />
                    ) : (
                        <>
                            {partnerTextAssets.map((asset: IPartnerTextAsset, index: number) => {
                                return (
                                    <PartnerSavedText
                                        key={index}
                                        asset={asset}
                                        index={index}
                                        tracking_properties={{
                                            saved_file_id: asset?.id,
                                            saved_file_title: asset.title,
                                        }}
                                        handleEdit={handleEditTextAsset}
                                        company_id={props.company_id}
                                        deleteAssetMedia={deleteTextAsset}
                                        hasEditAccess={hasEditAccess}
                                    />
                                );
                            })}
                            <div
                                ref={lastElementRef}
                                className="d-flex align-items-center justify-content-center"
                                style={{height: 50}}>
                                {loadingTextAssets && <Loader inline loading />}
                            </div>
                        </>
                    )}
                </div>
            </AccordionDetails>
        </Accordion>
    );
};

export default SavedTextAccordion;
