import {useEffect, useState} from "react";
import {Dropdown} from "react-bootstrap";
import {FormProvider, useForm} from "react-hook-form";
import {ISavedDocument} from "../../../Interfaces/documentRebrand.interface";
import {DocumentsIcon} from "../../../components/Icons/documents.icon";
import {ThreeDotsIcon} from "../../../components/Icons/vendorthreedots.icon";
import useAuthState from "../../../hooks/useAuthState";
import useNotification from "../../../hooks/useNotification";
import {PartnerPageService} from "../../../services/partnerpage.service";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import {formatDate} from "../../../utils/formatDate";
import Loader from "../../../utils/loader";
import Typography from "../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../FormControls/TextBox/textBox.component";
import ParagraphComponent from "../../Typography/Paragraph.component";

const PartnerSavedFile = (props: {
    savedFile: ISavedDocument;
    index: number;
    deleteSavedFile?: (file: ISavedDocument) => void;
    viewFile: Function;
    renameSavedFile?: Function;
    tracking_properties?: any;
    hasEditAccess?: boolean;
}) => {
    const {savedFile, index, hasEditAccess} = props;
    const [isLoading, setIsLoading] = useState(false);
    const {authState} = useAuthState();
    const notify = useNotification();
    const methods = useForm({
        defaultValues: {
            title: savedFile.custom_properties.title || "",
        },
    });

    const stringifiedCustomProperties = JSON.stringify(props.tracking_properties || {});

    const handleDelete = async () => {
        const answer = await getUserConfirmation("", {
            okButtonText: "Remove file",
            cancelButtonText: "Don't remove",
            title: "Remove content?",
            form: (
                <div className="d-flex flex-column align-items-center">
                    <Typography variant="body2">Are you sure you want to delete the file,</Typography>
                    <Typography variant="body2" fontWeight={600} className="my-2">
                        {savedFile.custom_properties.title || ""}
                    </Typography>
                    <Typography variant="body2">This process cannot be undone.</Typography>
                </div>
            ),
            mui: true,
            modalTheme: "error",
            justifyButtons: "center",
        });
        if (answer.value) {
            setIsLoading(true);
            PartnerPageService.deleteTemplateCopy(
                savedFile?.partner_template_id || "",
                () => {
                    props.deleteSavedFile && props.deleteSavedFile(savedFile);
                    setIsLoading(false);
                },
                () => {
                    setIsLoading(false);
                },
            );
        }
    };

    const handleEditName = async () => {
        const answer = await getUserConfirmation("", {
            okButtonText: "Save Update",
            cancelButtonText: "Don't Change",
            title: "Rename File",
            form: (
                <div className="d-flex flex-column">
                    <Typography variant="body2">You can change the name of the file here.</Typography>
                    <FormProvider {...methods}>
                        <form className="d-flex flex-column w-100">
                            <TextBoxComponent id="title" isRequired />
                        </form>
                    </FormProvider>
                </div>
            ),
            mui: true,
            modalTheme: "blue" as any,
            justifyButtons: "center",
        });
        if (answer.value) {
            const newFileName = methods.getValues("title");
            if (!newFileName) {
                notify("Please enter a file name", "Error");
                return handleEditName();
            }
            setIsLoading(true);
            PartnerPageService.updateSavedFile(
                savedFile?.partner_template_id || savedFile?.id,
                newFileName,
                res => {
                    props.renameSavedFile && props.renameSavedFile(res.data);
                    setIsLoading(false);
                },
                () => {
                    setIsLoading(false);
                },
            );
            return;
        }
        return methods.setValue("title", savedFile.custom_properties.title);
    };

    useEffect(() => {
        if (savedFile.custom_properties.title) {
            methods.setValue("title", savedFile.custom_properties.title);
        }
    }, [savedFile]);

    return (
        <div key={index} className="d-flex flex-row align-items-center my-2">
            <div style={{width: 50, height: 70, overflow: "hidden"}}>
                <DocumentsIcon mime={"application/pdf"} size="50" />
            </div>
            <div className="d-flex flex-column align-items-start ms-3 justify-content-start w-75 overflow-hidden">
                <ParagraphComponent text={savedFile.custom_properties.title || ""} size={12} />
                <ParagraphComponent text={formatDate(savedFile.created_at, "D")} size={12} />
            </div>
            <div className="ms-auto">
                <Dropdown>
                    <Dropdown.Toggle
                        variant="transparent"
                        split={false}
                        className="pt-0 pb-0 hideAfterElement"
                        data-track={false}
                        disabled={isLoading}>
                        {isLoading ? <Loader inline loading /> : <ThreeDotsIcon size={40} />}
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                        <Dropdown.Item
                            as="button"
                            data-track
                            className="d-flex"
                            onClick={() => props.viewFile && props.viewFile(savedFile)}
                            data-custom-properties={stringifiedCustomProperties}>
                            View File
                        </Dropdown.Item>
                        {authState.id === savedFile.author?.id && (
                            <>
                                <Dropdown.Item
                                    as="button"
                                    data-track
                                    className="d-flex"
                                    onClick={handleEditName}
                                    disabled={!hasEditAccess}
                                    data-custom-properties={stringifiedCustomProperties}>
                                    Edit Name
                                </Dropdown.Item>
                                <Dropdown.Item
                                    as="button"
                                    data-track
                                    className="d-flex"
                                    onClick={handleDelete}
                                    disabled={!hasEditAccess}
                                    data-custom-properties={stringifiedCustomProperties}>
                                    Delete
                                </Dropdown.Item>
                            </>
                        )}
                    </Dropdown.Menu>
                </Dropdown>
            </div>
        </div>
    );
};

export default PartnerSavedFile;
