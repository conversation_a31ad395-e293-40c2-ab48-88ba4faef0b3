import {Star, StarBorder} from "@mui/icons-material";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import CloseIcon from "@mui/icons-material/Close";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/material/styles/useTheme";
import React, {useEffect, useState} from "react";
import {ChipFilterItem} from "../../../Interfaces/MuiFilterChip.interface";
import {FilterIcon} from "../../../components/Icons/filter.icon";
import isMobileView from "../../../utils/isMobileView.util";
import Chip from "../../Atoms/Chip/Chip.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import MuiButtonComponent from "../../Buttons/MuiButtonComponent/MuiButton.component";
interface IProps {
    onChange: Function;
    resetKey?: number | string;
    filterValues: ChipFilterItem[];
    handleRemoveFilter?: Function;
    clearAllFilters?: Function;
    style?: React.CSSProperties;
}

const filters = [
    {
        type: "rating",
        label: "By Rating",
    },
    {
        type: "partner_flag",
        label: "By Portal",
    },
    {
        type: "partner_status",
        label: "By Status",
    },
];

export default function MyStackFilter(props: IProps) {
    const [starValue, setStarValue] = useState<any>();
    const theme = useTheme();
    const isMobile = isMobileView("768px");
    const ratingOptions: {faceValue: React.ReactNode; value: number}[] = [];
    const maxRating = 5;

    for (let i = 0; i <= maxRating - 1; i++) {
        const faceValue: React.ReactNode[] = [];
        for (let j = 1; j <= maxRating; j++) {
            const isSelected = starValue === i;
            const starComponent =
                j <= i ? (
                    <Star style={{color: isSelected ? theme.palette.warning[700] : theme.palette.neutral[500]}} />
                ) : (
                    <StarBorder style={{color: isSelected ? theme.palette.warning[700] : theme.palette.neutral[500]}} />
                );
            faceValue.push(starComponent);
        }
        ratingOptions.push({
            faceValue: (
                <div className="d-flex flex-row align-items-center">
                    {faceValue}{" "}
                    <Typography variant="body4" color="#000">
                        & Up
                    </Typography>
                </div>
            ),
            value: i,
        });
    }
    useEffect(() => {
        if (props?.filterValues?.length > 0) {
            const ratingValue = props.filterValues.find(a => a.key === "rating")?.value;
            setStarValue(ratingValue);
        }
    }, [props?.filterValues]);

    return (
        <Grid item container className={"MuiFilterContainer"} spacing={isMobile ? 0.5 : 0}>
            <Grid item className="d-none d-md-flex">
                <FilterIcon size={30} />
            </Grid>
            {filters?.length > 0 &&
                filters?.map((filter, index) => {
                    const selectedFilterExists = props?.filterValues?.find(item => item.key === filter.type);
                    return (
                        <Grid item key={index} onClick={e => props?.onChange(filter?.type, e)}>
                            <Chip
                                className={`filterChip${selectedFilterExists?.key ? " selectedChip" : ""}`}
                                label={
                                    props?.filterValues?.length > 0 && selectedFilterExists
                                        ? selectedFilterExists?.label
                                        : filter?.label
                                }
                                variant="outlined"
                                deleteIcon={
                                    selectedFilterExists?.key ? (
                                        <CloseIcon fontSize="small" />
                                    ) : (
                                        <ArrowDropDownIcon fontSize="medium" sx={{pointerEvents: "none"}} />
                                    )
                                }
                                onDelete={
                                    selectedFilterExists?.key
                                        ? () => props?.handleRemoveFilter!(filter?.type)
                                        : e => props?.onChange(filter?.type, e)
                                }></Chip>
                        </Grid>
                    );
                })}
            {props?.filterValues?.filter(a => a.key !== "search_word")?.length > 0 && (
                <Grid item>
                    <MuiButtonComponent
                        variant="text"
                        size={"xs"}
                        id="clearFilters"
                        color="secondary"
                        sx={{color: theme.palette.blue[800]}}
                        onClick={props?.clearAllFilters}>
                        Clear All Filters
                    </MuiButtonComponent>
                </Grid>
            )}
        </Grid>
    );
}
