import AddIcon from "@mui/icons-material/Add";
import HistoryOutlinedIcon from "@mui/icons-material/HistoryOutlined";
import Grid from "@mui/material/Grid";
import useTheme from "@mui/system/useTheme";
import {useNavigate} from "react-router-dom";
import {portalRequestPending} from "../../../constants/commonStrings.constant";
import routeConfig from "../../../constants/routeConfig";
import Loader from "../../../utils/loader";
import IconButton from "../../Atoms/IconButton/IconButton.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import AvatarComponent from "../../Avatars/Avatar.component";
import styles from "../MyStack/MyStack.module.sass";

interface IProps {
    activity: any;
    handleRequestAccess: (activity: any) => void;
    isRequestingAccess: string;
    disableActions?: boolean;
}

export default function AddToStackActivity(props: IProps) {
    const {activity, isRequestingAccess} = props;
    const theme = useTheme();
    const navigate = useNavigate();
    return (
        <Grid container spacing={2}>
            <Grid item xs={2}>
                <div title="View Vendor">
                    <AvatarComponent
                        isCompany={true}
                        user={{
                            avatar: activity?.avatar || "",
                            profile_type: activity?.type,
                            friendly_url: activity?.friendly_url || "",
                            name: activity?.name || "",
                            type: activity?.company_type || "",
                        }}
                        size={40}
                        showProfileType
                        profileTypeMinimized
                        isRedirectEnabled={true}
                    />
                </div>
            </Grid>
            <Grid item xs={8} textOverflow="ellipsis" overflow="hidden">
                <Typography
                    className={`ms-1 ${styles.textOverflow}`}
                    variant="body2"
                    title="View Portal"
                    color={theme.palette.neutral[700]}
                    fontWeight="400 !important"
                    lineHeight="120% !important"
                    fontSize="16px !important">
                    {activity.name}
                </Typography>
                {activity?.subcategories?.length > 0 && (
                    <div
                        onClick={() =>
                            navigate(`${routeConfig.Categories.path}/${activity.subcategories[0]?.friendly_url}`)
                        }
                        style={{
                            border: "none",
                            color: "var(--cpBlue)",
                            padding: "4px 6px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            fontSize: 14,
                            cursor: "pointer",
                        }}>
                        {activity.subcategories[0]?.name}
                    </div>
                )}
            </Grid>
            <Grid item xs={2} sx={{display: "flex", justifyContent: "flex-end"}}>
                {activity?.partner ? (
                    <IconButton
                        size="small"
                        className="outlined-button mt-1"
                        title={portalRequestPending}
                        disabled={props.disableActions}
                        sx={{
                            color: "#7ABC79",
                            border: "1px solid #A4D0A4 !important",
                            background: "#E8F4E8",
                        }}>
                        <HistoryOutlinedIcon />
                    </IconButton>
                ) : (
                    <IconButton
                        size="small"
                        className="outlined-button mt-1"
                        title="Request Access"
                        disabled={props.disableActions}
                        onClick={() =>
                            isRequestingAccess.indexOf(activity?.id) === -1 && props.handleRequestAccess(activity)
                        }>
                        {isRequestingAccess.includes(activity?.id) ? (
                            <Loader inline version="iconOrange" loading={true} />
                        ) : (
                            <AddIcon sx={{fontSize: "14px"}} />
                        )}
                    </IconButton>
                )}
            </Grid>
        </Grid>
    );
}
