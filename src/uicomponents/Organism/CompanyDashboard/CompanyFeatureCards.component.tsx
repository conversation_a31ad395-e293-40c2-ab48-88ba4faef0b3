import React, {useMemo} from "react";
import useTheme from "@mui/material/styles/useTheme";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import useActiveCompany from "../../../hooks/useActiveCompany";
import Typography from "../../Atoms/Typography/Typography.component";
import Box from "../../Atoms/Box/Box.component";
import {Skeleton} from "@mui/material";
import {useCompanyDashboard} from "../../../hooks/fetches/useCompanyDashboard";
import routeConfig from "../../../constants/routeConfig";
import {IS_BETTERTRACKER_SITE} from "../../../constants/siteFlags";
import Button from "../../Atoms/Button/Button.Component";
import {useNavigate} from "react-router";
import usePricingLink from "../../../hooks/usePricingLink";

const BETTER_TRACKER_WATERMARK = "/Media/bettertracker_new/logo-light-gray.png";
const NAVI_STACK_WATERMARK = "/Media/navi-stack.png";

const CompanyFeatureCards = () => {
    const {activeCompany, isClientMsp, isDirect, isBasicMSP, isDirectBasic, hasExpensesAccess} = useActiveCompany();
    const {featureCount} = useCompanyDashboard(activeCompany?.id, {calls: {all: false, featureCount: true}});
    const subscriptionLink = usePricingLink();
    const theme = useTheme();
    const navigate = useNavigate();
    const cards = useMemo(() => {
        if (isBasicMSP) {
            return [
                {
                    title: IS_BETTERTRACKER_SITE || isClientMsp ? "Products" : "NaviStack",
                    subTitle: (
                        <Box maxWidth={327}> Navigate your Stack, organize it into categories and identify gaps! </Box>
                    ),
                    count: featureCount.data?.navi_stacks || 0,
                    countLabel: "added products",
                    bgColor: theme.palette.gradient.primary,
                    watermark: IS_BETTERTRACKER_SITE ? null : NAVI_STACK_WATERMARK,
                    linkPath: routeConfig.MyStack.path,
                },
                {
                    title: "BetterTracker",
                    subTitle: (
                        <Box
                            sx={{
                                "& ul li": {
                                    lineHeight: "1.5",
                                    fontSize: "14px !important",
                                },
                            }}>
                            <ul>
                                <li>
                                    <Typography color="neutral.100" variant="14" fontWeight={300} fontInter>
                                        CustomerTracker & Whitelabeled App
                                    </Typography>
                                </li>
                                <li>
                                    <Typography color="neutral.100" variant="14" fontWeight={300} fontInter>
                                        Contracts and Expenses under control with timely alerts
                                    </Typography>
                                </li>
                            </ul>
                            <Typography
                                color="neutral.100"
                                variant="14"
                                sx={{fontStyle: "italic"}}
                                fontWeight={300}
                                fontInter>
                                Available on MSP Business Premium
                            </Typography>
                        </Box>
                    ),
                    button: (
                        <>
                            <Button
                                sx={{
                                    background: theme.palette.gradient.green,
                                    fontWeight: 700,
                                    border: "none",
                                }}
                                id={"primaryButton"}
                                to={subscriptionLink}
                                target="_blank">
                                <Typography variant="14" color="blue.900" fontWeight={700}>
                                    Upgrade Now
                                </Typography>
                            </Button>
                        </>
                    ),
                    bgColor: theme.palette.gradient.lightBlue,
                    watermark: BETTER_TRACKER_WATERMARK,
                    linkPath: subscriptionLink,
                },
            ];
        }
        if (isDirectBasic)
            return [
                {
                    title: "Products",
                    subTitle: (
                        <Box maxWidth={327}>
                            <Typography color="neutral.100" variant="14" fontWeight={300} fontInter>
                                Manage your products & services, and organize it into categories.
                            </Typography>
                        </Box>
                    ),
                    count: featureCount.data?.navi_stacks || 0,
                    countLabel: "products added",
                    bgColor: theme.palette.gradient.primary,
                    watermark: null,
                    linkPath: routeConfig.MyStack.path,
                },
                {
                    title: "Upgrade Now",
                    subTitle: (
                        <Box>
                            <Typography color="neutral.100" variant="14" fontWeight={300} fontInter>
                                Manage your Contracts and Expenses seamlessly.
                            </Typography>
                        </Box>
                    ),
                    button: (
                        <>
                            <Button
                                sx={{
                                    background: theme.palette.gradient.green,
                                    fontWeight: 700,
                                    border: "none",
                                }}
                                id={"primaryButton"}
                                to={subscriptionLink}
                                target="_blank">
                                <Typography variant="14" color="blue.900" fontWeight={700}>
                                    Upgrade Now
                                </Typography>
                            </Button>
                        </>
                    ),
                    bgColor: theme.palette.gradient.lightBlue,
                    watermark: BETTER_TRACKER_WATERMARK,
                    linkPath: subscriptionLink,
                },
            ];
        return [
            {
                title: "Expenses",
                subTitle: "Sync your bank account or credit card to keep up with your Stack Expenses",
                count: featureCount.data?.accounts_synced || 0,
                countLabel: "accounts synced",
                bgColor: theme.palette.gradient.darkBlue,
                watermark: IS_BETTERTRACKER_SITE || isClientMsp ? null : BETTER_TRACKER_WATERMARK,
                linkPath: routeConfig.CompanyExpenses.path,
                noWhiteDetail: true,
            },
            {
                title: "Contracts",
                subTitle: "Follow up with your vendor contracts, renewal dates and expected costs.",
                count: featureCount.data?.contracts || 0,
                countLabel: "active contracts",
                bgColor: theme.palette.gradient.secondary,
                watermark: IS_BETTERTRACKER_SITE || isClientMsp ? null : BETTER_TRACKER_WATERMARK,
                linkPath: routeConfig.CompanyContracts.path,
            },
            {
                title: IS_BETTERTRACKER_SITE || isDirect || isClientMsp ? "Products" : "NaviStack",
                subTitle:
                    IS_BETTERTRACKER_SITE || isDirect || isClientMsp
                        ? "Manage your products & services, and organize it into categories."
                        : "Navigate your Stack, organize it into categories and identify gaps!",
                count: featureCount.data?.navi_stacks || 0,
                countLabel: "added products",
                bgColor: theme.palette.gradient.primary,
                watermark: IS_BETTERTRACKER_SITE || isClientMsp ? null : NAVI_STACK_WATERMARK,
                linkPath: isClientMsp ? routeConfig.CustomerNavistack.path : routeConfig.MyStack.path,
            },
            {
                title: "Customers",
                subTitle: "Manage your customers access and keep track of tech usage.",
                count: featureCount.data?.customers || 0,
                countLabel: "active customers",
                bgColor: theme.palette.gradient.pink,
                watermark: IS_BETTERTRACKER_SITE || isClientMsp ? null : BETTER_TRACKER_WATERMARK,
                linkPath: routeConfig.ManageClients.path,
            },
        ].filter(card => {
            if (!hasExpensesAccess && card.title === "Expenses") {
                return false;
            }
            if (IS_BETTERTRACKER_SITE || isDirect || isClientMsp) {
                return card.title !== "Customers";
            }

            return true;
        });
    }, [
        featureCount.isLoading,
        activeCompany?.id,
        activeCompany?.client_plaid_integration_enabled,
        isDirect,
        isClientMsp,
        subscriptionLink,
    ]);

    return (
        <Box display={"flex"} gap={"16px"}>
            <Box
                display={"grid"}
                sx={{
                    gridTemplateColumns: {
                        xs: "repeat(1, 1fr)",
                        md: "repeat(2, 1fr)",
                        lg: `repeat(${cards.length}, 1fr)`,
                    },
                    width: "100%",
                }}
                gap={2}>
                {cards.map(card => (
                    <Box
                        key={card.title}
                        display="flex"
                        padding={3}
                        flexDirection="column"
                        alignItems="flex-start"
                        justifyContent="space-between"
                        gap={2}
                        borderRadius={4}
                        position="relative"
                        overflow={"hidden"}
                        sx={{
                            cursor: "pointer",
                            background: card.bgColor,
                            "*": {zIndex: 1},
                            flexGrow: 1,
                            ...(isBasicMSP ? {paddingBottom: "80px", justifyContent: "center"} : {}),
                        }}
                        onClick={() => {
                            navigate(card.linkPath);
                        }}>
                        {card.watermark && (
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: 5,
                                    right: "-5px",
                                    width: 88,
                                    height: 90,
                                    zIndex: 0,
                                    background: `url(${card.watermark}) `,
                                    backgroundSize: "contain",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "right",
                                    filter: "grayscale(.5) brightness(100%) opacity(0.7)",
                                }}
                            />
                        )}
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                gap: 2,
                            }}>
                            <Typography color="neutral.100" variant="20" fontWeight={600}>
                                {card.title}
                            </Typography>
                            <Typography color="neutral.100" variant="14">
                                {card.subTitle}
                            </Typography>
                        </Box>

                        <Box
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                            width="100%"
                            sx={
                                isBasicMSP
                                    ? {
                                          position: "absolute",
                                          bottom: "20px",
                                          width: "90%",
                                      }
                                    : {}
                            }>
                            {card.button ? (
                                card.button
                            ) : (
                                <Typography
                                    padding="2px 6px"
                                    bgcolor={
                                        card.noWhiteDetail ? "rgba(229, 241, 254, 0.80)" : "rgba(232, 244, 232, 0.30)"
                                    }
                                    variant="14"
                                    borderRadius="17px"
                                    color={card.noWhiteDetail ? "blue.800" : "neutral.100"}
                                    fontWeight={600}>
                                    {featureCount.isLoading ? (
                                        <Skeleton variant="text" width={150} />
                                    ) : (
                                        <>
                                            {card.count} {card.countLabel}{" "}
                                        </>
                                    )}
                                </Typography>
                            )}

                            <Box sx={{cursor: "pointer"}}>
                                <ArrowForwardIosOutlinedIcon
                                    htmlColor={theme.palette.neutral[100]}
                                    sx={{height: 24, width: 24}}
                                />
                            </Box>
                        </Box>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};

export default CompanyFeatureCards;
