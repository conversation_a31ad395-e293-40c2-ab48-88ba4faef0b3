import Typography from "../../../Atoms/Typography/Typography.component";
import Box from "../../../Atoms/Box/Box.component";
import {Link} from "react-router-dom";
import routeConfig from "../../../../constants/routeConfig";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import FileCopyOutlinedIcon from "@mui/icons-material/FileCopyOutlined";
import React from "react";
import useNotification from "../../../../hooks/useNotification";
import KApiCard from "../../../Molecules/KApiCard/KApiCard.component";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useTheme} from "@mui/material";
import {useCompanyDashboard} from "../../../../hooks/fetches/useCompanyDashboard";
import {useMspClients} from "../../../../hooks/fetches/useMspClients";

const CustomerBetterTrackerCard = () => {
    const theme = useTheme();

    const notify = useNotification();
    const {activeCompany} = useActiveCompany();
    const {mspRegisterLink} = useMspClients(activeCompany?.friendly_url, {
        calls: {
            all: false,
            mspRegisterLink: true,
        },
    });
    const {quickOverview} = useCompanyDashboard(activeCompany?.id, {calls: {all: false, quickOverview: true}});

    const mspRegisterLinkUrl = mspRegisterLink?.data?.url ?? "";

    const handleCopyLink = async () => {
        try {
            if (mspRegisterLinkUrl) {
                await navigator.clipboard.writeText(mspRegisterLinkUrl);
                notify("The link has been copied to your clipboard.", "Success");
            }
        } catch (err) {
            notify("Failed to copy: " + err, "Error");
        }
    };

    return (
        <KApiCard
            isLoading={mspRegisterLink.isLoading || quickOverview.isLoading}
            title={"Customers on BetterTracker"}
            body={quickOverview.data?.customers}
            footer={
                <>
                    <Typography
                        variant="14"
                        fontInter
                        sx={{
                            color: "neutral.800",
                            fontWeight: "400",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                            flexGrow: 1,
                        }}
                        title={mspRegisterLinkUrl}>
                        {mspRegisterLink?.data?.url}
                    </Typography>
                    <Box display={"flex"} alignItems={"flex-end"} gap={2}>
                        <Box tooltip={{title: "Manage Customers"}}>
                            <Link to={routeConfig.ManageClients.path}>
                                <SettingsOutlinedIcon
                                    htmlColor={theme.palette.secondary[600]}
                                    sx={{height: 16, width: 16}}
                                />
                            </Link>
                        </Box>

                        <Box tooltip={{title: "Copy Invite URL"}}>
                            <FileCopyOutlinedIcon
                                onClick={handleCopyLink}
                                sx={{
                                    display: "inline-flex",
                                    cursor: "pointer",
                                    height: 16,
                                    width: 16,
                                }}
                                htmlColor={theme.palette.secondary[600]}
                            />
                        </Box>
                    </Box>
                </>
            }
        />
    );
};

export default CustomerBetterTrackerCard;
