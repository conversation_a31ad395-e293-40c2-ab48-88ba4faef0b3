import React from "react";
import KApiCard from "../../../Molecules/KApiCard/KApiCard.component";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useCompanyDashboard} from "../../../../hooks/fetches/useCompanyDashboard";
import {getCurrentMonth} from "../../../../utils/formatDate";

const UpcomingRenewalsCard = () => {
    const {activeCompany} = useActiveCompany();
    const {quickOverview} = useCompanyDashboard(activeCompany?.id, {calls: {all: false, quickOverview: true}});
    const currentMonth = getCurrentMonth();
    const upComingRenewals = quickOverview.data?.upcoming_renewal || 0;

    return (
        <KApiCard
            highLight={upComingRenewals > 0}
            isLoading={quickOverview.isLoading}
            title={`Upcoming Renewals/Notice`}
            body={upComingRenewals}
            footerLabel={`For this month`}
            info={`Upcoming Contract Renewals for the Month of ${currentMonth}`}
        />
    );
};

export default UpcomingRenewalsCard;
