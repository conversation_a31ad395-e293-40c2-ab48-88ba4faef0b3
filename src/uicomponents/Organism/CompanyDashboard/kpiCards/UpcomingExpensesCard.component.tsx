import React from "react";
import KApiCard from "../../../Molecules/KApiCard/KApiCard.component";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useCompanyDashboard} from "../../../../hooks/fetches/useCompanyDashboard";
import Amount from "../../../Molecules/Amount/Amount.component";
import {getCurrentMonth} from "../../../../utils/formatDate";

const UpcomingExpensesCard = () => {
    const {activeCompany} = useActiveCompany();
    const {quickOverview} = useCompanyDashboard(activeCompany?.id, {calls: {all: false, quickOverview: true}});
    return (
        <KApiCard
            isLoading={quickOverview.isLoading}
            title={`Upcoming Expenses`}
            body={<Amount amount={quickOverview.data?.upcoming_expenses.amount || 0} />}
            footerLabel={`In the next ${quickOverview.data?.upcoming_expenses.days} days`}
            info={`Upcoming Expenses for the month of ${getCurrentMonth()}`}
        />
    );
};

export default UpcomingExpensesCard;
