import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import {ADOPTION_STATUS, ADOPTION_STATUS_ICON} from "../../../constants/mspAdoption.constant";
import {SxProps, Theme} from "@mui/material";

interface IProps {
    showOnMobile?: boolean;
    sx?: SxProps<Theme>;
}

const AffiliateParentStackLegend = (props: IProps) => {
    return (
        <Box
            display={props.showOnMobile ? {xs: "flex", md: "none"} : {xs: "none", md: "flex"}}
            flexDirection={{xs: "column", md: "row"}}
            padding={1}
            justifyContent="center"
            alignItems={{xs: "flex-start", md: "center"}}
            gap={2}
            marginTop={2}
            width="100%"
            alignSelf="stretch"
            id="affiliate-brand-stack-legend"
            sx={props.sx}>
            <Box display="flex" gap={1}>
                {ADOPTION_STATUS_ICON[ADOPTION_STATUS.ADDED_TO_STACK]}
                <Typography fontInter variant="subtitle3" fontWeight={500}>
                    Added to Stack
                </Typography>
            </Box>
            <Box display="flex" gap={1}>
                {ADOPTION_STATUS_ICON[ADOPTION_STATUS.MISSING]}
                <Typography fontInter variant="subtitle3" fontWeight={500}>
                    Not on Stack
                </Typography>
            </Box>
            <Box display="flex" gap={1} alignItems="center">
                {ADOPTION_STATUS_ICON[ADOPTION_STATUS.AFFILIATE_CHOICE]}
                <Typography fontInter variant="subtitle3" fontWeight={500}>
                    Added by Location
                </Typography>
            </Box>
        </Box>
    );
};

export default AffiliateParentStackLegend;
