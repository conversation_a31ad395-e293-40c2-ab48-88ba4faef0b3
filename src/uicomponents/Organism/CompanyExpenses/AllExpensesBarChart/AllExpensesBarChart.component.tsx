import useTheme from "@mui/material/styles/useTheme";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useCompanyExpenses} from "../../../../hooks/fetches/useCompanyExpenses";
import Box from "../../../Atoms/Box/Box.component";
import Skeleton from "@mui/material/Skeleton";
import Bar<PERSON>hart from "../../../Atoms/BarChart/BarChart.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {useEffect, useMemo, useRef, useState} from "react";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {getStartAndEndDateOfYear, monthNamesShort, monthsLongAndShort} from "../../../../utils/formatDate";
import {ChartsReferenceLine} from "@mui/x-charts/ChartsReferenceLine";
import {BarPlot} from "@mui/x-charts/BarChart";
import useElementSize from "../../../../hooks/useElementSize";
import CircleIcon from "@mui/icons-material/Circle";
import {ExpenseItem} from "../../../../Interfaces/plaid.interface";
import FormatNumber from "../../../../utils/formatNumber.util";
import {DefaultizedBarSeriesType} from "@mui/x-charts/models/seriesType";
import {STACK_EXPENSES_TEXT} from "../../../../constants/siteFlags";

interface IProps {
    start_date?: string;
    end_date?: string;
    year?: string;
}

const AllExpensesBarChart = ({start_date, end_date, year}: IProps) => {
    const [chartBounds, setChartBounds] = useState({min: 0, max: 0});
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {allExpensesBarChartData, hasNoAccounts} = useCompanyExpenses(activeCompany?.id, {
        calls: {
            all: false,
            allExpensesBarChartData: true,
        },
        allExpensesBarChartDates: {
            ...(start_date && {start_date, end_date}),
            ...(year && {year}),
        },
    });
    const wrapperRef = useRef<HTMLDivElement | null>(null);
    const {width} = useElementSize(wrapperRef);
    const {isLoading, data} = allExpensesBarChartData;
    const monthStartIndex = start_date && end_date ? new Date(start_date).getMonth() : 0;
    const startYear = start_date ? new Date(start_date).getUTCFullYear() : Number(year);
    const numOfBars = start_date && end_date ? 13 : 12;
    const chartLabels = Array(numOfBars)
        .fill(null)
        .map((_, index) => {
            const monthIndex = monthStartIndex + index;
            const yearOffset = Math.floor(monthIndex / 12);
            const adjustedMonthIndex = monthIndex % 12;
            const long = monthsLongAndShort[adjustedMonthIndex]?.long;
            const year = startYear + yearOffset;
            return {
                short: monthNamesShort[adjustedMonthIndex],
                long,
                year,
                formatted: `${long} ${year}`,
            };
        });
    const prepareTableValues = (values: ExpenseItem[] | undefined) => {
        if (!values) return [];
        return Array(numOfBars)
            .fill(null)
            .map((_, index) => {
                const formattedLabel = chartLabels[index]?.formatted;
                const foundValue = values?.find(v => v.label === formattedLabel);
                return foundValue?.value || 0;
            });
    };
    const expenses = useMemo(() => prepareTableValues(data?.stack_expenses), [data]);
    const recurringExpenses = useMemo(() => prepareTableValues(data?.subscription_expenses), [data]);
    const unmappedExpenses = useMemo(() => prepareTableValues(data?.unmapped_expenses), [data]);
    const numOfHorizontalLines = 4;
    const horizontalOffset = chartBounds.max / numOfHorizontalLines;
    const chartLegendLabels = ["Unmapped Expenses", STACK_EXPENSES_TEXT, "Recurring Expenses"];
    const chartColors = {
        "Recurring Expenses": theme.palette.blue[600],
        [STACK_EXPENSES_TEXT]: theme.palette.blue[400],
        "Unmapped Expenses": theme.palette.blue[200],
    };
    const chartBarOrder = ["Recurring Expenses", STACK_EXPENSES_TEXT, "Unmapped Expenses"];

    const getChartLabel = (key: string) => {
        return key === "Expenses" ? STACK_EXPENSES_TEXT : key === "Total" ? "Unmapped Expenses" : key;
    };

    useEffect(() => {
        if (!isLoading) {
            const maxValue = Math.max(
                ...expenses.map(
                    (_, i) => (expenses[i] || 0) + (recurringExpenses[i] || 0) + (unmappedExpenses[i] || 0),
                ),
            );
            setChartBounds({min: 0, max: maxValue});
        }
    }, [isLoading, expenses, recurringExpenses, unmappedExpenses]);

    return (
        <ErrorBoundary>
            <Box sx={{width: "100%", minHeight: 200}} ref={wrapperRef}>
                {isLoading ? (
                    <Box
                        display="flex"
                        width="100%"
                        height="100%"
                        overflow="hidden"
                        alignItems="flex-end"
                        justifyContent="space-evenly">
                        {Array(12)
                            .fill(null)
                            .map((_, i) => (
                                <Skeleton key={i} height={i * 20} width={24} />
                            ))}
                    </Box>
                ) : hasNoAccounts ? null : !unmappedExpenses && !expenses && !recurringExpenses ? (
                    <Box
                        height="100%"
                        width="100%"
                        minHeight={200}
                        display="flex"
                        alignItems="center"
                        justifyContent="center">
                        <Typography>No Data</Typography>
                    </Box>
                ) : (
                    <Box width="100%" height="100%" sx={{overflowX: "auto", overflowY: "hidden"}}>
                        <Box display="flex" flexDirection="row" gap={2}>
                            {chartLegendLabels.map(label => (
                                <Box key={label} display="flex" flexDirection="row" alignItems="center" gap={1}>
                                    <CircleIcon htmlColor={chartColors[label]} sx={{height: 16, width: 16}} />
                                    <Typography variant="12" fontWeight={500}>
                                        {label}
                                    </Typography>
                                </Box>
                            ))}
                        </Box>
                        <BarChart
                            xAxis={[
                                {
                                    scaleType: "band",
                                    data: chartLabels.map(item => item.formatted),
                                    disableTicks: true,
                                    tickSize: 0,
                                    valueFormatter: v => {
                                        const month = v.split(" ")?.[0];
                                        return monthsLongAndShort?.find(m => m.long === month)?.short || "";
                                    },
                                },
                            ]}
                            yAxis={[
                                {
                                    disableLine: true,
                                    disableTicks: true,
                                    tickSize: 0,
                                    valueFormatter: v => FormatNumber.abbreviate(v, {roundThousands: true}),
                                },
                            ]}
                            series={[
                                {
                                    data: recurringExpenses,
                                    label: "Recurring Expenses",
                                    stack: "total",
                                    stackOffset: "none",
                                },
                                {
                                    data: expenses,
                                    label: "Expenses",
                                    stack: "total",
                                    stackOffset: "none",
                                },
                                {
                                    data: unmappedExpenses,
                                    label: "Unmapped Expenses",
                                    stack: "total",
                                    stackOffset: "none",
                                },
                            ]}
                            height={200}
                            width={width || 800}
                            slotProps={{
                                legend: {hidden: true},
                                axisLine: {
                                    style: {stroke: theme.palette.neutral[300], fill: theme.palette.neutral[300]},
                                },
                                axisTick: {display: "none"},
                                bar: {
                                    style: {
                                        width: 32,
                                        // clipPath: "url(#rounded-top)",
                                    },
                                },
                                axisTickLabel: {style: {transform: "translateX(-8px)"}},
                            }}
                            slots={{
                                axisContent: axisData => {
                                    const monthAndYear = axisData.axisValue;
                                    const seriesDataIndex = axisData.dataIndex || 0;
                                    const seriesData = axisData.series.map(series => {
                                        return {
                                            label: getChartLabel((series as {label: string}).label),
                                            value: series.data?.[seriesDataIndex] as number,
                                        };
                                    });
                                    return (
                                        <Box
                                            key={monthAndYear}
                                            display="flex"
                                            flexDirection="column"
                                            bgcolor={theme.palette.neutral[100]}
                                            boxShadow={4}
                                            gap={2}
                                            padding={2}
                                            borderRadius={2}>
                                            <Typography
                                                variant="20"
                                                fontWeight={700}
                                                paddingBottom={2}
                                                borderBottom={`1px solid ${theme.palette.neutral[500]}`}>
                                                {monthAndYear}
                                            </Typography>
                                            {seriesData.map(item => {
                                                return (
                                                    <Box
                                                        display="flex"
                                                        alignItems="center"
                                                        justifyContent="space-between">
                                                        <Typography
                                                            variant="16"
                                                            fontWeight={400}
                                                            marginRight={0.5}
                                                            color={theme.palette.neutral[800]}>
                                                            <CircleIcon
                                                                sx={{height: 16, width: 16, marginRight: 0.5}}
                                                                htmlColor={chartColors[item.label]}
                                                            />
                                                            {item.label}:{" "}
                                                        </Typography>
                                                        <Typography variant="16" fontWeight={600}>
                                                            {FormatNumber.currency(item.value || 0)}
                                                        </Typography>
                                                    </Box>
                                                );
                                            })}
                                            <Box display="flex" alignItems="center" justifyContent="space-between">
                                                <Typography
                                                    variant="16"
                                                    fontWeight={400}
                                                    marginRight={0.5}
                                                    color={theme.palette.neutral[800]}>
                                                    Total:{" "}
                                                </Typography>
                                                <Typography variant="16" fontWeight={600}>
                                                    {FormatNumber.currency(
                                                        seriesData.reduce((acc, curr) => acc + (curr.value || 0), 0),
                                                    )}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    );
                                },
                            }}
                            colors={chartBarOrder.map(label => chartColors[label])}
                            margin={{
                                top: 16,
                                bottom: 32,
                                right: 16,
                                left: 58,
                            }}
                            tooltip={{}}
                            sx={{
                                "text.MuiChartsReferenceLine-label": {
                                    fontSize: "10px !important",
                                },
                            }}
                            children={
                                <>
                                    {/* <svg width="32">
                                        <defs>
                                            <clipPath id="rounded-top">
                                                <rect width="100%" height="100%" rx="8" ry="8" />
                                            </clipPath>
                                        </defs>
                                    </svg> */}
                                    {chartBounds.min !== chartBounds.max &&
                                        Array.from({length: 4}, (_, i) => {
                                            const value = horizontalOffset * (i + 1);
                                            return (
                                                <ChartsReferenceLine
                                                    key={`ref-line-${i}`}
                                                    y={value}
                                                    lineStyle={{
                                                        stroke: theme.palette.neutral[300],
                                                        strokeOpacity: 0.6,
                                                    }}
                                                />
                                            );
                                        })}
                                    {chartLabels
                                        .filter((_, i) => i % 3 === 0)
                                        .map(item => {
                                            return (
                                                <ChartsReferenceLine
                                                    key={`ref-line-${item.formatted}`}
                                                    x={item.formatted}
                                                    lineStyle={{
                                                        stroke: theme.palette.neutral[300],
                                                        strokeOpacity: 0.6,
                                                    }}
                                                />
                                            );
                                        })}
                                    {chartLabels
                                        .filter(label => label.short === "Jan" && label.year !== startYear)
                                        .map(label => {
                                            return (
                                                <ChartsReferenceLine
                                                    key={`ref-line-${label.short}`}
                                                    x={label.formatted}
                                                    label={label.year.toString()}
                                                    labelAlign="start"
                                                    labelStyle={{
                                                        fontSize: "10px !important",
                                                        fill: theme.palette.neutral[600],
                                                        transform: "translateX(-16px)",
                                                    }}
                                                    lineStyle={{
                                                        stroke: theme.palette.neutral[300],
                                                        strokeOpacity: 0.6,
                                                        transform: "translateX(-16px)",
                                                    }}
                                                />
                                            );
                                        })}
                                </>
                            }
                        />
                    </Box>
                )}
            </Box>
        </ErrorBoundary>
    );
};

export default AllExpensesBarChart;
