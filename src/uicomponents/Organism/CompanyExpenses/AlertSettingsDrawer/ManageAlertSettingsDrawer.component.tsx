import ArrowCircleDownIcon from "@mui/icons-material/ArrowCircleDown";
import ArrowCircleUpIcon from "@mui/icons-material/ArrowCircleUp";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import RefreshOutlinedIcon from "@mui/icons-material/RefreshOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useAtom} from "jotai";
import {useEffect} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {useCompanyExpenses} from "../../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {IPlaidAlertNotificationOption, ISaveAlertSettingsPayload} from "../../../../Interfaces/plaid.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {validateEmail} from "../../../../utils/validation.util";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import Box from "../../../Atoms/Box/Box.component";
import Button from "../../../Atoms/Button/Button.Component";
import Drawer from "../../../Atoms/Drawer/Drawer.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {INITIAL_MODAL_DATA} from "../../../ModalManager/base.constant";
import {AlertSettingsDrawerOpenAtom} from "../../../ModalManager/ExpensesModals/expenses.atoms";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import CheckboxWithLabel from "../../../Molecules/CheckboxWithLabel/CheckboxWithLabel.component";
import CompanyEmailSearch from "../../../Molecules/CompanyEmailSearch/CompanyEmailSearch.component";
import RoleListInput from "../../../Molecules/RoleListInput/RoleListInput.component";
import AlertPeriodMenuButton from "./AlertPeriodMenuButton.component";
import {IS_BETTERTRACKER_SITE, NO_REPLY_CP_EMAIL} from "../../../../constants/siteFlags";

const ManageAlertSettingsDrawer = () => {
    const [drawerData, setDrawerData] = useAtom(AlertSettingsDrawerOpenAtom);
    const theme = useTheme();
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {alertSettings, alertOptions, alertPeriodOptions, updateCompanyExpenses, isUpdating} = useCompanyExpenses(
        activeCompany?.id,
        {
            calls: {
                all: false,
                alertSettings: true,
                alertOptions: true,
                alertPeriodOptions: true,
            },
        },
    );
    const methods = useForm();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const isLoading = alertSettings.isLoading || alertOptions.isLoading || alertPeriodOptions.isLoading;
    const isSaving = isUpdating?.[MutateTypes.UpdateAlertSettings];
    const selectedRoleId = methods.watch("role_id");
    if (!drawerData.open) return null;
    const NOTIFICATION_ICONS = {
        PLAID_INCREASES_IN_MY_EXPENSES: <ArrowCircleUpIcon htmlColor={theme.palette.error[500]} />,
        PLAID_DECREASES_IN_MY_EXPENSES: <ArrowCircleDownIcon htmlColor={theme.palette.success[500]} />,
        PLAID_STABLE_EXPENSES: <InfoOutlinedIcon htmlColor={theme.palette.blue[600]} />,
        PLAID_UPCOMING_EXPENSES: <RefreshOutlinedIcon htmlColor={theme.palette.blue[600]} />,
    };

    const onClose = () => {
        setDrawerData(INITIAL_MODAL_DATA);
    };

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation || !alertOptions.data) return;
        const values = methods.getValues();
        let hasRequiredPeriods = true;
        alertOptions.data?.forEach((option: IPlaidAlertNotificationOption) => {
            const period_id = values["period_id__" + option.id] || null;
            const active = !!values["active__" + option.id];
            if (active && !period_id) {
                hasRequiredPeriods = false;
                methods.setError("period_id__" + option.id, {type: "required"});
            }
        });
        if (!hasRequiredPeriods) return;
        const requestBody: ISaveAlertSettingsPayload = {
            alerts: [],
            recipients: {
                role_id: "",
                emails: [],
            },
        };
        alertOptions.data.forEach((option: IPlaidAlertNotificationOption) => {
            const period_id = values["period_id__" + option.id] || null;
            const active = !!values["active__" + option.id];
            requestBody.alerts.push({
                type_id: option.id,
                period_id,
                active,
            });
        });
        requestBody.recipients.role_id = values.role_id || null;
        requestBody.recipients.emails =
            values.emails?.map(email => (typeof email === "string" ? email : email.email)) || [];
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.UpdateAlertSettings,
            company_id: activeCompany?.id,
            body: requestBody,
            onSuccess: () => {
                onClose?.();
            },
        });
    };

    useEffect(() => {
        if (alertSettings.data) {
            const newValues = {};
            alertSettings.data.alerts.forEach(option => {
                newValues["active__" + option.type.id] = option.active;
                newValues["period_id__" + option.type.id] = option.period?.id || null;
            });
            newValues["role_id"] = alertSettings.data.recipients?.role_id?.id || null;
            newValues["emails"] = alertSettings.data.recipients?.emails || [];
            methods.reset(newValues);
        }
    }, [alertSettings.data]);

    return (
        <Drawer
            open
            onClose={onClose}
            anchor="right"
            sx={{
                ".MuiDrawer-paper": {
                    maxWidth: {xs: "100vw", lg: "50vw"},
                    width: {xs: "100vw", lg: "50vw"},
                    padding: 2,
                    overflowX: "hidden",
                },
            }}>
            <Box
                display="flex"
                flexDirection="row"
                gap={2}
                paddingBottom={2}
                borderBottom={`1px solid ${theme.palette.neutral[400]}`}>
                <Typography variant="20" fontWeight={500} color={theme.palette.neutral[700]}>
                    Manage Alert Settings
                </Typography>
            </Box>
            <Box display="flex" flexGrow={1} width="100%">
                <Box
                    marginTop={2}
                    display="grid"
                    height="fit-content"
                    width="100%"
                    gridTemplateColumns="1fr"
                    columnGap={3}
                    rowGap={2}>
                    <FormProvider {...methods}>
                        <AlertBanner
                            id="alert-settings-banner"
                            variant="info"
                            title={{text: "We can notify you about fluctuations in your expenses."}}
                            subTitle={{
                                text: "Select what notifications you’d like to receive and the desired frequency for the alerts.",
                            }}
                        />
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Typography variant="body3" fontWeight={500} color={theme.palette.neutral[800]}>
                                Notify me about:
                            </Typography>
                            <Box display="flex" flexDirection="column" gap={2}>
                                {isLoading
                                    ? Array(4)
                                          .fill(null)
                                          .map((_, index) => <Skeleton key={index} height={64} width="100%" />)
                                    : alertOptions.data?.map((alertOption: IPlaidAlertNotificationOption) => {
                                          const {id, name, value_key} = alertOption;
                                          const icon = NOTIFICATION_ICONS[value_key];
                                          return (
                                              <BorderedBox
                                                  key={id}
                                                  sx={{
                                                      display: "flex",
                                                      flexDirection: "row",
                                                      alignItems: "center",
                                                      paddingY: 1,
                                                      paddingX: 2,
                                                      gap: 1,
                                                  }}>
                                                  <CheckboxWithLabel
                                                      id={"active__" + id}
                                                      formControlSx={{
                                                          ".MuiCheckbox-root": {padding: 0},
                                                          ".MuiFormControlLabel-root": {margin: 0},
                                                      }}
                                                      onChange={(_, checked) => {
                                                          if (!checked) methods.setValue("period_id__" + id, null);
                                                      }}
                                                      disabled={!hasEditAccess}
                                                  />
                                                  {icon}
                                                  <Typography variant="body4" fontWeight={600} fontInter>
                                                      {name}
                                                  </Typography>
                                                  <Box justifySelf="flex-end" marginLeft="auto">
                                                      <AlertPeriodMenuButton
                                                          id={"period_id__" + id}
                                                          disabled={!methods.watch("active__" + id) || !hasEditAccess}
                                                      />
                                                  </Box>
                                              </BorderedBox>
                                          );
                                      })}
                            </Box>
                        </Box>
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Typography variant="body3" fontWeight={500} color={theme.palette.neutral[800]}>
                                Notification Recipients
                            </Typography>
                            <Typography variant="body4" fontInter fontWeight={400}>
                                <InfoOutlinedIcon sx={{marginRight: 1}} htmlColor={theme.palette.blue[600]} />
                                Be sure to add this email to your allowlist{" "}
                                <Typography
                                    variant="body4"
                                    fontInter
                                    fontWeight={400}
                                    color={theme.palette.blue[600]}
                                    sx={{textDecoration: "underline"}}>
                                    {NO_REPLY_CP_EMAIL}
                                </Typography>{" "}
                                ?
                            </Typography>
                            <RoleListInput
                                id="role_id"
                                label="Roles to be notified"
                                placeholder="Select Role"
                                companyId={activeCompany?.id}
                                disabled={!activeCompany?.id || !hasEditAccess}
                                validateOnTheFly
                                filterSelectedOptions={false}
                                disableClearable={false}
                                onClear={() => methods.setValue("role_id", undefined)}
                                key={selectedRoleId}
                                defaultValue={
                                    !selectedRoleId ? "" : alertSettings?.data?.recipients?.role_id?.display_name
                                }
                            />
                            <CompanyEmailSearch
                                id="emails"
                                label="Alert Recipients"
                                placeholder="Enter alert recipients"
                                helperText="Select a user from your company, and/or input the emails separated by commas. If no alert recipient is selected or inputted, no alerts will be received."
                                validateOption={v => validateEmail(v)}
                                disabled={!hasEditAccess}
                                sx={{
                                    "& div>div.MuiInputBase-root": {
                                        paddingBottom: "8px!important",
                                    },
                                }}
                            />
                        </Box>
                    </FormProvider>
                </Box>
            </Box>
            <Box
                display="flex"
                flexDirection="row"
                gap={1}
                paddingTop={2}
                borderTop={`1px solid ${theme.palette.neutral[400]}`}>
                {hasEditAccess && (
                    <Button
                        id="add-tag-btn"
                        variant="contained"
                        color="blue"
                        sx={{gap: 0.5}}
                        onClick={handleSubmit}
                        loading={isSaving}>
                        SAVE
                    </Button>
                )}
                <Button id="cancel-btn" variant="text" color="blue" onClick={onClose}>
                    CANCEL
                </Button>
            </Box>
        </Drawer>
    );
};

export default ManageAlertSettingsDrawer;
