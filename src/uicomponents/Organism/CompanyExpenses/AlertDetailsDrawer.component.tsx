import useTheme from "@mui/material/styles/useTheme";
import Drawer from "../../Atoms/Drawer/Drawer.component";
import Box from "../../Atoms/Box/Box.component";
import Typography from "../../Atoms/Typography/Typography.component";
import Button from "../../Atoms/Button/Button.Component";
import AlertBanner from "../../Molecules/AlertBanner/alertBanner.component";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import CompanyExpensesKPICard from "./kpiCards/CompanyExpenseKPICard.component";
import ExpensesOverTimeBarChart from "./ExpensesOverTimeBarChart.component";
import {useMemo, useRef} from "react";
import CompanyExpensesTransactionsTab from "./CompanyExpensesTransactionsTab.component";
import {PLAID_ALERT_TYPES} from "../../../constants/plaid.constant";
import {useCompanyExpenses} from "../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../hooks/useActiveCompany";
import {formatDate, MONTH_DAY_YEAR} from "../../../utils/formatDate";
import {pluralizeString} from "../../../utils/formatString.util";
import {DateTime} from "luxon";
import TableSearchFiltersMinimized from "../../../components/Table/TableSearchFiltersMinimized.component";
import UpcomingTransactionsTable from "./UpcomingTransactionsTable/UpcomingTransactionsTable.component";
import PlaidUpcomingExpensesKpiCard from "./kpiCards/PlaidUpcomingExpensesKpiCard.component";
import {useAtom} from "jotai";
import {AlertSettingsDrawerDataAtom} from "../../ModalManager/ExpensesModals/expenses.atoms";
import {INITIAL_MODAL_DATA} from "../../ModalManager/base.constant";

const AlertDetailsDrawer = () => {
    const [drawerData, setDrawerData] = useAtom(AlertSettingsDrawerDataAtom);
    const {data: alert} = drawerData;
    const isUpcomingAlert = alert?.type.value_key === PLAID_ALERT_TYPES.PLAID_UPCOMING_EXPENSES;
    const {activeCompany} = useActiveCompany();
    const now = DateTime.now();
    const {expensesSummary, upcomingSummary} = useCompanyExpenses(activeCompany?.id, {
        calls: {
            all: false,
            expensesSummary: !!alert && !isUpcomingAlert,
            upcomingSummary: !!alert && isUpcomingAlert,
        },
        dates: {
            start_date: now.startOf("month").toUTC().toISO(),
            end_date: now.endOf("month").toUTC().toISO(),
        },
    });
    const theme = useTheme();
    const chartWrapperRef = useRef(null);
    if (!drawerData.open || !drawerData.data) return null;
    const diffUp = expensesSummary.data?.all?.percentage_diff?.increased;
    const absDiff = Math.abs(
        expensesSummary.data?.all?.percentage_diff?.change_percentage
            ? Number(expensesSummary.data?.all?.percentage_diff?.change_percentage)
            : 0,
    );

    const onClose = () => {
        setDrawerData(INITIAL_MODAL_DATA);
    };

    const KpisAndTables = useMemo(() => {
        return !isUpcomingAlert ? (
            <>
                <Box display="grid" gridTemplateColumns={{xs: "1fr", md: "1fr 1fr"}} gap={2}>
                    <CompanyExpensesKPICard type="total-monthly-cost" />
                    <CompanyExpensesKPICard type="total-yearly-cost" />
                </Box>
                <Box maxWidth="100%" sx={{overflowX: "scroll", overflowY: "hidden"}}>
                    <Box minWidth={800} ref={chartWrapperRef}>
                        <ExpensesOverTimeBarChart />
                    </Box>
                </Box>
                <Box
                    display="flex"
                    flexDirection="row"
                    gap={2}
                    width="100%"
                    minWidth="100%"
                    sx={{overflowX: "scroll", overflowY: "hidden"}}>
                    <CompanyExpensesTransactionsTab
                        customFilterElement={({filterState, filters, searchState}) => {
                            return (
                                <Box display="flex" flexDirection="row" justifyContent="space-between">
                                    <Typography variant="body4" fontWeight={600} color={theme.palette.neutral[600]}>
                                        RELATED TRANSACTIONS
                                    </Typography>
                                    <TableSearchFiltersMinimized
                                        filterState={filterState}
                                        filters={filters}
                                        search={searchState?.[0]}
                                        setSearch={searchState?.[1]}
                                    />
                                </Box>
                            );
                        }}
                        hiddenColumns={["action", "contract", "add-to-stack", "category", "product"]}
                        hideFilterKeys={["category"]}
                        hideSearchAndFilters
                        useInfinite
                        fullWidthSearch
                        subscriptionId={alert?.subscription?.id}
                        dates={
                            alert?.extra_info?.current_date_range?.start && alert?.extra_info?.current_date_range?.end
                                ? {
                                      start_date: alert?.extra_info?.current_date_range?.start,
                                      end_date: alert?.extra_info?.current_date_range?.end,
                                  }
                                : undefined
                        }
                        hideKpis
                    />
                </Box>
            </>
        ) : (
            <>
                <PlaidUpcomingExpensesKpiCard />
                <UpcomingTransactionsTable />
            </>
        );
    }, [isUpcomingAlert, alert]);

    return (
        <Drawer
            open
            onClose={onClose}
            anchor="right"
            sx={{
                ".MuiDrawer-paper": {
                    maxWidth: {xs: "100vw", lg: "50vw"},
                    width: {xs: "100vw", lg: "50vw"},
                    padding: 2,
                    overflowX: "hidden",
                },
            }}>
            <Box
                display="flex"
                flexDirection="row"
                gap={2}
                paddingBottom={2}
                borderBottom={`1px solid ${theme.palette.neutral[400]}`}>
                <Typography variant="20" fontWeight={500} color={theme.palette.neutral[700]}>
                    Alert Details
                </Typography>
            </Box>
            <Box display="flex" flexGrow={1} width="100%" maxWidth="100%">
                <Box
                    marginTop={2}
                    display="grid"
                    height="fit-content"
                    width="100%"
                    gridTemplateColumns="1fr"
                    columnGap={3}
                    rowGap={2}>
                    <AlertBanner
                        id="alert-details-banner"
                        variant={isUpcomingAlert ? "warning" : diffUp ? "error" : "success"}
                        title={{
                            sx: {
                                "h1, h2, h3, h4, h5, h6": {},
                                fontWeight: "",
                            },
                            text: isUpcomingAlert ? (
                                <Typography variant="16" fontWeight={"400"} fontInter>
                                    As of today you have
                                    <Typography
                                        display="inline-block"
                                        variant="16"
                                        fontWeight={"700 !important"}
                                        fontSize="16px !important"
                                        fontInter>
                                        {upcomingSummary?.data?.count || 0} Upcoming{" "}
                                        {pluralizeString("Expense", upcomingSummary?.data?.count || 0)}.
                                    </Typography>
                                </Typography>
                            ) : (
                                <Typography variant="16" fontInter>
                                    Your expenses have been {diffUp ? "increased" : "reduced"} by{" "}
                                    <Typography
                                        display="inline-block"
                                        variant="16"
                                        fontWeight={"700 !important"}
                                        fontSize="16px !important"
                                        fontInter>
                                        {absDiff}%
                                    </Typography>{" "}
                                    {alert?.period?.name === "Daily"
                                        ? `as of ${formatDate(alert?.created_at, MONTH_DAY_YEAR)}`
                                        : `this ${alert?.period.name.toLowerCase().replace("ly", "")}`}
                                </Typography>
                            ),
                        }}
                    />
                    {KpisAndTables}
                </Box>
            </Box>
            <Box
                display="flex"
                flexDirection="row"
                gap={1}
                paddingTop={2}
                borderTop={`1px solid ${theme.palette.neutral[400]}`}>
                <Button id="close-btn" variant="contained" color="blue" sx={{gap: 0.5}} onClick={onClose}>
                    <CloseOutlinedIcon /> CLOSE
                </Button>
            </Box>
        </Drawer>
    );
};

export default AlertDetailsDrawer;
