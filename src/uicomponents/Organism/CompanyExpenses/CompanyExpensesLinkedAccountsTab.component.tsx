import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import SyncOutlinedIcon from "@mui/icons-material/SyncOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import TextField from "@mui/material/TextField";
import {AxiosResponse} from "axios";
import {DateTime} from "luxon";
import {useCallback, useMemo, useRef, useState} from "react";
import {IndeterminateCheckbox, TableV2Component} from "../../../components/Table/TableV2.component";
import {CPSortType} from "../../../enums/sortType.enum";
import {useCompanyExpenses} from "../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useAppConfig from "../../../hooks/useAppConfig";
import useElementSize from "../../../hooks/useElementSize";
import {useElementVisibility} from "../../../hooks/useElementVisibility";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {ILinkedAccountData} from "../../../Interfaces/plaid.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import debounce from "../../../utils/debounce.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {DATE_SHORT_MONTH_TIME, formatDate} from "../../../utils/formatDate";
import {pluralizeString} from "../../../utils/formatString.util";
import Loader from "../../../utils/loader";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import ExpenseAccountAvatar from "../../Molecules/ExpenseAccountAvatar/ExpenseAccountAvatar.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import NoAccountsBanner from "./noAccountsBanner.component";

const CompanyExpensesLinkedAccountsTab = ({setSyncInfo}: {setSyncInfo: any}) => {
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {
        pagedLinkedAccounts,
        linkedAccountsSearch,
        setLinkedAccountsSearch,
        updateCompanyExpenses,
        isUpdating,
        setLinkedAccountsFilterState,
        linkedAccountsFilterState,
        hasNoAccounts,
    } = useCompanyExpenses(activeCompany?.id, {
        calls: {
            all: false,
            pagedLinkedAccounts: true,
        },
    });
    const containerRef = useRef<HTMLDivElement | null>(null);
    const bulkContainerRef = useRef<HTMLDivElement | null>(null);
    const isVisible = useElementVisibility(bulkContainerRef);
    const {width} = useElementSize(containerRef);
    const {config} = useAppConfig({config_key: "PLAID_ACCOUNT_SYNC_TIMEOUT_IN_HOUR"});
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const syncTimeoutValue = Number(config?.value || 0);
    const tableData =
        pagedLinkedAccounts.data?.pages.reduce((cur: ILinkedAccountData[], page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || [];
    const isBulkSyncing = isUpdating?.[MutateTypes.BulkSyncExpenseAccount];
    const isBulkDeleting = isUpdating?.[MutateTypes.BulkDeleteExpenseAccount];

    const handleToggleRow = (rowData: any) => {
        const selectedRow = selectedRows.includes(rowData.id);
        if (selectedRow) {
            setSelectedRows(selectedRows.filter(row => row !== rowData.id));
        } else {
            setSelectedRows([...selectedRows, rowData.id]);
        }
    };

    const handleSyncAccount = (account: ILinkedAccountData) => {
        if (!account?.id) return;
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.SyncExpenseAccount,
            id: account.id,
            mutate_is_updating_append: account.id,
        });
    };

    const handleBulkSyncAccounts = () => {
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.BulkSyncExpenseAccount,
            body: selectedRows,
            onSuccess: (response: AxiosResponse<any>) => {
                const sync = response.data.filter(item => item.is_syncing).map(item => item.display_name);
                const notSync = response.data.filter(item => !item.is_syncing).map(item => item.display_name);
                setSyncInfo({
                    open: true,
                    sync: sync,
                    notSync: notSync,
                });
                setSelectedRows([]);
            },
        });
    };

    const handleDeleteAccount = async (account: ILinkedAccountData) => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "error",
            title: "Delete Account?",
            form: (
                <Box>
                    <Typography>
                        Are you sure you want to delete <strong>{account.name}</strong>?
                    </Typography>
                </Box>
            ),
            okButtonText: "YES, DELETE",
        });
        if (!answer?.value) return;
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.DeleteExpenseAccount,
            id: account.id,
            mutate_is_updating_append: account.id,
        });
    };

    const handleBulkDeleteAccounts = () => {
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.BulkDeleteExpenseAccount,
            body: selectedRows,
            onSuccess: () => {
                setSelectedRows([]);
            },
        });
    };

    const handleSortChange = (sortType: CPSortType, order_by) => {
        setLinkedAccountsFilterState({
            ...linkedAccountsFilterState,
            sort: `sorting_${order_by}__${sortType}`,
        });
    };

    const handleNameChange = (id: string, newName: string) => {
        if (!newName) return;
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.UpdateExpenseAccountDisplayName,
            mutate_is_updating_append: id,
            body: {display_name: newName},
            id: id,
        });
    };

    const debouncedHandleNameChange = debounce(handleNameChange, 1000);

    const nameColumn = useCallback(
        ({row}) =>
            isUpdating?.[MutateTypes.UpdateExpenseAccountDisplayName + row.original.id] ? (
                <Skeleton height={40} width={"100%"} />
            ) : hasEditAccess ? (
                <TextField
                    defaultValue={row.original.display_name ? row.original.display_name : row.original.name || ""}
                    id={"display_name__" + row.original.id}
                    className="simpleTextField"
                    sx={{
                        input: {borderRadius: 1, borderBottom: "none !important", width: "100%"},
                        ".MuiInput-root::before": {display: "none !important"},
                    }}
                    onChange={e => {
                        debouncedHandleNameChange(row.original.id, e.target.value);
                    }}
                    key={row.original.display_name}
                />
            ) : (
                <Typography variant="body4" fontInter>
                    {row.original.display_name || row.original.name}
                </Typography>
            ),
        [isUpdating, pagedLinkedAccounts?.data],
    );

    const checkboxHeader = useCallback(
        header => {
            const rowsLength = header?.rows?.length || 0;
            const isIndeterminate =
                Array.isArray(selectedRows) && selectedRows.length > 0 && selectedRows.length < rowsLength;

            return (
                <IndeterminateCheckbox
                    indeterminate={isIndeterminate}
                    checked={!!selectedRows?.length}
                    onChange={() => {
                        if (selectedRows.length > 0) {
                            setSelectedRows([]);
                        } else {
                            const newSelectedRows = (header?.rows || [])
                                .map(row => row.original?.id || null)
                                .filter(Boolean);
                            setSelectedRows(newSelectedRows);
                        }
                    }}
                />
            );
        },
        [selectedRows],
    );

    const checkboxColumn = useCallback(
        ({row}) => (
            <IndeterminateCheckbox
                indeterminate={false}
                checked={selectedRows?.includes(row.original?.id)}
                onChange={() => handleToggleRow(row.original)}
            />
        ),
        [selectedRows],
    );

    const avatarColumn = useCallback(({row}) => {
        const account = row.original;
        return <ExpenseAccountAvatar name={account.name} logoUrl={account.institution_logo} />;
    }, []);

    const actionsColumn = useCallback(
        ({row}) => {
            const {is_syncing, can_be_synced} = row.original;
            const last_sync = row.original.last_sync
                ? DateTime.fromFormat(row.original.last_sync, "yyyy-MM-dd HH:mm:ss", {
                      zone: "utc",
                  }).toISO() || ""
                : "";
            const isSyncing = is_syncing;
            const canBeSynced = !!row.original.can_be_synced;
            let nextSyncTime = "";
            if (!can_be_synced && last_sync && syncTimeoutValue) {
                const lastSyncDate = new Date(last_sync);
                const nextSyncDate = new Date(lastSyncDate.getTime() + syncTimeoutValue * 60 * 60 * 1000);
                nextSyncTime = formatDate(nextSyncDate.toISOString(), DATE_SHORT_MONTH_TIME);
            }
            const titleAndAltText = isSyncing
                ? "Syncing Account..."
                : canBeSynced
                ? "Sync Account"
                : "Next sync available: " + nextSyncTime;
            return (
                <Box display="flex" flexDirection="row" alignItems="center" justifyContent="flex-end" gap={2}>
                    <Box
                        sx={{cursor: isSyncing || !canBeSynced ? "default" : "pointer"}}
                        tooltip={{
                            title: titleAndAltText,
                        }}
                        title={titleAndAltText}
                        onClick={isSyncing || !canBeSynced ? undefined : () => handleSyncAccount(row.original)}>
                        {isUpdating?.[MutateTypes.SyncExpenseAccount + row.original.id] ? (
                            <Loader inline loading style={{margin: 0}} />
                        ) : (
                            <SyncOutlinedIcon
                                sx={{height: 24, width: 24}}
                                htmlColor={
                                    isSyncing || !canBeSynced ? theme.palette.neutral[500] : theme.palette.blue[600]
                                }
                            />
                        )}
                    </Box>
                    <Box
                        sx={{cursor: "pointer"}}
                        title="Delete Account"
                        tooltip={{title: "Delete Account"}}
                        onClick={() => handleDeleteAccount(row.original)}>
                        {isUpdating?.[MutateTypes.DeleteExpenseAccount + row.original.id] ? (
                            <Loader inline loading style={{margin: 0}} />
                        ) : (
                            <DeleteOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.error[600]} />
                        )}
                    </Box>
                </Box>
            );
        },
        [isUpdating, syncTimeoutValue],
    );

    const columns = [
        ...(hasEditAccess
            ? [
                  {
                      Header: checkboxHeader,
                      accessor: "action",
                      Cell: checkboxColumn,
                      width: 50,
                  },
              ]
            : []),
        {
            Header: "",
            accessor: "institution_logo",
            Cell: avatarColumn,
            width: 50,
        },
        {
            Header: "Account Name",
            accessor: "name",
            Cell: nameColumn,
            minWidth: 200,
        },
        {
            Header: "Last 4",
            accessor: "mask",
            sortable: true,
        },
        {
            Header: "Last Sync",
            accessor: "last_sync",
            sortable: true,
            Cell: ({row}) => {
                const isoString: string = row.original.last_sync
                    ? DateTime.fromFormat(row.original.last_sync, "yyyy-MM-dd HH:mm:ss", {
                          zone: "utc",
                      }).toISO() || ""
                    : "";
                return (
                    <Typography variant="14" color={theme.palette.neutral[800]} fontWeight={700}>
                        {row.original?.is_syncing ? "Syncing..." : formatDate(isoString, DATE_SHORT_MONTH_TIME)}
                    </Typography>
                );
            },
        },
        ...(hasEditAccess
            ? [
                  {
                      Header: "",
                      accessor: "actionsColumn",
                      Cell: actionsColumn,
                  },
              ]
            : []),
    ];

    const MemoizedTable = useMemo(
        () => (
            <TableV2Component
                id="expenses-linked-accounts-tab-table"
                customData={tableData || []}
                columns={columns as []}
                headerBackground={theme.palette.blue[100]}
                isLoading={pagedLinkedAccounts.isLoading}
                rowsAsBorderedBox
                onSortChange={handleSortChange}
                infiniteLoad={pagedLinkedAccounts?.data?.pages?.[0]?.meta}
            />
        ),
        [pagedLinkedAccounts.dataUpdatedAt, isUpdating, selectedRows],
    );

    return (
        <ErrorBoundary>
            <Box display="flex" flexDirection="column" gap={2} ref={containerRef}>
                {!hasNoAccounts && (
                    <PageInnerHeader
                        id="expenses-linked-accounts-tab-header"
                        searchState={[linkedAccountsSearch, setLinkedAccountsSearch]}
                        growSearch
                    />
                )}
                {hasNoAccounts ? (
                    <NoAccountsBanner />
                ) : (
                    <>
                        {MemoizedTable}
                        <Box ref={bulkContainerRef} />
                        {!!selectedRows?.length && (
                            <BorderedBox
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    padding: 1,
                                    alignItems: "center",
                                    gap: 1,
                                    alignSelf: "stretch",
                                    borderRadius: 2,
                                    bgcolor: theme.palette.neutral[200],
                                    boxShadow: 1,
                                    border: "none",
                                    marginTop: -1,
                                    position: isVisible ? "relative" : "fixed",
                                    bottom: isVisible ? 0 : 8,
                                    left: "auto",
                                    width: isVisible ? "auto" : width,
                                    zIndex: 2,
                                }}>
                                <Typography
                                    textAlign="center"
                                    width="100%"
                                    variant="14"
                                    fontWeight={700}
                                    color={theme.palette.blue[800]}
                                    paddingX={2}
                                    whiteSpace="nowrap">
                                    {selectedRows.length} {pluralizeString("item", selectedRows.length)} selected
                                </Typography>
                                <Box marginLeft="auto" display="flex" flexDirection="row" gap={1}>
                                    <Button
                                        id="sync-all"
                                        color="blue"
                                        variant="outlined"
                                        sx={{gap: 1}}
                                        onClick={handleBulkSyncAccounts}
                                        disabled={isBulkDeleting || isBulkSyncing}>
                                        {isBulkSyncing ? (
                                            <Loader version="iconWhite" inline loading />
                                        ) : (
                                            <SyncOutlinedIcon sx={{height: 24, width: 24}} />
                                        )}
                                        Sync
                                    </Button>
                                    <Button
                                        id="delete-all"
                                        color="error"
                                        sx={{gap: 1, color: theme.palette.error["main"]}}
                                        variant="text"
                                        onClick={handleBulkDeleteAccounts}
                                        disabled={isBulkDeleting || isBulkSyncing}>
                                        {isBulkDeleting ? (
                                            <Loader version="iconWhite" inline loading />
                                        ) : (
                                            <DeleteOutlinedIcon sx={{height: 24, width: 24}} />
                                        )}
                                        Delete
                                    </Button>
                                </Box>
                            </BorderedBox>
                        )}
                    </>
                )}
            </Box>
        </ErrorBoundary>
    );
};

export default CompanyExpensesLinkedAccountsTab;
