import useTheme from "@mui/material/styles/useTheme";
import Drawer from "../../../Atoms/Drawer/Drawer.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import CircleIcon from "@mui/icons-material/Circle";
import {
    IExpenseBreakdownItem,
    INotLinkedExpenseItem,
    ISuggestedExpenseItem,
} from "../../../../Interfaces/plaid.interface";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useCompanyExpenses} from "../../../../hooks/fetches/useCompanyExpenses";
import ArrowForwardOutlinedIcon from "@mui/icons-material/ArrowForwardOutlined";
import AlertBanner from "../../../Molecules/AlertBanner/alertBanner.component";
import {TableV2Component} from "../../../../components/Table/TableV2.component";
import {useCallback, useEffect, useMemo, useState} from "react";
import CompanyExpensesAutocomplete from "../../../Molecules/CompanyExpensesAutocomplete/CompanyExpensesAutocomplete.component";
import {FormProvider, useForm} from "react-hook-form";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import {useQuery} from "@tanstack/react-query";
import {DEFAULT_QUERY_CONFIGS} from "../../../../constants/commonStrings.constant";
import FormatNumber from "../../../../utils/formatNumber.util";
import {pluralizeString} from "../../../../utils/formatString.util";
import AvatarComponentV2 from "../../../Molecules/Avatar/avatarV2.component";
import {formatDate, MONTH_DAY_YEAR} from "../../../../utils/formatDate";
import ExpenseAccountAvatar from "../../../Molecules/ExpenseAccountAvatar/ExpenseAccountAvatar.component";
import DragHandleIcon from "@mui/icons-material/DragHandle";
import {useDrag, useDrop, DndProvider, useDragLayer} from "react-dnd";
import {HTML5Backend, getEmptyImage} from "react-dnd-html5-backend";
import Skeleton from "@mui/material/Skeleton";
import {plaidService} from "../../../../services/plaid.service";
import CustomPagination from "../../../Molecules/CustomPagination/CustomPagination.component";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import {IPaginationData} from "../../../../Interfaces/pagination.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {generateUID} from "../../../../utils/UID.util";
import Button from "../../../Atoms/Button/Button.Component";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import Badge from "../../../Atoms/Badge/badge.component";
import ViewEditContractDrawer from "../../MSPContracts/ViewEditContractDrawer/ViewEditContractDrawer.component";
import NaviStackProductDetailsModal from "../../NavistackProductDetailModal/NavistackProductDetailModal.component";
import {useNaviStack} from "../../../../hooks/fetches/useNaviStack";
import {IStackCategorization} from "../../../../Interfaces/myStack.interface";
import ChangePartnershipTypeModal from "../../ChangePartnerShipTypeModal/ChangePartnerShipTypeModal.component";
import {IContract} from "../../../../Interfaces/mspContracts.interface";
import {useQueryHelper} from "../../../../hooks/helpers/useQueryHelper";

interface IProps {
    open: boolean;
    onClose?: () => void;
    item?: IExpenseBreakdownItem;
    //? Overwrites for when IExpenseBreakdownItem is undefined
    contract?: IContract;
}

const ITEM_TYPE = "SUGGESTED_EXPENSE";

const CustomDragLayer = () => {
    const {item, isDragging, currentOffset} = useDragLayer(monitor => ({
        item: monitor.getItem(),
        isDragging: monitor.isDragging(),
        currentOffset: monitor.getSourceClientOffset(),
    }));

    if (!isDragging || !currentOffset) return null;

    return (
        <Box
            sx={{
                position: "fixed",
                pointerEvents: "none",
                left: currentOffset.x,
                top: currentOffset.y,
                transform: "translate(-50%, -50%)",
                background: "#fff",
                padding: "8px",
                borderRadius: "8px",
                boxShadow: 2,
                fontWeight: "bold",
                zIndex: 9999,
                display: "flex",
                gap: 1,
                alignItems: "center",
            }}>
            <ExpenseAccountAvatar name={item.account?.name} logoUrl={item?.logo_url} />
            <Typography>
                {item.vendor?.name || item.merchant_name ? `${(item.vendor?.name || item.merchant_name) + " - "}` : ""}
                {item?.description}
            </Typography>
        </Box>
    );
};

const DraggableCell = ({row}) => {
    const theme = useTheme();
    const [{isDragging}, drag, dragPreview] = useDrag(() => ({
        type: ITEM_TYPE,
        item: row,
        collect: monitor => ({
            isDragging: monitor.isDragging(),
        }),
    }));

    useEffect(() => {
        dragPreview(getEmptyImage());
    }, []);

    return (
        <Box
            ref={drag}
            sx={{
                cursor: "grab",
            }}>
            {isDragging ? (
                <Skeleton height={24} width={24} />
            ) : (
                <DragHandleIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
            )}
        </Box>
    );
};

const DroppableCell = ({row, onDropExpense, children}) => {
    const theme = useTheme();
    const [{isOver}, drop] = useDrop(() => ({
        accept: ITEM_TYPE,
        drop: item => onDropExpense(item),
        collect: monitor => ({
            isOver: monitor.isOver(),
        }),
    }));

    return (
        <Box
            ref={drop}
            sx={{
                border: isOver ? `2px solid ${theme.palette.blue[600]}` : "none",
                borderRadius: 1,
            }}>
            {children}
        </Box>
    );
};

const LinkExpenseDrawer = ({open, onClose, item, contract}: IProps) => {
    const [mappedExpenses, setMappedExpenses] = useState<
        Record<string, {expense: ISuggestedExpenseItem; notLinkedItem: INotLinkedExpenseItem}>
    >({});
    const [mappedSuggestedExpenseIds, setMappedSuggestedExpenseIds] = useState<Set<string>>(new Set());
    const [suggestedExpensesPage, setSuggestedExpensesPage] = useState(1);
    const [suggestedExpensesItemsPerPage, setSuggestedExpensesItemsPerPage] = useState(20);
    const [contractDetailsId, setContractDetailsId] = useState<string>("");
    const [productDetails, setProductDetails] = useState<INotLinkedExpenseItem | null>(null);
    const [showEditPartnership, setShowEditPartnership] = useState<{show: boolean; stack: IStackCategorization}>({
        show: false,
        stack: {} as IStackCategorization,
    });
    const [isSaving, setIsSaving] = useState(false);
    const theme = useTheme();
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {invalidateMatchedQueries} = useQueryHelper();
    const methods = useForm();
    const {updateCompanyExpenses} = useCompanyExpenses(activeCompany?.id, {
        calls: {
            all: false,
        },
    });
    const {naviStack} = useNaviStack(activeCompany?.id || "", {
        calls: {
            all: false,
            naviStack: true,
        },
    });
    const sub_category_id = item?.id || contract?.category_id || "";
    const parentCategoryName = item?.parent?.name || contract?.category?.parent?.name || "";
    const subCategoryName = item?.name || contract?.category?.name || "";
    const categoryColor = item?.parent?.color || item?.color || contract?.category?.parent?.color || "";

    const naviStackProductToShowDetailsFor = useMemo(
        () =>
            naviStack.data?.find(
                item =>
                    item.product_id === productDetails?.product?.id &&
                    item.product?.company_id === productDetails?.vendor?.id,
            ),
        [naviStack.data, productDetails?.product?.id, productDetails?.vendor?.id],
    );

    const notLinkedExpensesQuery = useQuery<INotLinkedExpenseItem[]>(
        ["not-linked-expenses", activeCompany?.id, sub_category_id],
        () => {
            if (contract?.id) {
                return Promise.resolve([
                    {
                        product: contract?.product,
                        contract: contract,
                        vendor: contract?.company,
                        row_id: generateUID(),
                    } as unknown as INotLinkedExpenseItem,
                ]);
            }
            return new Promise((resolve, reject) =>
                plaidService
                    .getStackNotLinkedExpenses(activeCompany?.id || "", sub_category_id)
                    .then(res => resolve(res.data?.map(item => ({...item, row_id: generateUID()}))))
                    .catch(err => reject(err)),
            );
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.id && (!!sub_category_id || !!contract?.id),
        },
    );

    const suggestedExpensesQuery = useQuery<IPaginationData<ISuggestedExpenseItem[]>>(
        [
            "suggested-expenses-category-detail",
            activeCompany?.id,
            sub_category_id,
            suggestedExpensesPage,
            suggestedExpensesItemsPerPage,
        ],
        () => {
            return new Promise((resolve, reject) =>
                plaidService
                    .getStackSuggestedExpenses(activeCompany?.id || "", {
                        page: 1,
                        items_per_page: suggestedExpensesItemsPerPage,
                        dynamic: {
                            ...(item?.id || contract?.category_id
                                ? {sub_category_id: item?.id || contract?.category_id}
                                : {}),
                        },
                    })
                    .then(res => resolve(res.data))
                    .catch(err => reject(err)),
            );
        },
        {
            ...DEFAULT_QUERY_CONFIGS(),
            enabled: !!activeCompany?.id,
        },
    );

    const suggestedExpensesMetadata = suggestedExpensesQuery.data?.meta;

    const handleDropExpense = (droppedExpense: ISuggestedExpenseItem, notLinkedItem: INotLinkedExpenseItem) => {
        setMappedExpenses(prev => ({
            ...prev,
            [notLinkedItem.row_id || ""]: {
                notLinkedItem,
                expense: droppedExpense,
            },
        }));
    };

    const DroppableExpenseColumn = useMemo(
        () =>
            ({row}) => {
                const mappedExpense: ISuggestedExpenseItem | undefined =
                    mappedExpenses?.[row.original.row_id || ""]?.expense;
                return (
                    <DroppableCell
                        row={row.original}
                        onDropExpense={expense => handleDropExpense(expense, row.original)}>
                        <CompanyExpensesAutocomplete
                            id={`expense-${row.original?.row_id}`}
                            companyId={activeCompany?.id}
                            useSimple
                            placeholder="Search or drop an expense"
                            sx={{
                                "& input::placeholder": {
                                    color: `${theme.palette.blue[600]} !important`,
                                    fontWeight: "700 !important",
                                },
                                width: "100%",
                            }}
                            defaultOptions={
                                mappedExpense
                                    ? [
                                          {
                                              id: mappedExpense.id,
                                              amount: mappedExpense.amount,
                                              description: mappedExpense.description || "",
                                              logo_url: mappedExpense.logo_url || "",
                                              merchant_name: mappedExpense.merchant_name || "",
                                              product: null,
                                              vendor: mappedExpense.vendor || {
                                                  name: "",
                                                  avatar: mappedExpense.account?.institution_logo,
                                              },
                                          } as any,
                                      ]
                                    : undefined
                            }
                            defaultValue={mappedExpense?.id}
                            onValueChange={(_, __, newOption) => {
                                if (!newOption) {
                                    setMappedExpenses({...mappedExpenses, [row.original.row_id]: null});
                                    return;
                                }
                                handleDropExpense(newOption?.data, row.original);
                            }}
                            minDropdownWidth={160}
                        />
                    </DroppableCell>
                );
            },
        [mappedExpenses, mappedSuggestedExpenseIds],
    );

    const notLinkedColumns = useMemo(
        () => [
            {
                Header: "Product",
                accessor: "product.name",
                sortable: true,
                Cell: ({row}) => {
                    return (
                        <Box display="flex" alignItems="center" gap={2}>
                            <AvatarComponentV2
                                size={32}
                                isCompany
                                user={row.original.vendor}
                                profileTypeMinimized
                                showProfileType
                            />
                            <Typography variant="14" fontWeight={700} color={theme.palette.blue[600]}>
                                {row.original.contract?.name || row.original.product?.name}
                                {row.original.vendor?.name ? ` - ${row.original.vendor?.name}` : ""}
                            </Typography>
                        </Box>
                    );
                },
            },
            {
                Header: "",
                accessor: "type",
                Cell: ({row}) => (
                    <Badge color="blue">{!!row.original.contract?.id ? "Contract" : "Stack Product"}</Badge>
                ),
                width: 100,
            },
            {
                Header: "Expense",
                accessor: "expense",
                sortable: true,
                Cell: DroppableExpenseColumn,
                minWidth: 250,
            },
            {
                Header: "",
                accessor: "viewDetails",
                Cell: ({row}) => (
                    <Box
                        sx={{cursor: "pointer"}}
                        onClick={() => {
                            if (row.original.contract?.id) {
                                setContractDetailsId(row.original.contract.id);
                            } else if (row.original.product?.id) {
                                setProductDetails(row.original);
                            }
                        }}>
                        <ArrowForwardIosOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
                    </Box>
                ),
                width: 50,
            },
        ],
        [mappedExpenses],
    );

    const DraggableSuggestedExpenseCell = useCallback(
        ({row}) => (mappedSuggestedExpenseIds.has(row.original.id) ? null : <DraggableCell row={row.original} />),
        [mappedSuggestedExpenseIds],
    );

    const suggestedExpensesColumns = useMemo(
        () => [
            {
                Header: "",
                accessor: "action",
                Cell: DraggableSuggestedExpenseCell,
            },
            {
                Header: "Date",
                accessor: "date",
                sortable: true,
                Cell: ({row}) => <Typography variant="14">{formatDate(row.original.date, MONTH_DAY_YEAR)}</Typography>,
            },
            {
                Header: "",
                accessor: "vendor.name",
                Cell: ({row}) =>
                    !!row.original.vendor ? (
                        <AvatarComponentV2
                            size={32}
                            isCompany
                            showProfileType
                            profileTypeMinimized
                            user={row.original.vendor}
                        />
                    ) : null,
            },
            {
                Header: "Description",
                accessor: "description",
                sortable: true,
                Cell: ({row}) => (
                    <Box display="flex" alignItems="center">
                        <Typography variant="14">{row.original.description}</Typography>
                        {row.original.type !== "transaction" && <Badge color="blue">Recurring</Badge>}
                    </Box>
                ),
            },
            {
                Header: "Account",
                accessor: "account.name",
                sortable: true,
                Cell: ({row}) => (
                    <Box display="flex" alignItems="center" gap="10px">
                        <ExpenseAccountAvatar
                            name={row.original.account?.name}
                            logoUrl={row.original.account?.institution_logo}
                        />
                        <Typography
                            variant="14"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            whiteSpace="nowrap"
                            display="block"
                            width={118}
                            title={`${row.original.account?.name} - ${row.original.account?.mask}`}>
                            {row.original.account?.name} - {row.original.account?.mask}
                        </Typography>
                    </Box>
                ),
            },
            {
                Header: "Amount",
                accessor: "amount",
                sortable: true,
                Cell: ({row}) => (
                    <Typography variant="14" fontWeight={700} color={theme.palette.neutral[800]}>
                        -{FormatNumber.currency(row.original.amount || 0)}
                    </Typography>
                ),
            },
        ],
        [mappedSuggestedExpenseIds],
    );

    const handleSubmit = async () => {
        if (!Object.keys(mappedExpenses)?.length) return;

        let subscriptionsToUpdate: Array<{id: string; contract_id: string; product_id: string}> = [];
        let transactionsToUpdate: Array<{id: string; contract_id: string; product_id: string}> = [];

        Object.values(mappedExpenses).forEach(({expense, notLinkedItem}) => {
            const updateObj = {
                id: expense.id,
                contract_id: notLinkedItem?.contract?.id,
                product_id: notLinkedItem?.product?.id,
            };

            if (expense.type === "transaction") {
                transactionsToUpdate.push(updateObj);
            } else {
                subscriptionsToUpdate.push(updateObj);
            }
        });

        if (!transactionsToUpdate.length && !subscriptionsToUpdate.length) return;
        setIsSaving(true);

        try {
            await Promise.all([
                ...transactionsToUpdate.map(body =>
                    updateCompanyExpenses.mutateAsync({
                        mutate_type: MutateTypes.UpdateTransaction,
                        mutate_is_updating_append: body.id,
                        body,
                    }),
                ),
                ...subscriptionsToUpdate.map(body =>
                    updateCompanyExpenses.mutateAsync({
                        mutate_type: MutateTypes.UpdateSubscription,
                        mutate_is_updating_append: body.id,
                        body,
                    }),
                ),
            ]);

            setMappedExpenses({});
            setIsSaving(false);
            invalidateMatchedQueries(["not-linked-expenses", activeCompany?.id, sub_category_id]);
            invalidateMatchedQueries(["suggested-expenses-category-detail", activeCompany?.id, sub_category_id]);
            invalidateMatchedQueries(["company-expenses-all-expenses-breakdown", activeCompany?.id]);
            invalidateMatchedQueries(["upcomingSummary", activeCompany?.id]);
            onClose?.();
        } catch (error) {
            console.error("Error updating expenses:", error);
        }
    };

    useEffect(() => {
        if (suggestedExpensesQuery.isStale) {
            suggestedExpensesQuery.refetch();
        }
    }, [suggestedExpensesQuery.isStale]);

    useEffect(() => {
        const newIds = new Set(Object.values(mappedExpenses || {}).map(item => item?.expense?.id));
        setMappedSuggestedExpenseIds(newIds);
    }, [mappedExpenses]);

    return (
        <ErrorBoundary>
            <DndProvider backend={HTML5Backend}>
                <CustomDragLayer />
                <Drawer
                    open={open}
                    onClose={onClose}
                    anchor="right"
                    sx={{
                        ".MuiDrawer-paper": {
                            maxWidth: {xs: "100vw", lg: "50vw"},
                            width: {xs: "100vw", lg: "50vw"},
                            padding: 2,
                            overflowX: "hidden",
                        },
                    }}>
                    <Box
                        display="flex"
                        flexDirection="column"
                        gap={2}
                        paddingBottom={2}
                        borderBottom={`1px solid ${theme.palette.neutral[400]}`}>
                        <Box display="flex" alignItems="center" gap={2}>
                            <CircleIcon sx={{height: 16, width: 16}} htmlColor={categoryColor || "#000"} />
                            <Typography variant="16" fontInter fontWeight={600} display="flex" alignItems="center">
                                {parentCategoryName && subCategoryName ? (
                                    <>
                                        {parentCategoryName}
                                        <ArrowForwardOutlinedIcon sx={{height: 16, width: 16, marginX: 0.5}} />
                                        {subCategoryName}
                                    </>
                                ) : (
                                    "Uncategorized"
                                )}
                            </Typography>
                        </Box>
                        <Typography variant="16" fontWeight={500} color={theme.palette.neutral[600]}>
                            Link Expenses
                        </Typography>
                    </Box>
                    <Box marginY={3}>
                        <AlertBanner
                            id="link-expense-banner"
                            variant="info"
                            subTitle={{
                                text: `You have ${FormatNumber.words(
                                    notLinkedExpensesQuery.data?.length || 0,
                                )} ${pluralizeString(
                                    "product",
                                    notLinkedExpensesQuery.data?.length || 0,
                                )}/${pluralizeString(
                                    "contract",
                                    notLinkedExpensesQuery.data?.length || 0,
                                )} on your stack not linked to expenses`,
                            }}
                            contentContainerSx={{gap: 0}}
                        />
                    </Box>

                    <FormProvider {...methods}>
                        <Box width="100%" minHeight={200} sx={{overflowY: "scroll"}}>
                            <TableV2Component
                                id="link-expenses-mapping-table"
                                customData={notLinkedExpensesQuery.data || []}
                                columns={notLinkedColumns as []}
                                rowsAsBorderedBox
                                headerBackground={theme.palette.blue[100]}
                                isLoading={notLinkedExpensesQuery.isLoading}
                            />
                        </Box>
                    </FormProvider>
                    <Typography variant="18" fontWeight={500} color={theme.palette.neutral[800]} marginY={2}>
                        Suggested Expenses
                    </Typography>
                    <Box display="flex" flexDirection="column" maxHeight={300} marginBottom={2}>
                        <TableV2Component
                            id="link-expenses-suggested-expenses-table"
                            customData={suggestedExpensesQuery.data?.data || []}
                            columns={suggestedExpensesColumns as []}
                            headerBackground={theme.palette.blue[100]}
                            isLoading={suggestedExpensesQuery.isLoading}
                            conditionTdStyle={[{opacity: "0.5"}, row => mappedSuggestedExpenseIds.has(row.original.id)]}
                        />
                    </Box>
                    {suggestedExpensesMetadata && (
                        <Box marginBottom={3}>
                            <CustomPagination
                                metadata={suggestedExpensesMetadata}
                                itemsPerPage={suggestedExpensesItemsPerPage}
                                setItemsPerPage={setSuggestedExpensesItemsPerPage}
                                page={suggestedExpensesPage}
                                setPage={setSuggestedExpensesPage}
                            />
                        </Box>
                    )}

                    <Box
                        marginTop="auto"
                        display="flex"
                        flexDirection="row"
                        gap={1}
                        paddingTop={2}
                        borderTop={`1px solid ${theme.palette.neutral[400]}`}>
                        <Button
                            id="add-to-stack-btn"
                            variant="contained"
                            color="blue"
                            sx={{display: "flex", gap: 1}}
                            loading={isSaving}
                            onClick={handleSubmit}>
                            <CheckOutlinedIcon sx={{height: 18, width: 18}} /> SAVE
                        </Button>
                        <Button id="close-btn" variant="text" color="blue" sx={{gap: 0.5}} onClick={() => onClose?.()}>
                            CLOSE
                        </Button>
                    </Box>
                </Drawer>
            </DndProvider>
            {contractDetailsId && (
                <ViewEditContractDrawer
                    open={!!contractDetailsId}
                    onClose={() => setContractDetailsId("")}
                    contract_id={contractDetailsId}
                    companyId={activeCompany?.id}
                    mode={"READ"}
                    isClientMsp={isClientMsp}
                />
            )}
            {!!naviStackProductToShowDetailsFor && (
                <NaviStackProductDetailsModal
                    open
                    stack={naviStackProductToShowDetailsFor}
                    handleEditPartnerShipType={stack => {
                        setShowEditPartnership({show: true, stack});
                    }}
                    onClose={() => setProductDetails(null)}
                    companyFriendlyUrl={naviStackProductToShowDetailsFor.stack_company?.friendly_url}
                    onClickRemoveFromStack={() => {
                        setProductDetails(null);
                    }}
                />
            )}
            {showEditPartnership.show && showEditPartnership.stack && activeCompany?.id && (
                <ChangePartnershipTypeModal
                    stack={showEditPartnership.stack}
                    open={showEditPartnership.show}
                    onClose={() => setShowEditPartnership({show: false, stack: {} as IStackCategorization})}
                    companyId={activeCompany?.id}
                    onSave={naviStack.refetch}
                    isBrandStack={false}
                />
            )}
        </ErrorBoundary>
    );
};

export default LinkExpenseDrawer;
