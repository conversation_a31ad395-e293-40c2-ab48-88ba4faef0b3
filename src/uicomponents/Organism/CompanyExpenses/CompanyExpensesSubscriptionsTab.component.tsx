import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import BlockIcon from "@mui/icons-material/Block";
import BlockOutlinedIcon from "@mui/icons-material/BlockOutlined";
import CircleIcon from "@mui/icons-material/Circle";
import DoDisturbAltOutlinedIcon from "@mui/icons-material/DoDisturbAltOutlined";
import DoneAllOutlinedIcon from "@mui/icons-material/DoneAllOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {atom, useAtom} from "jotai";
import {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IndeterminateCheckbox, TableV2Component} from "../../../components/Table/TableV2.component";
import {CPSortType} from "../../../enums/sortType.enum";
import {useCategories} from "../../../hooks/fetches/useCategories";
import {useCompanyExpenses} from "../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useElementSize from "../../../hooks/useElementSize";
import {useElementVisibility} from "../../../hooks/useElementVisibility";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IMeta} from "../../../Interfaces/pagination.interface";
import {IExpenseSubscription} from "../../../Interfaces/plaid.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import FormatNumber from "../../../utils/formatNumber.util";
import formatString, {CAPITALIZE_FIRST, pluralizeString} from "../../../utils/formatString.util";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import {ExpenseDetailDrawerAtom} from "../../ModalManager/ExpensesModals/expenses.atoms";
import CategoryAutocomplete from "../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import PlaidCategoryAutocomplete from "../../Molecules/CategoryAutocomplete/plaidCategoryAutocomplete.component";
import ClientProductInput, {IClientProduct} from "../../Molecules/ClientProductInput/clientProductInput.component";
import ContractAutocomplete from "../../Molecules/ContractAutocomplete/ContractAutocomplete.component";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import ProductSearch from "../../Molecules/ProductSearch/productSearch.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import AddExpenseToStackDialog from "./AddExpenseToStackDialog/AddExpenseToStackDialog.component";
import CompanyExpensesKPICard from "./kpiCards/CompanyExpenseKPICard.component";
import NoAccountsBanner from "./noAccountsBanner.component";
import {DATE_FORMAT_Year_Month_Day, formatDate} from "../../../utils/formatDate";
import IgnoreButton from "../../Atoms/CompanyExpense/IgnoreButton.component";

const fieldPrepends = {
    product: "product_",
    contract: "contract_",
    category: "category_",
    plaid_category: "plaid_category__",
};

const updatingRowsAtom = atom(new Set<string>());
const selectedRowsAtom = atom<string[]>([]);

const MemoizedProductSearch = memo(ProductSearch);
// const MemoizedCategoryAutocomplete = memo(CategoryAutocomplete);
const MemoizedClientProductSearch = memo(ClientProductInput);

const CompanyExpensesSubscriptionsTab = ({
    dates,
    hideSearchAndFilters,
    hiddenColumns,
    hideKpis,
    fullWidthSearch,
    hideFilterKeys,
    useInfinite,
    categoryId,
    subcategoryId,
    plaidCategoryId,
    stackOnly,
    company_id_overwrite,
    readOnly,
    isViewingAsClientParent,
}: {
    dates?: {
        start_date?: string;
        end_date?: string;
    };
    hideSearchAndFilters?: boolean;
    hiddenColumns?: string[];
    hideKpis?: boolean;
    fullWidthSearch?: boolean;
    hideFilterKeys?: string[];
    useInfinite?: boolean;
    categoryId?: string;
    subcategoryId?: string | null;
    plaidCategoryId?: string;
    stackOnly?: boolean;
    company_id_overwrite?: string;
    readOnly?: boolean;
    isViewingAsClientParent?: boolean;
}) => {
    const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);
    const [, setShowDetailsFor] = useAtom(ExpenseDetailDrawerAtom);
    const [showAddToStackFor, setShowAddToStackFor] = useState<IExpenseSubscription | null>(null);
    const theme = useTheme();
    const methods = useForm();
    const {activeCompany, isClientMsp} = useActiveCompany();
    const company_id = company_id_overwrite || activeCompany?.id;
    const {
        subscriptions,
        infiniteSubscriptions,
        subscriptionsFilters,
        updateCompanyExpenses,
        isUpdating: isUpdatingCompanyExpenses,
        subscriptionFilterState,
        setSubscriptionFilterState,
        subscriptionPage,
        subscriptionSearch,
        subscriptionItemsPerPage,
        setSubscriptionsPage,
        setSubscriptionsSearch,
        setSubscriptionsItemsPerPage,
        hasNoAccounts,
    } = useCompanyExpenses(company_id, {
        calls: {
            all: false,
            subscriptions: !useInfinite,
            infiniteSubscriptions: !!useInfinite,
            subscriptionsFilters: true,
        },
        dates,
        categoryId: categoryId,
        subCategoryId: subcategoryId,
        plaidCategoryId,
        stack_only: stackOnly,
    });
    const {categories, plaidCategories} = useCategories(activeCompany?.company_type, {plaidCategories: true});
    const isLoadingCompanyCategories = categories.isLoading || plaidCategories.isLoading;
    const [_, setUpdatingRows] = useAtom(updatingRowsAtom);
    const {settings, getAllCategories} = useSettings();
    const bulkContainerRef = useRef<HTMLDivElement | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const hasLoadedOnce = useRef<boolean>(false);
    const {width} = useElementSize(containerRef, {ignoreInnerHtmlChange: true});
    const isVisible = useElementVisibility(selectedRows.length ? bulkContainerRef : null);
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const isReadOnly = readOnly || !hasEditAccess;
    const isBulkUpdating = isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateSubscription + "update"];
    const isBulkIgnoring = isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateSubscription + "ignore"];
    const meta = useInfinite ? infiniteSubscriptions?.data?.pages?.[0]?.meta : subscriptions?.data?.meta || {};
    const tableData = useInfinite
        ? infiniteSubscriptions.data?.pages.reduce((cur: IExpenseSubscription[], page) => {
              return [...cur, ...(page?.data || [])];
          }, []) || []
        : subscriptions?.data?.data || [];
    const isLoading = useInfinite
        ? infiniteSubscriptions?.isLoading || !settings.all_categories?.length || isLoadingCompanyCategories
        : subscriptions.isLoading || !settings.all_categories?.length || isLoadingCompanyCategories;
    const subscriptionsQuery = useInfinite ? infiniteSubscriptions : subscriptions;
    const isSettingDefaults = useRef<boolean>(false);
    const isEditable = !!hasEditAccess;

    const handleToggleRow = useCallback((rowData: any) => {
        setSelectedRows(prevRows =>
            prevRows.includes(rowData.id) ? prevRows.filter(row => row !== rowData.id) : [...prevRows, rowData.id],
        );
    }, []);

    const filledAutocompleteStyles = useMemo(
        () => ({
            width: "100%",
            color: theme.palette.blue[600],
            ".MuiInputBase-input": {
                background: "#fff !important",
                color: `${theme.palette.blue[600]} !important`,
            },
            "& .MuiInputBase-input::placeholder": {
                color: theme.palette.blue[600],
            },
        }),
        [],
    );

    const handleSortChange = (sortType: CPSortType, order_by) => {
        if (!!selectedRows.length) setSelectedRows([]);
        setSubscriptionFilterState({
            ...subscriptionFilterState,
            sort: `sorting_${order_by}__${sortType}`,
        });
    };

    const handleExpenseChange = useCallback(
        (
            expenseId: string,
            fieldName: "contract_id" | "product_id" | "category_id" | "plaid_category_id",
            id: string | null,
        ) => {
            setUpdatingRows(prev => {
                const newSet = new Set(prev);
                newSet.add(expenseId);
                return newSet;
            });
            updateCompanyExpenses.mutate({
                mutate_type: MutateTypes.UpdateSubscription,
                mutate_is_updating_append: expenseId,
                body: {
                    id: expenseId,
                    [fieldName]: id || null,
                    ...(fieldName === "category_id" ? {sub_category_id: null} : {}),
                },
                onSuccess: () => {
                    setUpdatingRows(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(expenseId);
                        return newSet;
                    });
                },
            });
        },
        [updateCompanyExpenses, setUpdatingRows],
    );

    const handleBulkSubmit = () => {
        const {bulk_product_id, bulk_category_id, bulk_contract_id} = methods.getValues();
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.BulkUpdateSubscription,
            mutate_is_updating_append: "update",
            body: {
                ids: selectedRows,
                ...(bulk_product_id && {product_id: bulk_product_id}),
                ...(bulk_category_id && {category_id: bulk_category_id, sub_category_id: null}),
                ...(bulk_contract_id && {contract_id: bulk_contract_id}),
            },
            onSuccess: () => {
                setSelectedRows([]);
            },
        });
    };

    const handleBulkIgnoreSubmit = async () => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "blue",
            title: `Ignore ${pluralizeString("Subscription", selectedRows.length)}?`,
            form: (
                <Box>
                    <Typography>
                        By ignoring {selectedRows.length > 1 ? `these subscriptions, they` : "this subscription, it"}{" "}
                        will be removed from your reports. You can revert this later.
                    </Typography>
                </Box>
            ),
            okButtonIcon: <BlockIcon />,
            okButtonText: "IGNORE",
        });
        if (!answer?.value) return;
        updateCompanyExpenses.mutate({
            mutate_type: MutateTypes.BulkUpdateSubscription,
            mutate_is_updating_append: "ignore",
            body: {
                ids: selectedRows,
                ignore: true,
            },
            onSuccess: () => {
                setSelectedRows([]);
            },
        });
    };

    const checkboxHeader = useCallback(
        header => {
            const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);
            const rowsLength = header?.rows?.length || 0;
            const isIndeterminate =
                Array.isArray(selectedRows) && selectedRows.length > 0 && selectedRows.length < rowsLength;

            return (
                <IndeterminateCheckbox
                    indeterminate={isIndeterminate}
                    checked={!!selectedRows?.length}
                    onChange={() => {
                        if (selectedRows.length > 0) {
                            setSelectedRows([]);
                        } else {
                            const newSelectedRows = (header?.rows || [])
                                .map(row => row.original?.id || null)
                                .filter(Boolean);
                            setSelectedRows(newSelectedRows);
                        }
                    }}
                />
            );
        },
        [selectedRows],
    );

    const checkboxColumn = useCallback(
        ({row}) => {
            const [selectedRows] = useAtom(selectedRowsAtom);
            const isSelected = selectedRows.includes(row.original.id);
            return (
                <IndeterminateCheckbox
                    indeterminate={false}
                    checked={isSelected}
                    onChange={() => handleToggleRow(row.original)}
                    sx={{svg: {height: "24px !important", width: "24px !important"}}}
                />
            );
        },
        [selectedRows, handleToggleRow],
    );

    /* const avatarColumn = useCallback(
        ({row}) => (
            <AvatarComponentV2
                size={32}
                isCompany
                user={{
                    ...row.original.vendor,
                    type: "vendor",
                }}
                profileTypeMinimized
                showProfileType={!!row.original.vendor}
                disableLink
            />
        ),
        [],
    ); */

    const productColumn = useMemo(
        () =>
            ({row}) => {
                const [updatingRows] = useAtom(updatingRowsAtom);
                const isUpdating = updatingRows.has(row.original.id);
                return isUpdating ? (
                    <Skeleton width={166} height={40} />
                ) : isReadOnly || !isEditable ? (
                    <Box sx={{overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", maxWidth: "100%"}}>
                        <Typography variant="body4" fontInter display="flex" alignItems="center" gap={1}>
                            {row.original.product?.name || "Not Selected"}
                        </Typography>
                    </Box>
                ) : isClientMsp ? (
                    <MemoizedClientProductSearch
                        id={fieldPrepends.product + row.original.id}
                        validateOnTheFly
                        showDropdownIcon
                        minDropdownWidth={160}
                        useSimple
                        useIdForOptions
                        handleChange={newOption => {
                            handleExpenseChange(
                                row.original.id,
                                "product_id",
                                (newOption as IClientProduct)?.id || null,
                            );
                        }}
                        helperText=""
                        disableClearable
                        sx={row.original.product ? filledAutocompleteStyles : {width: "100%"}}
                        defaultOptions={row.original.product ? [row.original.product] : undefined}
                        filterSelectedOptions={false}
                        includeInputInList={false}
                        freeSolo={false}
                        autoComplete={false}
                        noOptionsText="No products found"
                    />
                ) : (
                    <MemoizedProductSearch
                        id={fieldPrepends.product + row.original.id}
                        useSimple
                        sx={!!row.original.product ? filledAutocompleteStyles : {width: "100%"}}
                        defaultOptions={!!row.original.product ? [row.original.product] : undefined}
                        hideSearchPrepend
                        clearOnSelect={false}
                        onValueChange={(_, __, newOption) => {
                            handleExpenseChange(row.original.id, "product_id", newOption?.id || null);
                        }}
                        showStackAndContractProductsFor={activeCompany?.id}
                        placeholder="Not Selected"
                        disableClearable
                        minDropdownWidth={380}
                        multiple={false}
                    />
                );
            },
        [activeCompany?.id, handleExpenseChange],
    );

    const PlaidCategoryCell = useMemo(
        () =>
            ({row, filledAutocompleteStyles, handleExpenseChange}: any) => {
                const [updatingRows] = useAtom(updatingRowsAtom);
                const isUpdating = updatingRows.has(row.original.id);

                return isUpdating ? (
                    <Skeleton width={166} height={40} />
                ) : !isEditable || readOnly ? (
                    <Typography
                        variant="body4"
                        fontInter
                        color={theme.palette.neutral[800]}
                        display="flex"
                        alignItems="center"
                        gap={1}>
                        <CircleIcon sx={{height: 16, width: 16}} htmlColor={row.original.plaid_category?.color} />
                        {row.original.plaid_category?.name || "--"}
                    </Typography>
                ) : (
                    <PlaidCategoryAutocomplete
                        id={fieldPrepends.plaid_category + row.original.id}
                        sx={row.original.plaid_category?.id ? filledAutocompleteStyles : {width: "100%"}}
                        useSimple
                        validateOnTheFly
                        minDropdownWidth={160}
                        onValueChange={(_, __, newOption) => {
                            if (!newOption?.id) return;
                            handleExpenseChange(row.original.id, "plaid_category_id", newOption?.id || null);
                        }}
                        disableClearable
                        isRequired
                    />
                );
            },
        [filledAutocompleteStyles, handleExpenseChange, activeCompany?.id],
    );

    // ? Still needed for V2
    // const categoryColumn = useMemo(
    //     () =>
    //         ({row}) => {
    //             const [updatingRows] = useAtom(updatingRowsAtom);
    //             const isUpdating = updatingRows.has(row.original.id);
    //             return isUpdating ? (
    //                 <Skeleton width={166} height={40} />
    //             ) : isEditable && !readOnly ? (
    //                 <MemoizedCategoryAutocomplete
    //                     hideSubcategoryInput
    //                     useSimple
    //                     wrapperSx={{width: "100%"}}
    //                     mainCategorySx={!!row.original.category ? filledAutocompleteStyles : {width: "100%"}}
    //                     categoryInputId={fieldPrepends.category + row.original.id}
    //                     mainCategoryPlaceholder="Uncategorized"
    //                     onParentValueChange={(_, __, newOption) => {
    //                         handleExpenseChange(row.original.id, "category_id", newOption?.id || null);
    //                     }}
    //                     disableClearable
    //                     company_type={activeCompany?.company_type}
    //                 />
    //             ) : (
    //                 <Box sx={{overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", maxWidth: "100%"}}>
    //                     <Typography variant="body4" fontInter display="flex" alignItems="center" gap={1}>
    //                         {row.original.category?.color && (
    //                             <CircleIcon sx={{height: 16, width: 16}} htmlColor={row.original.category?.color} />
    //                         )}
    //                         {row.original.category?.name || "Not Selected"}
    //                     </Typography>
    //                 </Box>
    //             );
    //         },
    //     [activeCompany?.id, handleExpenseChange],
    // );

    // const contractColumn = useMemo(
    //     () =>
    //         ({row}) => {
    //             const [updatingRows] = useAtom(updatingRowsAtom);
    //             const isUpdating = updatingRows.has(row.original.id);
    //             return isUpdating ? (
    //                 <Skeleton width={166} height={40} />
    //             ) : (
    //                 <ContractAutocomplete
    //                     id={fieldPrepends.contract + row.original.id}
    //                     company_id={activeCompany?.id}
    //                     useSimple
    //                     sx={!!row.original.contract ? filledAutocompleteStyles : {width: "100%"}}
    //                     onValueChange={(_, __, newOption) => {
    //                         handleExpenseChange(row.original.id, "contract_id", newOption?.id || null);
    //                     }}
    //                     placeholder="Not Selected"
    //                     disableClearable
    //                 />
    //             );
    //         },
    //     [activeCompany?.id],
    // );

    const viewDetailsColumn = useCallback(
        ({row}) => (
            <Box
                onClick={() => {
                    setShowDetailsFor({
                        open: true,
                        data: {
                            expense: row.original,
                            type: "subscription",
                        },
                    });
                }}
                tooltip={{title: "View Subscription Details"}}>
                <ArrowForwardIosOutlinedIcon
                    sx={{cursor: "pointer", height: 24, width: 24}}
                    htmlColor={theme.palette.blue[600]}
                />
            </Box>
        ),
        [],
    );

    const columns = useMemo(
        () => [
            ...(isEditable
                ? [
                      {
                          Header: checkboxHeader,
                          accessor: "action",
                          Cell: checkboxColumn,
                          width: 50,
                      },
                  ]
                : []),
            {
                Header: "Last Billed",
                accessor: "last_date",
                Cell: ({row}) => (
                    <Typography variant="body4" fontInter>
                        {formatDate(row.original.last_date || "", DATE_FORMAT_Year_Month_Day)}
                    </Typography>
                ),
                minWidth: 90,
                sortable: true,
            },
            /* ...(activeCompany?.company_type !== CompanyType.MSP_CLIENT
                ? [
                      {
                          Header: "",
                          accessor: "vendor.name",
                          Cell: avatarColumn,
                          width: 50,
                      },
                  ]
                : []), */
            {
                Header: "Description",
                accessor: "description",
                minWidth: 140,
                maxWidth: 140,
                sortable: true,
                Cell: ({row}) => (
                    <Box maxWidth={140} overflow="hidden" title={row.original.description || ""}>
                        <Typography
                            variant="body4"
                            fontInter
                            width={140}
                            textOverflow="ellipsis"
                            whiteSpace="nowrap"
                            overflow="hidden"
                            display="block">
                            {row.original.description || ""}
                        </Typography>
                    </Box>
                ),
            },
            {
                Header: "Expense Category",
                accessor: "plaid_category",
                sortable: true,
                Cell: ({row}) => (
                    <PlaidCategoryCell
                        row={row}
                        filledAutocompleteStyles={filledAutocompleteStyles}
                        handleExpenseChange={handleExpenseChange}
                        activeCompanyId={activeCompany?.id}
                    />
                ),
                minWidth: 200,
            },
            {
                Header: "Stack Product",
                accessor: "product",
                sortable: true,
                Cell: productColumn,
                minWidth: 166,
            },
            // {
            //     Header: "Contract",
            //     accessor: "contract",
            //     sortable: true,
            //     Cell: contractColumn,
            //     minWidth: 166,
            // },
            // {
            //     Header: "Stack Category",
            //     accessor: "category",
            //     sortable: true,
            //     Cell: categoryColumn,
            //     minWidth: 166,
            // },
            {
                Header: "Frequency",
                accessor: "frequency",
                sortable: true,
                Cell: ({row}) => (
                    <Typography variant="body4" fontInter color={theme.palette.neutral[800]}>
                        {formatString(row.original.frequency, CAPITALIZE_FIRST)}
                    </Typography>
                ),
            },
            {
                Header: "Amount",
                accessor: "amount",
                sortable: true,
                Cell: ({row}) => (
                    <Box display="flex" flexDirection="column" gap={0.5}>
                        {!!(row.original as IExpenseSubscription)?.ignore && (
                            <Typography
                                variant="body5"
                                color={theme.palette.neutral[600]}
                                display="flex"
                                alignItems="center"
                                lineHeight={1}
                                gap={0.5}
                                fontWeight={700}>
                                <DoDisturbAltOutlinedIcon sx={{height: 16, width: 16}} />
                                IGNORED
                            </Typography>
                        )}
                        <Typography variant="body4" fontInter fontWeight={700} color={theme.palette.neutral[800]}>
                            -{FormatNumber.currency(row.original.amount || 0)}
                        </Typography>
                    </Box>
                ),
            },
            ...(isEditable && !isReadOnly
                ? [
                      {
                          Header: "",
                          accessor: "add-to-stack",
                          Cell: ({row}) => {
                              const isLinked = !!row.original.my_stack_id;
                              return (
                                  <Box
                                      sx={{cursor: isLinked ? "default" : "pointer"}}
                                      title={isLinked ? "Already added to stack" : "Add to Stack"}
                                      tooltip={{title: isLinked ? "Already added to stack" : "Add to Stack"}}
                                      onClick={
                                          isLinked
                                              ? undefined
                                              : () => setShowAddToStackFor(row.original as IExpenseSubscription)
                                      }>
                                      <AddCircleOutlineIcon
                                          sx={{height: 24, width: 24}}
                                          htmlColor={isLinked ? theme.palette.neutral[500] : theme.palette.blue[600]}
                                      />
                                  </Box>
                              );
                          },
                          width: 56,
                      },
                      {
                          Header: "",
                          accessor: "ignore",
                          Cell: ({row}) => (
                              <IgnoreButton
                                  sx={{fontSize: "12px !important", padding: "6px 14px !important"}}
                                  iconSx={{width: 14, height: 14}}
                                  expense={row.original}
                                  isTransaction={false}
                              />
                          ),
                      },
                  ]
                : []),
            ...(isViewingAsClientParent
                ? []
                : [
                      {
                          Header: "",
                          accessor: "viewDetails",
                          Cell: viewDetailsColumn,
                          width: 50,
                      },
                  ]),
        ],
        [company_id, isReadOnly],
    );

    const handleSetDefaultValues = async () => {
        isSettingDefaults.current = true;
        const defaultValues = {};
        tableData?.forEach(item => {
            defaultValues[fieldPrepends.product + item.id] = item?.product?.id || null;
            defaultValues[fieldPrepends.category + item.id] = item?.category?.id || null;
            defaultValues[fieldPrepends.contract + item.id] = item?.contract?.id || null;
            defaultValues[fieldPrepends.plaid_category + item.id] = item?.plaid_category?.id || null;
        });
        await new Promise(resolve => {
            methods.reset(defaultValues);
            setTimeout(resolve, 0);
        });
        isSettingDefaults.current = false;
    };

    const MemoizedTable = useMemo(
        () => (
            <TableV2Component
                id="expenses-subscriptions-tab-table"
                customData={tableData}
                columns={columns as []}
                headerBackground={theme.palette.blue[100]}
                isLoading={useInfinite ? infiniteSubscriptions.isLoading : subscriptions.isLoading}
                rowsAsBorderedBox
                onSortChange={handleSortChange}
                hiddenColumns={hiddenColumns || []}
                infiniteLoad={useInfinite ? meta : undefined}
                onInfinitePageChange={() => {
                    infiniteSubscriptions.fetchNextPage();
                }}
            />
        ),
        [subscriptionsQuery.dataUpdatedAt, columns, isLoading, meta],
    );

    useEffect(() => {
        if (!!selectedRows.length) setSelectedRows([]);
    }, [subscriptionPage, subscriptionFilterState]);

    useEffect(() => {
        if (!isLoading && company_id && !!tableData) {
            handleSetDefaultValues();
        }
    }, [subscriptionsQuery.dataUpdatedAt, isLoading]);

    useEffect(() => {
        if (dates?.start_date && dates?.end_date) {
            setSubscriptionFilterState({
                ...subscriptionFilterState,
                start_date: dates?.start_date,
                end_date: dates?.end_date,
            });
        }
    }, [dates]);

    useEffect(() => {
        if (!hasLoadedOnce.current) getAllCategories();
        hasLoadedOnce.current = true;
        return () => {
            setSelectedRows([]);
        };
    }, []);

    return (
        <ErrorBoundary>
            <FormProvider {...methods}>
                <Box display="flex" flexDirection="column" gap={2} ref={containerRef}>
                    {!hideKpis && !hasNoAccounts && (
                        <Box display="grid" gridTemplateColumns={{xs: "1fr", md: "1fr 1fr"}} width="100%" gap={2}>
                            <CompanyExpensesKPICard type="recurring-expenses-yearly" />
                            <CompanyExpensesKPICard type="recurring-expenses-monthly" />
                        </Box>
                    )}
                    {!hideSearchAndFilters && !hasNoAccounts && (
                        <PageInnerHeader
                            id="expenses-transactions-tab-header"
                            searchState={[subscriptionSearch, setSubscriptionsSearch]}
                            growSearch
                            fullWidthSearch={fullWidthSearch}
                            filters={subscriptionsFilters?.data}
                            filterState={[subscriptionFilterState, setSubscriptionFilterState]}
                            hideFilterKeys={hideFilterKeys}
                        />
                    )}
                    {hasNoAccounts ? (
                        <NoAccountsBanner />
                    ) : (
                        <>
                            {MemoizedTable}
                            <Box ref={bulkContainerRef} />
                            {!!selectedRows?.length && !isLoading && (
                                <BorderedBox
                                    sx={{
                                        display: "flex",
                                        flexDirection: "row",
                                        padding: 1,
                                        alignItems: "center",
                                        gap: 1,
                                        alignSelf: "stretch",
                                        borderRadius: 2,
                                        bgcolor: theme.palette.neutral[200],
                                        boxShadow: 1,
                                        border: "none",
                                        marginTop: -1,
                                        position: isVisible ? "relative" : "fixed",
                                        bottom: isVisible ? 0 : 8,
                                        left: "auto",
                                        width: isVisible ? "auto" : width,
                                        zIndex: 2,
                                    }}>
                                    <Typography
                                        textAlign="center"
                                        variant="14"
                                        fontWeight={700}
                                        color={theme.palette.blue[800]}
                                        paddingX={2}>
                                        {selectedRows.length} {pluralizeString("item", selectedRows.length)}
                                        <br /> selected
                                    </Typography>
                                    {isClientMsp ? (
                                        <MemoizedClientProductSearch
                                            id={"bulk_product_id"}
                                            validateOnTheFly
                                            showDropdownIcon
                                            minDropdownWidth={160}
                                            useIdForOptions
                                            helperText=""
                                            disableClearable
                                            multiple={false}
                                            filterSelectedOptions={false}
                                            includeInputInList={false}
                                            freeSolo={false}
                                            autoComplete={false}
                                            sx={{flexGrow: 1}}
                                            label="Product"
                                        />
                                    ) : (
                                        <ProductSearch
                                            id="bulk_product_id"
                                            sx={{flexGrow: 1}}
                                            hideSearchPrepend
                                            placeholder="Please select"
                                            textFieldVariant="filled"
                                            variant="filled"
                                            clearOnSelect={false}
                                        />
                                    )}
                                    <CategoryAutocomplete
                                        hideSubcategoryInput
                                        mainCategorySx={{width: "100%"}}
                                        wrapperSx={{flexGrow: 1}}
                                        mainCategoryPlaceholder="Please select"
                                        categoryInputId="bulk_category_id"
                                        company_type={activeCompany?.company_type}
                                    />
                                    <ContractAutocomplete
                                        id="bulk_contract_id"
                                        sx={{flexGrow: 1}}
                                        placeholder="Please select"
                                        textFieldVariant="filled"
                                        company_id={activeCompany?.id}
                                    />
                                    <Button
                                        id="apply"
                                        color="blue"
                                        variant="outlined"
                                        sx={{gap: 1}}
                                        disabled={isBulkIgnoring}
                                        loading={isBulkUpdating}
                                        onClick={handleBulkSubmit}>
                                        <DoneAllOutlinedIcon />
                                        Apply
                                    </Button>
                                    <Button
                                        id="ignore"
                                        color="blue"
                                        variant="text"
                                        sx={{gap: 1}}
                                        disabled={isBulkUpdating}
                                        loading={isBulkIgnoring}
                                        onClick={handleBulkIgnoreSubmit}>
                                        <BlockOutlinedIcon />
                                        Ignore
                                    </Button>
                                </BorderedBox>
                            )}
                            {!useInfinite && (
                                <CustomPagination
                                    metadata={meta as IMeta}
                                    page={subscriptionPage}
                                    setPage={setSubscriptionsPage}
                                    itemsPerPage={subscriptionItemsPerPage}
                                    setItemsPerPage={setSubscriptionsItemsPerPage}
                                    tableId="expenses-subscriptions-tab-table"
                                />
                            )}
                        </>
                    )}
                </Box>
            </FormProvider>
            {showAddToStackFor && activeCompany?.id && (
                <AddExpenseToStackDialog
                    open
                    onClose={() => {
                        setShowAddToStackFor(null);
                    }}
                    companyId={activeCompany.id}
                    defaultParentCategory={showAddToStackFor.category?.id}
                    defaultSubCategory={showAddToStackFor.sub_category_id || undefined}
                    defaultProduct={showAddToStackFor.product}
                    defaultVendor={showAddToStackFor.vendor}
                    expenseId={showAddToStackFor.id}
                    isTransaction={false}
                />
            )}
        </ErrorBoundary>
    );
};

export default CompanyExpensesSubscriptionsTab;
