import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import BlockIcon from "@mui/icons-material/Block";
import BlockOutlinedIcon from "@mui/icons-material/BlockOutlined";
import CircleIcon from "@mui/icons-material/Circle";
import DoDisturbAltOutlinedIcon from "@mui/icons-material/DoDisturbAltOutlined";
import DoneAllOutlinedIcon from "@mui/icons-material/DoneAllOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {atom, useAtom} from "jotai";
import {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {IndeterminateCheckbox, TableV2Component} from "../../../components/Table/TableV2.component";
import {CPSortType} from "../../../enums/sortType.enum";
import {useCategories} from "../../../hooks/fetches/useCategories";
import {useCompanyExpenses} from "../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useElementSize from "../../../hooks/useElementSize";
import {useElementVisibility} from "../../../hooks/useElementVisibility";
import useNotification from "../../../hooks/useNotification";
import usePermissions from "../../../hooks/usePermissions";
import useSettings from "../../../hooks/useSettings";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {IMeta} from "../../../Interfaces/pagination.interface";
import {ITransactionItem} from "../../../Interfaces/plaid.interface";
import {MutateTypes} from "../../../Interfaces/queries.interface";
import getUserConfirmation from "../../../utils/confirmationDialog.util";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import {DATE_FORMAT_Year_Month_Day, formatDate} from "../../../utils/formatDate";
import FormatNumber from "../../../utils/formatNumber.util";
import {pluralizeString} from "../../../utils/formatString.util";
import Badge from "../../Atoms/Badge/badge.component";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import Typography from "../../Atoms/Typography/Typography.component";
import {ExpenseDetailDrawerAtom} from "../../ModalManager/ExpensesModals/expenses.atoms";
import CategoryAutocomplete from "../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import PlaidCategoryAutocomplete from "../../Molecules/CategoryAutocomplete/plaidCategoryAutocomplete.component";
import ClientProductInput, {IClientProduct} from "../../Molecules/ClientProductInput/clientProductInput.component";
import ContractAutocomplete from "../../Molecules/ContractAutocomplete/ContractAutocomplete.component";
import CustomPagination from "../../Molecules/CustomPagination/CustomPagination.component";
import ExpenseAccountAvatar from "../../Molecules/ExpenseAccountAvatar/ExpenseAccountAvatar.component";
import ProductSearch from "../../Molecules/ProductSearch/productSearch.component";
import PageInnerHeader from "../PageInnerHeader/pageInnerHeader";
import AddExpenseToStackDialog from "./AddExpenseToStackDialog/AddExpenseToStackDialog.component";
import CompanyExpensesKPICard from "./kpiCards/CompanyExpenseKPICard.component";
import NoAccountsBanner from "./noAccountsBanner.component";
import IgnoreButton from "../../Atoms/CompanyExpense/IgnoreButton.component";

const fieldPrepends = {
    product: "product__",
    contract: "contract__",
    category: "category__",
    plaid_category: "plaid_category__",
};

const updatingRowsAtom = atom(new Set<string>());
const selectedRowsAtom = atom<Array<{id: string; plaid_subscription_id?: string}>>([]);

const MemoizedProductSearch = memo(ProductSearch);
//const MemoizedCategoryAutocomplete = memo(CategoryAutocomplete);
// const MemoizedContractAutocomplete = memo(ContractAutocomplete);
const MemoizedClientProductSearch = memo(ClientProductInput);

const CompanyExpensesTransactionsTab = ({
    dates,
    hideSearchAndFilters,
    hiddenColumns,
    useInfinite,
    hideFilterKeys,
    fullWidthSearch,
    categoryId,
    subcategoryId,
    plaidCategoryId,
    contractId,
    stackOnly,
    subscriptionId,
    company_id_overwrite,
    readOnly,
    customFilterElement,
    hideKpis,
    isViewingAsClientParent,
}: {
    dates?: {
        year?: string;
        start_date?: string;
        end_date?: string;
        date?: string;
    };
    hideSearchAndFilters?: boolean;
    hiddenColumns?: string[];
    useInfinite?: boolean;
    hideFilterKeys?: string[];
    fullWidthSearch?: boolean;
    //? Default filter props
    categoryId?: string;
    subcategoryId?: string | null;
    plaidCategoryId?: string;
    contractId?: string;
    stackOnly?: boolean;
    subscriptionId?: string;
    //? Overwrite company_id expenses is fetched from
    company_id_overwrite?: string;
    readOnly?: boolean;
    customFilterElement?: (props: {
        filterState: [any, any];
        filters: any;
        searchState: [any, any];
    }) => JSX.Element | null;
    hideKpis?: boolean;
    isViewingAsClientParent?: boolean;
}) => {
    const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);
    const [__, setExpenseDetailDrawerData] = useAtom(ExpenseDetailDrawerAtom);
    const [showAddToStackFor, setShowAddToStackFor] = useState<ITransactionItem | null>(null);
    const {activeCompany, isClientMsp} = useActiveCompany();
    const {settings, getAllCategories} = useSettings();
    const company_id = company_id_overwrite || activeCompany?.id;
    const {categories, plaidCategories} = useCategories(activeCompany?.company_type, {plaidCategories: true});
    const isLoadingCompanyCategories = categories.isLoading || plaidCategories.isLoading;
    const {
        transactions,
        infiniteTransactions,
        transactionsFilters,
        transactionsFilterState,
        setTransactionsFilterState,
        transactionsPage,
        transactionsItemsPerPage,
        setTransactionsPage,
        setTransactionsItemsPerPage,
        transactionsSearch,
        setTransactionsSearch,
        updateCompanyExpenses,
        isUpdating: isUpdatingCompanyExpenses,
        hasNoAccounts,
    } = useCompanyExpenses(company_id, {
        calls: {
            all: false,
            transactions: !useInfinite,
            infiniteTransactions: !!useInfinite,
            transactionsFilters: true,
        },
        dates,
        categoryId: categoryId,
        subCategoryId: subcategoryId,
        plaidCategoryId,
        contract_id: contractId,
        stack_only: stackOnly,
        subscriptionId,
    });
    const theme = useTheme();
    const methods = useForm();
    const transactionsQuery = useInfinite ? infiniteTransactions : transactions;
    const isLoading = transactionsQuery.isLoading || !settings.all_categories?.length || isLoadingCompanyCategories;
    const isSettingDefaults = useRef<boolean>(false);
    const hasLoadedOnce = useRef<boolean>(false);
    const data = useMemo(
        () =>
            useInfinite
                ? infiniteTransactions.data?.pages.flatMap(page => page?.data || []) || []
                : transactions?.data?.data || [],
        [useInfinite, transactions?.data, infiniteTransactions?.data?.pages],
    );
    const meta = useMemo(
        () => (useInfinite ? infiniteTransactions?.data?.pages?.[0]?.meta : transactions?.data?.meta || {}),
        [useInfinite, infiniteTransactions?.data, transactions?.data],
    );
    const isBulkUpdating =
        isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateTransaction + "update"] ||
        isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateSubscription + "update"];
    const isBulkIgnoring =
        isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateTransaction + "ignore"] ||
        isUpdatingCompanyExpenses?.[MutateTypes.BulkUpdateSubscription + "ignore"];
    const bulkContainerRef = useRef<HTMLDivElement | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const notify = useNotification();
    const {width} = useElementSize(containerRef, {ignoreInnerHtmlChange: true});
    const isVisible = useElementVisibility(selectedRows?.length ? bulkContainerRef : null);
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const isReadOnly = !hasEditAccess || readOnly;
    const isEditable = !!hasEditAccess;

    const handleToggleRow = useCallback((rowData: any) => {
        setSelectedRows(prevRows =>
            prevRows.find(row => row.id === rowData.id)
                ? prevRows.filter(row => row.id !== rowData.id)
                : [
                      ...prevRows,
                      {
                          id: rowData.id,
                          plaid_subscription_id: rowData.plaid_subscription_id,
                      },
                  ],
        );
    }, []);

    const filledAutocompleteStyles = useMemo(
        () => ({
            width: "100%",
            color: theme.palette.blue[600],
            ".MuiInputBase-input": {
                background: "#fff !important",
                color: `${theme.palette.blue[600]} !important`,
            },
            "& .MuiInputBase-input::placeholder": {
                color: theme.palette.blue[600],
            },
        }),
        [],
    );

    const handleBulkSubmit = async () => {
        const {bulk_product_id, bulk_category_id, bulk_contract_id} = methods.getValues();
        const transactionRowIds = selectedRows.filter(row => !row.plaid_subscription_id).map(row => row.id);
        const subscriptionRowIds = selectedRows
            .filter(row => !!row.plaid_subscription_id)
            .map(row => row.plaid_subscription_id);
        if (transactionRowIds.length > 0) {
            await updateCompanyExpenses.mutateAsync({
                mutate_type: MutateTypes.BulkUpdateTransaction,
                mutate_is_updating_append: "update",
                body: {
                    ids: transactionRowIds,
                    ...(!!bulk_product_id && {product_id: bulk_product_id}),
                    ...(!!bulk_category_id && {category_id: bulk_category_id, subcategory_id: null}),
                    ...(!!bulk_contract_id && {contract_id: bulk_contract_id}),
                },
                disableDefaultNotify: true,
            });
        }
        if (subscriptionRowIds.length > 0) {
            await updateCompanyExpenses.mutateAsync({
                mutate_type: MutateTypes.BulkUpdateSubscription,
                mutate_is_updating_append: "update",
                body: {
                    ids: subscriptionRowIds,
                    ...(!!bulk_product_id && {product_id: bulk_product_id}),
                    ...(!!bulk_category_id && {category_id: bulk_category_id, subcategory_id: null}),
                    ...(!!bulk_contract_id && {contract_id: bulk_contract_id}),
                },
                disableDefaultNotify: true,
            });
        }
        setSelectedRows([]);
        notify("Transactions updated successfully", "Success");
    };

    const handleBulkIgnoreSubmit = async () => {
        const answer = await getUserConfirmation("", {
            mui: true,
            modalTheme: "blue",
            title: `Ignore ${pluralizeString("Transaction", selectedRows.length)}?`,
            form: (
                <Box>
                    <Typography>
                        By ignoring {selectedRows.length > 1 ? `these transactions, they` : "this transaction, it"} will
                        be removed from your reports. You can revert this later.
                    </Typography>
                </Box>
            ),
            okButtonIcon: <BlockIcon />,
            okButtonText: "IGNORE",
        });
        if (!answer?.value) return;
        const transactionRowIds = selectedRows.filter(row => !row.plaid_subscription_id).map(row => row.id);
        const subscriptionRowIds = selectedRows
            .filter(row => !!row.plaid_subscription_id)
            .map(row => row.plaid_subscription_id);
        if (transactionRowIds.length > 0) {
            await updateCompanyExpenses.mutateAsync({
                mutate_type: MutateTypes.BulkUpdateTransaction,
                mutate_is_updating_append: "ignore",
                body: {
                    ids: transactionRowIds,
                    ignore: true,
                },
                disableDefaultNotify: true,
            });
        }
        if (subscriptionRowIds.length > 0) {
            await updateCompanyExpenses.mutate({
                mutate_type: MutateTypes.BulkUpdateSubscription,
                mutate_is_updating_append: "ignore",
                body: {
                    ids: subscriptionRowIds,
                    ignore: true,
                },
                disableDefaultNotify: true,
            });
        }
        setSelectedRows([]);
        notify("Transactions ignored successfully", "Success");
    };

    const checkboxHeader = useCallback(
        header => {
            const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);
            const rowsLength = header?.rows?.length || 0;
            const isIndeterminate =
                Array.isArray(selectedRows) && selectedRows.length > 0 && selectedRows.length < rowsLength;

            return (
                <IndeterminateCheckbox
                    indeterminate={isIndeterminate}
                    checked={!!selectedRows?.length}
                    onChange={() => {
                        if (selectedRows.length > 0) {
                            setSelectedRows([]);
                        } else {
                            const newSelectedRows = (header?.rows || [])
                                .map(row =>
                                    row.original?.id
                                        ? {
                                              id: row.original.id,
                                              plaid_subscription_id: row.original.plaid_subscription_id,
                                          }
                                        : null,
                                )
                                .filter(Boolean);
                            setSelectedRows(newSelectedRows);
                        }
                    }}
                />
            );
        },
        [selectedRows],
    );

    const checkboxColumn = useCallback(
        ({row}) => {
            const [selectedRows] = useAtom(selectedRowsAtom);
            const isSelected = selectedRows.find(r => r.id === row.original.id);
            return (
                <IndeterminateCheckbox
                    indeterminate={false}
                    checked={isSelected}
                    onChange={() => handleToggleRow(row.original)}
                    sx={{svg: {height: "24px !important", width: "24px !important"}}}
                />
            );
        },
        [selectedRows, handleToggleRow],
    );

    /* //? Commented as requested in CPK-3923 at March 20th 2025
    const avatarColumn = useCallback(
        ({row}) => (
            <AvatarComponentV2
                size={32}
                isCompany
                user={{
                    ...row.original.vendor,
                    type: "vendor",
                }}
                profileTypeMinimized
                showProfileType={!!row.original.vendor}
                disableLink
            />
        ),
        [],
    ); */

    const viewDetailsColumn = useCallback(
        ({row}) => (
            <Box
                onClick={() => {
                    setExpenseDetailDrawerData({
                        data: {
                            expense: row.original,
                            type: row.original.plaid_subscription_id ? "subscription" : "transaction",
                        },
                        company_id_overwrite: company_id,
                        readOnly: isReadOnly,
                        open: true,
                    });
                }}
                tooltip={{title: "View Transaction Details"}}>
                <ArrowForwardIosOutlinedIcon
                    sx={{cursor: "pointer", height: 24, width: 24}}
                    htmlColor={theme.palette.blue[600]}
                />
            </Box>
        ),
        [isReadOnly],
    );

    const handleSortChange = (sortType: CPSortType, order_by: string | number) => {
        if (!!selectedRows.length) setSelectedRows([]);
        setTransactionsFilterState({
            ...transactionsFilterState,
            sort: `sorting_${order_by}__${sortType}`,
        });
    };

    const handleSetDefaultValues = async () => {
        isSettingDefaults.current = true;
        const newDefaultValues = {};
        data?.forEach(item => {
            newDefaultValues[fieldPrepends.product + item.id] = item?.product?.id || null;
            newDefaultValues[fieldPrepends.category + item.id] = item?.category?.id || null;
            newDefaultValues[fieldPrepends.contract + item.id] = item?.contract?.id || null;
            newDefaultValues[fieldPrepends.plaid_category + item.id] = item?.plaid_category?.id || null;
        });
        await new Promise(resolve => {
            methods.reset(newDefaultValues);
            setTimeout(resolve, 0);
        });
        isSettingDefaults.current = false;
    };

    useEffect(() => {
        if (!isLoading && company_id && !!data && !isSettingDefaults.current) {
            handleSetDefaultValues();
        }
    }, [transactionsQuery.dataUpdatedAt, isLoading, company_id]);

    useEffect(() => {
        if (dates?.start_date && dates?.end_date) {
            setTransactionsFilterState({
                ...transactionsFilterState,
                date: {
                    start_date: dates?.start_date,
                    end_date: dates?.end_date,
                },
            });
        }
    }, [dates]);

    const [_, setUpdatingRows] = useAtom(updatingRowsAtom);

    const handleExpenseChange = useCallback(
        (
            expenseId: string,
            fieldName: "contract_id" | "product_id" | "category_id" | "plaid_category_id",
            id: string | null,
            isSubscription: boolean = false,
        ) => {
            setUpdatingRows(prev => {
                const newSet = new Set(prev);
                newSet.add(expenseId);
                return newSet;
            });

            updateCompanyExpenses.mutate({
                mutate_type: isSubscription ? MutateTypes.UpdateSubscription : MutateTypes.UpdateTransaction,
                mutate_is_updating_append: expenseId,
                body: {
                    id: expenseId,
                    [fieldName]: id || null,
                    ...(fieldName === "category_id" ? {sub_category_id: null} : {}),
                },
                onSuccess: () => {
                    setUpdatingRows(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(expenseId);
                        return newSet;
                    });
                },
                onError: () => {
                    setUpdatingRows(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(expenseId);
                        return newSet;
                    });
                },
            });
        },
        [updateCompanyExpenses, setUpdatingRows],
    );

    const ProductCell = useMemo(
        () =>
            ({row, filledAutocompleteStyles, handleExpenseChange}: any) => {
                const [updatingRows] = useAtom(updatingRowsAtom);
                const isUpdating =
                    updatingRows.has(row.original.id) || updatingRows.has(row.original.plaid_subscription_id);
                const isRecurring = !!row.original.plaid_subscription_id;
                const showAsReadOnly = isReadOnly;
                const updateId = isRecurring ? row.original.plaid_subscription_id : row.original.id;

                return isUpdating ? (
                    <Skeleton width={166} height={40} />
                ) : showAsReadOnly ? (
                    <Box sx={{overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", maxWidth: "100%"}}>
                        <Typography variant="body4" fontInter display="flex" alignItems="center" gap={1}>
                            {!!row.original.product?.name ? row.original.product.name : "Not Selected"}
                        </Typography>
                    </Box>
                ) : isClientMsp ? (
                    <MemoizedClientProductSearch
                        companyId={company_id ?? ""}
                        id={fieldPrepends.product + row.original.id}
                        validateOnTheFly
                        showDropdownIcon
                        minDropdownWidth={160}
                        useSimple
                        useIdForOptions
                        handleChange={newOption => {
                            handleExpenseChange(
                                updateId,
                                "product_id",
                                (newOption as IClientProduct)?.id || null,
                                isRecurring,
                            );
                        }}
                        helperText=""
                        disableClearable
                        sx={row.original.product ? filledAutocompleteStyles : {width: "100%"}}
                        defaultOptions={row.original.product ? [row.original.product] : undefined}
                        multiple={false}
                        filterSelectedOptions={false}
                        includeInputInList={false}
                        freeSolo={false}
                        autoComplete={false}
                    />
                ) : (
                    <MemoizedProductSearch
                        id={fieldPrepends.product + row.original.id}
                        useSimple
                        sx={row.original.product ? filledAutocompleteStyles : {width: "100%"}}
                        defaultOptions={
                            row.original.product
                                ? [
                                      {
                                          ...row.original.product,
                                          company: row.original.vendor,
                                      },
                                  ]
                                : undefined
                        }
                        hideSearchPrepend
                        clearOnSelect={false}
                        onValueChange={(_, __, newOption) =>
                            handleExpenseChange(updateId, "product_id", newOption?.id || null, isRecurring)
                        }
                        showStackAndContractProductsFor={activeCompany?.id}
                        placeholder="Not Selected"
                        disableClearable
                        minDropdownWidth={380}
                    />
                );
            },
        [filledAutocompleteStyles, handleExpenseChange, activeCompany?.id, isReadOnly],
    );

    const PlaidCategoryCell = useMemo(
        () =>
            ({row, filledAutocompleteStyles, handleExpenseChange}: any) => {
                const [updatingRows] = useAtom(updatingRowsAtom);
                const isUpdating =
                    updatingRows.has(row.original.id) || updatingRows.has(row.original.plaid_subscription_id);
                const showAsReadOnly = isReadOnly || !isEditable;
                const isRecurring = !!row.original.plaid_subscription_id;
                const updateId = isRecurring ? row.original.plaid_subscription_id : row.original.id;

                return isUpdating ? (
                    <Skeleton width={166} height={40} />
                ) : showAsReadOnly ? (
                    <Typography
                        variant="body4"
                        fontInter
                        color={theme.palette.neutral[800]}
                        display="flex"
                        alignItems="center"
                        marginLeft={1}
                        gap={1}>
                        <CircleIcon sx={{height: 16, width: 16}} htmlColor={row.original.plaid_category?.color} />
                        {row.original.plaid_category?.name || "--"}
                    </Typography>
                ) : (
                    <PlaidCategoryAutocomplete
                        id={fieldPrepends.plaid_category + row.original.id}
                        sx={row.original.plaid_category?.id ? filledAutocompleteStyles : {width: "100%"}}
                        useSimple
                        validateOnTheFly
                        minDropdownWidth={160}
                        onValueChange={(_, __, newOption) => {
                            if (!newOption?.id) return;
                            handleExpenseChange(updateId, "plaid_category_id", newOption?.id || null, isRecurring);
                        }}
                        disableClearable
                        isRequired
                    />
                );
            },
        [filledAutocompleteStyles, handleExpenseChange, activeCompany?.id],
    );

    //? Still needed for V2
    // const CategoryCell = useMemo(
    //     () =>
    //         ({row, filledAutocompleteStyles, handleExpenseChange}: any) => {
    //             const [updatingRows] = useAtom(updatingRowsAtom);
    //             const isUpdating = updatingRows.has(row.original.id);

    //             return isUpdating ? (
    //                 <Skeleton width={166} height={40} />
    //             ) : isEditable ? (
    //                 <MemoizedCategoryAutocomplete
    //                     hideSubcategoryInput
    //                     useSimple
    //                     wrapperSx={{width: "100%"}}
    //                     mainCategorySx={row.original.category ? filledAutocompleteStyles : {width: "100%"}}
    //                     categoryInputId={fieldPrepends.category + row.original.id}
    //                     mainCategoryPlaceholder="Uncategorized"
    //                     onParentValueChange={(_, __, newOption) =>
    //                         handleExpenseChange(row.original.id, "category_id", newOption?.id || null)
    //                     }
    //                     disableClearable
    //                     company_type={activeCompany?.company_type}
    //                 />
    //             ) : (
    //                 <Box sx={{overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", maxWidth: "100%"}}>
    //                     <Typography variant="body4" fontInter display="flex" alignItems="center" gap={1}>
    //                         {row.original.category?.color && (
    //                             <CircleIcon sx={{height: 16, width: 16}} htmlColor={row.original.category?.color} />
    //                         )}
    //                         {row.original.category?.name || "Not Selected"}
    //                     </Typography>
    //                 </Box>
    //             );
    //         },
    //     [filledAutocompleteStyles, handleExpenseChange, activeCompany?.id],
    // );

    // const ContractCell = useMemo(
    //     () =>
    //         ({row, filledAutocompleteStyles, handleExpenseChange}: any) => {
    //             const [updatingRows] = useAtom(updatingRowsAtom);
    //             const isUpdating = updatingRows.has(row.original.id);

    //             return isUpdating ? (
    //                 <Skeleton width={166} height={40} />
    //             ) : (
    //                 <MemoizedContractAutocomplete
    //                     id={fieldPrepends.contract + row.original.id}
    //                     company_id={activeCompany?.id}
    //                     useSimple
    //                     sx={row.original.contract ? filledAutocompleteStyles : {width: "100%"}}
    //                     onValueChange={(_, __, newOption) =>
    //                         handleExpenseChange(row.original.id, "contract_id", newOption?.id || null)
    //                     }
    //                     placeholder="Not Selected"
    //                     disableClearable
    //                 />
    //             );
    //         },
    //     [filledAutocompleteStyles, handleExpenseChange, activeCompany?.id],
    // );

    const columns = useMemo(
        () => [
            ...(isEditable
                ? [
                      {
                          Header: checkboxHeader,
                          accessor: "action",
                          Cell: checkboxColumn,
                          width: 50,
                      },
                  ]
                : []),
            {
                Header: "Date",
                accessor: "date",
                sortable: true,
                Cell: ({row}) => (
                    <Typography variant="body4" fontInter>
                        {formatDate(row.original.date || "", DATE_FORMAT_Year_Month_Day)}
                    </Typography>
                ),
            },
            /* //? Commented as requested in CPK-3923 at March 20th 2025
            ...(activeCompany?.company_type !== CompanyType.MSP_CLIENT
                ? [
                      {
                          Header: "",
                          accessor: "vendor.name",
                          Cell: avatarColumn,
                          width: 50,
                      },
                  ]
                : []), */
            {
                Header: "Description",
                accessor: "description",
                maxWidth: 140,
                sortable: true,
                Cell: ({row}) => (
                    <Box maxWidth={140} overflow="hidden" title={row.original.description || ""}>
                        <Typography
                            variant="body4"
                            fontInter
                            maxWidth={140}
                            textOverflow="ellipsis"
                            whiteSpace="nowrap"
                            overflow="hidden"
                            display="block">
                            {row.original.description || ""}
                        </Typography>
                    </Box>
                ),
            },
            {
                Header: "Account",
                accessor: "account.name",
                sortable: true,
                Cell: ({row}) => (
                    <Box display="flex" alignItems="center" gap="10px">
                        <ExpenseAccountAvatar
                            name={row.original.account?.name}
                            logoUrl={row.original.account?.institution_logo}
                        />
                        <Typography
                            variant="14"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            whiteSpace="nowrap"
                            display="block"
                            width={118}
                            title={`${row.original.account?.name} - ${row.original.account?.mask}`}>
                            {row.original.account?.name} - {row.original.account?.mask}
                        </Typography>
                    </Box>
                ),
            },
            {
                Header: "Expense Category",
                accessor: "plaid_category",
                sortable: true,
                Cell: ({row}) => (
                    <PlaidCategoryCell
                        row={row}
                        filledAutocompleteStyles={filledAutocompleteStyles}
                        handleExpenseChange={handleExpenseChange}
                        activeCompanyId={activeCompany?.id}
                    />
                ),
                minWidth: 166,
            },
            {
                Header: "Stack Product",
                accessor: "product",
                sortable: true,
                Cell: ({row}) => {
                    return (
                        <ProductCell
                            row={row}
                            filledAutocompleteStyles={filledAutocompleteStyles}
                            handleExpenseChange={handleExpenseChange}
                            activeCompanyId={activeCompany?.id}
                        />
                    );
                },
                minWidth: 166,
            },
            // {
            //     Header: "Contract",
            //     accessor: "contract",
            //     sortable: true,
            //     Cell: ({row}) => {
            //         return (
            //             <ContractCell
            //                 row={row}
            //                 filledAutocompleteStyles={filledAutocompleteStyles}
            //                 handleExpenseChange={handleExpenseChange}
            //                 activeCompanyId={activeCompany?.id}
            //             />
            //         );
            //     },
            //     minWidth: 166,
            // },
            // {
            //     Header: "Stack Category",
            //     accessor: "category",
            //     sortable: true,
            //     Cell: ({row}) => {
            //         return (
            //             <CategoryCell
            //                 row={row}
            //                 filledAutocompleteStyles={filledAutocompleteStyles}
            //                 handleExpenseChange={handleExpenseChange}
            //             />
            //         );
            //     },
            //     minWidth: 166,
            // },
            {
                Header: "Amount",
                accessor: "amount",
                sortable: true,
                Cell: ({row}) => (
                    <Box display="flex" flexDirection="column" gap={0.5}>
                        {!!(row.original as ITransactionItem)?.ignore && (
                            <Typography
                                variant="body5"
                                color={theme.palette.neutral[600]}
                                display="flex"
                                alignItems="center"
                                gap={0.5}
                                lineHeight={1}
                                fontWeight={700}>
                                <DoDisturbAltOutlinedIcon sx={{height: 16, width: 16}} />
                                IGNORED
                            </Typography>
                        )}
                        {row.original.plaid_subscription_id && (
                            <Box>
                                <Badge color="blue">Recurring</Badge>
                            </Box>
                        )}
                        <Typography variant="body4" fontInter fontWeight={700} color={theme.palette.neutral[800]}>
                            -{FormatNumber.currency(row.original.amount || 0)}
                        </Typography>
                    </Box>
                ),
            },
            ...(isReadOnly
                ? []
                : [
                      {
                          Header: "",
                          accessor: "add-to-stack",
                          Cell: ({row}) => {
                              const isLinked = !!row.original.my_stack_id;
                              return (
                                  <Box
                                      sx={{cursor: isLinked ? "default" : "pointer"}}
                                      title={isLinked ? "Already added to stack" : "Add to Stack"}
                                      tooltip={{title: isLinked ? "Already added to stack" : "Add to Stack"}}
                                      onClick={
                                          isLinked
                                              ? undefined
                                              : () => setShowAddToStackFor(row.original as ITransactionItem)
                                      }>
                                      <AddCircleOutlineIcon
                                          sx={{height: 24, width: 24}}
                                          htmlColor={isLinked ? theme.palette.neutral[500] : theme.palette.blue[600]}
                                      />
                                  </Box>
                              );
                          },
                          width: 56,
                      },
                      {
                          Header: "",
                          accessor: "ignore",
                          Cell: ({row}) => (
                              <IgnoreButton
                                  sx={{fontSize: "12px !important", padding: "6px 14px !important"}}
                                  iconSx={{width: 14, height: 14}}
                                  expense={row.original}
                              />
                          ),
                          width: 30,
                      },
                  ]),
            ...(isViewingAsClientParent
                ? []
                : [
                      {
                          Header: "",
                          accessor: "viewDetails",
                          Cell: viewDetailsColumn,
                          width: 50,
                      },
                  ]),
        ],
        [company_id, isReadOnly, transactionsQuery.dataUpdatedAt],
    );

    const MemoizedTable = useMemo(
        () => (
            <TableV2Component
                id="expenses-transactions-tab-table"
                customData={data || []}
                columns={columns as []}
                headerBackground={theme.palette.blue[100]}
                isLoading={isLoading}
                onSortChange={handleSortChange}
                hiddenColumns={hiddenColumns || []}
                infiniteLoad={useInfinite ? meta : undefined}
                onInfinitePageChange={() => {
                    infiniteTransactions?.fetchNextPage();
                }}
            />
        ),
        [transactionsQuery.dataUpdatedAt, columns, isLoading, meta],
    );

    useEffect(() => {
        if (!!selectedRows.length) setSelectedRows([]);
    }, [transactionsPage, transactionsFilterState]);

    useEffect(() => {
        if (!hasLoadedOnce.current) getAllCategories();
        hasLoadedOnce.current = true;
        return () => {
            setSelectedRows([]);
        };
    }, []);

    return (
        <ErrorBoundary>
            <FormProvider {...methods}>
                <Box display="flex" flexDirection="column" gap={2} ref={containerRef} width="100%">
                    {!hasNoAccounts && !hideKpis && (
                        <Box display="grid" gridTemplateColumns={{xs: "1fr", md: "1fr 1fr"}} width="100%" gap={2}>
                            <CompanyExpensesKPICard type="total-yearly-cost" />
                            <CompanyExpensesKPICard type="total-monthly-cost" />
                        </Box>
                    )}
                    {!hideSearchAndFilters && !hasNoAccounts && (
                        <PageInnerHeader
                            id="expenses-transactions-tab-header"
                            searchState={[transactionsSearch, setTransactionsSearch]}
                            growSearch
                            filters={transactionsFilters.data}
                            filterState={[transactionsFilterState, setTransactionsFilterState]}
                            hideFilterKeys={hideFilterKeys}
                            fullWidthSearch={fullWidthSearch}
                            key={transactionsFilters.dataUpdatedAt}
                        />
                    )}
                    {customFilterElement
                        ? customFilterElement({
                              filterState: [transactionsFilterState, setTransactionsFilterState],
                              filters: transactionsFilters.data,
                              searchState: [transactionsSearch, setTransactionsSearch],
                          })
                        : null}
                    {hasNoAccounts ? (
                        <NoAccountsBanner />
                    ) : (
                        <>
                            {MemoizedTable}
                            <Box ref={bulkContainerRef} />
                            {!!selectedRows?.length && !isLoading && (
                                <BorderedBox
                                    sx={{
                                        display: "flex",
                                        flexDirection: "row",
                                        padding: 1,
                                        alignItems: "center",
                                        gap: 1,
                                        alignSelft: "stretch",
                                        borderRadius: 2,
                                        bgcolor: theme.palette.neutral[200],
                                        boxShadow: 1,
                                        border: "none",
                                        position: isVisible ? "relative" : "fixed",
                                        bottom: isVisible ? 0 : 8,
                                        left: "auto",
                                        width: isVisible ? "auto" : width,
                                        zIndex: 2,
                                    }}>
                                    <Typography
                                        textAlign="center"
                                        variant="14"
                                        fontWeight={700}
                                        color={theme.palette.blue[800]}
                                        paddingX={2}>
                                        {selectedRows.length} {pluralizeString("item", selectedRows.length)}
                                        <br /> selected
                                    </Typography>
                                    {isClientMsp ? (
                                        <ClientProductInput
                                            companyId={company_id ?? ""}
                                            id={"bulk_product_id"}
                                            validateOnTheFly
                                            showDropdownIcon
                                            minDropdownWidth={160}
                                            useIdForOptions
                                            helperText=""
                                            disableClearable
                                            multiple={false}
                                            filterSelectedOptions={false}
                                            includeInputInList={false}
                                            freeSolo={false}
                                            autoComplete={false}
                                            sx={{flexGrow: 1}}
                                            label="Product"
                                            noOptionsText="No products found"
                                        />
                                    ) : (
                                        <ProductSearch
                                            id="bulk_product_id"
                                            sx={{flexGrow: 1}}
                                            hideSearchPrepend
                                            placeholder="Please select"
                                            textFieldVariant="filled"
                                            variant="filled"
                                            clearOnSelect={false}
                                            minDropdownWidth={380}
                                            showStackAndContractProductsFor={activeCompany?.id}
                                        />
                                    )}
                                    <CategoryAutocomplete
                                        hideSubcategoryInput
                                        mainCategorySx={{width: "100%"}}
                                        wrapperSx={{flexGrow: 1}}
                                        mainCategoryPlaceholder="Please select"
                                        categoryInputId="bulk_category_id"
                                        company_type={activeCompany?.company_type}
                                    />
                                    <ContractAutocomplete
                                        id="bulk_contract_id"
                                        sx={{flexGrow: 1}}
                                        placeholder="Please select"
                                        textFieldVariant="filled"
                                        company_id={activeCompany?.id}
                                    />
                                    <Button
                                        id="apply"
                                        color="blue"
                                        variant="outlined"
                                        sx={{gap: 1}}
                                        disabled={isBulkIgnoring || isBulkUpdating}
                                        loading={isBulkUpdating}
                                        onClick={handleBulkSubmit}>
                                        <DoneAllOutlinedIcon />
                                        Apply
                                    </Button>
                                    <Button
                                        id="ignore"
                                        color="blue"
                                        variant="text"
                                        sx={{gap: 1}}
                                        disabled={isBulkUpdating || isBulkIgnoring}
                                        loading={isBulkIgnoring}
                                        onClick={handleBulkIgnoreSubmit}>
                                        <BlockOutlinedIcon />
                                        Ignore
                                    </Button>
                                </BorderedBox>
                            )}
                            {!useInfinite && (
                                <CustomPagination
                                    metadata={meta as IMeta}
                                    page={transactionsPage}
                                    setPage={setTransactionsPage}
                                    itemsPerPage={transactionsItemsPerPage}
                                    setItemsPerPage={setTransactionsItemsPerPage}
                                    tableId="expenses-transactions-tab-table"
                                />
                            )}
                        </>
                    )}
                </Box>
            </FormProvider>
            {showAddToStackFor && activeCompany?.id && (
                <AddExpenseToStackDialog
                    open
                    onClose={() => {
                        setShowAddToStackFor(null);
                    }}
                    companyId={activeCompany.id}
                    defaultParentCategory={showAddToStackFor.category?.id}
                    defaultSubCategory={showAddToStackFor.sub_category_id || undefined}
                    defaultProduct={showAddToStackFor.product}
                    defaultVendor={showAddToStackFor.vendor}
                    expenseId={showAddToStackFor.plaid_subscription_id || showAddToStackFor.id}
                    isTransaction={!showAddToStackFor.plaid_subscription_id}
                />
            )}
        </ErrorBoundary>
    );
};

export default CompanyExpensesTransactionsTab;
