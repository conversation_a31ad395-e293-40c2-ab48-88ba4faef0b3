import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import Grid from "@mui/material/Grid";
import {FormProvider, useForm} from "react-hook-form";
import ModalComponent from "../../../Molecules/Modal/Modal.component";
import Box from "../../../Atoms/Box/Box.component";
import VendorSearch from "../../../Molecules/VendorSearch/vendorSearch.component";
import ProductSearch from "../../../Molecules/ProductSearch/productSearch.component";
import CategoryAutocomplete from "../../../Molecules/CategoryAutocomplete/categoryAutocomplete.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import {useNaviStack} from "../../../../hooks/fetches/useNaviStack";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {useCompanyExpenses} from "../../../../hooks/fetches/useCompanyExpenses";
import {useState} from "react";
import CustomerVendorSearch from "../../../Molecules/CustomerVendorSearch/customerVendorSearch.component";
import CustomerProductSearch from "../../../Molecules/CustomerProductSearch/customerProductSearch.component";
import {stackService} from "../../../../services/stack.service";
import useNotification from "../../../../hooks/useNotification";
import {useQueryHelper} from "../../../../hooks/helpers/useQueryHelper";
import {getErrorFromArray} from "../../../../utils/error.util";
import {IAutocompleteOption} from "../../../Molecules/Autocomplete/Autocomplete.component";

interface IProps {
    open: boolean;
    onClose?: (vendorFriendlyUrl?: string) => void;
    companyId?: string;
    defaultVendor?: any;
    defaultProduct?: any;
    defaultParentCategory?: string;
    defaultSubCategory?: string;
    isTransaction?: boolean;
    expenseId?: string;
}

const AddExpenseToStackDialog = (props: IProps) => {
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const methods = useForm<{
        category_id: string | undefined;
        sub_category_id: string | undefined;
        vendor_id: any;
        product_id: any;
        product: null;
        vendor: null | IAutocompleteOption;
    }>({
        defaultValues: {
            category_id: props.defaultParentCategory,
            sub_category_id: props.defaultSubCategory,
            vendor_id: props.defaultVendor?.id,
            product_id: props.defaultProduct?.id,
            product: null,
            vendor: null,
        },
    });
    const {activeCompany, isClientMsp} = useActiveCompany();
    const companyId = props.companyId || activeCompany?.id;
    const {updateCompanyExpenses, isUpdating: isUpdatingCompanyExpenses} = useCompanyExpenses(companyId || "", {
        calls: {
            all: false,
        },
    });
    const {updateNaviStack, isUpdating: isUpdatingNaviStack} = useNaviStack(companyId || "", {
        calls: {
            all: false,
        },
    });
    const notify = useNotification();
    const {invalidateMatchedQueries} = useQueryHelper();
    const vendorIdWatcher = methods.watch("vendor_id");
    const productIdWatcher = methods.watch("product_id");

    const handleSubmit = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        setIsSaving(true);
        if (isClientMsp) {
            const data: any = methods.getValues();
            const payload = {
                [data.sub_category_id as string]: [
                    {
                        stack_company_name: data.vendor?.isCreatableOption ? data.vendor.id : undefined,
                        stack_company_id: data.vendor?.isCreatableOption
                            ? undefined
                            : data.vendor?.id ?? data.vendor_id,
                        product_name: data.product?.isCreatableOption ? data.product.id : undefined,
                        product_id: data.product?.isCreatableOption ? undefined : data.product?.id ?? data.product_id,
                    },
                ],
            };
            stackService
                .addToCustomerStack(activeCompany?.id ?? "", payload)
                .then(async res => {
                    notify("Product added successfully", "Success");
                    invalidateMatchedQueries(["naviStack", activeCompany?.id]);
                    const newlyAddedStackItem = res.data?.[0];
                    await updateCompanyExpenses.mutateAsync({
                        mutate_type: props.isTransaction
                            ? MutateTypes.UpdateTransaction
                            : MutateTypes.UpdateSubscription,
                        body: {
                            id: props.expenseId,
                            product_id: newlyAddedStackItem?.product_id,
                            category_id: newlyAddedStackItem?.parent_category_id,
                            sub_category_id: newlyAddedStackItem?.category_id,
                        },
                    });
                })
                .catch(err => {
                    notify(getErrorFromArray(err), "Error");
                })
                .finally(() => {
                    setIsSaving(false);
                    setIsSaving(false);
                    props.onClose?.();
                });
        } else {
            const {vendor_id, product_id, category_id, sub_category_id} = methods.getValues();
            await updateNaviStack.mutateAsync({
                mutate_type: MutateTypes.AddToStack,
                subject_id: companyId,
                msp_friendly_url: activeCompany?.friendly_url,
                requestBody: {
                    [sub_category_id as string]: [
                        {
                            company_id: vendor_id,
                            product_id: product_id,
                            partner_status: "I am a current partner",
                            sold_by_distributor: undefined,
                            distributor_id: undefined,
                            is_recommended_stack: false,
                        },
                    ],
                },
                updateAdoptionDetails: false,
            });
            await updateCompanyExpenses.mutateAsync({
                mutate_type: props.isTransaction ? MutateTypes.UpdateTransaction : MutateTypes.UpdateSubscription,
                body: {
                    id: props.expenseId,
                    product_id,
                    category_id,
                    sub_category_id,
                },
            });
            setIsSaving(false);
            props.onClose?.();
        }
    };

    return (
        <>
            <ModalComponent
                open={props.open}
                onClose={() => props.onClose?.()}
                onSuccess={handleSubmit}
                title={"Add to Stack"}
                okButtonText="ADD TO STACK"
                contentClassName="mt-0"
                modalTheme="blue"
                icon={<AddCircleOutlineOutlinedIcon />}
                justifyButtons="flex-start"
                loading={isSaving}
                customModalBtnTracking={{
                    company_id: props.companyId,
                }}
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "800px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflow: "hidden"},
                }}
                content={
                    <Grid
                        marginTop={2}
                        sx={{overflowY: "auto", overflowX: "hidden", maxHeight: "100%", height: "fit-content"}}>
                        <FormProvider {...methods}>
                            <Box
                                display="grid"
                                gridTemplateColumns={{xs: "1fr", md: "1fr 1fr"}}
                                columnGap={3}
                                rowGap={2}>
                                {isClientMsp ? (
                                    <>
                                        <CustomerVendorSearch
                                            id="vendor_id"
                                            onValueChange={(_: any, __: any, value) =>
                                                methods.setValue("vendor", value)
                                            }
                                            validateOnTheFly
                                            placeholder="Enter Vendor Name"
                                            companyId={activeCompany?.id || ""}
                                            isRequired
                                        />
                                        <CustomerProductSearch
                                            id="product_id"
                                            onValueChange={(_: any, __: any, value) =>
                                                methods.setValue("product", value)
                                            }
                                            validateOnTheFly
                                            placeholder="Enter Product Name"
                                            defaultOptions={props.defaultProduct ? [props.defaultProduct] : undefined}
                                            defaultProductId={props.defaultProduct?.id}
                                            companyId={activeCompany?.id || ""}
                                            isRequired
                                            clearOnBlur={false}
                                            clearOnSelect={false}
                                        />
                                    </>
                                ) : (
                                    <>
                                        <VendorSearch
                                            id="vendor_id"
                                            label="Vendor"
                                            isRequired
                                            placeholder="Select or add a vendor"
                                            defaultOptions={props.defaultVendor ? [props.defaultVendor] : undefined}
                                            clearOnSelect={false}
                                            validateOnTheFly
                                        />
                                        <ProductSearch
                                            id="product_id"
                                            isRequired
                                            placeholder="Select or add a product"
                                            defaultOptions={
                                                props.defaultProduct && vendorIdWatcher === props.defaultVendor?.id
                                                    ? [
                                                          {
                                                              ...props.defaultProduct,
                                                              company: props.defaultVendor || null,
                                                              company_id: props.defaultVendor?.id,
                                                          },
                                                      ]
                                                    : undefined
                                            }
                                            clearOnSelect={false}
                                            companyId={vendorIdWatcher}
                                            disabled={!vendorIdWatcher}
                                            key={vendorIdWatcher + props.defaultProduct?.id}
                                            ignoreOnMountSearch
                                            validateOnTheFly
                                        />
                                    </>
                                )}
                                <Box gridColumn="span 2">
                                    <CategoryAutocomplete
                                        isRequired
                                        wrapperSx={{
                                            display: "grid",
                                            gap: 3,
                                            gridTemplateColumns: {xs: "1fr", md: "1fr 1fr"},
                                        }}
                                        categoryInputId="category_id"
                                        subCategoryInputId="sub_category_id"
                                        validateOnTheFly
                                        company_type={activeCompany?.company_type}
                                    />
                                </Box>
                            </Box>
                        </FormProvider>
                    </Grid>
                }
            />
        </>
    );
};

export default AddExpenseToStackDialog;
