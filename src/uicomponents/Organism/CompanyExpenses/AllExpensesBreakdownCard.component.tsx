import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import ArrowDropDownOutlinedIcon from "@mui/icons-material/ArrowDropDownOutlined";
import ArrowDropUpOutlinedIcon from "@mui/icons-material/ArrowDropUpOutlined";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import CategoryOutlinedIcon from "@mui/icons-material/CategoryOutlined";
import CircleIcon from "@mui/icons-material/Circle";
import RemoveCircleOutlineOutlinedIcon from "@mui/icons-material/RemoveCircleOutlineOutlined";
import SyncOutlinedIcon from "@mui/icons-material/SyncOutlined";
import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {DefaultizedPieValueType, PieItemIdentifier} from "@mui/x-charts/models";
import {useAtom} from "jotai";
import {DateTime} from "luxon";
import {useMemo, useRef, useState} from "react";
import {useNavigate} from "react-router-dom";
import {TableV2Component} from "../../../components/Table/TableV2.component";
import routeConfig from "../../../constants/routeConfig";
import {CPSortType} from "../../../enums/sortType.enum";
import {useCompanyExpenses} from "../../../hooks/fetches/useCompanyExpenses";
import useActiveCompany from "../../../hooks/useActiveCompany";
import useElementSize from "../../../hooks/useElementSize";
import {IAllExpensesBreakdownItem, IExpenseBreakdownItem} from "../../../Interfaces/plaid.interface";
import ErrorBoundary from "../../../utils/errorBoundary.util";
import FormatNumber from "../../../utils/formatNumber.util";
import {pluralizeString} from "../../../utils/formatString.util";
import Badge from "../../Atoms/Badge/badge.component";
import BorderedBox from "../../Atoms/Box/BorderedBox.component";
import Box from "../../Atoms/Box/Box.component";
import Button from "../../Atoms/Button/Button.Component";
import ButtonGroup from "../../Atoms/ButtonGroup/ButtonGroup.component";
import PieChart from "../../Atoms/PieChart/PieChart.component";
import Typography from "../../Atoms/Typography/Typography.component";
import {ExpensesBreakdownDetailAtom} from "../../ModalManager/ExpensesModals/expenses.atoms";
import DateSelectionDropdown from "../../Molecules/DateSelectionDropdown/DateSelectionDropdown.component";
import PlaidLinkButton from "../../Molecules/Plaid/plaidLinkButton.component";
import CompanyExpenseCard from "./CompanyExpenseCard.component";
import LinkExpenseDrawer from "./LinkExpenseDrawer/LinkExpenseDrawer.component";
import useWindowSize from "../../../hooks/useWindowSize";
import usePermissions from "../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../Interfaces/features/features.interface";
import {STACK_EXPENSES_TEXT} from "../../../constants/siteFlags";

const AllExpensesBreakdownCard = ({
    isParentViewingChild,
    company_id,
}: {
    isParentViewingChild?: boolean;
    company_id?: string;
}) => {
    const showOnlyStackExpenses = !!isParentViewingChild;
    const [hoveredData, setHoveredData] = useState<{name: string; value: number; amount: string} | null>(null);
    const [, setDetailsDrawerData] = useAtom(ExpensesBreakdownDetailAtom);
    const [showLinkExpenseFor, setShowLinkExpenseFor] = useState<IExpenseBreakdownItem | null>(null);
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const now = DateTime.now();
    const {
        hasNoAccounts,
        expensesSummary,
        expensesBreakdownOverview,
        expensesBreakdownOverviewFilters,
        setExpensesBreakdownOverviewFilters,
        setExpenseSummaryDates,
    } = useCompanyExpenses(company_id || activeCompany?.id, {
        calls: {
            all: false,
            expensesBreakdownOverview: true,
            expensesSummary: true,
            linkedAccounts: !isParentViewingChild,
        },
        dates: {
            start_date: now.minus({days: 30}).toUTC().toISO(),
            end_date: now.endOf("day").toUTC().toISO(),
        },
        expensesBreakdownOverviewDefaultToggle: showOnlyStackExpenses ? "stack" : "all",
        hide_uncategorized: isParentViewingChild,
        stack_only: isParentViewingChild,
    });
    const pieChartWrapperRef = useRef(null);
    const {width} = useElementSize(pieChartWrapperRef);
    const {width: windowWidth} = useWindowSize();
    const navigate = useNavigate();
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.PLAID_MGNMT_UPDATE]);
    const filterBy = expensesBreakdownOverviewFilters?.filter;
    const isShowingPlaidCategories = expensesBreakdownOverviewFilters?.filter === "all";
    const hasNoPieChartData = !expensesBreakdownOverview.data?.length;

    const showDetailsDrawer = (item: IExpenseBreakdownItem) => {
        setDetailsDrawerData({
            open: true,
            company_id_overwrite: showOnlyStackExpenses ? company_id : undefined,
            data: {
                type: isShowingPlaidCategories ? "plaid_category" : "category",
                start_date: expensesBreakdownOverviewFilters?.start_date,
                end_date: expensesBreakdownOverviewFilters?.end_date,
                item: item,
            },
        });
    };

    const cards = useMemo(
        () => [
            ...(showOnlyStackExpenses
                ? []
                : [
                      {
                          title: "All Expenses",
                          icon: (
                              <RemoveCircleOutlineOutlinedIcon
                                  sx={{height: 24, width: 24}}
                                  htmlColor={theme.palette.error[400]}
                              />
                          ),
                          amount: expensesSummary?.data?.all?.amount || 0,
                          onClick: () => navigate(routeConfig.CompanyExpenses.path + "?tab=transactions"),
                          subtitle: `${expensesSummary?.data?.all?.transactions || 0} ${pluralizeString(
                              "transaction",
                              expensesSummary?.data?.all?.transactions || 0,
                          )} and ${expensesSummary.data?.all?.subscriptions || 0} Recurring Expenses`,
                      },
                  ]),
            ...(showOnlyStackExpenses
                ? []
                : [
                      {
                          title: "Recurring Expenses",
                          icon: (
                              <SyncOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.neutral[500]} />
                          ),
                          amount: expensesSummary.data?.subscriptions?.amount || 0,
                          onClick: () => navigate(routeConfig.CompanyExpenses.path + "?tab=subscriptions"),
                          subtitle: `${expensesSummary.data?.all?.subscriptions || 0} recurring ${pluralizeString(
                              "transaction",
                              expensesSummary.data?.all?.subscriptions || 0,
                          )}`,
                      },
                  ]),
            {
                title: STACK_EXPENSES_TEXT,
                icon: <CategoryOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.neutral[500]} />,
                amount: showOnlyStackExpenses ? undefined : expensesSummary.data?.stack?.amount,
                onClick: () => {
                    setDetailsDrawerData({
                        open: true,
                        data: {
                            type: "stack-categories",
                            start_date: expensesBreakdownOverviewFilters?.start_date,
                            end_date: expensesBreakdownOverviewFilters?.end_date,
                            item: undefined,
                        },
                        company_id_overwrite: showOnlyStackExpenses ? company_id : undefined,
                    });
                },
                subtitle: `From ${expensesSummary.data?.stack?.transactions || 0} ${pluralizeString(
                    "transaction",
                    expensesSummary.data?.stack?.transactions || 0,
                )} and ${expensesSummary.data?.stack?.subscriptions || 0} Recurring ${pluralizeString(
                    "Expense",
                    expensesSummary.data?.stack?.subscriptions || 0,
                )}`,
                additional: showOnlyStackExpenses ? (
                    <Box display="flex" flexDirection="row" alignItems="center" marginTop={1}>
                        <Typography variant="body4" fontWeight={700} color={theme.palette.neutral[700]} fontInter>
                            -
                            {expensesSummary.data?.stack?.amount
                                ? FormatNumber.currency(Number(expensesSummary.data?.stack?.amount || 0))
                                : "$0"}
                        </Typography>
                        <Box
                            display="flex"
                            flexDirection="row"
                            alignItems="center"
                            justifyContent="space-between"
                            gap={3}>
                            <Typography
                                variant="14"
                                fontWeight={700}
                                display="flex"
                                marginLeft={0.5}
                                alignItems="center"
                                color={
                                    expensesSummary.data?.stack?.percentage_diff?.increased
                                        ? theme.palette.error[500]
                                        : theme.palette.success[600]
                                }>
                                {expensesSummary.data?.stack?.percentage_diff?.increased ? (
                                    <ArrowDropUpOutlinedIcon htmlColor={theme.palette.error[500]} />
                                ) : (
                                    <ArrowDropDownOutlinedIcon htmlColor={theme.palette.success[600]} />
                                )}
                                {Math.abs(Number(expensesSummary.data?.stack?.percentage_diff?.change_percentage))}%
                            </Typography>
                        </Box>
                    </Box>
                ) : undefined,
            },
        ],
        [expensesSummary.data],
    );

    const handleMouseMove = (
        e: React.MouseEvent<SVGPathElement, MouseEvent>,
        __: PieItemIdentifier,
        item: DefaultizedPieValueType,
    ) => {
        if (e.shiftKey) {
            setHoveredData({
                name: (item as DefaultizedPieValueType & {name: string}).name as string,
                value: item.value,
                amount: FormatNumber.currency((item as unknown as {amount: number}).amount || 0),
            });
            return;
        }
        const data = expensesBreakdownOverview.data?.find(i => i.id === item?.id);
        if (data)
            showDetailsDrawer({
                ...data,
                isSubRow: false,
            } as unknown as IExpenseBreakdownItem);
    };

    const handleSortChange = (sort: CPSortType.ASC | CPSortType.DESC, orderBy: string | number) => {
        setExpensesBreakdownOverviewFilters({...expensesBreakdownOverviewFilters, sort: `sorting_${orderBy}__${sort}`});
    };

    const columns = [
        {
            Header: "Category",
            accessor: "name",
            Cell: ({row}) => {
                const isUnlinkedItem = !!row.original?.products_count;
                const isSubRow = !!row.original?.isSubRow;
                return (
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        gap={1}
                        sx={{cursor: "pointer"}}
                        onClick={() => showDetailsDrawer(row.original)}>
                        {row.original.color ? (
                            <CircleIcon sx={{height: 16, width: 16}} htmlColor={row.original.color} />
                        ) : null}
                        {isSubRow && (
                            <Typography variant="14" marginRight={1} marginLeft={1}>
                                ⮡
                            </Typography>
                        )}
                        <Typography variant="14" fontInter whiteSpace="nowrap">
                            {row.original?.name}
                        </Typography>
                        {isUnlinkedItem && !isParentViewingChild && (
                            <Badge color="primary">{`${row.original?.products_count} ${pluralizeString(
                                "product",
                                row.original?.products_count,
                            )}/${pluralizeString("contract", row.original?.products_count)} found`}</Badge>
                        )}
                    </Box>
                );
            },
            sortable: true,
        },
        {
            Header: "",
            accessor: "products_count",
            Cell: ({row}) => {
                if (
                    (row.original?.amount || row.original?.percentage) &&
                    row.original.products_count &&
                    !isShowingPlaidCategories &&
                    hasEditAccess &&
                    !isParentViewingChild
                ) {
                    return (
                        <Box display="flex" flexDirection="row" alignItems="center" justifyContent="flex-end">
                            <Button
                                onClick={() => setShowLinkExpenseFor(row.original)}
                                id={`${row.original.id}-link-expense`}
                                color="blue"
                                sx={{color: theme.palette.blue[600], padding: 0.5}}>
                                Link Expense <AddCircleOutlineOutlinedIcon htmlColor={theme.palette.blue[600]} />
                            </Button>
                        </Box>
                    );
                }
                return null;
            },
            width: 160,
        },
        {
            Header: "%",
            accessor: "percentage",
            Cell: ({row}) => (
                <Typography variant="14" fontInter fontWeight={600}>
                    {row.original.percentage}%
                </Typography>
            ),
            sortable: true,
        },
        {
            Header: "Amount",
            accessor: "amount",
            Cell: ({row}) => (
                <Typography variant="14" fontInter fontWeight={600} textAlign="end">
                    {FormatNumber.currency(row.original.amount)} USD
                </Typography>
            ),
            sortable: true,
        },
        {
            Header: (
                <Box display="flex" width="100%">
                    Change %
                </Box>
            ),
            accessor: "change_percentage",
            Cell: ({row}) => {
                const showLinkBtn =
                    !!row.original?.product_count &&
                    !(row.original?.amount || row.original?.percentage) &&
                    !isParentViewingChild;
                if (showLinkBtn && !isShowingPlaidCategories && hasEditAccess)
                    return (
                        <Box display="flex" flexDirection="row" alignItems="center" justifyContent="flex-end">
                            <Button
                                id={`${row.original.id}-link-expense`}
                                color="blue"
                                sx={{color: theme.palette.blue[600], padding: 0.5}}>
                                Link Expense <AddCircleOutlineOutlinedIcon htmlColor={theme.palette.blue[600]} />
                            </Button>
                        </Box>
                    );
                const value = row.original?.percentage_diff?.change_percentage;
                const absDiff = Math.abs(value || 0);
                const diffUp = row.original?.percentage_diff?.increased;
                return (
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        justifyContent="space-between"
                        gap={3}
                        sx={{cursor: "pointer"}}
                        onClick={() => showDetailsDrawer(row.original)}>
                        <Typography
                            variant="14"
                            fontWeight={700}
                            display="flex"
                            alignItems="center"
                            color={diffUp ? theme.palette.error[500] : theme.palette.success[600]}>
                            {diffUp ? (
                                <ArrowDropUpOutlinedIcon htmlColor={theme.palette.error[500]} />
                            ) : (
                                <ArrowDropDownOutlinedIcon htmlColor={theme.palette.success[600]} />
                            )}
                            {absDiff}%
                        </Typography>
                        <ArrowForwardIosOutlinedIcon sx={{height: 24, width: 24}} htmlColor={theme.palette.blue[600]} />
                    </Box>
                );
            },
            sortable: true,
            width: 150,
        },
    ];

    const handleFilterByChange = (newValue: "all" | "stack" | "subscription") => {
        setExpensesBreakdownOverviewFilters({...expensesBreakdownOverviewFilters, filter: newValue});
        setHoveredData(null);
    };

    const toggleBtnStyles = (identifier: string) => ({
        border: `1px solid ${identifier === filterBy ? theme.palette.blue[400] : theme.palette.neutral[300]}`,
        ...(identifier === filterBy && {bgcolor: theme.palette.blue[100]}),
    });

    return (
        <ErrorBoundary>
            <CompanyExpenseCard
                title={
                    <Box display="flex" alignItems="center" gap={2}>
                        {showOnlyStackExpenses ? (
                            <Typography variant="18" color={theme.palette.neutral[800]} fontWeight={500}>
                                {STACK_EXPENSES_TEXT}
                            </Typography>
                        ) : (
                            "Expenses Breakdown"
                        )}
                        {!hasNoAccounts && (
                            <DateSelectionDropdown
                                id="expenseBreakdownCardDateSelection"
                                options={["last-30-days", "this-month", "last-month", "custom"]}
                                onChange={(_, dates) => {
                                    setExpensesBreakdownOverviewFilters({
                                        ...expensesBreakdownOverviewFilters,
                                        start_date: dates.start,
                                        end_date: dates.end,
                                    });
                                    setExpenseSummaryDates({
                                        start_date: dates.start,
                                        end_date: dates.end,
                                    });
                                    setHoveredData(null);
                                }}
                                defaultValue="last-30-days"
                            />
                        )}
                    </Box>
                }
                actionsSx={{width: {xs: "100%", md: "auto"}}}
                actions={
                    showOnlyStackExpenses || hasNoAccounts ? undefined : (
                        <ButtonGroup
                            sx={{
                                width: "100%",
                                borderRadius: 1,
                                marginTop: {xs: 3, md: 0},
                                button: {
                                    padding: "10px 12px",
                                    fontSize: "14px",
                                    ...(windowWidth <= 800 && {borderRadius: 2}),
                                },
                            }}
                            fullWidth
                            orientation={windowWidth <= 800 ? "vertical" : "horizontal"}>
                            <Button
                                id="all-expenses"
                                color="blue"
                                onClick={() => handleFilterByChange("all")}
                                sx={toggleBtnStyles("all")}>
                                All Expenses
                            </Button>
                            <Button
                                id="recurring-expenses"
                                color="blue"
                                onClick={() => handleFilterByChange("subscription")}
                                sx={{
                                    ...toggleBtnStyles("subscription"),
                                    minWidth: "160px !important",
                                }}>
                                Recurring Expenses
                            </Button>
                            <Button
                                id="stack-expenses"
                                color="blue"
                                onClick={() => handleFilterByChange("stack")}
                                sx={toggleBtnStyles("stack")}>
                                {STACK_EXPENSES_TEXT}
                            </Button>
                        </ButtonGroup>
                    )
                }
                showOverlay={hasNoAccounts && !showOnlyStackExpenses}
                overlayProps={{
                    variant: "opaque",
                    color: theme.palette.neutral[200] as string,
                    title: (
                        <Typography textAlign="center">
                            <PlaidLinkButton
                                isTypography
                                typographyProps={{
                                    color: theme.palette.blue[600],
                                    fontWeight: 600,
                                    fontInter: true,
                                    sx: {cursor: "pointer"},
                                }}>
                                Add an account
                            </PlaidLinkButton>
                            <br />
                            to see custom reports
                        </Typography>
                    ),
                }}
                titleWrapperSx={{flexDirection: {xs: "column", md: "row"}}}
                sx={{gridColumn: "span 2", ...(showOnlyStackExpenses && {border: "none"})}}>
                <Box
                    display="grid"
                    width="100%"
                    gridTemplateColumns={{xs: "1fr", lg: "300px 1fr"}}
                    gap={8}
                    overflow="hidden">
                    <Box ref={pieChartWrapperRef} position="relative" height="fit-content" width="300px">
                        {expensesBreakdownOverview.isLoading ? (
                            <Skeleton height={300} width={300} variant="circular" />
                        ) : hasNoPieChartData ? (
                            <Box
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                height={300}
                                width={300}
                                borderRadius="50%"
                                bgcolor={theme.palette.neutral[200]}>
                                No data
                            </Box>
                        ) : (
                            <PieChart
                                height={300}
                                width={width || 300}
                                slotProps={{
                                    legend: {hidden: true},
                                    pieArc: {
                                        onMouseOver: e => {
                                            const target = e.target as HTMLElement;
                                            const clickEvent = new MouseEvent("click", {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                shiftKey: true,
                                            });
                                            target.dispatchEvent(clickEvent);
                                        },
                                    },
                                }}
                                tooltip={{
                                    trigger: "none",
                                }}
                                series={[
                                    {
                                        data:
                                            (expensesBreakdownOverview.data?.map(item => ({
                                                ...item,
                                                value: item.percentage,
                                                "data-slice-id": item.id,
                                            })) as []) || [],
                                        innerRadius: 80,
                                        outerRadius: 136,
                                        startAngle: 365,
                                        endAngle: 0,
                                        cx: width / 2,
                                        cy: "50%",
                                        highlightScope: {highlighted: "item"},
                                        highlighted: {innerRadius: 76, additionalRadius: 8},
                                    },
                                ]}
                                slots={{}}
                                onClick={handleMouseMove}
                                sx={{position: "relative"}}
                            />
                        )}
                        {hoveredData && (
                            <Box
                                position="absolute"
                                display="flex"
                                alignItems={"center"}
                                justifyContent="center"
                                flexDirection="column"
                                top="50%"
                                left="50%"
                                textAlign="center"
                                sx={{
                                    transform: "translate(calc(-50% + 4px), calc(-50% - 4px))",
                                    gap: "5px",
                                    maxWidth: 160,
                                }}>
                                <Typography variant={"16"} fontWeight={700} color={theme.palette.neutral[800]}>
                                    {hoveredData.value}%
                                </Typography>
                                <Typography
                                    variant={"20"}
                                    color={theme.palette.neutral[600]}
                                    fontWeight={700}
                                    fontInter>
                                    {hoveredData.amount}
                                </Typography>
                                <Typography
                                    variant={"12"}
                                    color={theme.palette.neutral[600]}
                                    fontWeight={700}
                                    fontInter>
                                    {hoveredData.name}
                                </Typography>
                            </Box>
                        )}
                    </Box>
                    <Box
                        width="100%"
                        display="flex"
                        flexDirection="column"
                        justifyContent={"flex-start"}
                        gap={1}
                        overflow="hidden">
                        {expensesSummary.isLoading
                            ? Array(showOnlyStackExpenses ? 1 : 3)
                                  .fill(null)
                                  .map((_, i) => <Skeleton key={i} height={64} width="100%" />)
                            : cards.map(card => (
                                  <BorderedBox
                                      key={card.title}
                                      sx={{
                                          display: "flex",
                                          flexDirection: {xs: "column", md: "row"},
                                          gap: 2,
                                          cursor: "pointer",
                                          transition: "all 0.2s ease-in-out",
                                          width: "100%",
                                          "&:hover": {bgcolor: theme.palette.neutral[200]},
                                      }}
                                      onClick={card.onClick}>
                                      <Box display="flex" alignItems="center">
                                          {card.icon}
                                      </Box>
                                      <Box display="flex" flexDirection="column" flexGrow={1}>
                                          <Typography
                                              variant="body4"
                                              fontInter
                                              fontWeight={700}
                                              color={theme.palette.blue[800]}>
                                              {card.title}
                                          </Typography>
                                          <Typography
                                              variant="body4"
                                              fontWeight={700}
                                              fontInter
                                              color={theme.palette.neutral[600]}>
                                              {card.subtitle}
                                          </Typography>
                                          {card.additional && card.additional}
                                      </Box>
                                      <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
                                          {card.amount !== undefined && (
                                              <Typography
                                                  variant="body4"
                                                  fontWeight={700}
                                                  color={theme.palette.neutral[700]}
                                                  fontInter>
                                                  -
                                                  {card.amount
                                                      ? FormatNumber.currency(Number(card?.amount || 0))
                                                      : "$0"}
                                              </Typography>
                                          )}

                                          <ArrowForwardIosOutlinedIcon
                                              sx={{height: 24, width: 24, marginLeft: "auto"}}
                                              htmlColor={theme.palette.blue[600]}
                                          />
                                      </Box>
                                  </BorderedBox>
                              ))}
                    </Box>
                    <Box gridColumn={{xs: "span 1", lg: "span 2"}} maxWidth="100%" sx={{overflowX: "auto"}}>
                        <TableV2Component
                            id="expenses-breakdown-table"
                            customData={expensesBreakdownOverview.data || []}
                            columns={columns as []}
                            headerBackground={theme.palette.blue[100]}
                            onSortChange={handleSortChange}
                            isLoading={expensesBreakdownOverview.isLoading}
                            subTables={
                                isShowingPlaidCategories
                                    ? undefined
                                    : {
                                          customData: (row: IAllExpensesBreakdownItem) => {
                                              return [
                                                  ...(row.sub_categories || []).map(i => ({
                                                      ...i,
                                                      isSubRow: true,
                                                      parent: {
                                                          id: row.id,
                                                          name: row.name,
                                                          color: row.color,
                                                      },
                                                  })),
                                              ];
                                          },
                                      }
                            }
                        />
                    </Box>
                </Box>
            </CompanyExpenseCard>
            {!!showLinkExpenseFor && (
                <LinkExpenseDrawer open onClose={() => setShowLinkExpenseFor(null)} item={showLinkExpenseFor} />
            )}
        </ErrorBoundary>
    );
};

export default AllExpensesBreakdownCard;
