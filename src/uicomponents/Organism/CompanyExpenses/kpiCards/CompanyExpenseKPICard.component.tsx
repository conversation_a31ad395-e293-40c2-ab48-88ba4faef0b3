import useTheme from "@mui/material/styles/useTheme";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import ArrowDropDownOutlinedIcon from "@mui/icons-material/ArrowDropDownOutlined";
import ArrowDropUpOutlinedIcon from "@mui/icons-material/ArrowDropUpOutlined";
import FormatNumber from "../../../../utils/formatNumber.util";
import {monthNamesShort, monthsLongAndShort} from "../../../../utils/formatDate";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useCompanyExpenses} from "../../../../hooks/fetches/useCompanyExpenses";
import Skeleton from "@mui/material/Skeleton";
import {useEffect, useState} from "react";
import {pluralizeString} from "../../../../utils/formatString.util";
import {DateTime} from "luxon";
import ArrowRightAltOutlinedIcon from "@mui/icons-material/ArrowRightAltOutlined";

type TCardTypes =
    | "total-yearly-cost"
    | "total-monthly-cost"
    | "all-expenses"
    | "recurring-expenses-yearly"
    | "recurring-expenses-monthly"
    | "recurring-expenses"
    | "total-contracts-cost";

const cardTypes: Record<string, TCardTypes> = {
    TOTAL_YEARLY_COST: "total-yearly-cost",
    TOTAL_MONTHLY_COST: "total-monthly-cost",
    ALL_EXPENSES: "all-expenses",
    RECURRING_EXPENSES_YEARLY: "recurring-expenses-yearly",
    RECURRING_EXPENSES_MONTHLY: "recurring-expenses-monthly",
    RECURRING_EXPENSES: "recurring-expenses",
    TOTAL_CONTRACTS_COST: "total-contracts-cost",
};

type TCardStats = {
    amount: number;
    percentage: string;
    increased: boolean;
    subTitle: string;
    hidePercentage: boolean;
};

interface IProps {
    type: TCardTypes;
    filterById?: string | null;
    filterType?: "category" | "vendor";
    dateRange?: {start_date?: string; end_date?: string};
    company_id_overwrite?: string;
    customTitle?: string;
    expensesSummaryFilters?: {category?: string[]; sub_category?: string[]};
}

const CompanyExpensesKPICard = ({
    type,
    filterById,
    filterType,
    dateRange,
    company_id_overwrite,
    customTitle,
    expensesSummaryFilters,
}: IProps) => {
    const [cardStats, setCardStats] = useState<TCardStats>({
        amount: 0,
        percentage: "",
        increased: false,
        subTitle: "",
        hidePercentage: false,
    });
    const previousMonth = new Date();
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    const fromDate =
        type === cardTypes.TOTAL_MONTHLY_COST || type === cardTypes.RECURRING_EXPENSES_MONTHLY
            ? monthNamesShort[previousMonth.getMonth()]
            : new Date().getFullYear() - 1;
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const company_id = company_id_overwrite || activeCompany?.id;
    const useSubscriptionCosts = type === cardTypes.TOTAL_MONTHLY_COST || type === cardTypes.TOTAL_YEARLY_COST;
    const now = DateTime.now();
    const isByMonth = type.includes("monthly");
    const isByYear = type.includes("yearly");
    const start = now
        .startOf(isByMonth ? "month" : "year")
        .toUTC()
        .toISO();
    const end = now
        .endOf(isByMonth ? "month" : "year")
        .toUTC()
        .toISO();
    const {subscriptionCosts, expensesSummary, expenseSummaryDates, setExpenseSummaryDates} = useCompanyExpenses(
        company_id,
        {
            calls: {
                all: false,
                subscriptionCosts: true,
                expensesSummary: true,
            },
            dates: dateRange,
            expenseSummaryDates: isByMonth || isByYear ? {start_date: start, end_date: end} : undefined,
            expensesSummaryFilters,
            ...(filterType === "category" && filterById !== undefined && {costsCategoryId: filterById}),
            ...(filterType === "vendor" && filterById !== undefined && {costsVendorId: filterById}),
        },
    );
    const isLoading = subscriptionCosts.isLoading || expensesSummary.isLoading;
    const currency = "USD";
    const title = customTitle
        ? customTitle
        : type === cardTypes.TOTAL_CONTRACTS_COST
          ? "Total Contract Cost"
          : type === cardTypes.TOTAL_MONTHLY_COST
            ? `Total ${monthsLongAndShort[new Date().getMonth()]?.long} Cost`
            : type === cardTypes.TOTAL_YEARLY_COST
              ? `Total ${new Date().getFullYear()} Cost`
              : type === cardTypes.ALL_EXPENSES
                ? "All Expenses"
                : type === cardTypes.RECURRING_EXPENSES_MONTHLY
                  ? `Total Recurring ${monthsLongAndShort[new Date().getMonth()]?.long} Expenses`
                  : type === cardTypes.RECURRING_EXPENSES_YEARLY
                    ? `Total Recurring ${new Date().getFullYear()} Expenses`
                    : "Recurring Expenses";
    const {amount, percentage, hidePercentage, increased, subTitle} = cardStats;
    const greenColor = theme.palette.success[600];
    const redColor = theme.palette.error[500];

    useEffect(() => {
        if (subscriptionCosts.data || expensesSummary.data) {
            const subscriptionsCount = expensesSummary.data?.subscriptions?.transactions || 0;
            switch (type) {
                case cardTypes.TOTAL_YEARLY_COST:
                    const allYearDifference =
                        Number(expensesSummary.data?.["all"].amount || 0) -
                        Number(expensesSummary.data?.["all"]?.percentage_diff?.previous_amount || 0);
                    setCardStats({
                        amount: Number(expensesSummary.data?.["all"].amount || 0),
                        percentage: expensesSummary.data?.["all"]?.percentage_diff?.change_percentage || "",
                        increased: !!expensesSummary.data?.["all"]?.percentage_diff?.increased,
                        subTitle:
                            allYearDifference === 0
                                ? `No changes since ${fromDate}`
                                : `${FormatNumber.currency(Math.abs(allYearDifference))} ${
                                      allYearDifference <= 0 ? "less" : "more"
                                  } than ${fromDate}`,
                        hidePercentage: allYearDifference === 0,
                    });
                    break;
                case cardTypes.TOTAL_MONTHLY_COST:
                    const allMonthDifference =
                        Number(expensesSummary.data?.["all"].amount || 0) -
                        Number(expensesSummary.data?.["all"]?.percentage_diff?.previous_amount || 0);
                    setCardStats({
                        amount: Number(expensesSummary.data?.["all"].amount || 0),
                        percentage: expensesSummary.data?.["all"]?.percentage_diff?.change_percentage || "",
                        increased: !!expensesSummary.data?.["all"]?.percentage_diff?.increased,
                        subTitle:
                            allMonthDifference === 0
                                ? `No changes since ${fromDate}`
                                : `${FormatNumber.currency(Math.abs(allMonthDifference))} ${
                                      allMonthDifference <= 0 ? "less" : "more"
                                  } than ${fromDate}`,
                        hidePercentage: allMonthDifference === 0,
                    });
                    break;
                case cardTypes.ALL_EXPENSES:
                    const allTransactionsCount = expensesSummary.data?.all?.transactions || 0;
                    const recurringTransactionsCount = expensesSummary.data?.all?.subscriptions || 0;
                    setCardStats({
                        amount: Number(expensesSummary.data?.all?.amount || 0),
                        percentage: expensesSummary.data?.all?.percentage_diff?.change_percentage || "0",
                        increased: expensesSummary.data?.all?.percentage_diff?.increased || false,
                        subTitle: `${allTransactionsCount} ${pluralizeString(
                            "transaction",
                            allTransactionsCount,
                        )} and ${recurringTransactionsCount} recurring ${pluralizeString(
                            "expense",
                            recurringTransactionsCount,
                        )}`,
                        hidePercentage: false,
                    });
                    break;
                case cardTypes.TOTAL_CONTRACTS_COST:
                    setCardStats({
                        amount: Number(expensesSummary.data?.contracts?.amount || 0),
                        percentage: expensesSummary.data?.contracts?.percentage_diff?.change_percentage || "0",
                        increased: expensesSummary.data?.contracts?.percentage_diff?.increased || false,
                        subTitle: `From ${expensesSummary.data?.contracts?.count} ${pluralizeString(
                            "Contract",
                            expensesSummary.data?.contracts?.count || 0,
                        )}`,
                        hidePercentage: false,
                    });
                    break;
                case cardTypes.RECURRING_EXPENSES_MONTHLY:
                    const recurringMonthDifference =
                        Number(expensesSummary.data?.["subscriptions"].amount) -
                        Number(expensesSummary.data?.["subscriptions"]?.percentage_diff?.previous_amount || 0);
                    setCardStats({
                        amount: Number(expensesSummary.data?.subscriptions?.amount || 0),
                        percentage: expensesSummary.data?.subscriptions?.percentage_diff?.change_percentage || "0",
                        increased: expensesSummary.data?.subscriptions?.percentage_diff?.increased || false,
                        subTitle:
                            recurringMonthDifference === 0
                                ? `No changes since ${fromDate}`
                                : `${FormatNumber.currency(Math.abs(recurringMonthDifference))} ${
                                      recurringMonthDifference <= 0 ? "less" : "more"
                                  } than ${fromDate}`,
                        hidePercentage: false,
                    });
                    break;
                case cardTypes.RECURRING_EXPENSES_YEARLY:
                    const recurringYearDifference =
                        Number(expensesSummary.data?.["subscriptions"].amount) -
                        Number(expensesSummary.data?.["subscriptions"]?.percentage_diff?.previous_amount || 0);
                    setCardStats({
                        amount: Number(expensesSummary.data?.subscriptions?.amount || 0),
                        percentage: expensesSummary.data?.subscriptions?.percentage_diff?.change_percentage || "0",
                        increased: expensesSummary.data?.subscriptions?.percentage_diff?.increased || false,
                        subTitle:
                            recurringYearDifference === 0
                                ? `No changes since ${fromDate}`
                                : `${FormatNumber.currency(Math.abs(recurringYearDifference))} ${
                                      recurringYearDifference <= 0 ? "less" : "more"
                                  } than ${fromDate}`,
                        hidePercentage: false,
                    });
                    break;
                case cardTypes.RECURRING_EXPENSES:
                    setCardStats({
                        amount: Number(expensesSummary.data?.subscriptions?.amount || 0),
                        percentage: expensesSummary.data?.subscriptions?.percentage_diff?.change_percentage || "0",
                        increased: expensesSummary.data?.subscriptions?.percentage_diff?.increased || false,
                        subTitle: `${subscriptionsCount} recurrent ${pluralizeString(
                            "transaction",
                            subscriptionsCount,
                        )}`,
                        hidePercentage: false,
                    });
                    break;
                default:
                    console.error("Logic not implemented for :: " + type);
                    break;
            }
        }
    }, [subscriptionCosts.dataUpdatedAt, expensesSummary.dataUpdatedAt]);

    useEffect(() => {
        if (
            dateRange &&
            !useSubscriptionCosts &&
            (dateRange.start_date !== expenseSummaryDates.start_date ||
                dateRange.end_date !== expenseSummaryDates.end_date)
        ) {
            setExpenseSummaryDates(dateRange);
        }
    }, [dateRange]);

    return (
        <BorderedBox sx={{display: "flex", flexDirection: "column", gap: 1}}>
            <Typography variant="16" fontInter fontWeight={500}>
                {title}
            </Typography>
            <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="23" fontSize={22} fontWeight={600} fontInter>
                    {isLoading ? <Skeleton width={80} height={32} /> : FormatNumber.currency(amount)}
                </Typography>
                <Typography variant="18" fontWeight={600} fontInter marginX={0.5}>
                    {currency}
                </Typography>
                <Box display={isLoading || hidePercentage ? "none" : "flex"} flexDirection="row" alignItems="center">
                    {Number(percentage) === 0 ? (
                        <ArrowRightAltOutlinedIcon
                            htmlColor={theme.palette.blue[600]}
                            sx={{height: 24, width: 24, marginLeft: 0.5}}
                        />
                    ) : increased ? (
                        <ArrowDropUpOutlinedIcon htmlColor={redColor} />
                    ) : (
                        <ArrowDropDownOutlinedIcon htmlColor={greenColor} />
                    )}
                    <Typography
                        variant="14"
                        fontWeight={700}
                        fontInter
                        color={Number(percentage) === 0 ? theme.palette.blue[600] : increased ? redColor : greenColor}>
                        {percentage}%
                    </Typography>
                </Box>
            </Box>
            {isLoading ? (
                <Skeleton height={18} width={120} />
            ) : (
                <Typography variant="body5" fontInter fontWeight={700} color={theme.palette.neutral[600]}>
                    {subTitle}
                </Typography>
            )}
        </BorderedBox>
    );
};

export default CompanyExpensesKPICard;
