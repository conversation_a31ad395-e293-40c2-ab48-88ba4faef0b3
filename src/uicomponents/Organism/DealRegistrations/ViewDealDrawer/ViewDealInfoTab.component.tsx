import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useRef} from "react";
import {useFormContext} from "react-hook-form";
import {DEAL_EXPIRATION_OPTIONS, DEAL_STATUS} from "../../../../constants/dealRegistrations.constant";
import {useDealRegistration} from "../../../../hooks/fetches/useDealRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import usePermissions from "../../../../hooks/usePermissions";
import {IDealRegistration} from "../../../../Interfaces/dealRegistrations.interface";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {DATE_FULL_SHORT_MONTH, formatDate} from "../../../../utils/formatDate";
import FormatNumber from "../../../../utils/formatNumber.util";
import {validateEmail} from "../../../../utils/validation.util";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import CompanyEmailSearch, {
    ICompanyEmailSearchOption,
} from "../../../Molecules/CompanyEmailSearch/CompanyEmailSearch.component";

const ViewDealInfoTab = ({deal}: {deal?: IDealRegistration}) => {
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {dealContacts, updateDealRegistration, isUpdating} = useDealRegistration(
        activeCompany?.id || "",
        deal?.id || "",
        {
            calls: {all: false, dealContacts: activeCompany?.type_is_of_vendor},
        },
    );
    const {watch, setValue} = useFormContext();
    const {hasPermissions} = usePermissions();
    const isVendor = activeCompany?.type_is_of_vendor;
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_DEALS_UPDATE]);
    const isUpdatingDeal = isUpdating[MutateTypes.UpdateDealRegistration + deal?.id];
    const isUpdatingExpirationDays = isUpdating[MutateTypes.UpdateDealExpirationDays + deal?.id];
    const isRemovingRecipients = isUpdating[MutateTypes.DeleteDealContacts];
    const isDeclinedOrAccepted = deal?.status === DEAL_STATUS.APPROVED || deal?.status === DEAL_STATUS.DECLINED;
    const expiration_days = watch("expiration_days");
    const prevRecipients = useRef<ICompanyEmailSearchOption[]>([]);

    const handleChangeExpirationDays = (_: string, value: string) => {
        if (value && deal) {
            updateDealRegistration.mutate({
                mutate_type: MutateTypes.UpdateDealExpirationDays,
                company_id: deal.owner.id,
                mutate_is_updating_append: deal?.id,
                body: Number(value) || null,
            });
        }
    };

    const handleRecipientsChange = (id: string, value, option) => {
        if (!value || !option.id) return;
        const isArrayOfOptions = Array.isArray(option);
        const isRemoving = !!prevRecipients.current?.find(o => o.id === option.id);
        prevRecipients.current = value || [];
        if (isRemoving) {
            updateDealRegistration.mutate({
                mutate_type: MutateTypes.DeleteDealContacts,
                body: [option.email],
            });
            return;
        }
        const contactsToAdd: Array<{user_id?: string; email: string}> = [];
        if (isArrayOfOptions) {
            option.forEach(o => {
                const contactToAdd = {email: typeof o === "string" ? o : o.email};
                if (o?.id) {
                    contactToAdd["user_id"] = o.id;
                }
                contactsToAdd.push(contactToAdd);
            });
        } else {
            const contactToAdd = {email: option.email};
            if (option.id) {
                contactToAdd["user_id"] = option.id;
            }
            contactsToAdd.push(contactToAdd);
        }
        updateDealRegistration.mutate({
            mutate_type: MutateTypes.CreateDealContacts,
            body: contactsToAdd,
        });
    };

    const handleClearRecipients = () => {
        updateDealRegistration.mutate({
            mutate_type: MutateTypes.DeleteDealContacts,
            body: dealContacts?.data?.map(c => c.email),
            onSuccess: () => {
                dealContacts.refetch();
            },
        });
    };

    useEffect(() => {
        if (dealContacts?.data) {
            const newOptions = dealContacts?.data?.map(item => ({
                id: item.id,
                label: item.name,
                email: item.email,
            }));
            prevRecipients.current = newOptions;
            setValue("recipients", newOptions);
        }
    }, [dealContacts.data]);

    useEffect(() => {
        if (isVendor && !!deal) {
            setValue("expiration_days", String(deal.expiration_days) || "null");
        }
    }, [deal?.expiration_days, isVendor]);

    return (
        <Box display="flex" flexDirection="column" gap={2} width="100%">
            <Typography fontInter variant="body2" fontWeight={500} color={theme.palette.neutral[700]}>
                Deal Information
            </Typography>
            {!isVendor && (
                <Box display="flex" flexDirection="column">
                    <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                        Vendor
                    </Typography>
                    <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                        {deal?.company?.name || "--"}
                    </Typography>
                </Box>
            )}
            <Box display="flex" flexDirection="column">
                <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                    Deal Name
                </Typography>
                <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                    {deal?.name || "--"}
                </Typography>
            </Box>
            <Box display="flex" flexDirection="column">
                <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                    Expected Deal Size ($)
                </Typography>
                <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                    {FormatNumber.currency(deal?.size || 0)}/month
                </Typography>
            </Box>
            {isVendor && (
                <>
                    <AutoCompleteComponent
                        id="expiration_days"
                        label="Deal Request Expiration"
                        placeholder="Select Deal Request Expiration"
                        options={DEAL_EXPIRATION_OPTIONS}
                        isRequired
                        validateOnTheFly
                        onValueChange={handleChangeExpirationDays}
                        loading={isUpdatingExpirationDays}
                        disabled={isUpdatingDeal || isUpdatingExpirationDays}
                        readOnly={isDeclinedOrAccepted || dealContacts.isLoading || !hasEditAccess}
                        key={expiration_days}
                    />
                    <CompanyEmailSearch
                        sx={{
                            width: "100%",
                        }}
                        id="recipients"
                        label="Deal Request Recipient(s)"
                        placeholder="Type to select a user or add an email"
                        validateOption={v => validateEmail(v)}
                        helperText="Select a user from your company, and/or input the emails separated by commas. If no email recipient is selected or inputted, no email notifications will be received."
                        onValueChange={handleRecipientsChange}
                        readOnly={isUpdatingDeal || isDeclinedOrAccepted || dealContacts.isLoading || !hasEditAccess}
                        limitTagsToSingleLine
                        disabled={isUpdating[MutateTypes.CreateDealContacts] || isRemovingRecipients}
                        hideEmailForCompanyEmail
                        onClear={handleClearRecipients}
                    />
                </>
            )}
            <Box display="flex" flexDirection="column">
                <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                    Deal Request Expiration
                </Typography>
                <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                    {deal?.expiration_date ? formatDate(deal.expiration_date, DATE_FULL_SHORT_MONTH) : "--"}
                </Typography>
            </Box>
            {/* Commenting out due to Krista's request - Drew 9/13/2024 */}
            {/* <Box display="flex" flexDirection="column">
                <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                    Deal Status
                </Typography>
                <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                    {formatString(deal?.partnership_status || "", CAPITALIZE_ALL)}
                </Typography>
            </Box> */}
            <Box display="flex" flexDirection="column">
                <Typography fontInter variant="body5" fontWeight={600} color={theme.palette.neutral[800]}>
                    Description of Opportunity
                </Typography>
                <Typography fontInter variant="body3" color={theme.palette.neutral[800]}>
                    {deal?.description || "--"}
                </Typography>
            </Box>
            <Typography fontInter variant="body2" fontWeight={500} color={theme.palette.neutral[700]} paddingTop={1}>
                Key Stakeholders Involved
            </Typography>
            {deal?.stakeholders?.map(stakeholder => (
                <>
                    <Box
                        display="flex"
                        flexDirection="row"
                        key={stakeholder.id}
                        maxWidth="100%"
                        overflow="hidden"
                        flexGrow={1}>
                        <Typography
                            fontInter
                            variant="body3"
                            color={theme.palette.neutral[800]}
                            overflow="hidden"
                            title={`${stakeholder.name} (${stakeholder.email})`}
                            textOverflow="ellipsis">
                            {stakeholder.name} ({stakeholder.email})
                        </Typography>
                    </Box>
                </>
            ))}
        </Box>
    );
};

export default ViewDealInfoTab;
