import useTheme from "@mui/material/styles/useTheme";
import {TableV2Component} from "../../../../components/Table/TableV2.component";
import {useDealRegistration} from "../../../../hooks/fetches/useDealRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import Box from "../../../Atoms/Box/Box.component";
import PageInnerHeader from "../../PageInnerHeader/pageInnerHeader";
import Typography from "../../../Atoms/Typography/Typography.component";
import {formatDate, FULL_DATETIME_SHORT_MONTH_TIMEZONE} from "../../../../utils/formatDate";
import formatString, {CAPITALIZE_ALL, CAPITALIZE_FIRST, FROM_CAMEL} from "../../../../utils/formatString.util";
import DownloadOutlinedIcon from "@mui/icons-material/DownloadOutlined";
import {CPSortType} from "../../../../enums/sortType.enum";
import {dealRegistrationService} from "../../../../services/dealRegistration.service";
import {downloadDoc} from "../../../../utils/downloadDoc.util";
import {useState} from "react";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import ReadMoreTypography from "../../../Molecules/ReadMoreTypography/ReadMoreTypography.component";

const DealHistoryTab = ({deal_id}: {deal_id?: string}) => {
    const [isExporting, setIsExporting] = useState<boolean>(false);
    const {activeCompany} = useActiveCompany();
    const {dealHistory, dealHistoryFilters, dealHistorySearchState, dealHistoryFilterState, dealHistoryPage} =
        useDealRegistration(activeCompany?.id || "", deal_id || "", {
            calls: {
                all: false,
                dealHistory: true,
                dealHistoryFilters: true,
            },
        });
    const theme = useTheme();
    const [filters, setFilters] = dealHistoryFilterState;
    const last_page = dealHistory.data?.pages?.[0]?.meta?.last_page;
    const dealHistoryData =
        dealHistory.data?.pages.reduce((cur, page) => {
            return [...cur, ...(page?.data || [])];
        }, []) || [];

    const [orderBy, sortType] = ((dealHistoryFilterState?.[0]?.sort as string) || "")?.split("__") as [
        string | undefined,
        CPSortType.ASC | CPSortType.DESC | undefined,
    ];

    const columns = [
        {
            accessor: "created_at",
            Header: "Date",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4" fontInter>
                    {formatDate(row.original?.created_at || "", FULL_DATETIME_SHORT_MONTH_TIMEZONE)}
                </Typography>
            ),
        },
        {
            accessor: "first_name",
            Header: "Author",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4" fontInter>
                    {row.original?.author?.name || ""}
                </Typography>
            ),
        },
        {
            accessor: "action",
            Header: "Event",
            sortable: true,
            Cell: ({row}) => (
                <Typography variant="body4" fontInter>
                    {formatString(formatString(row.original?.action || "", FROM_CAMEL), CAPITALIZE_ALL)}
                </Typography>
            ),
        },
        {
            accessor: "additional_data.description",
            Header: "Description",
            Cell: ({row}) => (
                <ReadMoreTypography
                    text={`${row.original?.additional_data?.event ? row.original?.additional_data?.event + "; " : ""}${
                        typeof row.original?.additional_data === "string"
                            ? row.original.additional_data
                            : `${
                                  row.original?.additional_data?.description
                                      ? ` ${row.original?.additional_data?.description}`
                                      : ""
                              }${row.original.additional_data?.requested_info ? "; " : ""}${
                                  row.original.additional_data?.requested_info || ""
                              }`
                    }`}
                    fontSize={14}
                    maxLines={2}
                />
            ),
        },
    ];

    const handleSortChange = (sort: CPSortType.ASC | CPSortType.DESC, order_by: string | number) => {
        setFilters({
            ...filters,
            sort: order_by + "__" + sort,
        });
    };

    const handleExportCSV = () => {
        if (!activeCompany?.id || !deal_id) return;
        setIsExporting(true);
        dealRegistrationService
            .getDealHistoryCSV(activeCompany?.id || "", deal_id)
            .then(res => {
                downloadDoc(res.data, "deal_history", "text/csv");
            })
            .finally(() => {
                setIsExporting(false);
            });
    };

    return (
        <Box id="deal-history-tab-wrapper" display="flex" flexDirection="column" gap={2}>
            <PageInnerHeader
                id="deal-history-header"
                title={{text: "History"}}
                searchState={dealHistorySearchState}
                filterState={dealHistoryFilterState}
                filters={dealHistoryFilters.data}
                actions={{
                    primary: {
                        id: "export-deals",
                        children: (
                            <>
                                <DownloadOutlinedIcon htmlColor={theme.palette.blue[800]} />
                                {isExporting ? "Exporting..." : "Export CSV"}
                            </>
                        ),
                        variant: "text",
                        onClick: handleExportCSV,
                        disabled: isExporting,
                        permissions: [PERMISSION_GROUPS.DATA_EXPORT],
                    },
                }}
            />
            <TableV2Component
                id="deal-history-table"
                headerBackground={theme.palette.blue[100]}
                columns={columns as []}
                customData={dealHistoryData || []}
                isLoading={dealHistory.isLoading}
                orderBy={orderBy}
                sortType={sortType}
                onSortChange={handleSortChange}
                infiniteLoad={{
                    last_page: last_page,
                    page: dealHistoryPage,
                    isLoading: dealHistory.isLoading || dealHistory.isFetchingNextPage,
                }}
                onInfinitePageChange={() => {
                    if (dealHistory.hasNextPage) {
                        dealHistory.fetchNextPage();
                    }
                }}
            />
        </Box>
    );
};

export default DealHistoryTab;
