import {useMemo} from "react";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import Box from "../../../Atoms/Box/Box.component";
import CPTooltip from "../../../Atoms/Tooltip/tooltip.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {DEAL_STATUS, DEAL_STATUS_LABELS} from "../../../../constants/dealRegistrations.constant";
import useTheme from "@mui/material/styles/useTheme";
import {pluralizeString} from "../../../../utils/formatString.util";
import Skeleton from "@mui/material/Skeleton";

const DealRegistrationsStatusChartWidget = () => {
    const {activeCompany} = useActiveCompany();
    const {dealRegistrationsTotals} = useDealsRegistrations(activeCompany?.id, {
        calls: {
            all: false,
            dealRegistrationsTotals: true,
        },
    });
    const isLoading = dealRegistrationsTotals.isLoading;
    const theme = useTheme();
    const chartColors = {
        [DEAL_STATUS.INFO_REQUESTED]: theme.palette.blue[300],
        [DEAL_STATUS.PENDING]: theme.palette.warning[400],
        [DEAL_STATUS.APPROVED]: theme.palette.success[300],
        [DEAL_STATUS.DECLINED]: theme.palette.error[400],
    };
    const chartData = useMemo(() => {
        const data: Array<{name: string; value: number; color: string}> = [];
        const visibleStatuses = [
            DEAL_STATUS.INFO_REQUESTED,
            DEAL_STATUS.PENDING,
            DEAL_STATUS.APPROVED,
            DEAL_STATUS.DECLINED,
        ];
        visibleStatuses.forEach(status => {
            const found = dealRegistrationsTotals.data?.data?.find(item => item.status === status);
            data.push({
                name: DEAL_STATUS_LABELS[status],
                value: found?.num_of_deals || 0,
                color: chartColors[status],
            });
        });
        return data;
    }, [dealRegistrationsTotals?.data]);
    const totalValue = dealRegistrationsTotals.data?.total_of_deals || 0;

    return (
        <Box
            display="flex"
            flexDirection="column"
            gap={0.5}
            sx={{
                "&:hover .status-row": {
                    opacity: 0.3,
                    transition: "all 0.3s ease-in-out",
                },
                "& .status-row:hover": {
                    opacity: 1,
                    transform: "scale(1.05)",
                },
            }}>
            {chartData.map(item => {
                const percentageVal = (item?.value / totalValue) * 100 ?? 0;
                const percentage = isNaN(percentageVal) ? 0 : percentageVal.toFixed(2);
                return (
                    <CPTooltip
                        key={item.name}
                        title={`${item.value} ${pluralizeString("Request", item.value)} ${item.name}`}>
                        <Box
                            display="flex"
                            alignItems="center"
                            justifyContent="flex-end"
                            gap={1}
                            className="status-row">
                            {isLoading ? (
                                <Skeleton width={200} height={18} />
                            ) : (
                                <Box
                                    display="flex"
                                    justifyContent="flex-end"
                                    flexGrow={1}
                                    sx={{
                                        width: {xs: 130, lg: 200},
                                    }}
                                    borderRadius={0.5}
                                    bgcolor={theme.palette.neutral[200]}>
                                    <Box
                                        sx={{
                                            height: 16,
                                            backgroundColor: item.color,
                                            borderRadius: "4px 0px 0px 4px",
                                            width: `${percentage}%`,
                                        }}
                                    />
                                </Box>
                            )}
                            <Typography
                                variant="body4"
                                alignSelf="flex-start"
                                width={{xs: "calc(100% - 150px)", lg: "calc(100% - 200px)"}}
                                whiteSpace="nowrap">
                                {item.name} -{" "}
                                <Typography variant="body4" fontWeight={600} whiteSpace="nowrap">
                                    {percentage}%
                                </Typography>
                            </Typography>
                        </Box>
                    </CPTooltip>
                );
            })}
        </Box>
    );
};

export default DealRegistrationsStatusChartWidget;
