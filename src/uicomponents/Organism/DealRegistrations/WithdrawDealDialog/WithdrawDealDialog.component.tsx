import {FormProvider, useForm} from "react-hook-form";
import {useState} from "react";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import useTheme from "@mui/material/styles/useTheme";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import ModalComponent from "../../../Molecules/Modal/Modal.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {IDealRegistration} from "../../../../Interfaces/dealRegistrations.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";

interface IProps {
    open: boolean;
    handleClose?: () => void;
    deal: IDealRegistration;
}

const WithdrawDealDialog = ({open, handleClose, deal}: IProps) => {
    const [saving, setSaving] = useState(false);
    const methods = useForm();
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {dealRegistrationWithdrawOptions, updateDealRegistrations} = useDealsRegistrations(activeCompany?.id, {
        calls: {
            all: false,
            dealRegistrationsWithdrawOptions: true,
        },
    });
    const reasonId = "withdraw_reason";
    const reason = methods.watch(reasonId);
    const showDescriptionOption = dealRegistrationWithdrawOptions.data?.find(o => o.id === reason)?.show_answer_option;

    const handleSuccess = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        setSaving(true);
        const values = methods.getValues();
        updateDealRegistrations.mutate({
            mutate_type: MutateTypes.WithdrawDeal,
            id: deal?.id,
            body: {
                id: deal?.id,
                reason: values.withdraw_reason,
                reason_description: values.description || "",
            },
            company_id: activeCompany?.id,
            mutate_is_updating_append: deal?.id,
            onSuccess: () => {
                setSaving(false);
                handleClose?.();
            },
            onError: () => {
                setSaving(false);
            },
        });
    };

    return (
        <ErrorBoundary>
            <ModalComponent
                open={!!open}
                onClose={handleClose}
                onSuccess={handleSuccess}
                title="Withdraw Deal?"
                okButtonText="Withdraw"
                okBtnProps={{
                    color: "error",
                }}
                additionalBtnProps={{
                    variant: "contained",
                }}
                cancelButtonText="Cancel"
                loading={saving}
                modalTheme="error"
                justifyButtons="flex-start"
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "600px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
                }}
                shouldCloseOnClickOutside={false}
                content={
                    <FormProvider {...methods}>
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Typography fontInter variant="body3" color={theme.palette.neutral[700]} width="100%">
                                Are you sure you want to withdraw <strong>{deal.name}</strong> with{" "}
                                <strong>{deal.company?.name}</strong>? Please provide a reason for withdrawing to
                                proceed.
                            </Typography>
                            <AutoCompleteComponent
                                id="withdraw_reason"
                                label="Reason"
                                placeholder="Enter withdraw reason"
                                options={
                                    dealRegistrationWithdrawOptions.data?.map(item => ({
                                        id: item.id,
                                        label: item.name,
                                    })) as []
                                }
                                isRequired
                            />
                            {!!showDescriptionOption && (
                                <TextBoxComponent
                                    id="description"
                                    label="Description"
                                    placeholder="Briefly explain your decision"
                                    isRequired
                                    validateOnTheFly
                                    maxLength={255}
                                    showCharacterCount
                                />
                            )}
                        </Box>
                    </FormProvider>
                }
            />
        </ErrorBoundary>
    );
};

export default WithdrawDealDialog;
