import useTheme from "@mui/material/styles/useTheme";
import Box from "../../../Atoms/Box/Box.component";
import Drawer from "../../../Atoms/Drawer/Drawer.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import Stepper from "../../../Atoms/Stepper/Stepper.component";
import Step from "../../../Atoms/Step/Step.component";
import StepLabel from "../../../Atoms/StepLabel/StepLabel.component";
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import CreateEditDealStepOne from "../CreateEditDealSteps/CreateEditDealStepOne.component";
import {FormProvider, useForm} from "react-hook-form";
import Divider from "@mui/material/Divider";
import Button from "../../../Atoms/Button/Button.Component";
import CreateEditDealStepTwo from "../CreateEditDealSteps/CreateEditDealStepTwo.component";
import CreateEditDealStepThree from "../CreateEditDealSteps/CreateEditDealStepThree.component";
import CreateEditDealStepFour from "../CreateEditDealSteps/CreateEditDealStepFour.component";
import {
    IDealDocument,
    IDealRegistration,
    IStoreDealRegistration,
    IUpdateDealRegistration,
} from "../../../../Interfaces/dealRegistrations.interface";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {ISearchVendorItem} from "../../../Molecules/VendorSearch/vendorSearch.component";
import {IProductSearchItem} from "../../../Molecules/ProductSearch/productSearch.component";
import {DEAL_STATUS} from "../../../../constants/dealRegistrations.constant";
import DealRegistrationStatusBadge from "../../../Badges/dealRegistrationStatusBadge.component";
import {AxiosResponse} from "axios";
import useNotification from "../../../../hooks/useNotification";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import ResubmitDealDialog from "../ResubmitDealDialog/ResubmitDealDialog.component";
import {validateEmail} from "../../../../utils/validation.util";

interface IProps {
    open?: boolean;
    onClose?: () => void;
    mode?: "CREATE" | "EDIT";
    deal?: IDealRegistration;
    vendor?: ISearchVendorItem;
}
const FINAL_STEP = 4;
const STEP_LABELS = {
    1: (
        <>
            Product/Service
            <br /> Information
        </>
    ),
    2: (
        <>
            Deal <br /> Details
        </>
    ),
    3: (
        <>
            Company
            <br />
            Information
        </>
    ),
    4: (
        <>
            Additional
            <br /> Information
        </>
    ),
};

const CreateEditDealDrawer = ({open, onClose, mode = "CREATE", deal, vendor}: IProps) => {
    const isEditing = mode === "EDIT";
    const [step, setStep] = useState(1);
    const [isSaving, setIsSaving] = useState(false);
    const [showResubmitDialog, setShowResubmitDialog] = useState<boolean>(false);
    const [defaultAutocompleteOptions, setDefaultAutocompleteOptions] = useState<{
        product_id: IProductSearchItem[];
    }>({
        product_id: isEditing && deal?.product ? ([deal.product] as any[]) : [],
    });
    const {activeCompany} = useActiveCompany();
    const {updateDealRegistrations, isUpdating} = useDealsRegistrations(activeCompany?.id, {calls: {all: false}});
    const theme = useTheme();
    const notify = useNotification();
    const methods = useForm({
        shouldUnregister: false,
        defaultValues:
            mode === "CREATE"
                ? ({stakeholder: [{name: "", email: ""}]} as any)
                : {stakeholder: deal?.stakeholders || [{name: "", email: ""}]},
    });
    const drawerRef = useRef<HTMLDivElement>(null);
    const isLoading =
        isUpdating[MutateTypes.CreateDealRegistration] ||
        isUpdating[MutateTypes.UpdateDealRegistration] ||
        isUpdating[MutateTypes.UploadDealDocument] ||
        isSaving;
    const clickedSaveBtn = useRef<"SAVE" | "SAVE_DRAFT">("SAVE");
    const files = useRef<IDealDocument[]>([]);
    const isDraftDeal = isEditing && deal?.status === DEAL_STATUS.DRAFT;

    const handleFilesChange = useCallback((newFiles: IDealDocument[]) => {
        files.current = newFiles;
    }, []);

    const steps = useMemo(() => {
        return {
            1: (
                <CreateEditDealStepOne
                    defaultAutocompleteOptions={defaultAutocompleteOptions}
                    setDefaultAutocompleteOptions={setDefaultAutocompleteOptions}
                    isDraftDeal={isDraftDeal}
                    isEditing={isEditing}
                    defaultVendor={mode === "CREATE" && !!vendor ? vendor : undefined}
                />
            ),
            2: <CreateEditDealStepTwo mode={mode} />,
            3: <CreateEditDealStepThree mode={mode} />,
            4: (
                <CreateEditDealStepFour
                    onFilesChange={handleFilesChange}
                    deal={deal}
                    isEditing={mode === "EDIT"}
                    defaultFiles={files.current}
                />
            ),
        };
    }, [defaultAutocompleteOptions, files.current]);

    const handleSubmit = async (reason_description?: string) => {
        const isDraft = clickedSaveBtn.current === "SAVE_DRAFT";
        const passedValidation = isDraft
            ? await methods.trigger(["deal_name", "company_id", "product_id"])
            : await methods.trigger();
        if (!passedValidation) return;
        if (
            !reason_description &&
            mode === "EDIT" &&
            !methods.formState.isDirty &&
            deal?.status === DEAL_STATUS.INFO_REQUESTED
        ) {
            setShowResubmitDialog(true);
            return;
        }
        setIsSaving(true);
        const values = isDraft
            ? Object.entries(methods.getValues())
                  .filter(([key]) => {
                      return !methods.getFieldState(key)?.invalid;
                  })
                  .reduce((acc, [key, value]) => {
                      acc[key] = value;
                      return acc;
                  }, {})
            : methods.getValues();
        const body: IStoreDealRegistration = {
            company_id: values.company_id,
            product_id: values.product_id,
            name: values.deal_name,
            scope: values.scope,
            size: values.size,
            status: isDraft ? DEAL_STATUS.DRAFT : DEAL_STATUS.PENDING,
            expiration_days: values.deal_request_expiration,
            notes: values.notes,
            description: values.description,
            company_details: {
                name: values.partner_company_name || activeCompany?.name,
                address: values.partner_company_address,
                manager_name: values.partner_account_manager,
                manager_email: values.partner_account_email,
                phone: values.partner_account_phone,
                contact_by: values.preferred_contact_method,
            },
            stakeholders: values.stakeholder.filter(s => s.name && validateEmail(s.email)),
        };
        if (mode === "CREATE") {
            updateDealRegistrations.mutate({
                mutate_type: MutateTypes.CreateDealRegistration,
                company_id: activeCompany?.id,
                body,
                onSuccess: async createResponse => {
                    const dealId = createResponse?.data?.id;
                    if (dealId && files.current.length > 0) {
                        const createPromises = files.current.map(file => {
                            return new Promise<AxiosResponse>((resolve, reject) => {
                                updateDealRegistrations.mutate({
                                    mutate_type: MutateTypes.CreateDealDocument,
                                    company_id: activeCompany?.id,
                                    id: dealId,
                                    name: file.name,
                                    onSuccess: res => resolve(res),
                                    onError: err => reject(err),
                                });
                            });
                        });
                        await Promise.all(createPromises).then(async responses => {
                            const uploadPromises = responses.map((res, index) => {
                                const documentName = res.data?.name;
                                const documentId = res.data?.id;
                                return new Promise((resolve, reject) =>
                                    updateDealRegistrations.mutate({
                                        mutate_type: MutateTypes.UploadDealDocument,
                                        id: dealId,
                                        doc_id: documentId,
                                        company_id: activeCompany?.id,
                                        name: documentName,
                                        file: files.current?.[index]?.file,
                                        onSuccess: res => resolve(res),
                                        onError: err => reject(err),
                                        progressCallback: (progress: number) => {
                                            // TODO: Add a way to track progress
                                        },
                                    }),
                                );
                            });
                            await Promise.all(uploadPromises);
                        });
                    }
                    setIsSaving(false);
                    notify("Deal successfully created", "Success");
                    handleClose();
                },
                onError: () => {
                    setIsSaving(false);
                },
            });
        } else if (mode === "EDIT") {
            const updateBody: IUpdateDealRegistration = {...body, id: deal?.id || ""};
            if (reason_description) {
                updateBody["reason_description"] = reason_description;
            }
            updateDealRegistrations.mutate({
                mutate_type: MutateTypes.UpdateDealRegistration,
                company_id: activeCompany?.id,
                body: updateBody,
                onSuccess: () => {
                    notify("Deal successfully updated", "Success");
                    setIsSaving(false);
                    handleClose();
                },
                onError: () => {
                    setIsSaving(false);
                },
            });
        }
    };

    const handleClose = () => {
        methods.reset();
        files.current = [];
        onClose && onClose();
    };

    const scrollToTop = () => {
        if (!drawerRef.current) return;
        const paper = drawerRef.current.querySelector(".MuiPaper-root");
        if (paper) {
            setTimeout(() => {
                paper.scrollTop = 0;
            }, 500);
        }
    };

    const formFields = {
        'form-deal-1':['company_id', 'deal_name', 'product_id'],
        'form-deal-2':['description', 'size', 'stakeholder'],
        'form-deal-3':['partner_account_manager', 'partner_company_address', 'preferred_contact_method'],
    }

    const handleNext = async () => {
        const passedValidation = step <= 3 ? await methods.trigger(formFields[`form-deal-${step}`]) : await methods.trigger();
        if (!passedValidation) return;
        setStep(step + 1);
        scrollToTop();
    };

    const handleBack = () => {
        setStep(step - 1);
        scrollToTop();
    };

    useEffect(() => {
        vendor?.id && methods.setValue("company_id", vendor.id);
    }, [vendor]);

    useEffect(() => {
        if (mode === "EDIT" && !!deal) {
            methods.reset({
                company_id: deal?.company?.id || null,
                product_id: deal.product?.id || "",
                scope: deal.scope || "",
                notes: deal.notes || "",
                deal_name: deal.name,
                size: deal.size || "",
                size_description: deal.size_description || "",
                partnership_status: deal.partnership_status || "",
                description: deal.description || "",

                partner_company_name: deal.company_details?.name || "",
                partner_company_address: deal.company_details?.address || "",
                partner_account_manager: deal.company_details?.manager_name || "",
                partner_account_email: deal.company_details?.manager_email || "",
                partner_account_phone: deal.company_details?.phone || "",
                preferred_contact_method: deal.company_details?.contact_by || "",
                deal_request_expiration: deal.expiration_date || "",
                submitter_name: deal.submitter?.name || "",
                submitter_email: deal.submitter?.email || "",
                submitter_phone: deal.submitter?.phone || "",
                submitter_preferred_contact_method: deal.submitter?.contact_by || "",
                date: deal.created_at || "",

                stakeholder: deal.stakeholders || [],
            });
        }
    }, [deal, mode]);

    return (
        <ErrorBoundary>
            <Drawer
                open={open}
                onClose={handleClose}
                anchor="right"
                sx={{
                    ".MuiDrawer-paper": {
                        maxWidth: {xs: "100vw", lg: "50vw"},
                        width: {xs: "100vw", lg: "50vw"},
                        padding: 2,
                    },
                }}
                ref={drawerRef}>
                {mode === "EDIT" ? (
                    <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="flex-start"
                        paddingBottom={2}
                        borderBottom={`1px solid ${theme.palette.neutral[500]}`}
                        width="100%"
                        gap={1}>
                        <Typography
                            fontInter
                            variant="body2"
                            fontSize="22px !important"
                            color={theme.palette.neutral[800]}>
                            Edit Deal Registration
                        </Typography>
                        <Box display="flex" flexDirection="row" gap={2}>
                            <Typography variant="body3" color={theme.palette.neutral[700]} fontWeight={600}>
                                {deal?.product?.name}
                            </Typography>
                            <DealRegistrationStatusBadge status={deal?.status} />
                        </Box>
                    </Box>
                ) : (
                    <Box
                        display="flex"
                        flexDirection="row"
                        alignItems="center"
                        paddingBottom={2}
                        borderBottom={`1px solid ${theme.palette.neutral[500]}`}
                        width="100%"
                        gap={1}>
                        <Typography
                            fontInter
                            variant="body2"
                            fontSize="20px !important"
                            color={theme.palette.neutral[800]}>
                            Send Deal Request
                        </Typography>
                    </Box>
                )}
                <Box
                    display="flex"
                    flexDirection="column"
                    padding={1}
                    alignItems="center"
                    justifySelf="stretch"
                    borderRadius={1}
                    marginTop={2}
                    gap={2}
                    flexGrow={1}>
                    <Stepper
                        activeStep={step - 1}
                        scrollStepsOnChange
                        sx={{
                            width: "100%",
                            paddingY: 2,
                            overflowX: "auto",
                        }}>
                        {Array(FINAL_STEP)
                            .fill(null)
                            .map((_, i) => (
                                <Step key={i}>
                                    <StepLabel sx={{maxWidth: "fit-content"}}>{STEP_LABELS[i + 1]}</StepLabel>
                                </Step>
                            ))}
                    </Stepper>
                    <FormProvider {...methods}>
                        <form
                            style={{display: "flex", flexDirection: "column", gap: "16px", width: "100%"}}
                            onSubmit={e => {
                                e.preventDefault();
                                e.stopPropagation();
                            }}>
                            {steps[step]}
                        </form>
                    </FormProvider>
                </Box>
                <Box sx={{width: "100%", paddingTop: 2, backgroundColor: "#fff"}}>
                    <Divider sx={{marginY: 2}} />
                    <Box width="100%" display="flex" flexWrap="wrap" flexDirection="row" gap={1}>
                        {step > 1 && (
                            <Button
                                id="prev-step"
                                variant="outlined"
                                color="blue"
                                onClick={handleBack}
                                disabled={isLoading}>
                                PREVIOUS
                            </Button>
                        )}
                        <Button
                            id="next-step"
                            variant="contained"
                            color="blue"
                            onClick={() => {
                                clickedSaveBtn.current = "SAVE";
                                step === FINAL_STEP ? handleSubmit() : handleNext();
                            }}
                            loading={clickedSaveBtn.current === "SAVE" && isLoading}
                            disabled={isLoading}>
                            {step === FINAL_STEP
                                ? mode === "CREATE" || deal?.status === DEAL_STATUS.DRAFT
                                    ? "SAVE AND SEND"
                                    : "RESUBMIT"
                                : "NEXT"}
                        </Button>
                        {(mode === "CREATE" || (mode === "EDIT" && step === FINAL_STEP)) &&
                            deal?.status !== DEAL_STATUS.PENDING && (
                                <Button
                                    id="save-as-draft"
                                    variant="outlined"
                                    color="blue"
                                    loading={clickedSaveBtn.current === "SAVE_DRAFT" && isLoading}
                                    disabled={isLoading}
                                    onClick={() => {
                                        clickedSaveBtn.current = "SAVE_DRAFT";
                                        handleSubmit();
                                    }}>
                                    SAVE AS DRAFT
                                </Button>
                            )}
                        <Button id="close" variant="text" color="blue" onClick={handleClose} disabled={isLoading}>
                            CANCEL
                        </Button>
                    </Box>
                </Box>
            </Drawer>
            {showResubmitDialog && (
                <ResubmitDealDialog
                    open
                    handleClose={() => setShowResubmitDialog(false)}
                    onSuccess={reason_description => {
                        handleSubmit(reason_description);
                    }}
                    isLoading={isSaving}
                />
            )}
        </ErrorBoundary>
    );
};

export default CreateEditDealDrawer;
