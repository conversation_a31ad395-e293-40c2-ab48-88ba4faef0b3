import useTheme from "@mui/material/styles/useTheme";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import CPTooltip from "../../../Atoms/Tooltip/tooltip.component";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import {DATE_FULL_SHORT_MONTH, formatDate} from "../../../../utils/formatDate";
import {pluralizeString} from "../../../../utils/formatString.util";

const SentDealRegistrationsCountWidget = () => {
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {dealRegistrationsTotals} = useDealsRegistrations(activeCompany?.id, {
        calls: {
            all: false,
            dealRegistrationsTotals: true,
        },
    });
    const total_of_deals = dealRegistrationsTotals.data?.total_of_deals || 0;

    return (
        <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent={{xs: "flex-start", md: "flex-start"}}
            gap={2}>
            <Typography fontInter variant="h4" fontWeight={"600 !important"}>
                {total_of_deals || 0}
            </Typography>
            <Box display="flex" flexDirection="column">
                <Typography variant="body3" color={theme.palette.neutral[700]} lineHeight={1.2}>
                    {activeCompany?.type_is_of_vendor ? "Received " : "Sent "} Deal <br />{" "}
                    {pluralizeString("Registration", total_of_deals)}
                </Typography>
            </Box>
            <CPTooltip
                title={`Total deal registrations ${
                    activeCompany?.type_is_of_vendor ? "received " : "sent "
                } (as of ${formatDate(new Date().toISOString(), DATE_FULL_SHORT_MONTH)}): ${total_of_deals || 0}`}>
                <InfoOutlinedIcon htmlColor={theme.palette.neutral[500]} />
            </CPTooltip>
        </Box>
    );
};

export default SentDealRegistrationsCountWidget;
