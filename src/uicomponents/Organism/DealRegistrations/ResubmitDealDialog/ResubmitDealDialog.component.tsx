import {FormProvider, useForm} from "react-hook-form";
import useTheme from "@mui/material/styles/useTheme";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import ModalComponent from "../../../Molecules/Modal/Modal.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import SendIcon from "@mui/icons-material/Send";

interface IProps {
    open: boolean;
    handleClose?: () => void;
    onSuccess?: (reason_description: string) => void;
    isLoading?: boolean;
}

const ResubmitDealDialog = ({open, handleClose, onSuccess, isLoading}: IProps) => {
    const methods = useForm();
    const theme = useTheme();

    const handleSuccess = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const {reason_description} = methods.getValues();
        onSuccess?.(reason_description);
    };

    return (
        <ErrorBoundary>
            <ModalComponent
                open={!!open}
                onClose={handleClose}
                onSuccess={handleSuccess}
                loading={isLoading}
                title="Resubmit without making any changes?"
                okButtonText="RESUBMIT"
                okButtonStartIcon={<SendIcon />}
                cancelButtonText="Cancel"
                modalTheme="blue"
                justifyButtons="flex-start"
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "600px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
                }}
                shouldCloseOnClickOutside={false}
                content={
                    <FormProvider {...methods}>
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Typography fontInter variant="body3" color={theme.palette.neutral[700]} width="100%">
                                Are you sure you want to resubmit this deal without making any changes? Your reason will
                                be shared with the vendor.
                            </Typography>
                            <TextBoxComponent
                                id="reason_description"
                                label="Reason"
                                placeholder="Please provide a reason for resubmitting without changes"
                                multiline
                                minRows={4}
                                maxLength={255}
                                showCharacterCount
                                isRequired
                                validateOnTheFly
                            />
                        </Box>
                    </FormProvider>
                }
            />
        </ErrorBoundary>
    );
};

export default ResubmitDealDialog;
