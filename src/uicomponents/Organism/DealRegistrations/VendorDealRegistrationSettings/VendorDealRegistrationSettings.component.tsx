import Skeleton from "@mui/material/Skeleton";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useRef} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {Navigate} from "react-router-dom";
import {DEAL_EXPIRATION_OPTIONS} from "../../../../constants/dealRegistrations.constant";
import routeConfig from "../../../../constants/routeConfig";
import {useCompany} from "../../../../hooks/fetches/useCompany";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {validateEmail} from "../../../../utils/validation.util";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import CompanyEmailSearch, {
    ICompanyEmailSearchOption,
} from "../../../Molecules/CompanyEmailSearch/CompanyEmailSearch.component";

const VendorDealRegistrationSettings = () => {
    const theme = useTheme();
    const methods = useForm();
    const {activeCompany} = useActiveCompany();
    const {companyDealContacts, updateDealRegistrations, isUpdating} = useDealsRegistrations(activeCompany?.id || "", {
        calls: {
            all: false,
            companyDealContacts: true,
        },
        isVendor: true,
    });
    const {
        companyConfigs,
        updateCompany,
        isUpdating: isUpdatingCompany,
    } = useCompany(activeCompany?.friendly_url || "", {
        isCompany: !!activeCompany?.type_is_of_vendor,
        calls: {
            all: false,
            company: true,
            companyConfigs: true,
        },
    });
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_DEALS_UPDATE]);
    const dealExpirationWatcher = methods.watch("deal_expiration");
    const prevRecipients = useRef<ICompanyEmailSearchOption[]>([]);
    const isUpdatingRecipients =
        isUpdating[MutateTypes.CreateDealContacts] || isUpdating[MutateTypes.DeleteDealContacts];
    const isUpdatingDealExpiration = isUpdatingCompany[MutateTypes.UpdateCompanyConfigs];

    const handleRecipientsChange = (id: string, value, option) => {
        if (!value) return;
        const isArrayOfOptions = Array.isArray(option);
        const isRemoving = !!prevRecipients.current?.find(o => (typeof o === "string" ? false : o.id === option.id));
        prevRecipients.current = methods.getValues("recipients") || [];
        if (isRemoving) {
            updateDealRegistrations.mutate({
                mutate_type: MutateTypes.DeleteDealContacts,
                body: [option.email],
                onSuccess: () => {
                    companyDealContacts.refetch();
                },
            });
            return;
        }
        const contactsToAdd: Array<{user_id?: string; email: string}> = [];
        if (isArrayOfOptions) {
            option.forEach(o => {
                const contactToAdd = {email: typeof o === "string" ? o : o.email};
                if (o?.id) {
                    contactToAdd["user_id"] = o.id;
                }
                contactsToAdd.push(contactToAdd);
            });
        } else {
            const contactToAdd = {email: option.email};
            if (option.id) {
                contactToAdd["user_id"] = option.id;
            }
            contactsToAdd.push(contactToAdd);
        }
        updateDealRegistrations.mutate({
            mutate_type: MutateTypes.CreateDealContacts,
            body: contactsToAdd,
            onSuccess: () => {
                companyDealContacts.refetch();
            },
        });
    };

    const handleClearRecipients = () => {
        updateDealRegistrations.mutate({
            mutate_type: MutateTypes.DeleteDealContacts,
            body: companyDealContacts?.data?.map(item => item.email),
            onSuccess: () => {
                companyDealContacts.refetch();
            },
        });
    };

    const handleDealExpirationChange = (value: string | null) => {
        if (
            (value || null) !==
            (companyConfigs.data?.deal_expiration_days
                ? String(companyConfigs?.data?.deal_expiration_days)
                : companyConfigs.data?.deal_expiration_days || null)
        ) {
            updateCompany.mutate({
                mutate_type: MutateTypes.UpdateCompanyConfigs,
                company_id: activeCompany?.id,
                body: {deal_expiration_days: value ? Number(value) : null},
            });
        }
    };

    useEffect(() => {
        if (!!companyConfigs.data) {
            const stringValue = String(companyConfigs.data.deal_expiration_days);
            const finalValue = stringValue || "null";
            methods.setValue("deal_expiration", finalValue);
        }
    }, [companyConfigs.data?.deal_expiration_days]);

    useEffect(() => {
        if (companyDealContacts?.data) {
            const newOptions = companyDealContacts?.data?.map(item => ({
                id: item.id,
                label: item.name,
                email: item.email,
            }));
            prevRecipients.current = newOptions;
            methods.setValue("recipients", newOptions);
        }
    }, [companyDealContacts.data]);

    if (activeCompany?.id && !activeCompany?.type_is_of_vendor) return <Navigate to={routeConfig.Home.path} />;
    return (
        <BorderedBox sx={{display: "flex", flexDirection: "column"}}>
            <Box width="100%" borderBottom={`1px solid ${theme.palette.neutral[300]}`}>
                <Typography fontInter variant="body1" fontSize={23} color={theme.palette.neutral[700]}>
                    Deal Registration Settings
                </Typography>
            </Box>
            <FormProvider {...methods}>
                <Box component="form" sx={{display: "flex", flexDirection: {xs: "column", md: "row"}, gap: 2}}>
                    {companyDealContacts?.isLoading ? (
                        <>
                            <Skeleton width={326} height={48} />
                            <Skeleton width="100%" height={48} />
                        </>
                    ) : (
                        <>
                            <AutoCompleteComponent
                                id="deal_expiration"
                                label="Deal Requests Expiration"
                                placeholder="Select Deal Request Lifetime"
                                options={DEAL_EXPIRATION_OPTIONS}
                                isOptionEqualToValue={(option, value) => {
                                    const v = typeof value === "string" ? value : value?.id;
                                    return option?.id === v || option?.label === v;
                                }}
                                sx={{
                                    minWidth: {xs: "100%", md: 326},
                                }}
                                disabled={isUpdatingDealExpiration || companyConfigs.isLoading}
                                onChange={(_, value) => {
                                    const valueId = value ? (value as {id: string; label: string}).id : null;
                                    handleDealExpirationChange(valueId === "null" ? null : valueId);
                                }}
                                key={dealExpirationWatcher}
                                readOnly={!hasEditAccess}
                                helperText="This can be overwritten individually, but will follow the above value as a default."
                            />
                            <CompanyEmailSearch
                                sx={{
                                    width: "100%",
                                }}
                                id="recipients"
                                label="Notification Recipients"
                                placeholder="Type to select a user or add an email"
                                validateOption={v => validateEmail(v)}
                                helperText="Select a user from your company, and/or input the emails separated by commas. If no email recipient is selected or inputted, no email notifications will be received."
                                onValueChange={handleRecipientsChange}
                                onClear={handleClearRecipients}
                                disabled={isUpdatingRecipients || companyDealContacts.isFetching}
                                readOnly={!hasEditAccess}
                                hideEmailForCompanyEmail
                                limitTagsToSingleLine
                                key={String(isUpdatingRecipients)}
                            />
                        </>
                    )}
                </Box>
            </FormProvider>
        </BorderedBox>
    );
};

export default VendorDealRegistrationSettings;
