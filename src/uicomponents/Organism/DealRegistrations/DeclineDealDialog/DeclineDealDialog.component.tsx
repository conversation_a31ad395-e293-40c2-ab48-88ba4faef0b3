import {FormProvider, useForm} from "react-hook-form";
import {useDealsRegistrations} from "../../../../hooks/fetches/useDealsRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import useTheme from "@mui/material/styles/useTheme";
import AutoCompleteComponent from "../../../Molecules/Autocomplete/Autocomplete.component";
import ErrorBoundary from "../../../../utils/errorBoundary.util";
import ModalComponent from "../../../Molecules/Modal/Modal.component";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import {IDealRegistration} from "../../../../Interfaces/dealRegistrations.interface";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";

interface IProps {
    open: boolean;
    handleClose?: () => void;
    deal: IDealRegistration;
}

const DeclineDealDialog = ({open, handleClose, deal}: IProps) => {
    const methods = useForm();
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const {dealRegistrationDeclineOptions, isUpdating, updateDealRegistrations} = useDealsRegistrations(
        activeCompany?.id,
        {
            calls: {
                all: false,
                dealRegistrationsDeclineOptions: true,
            },
        },
    );
    const reasonId = "reason";
    const reason = methods.watch(reasonId);
    const showDescriptionOption = dealRegistrationDeclineOptions.data?.find(o => o.id === reason)?.show_answer_option;
    const isDeclining = isUpdating[MutateTypes.DeclineDeal + deal.id];

    const handleSuccess = async () => {
        const passedValidation = await methods.trigger();
        if (!passedValidation) return;
        const {reason, description} = methods.getValues();
        updateDealRegistrations.mutate({
            mutate_type: MutateTypes.DeclineDeal,
            body: {
                id: deal.id,
                reason,
                reason_description: description,
            },
            mutate_is_updating_append: deal.id,
            onSuccess: () => {
                handleClose?.();
            },
        });
    };

    return (
        <ErrorBoundary>
            <ModalComponent
                open={!!open}
                onClose={handleClose}
                onSuccess={handleSuccess}
                title="Decline Deal?"
                okButtonText="DECLINE"
                okBtnProps={{
                    color: "error",
                }}
                additionalBtnProps={{
                    variant: "contained",
                }}
                cancelButtonText="Cancel"
                loading={isDeclining}
                modalTheme="error"
                justifyButtons="flex-start"
                dialogSx={{
                    ".MuiDialog-paper": {overflow: "unset", maxWidth: "600px !important", width: "100%"},
                    ".MuiDialogContent-root": {overflowY: "auto", overflowX: "hidden"},
                }}
                shouldCloseOnClickOutside={false}
                content={
                    <FormProvider {...methods}>
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Typography fontInter variant="body3" color={theme.palette.neutral[700]} width="100%">
                                Are you sure you want to decline <strong>{deal.name}</strong> from{" "}
                                <strong>{deal.company_details?.name}</strong>? Please provide a reason for declining to
                                proceed.
                            </Typography>
                            <AutoCompleteComponent
                                id={reasonId}
                                label="Reason"
                                placeholder="Select a reason"
                                options={
                                    dealRegistrationDeclineOptions.data?.map(item => ({
                                        id: item.id,
                                        label: item.name,
                                        show_answer_option: item.show_answer_option,
                                    })) as []
                                }
                                isRequired
                            />
                            {!!showDescriptionOption && (
                                <TextBoxComponent
                                    id="description"
                                    label="Description"
                                    placeholder="Tell us about your decision"
                                    multiline
                                    minRows={4}
                                    rows={4}
                                    isRequired
                                    validateOnTheFly
                                    maxLength={50}
                                    showCharacterCount
                                />
                            )}
                        </Box>
                    </FormProvider>
                }
            />
        </ErrorBoundary>
    );
};

export default DeclineDealDialog;
