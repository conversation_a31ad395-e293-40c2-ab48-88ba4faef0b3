import {useFieldArray, useFormContext} from "react-hook-form";
import Box from "../../../Atoms/Box/Box.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import useTheme from "@mui/material/styles/useTheme";
import {useEffect, useRef} from "react";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import useMediaQuery from "@mui/material/useMediaQuery";
import {validateEmail} from "../../../../utils/validation.util";
import {VERIFICATION_INVALID_EMAIL_ERROR} from "../../../../constants/errorMessages.constant";

const CreateEditDealStepTwo = ({mode}: {mode: "CREATE" | "EDIT"}) => {
    const {getValues, formState} = useFormContext();
    const {fields, append, remove} = useFieldArray({
        name: "stakeholder",
        keyName: mode === "EDIT" ? "id" : undefined,
    });
    const theme = useTheme();
    const isMdOrSmaller = useMediaQuery(theme.breakpoints.down("md"));
    const hasAppended = useRef<boolean>(false);

    useEffect(() => {
        if (fields.length === 0 && !hasAppended.current) {
            hasAppended.current = true;
            append({name: "", email: ""});
        }
    }, [fields.length, append]);

    return (
        <Box display="flex" flexDirection="column" width="100%" gap={2}>
            <Box display="grid" gridTemplateColumns={{xs: "1fr"}} gap={2}>
                <TextBoxComponent
                    id="size"
                    label="Expected Deal Size"
                    placeholder="Enter expected Monetary Value"
                    isRequired
                    keepRegistered
                    validateOnTheFly
                    type="number"
                    allowDecimals
                />
            </Box>
            <TextBoxComponent
                id="description"
                label="Description of opportunity"
                placeholder="Enter the deal information with as many details as possible"
                multiline
                rows={5}
                maxRows={10}
                isRequired
                keepRegistered
                validateOnTheFly
            />
            <Typography fontInter variant="body2" fontWeight={500}>
                Key Stakeholders Involved
            </Typography>
            <Box
                display="flex"
                flexDirection="column"
                gap={2}
                maxHeight={{xs: "none", md: 380}}
                sx={{overflowY: "auto"}}>
                {fields.map((field, index) => {
                    const emailFieldId = `stakeholder.${index}.email`;
                    const nameFieldId = `stakeholder.${index}.name`;
                    const email = getValues(emailFieldId);
                    const name = getValues(nameFieldId);
                    const isFilled = !!email && validateEmail(email || "") && !!name;
                    const showAddButton = index === fields.length - 1 && isFilled;
                    const EmailAndBtns = (
                        <>
                            <TextBoxComponent
                                id={emailFieldId}
                                label="Stakeholder Email"
                                placeholder="Enter email to receive updates"
                                isRequired
                                keepRegistered
                                validateOnTheFly
                                validationSchema={v => validateEmail(String(v) || "")}
                                tooltipHelperText={VERIFICATION_INVALID_EMAIL_ERROR}
                                isInvalid={!!formState.errors?.stakeholder?.[index]?.email}
                            />
                            <Box display="flex" alignItems="center" gap={1}>
                                {showAddButton && (
                                    <Box
                                        sx={{cursor: "pointer"}}
                                        title="Add Stakeholder"
                                        onClick={() => {
                                            append({email: "", name: ""});
                                        }}>
                                        <AddCircleOutlineOutlinedIcon htmlColor={theme.palette.blue[600]} />
                                    </Box>
                                )}
                                {fields.length > 1 && (
                                    <Box
                                        sx={{cursor: "pointer"}}
                                        title="Remove Stakeholder"
                                        onClick={() => {
                                            remove(index);
                                        }}>
                                        <DeleteOutlinedIcon htmlColor={theme.palette.error[600]} />
                                    </Box>
                                )}
                            </Box>
                        </>
                    );
                    return (
                        <BorderedBox
                            sx={{
                                padding: 1,
                                display: "grid",
                                gridTemplateColumns: {xs: "1fr", md: showAddButton ? "1fr 1fr 54px" : "1fr 1fr 24px"},
                                alignItems: "center",
                                gap: 1,
                            }}
                            key={field.id}>
                            <TextBoxComponent
                                id={nameFieldId}
                                label="Stakeholder Name"
                                placeholder="Enter Stakeholder Name"
                                isRequired
                                keepRegistered
                                validateOnTheFly
                                isInvalid={!!formState.errors?.stakeholder?.[index]?.name}
                            />
                            {isMdOrSmaller ? (
                                <Box display="grid" gridTemplateColumns={showAddButton ? "1fr 54px" : "1fr 24px"}>
                                    {EmailAndBtns}
                                </Box>
                            ) : (
                                EmailAndBtns
                            )}
                        </BorderedBox>
                    );
                })}
            </Box>
        </Box>
    );
};

export default CreateEditDealStepTwo;
