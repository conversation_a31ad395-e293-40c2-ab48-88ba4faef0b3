import useTheme from "@mui/material/styles/useTheme";
import {MediaTypes} from "../../../../enums/mediaTypes.enum";
import BorderedBox from "../../../Atoms/Box/BorderedBox.component";
import Box from "../../../Atoms/Box/Box.component";
import TextBoxComponent from "../../../FormControls/TextBox/textBox.component";
import MuiUpload from "../../../Molecules/MuiUpload/MuiUpload.component";
import Typography from "../../../Atoms/Typography/Typography.component";
import Skeleton from "@mui/material/Skeleton";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import FileCopyOutlinedIcon from "@mui/icons-material/FileCopyOutlined";
import LaunchOutlinedIcon from "@mui/icons-material/LaunchOutlined";
import {useCallback, useEffect, useState} from "react";
import {useFormContext} from "react-hook-form";
import {mimeToExtension} from "../../../../utils/downloadDoc.util";
import Button from "../../../Atoms/Button/Button.Component";
import {IDealDocument, IDealRegistration} from "../../../../Interfaces/dealRegistrations.interface";
import {useDealRegistration} from "../../../../hooks/fetches/useDealRegistrations";
import useActiveCompany from "../../../../hooks/useActiveCompany";
import {MutateTypes} from "../../../../Interfaces/queries.interface";
import {AxiosResponse} from "axios";
import Loader from "../../../../utils/loader";
import {generateUID} from "../../../../utils/UID.util";
import usePermissions from "../../../../hooks/usePermissions";
import {PERMISSION_GROUPS} from "../../../../Interfaces/features/features.interface";
import {DEAL_STATUS} from "../../../../constants/dealRegistrations.constant";
import getUserConfirmation from "../../../../utils/confirmationDialog.util";

interface IProps {
    isEditing?: boolean;
    isReadOnly?: boolean;
    deal?: IDealRegistration;
    onFilesChange?: (files: IDealDocument[]) => void;
    defaultFiles?: IDealDocument[]; //? Used for defaulting the files array
}

const CreateEditDealStepFour = ({isEditing = false, isReadOnly = false, onFilesChange, deal, defaultFiles}: IProps) => {
    const [files, setFiles] = useState<IDealDocument[]>(defaultFiles || []);
    const [isEditingFile, setIsEditingFile] = useState<any>({});
    const [fileUploadProgress, setFileUploadProgress] = useState<Record<string, number>>({});
    const {setValue, watch, trigger} = useFormContext();
    const theme = useTheme();
    const {activeCompany} = useActiveCompany();
    const isViewingAsVendor = activeCompany?.type_is_of_vendor;
    const {dealDocuments, updateDealRegistration, isUpdating} = useDealRegistration(
        activeCompany?.id || "",
        deal?.id || "",
        {
            calls: {
                all: false,
                dealDocuments: true,
            },
        },
    );
    const {hasPermissions} = usePermissions();
    const hasEditAccess = hasPermissions([PERMISSION_GROUPS.MANAGE_DEALS_UPDATE]);
    const hasExportAccess = hasPermissions([PERMISSION_GROUPS.DATA_EXPORT]);
    const disableUpdates =
        isEditing && (deal?.status === DEAL_STATUS.DECLINED || deal?.status === DEAL_STATUS.WITHDRAWN);
    const isLoading = deal?.id && dealDocuments.isLoading;

    const handleFileUploadProgress = useCallback(
        (progress, fileId) => {
            setFileUploadProgress({...fileUploadProgress, [fileId]: progress});
        },
        [fileUploadProgress],
    );

    const handleUploadNewFile = async (selectedFile: File | File[]) => {
        if (isEditing) {
            const isArrayOfFiles = Array.isArray(selectedFile);
            const uploadedFiles = isArrayOfFiles ? selectedFile : [selectedFile];
            const tempIdOrIds: string | string[] = isArrayOfFiles
                ? Array(selectedFile.length)
                      .fill(null)
                      .map(() => "uploading_" + generateUID())
                : "uploading_" + generateUID();
            setFiles(prev => [
                ...prev,
                ...(isArrayOfFiles
                    ? selectedFile.map((f, index) => ({
                          id: tempIdOrIds[index], // Assuming tempIdOrIds is an array of IDs matching selectedFile
                          file: f,
                          name: f.name,
                      }))
                    : [
                          {
                              id: tempIdOrIds as string,
                              file: selectedFile,
                              name: selectedFile.name,
                          },
                      ]),
            ]);

            const createPromises = uploadedFiles.map(file => {
                return new Promise<AxiosResponse>(resolve => {
                    updateDealRegistration.mutate({
                        mutate_type: MutateTypes.CreateDealDocument,
                        company_id: activeCompany?.id,
                        id: deal?.id,
                        name: file.name,
                        onSuccess: res => resolve(res),
                    });
                });
            });
            await Promise.all(createPromises).then(async responses => {
                const uploadPromises = responses.map((res, index) => {
                    const documentId = res?.data?.id;
                    return new Promise(resolve =>
                        updateDealRegistration.mutate({
                            mutate_type: MutateTypes.UploadDealDocument,
                            id: deal?.id,
                            doc_id: documentId,
                            company_id: activeCompany?.id,
                            name: res?.data?.name,
                            file: uploadedFiles[index],
                            onSuccess: res => resolve(res),
                            progressCallback: (progress: number) => {
                                handleFileUploadProgress(
                                    progress,
                                    documentId,
                                    // isArrayOfFiles ? tempIdOrIds[index] : (tempIdOrIds as string),
                                );
                            },
                        }),
                    );
                });
                await Promise.all(uploadPromises);
            });
            return;
        }
        const newFiles = Array.isArray(selectedFile)
            ? selectedFile.map((f, index) => {
                  return {
                      id: `${files.length + index + 1}`,
                      file: f,
                      name: f.name.split(".").slice(0, -1).join("."),
                  };
              })
            : [
                  {
                      id: `${files.length + 1}`,
                      file: selectedFile,
                      name: selectedFile.name.split(".").slice(0, -1).join("."),
                  },
              ];
        setFiles([...files, ...newFiles]);
    };

    const handleRemoveFile = async (fileId: string) => {
        if (isEditing) {
            const answer = await getUserConfirmation("", {
                mui: true,
                modalTheme: "error",
                title: "Remove File?",
                form: (
                    <Typography variant="body3">
                        Are you sure you want to delete this file?
                        <br />
                        <Typography variant="body3" fontWeight={600}>
                            {files?.find(file => file.id === fileId)?.name || ""}
                        </Typography>
                    </Typography>
                ),
                okButtonText: "Remove",
            });
            if (!answer.value) return;
            updateDealRegistration.mutate({
                mutate_type: MutateTypes.DeleteDealDocument,
                id: deal?.id,
                doc_id: fileId,
                mutate_is_updating_append: fileId,
            });
        } else {
            const filtered = [...files].filter(f => f.id !== fileId);
            setFiles(filtered);
        }
    };

    const handleToggleEditFile = useCallback(
        (fileId: string) => {
            const foundContractFile = files.find(item => item.id === fileId);
            if (foundContractFile) {
                setValue(
                    fileId + "_name",
                    foundContractFile.file instanceof File ? foundContractFile.name : foundContractFile.name,
                );
                setIsEditingFile({...isEditingFile, [fileId]: !isEditingFile[fileId]});
            }
        },
        [isEditingFile, files],
    );

    const handleUpdateFileTitle = useCallback(
        async (fileId: string) => {
            const passedValidation = await trigger(fileId + "_name");
            if (!passedValidation) return;
            const newTitle = watch(fileId + "_name");
            if (isEditing) {
                updateDealRegistration.mutate({
                    mutate_type: MutateTypes.UpdateDealDocument,
                    id: deal?.id,
                    body: {id: fileId, name: newTitle},
                    mutate_is_updating_append: fileId,
                    onSuccess: () => {
                        setIsEditingFile({...isEditingFile, [fileId]: false});
                    },
                    onError: () => {
                        setIsEditingFile({...isEditingFile, [fileId]: false});
                    },
                });
            } else {
                setFiles(files.map(file => (file.id === fileId ? {...file, name: newTitle} : file)));
                setIsEditingFile({...isEditingFile, [fileId]: false});
            }
        },
        [files, isEditingFile],
    );

    const renderFiles = (filterBy: "vendor" | "msp" | "none" = "none", showUploading: boolean) => {
        const filteredFiles = files.filter(file => {
            const filterByValue =
                filterBy === "none"
                    ? true
                    : file.company
                    ? file.company?.type?.type_is_of_vendor === (filterBy === "vendor")
                    : true;
            const isUploadingValue = file.id?.includes("uploading_");
            return filterByValue && (showUploading ? true : !isUploadingValue);
        });
        const filterLabel = isViewingAsVendor
            ? filterBy === "vendor"
                ? "your company"
                : "submitter"
            : filterBy === "msp"
            ? "you"
            : filterBy;
        const hideEditControls = isViewingAsVendor ? filterBy === "msp" : filterBy === "vendor";
        if (!filteredFiles.length && filterBy !== "none") {
            return (
                <Typography variant="body3" fontInter>
                    No files uploaded by {filterLabel}
                </Typography>
            );
        }
        return filteredFiles.map((file, index) => {
            const hasDoc = !!file.document?.url || !!file.file;
            const docUrl = hasDoc ? file.document?.url : "";
            const isUploading = file.id?.includes("uploading_");
            const isSaving = isUpdating[MutateTypes.UpdateDealDocument + file?.id];
            const isEditingCurFile = isEditingFile[file.id];
            const isDeleting = isUpdating[MutateTypes.DeleteDealDocument + file.id];
            return (
                <BorderedBox sx={{padding: 1, paddingRight: 2, flexDirection: "row", alignItems: "center"}} key={index}>
                    <Box
                        sx={{width: 48, height: 48, minWidth: 48, borderRadius: 1}}
                        bgcolor={theme.palette.blue[100]}
                        display="flex"
                        justifyContent="center"
                        alignItems="center">
                        <FileCopyOutlinedIcon htmlColor={theme.palette.blue[500]} />
                    </Box>
                    {isEditingCurFile ? (
                        <TextBoxComponent
                            id={file.id + "_name"}
                            label="File Name:"
                            placeholder="Please input the file name"
                            containerClassName="w-100"
                            isRequired
                            validationSchema={v => (typeof v === "string" ? !v.includes(".") : false)}
                            tooltipHelperText="File name must not include a period ('.')"
                            validateOnTheFly
                        />
                    ) : (
                        <Typography
                            variant="body2"
                            fontWeight={600}
                            fontInter
                            color={theme.palette.neutral[700]}
                            overflow="hidden"
                            title={file.name}
                            textOverflow="ellipsis">
                            {file.name}
                        </Typography>
                    )}
                    {isUploading && (
                        <Typography variant="body2" fontWeight={600} fontInter color={theme.palette.neutral[700]}>
                            {fileUploadProgress[file?.id] || 0}%
                        </Typography>
                    )}
                    {!isUploading && (
                        <Box marginLeft="auto" display={"flex"} gap={2} alignItems="center">
                            {isReadOnly || !hasEditAccess || disableUpdates || hideEditControls ? (
                                hasExportAccess && !!docUrl ? (
                                    <a
                                        href={docUrl}
                                        style={{cursor: !!docUrl ? "pointer" : "not-allowed"}}
                                        title={!!docUrl ? "View file" : "Upload in progress"}
                                        download={file.name + mimeToExtension[file.file?.mime_type || ""]}
                                        target="_blank"
                                        rel="noopener noreferrer">
                                        <LaunchOutlinedIcon
                                            htmlColor={theme.palette.blue[600]}
                                            sx={{height: 24, width: 24}}
                                        />
                                    </a>
                                ) : null
                            ) : !isEditingCurFile ? (
                                <Box display="flex" flexDirection="row" alignItems="center" gap="10px">
                                    {hasExportAccess && !!docUrl && (
                                        <a
                                            href={docUrl || ""}
                                            style={{cursor: hasDoc ? "pointer" : "not-allowed"}}
                                            title={hasDoc ? "View file" : "Upload in progress"}
                                            download={file.name + mimeToExtension[file.file?.mime_type || ""]}
                                            target="_blank"
                                            rel="noopener noreferrer">
                                            <LaunchOutlinedIcon
                                                htmlColor={theme.palette.blue[600]}
                                                sx={{height: 24, width: 24}}
                                            />
                                        </a>
                                    )}
                                    <Box
                                        sx={{cursor: hasDoc ? "pointer" : "not-allowed"}}
                                        onClick={() => {
                                            if (hasDoc) handleToggleEditFile(file.id);
                                        }}
                                        title={hasDoc ? "Edit file name" : "Upload in progress"}>
                                        <EditOutlinedIcon
                                            htmlColor={theme.palette.blue[600]}
                                            sx={{height: 24, width: 24}}
                                        />
                                    </Box>
                                    <Box
                                        sx={{cursor: hasDoc ? "pointer" : "not-allowed"}}
                                        onClick={() => {
                                            if (hasDoc) handleRemoveFile(file.id);
                                        }}
                                        title={hasDoc ? `Remove ${file.name}` : "Upload in progress"}>
                                        {isDeleting ? (
                                            <Loader inline loading />
                                        ) : (
                                            <DeleteOutlinedIcon color="error" sx={{height: 24, width: 24}} />
                                        )}
                                    </Box>
                                </Box>
                            ) : isEditingCurFile ? (
                                <Box display="flex" flexDirection="row" alignItems="center" gap="10px">
                                    <Box
                                        sx={{cursor: "pointer"}}
                                        onClick={() => handleToggleEditFile(file.id)}
                                        title="Cancel">
                                        <CancelOutlinedIcon color="neutral" sx={{height: 32, width: 32}} />
                                    </Box>
                                    <Button
                                        id={"save_file_" + file.id}
                                        color="secondary"
                                        variant="contained"
                                        onClick={() => handleUpdateFileTitle(file.id)}
                                        loading={isSaving}>
                                        Save
                                    </Button>
                                </Box>
                            ) : null}
                        </Box>
                    )}
                </BorderedBox>
            );
        });
    };

    useEffect(() => {
        if (dealDocuments.data) {
            setFiles(dealDocuments.data);
        }
    }, [dealDocuments.data]);

    useEffect(() => {
        onFilesChange?.(files);
    }, [files]);

    return (
        <Box display="flex" flexDirection="column" width="100%" gap={2}>
            {hasEditAccess && !disableUpdates && (
                <MuiUpload
                    id="contract-upload"
                    maxSize="1024"
                    type={MediaTypes.Document}
                    file={undefined}
                    setFile={handleUploadNewFile}
                    imagePreviewUrl={""}
                    setImagePreviewUrl={() => null}
                    dropZoneProps={{
                        width: "100%",
                    }}
                />
            )}
            {isLoading ? null : isEditing || deal?.id ? (
                <>
                    <Typography variant="body2" fontWeight={500} fontInter>
                        Uploaded by {isViewingAsVendor ? "your company" : "you"}
                    </Typography>
                    {renderFiles(isViewingAsVendor ? "vendor" : "msp", true)}
                    {deal?.status !== DEAL_STATUS.DRAFT && (
                        <>
                            <Typography variant="body2" fontWeight={500} fontInter>
                                Uploaded by {isViewingAsVendor ? "Submitter" : "Vendor"}
                            </Typography>
                            {renderFiles(isViewingAsVendor ? "msp" : "vendor", false)}
                        </>
                    )}
                </>
            ) : (
                renderFiles("none", false)
            )}
            {isLoading && <Skeleton width="100%" height={160} />}
        </Box>
    );
};

export default CreateEditDealStepFour;
