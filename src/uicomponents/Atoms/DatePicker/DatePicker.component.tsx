import {TextField, useTheme} from "@mui/material";
import Box from "../Box/Box.component";
import CustomPopover from "../../Molecules/CustomPopover/CustomPopover";
import {useEffect, useRef, useState} from "react";
import {StaticDatePicker} from "@mui/x-date-pickers/StaticDatePicker";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MenuButton from "../../Molecules/MenuButton/menuButton.component";
import {DateTime} from "luxon";
import {monthNamesShort, monthsLongAndShort} from "../../../utils/formatDate";
import {wait} from "../../../utils/async.util";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import {useFormContext} from "react-hook-form";

interface IProps {
    label?: string;
    onChange?: Function;
    value?: string | null;
    minDate?: string;
    maxDate?: string;
    id: string;
}

const DatePicker = ({label, onChange, value, minDate, maxDate, id}: IProps) => {
    const today = new Date().toISOString();
    const luxonToday = DateTime.fromISO(today);
    const [open, setOpen] = useState<boolean>(false);
    const [datepickerInfo, setDatePickerInfo] = useState({
        month: luxonToday.month,
        year: luxonToday.year,
    });
    const [datetimeValue, setDatetimeValue] = useState<DateTime | null>(null);
    const anchor = useRef<HTMLDivElement>(null);
    const datepickerRef = useRef<HTMLDivElement>(null);
    const theme = useTheme();
    const formContext = useFormContext();
    const isFormProviderAvailable = !!formContext;

    const handleChange = newValue => {
        setDatetimeValue(newValue);
    };

    const handleAccept = () => {
        setOpen(false);
        const newValue = datetimeValue?.toJSDate().toISOString().split("T")[0] || null;
        onChange?.(newValue);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleMonthPrevNext = (previous: boolean) => {
        const ele: HTMLButtonElement | null | undefined = datepickerRef.current?.querySelector(
            `.MuiPickersArrowSwitcher-button[aria-label='${previous ? "Previous" : "Next"} month']`,
        );
        if (!ele) return;
        const navigateToNextYear = !previous && datepickerInfo.month + 1 > 12;
        const navigateToPrevYear = previous && datepickerInfo.month - 1 < 1;
        setDatePickerInfo({
            ...datepickerInfo,
            month: previous
                ? navigateToPrevYear
                    ? 12
                    : datepickerInfo.month - 1
                : navigateToNextYear
                  ? 1
                  : datepickerInfo.month + 1,
            year: datepickerInfo.year + (navigateToNextYear ? 1 : navigateToPrevYear ? -1 : 0),
        });
        ele?.click();
    };

    const handleMonthSelect = async newValue => {
        if (!datepickerRef.current) return;
        const [shortCode, monthIndex] = newValue.id.split("_");
        const header: HTMLDivElement | null = datepickerRef.current.querySelector(".MuiPickersCalendarHeader-label");
        header?.click();
        await wait(0);
        const yearBtn: HTMLButtonElement | null = datepickerRef.current.querySelector(
            `.MuiPickersYear-yearButton[aria-checked="true"]`,
        );
        yearBtn?.click();
        await wait(0);
        const monthButton: HTMLButtonElement | undefined = Array.from(
            datepickerRef.current?.querySelectorAll(`.MuiPickersMonth-monthButton`) || [],
        ).find(button => button.textContent === shortCode) as HTMLButtonElement | undefined;
        monthButton?.click();
        setDatePickerInfo({
            ...datepickerInfo,
            month: Number(monthIndex),
        });
    };

    const handleYearPrevNext = async (previous: boolean) => {
        if (!datepickerRef.current) return;
        const header: HTMLDivElement | null = datepickerRef.current.querySelector(".MuiPickersCalendarHeader-label");
        if (!header) return;
        header.click();
        await wait(0);
        const yearButton: HTMLButtonElement | undefined = Array.from(
            datepickerRef.current?.querySelectorAll(`.MuiPickersYear-yearButton`) || [],
        ).find(button => button.textContent === String(datepickerInfo.year + (previous ? -1 : 1))) as
            | HTMLButtonElement
            | undefined;
        setDatePickerInfo({
            ...datepickerInfo,
            year: datepickerInfo.year + (previous ? -1 : 1),
        });
        yearButton?.click();
        await wait(0);
        const monthBtn: HTMLButtonElement | null = datepickerRef.current.querySelector(
            `.MuiPickersMonth-monthButton[aria-checked="true"]`,
        );
        monthBtn?.click();
    };

    const handleYearSelect = async newValue => {
        if (!datepickerRef.current) return;
        const selectedYear = Number(newValue.id);
        const header: HTMLDivElement | null = datepickerRef.current?.querySelector(".MuiPickersCalendarHeader-label");
        if (!header) return;
        header.click();
        await wait(0);
        const yearButton: HTMLButtonElement | undefined = Array.from(
            datepickerRef.current?.querySelectorAll(`.MuiPickersYear-yearButton`) || [],
        ).find(button => button.textContent === newValue.id) as HTMLButtonElement | undefined;
        setDatePickerInfo({
            ...datepickerInfo,
            year: selectedYear,
        });
        yearButton?.click();
        await wait(0);
        const monthBtn: HTMLButtonElement | null = datepickerRef.current.querySelector(
            `.MuiPickersMonth-monthButton[aria-checked="true"]`,
        );
        monthBtn?.click();
    };

    const generateYearItems = (startYear: number, endYear: number) => {
        return Array.from({length: endYear - startYear + 1}, (_, i) => {
            const year = startYear + i;
            return {id: year.toString(), children: year.toString()};
        });
    };

    const currentYear = new Date().getFullYear();
    const futureYear = currentYear + 20;
    const yearItems = generateYearItems(2000, futureYear);

    useEffect(() => {
        if (value) {
            let luxonValue;
            if (DateTime.fromISO(value).isValid) {
                luxonValue = DateTime.fromISO(value);
            } else if (DateTime.fromFormat(value, "yyyy-MM-dd").isValid) {
                luxonValue = DateTime.fromFormat(value, "yyyy-MM-dd");
            } else {
                console.warn("Invalid date format:", value);
                return;
            }
            setDatetimeValue(luxonValue);
            setDatePickerInfo({month: luxonValue.month, year: luxonValue.year});
        }
    }, [value]);

    return (
        <Box>
            <Box ref={anchor}>
                <TextField
                    type="date"
                    label={label}
                    onClick={e => {
                        e.preventDefault();
                        setOpen(true);
                    }}
                    value={datetimeValue?.toFormat("yyyy-MM-dd")}
                    {...(isFormProviderAvailable ? formContext.register(id) : {})}
                />
            </Box>
            <CustomPopover
                id={`date_range_popover`}
                style={{zIndex: 1501}}
                open={open}
                anchorEl={anchor.current}
                onClose={() => {
                    setOpen(false);
                }}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                content={
                    <Box ref={datepickerRef}>
                        <Box
                            display="flex"
                            flexDirection="row"
                            justifyContent="space-between"
                            alignItems="center"
                            padding={2}>
                            <Box display="flex" flexDirection="row" alignItems="center">
                                <Box sx={{cursor: "pointer"}} onClick={() => handleMonthPrevNext(true)}>
                                    <ChevronLeftIcon />
                                </Box>
                                <MenuButton
                                    id="month-select"
                                    onChange={handleMonthSelect}
                                    items={monthsLongAndShort.map(m => ({
                                        id: `${m.short}_${m.monthIndex}`,
                                        children: m.long,
                                    }))}
                                    buttonProps={{
                                        sx: {color: theme.palette.blue[800]},
                                    }}>
                                    {monthNamesShort[datepickerInfo.month - 1]} <ArrowDropDownIcon />
                                </MenuButton>
                                <Box sx={{cursor: "pointer"}} onClick={() => handleMonthPrevNext(false)}>
                                    <ChevronRightIcon />
                                </Box>
                            </Box>
                            <Box display="flex" flexDirection="row" alignItems="center">
                                <Box sx={{cursor: "pointer"}} onClick={() => handleYearPrevNext(true)}>
                                    <ChevronLeftIcon />
                                </Box>
                                <MenuButton
                                    id="year-select"
                                    items={yearItems}
                                    onChange={handleYearSelect}
                                    buttonProps={{
                                        sx: {color: theme.palette.blue[800]},
                                    }}>
                                    {datepickerInfo.year} <ArrowDropDownIcon />
                                </MenuButton>
                                <Box sx={{cursor: "pointer"}} onClick={() => handleYearPrevNext(false)}>
                                    <ChevronRightIcon />
                                </Box>
                            </Box>
                        </Box>
                        <Box
                            sx={{
                                ".MuiPickersCalendarHeader-root": {
                                    display: "none",
                                },
                                ".MuiPickersToolbar-root": {
                                    display: "none",
                                },
                                ".MuiPickersCalendarHeader-label": {
                                    display: "none",
                                },
                                ".MuiPickersCalendarHeader-switchViewButton": {
                                    display: "none",
                                },
                                ".MuiYearCalendar-root, .MuiMonthCalendar-root": {
                                    display: "none !important",
                                },
                                ".MuiDateCalendar-root": {
                                    height: "fit-content !important",
                                },
                                ".MuiPickersSlideTransition-root": {
                                    minHeight: "fit-content !important",
                                    height: 192,
                                },
                            }}>
                            <StaticDatePicker
                                onChange={handleChange}
                                value={datetimeValue}
                                views={["year", "month", "day"]}
                                onAccept={handleAccept}
                                onClose={handleClose}
                                sx={{zIndex: 1, bgcolor: theme.palette.neutral[100], minHeight: 280}}
                                slotProps={{
                                    actionBar: {
                                        actions: ["cancel", "accept"],
                                        sx: {
                                            "& .MuiButton-root": {
                                                color: theme.palette.blue[800],
                                                "&:hover": {
                                                    backgroundColor: theme.palette.blue[100],
                                                },
                                            },
                                        },
                                    },
                                }}
                                minDate={minDate ? DateTime.fromFormat(minDate, "yyyy-MM-dd") : undefined}
                                maxDate={maxDate ? DateTime.fromFormat(maxDate, "yyyy-MM-dd") : undefined}
                            />
                        </Box>
                    </Box>
                }
            />
        </Box>
    );
};

export default DatePicker;
