import {useEffect} from "react";
import {useLocation} from "react-router-dom";
import {scrollToTop} from "../../../utils/scrollToTop.util";

export default function ScrollToTopWrapper() {
    const {pathname} = useLocation();
    const body = document.querySelector("body");
    const bodyWidth = body?.clientWidth || 0;
    const hasFixedScrollbar = bodyWidth < window.innerWidth;
    const scrollbarWidth = window.innerWidth - bodyWidth;

    useEffect(() => {
        if (pathname.includes("/channelcharts/")) {
            return;
        }
        scrollToTop();
    }, [pathname]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            if (hasFixedScrollbar) {
                const compensateName = body?.classList?.value?.match(/compensate-\d+/)?.[0];
                if (compensateName) body?.classList?.remove(compensateName);
                body?.classList?.add(`compensate-${scrollbarWidth}`);
            }
        }, 1000);

        return () => clearTimeout(timeout);
    }, [pathname, hasFixedScrollbar]);

    return <></>;
}
