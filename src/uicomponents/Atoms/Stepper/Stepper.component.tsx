import {default as Mu<PERSON><PERSON>tep<PERSON>, StepperProps} from "@mui/material/Stepper";
import {useEffect, useRef} from "react";

interface IStepperProps extends StepperProps {
    scrollStepsOnChange?: boolean;
}

const Stepper = ({scrollStepsOnChange, ...props}: IStepperProps) => {
    const stepperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (scrollStepsOnChange && typeof props.activeStep === "number" && stepperRef.current) {
            const steps = stepperRef.current?.querySelectorAll(".MuiStep-root");
            const activeStepElement = steps?.[props.activeStep];

            if (activeStepElement) {
                activeStepElement.scrollIntoView({behavior: "smooth"});
            }
        }
    }, [scrollStepsOnChange, props?.activeStep]);

    return <MuiStepper {...props} ref={stepperRef} />;
};

export default Stepper;
