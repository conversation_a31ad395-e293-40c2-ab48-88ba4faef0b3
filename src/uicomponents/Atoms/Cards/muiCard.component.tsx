import MoreVert from "@mui/icons-material/MoreVert";
import {type SxProps, type Theme, useTheme} from "@mui/material";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import type {CSSProperties, PropsWithChildren, ReactNode} from "react";
import MenuButton, {type IMenuButtonItem} from "../../Molecules/MenuButton/menuButton.component";
import Box from "../Box/Box.component";
import Typography from "../Typography/Typography.component";

interface IProps {
    id?: string;
    headerAction?: ReactNode;
    headerAvatar?: ReactNode;
    headerTitle: {
        text: ReactNode;
        color?: string | ((theme: Theme) => string);
        fontSize?: CSSProperties["fontSize"];
        sx?: SxProps<Theme>;
        indicator?: ReactNode;
    };
    headerSubtitle?: {
        text: ReactNode;
        color?: string | ((theme: Theme) => string);
        fontSize?: CSSProperties["fontSize"];
        sx?: SxProps<Theme>;
    };
    className?: string;
    contentSx?: SxProps<Theme>;
    sx?: SxProps<Theme>;
    cardActions?: ReactNode;
    onClickHeader?: () => void;
    threeDotActions?: IMenuButtonItem[];
    highlighted?: boolean;
    showBoxShadow?: boolean;
}

export default function MuiCard({
    id,
    headerAction,
    headerAvatar,
    headerTitle,
    headerSubtitle,
    className,
    contentSx,
    sx,
    cardActions,
    children,
    onClickHeader,
    threeDotActions,
    showBoxShadow,
}: PropsWithChildren<IProps>) {
    const theme = useTheme();
    return (
        <Card
            className={className}
            id={id}
            sx={sx}
            style={{
                padding: "16px",
                boxShadow: showBoxShadow ? "0px 4px 4px 0px rgba(0, 0, 0, 0.10)" : "none",
                border: `1px solid ${theme.palette.neutral[300]}`,
                borderRadius: "8px",
                position: "relative",
                height: "auto",
            }}>
            <CardContent
                sx={contentSx}
                style={{
                    padding: "0",
                    display: "flex",
                    flexDirection: "column",
                    gap: "24px",
                    justifyContent: "space-between",
                    height: "100%",
                }}>
                <div
                    style={{
                        display: "flex",
                        gap: "16px",
                        alignItems: "center",
                        justifyContent: "space-between",
                    }}>
                    <Box
                        onClick={onClickHeader}
                        sx={{
                            display: "flex",
                            flexGrow: 1,
                            gap: "12px",
                            alignItems: "center",
                            cursor: onClickHeader ? "pointer" : undefined,
                        }}>
                        {headerAvatar}
                        <div
                            style={{
                                display: "flex",
                                flexDirection: "column",
                                gap: "4px",
                                justifyContent: "center",
                                flexGrow: 1,
                            }}>
                            <Box sx={{display: "flex", alignItems: "center", gap: 1}}>
                                <Typography
                                    variant="subtitle1"
                                    fontInter
                                    title={typeof headerTitle.text === "string" ? headerTitle.text : ""}
                                    sx={{
                                        fontWeight: "500 !important",
                                        textOverflow: "ellipsis",
                                        overflow: "hidden",
                                        display: "-webkit-box",
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: "vertical",
                                        wordBreak: "break-word",
                                        color: headerTitle.color
                                            ? typeof headerTitle.color === "string"
                                                ? headerTitle.color
                                                : headerTitle.color(theme)
                                            : theme.palette.secondary.main,
                                        fontSize: headerTitle.fontSize ? headerTitle.fontSize : undefined,
                                        ...(typeof headerTitle.sx === "function"
                                            ? headerTitle.sx(theme)
                                            : headerTitle.sx),
                                    }}>
                                    {headerTitle.text}
                                </Typography>
                                {headerTitle?.indicator}
                            </Box>
                            {headerSubtitle && (
                                <Typography
                                    variant="subtitle3"
                                    fontInter
                                    title={typeof headerSubtitle.text === "string" ? headerSubtitle.text : ""}
                                    sx={{
                                        fontWeight: "400 !important",
                                        textOverflow: "ellipsis",
                                        overflow: "hidden",
                                        display: "-webkit-box",
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: "vertical",
                                        wordBreak: "break-word",
                                        color: headerSubtitle.color
                                            ? typeof headerSubtitle.color === "string"
                                                ? headerSubtitle.color
                                                : headerSubtitle.color(theme)
                                            : theme.palette.secondary.main,
                                        fontSize: headerSubtitle.fontSize ? headerSubtitle.fontSize : undefined,
                                        ...(typeof headerSubtitle.sx === "function"
                                            ? headerSubtitle.sx(theme)
                                            : headerSubtitle.sx),
                                    }}>
                                    {headerSubtitle.text}
                                </Typography>
                            )}
                        </div>
                    </Box>
                    {headerAction}
                    {!!threeDotActions?.length && (
                        <MenuButton
                            id={`more-actions-${id}`}
                            buttonVariant="iconButton"
                            buttonProps={{
                                sx: {
                                    width: "24px",
                                    minWidth: "24px!important",
                                    height: "24px",
                                    boxShadow: "none",
                                    position: "relative",
                                    zIndex: 10,
                                },
                            }}
                            containerStyles={{
                                flexShrink: 0,
                            }}
                            items={threeDotActions}
                            aria-label="settings">
                            <MoreVert color="neutral" />
                        </MenuButton>
                    )}
                </div>
                {children}
                {cardActions && (
                    <CardActions style={{padding: "0", width: "100%", alignItems: "flex-end"}}>
                        {cardActions}
                    </CardActions>
                )}
            </CardContent>
        </Card>
    );
}
