import ArrowDropDown from "@mui/icons-material/ArrowDropDown";
import ArrowDropUp from "@mui/icons-material/ArrowDropUp";
import {
    List,
    ListItemAvatar,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    useTheme,
    type SxProps,
    type Theme,
} from "@mui/material";
import {Dispatch, SetStateAction, useEffect, useState, type ReactNode} from "react";
import {stripHtml} from "../../../utils/formatString.util";
import AvatarComponentV2 from "../../Molecules/Avatar/avatarV2.component";
import Checkbox from "../Checkbox/Checkbox.component";
import Collapse from "../Collapse/Collapse.component";
import Typography from "../Typography/Typography.component";

interface IListCollapseItem {
    id: string;
    children: ReactNode;
    isSubHeader?: boolean;
    group?: string;
    isGroupHeader?: boolean;
    icon?: ReactNode;
    image?: string;
    profileType?: "vendor" | "msp" | "user" | "influencer";
    search?: string;
    onClick?: (item: IListItem) => void;
}

export interface IListItem extends IListCollapseItem {
    collapseItems?: IListItem[];
}

export interface ISelectedListItems {
    [key: string]: string[];
}

interface IProps {
    items: IListItem[];
    sx?: SxProps<Theme>;
    selectedItemsState?: [ISelectedListItems, Dispatch<SetStateAction<ISelectedListItems>>];
    startOpen?: boolean;
}

export default function MuiList(props: IProps) {
    const [open, setOpen] = useState<string[]>([]);
    const [selectedItems, setSelectedItems] = props.selectedItemsState || [{}, () => {}];
    const theme = useTheme();
    const collapseTriggers = props.items?.map(item => !!item.collapseItems && item.id).filter(Boolean) as string[];

    const handleItemClick = (item: IListItem) => {
        if (item.collapseItems) {
            setOpen(prev => (prev.includes(item.id) ? prev.filter(i => i !== item.id) : [...prev, item.id]));
        } else {
            const itemKey = item.group || item.id;
            const isSelected = selectedItems[itemKey]?.includes(item.id);
            if (isSelected) {
                const filteredItems = selectedItems[itemKey]?.filter(i => i !== item.id);
                setSelectedItems(prev => ({...prev, [itemKey]: filteredItems || []}));
            } else {
                setSelectedItems(prev => ({...prev, [itemKey]: [...(prev[itemKey] || []), item.id]}));
            }
        }
        if (item.onClick) item.onClick(item);
    };

    const renderItem = (item: IListItem) => {
        const isCollapseHeader = !!item.collapseItems;
        const itemKey = item.group || item.id;
        const isSelected = selectedItems[itemKey]?.includes(item.id);
        const isMSPOrVendor =
            `${item.profileType ?? ""}`?.toLowerCase().includes("msp") ||
            `${item.profileType ?? ""}`?.toLowerCase().includes("vendor");
        return (
            <>
                <ListItemButton
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    sx={{
                        padding: isCollapseHeader ? "8px 16px 8px 8px" : "8px 16px",
                        display: "flex",
                        gap: "16px",
                        alignItems: "center",
                        bgcolor: isSelected ? theme.palette.primary[100] : undefined,
                        pointerEvents: isCollapseHeader && collapseTriggers.length < 2 ? "none" : undefined,
                        borderBottom: isCollapseHeader ? `1px solid ${theme.palette.neutral[300]}` : undefined,
                        "&:hover": {
                            bgcolor: isSelected ? theme.palette.primary[200] : theme.palette.secondary[100],
                        },
                        "& *": {
                            fontFamily: "Inter,sans-serif!important",
                        },
                    }}>
                    {(!item.onClick || item.icon) && (
                        <ListItemIcon
                            key={item.id + isSelected + "-listItemIcon"}
                            sx={{
                                minWidth: "24px",
                                display: "flex",
                                alignItems: "center",
                                gap: "16px",
                            }}>
                            {!item.onClick && !isCollapseHeader && (
                                <Checkbox
                                    edge="start"
                                    checked={isSelected}
                                    tabIndex={-1}
                                    disableRipple
                                    inputProps={{"aria-labelledby": `checkbox-${item.id}`, style: {height: "24px"}}}
                                    sx={{
                                        width: "24px",
                                        height: "24px",
                                        padding: "0",
                                        margin: "0",
                                        pointerEvents: "none",
                                    }}
                                />
                            )}
                            {item.icon}
                        </ListItemIcon>
                    )}
                    {item.image && (
                        <ListItemAvatar
                            sx={{
                                width: 32,
                                height: 32,
                                minWidth: "fit-content",
                            }}>
                            <AvatarComponentV2
                                isCompany={isMSPOrVendor}
                                profileTypeMinimized={isMSPOrVendor}
                                showProfileType={isMSPOrVendor}
                                user={{
                                    avatar: item.image ?? "",
                                    type: item.profileType ?? "",
                                    profile_type: item.profileType ?? "",
                                    friendly_url: "",
                                    name: typeof item.children === "string" ? item.children : "Avatar",
                                }}
                                disableLink
                                size={32}
                            />
                        </ListItemAvatar>
                    )}
                    <ListItemText
                        sx={{
                            height: "24px",
                            margin: "0",
                            display: "flex",
                            alignItems: "center",
                            "&, &>*": {
                                width: "100%",
                            },
                        }}>
                        <Typography
                            variant="body3"
                            fontInter
                            sx={{
                                color: item.collapseItems
                                    ? `${theme.palette.secondary.main}!important`
                                    : `${theme.palette.neutral[600]}!important`,
                                fontSize: "16px!important",
                                fontWeight: item.collapseItems ? "600!important" : undefined,
                                display: "flex",
                                alignItems: "center",
                                gap: "16px",
                            }}>
                            {item.children}
                        </Typography>
                    </ListItemText>
                    {isCollapseHeader && collapseTriggers.length > 1 ? (
                        open.includes(item.id) ? (
                            <ArrowDropUp color="secondary" />
                        ) : (
                            <ArrowDropDown color="secondary" />
                        )
                    ) : undefined}
                </ListItemButton>
                {item.collapseItems && item.collapseItems.length > 0 && (
                    <Collapse in={open.includes(item.id)} timeout="auto">
                        <List component="div" disablePadding>
                            {item.collapseItems
                                .sort((a, b) => {
                                    return stripHtml(a.children).localeCompare(stripHtml(b.children));
                                })
                                .map(item => renderItem(item))}
                        </List>
                    </Collapse>
                )}
            </>
        );
    };

    useEffect(() => {
        if (collapseTriggers.length > 0 && props.startOpen) {
            setOpen(collapseTriggers);
        }
    }, []);

    return (
        <List sx={{width: "100%", bgcolor: theme.palette.neutral[100], ...props.sx}}>
            {props.items
                .sort((a, b) => {
                    return stripHtml(a.children).localeCompare(stripHtml(b.children));
                })
                .map(item => renderItem(item))}
        </List>
    );
}
