import {MoreVert} from "@mui/icons-material";
import MenuButton, {IMenuButtonItem} from "../../Molecules/MenuButton/menuButton.component";

interface IProps<TMultiSelect extends boolean = false> {
    items: IMenuButtonItem<TMultiSelect>[];
    id: string;
}
const CellActionsMenu = ({items, id}: IProps) => {
    return (
        <div style={{display: "flex", justifyContent: "center"}}>
            <MenuButton
                id={id}
                buttonVariant="iconButton"
                buttonProps={{
                    sx: {
                        width: "24px",
                        minWidth: "24px!important",
                        height: "24px",
                        boxShadow: "none",
                        flexShrink: 0,
                        position: "relative",
                        zIndex: 10,
                    },
                }}
                items={items}
                aria-label="settings">
                <MoreVert sx={{color: "secondary.600"}} />
            </MenuButton>
        </div>
    );
};

export default CellActionsMenu;
