import {FormControl, MenuItem, MenuProps, Select, SelectProps, SxProps, Theme} from "@mui/material";
import type {Key} from "react";

type DropdownProps<TValue> = SelectProps<TValue> & {
    id: string;
    options: {value: TValue; label: string}[];
    styles?: {
        formControl?: SxProps<Theme>;
        select?: SxProps<Theme>;
        menuItem?: SxProps<Theme>;
    };
    autoFocusValue?: string;
    MenuProps?: Partial<MenuProps>;
    onOpen?: () => void;
};

export default function Dropdown<TValue>(props: DropdownProps<TValue>) {
    const handleFocusValue = () => {
        setTimeout(() => {
            const wrapper = document.querySelector(`#listbox-${props?.id}`);
            if (wrapper) {
                const element: any = wrapper.querySelector(`[data-value="${props?.autoFocusValue}"]`);
                if (element) {
                    element.scrollIntoView({behavior: "smooth", block: "center"});
                }
            }
        }, 600);
    };

    return (
        <FormControl sx={props?.styles?.formControl} variant="standard">
            <Select
                defaultValue={props?.options[0]?.value}
                sx={props?.styles?.select}
                {...props}
                MenuProps={{
                    ...(props?.MenuProps || {}),
                    PaperProps: {id: `listbox-${props?.id}`},
                }}
                onOpen={props.autoFocusValue ? handleFocusValue : props.onOpen}>
                {props?.options.map(option => (
                    <MenuItem
                        sx={props?.styles?.menuItem}
                        key={option.value as Key}
                        value={option.value as string | number}>
                        {option.label}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    );
}
