import type {EditorConfig, LexicalNode, NodeKey, SerializedTextNode, Spread} from "lexical";

import {TextNode} from "lexical";

const BASE_EMOJI_URI = "/Media/rte-icons/emojis/64/";

export type SerializedEmojiNode = Spread<
    {
        unifiedID: string;
        hasImage: boolean;
    },
    SerializedTextNode
>;

type TEmojiConfig = {
    unifiedID: string;
    hasImage: boolean;
};

export class EmojiNode extends TextNode {
    __emojiConfig: TEmojiConfig;

    static getType(): string {
        return "emoji";
    }

    static clone(node: EmojiNode): EmojiNode {
        return new EmojiNode(node.__emojiConfig, node.__key);
    }

    constructor(emojiConfig: TEmojiConfig, key?: NodeKey) {
        const {unifiedID, hasImage} = emojiConfig;
        const unicodeEmoji = String.fromCodePoint(...unifiedID.split("-").map(v => parseInt(v, 16)));
        super(unicodeEmoji, key);

        this.__emojiConfig = {
            unifiedID: unifiedID.toLowerCase(),
            hasImage,
        };
    }

    createDOM(_config: EditorConfig): HTMLElement {
        const dom = document.createElement("span");
        dom.className = "emoji-node";
        if (this.__emojiConfig.hasImage) {
            dom.style.backgroundImage = `url('${BASE_EMOJI_URI}/${this.__emojiConfig.unifiedID}.png')`;
            dom.style.backgroundSize = "contain";
            dom.style.backgroundRepeat = "no-repeat";
            dom.style.color = "transparent";
            dom.style.caretColor = "black";
            dom.classList.add("emoji-image");
        }
        dom.innerText = this.__text;
        return dom;
    }

    updateDOM(prevNode: TextNode, dom: HTMLElement, config: EditorConfig): boolean {
        const inner = dom.firstChild;
        if (inner === null) {
            return true;
        }
        super.updateDOM(prevNode, inner as HTMLElement, config);
        return false;
    }

    static importJSON(serializedNode: SerializedEmojiNode): EmojiNode {
        const node = $createEmojiNode(serializedNode.unifiedID, serializedNode.hasImage);
        return node;
    }

    exportJSON(): SerializedEmojiNode {
        return {
            ...super.exportJSON(),
            type: "emoji",
            unifiedID: this.__emojiConfig.unifiedID,
            hasImage: this.__emojiConfig.hasImage,
        };
    }
}

export function $isEmojiNode(node: LexicalNode | null | undefined): node is EmojiNode {
    return node instanceof EmojiNode;
}

export function $createEmojiNode(unifiedID: string, hasImage: boolean): EmojiNode {
    return new EmojiNode({unifiedID, hasImage}).setMode("token");
}
