/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {LexicalEditor} from "lexical";

import {
    AutoEmbedOption,
    EmbedConfig,
    EmbedMatchResult,
    LexicalAutoEmbedPlugin,
    URL_MATCHER,
} from "@lexical/react/LexicalAutoEmbedPlugin";
import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {useMemo, useState} from "react";
import * as ReactDOM from "react-dom";

import ModalComponent from "../../../../Molecules/Modal/Modal.component";
import Box from "../../../Box/Box.component";
import Typography from "../../../Typography/Typography.component";
import {INSERT_YOUTUBE_COMMAND} from "../YouTubePlugin/youTubePlugin";

interface PlaygroundEmbedConfig extends EmbedConfig {
    // Human readable name of the embeded content.
    contentName: string;

    // Icon for display.
    icon?: JSX.Element;

    // An example of a matching url
    exampleUrl: string;

    // For extra searching.
    keywords: Array<string>;

    // Embed a Youtube Video.
    description?: string;
}

export const YoutubeEmbedConfig: PlaygroundEmbedConfig = {
    contentName: "Youtube Video",

    exampleUrl: "https://www.youtube.com/watch?v=vn0f1yHIh3s",

    // Icon for display.
    icon: <i className="icon youtube" />,

    insertNode: (editor: LexicalEditor, result: EmbedMatchResult) => {
        editor.dispatchCommand(INSERT_YOUTUBE_COMMAND, result.id);
    },

    keywords: ["youtube", "video"],

    // Determine if a given URL is a match and return url data.
    parseUrl: async (url: string) => {
        const match = /^.*(youtu\.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/.exec(url);

        const id = match ? (match?.[2].length === 11 ? match[2] : null) : null;

        if (id != null) {
            return {
                id,
                url,
            };
        }

        return null;
    },

    type: "youtube-video",
};

export const EmbedConfigs = [YoutubeEmbedConfig];

function AutoEmbedMenuItem({
    index,
    isSelected,
    onClick,
    onMouseEnter,
    option,
}: {
    index: number;
    isSelected: boolean;
    onClick: () => void;
    onMouseEnter: () => void;
    option: AutoEmbedOption;
}) {
    let className = "item";
    if (isSelected) {
        className += " selected";
    }
    return (
        <li
            key={option.key}
            tabIndex={-1}
            className={className}
            ref={option.setRefElement}
            role="option"
            aria-selected={isSelected}
            id={"typeahead-item-" + index}
            onMouseEnter={onMouseEnter}
            onClick={onClick}>
            <Typography color="secondary.main" size={16} weight={400}>
                {option.title}
            </Typography>
        </li>
    );
}

function AutoEmbedMenu({
    options,
    selectedItemIndex,
    onOptionClick,
    onOptionMouseEnter,
}: {
    selectedItemIndex: number | null;
    onOptionClick: (option: AutoEmbedOption, index: number) => void;
    onOptionMouseEnter: (index: number) => void;
    options: Array<AutoEmbedOption>;
}) {
    return (
        <Box className="typeahead-popover">
            <ul>
                {options.map((option: AutoEmbedOption, i: number) => (
                    <AutoEmbedMenuItem
                        index={i}
                        isSelected={selectedItemIndex === i}
                        onClick={() => onOptionClick(option, i)}
                        onMouseEnter={() => onOptionMouseEnter(i)}
                        key={option.key}
                        option={option}
                    />
                ))}
            </ul>
        </Box>
    );
}

const debounce = (callback: (text: string) => void, delay: number) => {
    let timeoutId: number;
    return (text: string) => {
        window.clearTimeout(timeoutId);
        timeoutId = window.setTimeout(() => {
            callback(text);
        }, delay);
    };
};

export function AutoEmbedDialog({
    embedConfig,
    onClose,
    open,
}: {
    embedConfig?: PlaygroundEmbedConfig;
    onClose: () => void;
    open: boolean;
}): JSX.Element {
    const [text, setText] = useState("");
    const [editor] = useLexicalComposerContext();
    const [embedResult, setEmbedResult] = useState<EmbedMatchResult | null>(null);

    const validateText = useMemo(
        () =>
            debounce((inputText: string) => {
                const urlMatch = URL_MATCHER.exec(inputText);
                if (!!embedConfig && inputText != null && urlMatch != null) {
                    Promise.resolve(embedConfig.parseUrl(inputText)).then(parseResult => {
                        setEmbedResult(parseResult);
                    });
                } else if (embedResult != null) {
                    setEmbedResult(null);
                }
            }, 200),
        [embedConfig, embedResult],
    );

    const onClick = () => {
        if (embedResult != null) {
            embedConfig?.insertNode(editor, embedResult);
            onClose();
        }
    };

    return (
        <ModalComponent
            open={open}
            onClose={onClose}
            title="Embed"
            okButtonText="Embed"
            okButtonDisabled={!embedResult}
            onSuccess={onClick}
            content={
                <Box>
                    <div className="Input__wrapper">
                        <input
                            type="text"
                            className="Input__input"
                            placeholder={embedConfig?.exampleUrl}
                            value={text}
                            data-test-id={`${embedConfig?.type}-embed-modal-url`}
                            onChange={e => {
                                const {value} = e.target;
                                setText(value);
                                validateText(value);
                            }}
                        />
                    </div>
                </Box>
            }
        />
    );
}

export default function AutoEmbedPlugin(): JSX.Element {
    const [modal, showModal] = useState<PlaygroundEmbedConfig>();

    const getMenuOptions = (activeEmbedConfig: PlaygroundEmbedConfig, embedFn: () => void, dismissFn: () => void) => {
        return [
            new AutoEmbedOption("Dismiss", {
                onSelect: dismissFn,
            }),
            new AutoEmbedOption(`Embed ${activeEmbedConfig.contentName}`, {
                onSelect: embedFn,
            }),
        ];
    };

    return (
        <>
            <AutoEmbedDialog embedConfig={modal} open={!!modal} onClose={() => showModal(undefined)} />
            <LexicalAutoEmbedPlugin<PlaygroundEmbedConfig>
                embedConfigs={EmbedConfigs}
                onOpenEmbedModalForConfig={showModal}
                getMenuOptions={getMenuOptions}
                menuRenderFn={(
                    anchorElementRef,
                    {selectedIndex, options, selectOptionAndCleanUp, setHighlightedIndex},
                ) =>
                    anchorElementRef.current
                        ? ReactDOM.createPortal(
                              <Box
                                  className="typeahead-popover auto-embed-menu"
                                  sx={{
                                      marginLeft: `${Math.max(
                                          parseFloat(anchorElementRef.current.style.width) - 200,
                                          0,
                                      )}px`,
                                      width: 200,
                                      zIndex: 99,
                                  }}>
                                  <AutoEmbedMenu
                                      options={options}
                                      selectedItemIndex={selectedIndex}
                                      onOptionClick={(option: AutoEmbedOption, index: number) => {
                                          setHighlightedIndex(index);
                                          selectOptionAndCleanUp(option);
                                      }}
                                      onOptionMouseEnter={(index: number) => {
                                          setHighlightedIndex(index);
                                      }}
                                  />
                              </Box>,
                              anchorElementRef.current,
                          )
                        : null
                }
            />
        </>
    );
}
