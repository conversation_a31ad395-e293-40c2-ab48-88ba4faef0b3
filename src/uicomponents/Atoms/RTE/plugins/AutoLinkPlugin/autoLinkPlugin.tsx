import {AutoLinkNode} from "@lexical/link";
import {AutoLinkPlugin, createLinkMatcherWithRegExp} from "@lexical/react/LexicalAutoLinkPlugin";
import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {useEffect} from "react";

const URL_REGEX =
    /((https?:\/\/(www\.)?)|(www\.))[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/;

const EMAIL_REGEX =
    /(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/;

const MATCHERS = [
    createLinkMatcherWithRegExp(URL_REGEX, text => {
        return text.startsWith("http") ? text : `https://${text}`;
    }),
    createLinkMatcherWithRegExp(EMAIL_REGEX, text => {
        return `mailto:${text}`;
    }),
];

export default function LexicalAutoLinkPlugin(): JSX.Element {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        if (!editor) return;
        const removeNodeListener = editor.registerNodeTransform(AutoLinkNode, node => {
            if (!node) return;

            const dom = editor.getElementByKey(node.__key);
            if (!dom) return;
            dom.setAttribute("target", "_blank");
        });
        return () => removeNodeListener();
    }, [editor]);

    return <AutoLinkPlugin matchers={MATCHERS} />;
}
