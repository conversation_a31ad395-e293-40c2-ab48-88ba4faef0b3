import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {
    LexicalTypeaheadMenuPlugin,
    MenuOption,
    useBasicTypeaheadTriggerMatch,
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import emojis from "emoji-datasource-apple/emoji.json";
import {$getSelection, $isRangeSelection, TextNode} from "lexical";
import {useCallback, useMemo, useState} from "react";
import * as ReactDOM from "react-dom";
import {$createEmojiNode} from "../../nodes/Emoji/emojiNode";

class EmojiOption extends MenuOption {
    title: string;
    emoji: string;
    keywords: Array<string>;
    unifiedID: string;
    hasImage: boolean;

    constructor(
        title: string,
        emoji: string,
        options: {
            keywords?: Array<string>;
        },
        unifiedID: string,
        hasImage: boolean,
    ) {
        super(title);
        this.title = title;
        this.emoji = emoji;
        this.keywords = options.keywords || [];
        this.unifiedID = unifiedID;
        this.hasImage = hasImage;
    }
}

const BASE_URL = "/Media/rte-icons/emojis/64/";

function EmojiMenuItem({
    index,
    isSelected,
    onClick,
    onMouseEnter,
    option,
}: {
    index: number;
    isSelected: boolean;
    onClick: () => void;
    onMouseEnter: () => void;
    option: EmojiOption;
}) {
    let className = "item";
    if (isSelected) {
        className += " selected";
    }
    return (
        <li
            key={option.key}
            tabIndex={-1}
            className={className}
            ref={option.setRefElement}
            role="option"
            aria-selected={isSelected}
            id={"typeahead-item-" + index}
            onMouseEnter={onMouseEnter}
            onClick={onClick}>
            <span className="text">
                <span
                    style={{
                        width: "20px",
                        height: "20px",
                        backgroundImage: `url(${BASE_URL + option.emoji})`,
                        backgroundSize: "contain",
                    }}
                />{" "}
                {option.title}
            </span>
        </li>
    );
}

const MAX_EMOJI_SUGGESTION_COUNT = 10;

export default function EmojiPickerPlugin() {
    const [editor] = useLexicalComposerContext();
    const [queryString, setQueryString] = useState<string | null>(null);

    const emojiOptions = emojis.map(
        ({image: emoji, short_names, name, unified, has_img_apple}) =>
            new EmojiOption(
                name.toLowerCase(),
                emoji,
                {
                    keywords: [...short_names],
                },
                unified,
                has_img_apple,
            ),
    );

    const checkForTriggerMatch = useBasicTypeaheadTriggerMatch(":", {
        minLength: 0,
    });

    const options: Array<EmojiOption> = useMemo(() => {
        return emojiOptions
            .filter((option: EmojiOption) => {
                return queryString != null
                    ? new RegExp(queryString, "gi").exec(option.title) || option.keywords != null
                        ? option.keywords.some((keyword: string) => new RegExp(queryString, "gi").exec(keyword))
                        : false
                    : emojiOptions;
            })
            .slice(0, MAX_EMOJI_SUGGESTION_COUNT);
    }, [emojiOptions, queryString]);

    const onSelectOption = useCallback(
        (selectedOption: EmojiOption, nodeToRemove: TextNode | null, closeMenu: () => void) => {
            editor.update(() => {
                const selection = $getSelection();

                if (!$isRangeSelection(selection) || selectedOption == null) {
                    return;
                }

                if (nodeToRemove) {
                    nodeToRemove.remove();
                }

                selection.insertNodes([$createEmojiNode(selectedOption.unifiedID, selectedOption.hasImage)]);

                closeMenu();
            });
        },
        [editor],
    );

    return (
        <LexicalTypeaheadMenuPlugin
            onQueryChange={setQueryString}
            onSelectOption={onSelectOption}
            triggerFn={checkForTriggerMatch}
            options={options}
            menuRenderFn={(anchorElementRef, {selectedIndex, selectOptionAndCleanUp, setHighlightedIndex}) => {
                if (anchorElementRef.current == null || options.length === 0) {
                    return null;
                }

                return anchorElementRef.current && options.length
                    ? ReactDOM.createPortal(
                          <div className="typeahead-popover emoji-menu">
                              <ul>
                                  {options.map((option: EmojiOption, index) => (
                                      <EmojiMenuItem
                                          key={option.key}
                                          index={index}
                                          isSelected={selectedIndex === index}
                                          onClick={() => {
                                              setHighlightedIndex(index);
                                              selectOptionAndCleanUp(option);
                                          }}
                                          onMouseEnter={() => {
                                              setHighlightedIndex(index);
                                          }}
                                          option={option}
                                      />
                                  ))}
                              </ul>
                          </div>,
                          anchorElementRef.current,
                      )
                    : null;
            }}
        />
    );
}
