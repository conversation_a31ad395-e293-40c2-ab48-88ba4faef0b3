import "../../nodes/InlineImage/inlineImageNode.css";

import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {$wrapNodeInElement, mergeRegister} from "@lexical/utils";
import {
    $createParagraphNode,
    $createRangeSelection,
    $getSelection,
    $insertNodes,
    $isNodeSelection,
    $isRootOrShadowRoot,
    $setSelection,
    COMMAND_PRIORITY_EDITOR,
    COMMAND_PRIORITY_HIGH,
    COMMAND_PRIORITY_LOW,
    createCommand,
    DRAGOVER_COMMAND,
    DRAGSTART_COMMAND,
    DROP_COMMAND,
    LexicalCommand,
    LexicalEditor,
} from "lexical";
import {useEffect, useRef, useState} from "react";

import {AxiosError, AxiosResponse} from "axios";
import {MediaTypes} from "../../../../../enums/mediaTypes.enum";
import useNotification from "../../../../../hooks/useNotification";
import {IImageUpload} from "../../../../../Interfaces/images.interface";
import {mediaService} from "../../../../../services/media.service";
import {getErrorFromArray} from "../../../../../utils/error.util";
import Loader from "../../../../../utils/loader";
import AlertBanner from "../../../../Molecules/AlertBanner/alertBanner.component";
import ModalComponent from "../../../../Molecules/Modal/Modal.component";
import {IMuiSelectObj} from "../../../../Molecules/MuiSelect/MuiSelect.component";
import MuiUpload from "../../../../Molecules/MuiUpload/MuiUpload.component";
import Box from "../../../Box/Box.component";
import Textbox from "../../../Textbox/Textbox.component";
import {
    $createInlineImageNode,
    $isInlineImageNode,
    InlineImageNode,
    InlineImagePayload,
} from "../../nodes/InlineImage/inlineImageNode";
import {CAN_USE_DOM} from "../../util/canUseDOM";
import {INSERT_IMAGE_COMMAND} from "../ImagesPlugin/imagesPlugin";

export type InsertInlineImagePayload = Readonly<InlineImagePayload>;

const getDOMSelection = (targetWindow: Window | null): Selection | null =>
    CAN_USE_DOM ? (targetWindow || window).getSelection() : null;

export const INSERT_INLINE_IMAGE_COMMAND: LexicalCommand<InlineImagePayload> =
    createCommand("INSERT_INLINE_IMAGE_COMMAND");

export function InsertInlineImageDialog({
    activeEditor,
    open,
    onClose,
    entityId,
    isUserProfile,
}: {
    activeEditor: LexicalEditor;
    open: boolean;
    onClose: () => void;
    entityId: string;
    isUserProfile?: boolean;
}): JSX.Element {
    const hasModifier = useRef(false);

    const [altText, setAltText] = useState("");
    const [showCaption, setShowCaption] = useState(false);
    const [_position, setPosition] = useState<IMuiSelectObj[]>([{id: "left", name: "Left"}]);
    const [errorMessage, setErrorMessage] = useState<string>();
    const [file, setFile] = useState<File>();
    const [previewUrl, setPreviewUrl] = useState<string>("");
    const [uploading, setUploading] = useState(false);
    const notify = useNotification();

    const isDisabled = !file || uploading;

    /* const handleShowCaptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setShowCaption(e.target.checked);
    }; */

    const resetData = () => {
        setAltText("");
        setShowCaption(false);
        setPosition([{id: "left", name: "Left"}]);
        setErrorMessage("");
        setFile(undefined);
        setPreviewUrl("");
        setUploading(false);
    };

    const handleClose = () => {
        resetData();
        onClose();
    };

    useEffect(() => {
        hasModifier.current = false;
        const handler = (e: KeyboardEvent) => {
            hasModifier.current = e.altKey;
        };
        document.addEventListener("keydown", handler);
        return () => {
            document.removeEventListener("keydown", handler);
        };
    }, [activeEditor]);

    const handleOnClick = async () => {
        if (file) {
            setUploading(true);
            try {
                const src = await handleImageUpload(file);
                const payload = {altText, showCaption, src};
                activeEditor.dispatchCommand(INSERT_IMAGE_COMMAND, payload);
                handleClose();
            } catch (e) {
                setUploading(false);
                return;
            }
        }
    };

    const clearInputFile = () => {
        setFile(undefined);
    };

    const handleSetFile = (file: File | File[]) => {
        const f = Array.isArray(file) ? file[0] : file;
        setFile(f);
    };

    const handleImageUpload = (file: File): Promise<string> =>
        new Promise((resolve, reject) => {
            mediaService
                .uploadImage(file, isUserProfile ? "userProfile" : "companyProfile", entityId)
                .then((result: AxiosResponse<IImageUpload>) => {
                    resolve(result.data.src);
                })
                .catch(e => {
                    notify(getErrorFromArray(e as AxiosError), "Error");
                    reject(e);
                });
        });

    return (
        <ModalComponent
            open={open}
            onClose={handleClose}
            title="Insert Image"
            okButtonText={uploading ? <Loader inline loading version="iconBlue" /> : "Confirm"}
            onSuccess={handleOnClick}
            okButtonDisabled={isDisabled}
            justifyButtons="flex-start"
            content={
                <Box
                    sx={{
                        display: "flex",
                        gap: 3,
                        flexDirection: "column",
                    }}>
                    <div>
                        {errorMessage && (
                            <AlertBanner
                                id="upload-error"
                                variant="error"
                                title={{text: "Something went wrong!"}}
                                subTitle={{text: errorMessage}}
                            />
                        )}
                        <MuiUpload
                            id="uploadFile"
                            file={file}
                            setFile={handleSetFile}
                            clearInputFile={clearInputFile}
                            maxSize={"1"}
                            type={MediaTypes.Image}
                            onError={setErrorMessage}
                            variant="sideBySide"
                            imagePreviewUrl={previewUrl}
                            setImagePreviewUrl={setPreviewUrl}
                            showPreviewInUpload
                        />
                    </div>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 2,
                            flexDirection: "column",
                        }}>
                        <Textbox
                            id="image-modal-alt-text-input"
                            label="Alt Text"
                            InputLabelProps={{
                                shrink: true,
                            }}
                            placeholder="Descriptive alternative text"
                            onChange={e => setAltText(e.target.value)}
                            value={altText}
                            new
                        />
                        {/* <MuiSelect
                            id="position-select"
                            value={position}
                            label="Position"
                            name="position"
                            onChange={e => setPosition(e)}
                            showDropdownIcon
                            options={[
                                {id: "left", name: "Left"},
                                {id: "right", name: "Right"},
                                {id: "full", name: "Full Width"},
                            ]}
                        /> */}
                        {/* <Box sx={{height: "20px", display: "flex", gap: 2, alignItems: "center"}}>
                            <Checkbox
                                id="caption"
                                checked={showCaption}
                                onChange={handleShowCaptionChange}
                                sx={{padding: "0"}}
                            />
                            <label htmlFor="caption">Show Caption</label>
                        </Box> */}
                    </Box>
                </Box>
            }
        />
    );
}

export default function InlineImagePlugin(): JSX.Element | null {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        if (!editor.hasNodes([InlineImageNode])) {
            throw new Error("ImagesPlugin: ImageNode not registered on editor");
        }

        return mergeRegister(
            editor.registerCommand<InsertInlineImagePayload>(
                INSERT_INLINE_IMAGE_COMMAND,
                payload => {
                    const imageNode = $createInlineImageNode(payload);
                    $insertNodes([imageNode]);
                    if ($isRootOrShadowRoot(imageNode.getParentOrThrow())) {
                        $wrapNodeInElement(imageNode, $createParagraphNode).selectEnd();
                    }

                    return true;
                },
                COMMAND_PRIORITY_EDITOR,
            ),
            editor.registerCommand<DragEvent>(
                DRAGSTART_COMMAND,
                event => {
                    return $onDragStart(event);
                },
                COMMAND_PRIORITY_HIGH,
            ),
            editor.registerCommand<DragEvent>(
                DRAGOVER_COMMAND,
                event => {
                    return $onDragover(event);
                },
                COMMAND_PRIORITY_LOW,
            ),
            editor.registerCommand<DragEvent>(
                DROP_COMMAND,
                event => {
                    return $onDrop(event, editor);
                },
                COMMAND_PRIORITY_HIGH,
            ),
        );
    }, [editor]);

    return null;
}

const TRANSPARENT_IMAGE = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
const img = document.createElement("img");
img.src = TRANSPARENT_IMAGE;

function $onDragStart(event: DragEvent): boolean {
    const node = $getImageNodeInSelection();
    if (!node) {
        return false;
    }
    const dataTransfer = event.dataTransfer;
    if (!dataTransfer) {
        return false;
    }
    dataTransfer.setData("text/plain", "_");
    dataTransfer.setDragImage(img, 0, 0);
    dataTransfer.setData(
        "application/x-lexical-drag",
        JSON.stringify({
            data: {
                altText: node.__altText,
                caption: node.__caption,
                height: node.__height,
                key: node.getKey(),
                showCaption: node.__showCaption,
                src: node.__src,
                width: node.__width,
            },
            type: "image",
        }),
    );

    return true;
}

function $onDragover(event: DragEvent): boolean {
    const node = $getImageNodeInSelection();
    if (!node) {
        return false;
    }
    if (!canDropImage(event)) {
        event.preventDefault();
    }
    return true;
}

function $onDrop(event: DragEvent, editor: LexicalEditor): boolean {
    const node = $getImageNodeInSelection();
    if (!node) {
        return false;
    }
    const data = getDragImageData(event);
    if (!data) {
        return false;
    }
    event.preventDefault();
    if (canDropImage(event)) {
        const range = getDragSelection(event);
        node.remove();
        const rangeSelection = $createRangeSelection();
        if (range !== null && range !== undefined) {
            rangeSelection.applyDOMRange(range);
        }
        $setSelection(rangeSelection);
        editor.dispatchCommand(INSERT_INLINE_IMAGE_COMMAND, data);
    }
    return true;
}

function $getImageNodeInSelection(): InlineImageNode | null {
    const selection = $getSelection();
    if (!$isNodeSelection(selection)) {
        return null;
    }
    const nodes = selection.getNodes();
    const node = nodes[0];
    return $isInlineImageNode(node) ? node : null;
}

function getDragImageData(event: DragEvent): null | InsertInlineImagePayload {
    const dragData = event.dataTransfer?.getData("application/x-lexical-drag");
    if (!dragData) {
        return null;
    }
    const {type, data} = JSON.parse(dragData);
    if (type !== "image") {
        return null;
    }

    return data;
}

declare global {
    interface DragEvent {
        rangeOffset?: number;
        rangeParent?: Node;
    }
}

function canDropImage(event: DragEvent): boolean {
    const target = event.target;
    return !!(
        target &&
        target instanceof HTMLElement &&
        !target.closest("code, span.editor-image") &&
        target.parentElement &&
        target.parentElement.closest("div.ContentEditable__root")
    );
}

function getDragSelection(event: DragEvent): Range | null | undefined {
    let range;
    const target = event.target as null | Element | Document;
    const targetWindow =
        target == null
            ? null
            : target.nodeType === 9
            ? (target as Document).defaultView
            : (target as Element).ownerDocument.defaultView;
    const domSelection = getDOMSelection(targetWindow);
    if (document.caretRangeFromPoint) {
        range = document.caretRangeFromPoint(event.clientX, event.clientY);
    } else if (event.rangeParent && domSelection !== null) {
        domSelection.collapse(event.rangeParent, event.rangeOffset || 0);
        range = domSelection.getRangeAt(0);
    } else {
        throw Error("Cannot get the selection when dragging");
    }

    return range;
}
