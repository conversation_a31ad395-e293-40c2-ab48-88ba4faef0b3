import {INSERT_CHECK_LIST_COMMAND, INSERT_ORDERED_LIST_COMMAND, INSERT_UNORDERED_LIST_COMMAND} from "@lexical/list";
import {$setBlocksType} from "@lexical/selection";
import {ArrowDropDown, Checklist, FormatListBulleted, FormatListNumbered} from "@mui/icons-material";
import {$createParagraphNode, $getSelection, $isRangeSelection, LexicalEditor} from "lexical";
import MenuButton from "../../../../Molecules/MenuButton/menuButton.component";
import {TAllBlockTypes} from "./toolbarPlugin";

const listTypeToBlockName = {
    bullet: "Bulleted List",
    number: "Numbered List",
    check: "Check List",
};

export default function ListFormatDropDown({
    editor,
    listType,
    disabled = false,
}: {
    listType: TAllBlockTypes;
    editor: LexicalEditor;
    disabled?: boolean;
    value?: string;
}): JSX.Element {
    const dropDownActiveClass = (active: boolean) => (active ? "active dropdown-item-active" : "");
    const activeType = listType in listTypeToBlockName ? listType : "bullet";

    const listCommands = {
        bullet: INSERT_UNORDERED_LIST_COMMAND,
        number: INSERT_ORDERED_LIST_COMMAND,
        check: INSERT_CHECK_LIST_COMMAND,
    };

    const formatParagraph = () => {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                $setBlocksType(selection, () => $createParagraphNode());
            }
        });
    };

    const formatList = (listTypeSelected: keyof typeof listTypeToBlockName) => {
        if (listType !== listTypeSelected) {
            editor.dispatchCommand(listCommands[listTypeSelected], undefined);
        } else {
            formatParagraph();
        }
    };

    const listIcons = {
        bullet: <FormatListBulleted sx={{width: "24px", height: "24px", marginRight: "10px"}} color="secondary" />,
        number: <FormatListNumbered sx={{width: "24px", height: "24px", marginRight: "10px"}} color="secondary" />,
        check: <Checklist sx={{width: "24px", height: "24px", marginRight: "10px"}} color="secondary" />,
    };

    return (
        <MenuButton
            id="listFormatButton"
            buttonProps={{
                disabled: disabled,
                className: "toolbar-item spaced list-format",
                color: "secondary",
                "aria-label": "Formatting options for text style",
                sx: {positon: "relative"},
                tooltip: {
                    title: "List format options",
                },
                //@ts-expect-error - data-testid is not a valid prop for the Button component
                "data-testid": "rte-toolbar-list-format",
            }}
            items={[
                {
                    id: "bullet",
                    children: <>{listIcons.bullet} Bullet List</>,
                    onClick: () => formatList("bullet"),
                    customProps: {
                        className: "item " + dropDownActiveClass(listType === "bullet"),
                    },
                },
                {
                    id: "number",
                    children: <>{listIcons.number} Numbered List</>,
                    onClick: () => formatList("number"),
                    customProps: {
                        className: "item " + dropDownActiveClass(listType === "number"),
                    },
                },
                /* {
                    id: "check",
                    children: <>{listIcons.check} Checklist</>,
                    onClick: () => formatList("check"),
                    customProps: {
                        className: "item " + dropDownActiveClass(listType === "check"),
                    },
                }, */
            ]}>
            {listIcons[activeType]}
            <ArrowDropDown
                sx={{
                    width: "24px",
                    height: "24px",
                    pointerEvents: "none",
                    position: "absolute",
                    right: 0,
                    top: "50%",
                    transform: "translateY(-50%)",
                }}
                color="secondary"
            />
        </MenuButton>
    );
}
