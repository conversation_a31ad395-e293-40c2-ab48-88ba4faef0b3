import {$isDecoratorBlockNode} from "@lexical/react/LexicalDecoratorBlockNode";
import {$createHeadingNode, $isHeadingNode, $isQuoteNode, HeadingTagType} from "@lexical/rich-text";
import {$setBlocksType} from "@lexical/selection";
import {$getNearestBlockElementAncestorOrThrow} from "@lexical/utils";
import {ArrowDropDown, TextFormat} from "@mui/icons-material";
import {
    $createParagraphNode,
    $getSelection,
    $isRangeSelection,
    $isTextNode,
    FORMAT_TEXT_COMMAND,
    LexicalEditor,
} from "lexical";
import {useCallback} from "react";
import MenuButton from "../../../../Molecules/MenuButton/menuButton.component";
import {TAllBlockTypes} from "./toolbarPlugin";

export default function BlockFormatDropDown({
    editor,
    blockType,
    isSubscript,
    isSuperscript,
    disabled = false,
}: {
    blockType: TAllBlockTypes;
    editor: LexicalEditor;
    isSubscript: boolean;
    isSuperscript: boolean;
    disabled?: boolean;
}): JSX.Element {
    const dropDownActiveClass = (active: boolean) => (active ? "active dropdown-item-active" : "");

    const formatParagraph = () => {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                $setBlocksType(selection, () => $createParagraphNode());
            }
        });
    };

    const formatHeading = (headingSize: HeadingTagType) => {
        if (blockType !== headingSize) {
            editor.update(() => {
                const selection = $getSelection();
                $setBlocksType(selection, () => $createHeadingNode(headingSize));
            });
        }
    };

    const formatPosition = (position: "subscript" | "superscript") => {
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, position);
    };

    const clearFormatting = useCallback(() => {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                const anchor = selection.anchor;
                const focus = selection.focus;
                const nodes = selection.getNodes();
                const extractedNodes = selection.extract();

                if (anchor.key === focus.key && anchor.offset === focus.offset) {
                    return;
                }

                nodes.forEach((node, idx) => {
                    // We split the first and last node by the selection
                    // So that we don't format unselected text inside those nodes
                    if ($isTextNode(node)) {
                        // Use a separate variable to ensure TS does not lose the refinement
                        let textNode = node;
                        if (idx === 0 && anchor.offset !== 0) {
                            textNode = textNode.splitText(anchor.offset)[1] || textNode;
                        }
                        if (idx === nodes.length - 1) {
                            textNode = textNode.splitText(focus.offset)[0] || textNode;
                        }
                        /**
                         * If the selected text has one format applied
                         * selecting a portion of the text, could
                         * clear the format to the wrong portion of the text.
                         *
                         * The cleared text is based on the length of the selected text.
                         */
                        // We need this in case the selected text only has one format
                        const extractedTextNode = extractedNodes[0];
                        if (nodes.length === 1 && $isTextNode(extractedTextNode)) {
                            textNode = extractedTextNode;
                        }

                        if (textNode.__style !== "") {
                            textNode.setStyle("");
                        }
                        if (textNode.__format !== 0) {
                            textNode.setFormat(0);
                            $getNearestBlockElementAncestorOrThrow(textNode).setFormat("");
                        }
                        node = textNode;
                    } else if ($isHeadingNode(node) || $isQuoteNode(node)) {
                        node.replace($createParagraphNode(), true);
                    } else if ($isDecoratorBlockNode(node)) {
                        node.setFormat("");
                    }
                });
            }
        });
    }, [editor]);

    return (
        <MenuButton
            id="blockFormatButton"
            buttonProps={{
                disabled: disabled,
                className: "toolbar-item spaced block-format",
                color: "secondary",
                "aria-label": "Formatting options for text style",
                sx: {positon: "relative"},
                tooltip: {title: "Text format options"},
                //@ts-expect-error - data-testid is not a valid prop for the Button component
                "data-testid": "rte-toolbar-block-format",
            }}
            items={[
                {
                    id: "normal",
                    children: (
                        <>
                            <i className="icon paragraph" />
                            Normal
                        </>
                    ),
                    onClick: formatParagraph,
                    customProps: {
                        className: "item " + dropDownActiveClass(blockType === "paragraph"),
                    },
                },
                {
                    id: "h1",
                    children: (
                        <>
                            <i className="icon h1" />
                            Heading 1
                        </>
                    ),
                    onClick: () => formatHeading("h1"),
                    customProps: {
                        className: "item " + dropDownActiveClass(blockType === "h1"),
                    },
                },
                {
                    id: "h2",
                    children: (
                        <>
                            <i className="icon h2" />
                            Heading 2
                        </>
                    ),
                    onClick: () => formatHeading("h2"),
                    customProps: {
                        className: "item " + dropDownActiveClass(blockType === "h2"),
                    },
                },
                {
                    id: "h3",
                    children: (
                        <>
                            <i className="icon h3" />
                            Heading 3
                        </>
                    ),
                    onClick: () => formatHeading("h3"),
                    customProps: {
                        className: "item " + dropDownActiveClass(blockType === "h3"),
                    },
                },
                {
                    id: "subcript",
                    children: (
                        <>
                            <i className="icon subscript" />
                            Subscript
                        </>
                    ),
                    onClick: () => formatPosition("subscript"),
                    customProps: {
                        className: "item " + dropDownActiveClass(isSubscript),
                    },
                },
                {
                    id: "superscript",
                    children: (
                        <>
                            <i className="icon superscript" />
                            Superscript
                        </>
                    ),
                    onClick: () => formatPosition("superscript"),
                    customProps: {
                        className: "item " + dropDownActiveClass(isSuperscript),
                    },
                },
                {
                    id: "superscript",
                    children: (
                        <>
                            <i className="icon clear-format" />
                            Clear Formatting
                        </>
                    ),
                    onClick: clearFormatting,
                    customProps: {
                        className: "item",
                    },
                },
            ]}>
            <TextFormat sx={{width: "24px", height: "24px", marginRight: "10px"}} color="secondary" />
            <ArrowDropDown
                sx={{
                    width: "24px",
                    height: "24px",
                    pointerEvents: "none",
                    position: "absolute",
                    right: 0,
                    top: "50%",
                    transform: "translateY(-50%)",
                }}
                color="secondary"
            />
        </MenuButton>
    );
}
