/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {LexicalEditor} from "lexical";

import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {TextNode} from "lexical";
import {useEffect} from "react";
import {$createEmojiNode, EmojiNode} from "../../nodes/Emoji/emojiNode";
import findEmoji from "./findEmoji";

function findAndTransformEmoji(node: TextNode): null | TextNode {
    const text = node.getTextContent();

    const emojiMatch = findEmoji(text);
    if (emojiMatch === null) {
        return null;
    }

    let targetNode;
    if (emojiMatch.position === 0) {
        // First text chunk within string, splitting into 2 parts
        [targetNode] = node.splitText(emojiMatch.position + emojiMatch.shortcode.length);
    } else {
        // In the middle of a string
        [, targetNode] = node.splitText(emojiMatch.position, emojiMatch.position + emojiMatch.shortcode.length);
    }

    const emojiNode = $createEmojiNode(emojiMatch.unifiedID, emojiMatch.hasImage);
    targetNode.replace(emojiNode);
    return emojiNode;
}

function textNodeTransform(node: TextNode): void {
    let targetNode: TextNode | null = node;

    while (targetNode !== null) {
        if (!targetNode.isSimpleText()) {
            return;
        }

        targetNode = findAndTransformEmoji(targetNode);
    }
}

function useEmojis(editor: LexicalEditor): void {
    useEffect(() => {
        if (!editor.hasNodes([EmojiNode])) {
            throw new Error("EmojisPlugin: EmojiNode not registered on editor");
        }

        return editor.registerNodeTransform(TextNode, textNodeTransform);
    }, [editor]);
}

export default function EmojisPlugin(): JSX.Element | null {
    const [editor] = useLexicalComposerContext();
    useEmojis(editor);
    return null;
}
