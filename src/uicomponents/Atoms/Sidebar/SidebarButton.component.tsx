import {ArrowRight, LaunchOutlined} from "@mui/icons-material";
import ArrowDropDown from "@mui/icons-material/ArrowDropDown";
import {type SxProps, type Theme, useTheme} from "@mui/material";
import {type ReactNode, useEffect, useRef, useState} from "react";
import CustomLink from "../../../components/Links/CustomLink.component";
import OutboundLink from "../../../components/Links/OutboundLink.component";
import {CP_HELP_URL, CP_INFO_URL} from "../../../constants/commonStrings.constant";
import useWhitelabeling from "../../../hooks/fetches/useWhitelabeling";
import {useSubdomain} from "../../../hooks/useSubdomain";
import {TPermissionGroupsKeysArr} from "../../../Interfaces/features/features.interface";
import {getSubdomain} from "../../../utils/url.util";
import Badge, {IBadgeProps} from "../Badge/badge.component";
import Box from "../Box/Box.component";
import Button from "../Button/Button.Component";
import Menu from "../Menu/menu.component";
import Typography from "../Typography/Typography.component";
import {IS_CUSTOMER_BETTERTRACKER_SITE, IS_DIRECT_IT_SITE} from "../../../constants/siteFlags";

interface ISidebarButtonItems {
    [key: string]: ISidebarButtonProps;
}

type TPossibleFilters = {
    isAdmin?: boolean;
    hasPRM?: boolean;
    isPremium?: boolean;
    nonClaimer?: boolean;
    nonVendorClaimer?: boolean;
    mspClaimer?: boolean;
    hideForMspClient?: boolean;
    hasSupportContact?: boolean;
    onlyAffiliateMainCompany?: boolean;
    hasIntegrationAccess?: boolean;
    hidden?: boolean;
    manageClients?: boolean;
    hideForAffiliatedMSP?: boolean;
    isPlaidEnabled?: boolean;
    client_plaid_integration_enabled?: boolean;
    hideIfMSPAndPlaidSubscribedAndHasNoPermissions?: boolean;
    hasExpensesAccess?: boolean;
    hideForMSPParentInteractingAsCustomer?: boolean;
    hideForDirect?: boolean;
    hideForDirectSite?: boolean;
};

export interface ISidebarButtonProps {
    children: string;
    icon?: ReactNode;
    collapsed?: boolean;
    onClick?: () => void;
    sx?: SxProps<Theme>;
    href?: string;
    checkActiveHref?: string;
    subdomain?: string;
    items?: ISidebarButtonItems;
    badge?: {
        content: ReactNode;
        color?: IBadgeProps["color"];
    };
    permissions?: TPermissionGroupsKeysArr;
    active?: boolean;
    isSubItem?: boolean;
    containerSx?: SxProps<Theme>;
    filters?: TPossibleFilters;
    isExternalLink?: boolean;
    showExternalIcon?: boolean;
    allowEmptyItems?: boolean;
    hidden?: boolean;
    noSubdomainOnRedirect?: boolean;
    ignoreGrouping?: boolean;
}

export default function SidebarButton(props: ISidebarButtonProps) {
    const {
        children,
        onClick,
        sx,
        href,
        collapsed,
        items,
        subdomain,
        badge,
        isSubItem,
        containerSx,
        isExternalLink,
        showExternalIcon,
        noSubdomainOnRedirect,
        ignoreGrouping,
    } = props;
    const anchorEl = useRef<HTMLDivElement>(null);
    const theme = useTheme();
    const hasItems = items && Object.keys(items).length > 0;
    const subdomainObj = useSubdomain();
    const {whitelabeling} = useWhitelabeling();
    const active =
        (!!href &&
            (noSubdomainOnRedirect ? !subdomainObj.company : subdomain ? subdomain === subdomainObj.company : true) &&
            subdomainObj.isActive(href)) ||
        (!!items &&
            subdomainObj.isActive(
                Object.keys(items)
                    .map(key => items[key].href ?? "")
                    .filter(href => !!href),
            )) ||
        props.active;
    const [open, setOpen] = useState(active && !collapsed);
    const icon = props.icon ? (
        props.icon
    ) : isSubItem && active ? (
        <ArrowRight
            sx={{
                fontSize: "16px",
                color: IS_DIRECT_IT_SITE
                    ? "btBlue.secondary"
                    : whitelabeling?.accent_color
                      ? whitelabeling.accent_color
                      : "primary.main",
            }}
        />
    ) : null;
    //? Applies to open in new tab and dropdown icons
    const iconRightColor = IS_DIRECT_IT_SITE
        ? "neutral.700"
        : IS_CUSTOMER_BETTERTRACKER_SITE
          ? "neutral.800"
          : "neutral.500";
    const subItemContainerBg = IS_DIRECT_IT_SITE
        ? "neutral.200"
        : IS_CUSTOMER_BETTERTRACKER_SITE
          ? "neutral.100"
          : "secondary.900";

    useEffect(() => {
        if (collapsed) setOpen(false);
    }, [collapsed]);

    useEffect(() => {
        if (!active) setOpen(false);
    }, [active]);

    return (
        <Box ref={anchorEl} sx={containerSx}>
            <Box
                tooltip={
                    collapsed && !open && (ignoreGrouping ? !hasItems : true)
                        ? {
                              title: (
                                  <Typography
                                      fontInter
                                      sx={theme => ({
                                          fontSize: "14px !important",
                                          color: theme.palette.neutral[100],
                                          display: "flex",
                                          alignItems: "center",
                                          gap: 1,
                                      })}>
                                      {children}
                                      {collapsed && badge && (
                                          <Badge
                                              color={badge.color || "tonal"}
                                              padding="2px 6px"
                                              sx={{
                                                  color: `${theme.palette.secondary.main}!important`,
                                              }}>
                                              {badge.content}
                                          </Badge>
                                      )}
                                  </Typography>
                              ),
                              placement: "right",
                          }
                        : undefined
                }>
                {isExternalLink || (href && !subdomain && !hasItems) ? (
                    <SidebarButtonLink
                        {...props}
                        subdomain={subdomain || subdomainObj.company}
                        active={active}
                        onClick={hasItems ? () => setOpen(prev => !prev) : undefined}
                    />
                ) : ignoreGrouping && hasItems ? null : (
                    <Button
                        data-track="false"
                        id={
                            (subdomain ? `${subdomain}-${href}` : href) ||
                            `${children}`.toLowerCase().replaceAll(" ", "-")
                        }
                        type="button"
                        onClick={hasItems ? () => setOpen(prev => !prev) : onClick}
                        href={
                            subdomain
                                ? getSubdomain({
                                      defaultValues: {
                                          company: subdomain,
                                          pathname: href,
                                      },
                                  }).toString()
                                : undefined
                        }
                        sx={{
                            padding: "8px!important",
                            border: "none!important",
                            borderRadius: "0!important",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: collapsed ? "center" : "flex-start",
                            gap: collapsed ? 0 : 1,
                            width: collapsed ? "40px" : "100%",
                            height: collapsed ? "40px" : "auto",
                            boxSizing: "border-box",
                            minWidth: "unset",
                            pointerEvents: active && !hasItems ? "none" : undefined,
                            borderBottom:
                                active && !isSubItem
                                    ? `2px solid ${
                                          IS_DIRECT_IT_SITE
                                              ? theme.palette.btBlue["secondary"]
                                              : whitelabeling && whitelabeling?.accent_color
                                                ? whitelabeling?.accent_color
                                                : theme.palette.primary.main
                                      }!important`
                                    : undefined,
                            "&:hover,&:active,&:focus": {
                                backgroundColor: "rgba(255,255,255,0.2)",
                            },
                            "& .sidebar-icon": {
                                fill: `${
                                    active
                                        ? IS_DIRECT_IT_SITE
                                            ? theme.palette.btBlue["secondary"]
                                            : whitelabeling?.accent_color
                                              ? whitelabeling?.accent_color
                                              : theme.palette.primary.main
                                        : collapsed
                                          ? theme.palette.neutral[600]
                                          : theme.palette.neutral[500]
                                }`,
                                transition: "fill 0.2s ease-in-out",
                            },
                            ...(typeof sx === "function" ? sx(theme) : sx),
                        }}>
                        {icon}
                        <Typography
                            fontInter
                            variant="subtitle2"
                            sx={{
                                color: `${IS_DIRECT_IT_SITE || IS_CUSTOMER_BETTERTRACKER_SITE ? "neutral.800" : "neutral.100"}`,
                                fontWeight: active || open ? "700!important" : "400!important",
                                width: collapsed ? "0px" : "auto",
                                transition: "all 0.2s ease-in-out",
                                opacity: collapsed ? 0 : 1,
                                whiteSpace: "break-spaces",
                                textAlign: "left",
                            }}>
                            {children}
                        </Typography>
                        {!collapsed && badge && (
                            <Badge color={badge.color || "secondary"} padding="2px 6px">
                                {badge.content}
                            </Badge>
                        )}
                        {showExternalIcon && !collapsed ? (
                            <LaunchOutlined
                                sx={{
                                    maxHeight: "20px",
                                    marginLeft: "auto",
                                    color: iconRightColor,
                                }}
                            />
                        ) : null}
                        {hasItems && !collapsed ? (
                            <ArrowDropDown
                                sx={{
                                    maxHeight: "20px",
                                    marginLeft: "auto",
                                    transition: "transform 0.2s ease-in-out",
                                    transform: open ? "rotate(180deg)" : "rotate(0deg)",
                                    color: iconRightColor,
                                }}
                            />
                        ) : null}
                    </Button>
                )}
                {hasItems && ignoreGrouping ? (
                    Object.keys(items).map(item => {
                        const {
                            href: itemHref,
                            children: itemChildren,
                            onClick: onClickItem,
                            badge: itemBadge,
                            checkActiveHref: itemCheckActiveHref,
                            hidden,
                            icon,
                        } = items[item];
                        if (hidden) return null;
                        return (
                            <SidebarButton
                                key={item}
                                href={itemHref}
                                subdomain={subdomain}
                                onClick={onClickItem}
                                badge={itemBadge}
                                checkActiveHref={itemCheckActiveHref}
                                collapsed={collapsed}
                                icon={icon}
                                sx={{
                                    "&:hover,&:active,&:focus": {
                                        backgroundColor: `${
                                            IS_CUSTOMER_BETTERTRACKER_SITE
                                                ? "rgba(0, 0, 0, 0.05)"
                                                : "rgba(255, 255, 255, 0.2)"
                                        }`,
                                    },
                                }}>
                                {itemChildren}
                            </SidebarButton>
                        );
                    })
                ) : hasItems ? (
                    <Box
                        sx={{
                            transition: "max-height 0.35s ease-out",
                            maxHeight: open ? "30rem" : 0,
                            overflow: "hidden",
                            display: collapsed ? "none" : "block",
                        }}>
                        <Box
                            sx={{
                                borderRadius: open ? "0 0 8px 8px" : 0,
                                marginBottom: "8px",
                                padding: 1,
                                backgroundColor: subItemContainerBg,
                                display: "block",
                            }}>
                            {Object.keys(items).map(item => {
                                const {
                                    href: itemHref,
                                    children: itemChildren,
                                    onClick: onClickItem,
                                    badge: itemBadge,
                                    checkActiveHref: itemCheckActiveHref,
                                    hidden,
                                } = items[item];
                                if (hidden) return null;
                                return (
                                    <SidebarButton
                                        key={item}
                                        href={itemHref}
                                        subdomain={subdomain}
                                        collapsed={collapsed}
                                        onClick={onClickItem}
                                        isSubItem
                                        badge={itemBadge}
                                        checkActiveHref={itemCheckActiveHref}
                                        sx={{
                                            padding: "8px 16px",
                                            "&:hover,&:active,&:focus": {
                                                backgroundColor: `${
                                                    IS_CUSTOMER_BETTERTRACKER_SITE
                                                        ? "rgba(0, 0, 0, 0.05)"
                                                        : "rgba(255, 255, 255, 0.2)"
                                                }`,
                                            },
                                        }}>
                                        {itemChildren}
                                    </SidebarButton>
                                );
                            })}
                        </Box>
                    </Box>
                ) : null}
                {hasItems && (
                    <Menu
                        open={!!(open && collapsed)}
                        onClose={() => setOpen(false)}
                        anchorEl={anchorEl.current}
                        anchorOrigin={{
                            horizontal: "right",
                            vertical: "top",
                        }}
                        sx={{
                            display: collapsed ? "block" : "none",
                            "& .MuiPaper-root": {
                                background: IS_CUSTOMER_BETTERTRACKER_SITE
                                    ? theme.palette.neutral[300]
                                    : theme.palette.neutral[800],
                                padding: 1,
                            },
                        }}>
                        <Box
                            sx={{
                                padding: 1,
                            }}>
                            <Typography
                                fontInter
                                variant="subtitle2"
                                sx={{
                                    color: `${
                                        IS_CUSTOMER_BETTERTRACKER_SITE
                                            ? theme.palette.neutral[800]
                                            : theme.palette.neutral[100]
                                    } !important`,
                                    fontWeight: "400!important",
                                    transition: "all 0.2s ease-in-out",
                                    whiteSpace: "break-spaces",
                                }}>
                                {children}
                            </Typography>
                        </Box>
                        {Object.keys(items).map(item => {
                            const {
                                href: itemHref,
                                children: itemChildren,
                                onClick: onClickItem,
                                badge: itemBadge,
                                checkActiveHref: itemCheckActiveHref,
                                hidden,
                            } = items[item];
                            if (hidden) return null;
                            return (
                                <SidebarButton
                                    key={item}
                                    href={itemHref}
                                    subdomain={subdomain}
                                    onClick={onClickItem}
                                    isSubItem
                                    badge={itemBadge}
                                    checkActiveHref={itemCheckActiveHref}
                                    sx={{
                                        "& h6": {
                                            fontSize: "14px!important",
                                            ...(IS_DIRECT_IT_SITE && {color: theme.palette.neutral[100]}),
                                        },
                                        "&:hover,&:active,&:focus": {
                                            backgroundColor: "rgba(255,255,255,0.2)",
                                        },
                                    }}>
                                    {itemChildren}
                                </SidebarButton>
                            );
                        })}
                    </Menu>
                )}
            </Box>
        </Box>
    );
}

function SidebarButtonLink({href, isExternalLink, subdomain, noSubdomainOnRedirect, ...props}: ISidebarButtonProps) {
    const treatAsExternal = noSubdomainOnRedirect && !!subdomain;
    let finalHref =
        treatAsExternal || isExternalLink
            ? noSubdomainOnRedirect
                ? window.location.origin.replace(`${subdomain}.`, "") + href
                : href
            : subdomain
              ? getSubdomain({
                    defaultValues: {
                        company: subdomain,
                    },
                }).host + href
              : undefined;
    if (subdomain === "help") {
        finalHref = CP_HELP_URL;
    }
    if (subdomain === "info") {
        finalHref = CP_INFO_URL + href || "/";
    }
    return isExternalLink || treatAsExternal ? (
        <OutboundLink
            href={finalHref ?? "/"}
            target={treatAsExternal ? "_self" : "_blank"}
            data-track="true"
            style={{
                textDecoration: "none",
                cursor: "pointer",
                pointerEvents: props.active ? "none" : undefined,
            }}>
            <SidebarButton showExternalIcon {...props} />
        </OutboundLink>
    ) : (
        <CustomLink
            to={href ?? ""}
            subdomain={subdomain}
            data-track="true"
            style={{
                textDecoration: "none",
                cursor: "pointer",
                pointerEvents: !!!props.items && props.active ? "none" : undefined,
            }}>
            <SidebarButton subdomain={subdomain} {...props} />
        </CustomLink>
    );
}
