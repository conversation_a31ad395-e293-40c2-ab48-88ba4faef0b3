import {useTheme} from "@mui/material";
import {default as Mui<PERSON><PERSON>, type BoxProps} from "@mui/material/Box";
import {forwardRef} from "react";

type IProps = BoxProps;

/**
 * BorderedBox - Box that applies the default styles for a box
 * We apply, padding, border, border-radius, display flex, flex-direction column and gap
 * If you need to override the styles, you can pass a sx prop
 */
const BorderedBox = forwardRef<HTMLDivElement, IProps>(({children, sx, ...props}, ref) => {
    const theme = useTheme();
    return (
        <MuiBox
            ref={ref}
            {...props}
            sx={{
                border: "1px solid",
                borderColor: "neutral.300",
                borderRadius: 1,
                padding: 2,
                display: "flex",
                flexDirection: "column",
                gap: 3,
                backgroundColor: "neutral.100",
                ...(typeof sx === "function" ? sx(theme) : sx),
            }}>
            {children}
        </MuiBox>
    );
});

export default BorderedBox;
