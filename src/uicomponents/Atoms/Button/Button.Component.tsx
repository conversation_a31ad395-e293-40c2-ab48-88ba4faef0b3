import {<PERSON><PERSON>ropDown, Arrow<PERSON>ropUp} from "@mui/icons-material";
import {LinkProps, PopoverProps, useTheme} from "@mui/material";
import {ButtonProps, default as MuiButton} from "@mui/material/Button";
import {CSSProperties, useRef, useState, type ReactNode, forwardRef} from "react";
import {To} from "react-router-dom";
import CustomLink from "../../../components/Links/CustomLink.component";
import Loader from "../../../utils/loader";
import CustomPopover from "../../Molecules/CustomPopover/CustomPopover";
import CPTooltip, {DEFAULT_TOOLTIP, type ITooltip} from "../Tooltip/tooltip.component";

declare module "@mui/material/Button" {
    interface ButtonPropsVariantOverrides {
        primaryOrange: true;
        dashed: true;
        primaryButton: true;
        iconButton: true;
        chip: true;
        tonal: true;
        tonal_blue: true;
    }
    interface ButtonPropsColorOverrides {
        blue: true;
        neutral: true;
    }
}

export interface CustomMuiButtonProps extends ButtonProps {
    loading?: boolean;
    id: string;
    data_custom_properties?: string;
    tooltip?: ITooltip;
    popover?: {
        content: ReactNode;
        sx?: PopoverProps["sx"];
        anchor?: PopoverProps["anchorOrigin"];
        closeOnInnerClick?: boolean;
    };
    to?: To;
    target?: LinkProps["target"];
    showDropdownArrow?: boolean;
    disableTracking?: boolean;
    linkStyle?: CSSProperties;
}

const Button = forwardRef<HTMLButtonElement, CustomMuiButtonProps>(
    ({tooltip = DEFAULT_TOOLTIP, popover, to, target, onClick, showDropdownArrow, loading = false, ...props}, ref) => {
        const [openPopover, setOpenPopover] = useState(false);
        const btnRef = useRef<HTMLButtonElement | null>(null);
        const theme = useTheme();

        const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
            if (popover?.content) {
                setOpenPopover(!openPopover);
            }
            onClick?.(e);
        };

        const handleRef = (node: HTMLButtonElement) => {
            btnRef.current = node;
            if (ref) {
                if (typeof ref === "function") {
                    ref(node);
                } else {
                    ref.current = node;
                }
            }
        };

        return (
            <>
                {loading ? (
                    <MuiButton
                        {...props}
                        color={props.color}
                        onClick={e => e.preventDefault()}
                        endIcon={<></>}
                        data-custom-properties={props.data_custom_properties}
                        ref={handleRef}
                        data-track={props.disableTracking ? "false" : "true"}>
                        <Loader
                            inline
                            version={
                                ["primaryButton", "contained", "primaryOrange"].includes(props.variant ?? "contained")
                                    ? "iconWhite"
                                    : "iconBlue"
                            }
                            loading={loading}
                        />
                    </MuiButton>
                ) : (
                    <>
                        <CPTooltip {...tooltip}>
                            {to ? (
                                <CustomLink to={to} target={target} style={props.linkStyle}>
                                    <MuiButton
                                        {...props}
                                        color={props.color}
                                        ref={handleRef}
                                        onClick={handleClick}
                                        data-custom-properties={props.data_custom_properties}
                                        data-track={props.disableTracking ? "false" : "true"}>
                                        {props.children}
                                    </MuiButton>
                                </CustomLink>
                            ) : (
                                <MuiButton {...props} color={props.color} ref={handleRef} onClick={handleClick}>
                                    {props.children}
                                    {showDropdownArrow &&
                                        (openPopover ? (
                                            <ArrowDropUp htmlColor={theme.palette.secondary.main} fontSize="small" />
                                        ) : (
                                            <ArrowDropDown htmlColor={theme.palette.secondary.main} fontSize="small" />
                                        ))}
                                </MuiButton>
                            )}
                        </CPTooltip>
                        {!!popover?.content && (
                            <CustomPopover
                                id={props.id + "-popover"}
                                open={openPopover}
                                onClose={() => setOpenPopover(false)}
                                onClick={() => {
                                    if (popover.closeOnInnerClick) {
                                        setOpenPopover(false);
                                    }
                                }}
                                anchorEl={btnRef.current}
                                closeAfterTransition
                                anchorOrigin={
                                    popover?.anchor
                                        ? popover.anchor
                                        : {
                                              vertical: "bottom",
                                              horizontal: "left",
                                          }
                                }
                                sx={{
                                    "& .MuiPopover-paper": {
                                        borderRadius: 1,
                                        padding: "16px 8px",
                                        boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.15)",
                                    },
                                    ...(typeof popover.sx === "function" ? popover.sx(theme) : popover.sx),
                                }}
                                content={popover.content}
                            />
                        )}
                    </>
                )}
            </>
        );
    },
);

export default Button;
