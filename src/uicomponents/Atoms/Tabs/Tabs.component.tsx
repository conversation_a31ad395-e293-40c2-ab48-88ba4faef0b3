import {useTheme} from "@mui/material";
import {default as MuiTabs, type TabsProps} from "@mui/material/Tabs";
import {type SyntheticEvent} from "react";
import useSettings from "../../../hooks/useSettings";
import {generateSxFromProps} from "../../../utils/sx.util";

interface IProps<TValue extends string> extends TabsProps {
    value: TValue;
    onChange: (e: SyntheticEvent, value: TValue) => void;
}

export default function Tabs<TValue extends string>({sx, children, ...props}: IProps<TValue>) {
    const theme = useTheme();
    const {settings} = useSettings();
    const mode = settings.theme;
    return (
        <MuiTabs
            variant="scrollable"
            scrollButtons={false}
            textColor="secondary"
            indicatorColor="primary"
            {...props}
            sx={{
                color: theme.palette.secondary[mode],
                "& button": {
                    fontSize: "16px",
                },
                "& button.Mui-selected": {
                    fontWeight: "700 !important",
                },
                ...generateSxFromProps(sx, theme),
            }}>
            {children}
        </MuiTabs>
    );
}
