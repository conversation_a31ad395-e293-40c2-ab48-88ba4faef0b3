import {IChannelDealData} from "../../../Interfaces/channelDeal.interface";
import {AxiosError} from "axios";
import DeleteOutlineOutlined from "@mui/icons-material/DeleteOutlineOutlined";
import Typography from "../Typography/Typography.component";
import {channelDealService} from "../../../services/channelDealService";
import ModalComponent from "../../Molecules/Modal/Modal.component";

interface IProps {
    channelDeal: IChannelDealData;
    onSuccess?: () => void;
    onClose?: () => void;
    onError?: (error: AxiosError<any>) => void;
}

const ChannelDealDeleteDialog = ({channelDeal, onSuccess, onClose, onError}: IProps) => {
    const handleOnSuccess = () => {
        channelDealService
            .deleteChannelDeal(channelDeal.id)
            .then(() => {
                onSuccess && onSuccess();
            })
            .catch(err => onError && onError(err));
    };

    return (
        <ModalComponent
            title={"Delete Deal?"}
            open={true}
            onClose={() => onClose && onClose()}
            modalTheme="error"
            onSuccess={handleOnSuccess}
            justifyButtons="flex-start"
            okButtonText={"Delete"}
            cancelButtonText={"CANCEL"}
            okButtonStartIcon={<DeleteOutlineOutlined sx={{fontSize: "18px"}} />}
            content={
                <>
                    <Typography fontInter>
                        Are you sure you want to delete <b>{channelDeal.deal_name}</b> for{" "}
                        <b>{channelDeal.company_name}</b>?
                    </Typography>

                    <Typography fontInter>
                        This action is permanent and cannot be undone. Please confirm if you'd like to proceed.
                    </Typography>
                </>
            }
        />
    );
};
export default ChannelDealDeleteDialog;
