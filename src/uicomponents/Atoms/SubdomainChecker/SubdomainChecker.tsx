import {useLocation} from "react-router-dom";
import {getSubdomain} from "../../../utils/url.util";
import {useEffect} from "react";
import {
    AFFILIATE_WHITELABEL_HOSTS,
    AFFILIATE_WHITELABEL_IGNORE,
    DEFAULT_SUBDOMAINS,
    HTTP_PROTOCOLS,
    MAIN_HOSTS,
} from "../../../constants/commonStrings.constant";

/**
 * SubdomainChecker
 *
 * If the host has more than one subdomain and the subdomain is not in the
 * allowed parts, the component will redirect the user to the route with
 * only one subdomain.
 */
const SubdomainChecker = () => {
    const location = useLocation();

    useEffect(() => {
        const host = window.location.host;
        const parts = host.split(".");
        const allowedParts = [
            ...HTTP_PROTOCOLS,
            ...AFFILIATE_WHITELABEL_IGNORE,
            ...DEFAULT_SUBDOMAINS,
            ...(MAIN_HOSTS?.flatMap(domain => domain.split(".")) || []),
            ...(AFFILIATE_WHITELABEL_HOSTS?.flatMap(domain => domain.split(".")) || []),
        ];
        const subdomains = parts.filter(part => {
            return !allowedParts.includes(part);
        });
        if (subdomains.length > 1) {
            //? Multiple subdomains not allowed redirect the user to route with only 1 subdomain
            const first = subdomains[0];
            const newHostParts = parts.filter(part => allowedParts.includes(part));
            const newHost = [first, ...newHostParts].join(".");
            const newUrl = window.location.href.replace(window.location.host, newHost);
            window.location.href = newUrl;
        }
    }, [location]);

    return null;
};

export default SubdomainChecker;
