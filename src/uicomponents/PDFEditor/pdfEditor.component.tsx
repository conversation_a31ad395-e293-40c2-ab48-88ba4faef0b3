import {useEffect, useRef, useState} from "react";
import {pdfjs} from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import {v4 as uuidv4} from "uuid";
import {assetTypes} from "../../constants/documentRebrandTypes.constant";
import SinglePage from "./SinglePage.component";
import ModifyPage from "./modifyPage.component";
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface IProps {
    pdf?: string;
    buttonType?: "download" | "saveCopy";
    pageNumber?: number;
    setNumberPages?: (num: number) => any;
    saveCopyCallback?: Function;
    downloadName?: string;
    mspId?: string;
    setButtonStatus: any;
    vendorId?: string;
}

export interface IResult {
    uid: string;
    id: string;
    x: number;
    y: number;
    rx: number;
    ry: number;
    text?: string;
    src?: string;
    type: "image" | "text" | string;
    ref?: any;
    page?: number;
    imgType?: string;
    width?: number;
    height?: number;
    isCopiedOver?: boolean;
    minWidth?: number;
    minHeight?: number;
    fontFamily?: string;
    color?: string;
    fontSize?: string;
}

const PDFEditor = (props: IProps) => {
    const {pageNumber} = props;
    const [result, setResult] = useState<{[key: string]: IResult}>({});
    const setResultTimeout = useRef<any>(undefined);
    const [bounds, setBounds] = useState({});
    const [buttonType, setButtonType] = useState("");
    const pdfDimensions = useRef({width: 0, height: 0});

    const getBounds = obj => {
        setBounds(obj);
    };

    const resetButtonType = () => {
        setButtonType("");
        props.setButtonStatus && props.setButtonStatus("");
    };

    const getPaths = () => {
        // setResult(res => [...res, el]);
    };

    const measureText = (
        text: string,
        fontSize: string = "16px",
        fontFamily: string = "roc-grotesk, arial, verdana",
    ): {width: number; height: number} => {
        const div = document.createElement("div");
        div.style.position = "absolute";
        div.style.top = "-9999px";
        div.style.left = "-9999px";
        div.style.font = `${fontSize} ${fontFamily}`;
        div.style.whiteSpace = "pre";
        document.body.appendChild(div);
        div.innerText = text;
        const dimensions: {width: number; height: number} = {width: div.clientWidth, height: div.clientHeight + 24};
        document.body.removeChild(div);
        return dimensions;
    };

    const handleDrop = async e => {
        const data = e.dataTransfer.getData("text");
        const savedObj = e.dataTransfer.getData("data");
        const parsed = JSON.parse(savedObj);
        const assetType = parsed.content && parsed.title ? assetTypes.TEXT : assetTypes.IMAGE;
        const x = e.pageX;
        const y = e.pageY - 10;
        const rx = e.nativeEvent?.offsetX;
        const ry = e.nativeEvent?.offsetY;
        if (assetType === assetTypes.TEXT) {
            const dimensions = measureText(parsed.content);
            const newUid = uuidv4();
            const textOverflowHeight = pdfDimensions.current.height - (ry + dimensions.height);
            const textOverflowWidth = pdfDimensions.current.width - (rx + dimensions.width);
            const newResult: IResult = {
                uid: newUid,
                id: newUid,
                x: textOverflowWidth < 0 ? x + textOverflowWidth : x,
                y: textOverflowHeight < 0 ? y + textOverflowHeight : y,
                src: data,
                page: pageNumber,
                type: assetTypes.TEXT,
                rx: textOverflowWidth < 0 ? rx + textOverflowWidth : rx,
                ry: textOverflowHeight < 0 ? ry + textOverflowHeight : ry,
                imgType: "",
                ref: 50,
                width: dimensions.width > 100 ? dimensions.width : 100,
                height: dimensions.height > 50 ? dimensions.height : 50,
                text: parsed.content,
                minWidth: dimensions.width,
                minHeight: dimensions.height,
            };
            setResult({
                ...result,
                [newResult.uid]: newResult,
            });
            return;
        } else if (assetType === assetTypes.IMAGE) {
            const imgDimensions: any = JSON.parse(e.dataTransfer.getData("dimensions"));
            if (!imgDimensions) return;
            const aspectRatio = imgDimensions.width / imgDimensions.height;
            const availableImgHeight = pdfDimensions.current.height - ry;
            const availableImgWidth = pdfDimensions.current.width - rx;
            let renderedWidth = imgDimensions.width;
            let renderedHeight = imgDimensions.height;
            if (imgDimensions.width > availableImgWidth || imgDimensions.height > availableImgHeight) {
                if (imgDimensions.width > availableImgWidth && imgDimensions.height > availableImgHeight) {
                    const widthExcessRatio = imgDimensions.width / availableImgWidth;
                    const heightExcessRatio = imgDimensions.height / availableImgHeight;

                    if (widthExcessRatio > heightExcessRatio) {
                        // Width exceeds its boundary more than height
                        renderedWidth = availableImgWidth;
                        renderedHeight = availableImgWidth / aspectRatio;
                    } else {
                        // Height exceeds its boundary more than width
                        renderedHeight = availableImgHeight;
                        renderedWidth = availableImgHeight * aspectRatio;
                    }
                } else if (imgDimensions.width > availableImgWidth) {
                    renderedWidth = availableImgWidth;
                    renderedHeight = availableImgWidth / aspectRatio;
                } else if (imgDimensions.height > availableImgHeight) {
                    renderedHeight = availableImgHeight;
                    renderedWidth = availableImgHeight * aspectRatio;
                }
            }
            const newResult: IResult = {
                uid: uuidv4(),
                id: parsed.media[0]?.id,
                x,
                y,
                src: data,
                page: pageNumber,
                type: "image",
                rx,
                ry,
                imgType: parsed.media[0]?.mime_type,
                ref: 50,
                width: renderedWidth,
                height: renderedHeight,
            };
            setResult({
                ...result,
                [newResult.uid]: newResult,
            });
        }
    };

    const debouncedSetResult = (newResults: any) => {
        if (setResultTimeout.current) {
            clearTimeout(setResultTimeout.current);
        }
        setResultTimeout.current = setTimeout(() => {
            setResult(newResults);
            setResultTimeout.current = undefined;
        }, 1000);
    };

    useEffect(() => {
        setButtonType(props.buttonType || "");
    }, [props.buttonType]);

    useEffect(() => {
        if (props.pdf) {
            setResult({});
        }
    }, [props.pdf]);

    return (
        <>
            <SinglePage
                resetButtonType={resetButtonType}
                buttonType={buttonType}
                pdf={props.pdf}
                getPaths={getPaths}
                getBounds={getBounds}
                onDrop={handleDrop}
                result={result}
                setResult={setResult}
                setNumberPages={props.setNumberPages}
                pageNumber={pageNumber || 0}
                debouncedSetResult={debouncedSetResult}
                setPdfDimensions={(d: any) => (pdfDimensions.current = d)}
            />
            <ModifyPage
                pdf={props.pdf}
                buttonType={buttonType}
                resetButtonType={resetButtonType}
                result={result}
                bounds={bounds}
                saveCopyCallback={props.saveCopyCallback}
                downloadName={props.downloadName}
                mspId={props.mspId || ""}
                vendorId={props.vendorId || ""}
            />
        </>
    );
};

export default PDFEditor;
