import {useTheme} from "@mui/material";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import React, {Ref, forwardRef} from "react";

interface IProps {
    title?: string | any;
    subtitle?: string | any;
    headerActions?: React.ReactNode;
    headerAvatar?: React.ReactNode;
    headerStyles?: any;
    children?: any;
    cardActions?: React.ReactNode;
    backgroundColor?: string;
    headerClassName?: string;
    className?: string;
    contentStyles?: any;
    sx?: any;
    ref?: any;
    elevation?: number;
}

const CardComponent = forwardRef((props: IProps, ref: Ref<any>) => {
    const theme = useTheme();
    return (
        <Card
            ref={ref}
            className={props.className}
            elevation={props?.elevation ?? 4}
            sx={props.sx}
            style={{
                backgroundColor: props.backgroundColor,
                borderRadius: "8px",
                border: `1px solid ${theme.palette.neutral[300]}`,
                ...(props.sx || {}),
            }}
            variant="elevation">
            <CardHeader
                title={props.title}
                subheader={props.subtitle}
                action={props.headerActions}
                avatar={props.headerAvatar}
                className={props.headerClassName}
                sx={props.headerStyles}
            />
            <CardContent sx={{position: "relative", ...(props.contentStyles || {})}}>{props.children}</CardContent>
            {props.cardActions && <CardActions>{props.cardActions}</CardActions>}
        </Card>
    );
});

export default CardComponent;
