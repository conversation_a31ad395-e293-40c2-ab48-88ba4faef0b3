import Typography from "@mui/material/Typography";
import {ReactNode} from "react";

interface IProps {
    position?: "start" | "end" | "center";
    breakpoint?: string;
    size?: "lg" | "md" | "sm" | "xs" | "xxs" | 24 | 18 | 16 | 14 | 12;
    text?: string | ReactNode;
    className?: string;
    color?: string;
    fontWeight?: string | number;
    typographyStyles?: any;
    wrapperStyles?: any;
    id?: string;
    sx?: any;
    title?: string;
}

const ParagraphComponent = (props: IProps) => {
    const align = () => {
        if (props.position) {
            if (props.position === "start") {
                return "align-items-start text-start";
            }
            if (props.position === "end") {
                return "align-items-end text-end";
            }
            if (props.position === "center") {
                return "align-items-center text-center";
            }
        }
        if (props.position) {
            if (props.position === "start") {
                return `align-items-center align-items-${props.breakpoint || "lg"}-start text-center text-${
                    props.breakpoint || "lg"
                }-start`;
            }
            if (props.position === "end") {
                return `align-items-center align-items-${props.breakpoint || "lg"}-end text-center text-${
                    props.breakpoint || "lg"
                }-start`;
            }
            if (props.position === "center") {
                return "align-items-center text-center";
            }
        } else {
            return "align-items-center text-center";
        }
    };
    const sizeVariant = (): any => {
        if (props.size === "lg" || props.size === 24) return "body1";
        if (props.size === "md" || props.size === 18) return "body2";
        if (props.size === "sm" || props.size === 16) return "body3";
        if (props.size === "xs" || props.size === 14) return "body4";
        if (props.size === "xxs" || props.size === 12) return "body5";
        else {
            return "body1";
        }
    };
    return (
        <div id={props.id} className={`${align()} ${props.className}`} style={props.wrapperStyles} title={props.title}>
            <Typography
                variant={sizeVariant()}
                color={props.color}
                fontWeight={props.fontWeight}
                style={props.typographyStyles}
                sx={props.sx}>
                {props.text}
            </Typography>
        </div>
    );
};

ParagraphComponent.defaultProps = {
    color: "#000",
};

export default ParagraphComponent;
