import Box from "../../Atoms/Box/Box.component";

interface IProps {
    size: number;
}
export default function MDFIcon({size}: IProps) {
    return (
        <Box
            data-testid="mdf-icon-container"
            sx={{
                width: size,
                height: size,
            }}>
            <img
                src="/Media/icons/handcoin.png"
                alt="handcoin"
                style={{width: "100%", height: "100%", verticalAlign: "top"}}
            />
        </Box>
    );
}
