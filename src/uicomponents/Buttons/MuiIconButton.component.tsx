import IconButton from "@mui/material/IconButton";
import ReactIcon from "../../components/Icons/reactIcon.component";

interface IProps {
    variant?: "text" | "outlined" | "contained";
    color?: string;
    size?: "xl" | "lg" | "md" | "sm" | "xs" | 52 | 48 | 40 | 36 | 32;
    children?: any;
    disabled?: boolean;
    type?: "button" | "submit" | "reset";
    onClick?: any;
    icon: string | any;
    id: string;
    style?: any;
    backgroundColor?: string;
    iconSize?: string;
    title?: string;
    data_custom_properties?: string;
    disableTrack?: boolean;
}

const MuiIconButtonComponent = (props: IProps) => {
    const {color, disabled} = props;
    const Icon =
        typeof props.icon === "string" ? <ReactIcon iconName={props.icon} size={props.iconSize} /> : props.icon;
    const size: any =
        typeof props.size === "string"
            ? props.size
            : props.size === 52
            ? "xl"
            : props.size === 48
            ? "lg"
            : props.size === 40
            ? "md"
            : props.size === 36
            ? "sm"
            : "xs";
    return (
        <IconButton
            size={size}
            color={color as any}
            disabled={disabled}
            id={props.id}
            onClick={props.onClick}
            title={props.title}
            data-custom-properties={props.data_custom_properties}
            data-track={props.disableTrack ? false : true}
            sx={
                props.style || props.backgroundColor
                    ? props.style
                        ? props.style
                        : {backgroundColor: `${props.backgroundColor} !important`}
                    : {}
            }>
            {Icon}
        </IconButton>
    );
};

MuiIconButtonComponent.defaultProps = {
    variant: "contained",
    color: "primaryOrange",
    size: "md",
    type: "button",
};

export default MuiIconButtonComponent;
