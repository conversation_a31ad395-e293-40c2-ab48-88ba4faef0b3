import React from "react";
import {Link} from "react-router-dom";
import MuiIconButtonComponent from "../MuiIconButton.component";

interface IProps {
    to: string;
    children: React.ReactNode;
    link?: string;
    id: string;
}

const MuiLinkButton = (props: IProps) => {
    return (
        <Link to={props.to}>
            <span>{props.link}</span>
            <MuiIconButtonComponent id={props.id} icon="" />
        </Link>
    );
};

export default MuiLinkButton;
