import {render, screen} from "@testing-library/react";
import {describe, expect, it} from "vitest";
import TestWrapper from "../../../__test_utils__/TestWrapper";
import MdfActiveBadge from "./mdfActiveBadge.component";

describe("MdfActiveBadge", () => {
    const renderComponent = (version?: "short" | "long") =>
        render(
            <TestWrapper>
                <MdfActiveBadge version={version} />
            </TestWrapper>,
        );

    it("renders correctly", () => {
        renderComponent();
        expect(screen.getByTestId("mdf-badge")).toBeTruthy();
    });

    it('displays "MDF" when version is "short"', () => {
        renderComponent("short");
        expect(screen.getByText("MDF")).toBeTruthy();
    });

    it('displays "Marketing Development Funds" when version is "long"', () => {
        renderComponent("long");
        expect(screen.getByText("Marketing Development Funds")).toBeTruthy();
    });

    it("includes an image with the correct src attribute", () => {
        renderComponent();
        const image = screen.getByAltText("handcoin");
        expect(image).property("src").contains("/Media/icons/handcoin.png");
    });
});
