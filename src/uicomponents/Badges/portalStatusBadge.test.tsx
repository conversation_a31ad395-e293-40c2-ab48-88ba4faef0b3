import {render} from "@testing-library/react";
import {describe, expect, it} from "vitest";
import TestWrapper from "../../../__test_utils__/TestWrapper";
import {PARTNER_INVITE_STATUS, PartnerInviteStatusType} from "../../constants/partnerInviteStatus.constant";
import PortalStatusBadge from "./portalStatusBadge.component";

describe("PortalStatusBadge", () => {
    const renderComponent = (status?: PartnerInviteStatusType, reason?: string) =>
        render(
            <TestWrapper>
                <PortalStatusBadge partner_status={status} reason={reason} />
            </TestWrapper>,
        );

    it('should display "Portal Unavailable" for REJECTED status', () => {
        const {getByText} = renderComponent(PARTNER_INVITE_STATUS.REJECTED);
        expect(getByText("Portal Unavailable")).toBeTruthy();
    });

    it('should display "Portal Request Sent" for INVITED status', () => {
        const {getByText} = renderComponent(PARTNER_INVITE_STATUS.INVITED);
        expect(getByText("Portal Request Sent")).toBeTruthy();
    });

    it('should display "Portal Request Sent" for REQUESTED status', () => {
        const {getByText} = renderComponent(PARTNER_INVITE_STATUS.REQUESTED);
        expect(getByText("Portal Request Sent")).toBeTruthy();
    });

    it('should display "Accepted" or reason for ACCEPTED status', () => {
        const {getByText} = renderComponent(PARTNER_INVITE_STATUS.ACCEPTED, "Custom Reason");
        expect(getByText("Custom Reason")).toBeTruthy();
    });

    it("should return null when partner_status is not provided", () => {
        const {container} = renderComponent();
        expect(container.firstChild).toBeNull();
    });
});
