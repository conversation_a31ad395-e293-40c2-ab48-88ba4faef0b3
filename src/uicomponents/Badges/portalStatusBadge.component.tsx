import {FiberManualRecord} from "@mui/icons-material";
import {useTheme} from "@mui/material";
import {PARTNER_INVITE_STATUS, PartnerInviteStatusType} from "../../constants/partnerInviteStatus.constant";
import Badge, {IBadgeProps} from "../Atoms/Badge/badge.component";

interface IProps extends Omit<IBadgeProps, "color" | "customColors"> {
    partner_status?: PartnerInviteStatusType;
    reason?: string;
}

const PortalStatusBadge = (props: IProps) => {
    const {partner_status, ...badgeProps} = props;
    const theme = useTheme();
    if (!partner_status) return null;
    const color =
        partner_status === PARTNER_INVITE_STATUS.REJECTED
            ? theme.palette.error[600]
            : partner_status === PARTNER_INVITE_STATUS.INVITED || partner_status === PARTNER_INVITE_STATUS.REQUESTED
            ? theme.palette.warning[600]
            : partner_status === PARTNER_INVITE_STATUS.ACCEPTED
            ? theme.palette.success[700]
            : theme.palette.error[600];
    return (
        <>
            <Badge
                color="neutral"
                size={11}
                padding="4px 8px"
                style={{
                    background: theme.palette.neutral[100],
                    border: `1px solid ${theme.palette.neutral[300]}`,
                }}
                sx={{
                    color: `${theme.palette.neutral[700]} !important`,
                    fontWeight: 600,
                    display: "flex",
                    gap: 0.5,
                    alignItems: "center",
                }}
                {...badgeProps}>
                <FiberManualRecord
                    sx={{
                        fill: color,
                    }}
                />
                {partner_status === PARTNER_INVITE_STATUS.REJECTED
                    ? "Portal Unavailable"
                    : partner_status === PARTNER_INVITE_STATUS.INVITED ||
                      partner_status === PARTNER_INVITE_STATUS.REQUESTED
                    ? "Portal Request Sent"
                    : partner_status === PARTNER_INVITE_STATUS.ACCEPTED
                    ? props.reason || "Accepted"
                    : ""}
            </Badge>
        </>
    );
};

export default PortalStatusBadge;
