import {TStatusKey} from "../../Interfaces/channelDeal.interface";
import {
    CHANNEL_DEALS_CLAIMED_APPROVED,
    CHANNEL_DEALS_CLAIMED_DECLINED,
    CHANNEL_DEALS_CLAIMED_PENDING,
} from "../../constants/channeldeals.constant";
import Badge, {TBadgeColor} from "../Atoms/Badge/badge.component";
import useChannelDealRequestStatuses from "../../hooks/fetches/useChannelDealRequestStatuses";

interface IProps {
    statusKey: TStatusKey | string;
}

const colors = {
    [CHANNEL_DEALS_CLAIMED_APPROVED]: "success",
    [CHANNEL_DEALS_CLAIMED_PENDING]: "warning",
    [CHANNEL_DEALS_CLAIMED_DECLINED]: "error",
};

const ChannelDealRequestStatusBadge = ({statusKey}: IProps) => {
    const {getStatus} = useChannelDealRequestStatuses();
    const status = getStatus(statusKey);

    return (
        <Badge color={colors[status?.value_key || ""] as TBadgeColor} size={14}>
            {status?.name}
        </Badge>
    );
};

export default ChannelDealRequestStatusBadge;
