import useTheme from "@mui/material/styles/useTheme";
import {DEAL_STATUS, DEAL_STATUS_LABELS, DEAL_STATUS_TYPES} from "../../constants/dealRegistrations.constant";
import Badge from "../Atoms/Badge/badge.component";

const DealRegistrationStatusBadge = ({status}: {status: DEAL_STATUS_TYPES | undefined | null}) => {
    const theme = useTheme();
    return (
        <Badge
            color={
                status === DEAL_STATUS.APPROVED
                    ? "success"
                    : status === DEAL_STATUS.DECLINED
                    ? "error"
                    : status === DEAL_STATUS.INFO_REQUESTED
                    ? "blue"
                    : status === DEAL_STATUS.WITHDRAWN
                    ? "neutral"
                    : "warning"
            }
            title={status ? DEAL_STATUS_LABELS[status] : ""}
            sx={
                status === DEAL_STATUS.PENDING
                    ? {
                          color: `${theme.palette.warning[800]}`,
                      }
                    : undefined
            }
            style={
                status === DEAL_STATUS.WITHDRAWN
                    ? {
                          background: `${theme.palette.neutral[200]}`,
                      }
                    : {}
            }>
            {status ? DEAL_STATUS_LABELS[status] : "Not Set"}
        </Badge>
    );
};

export default DealRegistrationStatusBadge;
