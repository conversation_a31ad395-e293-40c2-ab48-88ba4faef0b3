import Badge, {IBadgeProps} from "../Atoms/Badge/badge.component";
import FileDownloadDoneIcon from "@mui/icons-material/FileDownloadDone";
import WarningIcon from "@mui/icons-material/Warning";

interface IProps extends Omit<IBadgeProps, "color" | "customColors"> {
    is_using_stack: boolean;
    installedText?: string;
    notInstalledText?: string;
}

const ClientUsageBadge = (props: IProps) => {
    const {is_using_stack, ...badgeProps} = props;
    return (
        <Badge color={is_using_stack ? "success" : "warning"} size={11} padding="2px 6px" {...badgeProps}>
            {is_using_stack ? (
                <>
                    <FileDownloadDoneIcon color="success" sx={{height: 12, width: 12}} fontSize="small" />
                    {props.installedText}
                </>
            ) : (
                <>
                    <WarningIcon color="warning" sx={{height: 12, width: 12}} fontSize="small" />
                    {props.notInstalledText}
                </>
            )}
        </Badge>
    );
};

ClientUsageBadge.defaultProps = {
    installedText: "Installed",
    notInstalledText: "Not Installed",
};

export default ClientUsageBadge;
