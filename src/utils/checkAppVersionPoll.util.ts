const POLL_INTERVAL_MS = 60000;
let currentVersion: string | null = null;

async function checkForUpdate() {
    try {
        const res = await fetch("/meta.json", {cache: "no-store"});
        const {version} = await res.json();

        if (!currentVersion) {
            currentVersion = version;
            return;
        }

        if (version !== currentVersion) {
            console.info("VC:: New version detected. Reloading...");
            window.location.reload();
        }
    } catch (e) {
        console.error("VC:: Error checking for version update", e);
    }
}

checkForUpdate();
setInterval(checkForUpdate, POLL_INTERVAL_MS);
