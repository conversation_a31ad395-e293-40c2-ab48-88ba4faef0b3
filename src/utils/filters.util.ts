import type {
    INewFilters,
    IPageFiltersResponse,
    IPageInnerHeadFilter,
    IPageSortsResponse,
} from "../Interfaces/filters.interface";
import {TFiltersQueryParams} from "../hooks/fetches/useFilters";
import type {IMenuButtonItem} from "../uicomponents/Molecules/MenuButton/menuButton.component";

const nullItem = {
    id: null,
    name: "Empty",
    is_hidden: false,
    handle: "",
    friendly_url: "",
    avatar: "",
    user_profile_type: "",
    profile_link: "",
};

export function buildFilters(rawFilters: IPageFiltersResponse, rawSorts: IPageSortsResponse) {
    const standardizedFiltersArr = rawFilters
        ? Object.entries(rawFilters)
              .map(([id, filter]) => {
                  if (filter.is_nullable && filter.is_nullable === true && Array.isArray(filter.items)) {
                      filter.items.push(nullItem);
                  }
                  return [
                      id,
                      {
                          ...filter,
                          items: filter.is_date
                              ? []
                              : Array.isArray(filter.items)
                              ? filter.items
                                    .map(i => {
                                        const finalItem: IMenuButtonItem = {
                                            id: i.id,
                                            children: ("name" in i ? i.name : i.title) || "",
                                            image: ("avatar" in i ? i.avatar : i.image) || undefined,
                                            type: i.type ?? undefined,
                                            type_is_of_vendor: i.type_is_of_vendor ?? undefined,
                                            profileType: i.profileType ?? undefined,
                                        };
                                        return finalItem;
                                    })
                                    .sort((a, b) => (filter.is_duration ? Number(a.id) - Number(b.id) : 0))
                              : Object.entries(filter?.items || []).map(([filterValue, filterLabel]) => ({
                                    id: filterValue,
                                    children: filterLabel,
                                })),
                      },
                  ];
              })
              .sort((a, b) =>
                  (a[1] as {placeholder: string}).placeholder.localeCompare(
                      (b[1] as {placeholder: string}).placeholder,
                  ),
              )
        : [];

    const sortFilters: IPageInnerHeadFilter = rawSorts
        ? {
              sort: {
                  multiple: false,
                  placeholder: "Sort",
                  is_public: true,
                  is_sorting: true,
                  items: Object.entries(rawSorts).flatMap(([id, filter]) => {
                      const itemsPerOption = Object.entries(filter.items).map(([filterValue, filterLabel]) => ({
                          id: `sorting_${id}__${filterValue}`,
                          children: filterLabel,
                      }));
                      return itemsPerOption;
                  }),
              },
          }
        : {};
    const standardizedFilters: IPageInnerHeadFilter = Object.fromEntries(standardizedFiltersArr);
    return {
        ...(sortFilters?.sort?.items.length ? sortFilters : {}),
        ...standardizedFilters,
    } as IPageInnerHeadFilter;
}

/**
 * Remove undefined and empty arrays from filters so we can give back to react-query and API
 * @param {IPageInnerHeadFilter} filters
 * @returns {IPageInnerHeadFilter} clearedFilters
 */
export function clearFilters(filters: INewFilters | undefined): INewFilters | undefined {
    if (!filters) {
        return undefined;
    }
    const clearedFilters = Object.fromEntries(
        Object.entries(filters).filter(([, value]) => {
            if (Array.isArray(value)) {
                return value.length > 0;
            }
            return value !== undefined;
        }),
    );
    if (Object.keys(clearedFilters).length === 0) {
        return undefined;
    }
    return clearedFilters;
}

/**
 * Build url query string from queryParams
 * @param {string} url
 * @param {Record<string, string>} queryParams
 * @returns {string} url with query string
 */
export function buildFilterQuery(url: string, queryParams?: TFiltersQueryParams): string {
    if (!queryParams || Object.keys(queryParams).length === 0) {
        return url;
    }
    // remove any key that has undefined value
    const clearedParams = Object.fromEntries(Object.entries(queryParams).filter(([, value]) => !!value)) as Record<
        string,
        string
    >;
    const query = new URLSearchParams(clearedParams);
    return `${url}?${query.toString()}`;
}

/**
 * Decodes an array parameter from the query string and adds it to the params object.
 *
 * @param {object} params - The parameters object to which the decoded array will be added.
 * @param {string} decodedKey - The decoded key from the query string, expected to end with "[]".
 * @param {string} decodedValue - The decoded value to be added to the array.
 */
function decodeArray(params: object, decodedKey: string, decodedValue: string) {
    const arrayKey = decodedKey.slice(0, -2);

    if (!params[arrayKey]) {
        params[arrayKey] = [];
    }

    params[arrayKey].push(decodedValue);
}

/**
 * Decodes nested keys from the query string and constructs nested objects within the params object.
 *
 * @param {object} params - The parameters object to which the decoded nested keys will be added.
 * @param {string} decodedKey - The decoded key from the query string, expected to contain nested keys.
 * @param {string} decodedValue - The decoded value to be assigned to the nested key.
 */
function decodeNeastedKeys(params: object, decodedKey: string, decodedValue: string) {
    const type = decodedKey.endsWith("[]") ? "array" : undefined;
    const nestedKeys = decodedKey.split(/\[|\]/).filter(Boolean);
    let current = params;

    nestedKeys.forEach((nestedKey, index) => {
        if (index === nestedKeys.length - 1) {
            decodePair(current, nestedKey, decodedValue, type);
        } else {
            if (!current[nestedKey]) {
                current[nestedKey] = {};
            }
            current = current[nestedKey];
        }
    });
}

/**
 * Decodes a key-value pair from the query string and adds it to the params object.
 * Handles arrays denoted by '[]' and nested keys.
 *
 * @param {object} params - The parameters object to which the decoded pair will be added.
 * @param {string} decodedKey - The decoded key from the query string.
 * @param {string} decodedValue - The decoded value to be assigned to the key.
 * @param {string} [type='any'] - The type of the value, either 'any' or 'array'.
 */
function decodePair(params: object, decodedKey: string, decodedValue: string, type: "any" | "array" = "any") {
    if (decodedKey.includes("[") && decodedKey.includes("]")) {
        decodeNeastedKeys(params, decodedKey, decodedValue);
    } else if (decodedKey.endsWith("[]")) {
        decodeArray(params, decodedKey, decodedValue);
    } else {
        if (!!params[decodedKey]) {
            params[decodedKey] = [
                ...(Array.isArray(params[decodedKey]) ? params[decodedKey] : [params[decodedKey]]),
                decodedValue,
            ];
        } else {
            switch (type) {
                case "array":
                    params[decodedKey] = [decodedValue];
                    break;
                default:
                    params[decodedKey] = decodedValue;
                    break;
            }
        }
    }
}

/**
 * Recursively casts values in the params object to their appropriate types.
 * Converts numerical strings to numbers and 'null' strings to null.
 *
 * @param {object} params - The parameters object to be cast.
 */
function castValues(params: object) {
    for (const key in params) {
        if (params.hasOwnProperty(key)) {
            if (Array.isArray(params[key])) {
                params[key] = params[key].map(item => (isNaN(item) || item.length > 10 ? item : Number(item)));
            } else if (typeof params[key] === "object") {
                castValues(params[key]);
            } else if (params[key] === "null") {
                params[key] = null;
            } else {
                params[key] = isNaN(params[key]) ? params[key] : Number(params[key]);
            }
        }
    }
}

/**
 * Converts a query string into an object with key-value pairs.
 * Handles arrays denoted by '[]' and converts numerical values to numbers.
 * Handles nested objects denoted by keys with square brackets.
 *
 * @param {string} queryString - The query string to convert.
 * @returns {object} An object representing the parameters and values from the query string.
 */
export function queryStringToObject(queryString: string) {
    if (queryString.startsWith("?")) {
        queryString = queryString.substring(1);
    }

    const pairs = queryString.split("&");
    const params = {};
    for (const pair of pairs) {
        const [key, value] = pair.split("=");

        const decodedKey = decodeURIComponent(key);
        const decodedValue = decodeURIComponent(value);
        decodePair(params, decodedKey, decodedValue);
    }

    castValues(params);

    return params;
}

export const updateFilterNullIds = (filters: IPageFiltersResponse): IPageFiltersResponse => {
    Object.values(filters).forEach(filter => {
        if (Array.isArray(filter.items)) {
            filter.items.forEach(item => {
                if (item.id === null) {
                    item.id = "null";
                }
            });
        }
    });
    return filters;
};
