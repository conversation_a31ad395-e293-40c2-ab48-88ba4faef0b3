import React from "react";
import {renderToString} from "react-dom/server";
import Typography, {ITypographyProps} from "../uicomponents/Atoms/Typography/Typography.component";

export const measureTypography = (text: string, typographyProps: ITypographyProps): {width: number; height: number} => {
    const div = document.createElement("div");
    div.style.position = "absolute";
    div.style.top = "-9999px";
    div.style.left = "-9999px";
    div.style.width = "fit-content";
    div.style.whiteSpace = "nowrap";

    const typographyElement = React.createElement(Typography, typographyProps, text);
    div.innerHTML = renderToString(typographyElement);

    document.body.appendChild(div);
    const dimensions = {width: div.clientWidth, height: div.clientHeight};
    document.body.removeChild(div);

    return dimensions;
};
