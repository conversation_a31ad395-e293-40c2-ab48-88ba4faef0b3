export function linkify(inputText) {
    let replacedText: string, replacePattern1: RegExp, replacePattern2: RegExp, replacePattern3: RegExp;

    //URLs starting with http://, https://, or ftp://
    // eslint-disable-next-line
    replacePattern1 = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim;
    replacedText = inputText.replace(replacePattern1, '<a href="$1" target="_blank" rel="noopener" >$1</a>');

    //URLs starting with "www." (without // before it, or it'd re-link the ones done above).
    // eslint-disable-next-line
    replacePattern2 = /(^|[^\/])(www\.[\S]+(\b|$))/gim;
    replacedText = replacedText.replace(replacePattern2, '$1<a href="http://$2" target="_blank" rel="noopener">$2</a>');

    //Change email addresses to mailto:: links.
    // eslint-disable-next-line
    replacePattern3 = /(([a-zA-Z0-9\-\_\.])+@[a-zA-Z\_]+?(\.[a-zA-Z]{2,6})+)/gim;
    replacedText = replacedText.replace(replacePattern3, ' <a href="mailto:$1">$1</a> ');

    return replacedText;
}

export function formatLink(link: string) {
    if (typeof link !== "string") return "";
    if (link.startsWith("http://") || link.startsWith("https://")) {
        return link;
    }
    return `https://${link}`;
}

export const linkifyMentions = (inputText: string, mentions: Object): string => {
    if (!mentions) return inputText;
    let newInputText = inputText;
    Object.keys(mentions || {}).forEach((handle: any) => {
        const mentionRegex = new RegExp(`@${handle}`, "g");
        newInputText = newInputText.replace(
            mentionRegex,
            `<a href="${mentions[handle]}" title="Visit Profile">@${handle}</a>`,
        );
    });
    return newInputText;
};
