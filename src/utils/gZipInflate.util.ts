import pako from "pako";

const gZipInflate = (base64String: string, returnType: "string" | "parsed" = "parsed") => {
    const decoded = atob(base64String);
    const charData = decoded.split("").map(x => x.charCodeAt(0));
    const data = pako.inflate(new Uint8Array(charData), {to: "string", raw: true});
    return returnType === "string" ? data : JSON.parse(data);
};

const uint8ArrayToBase64 = (uint8: Uint8Array): string => {
    return btoa(String.fromCharCode(...uint8));
};

// Equivalent to: base64_encode(gzdeflate(json_encode(...), 9))
export const gZipDeflate = (data: any): string => {
    const jsonString = JSON.stringify(data);
    const deflated = pako.deflate(jsonString, {level: 9, raw: true});
    return uint8ArrayToBase64(deflated);
};

export default gZipInflate;
