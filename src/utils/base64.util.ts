export function base64ToArrayBuffer(base64) {
    const binary_string = atob(base64);
    const len = binary_string.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binary_string.charCodeAt(i);
    }
    return bytes.buffer;
}

export const fileToBase64 = file => {
    return new Promise(resolve => {
        const reader = new FileReader();

        reader.onload = function (event: any) {
            const base64String = event.target.result.split(",")[1];
            resolve(base64String);
        };

        reader.readAsDataURL(file);
    });
};

const stripBase64Prepend = (str: string) => {
    let strImage = str.replace(/^data:image\/[a-z]+;base64,/, "");
    strImage = strImage.replace("data:png;base64,", "");
    strImage = strImage.replace("data:jpeg;base64,", "");
    strImage = strImage.replace("data:jpg;base64,", "");
    strImage = strImage.replace("data:jfif;base64,", "");
    return strImage;
};

/**
 * Converts a WebP image in base64 format to a supported format (PNG) using a canvas.
 * @param {string} base64WebP The WebP image data as a base64-encoded string.
 * @return {Promise<string>} A promise that resolves to the converted image as a base64-encoded PNG string.
 */
export async function convertWebPToPNGBase64(base64WebP, _stripPrepend: boolean = true) {
    const fetchResponse = await fetch(base64WebP);
    const blob = await fetchResponse.blob();
    const url = URL.createObjectURL(blob);
    const img: any = await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = e => reject(e);
        img.src = url;
    });
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx: any = canvas.getContext("2d");
    ctx.drawImage(img, 0, 0);
    const pngBase64 = canvas.toDataURL("image/png");
    URL.revokeObjectURL(url);
    return stripBase64Prepend ? stripBase64Prepend(pngBase64) : pngBase64;
}
