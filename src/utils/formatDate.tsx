/* eslint-disable react-refresh/only-export-components */
import {DateTime} from "luxon";

// Date and time formats can be localized
export const DATE_FORMAT = "D"; // Ex: 12/13/2019
export const DATE_FORMAT_SHORT_YEAR = "MM/dd/yy"; // Ex: 12/13/19
export const TIME_FORMAT = "t"; // Ex: 8:17 AM
export const TIME_DATE_FORMAT = "t D"; // Ex: 8:17 AM 12/13/2019
export const DATE_TIME_FORMAT = "D t"; // Ex: 12/13/2019 8:17 AM
export const DATE_TIME_FULL_FORMAT = "DDDD t"; // Ex: Friday, December 13, 2019 8:17 AM
export const DATE_TIME_FULL_FORMAT_NODAY = "DDD t"; // Ex: December 13, 2019 8:17 AM
export const DATE_FULL_NODAY = "DDD"; // Ex: December 13, 2019
export const DATE_FULL_SHORT_MONTH = "ddd"; //Ex: Dec 13, 2019
export const DATE_STRING = "LLLL dd"; // Ex: December 13
export const DATE_HUGE_STRING = "cccc, LLLL dd"; // Ex: Friday, December 13
export const DATE_TIME_NUMBERED = "YYYY-MM-DD HH:MM"; // Ex: 2019-12-13 8:17
export const DATE_TIME_NUMBERED_W_SECONDS = "YYYY-MM-DD HH:MM:SS"; // Ex: 2019-12-13 8:17:00
export const INPUT_DATE_TIME = "YYYY-MM-DD HH:MM"; // Ex: 2019-12-13 8:17 AM
export const INPUT_TIME = "HH:MM"; // Ex: 8:17 AM
export const DATE_FORMAT_Year_Month_Day = "yyyy-mm-dd"; // Ex: 2019-12-13
export const DATE_MONTH_DAY_YEAR_AND_TIME_WITH_TIMEZONE = "MMMM dd, yyyy h:mm a timezone"; // Ex: December 13, 2019 8:17 AM MST
export const DATE_MONTH_DAY_YEAR = "MMMM dd, yyyy"; // Ex: May 22, 2024
export const FULL_DATETIME_SHORT_MONTH_TIMEZONE = "MMM dd, yyyy h:mm a timezone"; // Ex: Dec 13, 2019 8:17 AM MST
export const FULL_DATETIME_SHORT_MONTH = "MMM dd, yyyy h:mm a"; // Ex: Dec 13, 2019 8:17 AM
export const SHORT_MONTH_DATE_AT_TIME = "D 'at' h:mm a"; // Ex: Dec 13 at 8:17 AM
export const FULL_DATE_AT_TIME = "MMM dd, yyyy 'at' h:mm a"; // Ex: 11/15/24 at 8:17 AM
export const DAY_MONTH_YEAR = "d MMM, yyyy"; // Ex: 13 Dec, 2019
export const MONTH_DAY_YEAR = "MMM d, yyyy"; // Ex: Dec 13, 2019
export const DATE_HUGE_WITH_DATE_FIRST = "cccc, dd LLLL"; // Ex: Friday, 13 December
export const DATE_MONTH_NAME_FULL = "MMMM";
export const DATE_YEAR_ONLY = "YYYY"; //Ex: 2024
export const DATE_DAY_MONTH_ABBR = "cccc, dd yy"; // Ex: Friday, Jan 19
export const MONTH_YEAR = "MMMM yyyy"; // Ex: December 2019
export const DATE_AT_TIME = "DATE_AT_TIME";
export const MONTH_DAY = "MMM DD"; // Ex: Dec 19
export const MONTH_DAY_YEAR_SHORT = "MMM DD, YY";
export const DATE_SHORT_MONTH_TIME = "MMM DD, hh:mm a";

const SECONDS_IN_MINUTE = 60;
export const oneMonthInMS = 1000 * 60 * 60 * 24 * 30;

export function formatDate(dateToFormat: string, format: string, showTimeZone: boolean = false): string {
    let dateFromISO: string = DateTime.fromISO(dateToFormat).toFormat(format);
    let dateFromSQL: string = "";
    const padTo2Digits = num => num.toString().padStart(2, "0");
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    if (format === DATE_SHORT_MONTH_TIME) {
        const date = new Date(dateToFormat);
        const month = monthNames[date.getMonth()];
        const day = date.getDate();
        let hours = date.getHours();
        const minutes = padTo2Digits(date.getMinutes());
        const ampm = hours >= 12 ? "PM" : "AM";
        hours = hours % 12;
        hours = hours ? hours : 12; // Convert 0 to 12 for 12-hour format
        const time = `${hours}:${minutes} ${ampm}`;
        return `${month} ${day}, ${time}`;
    }
    if (format === MONTH_DAY) {
        const date = new Date(dateToFormat);
        const month = monthNames[date.getMonth()];
        const day = date.getDate();
        return `${month} ${day}`;
    }
    if (format === MONTH_DAY_YEAR_SHORT) {
        const date = new Date(dateToFormat);
        const month = monthNames[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear().toString().slice(-2); // Get last two digits of the year
        return `${month} ${day}, ${year}`;
    }
    if (format === DATE_MONTH_NAME_FULL) {
        const date = new Date(dateToFormat);
        const options: Intl.DateTimeFormatOptions = {
            month: "long",
        };
        return date.toLocaleDateString("en-US", options);
    }
    if (format === DATE_YEAR_ONLY) {
        const date = new Date(dateToFormat);
        return date.getFullYear().toString();
    }
    if (format === DATE_FULL_SHORT_MONTH) {
        const date = new Date(dateToFormat);
        const month = monthNames[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();
        return `${month} ${day}, ${year}`;
    }
    if (format.includes("timezone")) {
        format = format.replace("timezone", "");
        const options: Intl.DateTimeFormatOptions = {
            month: format.includes("MMMM") ? "long" : "short",
            day: "numeric",
            year: "numeric",
            hour: "numeric",
            minute: "numeric",
            hour12: true,
            timeZoneName: "short",
        };
        const date = new Date(dateToFormat);
        const formattedDate = date.toLocaleDateString("en-US", options).replace(/ at /g, ", ");
        return formattedDate;
    }
    if (format === INPUT_DATE_TIME) {
        const date = DateTime.fromISO(dateToFormat).toLocal();
        return `${date.year}-${padTo2Digits(date.month)}-${padTo2Digits(date.day)}T${padTo2Digits(
            date.hour,
        )}:${padTo2Digits(date.minute)}`;
    }
    if (format === INPUT_TIME) {
        const date = DateTime.fromISO(dateToFormat).toLocal();
        return `${padTo2Digits(date.hour)}:${padTo2Digits(date.minute)}`;
    }
    if (format === DATE_FORMAT_Year_Month_Day) {
        return dateToFormat.slice(0, 10);
    }
    if (format === DATE_TIME_NUMBERED_W_SECONDS) {
        const date = new Date(dateToFormat);
        return (
            [date.getFullYear(), padTo2Digits(date.getMonth() + 1), padTo2Digits(date.getDate())].join("-") +
            " " +
            [padTo2Digits(date.getHours()), padTo2Digits(date.getMinutes()), padTo2Digits(date.getSeconds())].join(":")
        );
    }
    if (format === DATE_DAY_MONTH_ABBR) {
        const date = new Date(dateToFormat);
        const options: Intl.DateTimeFormatOptions = {
            weekday: "long",
            day: "numeric",
            month: "short",
        };
        return date.toLocaleDateString("en-US", options);
    }
    if (format === DATE_AT_TIME) {
        const date = new Date(dateToFormat);
        const month = monthNames[date.getMonth()];
        const day = date.getDate();
        let hours = date.getHours();
        const minutes = padTo2Digits(date.getMinutes());
        const ampm = hours >= 12 ? "PM" : "AM";
        hours = hours % 12;
        hours = hours ? hours : 12;
        const time = `${hours}:${minutes} ${ampm}`;
        const timeZone = showTimeZone
            ? ` ${new Date().toLocaleTimeString("en-us", {timeZoneName: "long"}).split(" ")[2]}`
            : " ET";
        return `${month} ${day} at ${time}${timeZone}`;
    }
    if (dateFromISO === "Invalid DateTime") {
        dateFromSQL = DateTime.fromSQL(dateToFormat).toFormat(format);
        if (showTimeZone) {
            dateFromSQL = `${dateFromSQL} ${
                new Date(dateToFormat).toLocaleDateString("en-us", {timeZoneName: "long"}).split(" ")[1]
            }`;
        }
        return dateFromSQL;
    }
    if (showTimeZone) {
        dateFromISO = `${dateFromISO} ${
            new Date(dateToFormat).toLocaleDateString("en-us", {timeZoneName: "long"}).split(" ")[1]
        }`;
    }
    return dateFromISO;
}

export function formatDateBuilder(dateToFormat: number, format: string, showTimeZone: boolean = false): string {
    if (typeof dateToFormat === "string") {
        dateToFormat = 1639371319470;
    }
    let dateFromISO: string = DateTime.fromMillis(dateToFormat).toFormat(format);

    if (showTimeZone) {
        dateFromISO = `${dateFromISO} ${
            new Date(dateToFormat).toLocaleDateString("en-us", {timeZoneName: "short"}).split(" ")[1]
        }`;
    }
    return dateFromISO;
}

export function SubstractMinutes(date: string, minutes: number) {
    const formatedDate: DateTime = DateTime.fromISO(date);
    return formatedDate.minus({minutes: minutes});
}

export function GetCurrentMonthAndYear() {
    const month = new Date().toLocaleString("default", {month: "long"});
    const year = new Date().toLocaleString("default", {year: "numeric"});

    return month + " " + year;
}

export function convertSecondsToMinutes(seconds: number) {
    return (
        (seconds - (seconds %= SECONDS_IN_MINUTE)) / SECONDS_IN_MINUTE +
        (9 < seconds ? ":" : ":0") +
        Math.floor(seconds)
    );
}

/**
 * Function to format a date relative to now and return a friendly string to display
 * @param {string} date Send date you want to compare with now()
 * @returns {string} A friendly string like "1 day ago"
 */
export const getFriendlyDate = (date: string, format: "default" | "number" = "default"): string => {
    const rtf1 = new Intl.RelativeTimeFormat("en", {
        style: "long",
    });
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;
    const oneWeek = 7 * oneDay;
    const oneMonth = 30 * oneDay;
    const oneYear = 365 * oneDay;
    const timeAway = Date.now() - Date.parse(date);
    if (isNaN(timeAway)) {
        return "Invalid date"; // Handle invalid date string
    }
    const isInFuture = timeAway < 0;
    const timeAwayUnitValue = isInFuture ? timeAway * -1 : timeAway;
    const timeUnit =
        timeAwayUnitValue < oneHour
            ? "minute"
            : timeAwayUnitValue < oneDay
              ? "hour"
              : timeAwayUnitValue < oneWeek
                ? "day"
                : timeAwayUnitValue < oneMonth
                  ? "week"
                  : timeAwayUnitValue < oneYear
                    ? "month"
                    : "year";
    const timeAwayValue =
        Math.round(
            timeAway /
                (timeUnit === "minute"
                    ? 1000 * 60
                    : timeUnit === "hour"
                      ? 1000 * 60 * 60
                      : timeUnit === "day"
                        ? 1000 * 60 * 60 * 24
                        : timeUnit === "week"
                          ? 1000 * 60 * 60 * 24 * 7
                          : timeUnit === "month"
                            ? oneMonthInMS
                            : 1000 * 60 * 60 * 24 * 365),
        ) * -1;
    const friendlyTimeAway = rtf1.format(timeAwayValue, timeUnit);
    return format === "default" ? friendlyTimeAway : String(timeAwayValue);
};

/**
 * Function to calculate difference between two dates
 * @param {string} date Send date you want to compare with now()
 * @param {string} type Send type (hours | minutes | seconds) you want to return
 * @returns {string} A difference based on the type
 */

export const getDateDiff = (date: string, type: any) => {
    const givenDate = new Date(date).toISOString();
    const diff = DateTime.now().diff(DateTime.fromISO(givenDate), type);
    return Math.trunc(diff[type]);
};

export const getDaysDiff = (date: string, startDate?: string) => {
    const givenDate = new Date(date);
    const currentDate = startDate ? new Date(startDate) : new Date();
    const timeDiff = currentDate.getTime() - givenDate.getTime();
    return timeDiff / (1000 * 3600 * 24);
};

export const dateIsWithinTimeFrame = (date: string, hoursToCompare: string) => {
    const dateFormatted = new Date(date);
    const dateAsUTC = Date.UTC(
        dateFormatted.getFullYear(),
        dateFormatted.getMonth(),
        dateFormatted.getDate(),
        dateFormatted.getHours(),
        dateFormatted.getMinutes(),
        dateFormatted.getSeconds(),
    );

    const now = Date.now();
    const diff = now - dateAsUTC;

    const toMs = Number(hoursToCompare) * 60 * 60 * 1000;

    if (diff < toMs) {
        return true;
    } else {
        return false;
    }
};

export const formatDateRange = (date1, date2) => {
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    const day1 = date1.getDate();
    const day2 = date2.getDate();
    const monthName = monthNames[date1.getMonth()];
    const year = date1.getFullYear();

    if (day1 === day2 && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear()) {
        return `${monthName} ${day1}, ${year}`;
    } else if (date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear()) {
        return `${monthName} ${day1}-${day2}, ${year}`;
    } else {
        return `${monthName} ${day1}, ${year} - ${monthNames[date2.getMonth()]} ${day2}, ${date2.getFullYear()}`;
    }
};

export const roundToNearestHalfHour = (isoString: string, removeSeconds?: boolean) => {
    const date = new Date(isoString);
    let hours = date.getUTCHours();
    let minutes = date.getUTCMinutes();
    if (minutes < 30) {
        minutes = 30;
    } else {
        hours += 1;
        minutes = 0;
    }
    date.setUTCHours(hours);
    date.setUTCMinutes(minutes);
    if (removeSeconds) {
        date.setUTCSeconds(0);
    }
    const roundedISOString = date.toISOString();
    return roundedISOString;
};

export const getCurrentTimezone = (long?: boolean) => {
    if (!long) {
        const startTime = new Date();
        const time = new Date(startTime.getTime());
        const label = time.toLocaleString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
            timeZoneName: "short",
        });
        return label.split("M ")[1];
    }
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

export const todayIsBetweenTimeframe = (start_date: string, end_date: string) => {
    const today = new Date();

    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    if (startDate <= today && today <= endDate) {
        return true;
    }
    return false;
};

//? ISO Date string and the offset formatted as + XX:XX
export const applyOffsetToDate = (isoDateString: string, offset: string): string => {
    if (!isoDateString) return "";
    const date = new Date(isoDateString);
    const [sign, offsetHoursAndMinutes] = offset.split(" ");
    const [offsetHours, offsetMinutes] = offsetHoursAndMinutes.split(":");
    const offsetHoursValue = parseInt(offsetHours);
    const offsetMinutesValue = parseInt(offsetMinutes.substring(0, 2));
    const offsetInMinutes = (offsetHoursValue * 60 + offsetMinutesValue) * (sign === "-" ? 1 : -1);
    date.setMinutes(date.getMinutes() + offsetInMinutes);
    return date.toISOString();
};

export const formatDateWithoutOffset = (isoDateString: string) => {
    const date = new Date(isoDateString);
    const formattedDateTime = date.toLocaleString("en-US", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
        timeZone: "UTC",
    });
    return formattedDateTime;
};

//? Takes in an ISO date string and converts it to a specific timezone date
export const utcToSpecificTimezone = (dateString: string, timezone: string) => {
    const date: any = DateTime.fromISO(dateString);
    const localDate = date.setZone(timezone);
    const output = localDate.toFormat("M/d/yyyy h:mm a");
    return output;
};

//? converts dateString to a ISOString as the selected timezone value
export const dateToTimezone = (dateString: string, timezone: string) => {
    const date: any = DateTime.fromISO(dateString, {zone: timezone});
    const output = date.toUTC().toISO();
    return output;
};

export const dateToISO = (date: Date) => {
    const output = date.toISOString();
    return output;
};

//? Used to show a specific date in a timezone in a datetime-local input
export const convertToInputDateTimeLocal = (
    dateString: string,
    timezone: string,
    format: string = "yyyy-LL-dd'T'HH:mm",
) => {
    const dateInDesiredTimezone = DateTime.fromISO(dateString, {zone: "UTC"}).setZone(timezone);
    const output = dateInDesiredTimezone.toFormat(format);
    return output;
};

//? Returns the timezone in abbreviated format for Ex: MST/ MDT
export const getTimezoneAbbreviation = (timezone: string) => {
    try {
        const dt = DateTime.now().setZone(timezone);
        return dt.toFormat("ZZZZ");
    } catch (error: any) {
        console.error("Error converting timezone:", error.message);
        return "";
    }
};

export const simplifyLabel = (label: string): string => label.replace(/ (Standard|Daylight)/g, "");

export const getLongTimezoneName = (timezone: string, simplify: boolean = false) => {
    const ignoreSimplify = ["Mountain Time", "Central Time"];
    const dt = DateTime.local().setZone(timezone);
    const longTimeZoneName = dt.toLocaleString({timeZoneName: "long"}).split(", ")[1];
    const simplified = simplifyLabel(longTimeZoneName);
    return simplify && timezone.includes("America/") && !ignoreSimplify.includes(simplified)
        ? simplified
        : longTimeZoneName;
};

export const isDaylightTime = (timeZone: string): boolean => {
    const dt = DateTime.local().setZone(timeZone);
    return dt.isInDST;
};

export const getUTCOffset = (
    timeZone: string,
): {
    offset: number;
    friendlyString: string;
} => {
    const dt = DateTime.local().setZone(timeZone);
    const offset = dt.offset;
    const sign = offset >= 0 ? "+" : "-";
    const hours = Math.abs(Math.floor(offset / 60))
        .toString()
        .padStart(2, "0");
    const minutes = Math.abs(offset % 60)
        .toString()
        .padStart(2, "0");
    return {
        offset,
        friendlyString: `UTC ${sign}${hours}:${minutes}`,
    };
};

export const dateFromDateAndTimeStrings = (dateStr: string, timeStr: string) => {
    const [year, month, day] = dateStr.split("-");
    const [hour, minute] = timeStr.split(":");
    return new Date(Number(year), Number(month) - 1, Number(day), Number(hour), Number(minute));
};

export const getStartAndEndDateOfWeek = () => {
    const currentDate = new Date();
    const currentDay = currentDate.getDay(); // 0 (Sunday) to 6 (Saturday)

    // Calculate the start date of the week (Sunday as the start of the week)
    const startDate = new Date(currentDate);
    startDate.setDate(currentDate.getDate() - currentDay);

    // Calculate the end date of the week (Saturday as the end of the week)
    const endDate = new Date(currentDate);
    endDate.setDate(currentDate.getDate() + (6 - currentDay));

    return {
        start: startDate,
        end: endDate,
    };
};

export const getStartAndEndDateOfMonth = (date?: Date | string) => {
    const currentDate = date ? new Date(date) : new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Calculate the start date of the month
    const startDate = new Date(year, month, 1);

    // Calculate the end date of the month
    const endDate = new Date(year, month + 1, 0); // Setting day to 0 gets the last day of the previous month

    return {
        start: startDate,
        end: endDate,
    };
};

export const getStartAndEndDateOfYear = () => {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 0);
    return {
        start: startDate,
        end: endDate,
    };
};

export const getNextMonthStartAndEndDate = () => {
    const currentDate = new Date();
    // Calculate the start date of the next month
    const nextMonthStartDate = new Date(currentDate);
    nextMonthStartDate.setMonth(currentDate.getMonth() + 1, 1);
    // Calculate the end date of the next month
    const nextMonthEndDate = new Date(currentDate);
    nextMonthEndDate.setMonth(currentDate.getMonth() + 2, 0); // Setting day to 0 gets the last day of the current month

    return {
        start: nextMonthStartDate,
        end: nextMonthEndDate,
    };
};

export const getEndOfDay = (date: string): Date => {
    const dateArr = date.replaceAll("/", "-").split("-");
    const currentDate = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    currentDate.setHours(23, 59, 59, 999);
    return currentDate;
};

export const getStartOfDay = (date: string): Date => {
    const dateArr = date.replaceAll("/", "-").split("-");
    const currentDate = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    currentDate.setHours(0, 0, 0);
    return currentDate;
};

export const getDateXDaysFromNow = (days: number, customStartDate?: string, skipISO = false) => {
    const currentDate = customStartDate ? new Date(customStartDate) : new Date();
    if (days === 0) return skipISO ? currentDate.toISOString().split("T")[0] : currentDate.toISOString();
    currentDate.setDate(currentDate.getDate() + days);
    return skipISO ? currentDate.toISOString().split("T")[0] : currentDate.toISOString();
};

export const isValidDate = (dateString: string) => {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
};

export const convertHoursToDaysAndHours = hours => {
    if (hours < 0) {
        return "Invalid input";
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    const daysText = days > 0 ? `${days} Day${days > 1 ? "s" : ""}` : "";
    const hoursText = remainingHours > 0 ? `${remainingHours} Hour${remainingHours > 1 ? "s" : ""}` : "";

    return daysText || hoursText ? `${daysText} ${hoursText}` : "0 Hours";
};

export const monthNamesShort = ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug", "Sept", "Oct", "Nov", "Dec"];

export const monthsLongAndShort = [
    {long: "January", short: "Jan", monthIndex: 1},
    {long: "February", short: "Feb", monthIndex: 2},
    {long: "March", short: "Mar", monthIndex: 3},
    {long: "April", short: "Apr", monthIndex: 4},
    {long: "May", short: "May", monthIndex: 5},
    {long: "June", short: "Jun", monthIndex: 6},
    {long: "July", short: "Jul", monthIndex: 7},
    {long: "August", short: "Aug", monthIndex: 8},
    {long: "September", short: "Sep", monthIndex: 9},
    {long: "October", short: "Oct", monthIndex: 10},
    {long: "November", short: "Nov", monthIndex: 11},
    {long: "December", short: "Dec", monthIndex: 12},
];

export const getCurrentDateISOString = () => {
    return new Date().toISOString();
};
export const getCurrentYear = () => {
    return new Date().getFullYear();
};

export const getCurrentDay = () => {
    return new Date().getDate();
};

export const getCurrentMonth = (format: "long" | "short" | "monthIndex" = "long") => {
    return monthsLongAndShort[new Date().getMonth()]?.[format];
};

export const inputDateToLocalIsoString = (dateString: string): string => {
    const date = new Date(dateString);
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString().slice(0, -1);
};

export const getMonthDiff = (date: string, startDate?: string) => {
    const givenDate = new Date(date);
    const currentDate = startDate ? new Date(startDate) : new Date();
    let monthsDiff =
        (currentDate.getFullYear() - givenDate.getFullYear()) * 12 + (currentDate.getMonth() - givenDate.getMonth());
    if (currentDate.getDate() < givenDate.getDate()) {
        monthsDiff -= 1;
    }
    return monthsDiff;
};
