import {
    TFileAditionalParams,
    TFileWithError,
    TFileWithErrorList,
    TSelectedFile,
    TSelectedFileList,
} from "../Interfaces/fileUpload.interface";
import {SelectedFileStatus} from "../enums/fileUpload.enum";

/**
 * Generates a unique identifier for a file based on its name, type, and relative path.
 * The identifier is a SHA-256 hash of the concatenated file information, optionally prefixed.
 *
 * @param {string} fileName - The name of the file.
 * @param {string} fileType - The type of the file.
 * @param {string} relativePath - The relative path of the file.
 * @param {string} [prefix] - An optional prefix to be added to the generated ID.
 * @returns {Promise<string>} The unique identifier for the file.
 */
export const fileUniqID = async (fileName: string, fileType: string, relativePath: string, prefix?: string) => {
    const id = `${fileName}${fileType}${relativePath}`;

    const encoder = new TextEncoder();
    const data = encoder.encode(id);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, "0")).join("");
    return `${prefix}${hashHex}`;
};

/**
 * Recursively filters out files with status 'ERROR' from the file tree.
 * If a directory becomes empty after filtering, its status is also set to 'ERROR'.
 *
 * @param {TSelectedFileList} files - The list of selected files to be filtered.
 * @returns {TSelectedFileList} - The filtered list of selected files.
 */
export const getValidFileTree = (files: TSelectedFileList): TSelectedFileList => {
    const response: TSelectedFileList = [];
    files.forEach(file => {
        if (file.status === SelectedFileStatus.ERROR) return;
        const content = !!file.content ? getValidFileTree(file.content) : undefined;
        let status: SelectedFileStatus = file.status;
        if (!!file.isDirectory && (!Array.isArray(content) || !content.length)) {
            status = SelectedFileStatus.ERROR;
        }
        file.content = content;
        file.status = status;
        response.push(file);
    });
    return response;
};

/**
 * Recursively collects files with status 'ERROR' and their status messages from the file tree.
 *
 * @param {TSelectedFileList} files - The list of selected files to be checked.
 * @param {TFilesWithError[]} [response=[]] - The array to store files with errors.
 * @returns {TFileWithError[]} - An array of objects containing file names and their status messages.
 */
export const getErrorFileTree = (files: TSelectedFileList, response: TFileWithError[] = []): TFileWithErrorList => {
    files.forEach(({id, fileName, relativePath, status, statusMessage, isDirectory, content}) => {
        if (!!isDirectory && !!content) {
            getErrorFileTree(content, response);
            return;
        }
        if (status === SelectedFileStatus.ERROR) {
            response.push({id, fileName, relativePath, statusMessage});
        }
    });
    return response;
};

/**
 * Recursively updates the file name and ID for a specific file in a file tree structure.
 *
 * @param {string} id - The unique identifier of the file to be updated.
 * @param {string} name - The new name to be assigned to the file.
 * @param {TSelectedFileList} fileTree - The file tree structure containing the file.
 * @param {string} [relativePath] - The relative path of the file within the file tree.
 * @returns {Promise<TSelectedFileList>} - A promise that resolves to the updated file tree structure.
 */
const updateFileNameByIdRecursive = async (
    id: string,
    name: string,
    fileTree: TSelectedFileList,
    relativePath?: string,
) => {
    const newFileTree = await Promise.all(
        fileTree.map(async file => {
            if (file.id === id) {
                const prefix = id.slice(0, -64);
                const newRelativePath = `${relativePath ?? ""}${name}/`;
                const newId = await fileUniqID(name, file.fileType ?? "", file.relativePath ?? "", prefix);
                const newFileName = `${name}${!file.isDirectory ? "." : ""}${file.extension}`;
                return {
                    ...file,
                    fileName: newFileName,
                    id: newId,
                    content:
                        file.isDirectory && file.content
                            ? await updateFileNameByIdRecursive(id, name, file.content, newRelativePath)
                            : file.content,
                };
            } else {
                return {
                    ...file,
                    relativePath: relativePath ?? file.relativePath ?? "",
                    content:
                        file.isDirectory && file.content
                            ? await updateFileNameByIdRecursive(id, name, file.content)
                            : file.content,
                };
            }
        }),
    );
    return newFileTree;
};

/**
 * Updates the file name of a file or directory in the file tree by its ID.
 *
 * @param {string} id - The ID of the file or directory to update.
 * @param {string} name - The new name for the file or directory.
 * @param {TSelectedFileList} fileTree - The file tree structure containing files and directories.
 * @returns {TSelectedFileList} - The updated file tree with the new file name.
 */
export const updateFileNameById = async (id: string, name: string, fileTree: TSelectedFileList) => {
    return await updateFileNameByIdRecursive(id, name, fileTree);
};

/**
 * Recursively updates additional parameters for a specific file or all files in a file tree structure.
 *
 * @param {string | null} id - The unique identifier of the file to be updated. If null, all files will be updated.
 * @param {TFileAditionalParams} params - The additional parameters to be added or updated.
 * @param {TSelectedFileList} fileTree - The file tree structure containing the files.
 * @param {boolean} [clearBeforeUpdate=false] - If true, clears existing parameters before updating.
 * @param {Function} [filterAditionalParams] - An optional function to filter the additional parameters.
 * @param {string} [relativePath] - The relative path of the file within the file tree.
 * @returns {TSelectedFileList} - The updated file tree structure.
 */
const updateFileAditionalParamsRecursive = (
    id: string | null,
    params: TFileAditionalParams,
    fileTree: TSelectedFileList,
    clearBeforeUpdate: boolean = false,
    filterAditionalParams?: (file: TSelectedFile, params: TFileAditionalParams) => TFileAditionalParams,
    relativePath?: string,
): TSelectedFileList => {
    return fileTree.map(file => {
        let aditionalParams = file.aditionalParams;
        if (!file.isDirectory && (id === null || file.id === id)) {
            aditionalParams = {
                ...(clearBeforeUpdate ? {} : file.aditionalParams),
                ...params,
            };
            if (!!filterAditionalParams) {
                aditionalParams = filterAditionalParams(file, aditionalParams);
            }
        }
        return {
            ...file,
            aditionalParams,
            content:
                file.isDirectory && file.content
                    ? updateFileAditionalParamsRecursive(
                          id,
                          params,
                          file.content,
                          clearBeforeUpdate,
                          filterAditionalParams,
                          `${relativePath ?? ""}${file.fileName}/`,
                      )
                    : file.content,
        };
    });
};

/**
 * Updates the aditional params of a file or directory in the file tree by its ID.
 *
 * @param {string} id - The ID of the file or directory to update.
 * @param {TFileAditionalParams} params - The new aditional Params object for the file or directory.
 * @param {TSelectedFileList} fileTree - The file tree structure containing files and directories.
 * @returns {TSelectedFileList} - The updated file tree with the new file name.
 */
export const updateFileAditionalParamsById = (
    id: string,
    params: TFileAditionalParams,
    fileTree: TSelectedFileList,
    clearBeforeUpdate?: boolean,
    filterAditionalParams?: (file: TSelectedFile, params: TFileAditionalParams) => TFileAditionalParams,
): TSelectedFileList => {
    return updateFileAditionalParamsRecursive(id, params, fileTree, clearBeforeUpdate, filterAditionalParams);
};

/**
 * Updates the aditional params for all files and directories in the file tree.
 *
 * @param {TFileAditionalParams} params - The new aditional Params object for the file or directory.
 * @param {TSelectedFileList} fileTree - The file tree structure containing files and directories.
 * @returns {TSelectedFileList} - The updated file tree with the new file name.
 */
export const updateFileTreeAditionalParams = (
    params: TFileAditionalParams,
    fileTree: TSelectedFileList,
    clearBeforeUpdate?: boolean,
    filterAditionalParams?: (file: TSelectedFile, params: TFileAditionalParams) => TFileAditionalParams,
): TSelectedFileList => {
    return updateFileAditionalParamsRecursive(null, params, fileTree, clearBeforeUpdate, filterAditionalParams);
};

/**
 * Removes a file or directory from the file tree by its ID.
 *
 * @param {string} id - The ID of the file or directory to remove.
 * @param {TSelectedFileList} fileTree - The file tree structure containing files and directories.
 * @returns {TSelectedFileList} - The updated file tree with the specified file or directory removed.
 */
export const removeFileById = (id: string, fileTree: TSelectedFileList) =>
    fileTree
        .filter(file => file.id !== id)
        .map(file => ({
            ...file,
            content: !!file.isDirectory && !!file.content ? removeFileById(id, file.content) : file.content,
        }));
