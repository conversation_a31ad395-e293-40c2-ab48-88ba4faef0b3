import {AxiosResponse} from "axios";
import {v4 as uuidv4} from "uuid";

const defaultMaxChunkSize: number = 50 * 1024 * 1024;
const defaultMaxFileSize: number = 5000;
const defaultMaxParallelConnections: number = 3;

async function uploadChunks(
    file: File,
    uploadService: (data: FormData, id: string, d: any) => Promise<AxiosResponse<any, any>>,
    progressCallback?: (progress: number) => void,
    errorCallback?: (error: any) => void,
    maxChunkSizeParam: number | string = defaultMaxChunkSize,
    maxFileSizeParam: number | string = defaultMaxFileSize,
    maxParallelConnectionsParam: number | string = defaultMaxParallelConnections,
): Promise<void> {
    const maxChunkSize = Number(maxChunkSizeParam);
    const maxFileSize = Number(maxFileSizeParam);
    const maxParallelConnections = Number(maxParallelConnectionsParam);
    if (file.size > maxFileSize * 1024 * 1024) {
        errorCallback?.("File size is too large");
        return;
    }

    const totalChunks = Math.ceil(file.size / maxChunkSize);
    let chunksUploaded = 0;
    let uploadProgress = 0;
    const fileSize = file?.size;

    const updateProgress = (chunkSize: number) => {
        uploadProgress += (chunkSize / file.size) * 100;
        progressCallback?.(uploadProgress);
    };

    const uploadChunk = async (chunk: Blob, index: number, totalChunks: number, fileId: string) => {
        const formData = new FormData();
        const chunkId = `${fileId}-${index}`;
        const chunkSize = chunk.size;
        const isLastChunk = index === totalChunks;
        // formData.append("dzchunk", chunk, chunkId);
        formData.append("dztotalfilesize", fileSize.toString());
        formData.append("dzchunkindex", index.toString());
        formData.append("dzchunksize", chunkSize.toString());
        formData.append("dztotalchunkcount", totalChunks.toString());
        formData.append("dzchunkbyteoffset", (index * maxChunkSize).toString());
        formData.append("dzuuid", fileId);
        formData.append("file_extension", file?.name?.split(".").pop()!);
        formData.append("mime_type", file?.type);

        try {
            const response = await uploadService(formData, fileId, {
                chunk,
                index,
                totalChunks,
                fileId,
                chunkId,
                isLastChunk,
            });
            if (response.status === 200) {
                updateProgress(chunk.size);
                chunksUploaded++;
            } else {
                errorCallback?.("Failed to upload chunk");
            }
        } catch (error) {
            errorCallback?.(error);
        }
    };

    const fileId = uuidv4();
    let promises: Promise<any>[] = [];
    const lastPromises: Promise<any>[] = [];
    for (let i = 0; i < totalChunks; i++) {
        const start = i * maxChunkSize;
        const end = Math.min(start + maxChunkSize, file.size);
        const chunk = file.slice(start, end);
        const chunkIndex = i + 1;
        if (chunkIndex === totalChunks) {
            lastPromises.push(uploadChunk(chunk, chunkIndex, totalChunks, fileId));
        } else {
            promises.push(uploadChunk(chunk, chunkIndex, totalChunks, fileId));
        }
        if (promises.length >= maxParallelConnections || chunkIndex === totalChunks - 1) {
            await Promise.all(promises);
            promises = [];
        }
    }
    if (lastPromises) {
        await Promise.all(lastPromises);
    }

    return new Promise((resolve, reject) => {
        if (chunksUploaded === totalChunks) {
            resolve();
        } else {
            reject("Failed to upload all chunks");
        }
    });
}

export default uploadChunks;
