import routeConfig from "../constants/routeConfig";

/**
 * Check if routes is /c/*
 * Example channelprogram.com/c/ejemplo
 * Router will load a builder template specific for these pages.
 */

const regex_for_c_pages = /(\/c\/[-a-zA-Z0-9@:%._+~#=])/;
export const regex_for_vendor_pages = /(\/v\/[-a-zA-Z0-9@:%._+~#=])/;

/**
 * Check if routes is /r/*
 * Example channelprogram.com/r/ejemplo
 * Router will load a builder template specific for these pages.
 */
const regex_for_register_pages = /(\/register\/[-a-zA-Z0-9@:%._+~#=])/;

export function isFullUrl(str: string = "") {
    const substrings = ["https://", "http://", "www."];

    for (let i = 0; i !== substrings.length; i++) {
        const substring = substrings[i];
        if (str.indexOf(substring) !== -1) {
            return true;
        }
    }

    return false;
}

export function hideFooterResolver() {
    const current_path = window.location.pathname;

    if (current_path === "/admin") {
        return true;
    }
    if (current_path === routeConfig.Register.path) {
        return true;
    }
    //Check if routes is /c/*
    if (regex_for_c_pages.test(current_path)) {
        return true;
    }

    return false;
}

export function hideHeaderResolver(): boolean {
    const current_path = window.location.pathname;

    if (current_path === routeConfig.PRMLogin.path) {
        return true;
    }
    if (current_path === routeConfig.Register.path) {
        return true;
    }
    if (current_path === routeConfig.FirePage.path) {
        return true;
    }
    if (regex_for_c_pages.test(current_path)) {
        return true;
    }

    if (regex_for_register_pages.test(current_path)) {
        return true;
    }

    return false;
}

export function showOnlyForPage(page: string): boolean {
    const current_path = window.location.pathname;

    if (regex_for_vendor_pages.test(current_path) && page === "vendor") {
        return true;
    }
    return false;
}
