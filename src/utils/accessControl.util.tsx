import {ReactElement, ReactNode} from "react";
import usePermissions from "../hooks/usePermissions";
import {TPermissionGroupsKeysArr} from "../Interfaces/features/features.interface";
import {TCompanyType} from "../constants/companyType.constant";
import useActiveCompany from "../hooks/useActiveCompany";
import {TAllPlanType} from "../constants/profiles.constant";
import {IS_BETTERTRACKER_SITE} from "../constants/siteFlags";

interface IProps {
    permissions?: TPermissionGroupsKeysArr;
    children:
        | ReactNode
        | ((options: {
              hasAccess: boolean;
              hasClaimed?: boolean;
              hasCompanyTypeAccess: boolean;
              hasPlanTypeAccess: boolean;
              hasManageClientsAccess?: boolean;
          }) => ReactElement | null);
    claimedCompanyIdentifier?: string; //? Can be company id or friendly url
    companyTypes?: TCompanyType[] | TCompanyType;
    planTypes?: TAllPlanType[] | TAllPlanType;
    requireManageClients?: boolean;
    requireExpensesAccess?: boolean;
    showChildrenIfLoading?: boolean;
    hideForBetterTrackerSite?: boolean;
}

const AccessControl = (props: IProps) => {
    const {hasPermissions, isCompanySuperAdmin, isLoadingPermissions} = usePermissions();
    const {activeCompany, isLoading: isLoadingActiveCompany, hasExpensesAccess} = useActiveCompany();
    const hasPermissionsAccess =
        props.showChildrenIfLoading && isLoadingPermissions ? true : hasPermissions(props.permissions || []);
    const hasCompanyTypeAccess =
        !!props.companyTypes && !isLoadingActiveCompany && !!activeCompany?.id
            ? props.companyTypes instanceof Array
                ? props.companyTypes.includes(activeCompany?.company_type)
                : props.companyTypes === activeCompany?.company_type
            : true;
    const hasPlanTypeAccess =
        !!props.planTypes && !isLoadingActiveCompany && !!activeCompany?.id
            ? props.planTypes instanceof Array
                ? props.planTypes.includes(activeCompany?.company_profile_type?.value)
                : props.planTypes === activeCompany?.company_profile_type?.value
            : true;
    const hasManageClientsAccess = !!props.requireManageClients
        ? !isLoadingActiveCompany && !!activeCompany?.manage_clients
        : true;
    const meetsExpensesAccessRequirement = props.requireExpensesAccess
        ? !isLoadingActiveCompany && hasExpensesAccess
        : true;
    const meetsBetterTrackerSiteRequirement = props.hideForBetterTrackerSite ? !IS_BETTERTRACKER_SITE : true;
    const hasAccess =
        hasPermissionsAccess &&
        hasCompanyTypeAccess &&
        hasPlanTypeAccess &&
        hasManageClientsAccess &&
        meetsExpensesAccessRequirement &&
        meetsBetterTrackerSiteRequirement;

    if (typeof props.children === "function")
        return props.children({
            hasAccess,
            hasClaimed: props.claimedCompanyIdentifier ? isCompanySuperAdmin : undefined,
            hasCompanyTypeAccess,
            hasPlanTypeAccess,
            ...(props.requireManageClients ? {hasManageClientsAccess} : {}),
        });
    return hasAccess ? <>{props.children}</> : null;
};

export default AccessControl;
