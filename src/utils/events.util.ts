import {IPitchNames} from "../models/PitchObject.interface";
import {DATE_FORMAT, formatDate} from "./formatDate";

export function eventNameForDisplay(event: IPitchNames) {
    let eventName: string = event.name;

    if (event.start_date) {
        eventName += " (";
        eventName += formatDate(event.start_date.toString(), DATE_FORMAT, false);
        eventName += ")";
    }

    return eventName;
}
