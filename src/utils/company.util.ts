import type {ICompany} from "../Interfaces/company.interface";

type PERSISTENT_FIELDS = {
    id: string;
    name: string;
    description?: string;
    company_type: string;
    friendly_url: string;
    profile_company_friendly_name: string;
    subdomain?: string;
    type_is_of_vendor?: boolean;
    is_distributor?: boolean;
    manage_clients?: boolean;
    show_distributor_banner?: boolean;
    show_manage_clients_banner?: boolean;
    show_manage_affiliates_banner?: boolean;
    partner_flag?: boolean;
    handle?: string | null;
    hide_expenses?: boolean;
};

export type TCompanyAPIParameters = keyof Omit<ICompany, keyof Omit<PERSISTENT_FIELDS, "id">>;

export type TCompanyAPIParametersArray = Array<TCompanyAPIParameters>;
/**
 * @description
 * This type is used to create a dynamic object with the parameters you want to use from the ICompany interface
 * @param T - The interface of the company
 * @param K - The parameters you want to use from the interface
 * @example
 * const params: TCompanyAPIParametersArray = ['address'] satisfies TCompanyAPIParametersArray; // satisfies is needed for correct type inference
 * const _company: TDynamicCompany<ICompany, typeof params> = {};
 */
export type TDynamicCompany<T extends ICompany, K extends TCompanyAPIParametersArray> = PERSISTENT_FIELDS &
    Pick<T, K[number]>;

export enum CompanyAPIParameters {
    "likes_count",
    "user_has_liked",
    "categories",
    "revenue",
    "address",
    "address2",
    "city",
    "state",
    "zip",
    "phone",
    "industry",
    "employee_range",
    "founded",
    "is_profile_claimed",
    "profile_claimer_user_id",
    "profile_claimed_at",
    "profile_company_website_url",
    "followers_count",
    "features",
    "products",
    "blogs",
    "profile_images",
    "current_user_is_following",
    "company_profile_type",
    "users",
    "rating",
    "bulletin",
    "parent_id",
    "parent",
    "affiliate_brand_id",
    "affiliate_brand",
    "affiliate_id",
    "client_parent",
    "is_affiliate_brand_main_company",
    "focuses",
    "is_mdf",
}
