/* eslint-disable react-refresh/only-export-components */
import {ButtonPropsVariantOverrides} from "@mui/material/Button";
import {OverridableStringUnion} from "@mui/types";
import {ReactNode, useState} from "react";
import {createRoot, type Root} from "react-dom/client";
import {v4 as uuidv4} from "uuid";
import {ModalComponent} from "../components/Modal";
import {default as MuiModalComponent} from "../uicomponents/Molecules/Modal/Modal.component";
import type {SxProps, Theme} from "@mui/material/styles";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";

export interface IConfirmationDialogConfig {
    form?: JSX.Element;
    cancelButtonText?: string;
    okButtonText?: string | ReactNode;
    okButtonIcon?: React.ReactNode;
    title?: string;
    showOkButton?: boolean;
    showCancelButton?: boolean;
    confirmationInput?: string;
    className?: string;
    contentClassName?: string;
    mui?: boolean;
    //? Only allowed when using mui = true
    modalTheme?: "primary" | "secondary" | "error" | "info" | "warning" | "neutral" | "blue" | "success" | undefined;
    justifyButtons?: "flex-start" | "center" | "flex-end" | "space-between" | "space-around" | undefined;
    reverseBtnPosition?: boolean;
    customModalBtnTracking?: any;
    beforeSuccess?: () => boolean | Promise<Boolean>;
    customTitle?: ReactNode;
    cancelButtonVariant?: OverridableStringUnion<"text" | "outlined" | "contained", ButtonPropsVariantOverrides>;
    hideTopDivider?: boolean;
    hideCloseBtn?: boolean;
    okButtonSx?: SxProps<Theme>;
    dialogSx?: SxProps<Theme>;
    contentSx?: SxProps<Theme>;
}

interface IRenderDialogProps {
    resolve: (
        value:
            | {
                  value: any;
              }
            | PromiseLike<{
                  value: any;
              }>,
    ) => void;
    message: string;
    config: IConfirmationDialogConfig;
    rootRef?: Root;
}

const renderDialog = (message, config, resolve) => {
    const root = document.querySelector("body");
    if (!root) {
        console.error("No root element found.");
        return;
    }
    const div = document.createElement("div");
    div.setAttribute("id", "confirmationDialogContainer");
    root.appendChild(div);
    const container = document.getElementById("confirmationDialogContainer");
    if (container === null) {
        console.error("Container was not found.");
    }
    const dialog = createRoot(container as HTMLElement);
    return dialog.render(<ConfirmationDialog message={message} resolve={resolve} config={config} rootRef={dialog} />);
};

const removeDialog = (id?: any) => {
    const root = document.querySelector("body");
    if (!root) {
        console.error("No root element found.");
        return;
    }
    const checkElementId = document.getElementById(`#${id}`);
    const checkConfirmationDialog = document.getElementById("#confirmationDialogContainer");
    if (checkElementId && root) {
        root.removeChild(checkElementId);
    }
    if (checkConfirmationDialog && root) {
        root.removeChild(checkConfirmationDialog);
    }
};

const queryClient = new QueryClient();

const ConfirmationDialog = ({resolve, message, config, rootRef}: IRenderDialogProps) => {
    const [isOpen, setIsOpen] = useState(true);

    const clickOK = () => {
        setIsOpen(false);
        rootRef?.unmount();
        removeDialog();
        resolve({value: true});
    };

    const clickCancel = () => {
        setIsOpen(false);
        rootRef?.unmount();
        removeDialog();
        resolve({value: false});
    };

    const handleChange = data => {
        if (data) {
            setIsOpen(data);
            return;
        }
        setIsOpen(false);
        rootRef?.unmount();
        removeDialog();
    };

    return (
        <QueryClientProvider client={queryClient}>
            <ModalComponent
                showHeader
                showCancelButton={config?.showCancelButton ?? true}
                headerTitle={config?.title || "Confirmation"}
                okButtonText={config?.okButtonText || "Yes"}
                okButtonIcon={config?.okButtonIcon}
                showOkButton={typeof config?.showOkButton === "boolean" ? config.showOkButton : true}
                cancelButtonText={config?.cancelButtonText || "Cancel"}
                modalSwitcher={isOpen}
                modalSwitcherCallback={handleChange}
                confirmationInput={config?.confirmationInput}
                onCancel={clickCancel}
                onSuccess={clickOK}
                modalText={message ? message : "Are you sure?"}
                contentClassName={config?.contentClassName || "globalConfirmationModal"}
                className={config?.className || "globalConfirmationModalContainer"}
                form={config?.form || undefined}
                preventScroll
            />
        </QueryClientProvider>
    );
};

const MuiConfirmationDialog = ({resolve, message, config}: IRenderDialogProps) => {
    const [isOpen, setIsOpen] = useState(true);
    const uuid = uuidv4();

    const clickOK = async () => {
        if (config?.beforeSuccess) {
            const returnValue = await config.beforeSuccess();
            if (returnValue === false) return;
        }
        setIsOpen(false);
        removeDialog(uuid);
        resolve({value: true});
    };

    const clickCancel = () => {
        setIsOpen(false);
        removeDialog(uuid);
        resolve({value: false});
    };

    return (
        <MuiModalComponent
            id={uuid}
            open={isOpen}
            title={config?.title || "Confirmation"}
            customTitle={config?.customTitle}
            content={config?.form ? config.form : message ? message : undefined}
            onClose={clickCancel}
            modalTheme={config?.modalTheme || undefined}
            okButtonText={config?.okButtonText || "YES"}
            showOkButton={typeof config?.showOkButton === "boolean" ? config.showOkButton : true}
            okButtonSx={config?.okButtonSx}
            okButtonStartIcon={config?.okButtonIcon}
            cancelButtonText={config?.cancelButtonText?.toUpperCase() || "CANCEL"}
            onSuccess={clickOK}
            justifyButtons={"flex-start"}
            reverseBtnPosition={config?.reverseBtnPosition}
            customModalBtnTracking={config?.customModalBtnTracking || undefined}
            showCancelButton={config?.showCancelButton !== undefined ? config.showCancelButton : true}
            cancelButtonVariant={config.cancelButtonVariant}
            hideTopDivider={config?.hideTopDivider}
            hideCloseBtn={config?.hideCloseBtn}
            dialogSx={config?.dialogSx}
            contentSx={config?.contentSx}
        />
    );
};

const renderMuiDialog = (message, config, resolve) => {
    const root = document.querySelector("body");
    if (!root) {
        console.error("No root element found.");
        return;
    }
    const div = document.createElement("div");
    div.setAttribute("id", "confirmationDialogContainer");
    root.appendChild(div);
    const container = document.getElementById("confirmationDialogContainer");
    if (container === null) {
        console.error("Container was not found.");
    }
    const dialog = createRoot(container as HTMLElement);
    return dialog.render(<MuiConfirmationDialog message={message} resolve={resolve} config={config} />);
};

const getUserConfirmation = (
    message: string | null | JSX.Element,
    config: IConfirmationDialogConfig | undefined = undefined,
): Promise<{value: string}> =>
    config?.mui
        ? new Promise(resolve => {
              renderMuiDialog(message, config, resolve);
          })
        : new Promise(resolve => {
              renderDialog(message, config, resolve);
          });

export default getUserConfirmation;
