import {IStackCategorization} from "../Interfaces/myStack.interface";

type TPossibleLogTypes = "stack" | "other";
type TActivityLogData<T extends TPossibleLogTypes> = T extends "stack" ? IStackCategorization : any;

export function generateActivityLog<T extends TPossibleLogTypes>(
    data: TActivityLogData<TPossibleLogTypes>,
    type: T,
    company_id?: string,
) {
    if (type === "stack") {
        return {
            vendor_id: data?.stack_company_id,
            vendor_name: data?.stack_company?.name,
            vendor_friendly_url: data?.stack_company?.friendly_url,
            product_id: data?.product_id,
            product_name: data?.product?.name,
            product_friendly_url: data?.product?.friendly_url,
            msp_company_id: company_id,
        };
    }
    return {};
}
