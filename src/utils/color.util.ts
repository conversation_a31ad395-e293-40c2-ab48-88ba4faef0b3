import {colorPalette} from "../themes/themes.constant";

/**
 * Function that returns a hex color that contrasts with the given color.
 * @param color The color to contrast.
 * @returns The contrast color.
 */
export function getContrastColor(color: string): string {
    const isHex = isValidHexColor(color);
    const isRgb = isValidRgbColor(color);
    if (!isHex && !isRgb) return colorPalette.neutral[100];
    const colorRgb = isHex ? hexToRgb(color) : extractRgbColors(color);
    if (!colorRgb?.length) return colorPalette.neutral[100];
    const colorLuminance = getLuminance(colorRgb);
    if (colorLuminance > 0.5) {
        return colorPalette.neutral[800];
    } else {
        return colorPalette.neutral[100];
    }
}

export function hexToRgb(hex: string, opacity?: number): number[] | null {
    let normalizedHex = hex.replace(/^#/, "");

    if (normalizedHex.length === 3) {
        normalizedHex = normalizedHex
            .split("")
            .map(char => char + char)
            .join("");
    }

    if (!/^[a-f\d]{6}$/i.test(normalizedHex)) return null;

    const r = parseInt(normalizedHex.slice(0, 2), 16);
    const g = parseInt(normalizedHex.slice(2, 4), 16);
    const b = parseInt(normalizedHex.slice(4, 6), 16);

    return [r, g, b, opacity].filter(v => v !== undefined) as number[];
}

export function getLuminance(rgb: number[]): number {
    const [r, g, b] = rgb;
    const a = [r / 255, g / 255, b / 255];
    const bkgd = [0.2126, 0.7152, 0.0722];
    const weighted = a.map((v, i) => v * bkgd[i]);
    const c = weighted.reduce((acc, v) => acc + v);
    return c;
}

/**
 * Function that returns a hex color that is a percent shade lighter of the given color.
 * @param color The color to shade.
 * @param percent The percentage to shade the color.
 * @returns The shaded color.
 */
export function getShadeColor(color: string, percent: number): string {
    let R = parseInt(color.substring(1, 3), 16);
    let G = parseInt(color.substring(3, 5), 16);
    let B = parseInt(color.substring(5, 7), 16);

    R = parseInt(`${(R * (100 + percent)) / 100}`);
    G = parseInt(`${(G * (100 + percent)) / 100}`);
    B = parseInt(`${(B * (100 + percent)) / 100}`);

    R = R < 255 ? R : 255;
    G = G < 255 ? G : 255;
    B = B < 255 ? B : 255;

    const RR = R.toString(16).length === 1 ? "0" + R.toString(16) : R.toString(16);
    const GG = G.toString(16).length === 1 ? "0" + G.toString(16) : G.toString(16);
    const BB = B.toString(16).length === 1 ? "0" + B.toString(16) : B.toString(16);

    return "#" + RR + GG + BB;
}

export const rgbToHex = (rgbValue: string | number[]): string => {
    let r: number, g: number, b: number;

    if (typeof rgbValue === "string") {
        const parts = rgbValue.match(/\d+/g);
        if (!parts || parts.length !== 3) {
            throw new Error("Invalid RGB string format");
        }
        [r, g, b] = parts.map(Number);
    } else if (Array.isArray(rgbValue) && rgbValue.length === 3) {
        [r, g, b] = rgbValue;
    } else {
        throw new Error("Invalid input type. Expected a string or an array of three numbers");
    }

    if ([r, g, b].some(v => v < 0 || v > 255)) {
        throw new Error("Invalid RGB value. Each component must be between 0 and 255");
    }

    const toHex = (value: number): string => {
        const hex = Math.round(value).toString(16);
        return hex.length === 1 ? `0${hex}` : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

export function lightenColor(hex: string, percent: number): string {
    const rgb = hexToRgb(hex);
    if (!rgb) return hex;
    const [r, g, b] = rgb.map(value => {
        const newValue = value + (value * percent) / 100;
        return newValue > 255 ? 255 : newValue;
    });
    const finalHex = rgbToHex([r, g, b]);
    return finalHex;
}

export const hexColorRegex = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/;
export const rgbColorRegex = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/;

export const isValidHexColor = (color: string): boolean => {
    return hexColorRegex.test(color);
};

export const isValidRgbColor = (color: string): boolean => {
    return rgbColorRegex.test(color);
};

/**
 * Extracts numbers when giving a rgb css string
 * @param {string} text
 * @returns {number[]} Array of numbers extracted from the string. Returns empty array if no numbers are found.
 *
 * @example
 * extractRgbColors("rgb(255, 0, 0)") // returns [255, 0, 0]
 */
export const extractRgbColors = (text: string): number[] => {
    const matches = text.match(/\d+/g);
    return matches ? matches.map(Number) : [];
};
