import CompanyType, {MSPCompanyTypes, TCompanyType, VendorCompanyTypes} from "../constants/companyType.constant";
import {PERMISSION_GROUPS, TPermissionGroupsKeys} from "../Interfaces/features/features.interface";

export const ADMIN_COMPANY_PERMISSION_GROUP_NAME_OVERWRITES = {
    //? This is a key value pair with the
    //? key being the user permission group value and the value being the admin company permission group value append (without ADMIN_VENDOR, ADMIN_MSP, etc)
    //? This will be used with hasPermissions to auto select the company type
    [PERMISSION_GROUPS.MANAGE_DEALS_READ]: "DEAL_REGISTRATION_READ",
    [PERMISSION_GROUPS.MANAGE_DEALS_UPDATE]: "DEAL_REGISTRATION_UPDATE",
    [PERMISSION_GROUPS.MANAGE_CHANNEL_DEALS_READ]: "CHANNEL_DEALS_READ",
    [PERMISSION_GROUPS.MANAGE_CHANNEL_DEALS_UPDATE]: "CHANNEL_DEALS_UPDATE",
    [PERMISSION_GROUPS.AFFILIATES_MNGMT_READ]: "AFFILIATES_READ",
    [PERMISSION_GROUPS.AFFILIATES_MNGMT_UPDATE]: "AFFILIATES_UPDATE",
    [PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_READ]: "CLIENTS_MGNMT_READ",
    [PERMISSION_GROUPS.MSP_CLIENTS_MGNMT_UPDATE]: "CLIENTS_MGNMT_UPDATE",
};

export type TCompanyAdminCompanyType = TCompanyType | "vendor" | "msp" | "internal_it" | "company" | "client";

export const getAdminCompanyPermission = (
    basePermission: string,
    companyType: TCompanyAdminCompanyType,
): TPermissionGroupsKeys => {
    const companyAdminBasePermission = ADMIN_COMPANY_PERMISSION_GROUP_NAME_OVERWRITES[basePermission] || basePermission;
    let calculatedAdminPermission = "";
    switch (true) {
        case [CompanyType.MSP_CLIENT, "client"].includes(companyType):
            calculatedAdminPermission = "ADMIN_CUST_" + companyAdminBasePermission;
            break;
        case [...MSPCompanyTypes, "msp"].includes(companyType):
            calculatedAdminPermission = "ADMIN_MSP_" + companyAdminBasePermission;
            break;
        case [...VendorCompanyTypes, "vendor", "company"].includes(companyType):
            calculatedAdminPermission = "ADMIN_VENDOR_" + companyAdminBasePermission;
            break;
        case [CompanyType.DIRECT, "internal_it"].includes(companyType):
            calculatedAdminPermission = "ADMIN_INTIT_" + companyAdminBasePermission;
            break;
        default:
            calculatedAdminPermission = companyAdminBasePermission;
            break;
    }
    return PERMISSION_GROUPS[calculatedAdminPermission] ? calculatedAdminPermission : "";
};
