import {useLocation} from "react-router-dom";
import {
    AFFILIATE_WHITELABEL_HOSTS,
    AFFILIATE_WHITELABEL_IGNORE,
    DEFAULT_SUBDOMAINS,
    HTTP_PROTOCOLS,
    MAIN_HOSTS,
} from "../constants/commonStrings.constant";
import routeConfig from "../constants/routeConfig";
import {ISubdomainObj, ISubdomainProps, TSubdomainProp} from "../hooks/useSubdomain";

export const removeQueryParam = (url: string, param: string) => {
    const urlObject = new URL(url);
    urlObject.searchParams.delete(param);
    return urlObject.toString();
};

export const removeQueryParams = (url: string, params: string[]) => {
    const searchParams = new URLSearchParams(location.search);
    params.forEach(param => {
        if (searchParams.has(param)) {
            searchParams.delete(param);
        }
    });
    return `${location.pathname}?${searchParams.toString()}`;
};

export const useIsAdminPage = () => {
    const location = useLocation();
    const isAdminPage = location.pathname.includes("/admin/");

    return isAdminPage;
};

export const makeAbsoluteUrl = (url: string) => {
    if (!url) {
        return "";
    }
    if (!url.match(/^https?:\/\//i)) {
        url = "http://" + url;
    }

    return url;
};

/**
 * Checks if a given url starts with https, if not generates a new url with https
 * @param baseUrl base url
 */
export function makeSafeUrl(baseUrl: string) {
    if (baseUrl.startsWith("https://")) {
        return baseUrl;
    }
    if (baseUrl.startsWith("http://")) {
        return baseUrl.replace("http://", "https://");
    }
    return `https://${baseUrl}`;
}

export const buildSubdomainUrl = (subdomain: string, path?: string) => {
    const protocol = window.location.protocol;
    const host = window.location.host;

    return `${protocol}//${subdomain}.${host}${path || ""}`;
};

export const getLinkWithoutSubdomain = (path: string) => {
    const protocol = window.location.protocol;
    const host = window.location.host;

    return `${protocol}//${host}${path}`;
};

export const redirectWithoutSubdomain = (path: string) => {
    window.location.href = getLinkWithoutSubdomain(path);
};

export const useSafeLocation = () => {
    try {
        const location = useLocation();
        return location;
    } catch (error) {
        return window.location;
    }
};

/**
 * Custom function for extracting and managing subdomain-related data from a given URL.
 *
 * This function parses the URL structure to determine key subdomain components, such as
 * the protocol, main domain, company identifier, and special subdomains used in
 * affiliate/whitelabel configurations. It also provides utility methods for manipulating
 * and formatting subdomain information.
 *
 * @param {ISubdomainProps} [props] - Optional configuration properties.
 * @param {string} [props.url] - The full URL to process. Defaults to `window.location.href`.
 * @param {ISubdomain} [props.defaultValues] - Default values to use when extracting subdomains.
 * @param {boolean} [props.debug] - Enables console logs for debugging purposes.
 *
 * @returns {ISubdomainObj} - An object containing parsed subdomain data and utility methods.
 *
 * @example
 * const subdomainInfo = useSubdomain();
 * console.log(subdomainInfo.domain); // Outputs the main domain
 *
 * @example
 * const subdomainInfo = useSubdomain({ url: "https://msp.example.com/path?query=1" });
 * console.log(subdomainInfo.company); // Outputs "msp"
 *
 * Utility Methods:
 * - `stripSubdomain()`: Returns a new object with the company subdomain removed.
 * - `strip(params)`: Removes specified subdomain properties and updates the host.
 * - `toString()`: Returns the formatted URL string based on parsed subdomain data.
 * - `isActive(path)`: Checks if the given path matches the current pathname.
 *
 * Special Behavior:
 * - Identifies MSP client routes based on predefined configurations.
 * - Supports handling of whitelabel and ignored subdomains.
 */
export const getSubdomain = (props?: ISubdomainProps) => {
    const defaultProps = props?.defaultValues ?? {};
    let fullUrl = !!props?.url && typeof props?.url === "string" ? props?.url : window.location.href;
    // Getting protocol
    const protocol = defaultProps?.protocol ?? HTTP_PROTOCOLS.find(value => fullUrl.includes(value)) ?? "";
    fullUrl = fullUrl.replace(protocol, "");
    // Getting domain
    const domain =
        defaultProps?.domain ??
        [...MAIN_HOSTS, ...AFFILIATE_WHITELABEL_HOSTS].find(value => fullUrl.includes(value)) ??
        "";
    fullUrl = fullUrl.replace(domain, "");
    const [fullHost, searchParams] = fullUrl.split("?");
    const [host, ...pathParts] = fullHost.split("/");
    let pathname = defaultProps?.pathname ?? `/${pathParts.join("/")}`;
    if (!pathname.startsWith("/")) {
        pathname = `/${pathname}`;
    }
    const search = defaultProps?.search ?? searchParams;

    const hostParts = host.split(".");
    const defaultSubdomain = defaultProps?.default ?? hostParts.find(value => DEFAULT_SUBDOMAINS.includes(value));
    const ignoreSubdomain =
        defaultProps?.ignore ?? hostParts.find(value => AFFILIATE_WHITELABEL_IGNORE.includes(value));
    const companySubdomain =
        defaultProps?.company ??
        hostParts.find(
            value =>
                ![
                    ...HTTP_PROTOCOLS,
                    ...AFFILIATE_WHITELABEL_IGNORE,
                    ...DEFAULT_SUBDOMAINS,
                    ...MAIN_HOSTS,
                    ...AFFILIATE_WHITELABEL_HOSTS,
                ].includes(value),
        );
    // Getting finalHost
    const finalHost = [
        ...(ignoreSubdomain ? [ignoreSubdomain] : []),
        ...(companySubdomain ? [companySubdomain] : []),
        ...(defaultSubdomain ? [defaultSubdomain] : []),
        domain,
    ].join(".");
    // Checking if the route if for clients
    const isClientMSPRoute: boolean =
        Object.values(routeConfig).find(value =>
            value.path.includes(pathname?.endsWith("/") ? pathname.slice(0, pathname.length - 1) : pathname),
        )?.isClientMSPPage ?? false;
    const isMSPClientPage = isClientMSPRoute || (AFFILIATE_WHITELABEL_HOSTS.includes(domain) && !ignoreSubdomain);
    // Joinning all subdomains
    const subdomains = [
        ...(ignoreSubdomain ? [ignoreSubdomain] : []),
        ...(companySubdomain ? [companySubdomain] : []),
    ].join(".");
    // Joinning pathname and search
    const path = [pathname, ...(!!search ? [search] : [])].join("?");

    return {
        protocol,
        host: finalHost,
        default: defaultSubdomain,
        ignore: ignoreSubdomain,
        company: companySubdomain,
        domain,
        subdomains,
        pathname,
        path,
        search,
        isMSPClientPage,
        stripSubdomain(): ISubdomainObj {
            return {...this, company: undefined} as ISubdomainObj;
        },
        strip(params: TSubdomainProp | TSubdomainProp[]): ISubdomainObj {
            const paramsArray = typeof params === "string" ? [params] : params;
            const response = {...this};
            paramsArray.forEach(param => (response[param] = ""));
            // Updating host
            response.host = [response.ignore, response.company, response.default, response.domain]
                .filter(item => !!item)
                .join(".");
            return response as ISubdomainObj;
        },
        toString(): string {
            const host = [this.ignore, this.company, this.default, this.domain].filter(item => !!item).join(".");
            return `${this.protocol ?? "http://"}${host}${this.path}`;
        },
        isActive(path: string | string[]): boolean {
            const active = (typeof path !== "string" && path.includes(this.pathname)) || this.pathname === path;
            return active;
        },
    };
};
