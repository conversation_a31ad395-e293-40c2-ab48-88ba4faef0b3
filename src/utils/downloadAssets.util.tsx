export function openAsset(base64String: string | any, fileName?: string) {
    const decodedData = atob(base64String);
    const byteArray = new Uint8Array(decodedData.length);

    for (let i = 0; i < decodedData.length; i++) {
        byteArray[i] = decodedData.charCodeAt(i);
    }

    const blob = new Blob([byteArray], {type: "image/png"});
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.setAttribute("data-track", "false");
    link.href = url;
    link.download = fileName || "image.png";
    document.body.appendChild(link);
    setTimeout(() => {
        window.open(link.href, "_blank");
    }, 200);
}

export function downloadImageFromBase64(base64String: string, fileName?: string): void {
    const decodedData = atob(base64String);
    const byteArray = new Uint8Array(decodedData.length);

    for (let i = 0; i < decodedData.length; i++) {
        byteArray[i] = decodedData.charCodeAt(i);
    }

    const blob = new Blob([byteArray], {type: "image/png"});
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.setAttribute("data-track", "false");
    link.href = url;
    link.download = fileName || "image.png";

    link.click();
}
