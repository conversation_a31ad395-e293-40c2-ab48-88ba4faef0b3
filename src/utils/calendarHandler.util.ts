import {IEventToCalendar} from "../Interfaces/IEventToCalendar.interface";
import {PITCH_DURATION} from "../constants/commonStrings.constant";

const MINUTE_IN_MS = 60 * 1000;

const formatDateForCalendarUrl = (date: Date) => {
    return date.toISOString().replace(/-|:|\.\d+/g, "");
};
const getEndTime = (startDate: Date) => {
    const tempStartDate = startDate;
    const endDate = new Date(tempStartDate.setHours(startDate.getHours() + parseInt(PITCH_DURATION!)));
    return endDate;
};
export const generateGoogleCalendarUrl = (event: IEventToCalendar) => {
    const startDate = formatDateForCalendarUrl(new Date(event.start_date));
    const endDate = formatDateForCalendarUrl(getEndTime(new Date(event.start_date)));
    const encodedUrl = encodeURI(
        [
            "https://www.google.com/calendar/render",
            "?action=TEMPLATE",
            `&text=${event.name || ""}`,
            `&dates=${startDate || ""}`,
            `/${endDate || ""}`,
            `&details=${`${event.description}\n${event.viewer_link}` || ""}`,
            `&location=${""}`,
            "&sprop=&sprop=name:",
        ].join(""),
    );
    return encodedUrl;
};
export const generateIcsCalendarFile = (event: IEventToCalendar) => {
    const startDate = formatDateForCalendarUrl(new Date(event.start_date));
    const endDate = formatDateForCalendarUrl(getEndTime(new Date(event.start_date)));
    const encodedUrl = encodeURI(
        `data:text/calendar;charset=utf8,${[
            "BEGIN:VCALENDAR",
            "VERSION:2.0",
            "BEGIN:VEVENT",
            `URL:${document.URL}`,
            `DTSTART:${startDate || ""}`,
            `DTEND:${endDate || ""}`,
            `SUMMARY:${event.name || ""}`,
            `DESCRIPTION:${event.description || ""} \n ${event.viewer_link}`,
            `LOCATION:${""}`,
            "END:VEVENT",
            "END:VCALENDAR",
        ].join("\n")}`,
    );
    return encodedUrl;
};
export const generateYahooCalendarUrl = (event: IEventToCalendar) => {
    const st = helpers.formatTime(new Date(event.start_date));
    const duration = helpers.getEventDurationForYahoo(event);
    const encodedUrl = encodeURI(
        [
            "http://calendar.yahoo.com/?v=60&view=d&type=20",
            `&title=${event.name || ""}`,
            `&st=${st || ""}`,
            `&dur=${duration || ""}`,
            `&desc=${event.description || ""}\n${event.viewer_link}`,
            `&in_loc=${""}`,
        ].join(""),
    );
    return encodedUrl;
};
const helpers = {
    formatTime(date: Date) {
        return date.toISOString().replace(/-|:|\.\d+/g, "");
    },
    getEventDurationForYahoo(calendarEvent: IEventToCalendar) {
        const eventDuration = calendarEvent.end_date
            ? (new Date(calendarEvent.end_date).getTime() - new Date(calendarEvent.start_date).getTime()) / MINUTE_IN_MS
            : parseInt(PITCH_DURATION!) * 60 || 0;

        const yahooHourDuration =
            eventDuration < 600 ? `0${Math.floor(eventDuration / 60)}` : `${Math.floor(eventDuration / 60)}`;

        const yahooMinuteDuration = eventDuration % 60 < 10 ? `0${eventDuration % 60}` : `${eventDuration % 60}`;

        const yahooEventDuration = yahooHourDuration + yahooMinuteDuration;
        return yahooEventDuration;
    },
};
