import {jwtDecode} from "jwt-decode";
import {DEFAULT_SUBDOMAINS} from "../constants/commonStrings.constant";
import {ADDITIONAL_INFO_COOKIE, AUTH_COOKIE, AUTH_COOKIE_COMPANY_ID, TWOFA_COOKIE} from "../constants/cookies.contant";
import {IS_DEV_ENV} from "../constants/globals.constant";
import {getCookie} from "./cookies.util";
import gZipInflate from "./gZipInflate.util";
import {getSubdomain} from "./url.util";

export const getParentSubdomainUrl = (parentSubdomain: string): string => {
    const subdomain = getSubdomain();
    subdomain.company = parentSubdomain;
    return [subdomain.company, subdomain.default, subdomain.host].join(".");
};

export const getDomainForCookies = () => {
    const subdomains = window.location.hostname.split(".");
    if (DEFAULT_SUBDOMAINS.includes(subdomains[0]) && IS_DEV_ENV) {
        return `${subdomains[0]}.localhost`;
    }
    if (DEFAULT_SUBDOMAINS.includes(subdomains[1]) && IS_DEV_ENV) {
        return `${subdomains[1]}.localhost`;
    }
    const actualDefaultSubdomain = DEFAULT_SUBDOMAINS.find(subdomain => subdomains.includes(subdomain));
    let finalDomain = actualDefaultSubdomain ? actualDefaultSubdomain + "." : "";
    finalDomain += extractDomain(window.location.hostname);
    return finalDomain;
};

export const setSharedTokenCookie = (access_token: string, company_id?: string) => {
    const domain = getDomainForCookies();
    const exdate = new Date();
    exdate.setDate(exdate.getDate() + 1);
    if (getStringSize(access_token) > 4096) {
        console.warn("Cookie size is too big");
        return;
    }
    const newCookie = `${AUTH_COOKIE}=${access_token}; expires=${exdate.toUTCString()}; domain=${domain}; path=/`;
    const newCookieCompanyId = `${AUTH_COOKIE_COMPANY_ID}=${company_id}; expires=${exdate.toUTCString()}; domain=${domain}; path=/`;
    document.cookie = newCookie;
    document.cookie = newCookieCompanyId;
};

export const setShared2FACookie = (twoFA?: boolean) => {
    const domain = getDomainForCookies();
    const exdate = new Date();
    exdate.setDate(exdate.getDate() + 1);
    document.cookie = `${TWOFA_COOKIE}=${twoFA}; expires=${exdate.toUTCString()}; domain=${domain}; path=/`;
};

export const getDecodedSharedCookie = () => {
    const cookie = getCookie(AUTH_COOKIE);
    const twoFACookie = getCookie(TWOFA_COOKIE);
    const subdomainObj = getSubdomain();
    if (!!subdomainObj.company && localStorage.authState) {
        localStorage.removeItem("authState");
    }
    if (cookie) {
        const decoded: any = jwtDecode(cookie);
        let response = {...decoded, access_token: cookie};
        if(!!twoFACookie){
            response = {...response, twoFA:!!twoFACookie}
        }
        return response;
    }
    return null;
};

export const getStringSize = (str: string) => new Blob([str]).size;

export const setSharedInfoCookie = (additional_info_compressed: string) => {
    const domain = getDomainForCookies();
    const exdate = new Date();
    exdate.setDate(exdate.getDate() + 1);
    if (getStringSize(additional_info_compressed) > 4096) {
        console.warn("Cookie size is too big");
        return;
    }
    const newCookie = `${ADDITIONAL_INFO_COOKIE}=${additional_info_compressed}; expires=${exdate.toUTCString()}; domain=${domain}; path=/`;
    document.cookie = newCookie;
};

export const getDecodedSharedInfoCookie = () => {
    const cookie = getCookie(ADDITIONAL_INFO_COOKIE);
    if (cookie) {
        const decoded: any = gZipInflate(cookie, "parsed");
        return decoded;
    }
    return null;
};

export const removeAllCPSharedCookies = () => {
    const domain = getDomainForCookies();
    const cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.includes("CP_")) {
            const cookieName = cookie.split("=")[0];
            document.cookie = cookieName + `=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=${domain}; path=/`;
        }
    }
};

export const generateSubdomainUrl = (subdomain?: string) => {
    if (!subdomain) return "";
    const url = `${window.location.protocol}//${subdomain || ""}.${window.location.host}`;
    return url;
};

export const extractDomain = (value: URL | string) => {
    const url = toURL(value);

    if (!url) {
        return null;
    }

    const hostnameParts = url.hostname.split(".");

    // Determine typical hostnames like "domain.com" or "domain.org"
    if (hostnameParts.length <= 2) {
        return url.hostname;
    }

    // Determine two-part TLD if second last part of the hostname matches one of the suffixes
    const suffixes = ["com", "co", "org", "net", "gov", "edu"];
    const potentialTwoPartTLD = `${hostnameParts[hostnameParts.length - 2]}.${hostnameParts[hostnameParts.length - 1]}`;

    return suffixes.includes(hostnameParts[hostnameParts.length - 2])
        ? `${hostnameParts[hostnameParts.length - 3]}.${potentialTwoPartTLD}`
        : hostnameParts.slice(-2).join("."); // Fallback to last two parts of hostname
};

const toURL = (value: string | URL) => {
    if (value instanceof URL) {
        return value;
    }

    if (typeof value !== "string" || !value) {
        return null;
    }

    // Check if string starts with a protocol, if not, prepend "http://"
    const protocolPattern = /^(https?:\/\/)/i;
    if (!protocolPattern.test(value)) {
        value = `http://${value}` as string;
    }

    try {
        const url = new URL(value);
        return url.hostname ? url : null;
    } catch (error) {
        return null;
    }
};
