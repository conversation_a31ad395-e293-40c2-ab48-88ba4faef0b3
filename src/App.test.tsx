import {expect, test, vi} from "vitest";
import {render} from "@testing-library/react";
import App from "./App";

test("render correctly", () => {
    // mock this method because vitest can't understand it.
    vi.mock("./utils/scrollToTop.util");

    // mock this method because vitest can't understand it.
    vi.spyOn(window, "scrollTo").mockImplementation(() => null);

    // get the correct component
    const result = render(<App />);

    // check the header is showing
    const titleElement: HTMLElement | null = result.container.querySelector("#header");
    expect(titleElement).toBeTruthy();
});
