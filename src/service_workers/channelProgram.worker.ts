import {FileUploadServiceWorker} from "./fileUpload.worker";
/*
 * To activate debug mode, do not change this variable to false;
 * instead, run the script 'build-worker:debug' from package.json
 */
const SERVICE_WORKER_DEBUG_MODE = false;

const availableWorkers = {
    FILE_UPLOAD: new FileUploadServiceWorker(self, SERVICE_WORKER_DEBUG_MODE),
};

self.addEventListener("message", async e => {
    const worker = availableWorkers[e.data.worker] ?? null;
    if (!worker) {
        if (SERVICE_WORKER_DEBUG_MODE) {
            console.error(`Service Worker ${e.data.worker} not found`, e.data);
        }
        return;
    }
    worker.handleMessage(e);
});

self.addEventListener("install", async e => {
    if (SERVICE_WORKER_DEBUG_MODE) {
        console.info("Installing Service Worker: ", e);
    }
    (e.target as any)?.skipWaiting();
});

self.addEventListener("activate", async e => {
    if (SERVICE_WORKER_DEBUG_MODE) {
        console.info("Activating Service Worker: ", e);
    }
    // Making the SW the claimer of the page
    (e as any).waitUntil(
        (self as any).clients.claim().then(() => {
            if (SERVICE_WORKER_DEBUG_MODE) {
                console.info("Service Worker now claims all pages.");
            }
        }),
    );
});
