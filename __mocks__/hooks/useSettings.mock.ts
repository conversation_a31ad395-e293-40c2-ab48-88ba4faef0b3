import {Mock, beforeEach, vi} from "vitest";
import useSettings from "../../src/hooks/useSettings";

vi.mock("../../src/hooks/useSettings", () => ({
    default: vi.fn(),
}));

beforeEach(() => {
    (useSettings as Mock).mockReturnValue(settingsMock);
});

const settingsMock = {
    updateSettings: vi.fn(),
    clearSettings: vi.fn(),
    getAllCategories: vi.fn(),
    getAllCountries: vi.fn(),
    getAllLanguages: vi.fn(),
    getAllSocialTypes: vi.fn(),
    settings: {
        lang: "en",
        theme: "dark",
        loading: false,
        notification: {show: false},
        profaneModal: false,
        all_categories: [
            {
                id: "914121886441897985",
                parent_id: "914121886306795521",
                name: "Accounting",
                friendly_url: "accounting",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889178779649",
                parent_id: "914121888924631041",
                name: "Active Directory",
                friendly_url: "active-directory",
                number_of_products: 2,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: true,
                is_hidden: false,
                is_top_category: true,
                stack_chart_min_y: "0",
                stack_chart_max_y: "1",
                stack_chart_min_x: "0",
                stack_chart_max_x: "5",
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883919319041",
                parent_id: "914121883794702337",
                name: "Affiliate Marketing Software",
                friendly_url: "affiliate-marketing-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890267758593",
                parent_id: "914121890110898177",
                name: "AIaaS (Artificial Intelligence as a Service)",
                friendly_url: "aiaas-artificial-intelligence-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888492093441",
                parent_id: "914121888413417473",
                name: "Application",
                friendly_url: "application",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884607938561",
                parent_id: "914121884373516289",
                name: "Appointment Software",
                friendly_url: "appointment-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890040643585",
                parent_id: "914121889500561409",
                name: "Archiving",
                friendly_url: "archiving",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889403928577",
                parent_id: "914121888924631041",
                name: "Artificial Intelligence",
                friendly_url: "artificial-intelligence",
                number_of_products: 3,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884128542721",
                parent_id: "914121883955953665",
                name: "Assessment",
                friendly_url: "assessment",
                number_of_products: 2,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857334345729",
                parent_id: "914121888413417473",
                name: "Asset & Licensing",
                friendly_url: "asset-licensing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888527515649",
                parent_id: "914121888413417473",
                name: "Audit Logging",
                friendly_url: "audit-logging",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884249128961",
                parent_id: "914121883955953665",
                name: "Auditing",
                friendly_url: "auditing",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888924631041",
                parent_id: null,
                name: "Authentication",
                friendly_url: "authentication",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885108371457",
                parent_id: "914121885003448321",
                name: "Auto Dialer Software",
                friendly_url: "auto-dialer-software",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892052172801",
                parent_id: "914121891870408705",
                name: "AWS (Amazon Web Services)",
                friendly_url: "aws-amazon-web-services",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891981459457",
                parent_id: "914121891870408705",
                name: "Azure",
                friendly_url: "azure",
                number_of_products: 2,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890360393729",
                parent_id: "914121890110898177",
                name: "BaaS (Backend as a Service)",
                friendly_url: "baas-backend-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889500561409",
                parent_id: null,
                name: "Backup & Recovery",
                friendly_url: "backup-recovery",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886536925185",
                parent_id: "914121886306795521",
                name: "Banking",
                friendly_url: "banking",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889544765441",
                parent_id: "914121889500561409",
                name: "BCDR (Backup & Disaster Recovery)",
                friendly_url: "bcdr-backup-disaster-recovery",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889448656897",
                parent_id: "914121888924631041",
                name: "Behavioral",
                friendly_url: "behavioral",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886353326081",
                parent_id: "914121886306795521",
                name: "Billing Management",
                friendly_url: "billing-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889361166337",
                parent_id: "914121888924631041",
                name: "Biometric",
                friendly_url: "biometric",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884646834177",
                parent_id: "914121884373516289",
                name: "Business Automation",
                friendly_url: "business-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857601994753",
                parent_id: "914121889500561409",
                name: "Business Continuity",
                friendly_url: "business-continuity",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884531654657",
                parent_id: "914121884373516289",
                name: "Business Insurance",
                friendly_url: "business-insurance",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857872527361",
                parent_id: "914121884373516289",
                name: "Business Intelligence",
                friendly_url: "business-intelligence",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891936632833",
                parent_id: "914121891870408705",
                name: "Business Strategy",
                friendly_url: "business-strategy",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891761913857",
                parent_id: "914121891545317377",
                name: "Chat Software",
                friendly_url: "chat-software",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889226457089",
                parent_id: "914121888924631041",
                name: "Cloud Directory",
                friendly_url: "cloud-directory",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886257774593",
                parent_id: "914121885665034241",
                name: "Cloud Management",
                friendly_url: "cloud-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887855837185",
                parent_id: "914121857711833089",
                name: "Cloud Security",
                friendly_url: "cloud-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885704028161",
                parent_id: "914121885665034241",
                name: "Cloud Storage",
                friendly_url: "cloud-storage",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884879126529",
                parent_id: "914121884373516289",
                name: "Collaboration",
                friendly_url: "collaboration",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891194896385",
                parent_id: "914121890707046401",
                name: "Colocation",
                friendly_url: "colocation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891545317377",
                parent_id: null,
                name: "Communications",
                friendly_url: "communications",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883955953665",
                parent_id: null,
                name: "Compliance",
                friendly_url: "compliance",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890848735233",
                parent_id: "914121890707046401",
                name: "Computers",
                friendly_url: "computers",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888365019137",
                parent_id: "914121887941361665",
                name: "Consulting",
                friendly_url: "professional-services-consulting",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891870408705",
                parent_id: null,
                name: "Consulting",
                friendly_url: "consulting",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887343149057",
                parent_id: "914121857711833089",
                name: "Content Filtering",
                friendly_url: "content-filtering",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885151494145",
                parent_id: "914121885003448321",
                name: "Contract Analytics Software",
                friendly_url: "contract-analytics-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885196451841",
                parent_id: "914121885003448321",
                name: "Contract Lifecycle Management (CLM) Software",
                friendly_url: "contract-lifecycle-management-clm-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885241868289",
                parent_id: "914121885003448321",
                name: "Contract Management Software",
                friendly_url: "contract-management-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885040738305",
                parent_id: "914121885003448321",
                name: "CRM (Customer Relationship Management)",
                friendly_url: "crm-customer-relationship-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886728847361",
                parent_id: null,
                name: "Customer Experience",
                friendly_url: "customer-experience",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886882758657",
                parent_id: "914121886728847361",
                name: "Customer Feedback",
                friendly_url: "customer-feedback",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886921261057",
                parent_id: "914121886728847361",
                name: "Customer Portals",
                friendly_url: "customer-portals",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884738781185",
                parent_id: "914121884373516289",
                name: "Cyber Insurance",
                friendly_url: "cyber-insurance",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857711833089",
                parent_id: null,
                name: "Cybersecurity",
                friendly_url: "cybersecurity",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890183544833",
                parent_id: "914121890110898177",
                name: "DaaS (Desktop as a Service)",
                friendly_url: "daas-desktop-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887174819841",
                parent_id: "914121857711833089",
                name: "Dark Web Monitoring",
                friendly_url: "dark-web-monitoring",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884324102145",
                parent_id: "914121883955953665",
                name: "Data Privacy, Governance & Risk",
                friendly_url: "data-privacy-governance-risk",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889949155329",
                parent_id: "914121889500561409",
                name: "Data Warehouse",
                friendly_url: "data-warehouse",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890595176449",
                parent_id: "914121890110898177",
                name: "DBaaS (Database as a Service)",
                friendly_url: "dbaas-database-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883883798529",
                parent_id: "914121883794702337",
                name: "Demand Generation Software",
                friendly_url: "demand-generation-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888274644993",
                parent_id: "914121887941361665",
                name: "Distributors",
                friendly_url: "distributors",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889640579073",
                parent_id: "914121889500561409",
                name: "DLP (Data Loss Protection)",
                friendly_url: "dlp-data-loss-protection",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857751154689",
                parent_id: "914121857711833089",
                name: "DNS Protection",
                friendly_url: "dns-protection",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884282093569",
                parent_id: "914121883955953665",
                name: "Document Shredding",
                friendly_url: "document-shredding",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884167471105",
                parent_id: "914121883955953665",
                name: "Documentation",
                friendly_url: "documentation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884786032641",
                parent_id: "914121884373516289",
                name: "Documentation",
                friendly_url: "it-operations-documentation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890504146945",
                parent_id: "914121890110898177",
                name: "DRaaS (Disaster Recovery as a Service)",
                friendly_url: "draas-disaster-recovery-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884499574785",
                parent_id: "914121884373516289",
                name: "E-Signature",
                friendly_url: "e-signature",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891331276801",
                parent_id: null,
                name: "Education",
                friendly_url: "education",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889900986369",
                parent_id: "914121889500561409",
                name: "Email",
                friendly_url: "email",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121882836303873",
                parent_id: "914121891545317377",
                name: "Email & Office Software",
                friendly_url: "email-office-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121882856292353",
                parent_id: "914121857711833089",
                name: "Email Security",
                friendly_url: "email-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888687030273",
                parent_id: "914121888413417473",
                name: "Employee",
                friendly_url: "employee",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857242988545",
                parent_id: "914121884373516289",
                name: "Employee Engagement",
                friendly_url: "employee-engagement",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888831143937",
                parent_id: "914121888413417473",
                name: "Endpoint",
                friendly_url: "endpoint",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889773879297",
                parent_id: "914121889500561409",
                name: "Exchange",
                friendly_url: "exchange",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890631319553",
                parent_id: "914121890110898177",
                name: "FaaS (Function as a Service)",
                friendly_url: "faas-function-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885287055361",
                parent_id: "914121885003448321",
                name: "Field Sales Software",
                friendly_url: "field-sales-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884569501697",
                parent_id: "914121884373516289",
                name: "Field Service Management",
                friendly_url: "field-service-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889862189057",
                parent_id: "914121889500561409",
                name: "File",
                friendly_url: "file",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885842604033",
                parent_id: "914121885665034241",
                name: "File Sharing",
                friendly_url: "file-sharing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886306795521",
                parent_id: null,
                name: "Finance",
                friendly_url: "finance",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888571949057",
                parent_id: "914121888413417473",
                name: "Firewall",
                friendly_url: "firewall",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890799943681",
                parent_id: "914121890707046401",
                name: "Firewalls",
                friendly_url: "firewalls",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891100786689",
                parent_id: "914121890707046401",
                name: "Forensic",
                friendly_url: "forensic",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857786150913",
                parent_id: "914121887941361665",
                name: "Forensics",
                friendly_url: "forensics",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886589157377",
                parent_id: "914121886306795521",
                name: "Funding",
                friendly_url: "funding",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889730297857",
                parent_id: "914121889500561409",
                name: "G-Suite",
                friendly_url: "g-suite",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884022898689",
                parent_id: "914121883955953665",
                name: "GDPR (General Data Protection Regulation)",
                friendly_url: "gdpr-general-data-protection-regulation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890549006337",
                parent_id: "914121890110898177",
                name: "HaaS (Hardware as a Service)",
                friendly_url: "haas-hardware-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890707046401",
                parent_id: null,
                name: "Hardware",
                friendly_url: "hardware",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890073509889",
                parent_id: "914121889500561409",
                name: "Health Records",
                friendly_url: "health-records",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892344430593",
                parent_id: "914121891870408705",
                name: "HIPPA",
                friendly_url: "hippa",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883989704705",
                parent_id: "914121883955953665",
                name: "HIPPA (Health Insurance Portability and Accountability Act)",
                friendly_url: "hippa-health-insurance-portability-and-accountability-act",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892383916033",
                parent_id: "914121891870408705",
                name: "HITECH",
                friendly_url: "hitech",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884422078465",
                parent_id: "914121884373516289",
                name: "HR (Human Resource Software)",
                friendly_url: "hr-human-resource-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890224537601",
                parent_id: "914121890110898177",
                name: "IaaS (Infrastructure as a Service)",
                friendly_url: "iaas-infrastructure-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889007239169",
                parent_id: "914121888924631041",
                name: "IAM (Identity & Access Management)",
                friendly_url: "iam-identity-access-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887300124673",
                parent_id: "914121857711833089",
                name: "IDP (Identify, Detect & Protect)",
                friendly_url: "idp-identify-detect-protect",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891148693505",
                parent_id: "914121890707046401",
                name: "iOT (Internet of Things)",
                friendly_url: "iot-internet-of-things",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857826783233",
                parent_id: "914121885665034241",
                name: "IT Automation",
                friendly_url: "it-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885665034241",
                parent_id: null,
                name: "IT Management",
                friendly_url: "it-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884373516289",
                parent_id: null,
                name: "IT Operations",
                friendly_url: "it-operations",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886844485633",
                parent_id: "914121886728847361",
                name: "KB (Knowledgebase)",
                friendly_url: "kb-knowledgebase",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888134234113",
                parent_id: "914121887941361665",
                name: "Lead Generation",
                friendly_url: "lead-generation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886490034177",
                parent_id: "914121886306795521",
                name: "Leasing & Credit",
                friendly_url: "leasing-credit",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884206891009",
                parent_id: "914121883955953665",
                name: "Log Management",
                friendly_url: "log-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886019092481",
                parent_id: "914121885665034241",
                name: "Managed Printing",
                friendly_url: "managed-printing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888225656833",
                parent_id: "914121887941361665",
                name: "Managed Sales",
                friendly_url: "managed-sales",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888318881793",
                parent_id: "914121887941361665",
                name: "Marketing",
                friendly_url: "professional-services-marketing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892194451457",
                parent_id: "914121891870408705",
                name: "Marketing",
                friendly_url: "consulting-marketing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883794702337",
                parent_id: null,
                name: "Marketing",
                friendly_url: "marketing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121883843788801",
                parent_id: "914121883794702337",
                name: "Marketing Automation",
                friendly_url: "marketing-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888087113729",
                parent_id: "914121887941361665",
                name: "Marketing Automation",
                friendly_url: "professional-services-marketing-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889270661121",
                parent_id: "914121888924631041",
                name: "MDM (Mobile Device Management)",
                friendly_url: "mdm-mobile-device-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887410946049",
                parent_id: "914121857711833089",
                name: "MDR (Managed Detection & Response)",
                friendly_url: "mdr-managed-detection-response",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889066123265",
                parent_id: "914121888924631041",
                name: "MFA (Multi-Factor Authentication)",
                friendly_url: "mfa-multi-factor-authentication",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892102307841",
                parent_id: "914121891870408705",
                name: "Migration",
                friendly_url: "migration",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886107009025",
                parent_id: "914121885665034241",
                name: "Migration Tools",
                friendly_url: "migration-tools",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888413417473",
                parent_id: null,
                name: "Monitoring",
                friendly_url: "monitoring",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888733200385",
                parent_id: "914121888413417473",
                name: "Network",
                friendly_url: "network",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890951659521",
                parent_id: "914121890707046401",
                name: "Networking",
                friendly_url: "networking",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884053078017",
                parent_id: "914121883955953665",
                name: "NIST (National Institute of Standards and Technology)",
                friendly_url: "nist-national-institute-of-standards-and-technology",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888461619201",
                parent_id: "914121888413417473",
                name: "NOC (Network Operation Center)",
                friendly_url: "noc-network-operation-center",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857639350273",
                parent_id: "914121889500561409",
                name: "Office 365",
                friendly_url: "office-365",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884839149569",
                parent_id: "914121884373516289",
                name: "Office Procurement",
                friendly_url: "office-procurement",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889036271617",
                parent_id: "914121888924631041",
                name: "On-Premise",
                friendly_url: "on-premise",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887745540097",
                parent_id: "914121857711833089",
                name: "On-Premise Security",
                friendly_url: "on-premise-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886809194497",
                parent_id: "914121886728847361",
                name: "Onboarding Platform",
                friendly_url: "onboarding-platform",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890148646913",
                parent_id: "914121890110898177",
                name: "PaaS (Platform as a Service)",
                friendly_url: "paas-platform-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885333094401",
                parent_id: "914121885003448321",
                name: "Partner Management Software",
                friendly_url: "partner-management-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857288667137",
                parent_id: "914121888924631041",
                name: "Password Management",
                friendly_url: "password-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889317978113",
                parent_id: "914121888924631041",
                name: "Passwordless",
                friendly_url: "passwordless",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886397497345",
                parent_id: "914121886306795521",
                name: "Payroll",
                friendly_url: "payroll",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884090466305",
                parent_id: "914121883955953665",
                name: "PCI (Payment Card Industry)",
                friendly_url: "pci-payment-card-industry",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888177684481",
                parent_id: "914121887941361665",
                name: "Peer Groups",
                friendly_url: "peer-groups",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887044993025",
                parent_id: "914121857711833089",
                name: "Pentration Testing",
                friendly_url: "pentration-testing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887656968193",
                parent_id: "914121857711833089",
                name: "Phishing Security",
                friendly_url: "phishing-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887443255297",
                parent_id: "914121857711833089",
                name: "Phishing Simulation",
                friendly_url: "phishing-simulation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885933633537",
                parent_id: "914121885665034241",
                name: "Policy Management",
                friendly_url: "policy-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891004055553",
                parent_id: "914121890707046401",
                name: "Procurement",
                friendly_url: "procurement",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887941361665",
                parent_id: null,
                name: "Professional Services",
                friendly_url: "professional-services",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892247601153",
                parent_id: "914121891870408705",
                name: "Project",
                friendly_url: "project",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884953313281",
                parent_id: "914121884373516289",
                name: "Project Management",
                friendly_url: "project-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888010338305",
                parent_id: "914121887941361665",
                name: "PSA (Professional Services Automation)",
                friendly_url: "psa-professional-services-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886968119297",
                parent_id: "914121886728847361",
                name: "QBR (Quarterly Business Report)",
                friendly_url: "qbr-quarterly-business-report",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885375823873",
                parent_id: "914121885003448321",
                name: "Quote Management Software",
                friendly_url: "quote-management-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886635819009",
                parent_id: "914121886306795521",
                name: "Quoting & Invoicing",
                friendly_url: "quoting-invoicing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887477170177",
                parent_id: "914121857711833089",
                name: "Ransomware Security",
                friendly_url: "ransomware-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886679990273",
                parent_id: "914121886306795521",
                name: "Reconciliation",
                friendly_url: "reconciliation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891051241473",
                parent_id: "914121890707046401",
                name: "Refurbished",
                friendly_url: "refurbished",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857390018561",
                parent_id: "914121885665034241",
                name: "Remote Control & RDP",
                friendly_url: "remote-control-rdp",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886155276289",
                parent_id: "914121885665034241",
                name: "Repair Utilities",
                friendly_url: "repair-utilities",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885420027905",
                parent_id: "914121885003448321",
                name: "Revenue Operations Software",
                friendly_url: "revenue-operations-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887089098753",
                parent_id: "914121857711833089",
                name: "Risk Assessment",
                friendly_url: "risk-assessment",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885749673985",
                parent_id: "914121885665034241",
                name: "RMM (Remote Management & Monitoring)",
                friendly_url: "rmm-remote-management-monitoring",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885972430849",
                parent_id: "914121885665034241",
                name: "RPA (Robotic Process Automation)",
                friendly_url: "rpa-robotic-process-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887704612865",
                parent_id: "914121857711833089",
                name: "SaaS Management & Security",
                friendly_url: "saas-management-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885003448321",
                parent_id: null,
                name: "Sales",
                friendly_url: "sales",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885483696129",
                parent_id: "914121885003448321",
                name: "Sales Acceleration Software",
                friendly_url: "sales-acceleration-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885533601793",
                parent_id: "914121885003448321",
                name: "Sales Analytics Software",
                friendly_url: "sales-analytics-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885071769601",
                parent_id: "914121885003448321",
                name: "Sales Compensation Software",
                friendly_url: "sales-compensation-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885579247617",
                parent_id: "914121885003448321",
                name: "Sales Gamification Software",
                friendly_url: "sales-gamification-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885620109313",
                parent_id: "914121885003448321",
                name: "Sales Intelligence Software",
                friendly_url: "sales-intelligence-software",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891489808385",
                parent_id: "914121891331276801",
                name: "Sales Training",
                friendly_url: "sales-training",
                number_of_products: 1,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885885890561",
                parent_id: "914121885665034241",
                name: "Scripting",
                friendly_url: "scripting",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892149067777",
                parent_id: "914121891870408705",
                name: "Security",
                friendly_url: "security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891442884609",
                parent_id: "914121891331276801",
                name: "Security Awareness Training",
                friendly_url: "security-awareness-training",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891230220289",
                parent_id: "914121890707046401",
                name: "Server Farm",
                friendly_url: "server-farm",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890897690625",
                parent_id: "914121890707046401",
                name: "Servers",
                friendly_url: "servers",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886775967745",
                parent_id: "914121886728847361",
                name: "Service Desk",
                friendly_url: "service-desk",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121892302290945",
                parent_id: "914121891870408705",
                name: "Sharepoint",
                friendly_url: "consulting-sharepoint",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889687011329",
                parent_id: "914121889500561409",
                name: "Sharepoint",
                friendly_url: "sharepoint",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887379587073",
                parent_id: "914121857711833089",
                name: "SIEM (Security Information & Event Management)",
                friendly_url: "siem-security-information-event-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888616185857",
                parent_id: "914121888413417473",
                name: "SIEM (Security Information and Event Management)",
                friendly_url: "siem-security-information-and-event-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887605424129",
                parent_id: "914121857711833089",
                name: "SOAR (Security Automation, Orchestration & Response)",
                friendly_url: "soar-security-automation-orchestration-response",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887132450817",
                parent_id: "914121857711833089",
                name: "SOC (Security Operations Center)",
                friendly_url: "soc-security-operations-center",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889104920577",
                parent_id: "914121888924631041",
                name: "SSO (Single Sign-on)",
                friendly_url: "sso-single-sign-on",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121884693233665",
                parent_id: "914121884373516289",
                name: "Staffing",
                friendly_url: "staffing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888780976129",
                parent_id: "914121888413417473",
                name: "Storage and Database",
                friendly_url: "storage-and-database",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891722199041",
                parent_id: "914121891545317377",
                name: "Team Collaboration",
                friendly_url: "team-collaboration",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891393470465",
                parent_id: "914121891331276801",
                name: "Training & Certifications",
                friendly_url: "training-certifications",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891795795969",
                parent_id: "914121891545317377",
                name: "UCaaS (Unified Communications as a Service)",
                friendly_url: "communications-ucaas-unified-communications-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890406006785",
                parent_id: "914121890110898177",
                name: "UCaaS (Unified Communications as a Service)",
                friendly_url: "ucaas-unified-communications-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890759540737",
                parent_id: "914121890707046401",
                name: "UPS (Univeral Power Supply)",
                friendly_url: "ups-univeral-power-supply",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890454241281",
                parent_id: "914121890110898177",
                name: "VaaS (Virtualization as a Service)",
                friendly_url: "vaas-virtualization-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888047333377",
                parent_id: "914121887941361665",
                name: "vCIO (Virtual Chief Information Officer)",
                friendly_url: "vcio-virtual-chief-information-officer",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891668688897",
                parent_id: "914121891545317377",
                name: "Video Conferencing",
                friendly_url: "video-conferencing",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121889819787265",
                parent_id: "914121889500561409",
                name: "Virtual Machine",
                friendly_url: "virtual-machine",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121891617505281",
                parent_id: "914121891545317377",
                name: "VoIP (Voice over IP)",
                friendly_url: "voip-voice-over-ip",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121885797351425",
                parent_id: "914121885665034241",
                name: "VPN (Virtual Private Network)",
                friendly_url: "vpn-virtual-private-network",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887558795265",
                parent_id: "914121857711833089",
                name: "Vulnerability Assessment & Management",
                friendly_url: "vulnerability-assessment-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: true,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890663202817",
                parent_id: "914121890110898177",
                name: "WaaS Workspace as a Service",
                friendly_url: "waas-workspace-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121857200324609",
                parent_id: "914121888413417473",
                name: "Warranty Management",
                friendly_url: "warranty-management",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887218368513",
                parent_id: "914121857711833089",
                name: "Web Application Security",
                friendly_url: "web-application-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890001059841",
                parent_id: "914121889500561409",
                name: "Website",
                friendly_url: "website",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121886219796481",
                parent_id: "914121885665034241",
                name: "Workflow Automation",
                friendly_url: "workflow-automation",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890314092545",
                parent_id: "914121890110898177",
                name: "XaaS (Everything as a Service)",
                friendly_url: "xaas-everything-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887514394625",
                parent_id: "914121857711833089",
                name: "XDR (Cross-layered Detection & Response)",
                friendly_url: "xdr-cross-layered-detection-response",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121890110898177",
                parent_id: null,
                name: "XYZ as a Service",
                friendly_url: "xyz-as-a-service",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121887780012033",
                parent_id: "914121857711833089",
                name: "Zero Day Security",
                friendly_url: "zero-day-security",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
            {
                id: "914121888971063297",
                parent_id: "914121888924631041",
                name: "Zero Trust",
                friendly_url: "zero-trust",
                number_of_products: 0,
                icon_name: null,
                color: "#000000",
                description: null,
                featured: false,
                is_hidden: false,
                is_top_category: false,
                stack_chart_min_y: null,
                stack_chart_max_y: null,
                stack_chart_min_x: null,
                stack_chart_max_x: null,
                default_my_stack_category: false,
                navistack_reminder: false,
                "sub-categories": null,
            },
        ],
    },
};
