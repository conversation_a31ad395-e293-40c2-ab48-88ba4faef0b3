import {beforeEach, Mock, vi} from "vitest";
import useActiveCompany, {IActiveCompany} from "../../src/hooks/useActiveCompany";

vi.mock("../../src/hooks/useActiveCompany", () => ({
    default: vi.fn(),
}));

beforeEach(() => {
    (useActiveCompany as Mock).mockReturnValue({
        activeCompany: mockedActiveCompany,
        setActiveCompany: vi.fn(),
        clearActiveCompany: vi.fn(),
        selectedFriendlyUrl: vi.fn(),
        isLoading: false,
        isMSP: false,
        isClientMsp: false,
        isAffiliateMsp: false,
    });
});

const mockedActiveCompany: IActiveCompany = {
    id: "766586073155993601",
    name: "About 27",
    company_type: "VENDOR_ALL",
    type_is_of_vendor: true,
    friendly_url: "about27",
    subdomain: "about271",
    profile_company_friendly_name: null,
    profile_images: {
        CompanyAvatar: [
            {
                src: "https://pedro-cp-public.s3.us-east-005.backblazeb2.com/915269238276227073/pngtree-little-ghost-smiling-cartoon-png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=00529fd9a0348060000000001%2F20240730%2Fus-east-005%2Fs3%2Faws4_request&X-Amz-Date=20240730T175838Z&X-Amz-SignedHeaders=host&X-Amz-Expires=259199&X-Amz-Signature=b265c75e05e7ba066908524b54e34eafde06de1ad0cc717199bf3cc484db9640",
                id: "915269238276227073",
                type: "CompanyAvatar",
            },
        ],
    },
    created: "2022-05-31T16:18:23.000000Z",
    updated: "2024-07-24T14:53:17.000000Z",
    current_user_is_following: true,
    company_profile_type: {
        id: "7",
        label: "Vendor Enterprise",
        value: "VENDOR_ENTERPRISE",
        order: 7,
    },
    partner_flag: true,
    show_distributor_banner: true,
    show_manage_clients_banner: true,
    show_manage_affiliates_banner: true,
    total_approved_product_review: 1,
    total_accept_request: 5,
    total_saved_template: 0,
    total_pending_request: 2,
    is_distributor: false,
    manage_clients: false,
    manage_affiliates: false,
    parent: null,
    is_affiliate_brand_main_company: false,
    is_mdf: false,
    avatar: "https://pedro-cp-public.s3.us-east-005.backblazeb2.com/915269238276227073/pngtree-little-ghost-smiling-cartoon-png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=00529fd9a0348060000000001%2F20240730%2Fus-east-005%2Fs3%2Faws4_request&X-Amz-Date=20240730T175838Z&X-Amz-SignedHeaders=host&X-Amz-Expires=259199&X-Amz-Signature=b265c75e05e7ba066908524b54e34eafde06de1ad0cc717199bf3cc484db9640",
    counts: {
        vendor: {
            total_approved_product_review: 1,
            total_pending_request: 2,
            total_accept_request: 5,
        },
        msp: {
            total_saved_template: 0,
            total_folder: 0,
        },
    },
};
