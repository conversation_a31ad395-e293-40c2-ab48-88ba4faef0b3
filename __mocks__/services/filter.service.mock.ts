import {vi} from "vitest";
import {filterService} from "../../src/services/filters.service";
const mockResponse = Promise.resolve({data: {filters: [], sorts: []}});

vi.mock("../../src/services/filters.service", () => ({
    filterService: {
        GetUserTypes: vi.fn(() => mockResponse),
        GetProfilesTypes: vi.fn(() => mockResponse),
        GetProductsCategories: vi.fn(() => mockResponse),
        GetVideoCategories: vi.fn(() => mockResponse),
        GetUsedCategories: vi.fn(() => mockResponse),
        GetAllCategories: vi.fn(() => mockResponse),
        GetTopVideoCategoriesAndTags: vi.fn(() => mockResponse),
        getPitchBubbleMetrics: vi.fn(() => mockResponse),
        getPitchBubbleDates: vi.fn(() => mockResponse),
        getPitchVendorsByDate: vi.fn(() => mockResponse),
        getPitchPollProductCategoriesByVendor: vi.fn(() => mockResponse),
        getPitchEventsWithAnswers: vi.fn(() => mockResponse),
        getQuadrantProductCategories: vi.fn(() => mockResponse),
        getPitchPollCompanies: vi.fn(() => mockResponse),
        getNavistackDocumentsFilters: vi.fn(() => mockResponse),
        getMspContractBillingTypes: vi.fn(() => mockResponse),
        getMyPartnersFilters: vi.fn(() => mockResponse),
        getMyContactListPartnersFilters: vi.fn(() => mockResponse),
        getMspContractTypes: vi.fn(() => mockResponse),
        getCurrencyTypes: vi.fn(() => mockResponse),
        getContractNotificationTypes: vi.fn(() => mockResponse),
        getContractBillingTypeOptions: vi.fn(() => mockResponse),
        getPartnerInvitationsFilters: vi.fn(() => mockResponse),
        getFrequencyOptions: vi.fn(() => mockResponse),
        getBrandCompanies: vi.fn(() => mockResponse),
        getMspClientsFilters: vi.fn(() => mockResponse),
        getCompanyUsersFilters: vi.fn(() => mockResponse),
        getCompanyAffiliatesFilters: vi.fn(() => mockResponse),
    },
}));

export {filterService};
