{"name": "channel-program", "version": "14.0.0", "private": true, "dependencies": {"@builder.io/partytown": "^0.7.1", "@builder.io/react": "^3.0.8", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@lexical/clipboard": "0.16.0", "@lexical/file": "0.16.0", "@lexical/hashtag": "0.16.0", "@lexical/link": "0.16.0", "@lexical/list": "0.16.0", "@lexical/mark": "0.16.0", "@lexical/overflow": "0.16.0", "@lexical/plain-text": "0.16.0", "@lexical/react": "0.16.0", "@lexical/rich-text": "0.16.0", "@lexical/selection": "0.16.0", "@lexical/utils": "0.16.0", "@loadable/component": "^5.15.2", "@mantine/core": "^5.6.2", "@mantine/hooks": "^5.6.2", "@mantine/notifications": "^5.6.2", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.6", "@mui/x-charts": "^6.18.3", "@mui/x-date-pickers": "^6.18.4", "@react-pdf/renderer": "^3.1.3", "@tanstack/react-query": "^4.19.1", "@tanstack/react-query-devtools": "^4.19.1", "@types/autolinker": "^2.0.0", "@types/dompurify": "^2.3.4", "@types/node": "^18.11.7", "@types/pako": "^2.0.0", "@types/react": "^18.0.23", "@types/react-calendar": "^4.1.0", "@types/react-dom": "^18.0.7", "@types/react-resizable": "^3.0.3", "@types/react-table": "^7.7.12", "@vitejs/plugin-react": "^4.2.1", "autolinker": "^4.0.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.1.3", "bootstrap": "^5.2.2", "browserslist-to-esbuild": "^2.1.1", "buffer": "^6.0.3", "core-js": "^3.26.0", "dom-to-image": "^2.6.0", "dompurify": "^2.4.0", "email-validator": "^2.0.4", "emoji-datasource-apple": "^15.1.2", "emoji-picker-react": "^4.4.3", "fbjs": "^3.0.4", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "http-proxy-middleware": "^2.0.6", "jotai": "^2.12.2", "jwt-decode": "^4.0.0", "lexical": "^0.16.0", "lottie-react": "^2.4.1", "luxon": "^3.5.0", "pako": "^2.1.0", "pdf-lib": "^1.17.1", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-bootstrap-typeahead": "^6.4.0", "react-calendar": "^5.1.0", "react-content-loader": "^7.0.2", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "^18.3.1", "react-google-recaptcha-v3": "^1.10.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-hubspot-form": "^1.3.7", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-plaid-link": "^3.6.1", "react-player": "^2.16.0", "react-resizable": "^3.0.5", "react-router-dom": "^7.4.0", "react-share": "^5.2.2", "react-slick": "^0.30.3", "react-table": "^7.8.0", "recharts": "^2.15.1", "slick-carousel": "^1.8.1", "styled-components": "^6.1.16", "uuidv4": "^6.2.13", "vite": "^6.2.2", "web-vitals": "^4.2.4"}, "scripts": {"start": "vite", "build-worker": "node --experimental-modules scripts/build-service-workers.mjs", "build-worker:debug": "node --experimental-modules scripts/build-service-workers.mjs -- --debug", "start-alt": "vite --port 3001", "build": "npm run partytown && node --max-old-space-size=4096 ./node_modules/vite/bin/vite.js build", "preview": "vite preview", "test": "vitest --reporter=html", "test:silent": "vitest --reporter=verbose --silent", "test:console": "vitest --reporter=verbose", "test:preview": "vite preview --outDir html", "eject": "vite eject", "layout": "shx cp ./build/index.html ../app/views/pages/index.html.liquid", "assets:remove": "shx rm -rf ../app/assets/web-app", "assets:copy": "shx mv ./build/ ../app/assets/web-app", "partytown": "partytown copylib public/~partytown", "analyze": "source-map-explorer 'build/static/js/*.js'", "postinstall": "patch-package", "setup-precommit": "git config core.hooksPath git-hooks --local ; git config core.hooksPath ./git-hooks --local ; chmod ug+x git-hooks/* ; attrib +x git-hooks/*", "lint": "eslint .", "lint:fix": "eslint --fix", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "transform:debug": "vite --debug transform"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.3%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", ">0.3%", "not dead", "not op_mini all"]}, "resolutions": {"styled-components": "^5", "react-side-effect": "^2.1.0"}, "devDependencies": {"@mui/types": "^7.2.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/dom-to-image": "^2.6.6", "@types/luxon": "^3.0.2", "@types/react-bootstrap-typeahead": "^5.1.8", "@types/react-dnd": "^3.0.2", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.12", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-react-swc": "^3.6.0", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "chalk": "^5.1.2", "css-minimizer-webpack-plugin": "^4.2.2", "cssnano": "^5.1.13", "cssnano-preset-default": "^5.2.12", "cssnano-utils": "^3.1.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "identity-obj-proxy": "^3.0.0", "patch-package": "^8.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "progress-bar-webpack-plugin": "^2.1.0", "sass": "^1.86.0", "shx": "^0.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.2", "uglify-js": "^3.19.3", "vite-plugin-sass": "^0.1.0", "vite-plugin-static-copy": "^2.3.0", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^1.6.1", "vitest-canvas-mock": "^0.3.3", "vitest-localstorage-mock": "^0.1.2", "webpack-bundle-analyzer": "^4.10.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "^4.12.1"}}