version: 0.2
env:
  secrets-manager:
    BITBUCKET_USERNAME: bitbucket-credentials:username
    BITBUCKET_PASSWORD: bitbucket-credentials:password
  parameter-store:
    DOCKER_USER: "/cicd/docker-login"
    DOCKER_PASSWORD: "/cicd/docker-password"
    APP_NAME: $(echo $CODEBUILD_SOURCE_VERSION | cut -f2 -d '/')"
phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://0.0.0.0:2375 --storage-driver=overlay&
  pre_build:
    commands:
      - DATE=$(date +%Y-%m-%d-%H-%M-%S)
      - aws ecr get-login-password | docker login --username AWS --password-stdin ${REPOSITORY_URI}
      - SHORT_COMMIT_ID="$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | head -c 8)"
      - DATE=$(date +%Y-%m-%d-%H-%M-%S)
      - IMAGE_URI="${REPOSITORY_URI}:${SHORT_COMMIT_ID}-${DATE}"
      - export SHORT_COMMIT_ID
      - |
        echo "Sending mail notifying the start of the build."
        date=$(date)
        message='{"Subject":{ "Data": "Deploying '"$ENVIRONMENT"' for frontend on '"$date"'" },"Body":{"Html":{"Data": "<body>Deploying '"$ENVIRONMENT"' for frontend on '"$date"'<body/>"}}}'
        echo $message > message.txt
        destination='{ "ToAddresses": ["<EMAIL>"] }'
        echo $destination > destination.txt
        aws ses send-email --from <EMAIL> --destination=file://destination.txt --message=file://message.txt
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker login --username ${DOCKER_USER} --password ${DOCKER_PASSWORD}
      - echo $IMAGE_URI

      # Retrieving environment variables from AWS Secrets Manager.
      - |
        echo "Retrieving FE secrets from AWS Secrets Manager."
        aws secretsmanager get-secret-value --secret-id ${ENVIRONMENT}/fe/env_vars  --query SecretString --output text | jq '.' | jq -r "to_entries|map(\"\(.key)=\(.value|tostring)\")|.[]" > .env
        echo "Secrets for FE retrieved."
      - cat .env | grep -E 'REACT_ENVIRONMENT'
      - echo "VITE_APP_SHORT_COMMIT_ID="$SHORT_COMMIT_ID >> .env
      - cat .env | grep -E 'VITE_APP_SHORT_COMMIT_ID'
      - docker build --build-arg BASE_TAG=$BASE_IMAGE_TAG --tag "$IMAGE_URI" .
      - echo "Enabling Image Scanning for repository ${APP_NAME}-${ENVIRONMENT} "
      - aws ecr put-image-scanning-configuration --repository-name ${APP_NAME} --image-scanning-configuration scanOnPush=true --region ${AWS_REGION}
  post_build:
    commands:
      - |
        if [ $CODEBUILD_BUILD_SUCCEEDING -ne 1 ]; then
          echo "Sending mail notifying the fail of the build."
          date=$(date)
          message='{"Subject":{ "Data": "Build fail on env '"$ENVIRONMENT"' for frontend on '"$date"'" },"Body":{"Html":{"Data": "<body>Build fail for env '"$ENVIRONMENT"' for frontend on '"$date"'<body/>"}}}'
          echo $message > message.txt
          destination='{ "ToAddresses": ["<EMAIL>"] }'
          echo $destination > destination.txt
          aws ses send-email --from <EMAIL> --destination=file://destination.txt --message=file://message.txt
        fi
      - echo "CODEBUILD_BUILD_SUCCEEDING=" $CODEBUILD_BUILD_SUCCEEDING
      - '[ ${CODEBUILD_BUILD_SUCCEEDING:-0} -eq 1 ] || exit 1'
      - echo "$IMAGE_URI"
      - docker push "$IMAGE_URI"
      - printf '{"Tag":"%s-%s"}' $SHORT_COMMIT_ID $DATE > build.json
      - |
        ECR_IMAGE="$IMAGE_URI"
        echo "The ECR Image is: " $ECR_IMAGE
        TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_NAME} --region "$AWS_REGION") # Name of task should be there
        echo "Old task definiton is: " $TASK_DEFINITION

        # Updates the current task definition by setting the new image to the previous
        # Task definition
        NEW_TASK_DEFINTIION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$ECR_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
        echo "New task definition is: " $NEW_TASK_DEFINTIION

        # Registers the newly created task definition 
        NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINTIION")
        echo "New task info is: " $NEW_TASK_INFO
        NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')
        echo "Task defintion is: " $TASK_DEFINITION
        echo "New revision is: " $NEW_REVISION
      - printf '[{"name":"dev-cp-fe-svc","imageUri":"'$IMAGE_URI'"}]' > imagedefinitions.json
artifacts:
  files:
    - imagedefinitions.json
