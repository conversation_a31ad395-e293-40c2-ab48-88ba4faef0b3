AWSTemplateFormatVersion: 2010-09-09
Description: Fargate Template for channel_program_fe
Parameters:
  Environment:
    Type: String
  Branch:
    Type: String    
  DesiredCount:
    Type: String
    Default: 1
  VpcId:
    Type: AWS::EC2::VPC::Id
  PrivateSubnetIdA:
    Type: AWS::EC2::Subnet::Id
  PrivateSubnetIdB:
    Type: AWS::EC2::Subnet::Id
  PublicSubnetIdA:
    Type: AWS::EC2::Subnet::Id
  PublicSubnetIdB:
    Type: AWS::EC2::Subnet::Id    
  Repository:
    Type: String
  CertificateArn:
    Type: String 
    # Update with the certificate ARN from Certificate Manager, which must exist in the same region.
    Default: 'arn:aws:acm:us-east-1:806851826417:certificate/2c844aa8-4f45-4d24-a5b4-587f93186a62' 
  Tag:
    Type: String
  ServiceName:
    Type: String
  ContainerPort:
    Type: String
    Default: 80
  LoadBalancerPort:
    Type: Number
    Default: 443
  HealthCheckPath:
    Type: String
    Default: /
  HostedZoneName:
    Type: String
    Default: staging.channelprogram.com
  Subdomain:
    Type: String
  # for autoscaling 
  MinContainers:
    Type: Number
    Default: 1
  # for autoscaling
  MaxContainers:
    Type: Number
    Default: 2
  # target CPU utilization (%)
  AutoScalingTargetValue:
    Type: Number
    Default: 50
Mappings:
  PrivateSecurityGroups: 
    development:
      SecurityGroup: 'sg-0385091d4ecbfba26'
    staging:
      SecurityGroup: 'sg-0385091d4ecbfba26'    
Resources:
  Cluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${ServiceName}    
      ClusterSettings:
        - Name: 'containerInsights'
          Value: 'enabled'  
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    # Makes sure the log group is created before it is used.
    DependsOn: LogGroup
    Properties:
      # Name of the task definition. Subsequent versions of the task definition are grouped together under this name. 
      Family: !Join ['', [!Ref ServiceName, TaskDefinition]]
      # awsvpc is required for Fargate
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      # 256 (.25 vCPU) - Available memory values: 0.5GB, 1GB, 2GB  
      # 512 (.5 vCPU) - Available memory values: 1GB, 2GB, 3GB, 4GB
      # 1024 (1 vCPU) - Available memory values: 2GB, 3GB, 4GB, 5GB, 6GB, 7GB, 8GB
      # 2048 (2 vCPU) - Available memory values: Between 4GB and 16GB in 1GB increments
      # 4096 (4 vCPU) - Available memory values: Between 8GB and 30GB in 1GB increments
      Cpu: 1024
      # 0.5GB, 1GB, 2GB - Available cpu values: 256 (.25 vCPU)
      # 1GB, 2GB, 3GB, 4GB - Available cpu values: 512 (.5 vCPU)
      # 2GB, 3GB, 4GB, 5GB, 6GB, 7GB, 8GB - Available cpu values: 1024 (1 vCPU)
      # Between 4GB and 16GB in 1GB increments - Available cpu values: 2048 (2 vCPU)
      # Between 8GB and 30GB in 1GB increments - Available cpu values: 4096 (4 vCPU)
      Memory: 4GB
      # A role needed by ECS.
      # "The ARN of the task execution role that containers in this task can assume. All containers in this task are granted the permissions that are specified in this role."
      # "There is an optional task execution IAM role that you can specify with Fargate to allow your Fargate tasks to make API calls to Amazon ECR."
      ExecutionRoleArn: !Ref ExecutionRole 
      # "The Amazon Resource Name (ARN) of an AWS Identity and Access Management (IAM) role that grants containers in the task permission to call AWS APIs on your behalf."
      TaskRoleArn: !Ref TaskRole
      ContainerDefinitions:
        - Name: !Ref ServiceName
          Image: !Sub ${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/${Repository}:${Tag}
          # Following line for testing traffic outside ECR
          # Image: public.ecr.aws/nginx/nginx:latest
          Essential: true
          PortMappings:
            - ContainerPort: !Ref ContainerPort 
          # Send logs to CloudWatch Logs 
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-region: !Ref AWS::Region
              awslogs-group: !Ref LogGroup
              awslogs-stream-prefix: ecs
          Command:
            - '/docker-entrypoint.sh'              
  # A role needed by ECS
  ExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ['', [!Ref ServiceName, ExecutionRole]]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service:
              - ec2.amazonaws.com
              - ecs.amazonaws.com
              - ecs-tasks.amazonaws.com
            Action: 'sts:AssumeRole'     
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role
  # A role for the containers
  TaskRole:
    Type: AWS::IAM::Role
    Properties:
      Path: /
      RoleName: !Join ['', [!Ref ServiceName, TaskRole]]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: 'sts:AssumeRole'
      Policies:
        - PolicyName: root
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              # - Effect: Allow
              #   Action: ssm:*
              #   Resource: !Sub arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${Environment}/${Repository}/*
              - Effect: Allow
                Action:
                  - ecr:*
                  - ecs:*
                  - logs:*
                  - sns:Publish
                  - ssm:*
                  - kms:Decrypt
                  - secretsmanager:GetSecretValue
                Resource: '*'
  AutoScalingRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ['', [!Ref ServiceName, AutoScalingRole]]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: 'sts:AssumeRole'
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole'
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Join ['', [!Ref ServiceName, SecurityGroup]]
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 1
          ToPort: 65535
          CidrIp: 0.0.0.0/0    
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 1
          ToPort: 65535
          CidrIp: 0.0.0.0/0          
  # LoadBalancerSecurityGroup:
  #   Type: AWS::EC2::SecurityGroup
  #   Properties:
  #     GroupDescription: !Join ['', [!Ref ServiceName, LoadBalancerSecurityGroup]]
  #     VpcId: !Ref VpcId
  #     SecurityGroupIngress:
  #       - CidrIp: "0.0.0.0/0"
  #         IpProtocol: "TCP"
  #         FromPort: 80
  #         ToPort: 80
  #       - CidrIp: "0.0.0.0/0"
  #         IpProtocol: "TCP"
  #         FromPort: 443
  #         ToPort: 443    
  #     SecurityGroupEgress:
  #       - IpProtocol: tcp
  #         FromPort: 1
  #         ToPort: 65535
  #         CidrIp: 0.0.0.0/0          
  Service:
    Type: AWS::ECS::Service
    DependsOn:
      - ListenerHTTPS
    Properties: 
      ServiceName: !Ref ServiceName
      Cluster: !Ref Cluster
      TaskDefinition: !Ref TaskDefinition
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
        MaximumPercent: 200
      DesiredCount: 1
      # This may need to be adjusted if the container takes a while to start up
      HealthCheckGracePeriodSeconds: 120
      LaunchType: FARGATE
      NetworkConfiguration: 
        AwsvpcConfiguration:
          # change to DISABLED if you're using private subnets that have access to a NAT gateway 
          # AssignPublicIp: ENABLED
          AssignPublicIp: DISABLED
          Subnets:   
            - !Sub ${PrivateSubnetIdA}
            - !Sub ${PrivateSubnetIdB}
          SecurityGroups:
            - !Ref SecurityGroup
      LoadBalancers:
        - ContainerName: !Ref ServiceName
          ContainerPort: !Ref ContainerPort
          TargetGroupArn: !Ref TargetGroup
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      HealthCheckIntervalSeconds: 10
      # will look for a 200 status code by default unless specified otherwise
      HealthCheckPath: !Ref HealthCheckPath
      UnhealthyThresholdCount: 2
      HealthyThresholdCount: 2
      Name: !Sub ${ServiceName}
      Port: !Ref ContainerPort
      Protocol: HTTP
      Matcher:
        HttpCode: 200-299
      HealthCheckIntervalSeconds: 10
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: 30 # default is 300
      TargetType: ip
      VpcId: !Ref VpcId
  HTTPlistener:
    Type: "AWS::ElasticLoadBalancingV2::Listener"
    Properties:
      DefaultActions:
        - Type: "redirect"
          RedirectConfig:
            Protocol: "HTTPS"
            Port: 443
            Host: "#{host}"
            Path: "/#{path}"
            Query: "#{query}"
            StatusCode: "HTTP_301"
      LoadBalancerArn: !Ref LoadBalancer
      Port: 80
      Protocol: "HTTP"       
  ListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - TargetGroupArn: !Ref TargetGroup
          Type: forward
      LoadBalancerArn: !Ref LoadBalancer
      Port: !Ref LoadBalancerPort
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref CertificateArn
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      LoadBalancerAttributes:
        # this is the default, but is specified here in case it needs to be changed
        - Key: idle_timeout.timeout_seconds
          Value: 60
      Name: !Sub ${ServiceName}
      # Name: !Join ['', [!Ref ServiceName, LoadBalancer]] 
      # "internal" is also an option
      # Scheme: internet-facing
      SecurityGroups:
        - !FindInMap [PrivateSecurityGroups, !Ref Environment, SecurityGroup]
      Subnets:
        - !Sub ${PublicSubnetIdA}
        - !Sub ${PublicSubnetIdB}
  DNSRecord:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneName: !Join ['', [!Ref HostedZoneName, .]]
      # Name: !Join ['', [!Ref Subdomain, ., !Ref HostedZoneName, .]]
      Name: !Ref HostedZoneName
      Type: A
      AliasTarget:
        DNSName: !GetAtt LoadBalancer.DNSName
        HostedZoneId: !GetAtt LoadBalancer.CanonicalHostedZoneID
  LogGroup:
    DependsOn: ExecutionRole
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Join ['', [/ecs/, !Ref ServiceName, TaskDefinition]]
  AutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MinCapacity: !Ref MinContainers
      MaxCapacity: !Ref MaxContainers
      ResourceId: !Join ['/', [service, !Ref Cluster, !GetAtt Service.Name]]
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
      # "The Amazon Resource Name (ARN) of an AWS Identity and Access Management (IAM) role that allows Application Auto Scaling to modify your scalable target."
      RoleARN: !GetAtt AutoScalingRole.Arn
  AutoScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Join ['', [!Ref ServiceName, AutoScalingPolicy]]
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref AutoScalingTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        ScaleInCooldown: 10
        ScaleOutCooldown: 10
        # Keep things at or lower than 50% CPU utilization, for example
        TargetValue: !Ref AutoScalingTargetValue
Outputs:
  Endpoint:
    Description: Endpoint
    Value: !Join ['', ['https://', !Ref DNSRecord]]