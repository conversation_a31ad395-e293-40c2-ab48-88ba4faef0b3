<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <meta name="referrer" content="origin-when-cross-origin" />
        <!-- <link rel="manifest" href="/site.webmanifest" /> -->
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#ff6120" />
        <meta name="msapplication-TileColor" content="#0d2335" />
        <meta name="theme-color" content="#0d2335" />
        <link rel="stylesheet" href="https://use.typekit.net/aya1fzo.css" />
        <link rel="stylesheet" href="https://use.typekit.net/aya1fzo.css" />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-640x1136.png"
            media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-750x1294.png"
            media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-1242x2148.png"
            media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-1125x2436.png"
            media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-1536x2048.png"
            media="(min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-1668x2224.png"
            media="(min-device-width: 834px) and (max-device-width: 834px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait)"
        />
        <link
            rel="apple-touch-startup-image"
            href="/Media/splash/ios-splash-2048x2732.png"
            media="(min-device-width: 1024px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait)"
        />
        <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
        <link rel="manifest" crossorigin="use-credentials" href="/manifest.json" />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
            rel="stylesheet"
        />
        <link
            href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
            rel="stylesheet"
        />
        <meta property="og:image:type" content="image/jpeg" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:site" content="@ChannelProgram_" />
        <meta property="og:locale" content="en_US" />
        <meta property="fb:admins" content="Channel-Program-113031624351535" />
        <meta property="fb:app_id" content="%VITE_APP_FACEBOOK_APP_ID%" />
        <meta name="facebook-domain-verification" content="%VITE_APP_FACEBOOK_DOMAIN_VERIFY%" />
        <script type="text/javascript" src="https://cdn.addevent.com/libs/atc/1.6.1/atc.min.js" async defer></script>
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-CLC1TS9M3B"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-CLC1TS9M3B");
        </script>

        <!-- Google Tag Manager -->
        <script>
            (function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({"gtm.start": new Date().getTime(), event: "gtm.js"});
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != "dataLayer" ? "&l=" + l : "";
                j.async = true;
                j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, "script", "dataLayer", "%VITE_APP_GTM_ID%");
        </script>
        <!-- End Google Tag Manager -->
    </head>
    <script type="text/javascript">
        _linkedin_partner_id = "3611012";
        window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
        window._linkedin_data_partner_ids.push(_linkedin_partner_id);
    </script>
    <script type="text/javascript">
        (function (l) {
            if (!l) {
                window.lintrk = function (a, b) {
                    window.lintrk.q.push([a, b]);
                };
                window.lintrk.q = [];
            }
            var s = document.getElementsByTagName("script")[0];
            var b = document.createElement("script");
            b.type = "text/javascript";
            b.async = true;
            b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
            s.parentNode.insertBefore(b, s);
        })(window.lintrk);
    </script>
    <noscript>
        <img
            height="1"
            width="1"
            style="display: none"
            alt=""
            src="https://px.ads.linkedin.com/collect/?pid=3611012&fmt=gif"
        />
    </noscript>
    <body>
        <!-- Google Tag Manager (noscript) -->
        <noscript
            ><iframe
                src="https://www.googletagmanager.com/ns.html?id=%VITE_APP_GTM_ID%"
                height="0"
                width="0"
                style="display: none; visibility: hidden"
            ></iframe
        ></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root"></div>
        <!-- Start of HubSpot Embed Code -->
        <script
            type="text/javascript"
            id="hs-script-loader"
            async
            defer
            src="//js.hs-scripts.com/%VITE_APP_HUBSPOT_TRACKING_ID%.js"
        ></script>
        <!-- End of HubSpot Embed Code -->
        <script type="module" src="/src/index.tsx"></script>
    </body>
</html>
