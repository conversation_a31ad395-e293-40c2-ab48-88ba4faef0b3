#!/usr/bin/env pwsh

# Get a list of all staged files
$stagedFiles = git diff --cached HEAD --name-only --diff-filter=ACM
$denyPattern = "debugger;|binding\.pry|alert\(|console\.log\(|console\.debug\("
$errorFiles = @()

# Loop through each file
foreach ($file in $stagedFiles) {
    # Get the line numbers and matched strings
    $matches = Select-String -Path $file -Pattern $denyPattern -AllMatches
    foreach ($match in $matches) {
        $errorFiles += "$file - line $($match.LineNumber) - '$($match.Line)'"
    }
}

if ($errorFiles.Count -gt 0) {
    Write-Host "---"
    Write-Host "Found unwanted words at files:"
    foreach ($errorFile in $errorFiles) {
        Write-Host "`t- $errorFile" -BackgroundColor Yellow -ForegroundColor Black
    }
    Write-Host ""
    Write-Host "Please remove it."
    Write-Host "If you want to skip that then run git commit -m 'your comment' --no-verify" -ForegroundColor Blue
    Write-Host "---"
    exit 1
}

exit