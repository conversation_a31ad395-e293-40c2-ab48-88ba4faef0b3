#!/bin/sh

unameOut=$(uname -a)
case "${unameOut}" in
    *[mM]icrosoft*)  OS="WSL";;
    Linux*)          OS="Linux";;
    Darwin*)         OS="Mac";;
    CYGWIN*)         OS="Cygwin";;
    MINGW*)          OS="Windows";;
    *[mM]sys*)       OS="Windows";;
    *)               OS="UNKNOWN:${unameOut}";;
esac

if [ "$OS" = "Windows" ]; then
    # Call the Windows script
    powershell.exe -ExecutionPolicy Bypass -File git-hooks/windows-precommit.ps1
else
    # Call the Unix script
    ./git-hooks/unix-precommit.sh
fi